async function handler({
  action,
  reportType,
  format,
  dateRange,
  department,
  status,
  template,
  searchParams = {},
}) {
  const formatReport = async (data, format) => {
    switch (format) {
      case 'excel':
        const excelResponse = await fetch('/api/excel-service', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            data,
            template,
            headers: {
              name: 'الاسم',
              status: 'الحالة',
              date: 'التاريخ',
            },
          }),
        });
        return await excelResponse.json();

      default:
        return data;
    }
  };

  try {
    switch (action) {
      case 'generate': {
        const params = [dateRange.start, dateRange.end];
        let paramCount = 3;

        let queryStr = `
          SELECT 
            emp.*,
            att.*,
            dept.Name as DepartmentName,
            COUNT(*) OVER() as total_count
          FROM Employees emp
          LEFT JOIN Attendance att ON emp.EmployeeCode = att.EmployeeCode
          LEFT JOIN Departments dept ON emp.DepartmentID = dept.DepartmentID
          WHERE att.AttendanceDate BETWEEN $1 AND $2
        `;

        if (searchParams.employeeId) {
          queryStr += ` AND emp.EmployeeCode = $${paramCount}`;
          params.push(searchParams.employeeId);
          paramCount++;
        }

        if (department !== 'all') {
          queryStr += ` AND dept.DepartmentID = $${paramCount}`;
          params.push(department);
          paramCount++;
        }

        if (status && status !== 'all') {
          switch (status) {
            case 'active':
              queryStr += ' AND att.Status = \'Present\'';
              break;
            case 'inactive':
              queryStr += ' AND att.Status = \'Absent\'';
              break;
            case 'onLeave':
              queryStr += ' AND att.Status = \'Leave\'';
              break;
          }
        }

        queryStr += ' ORDER BY att.AttendanceDate DESC';

        const result = await sql(queryStr, params);

        const stats = {
          totalEmployees: result.length,
          presentPercentage: 0,
          absentPercentage: 0,
          leavePercentage: 0,
        };

        if (result.length > 0) {
          const totals = result.reduce((acc, row) => {
            switch (row.Status) {
              case 'Present':
                acc.present++;
                break;
              case 'Absent':
                acc.absent++;
                break;
              case 'Leave':
                acc.leave++;
                break;
            }
            acc.total++;
            return acc;
          }, { present: 0, absent: 0, leave: 0, total: 0 });

          stats.presentPercentage = Math.round((totals.present / totals.total) * 100);
          stats.absentPercentage = Math.round((totals.absent / totals.total) * 100);
          stats.leavePercentage = Math.round((totals.leave / totals.total) * 100);
        }

        const reportData = {
          title: 'تقرير الحضور الشهري',
          summary: stats,
          details: result,
        };

        const formattedReport = await formatReport(reportData, format);
        return {
          success: true,
          report: formattedReport,
        };
      }

      case 'statistics': {
        const params = [dateRange.start, dateRange.end];
        let paramCount = 3;

        let queryStr = `
          SELECT 
            COUNT(DISTINCT emp.EmployeeID) as total_employees,
            COUNT(CASE WHEN att.Status = 'Present' THEN 1 END) as total_present,
            COUNT(CASE WHEN att.Status = 'Absent' THEN 1 END) as total_absent,
            COUNT(CASE WHEN att.Status = 'Leave' THEN 1 END) as total_leave,
            COUNT(att.AttendanceID) as total_working_days
          FROM Employees emp
          LEFT JOIN Attendance att ON emp.EmployeeID = att.EmployeeID
          WHERE att.AttendanceDate BETWEEN $1 AND $2
        `;

        if (department !== 'all') {
          queryStr += ` AND emp.DepartmentID = $${paramCount}`;
          params.push(department);
        }

        const result = await sql(queryStr, params);

        const stats = {
          totalEmployees: result[0].total_employees,
          presentPercentage:
            Math.round(
              (result[0].total_present / result[0].total_working_days) * 100
            ) || 0,
          absentPercentage:
            Math.round(
              (result[0].total_absent / result[0].total_working_days) * 100
            ) || 0,
          leavePercentage:
            Math.round(
              (result[0].total_leave / result[0].total_working_days) * 100
            ) || 0,
        };

        return {
          success: true,
          statistics: stats,
        };
      }

      default:
        throw new Error('إجراء غير صالح');
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
}
