@echo off
chcp 65001 >nul
title إنشاء اختصار سطح المكتب
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    إنشاء اختصار سطح المكتب                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: الحصول على مسار سطح المكتب
for /f "tokens=3*" %%i in ('reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders" /v Desktop 2^>nul') do set "DESKTOP=%%i %%j"

:: إنشاء ملف VBS لإنشاء الاختصار
echo Set oWS = WScript.CreateObject("WScript.Shell") > "%temp%\CreateShortcut.vbs"
echo sLinkFile = "%DESKTOP%\🚀 نظام إدارة التكاليف.lnk" >> "%temp%\CreateShortcut.vbs"
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> "%temp%\CreateShortcut.vbs"
echo oLink.TargetPath = "%~dp0تشغيل النظام.cmd" >> "%temp%\CreateShortcut.vbs"
echo oLink.WorkingDirectory = "%~dp0" >> "%temp%\CreateShortcut.vbs"
echo oLink.Description = "تشغيل نظام إدارة التكاليف" >> "%temp%\CreateShortcut.vbs"
echo oLink.IconLocation = "shell32.dll,25" >> "%temp%\CreateShortcut.vbs"
echo oLink.Save >> "%temp%\CreateShortcut.vbs"

:: تشغيل ملف VBS
cscript "%temp%\CreateShortcut.vbs" >nul

:: حذف ملف VBS المؤقت
del "%temp%\CreateShortcut.vbs"

echo ✅ تم إنشاء اختصار على سطح المكتب بنجاح!
echo.
echo 📍 اسم الاختصار: 🚀 نظام إدارة التكاليف
echo 🖱️  انقر مرتين على الاختصار لتشغيل النظام
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        تم بنجاح!                            ║
echo ╚══════════════════════════════════════════════════════════════╝

pause
