import { NextResponse } from 'next/server';
import sql from 'mssql';

// إعداد قاعدة البيانات
const dbConfig = {
  user: process.env.DB_USER || 'sa',
  password: process.env.DB_PASSWORD || 'ojesta',
  server: process.env.DB_SERVER || 'HR-Samy\\DBOJESTA',
  database: process.env.DB_NAME || 'OJESTA',
  options: {
    encrypt: false,
    trustServerCertificate: true,
    enableArithAbort: true,
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
};

let pool;

async function getPool() {
  if (!pool) {
    pool = await sql.connect(dbConfig);
  }
  return pool;
}

export async function GET(request) {
  try {

    const pool = await getPool();
    
    // جلب جميع الجداول مع أعمدتها
    const tablesResult = await pool.request().query(`
      SELECT 
        t.TABLE_NAME,
        c.COLUMN_NAME,
        c.DATA_TYPE,
        c.IS_NULLABLE,
        c.ORDINAL_POSITION
      FROM INFORMATION_SCHEMA.TABLES t
      LEFT JOIN INFORMATION_SCHEMA.COLUMNS c ON t.TABLE_NAME = c.TABLE_NAME
      WHERE t.TABLE_TYPE = 'BASE TABLE'
      ORDER BY t.TABLE_NAME, c.ORDINAL_POSITION
    `);

    // تجميع البيانات حسب الجدول
    const tablesData = {};
    tablesResult.recordset.forEach(row => {
      if (!tablesData[row.TABLE_NAME]) {
        tablesData[row.TABLE_NAME] = {
          name: row.TABLE_NAME,
          columns: []
        };
      }
      if (row.COLUMN_NAME) {
        tablesData[row.TABLE_NAME].columns.push({
          name: row.COLUMN_NAME,
          type: row.DATA_TYPE,
          nullable: row.IS_NULLABLE === 'YES'
        });
      }
    });

    // تحليل المشاكل
    const issues = [];
    const employeeRelatedTables = [];
    const apartmentRelatedTables = [];
    const carRelatedTables = [];

    Object.values(tablesData).forEach(table => {
      const employeeCodeColumns = table.columns.filter(col => 
        col.name.toLowerCase().includes('employee') && 
        (col.name.toLowerCase().includes('code') || col.name.toLowerCase().includes('id'))
      );

      const employeeNameColumns = table.columns.filter(col => 
        col.name.toLowerCase().includes('employee') && 
        col.name.toLowerCase().includes('name')
      );

      const apartmentColumns = table.columns.filter(col => 
        col.name.toLowerCase().includes('apartment')
      );

      const carColumns = table.columns.filter(col => 
        col.name.toLowerCase().includes('car')
      );

      // تسجيل الجداول المرتبطة بالموظفين
      if (employeeCodeColumns.length > 0 || employeeNameColumns.length > 0) {
        employeeRelatedTables.push({
          table: table.name,
          employeeCodeColumn: employeeCodeColumns[0]?.name || 'غير موجود',
          employeeNameColumn: employeeNameColumns[0]?.name || 'غير موجود',
          issues: []
        });

        // فحص التسميات
        if (employeeCodeColumns.length > 0) {
          const colName = employeeCodeColumns[0].name;
          if (!['EmployeeCode', 'EmployeeID'].includes(colName)) {
            issues.push({
              table: table.name,
              type: 'تسمية غير موحدة',
              column: colName,
              expected: 'EmployeeCode',
              severity: 'متوسط'
            });
          }
        }

        if (employeeNameColumns.length > 0) {
          const colName = employeeNameColumns[0].name;
          if (!['EmployeeName', 'FullName'].includes(colName)) {
            issues.push({
              table: table.name,
              type: 'تسمية غير موحدة',
              column: colName,
              expected: 'EmployeeName',
              severity: 'متوسط'
            });
          }
        }
      }

      // تسجيل الجداول المرتبطة بالشقق
      if (apartmentColumns.length > 0) {
        apartmentRelatedTables.push({
          table: table.name,
          apartmentColumns: apartmentColumns.map(c => c.name)
        });
      }

      // تسجيل الجداول المرتبطة بالسيارات
      if (carColumns.length > 0) {
        carRelatedTables.push({
          table: table.name,
          carColumns: carColumns.map(c => c.name)
        });
      }
    });

    // إنشاء التقرير النهائي
    const report = {
      summary: {
        totalTables: Object.keys(tablesData).length,
        employeeRelatedTables: employeeRelatedTables.length,
        apartmentRelatedTables: apartmentRelatedTables.length,
        carRelatedTables: carRelatedTables.length,
        totalIssues: issues.length
      },
      employeeRelatedTables,
      apartmentRelatedTables,
      carRelatedTables,
      issues,
      recommendations: []
    };

    // إضافة التوصيات
    const employeeCodeVariations = new Set();
    const employeeNameVariations = new Set();

    employeeRelatedTables.forEach(table => {
      if (table.employeeCodeColumn !== 'غير موجود') {
        employeeCodeVariations.add(table.employeeCodeColumn);
      }
      if (table.employeeNameColumn !== 'غير موجود') {
        employeeNameVariations.add(table.employeeNameColumn);
      }
    });

    if (employeeCodeVariations.size > 1) {
      report.recommendations.push({
        type: 'توحيد تسمية كود الموظف',
        variations: Array.from(employeeCodeVariations),
        recommended: 'EmployeeCode',
        priority: 'عالي'
      });
    }

    if (employeeNameVariations.size > 1) {
      report.recommendations.push({
        type: 'توحيد تسمية اسم الموظف',
        variations: Array.from(employeeNameVariations),
        recommended: 'EmployeeName',
        priority: 'عالي'
      });
    }

    return NextResponse.json({
      success: true,
      report: report,
      timestamp: new Date().toISOString()
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'حدث خطأ في إنشاء التقرير',
      error: error.message
    }, { status: 500 });
  }
}

export async function POST(request) {
  try {
    const { action } = await request.json();
    
    if (action === 'fix_naming') {
      return await fixNamingIssues();
    }
    
    return NextResponse.json({
      success: false,
      message: 'إجراء غير مدعوم'
    }, { status: 400 });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'حدث خطأ في معالجة الطلب',
      error: error.message
    }, { status: 500 });
  }
}

async function fixNamingIssues() {
  try {
    const pool = await getPool();
    const results = [];

    // قائمة الإصلاحات المطلوبة
    const fixes = [
      {
        table: 'Employees',
        oldColumn: 'EmployeeID',
        newColumn: 'EmployeeCode',
        description: 'توحيد كود الموظف في جدول الموظفين'
      },
      {
        table: 'Employees',
        oldColumn: 'FullName',
        newColumn: 'EmployeeName',
        description: 'توحيد اسم الموظف في جدول الموظفين'
      },
      {
        table: 'ApartmentBeneficiaries',
        oldColumn: 'ApartmentID',
        newColumn: 'ApartmentCode',
        description: 'توحيد كود الشقة في جدول المستفيدين'
      }
    ];

    for (const fix of fixes) {
      try {
        // التحقق من وجود العمود القديم
        const checkColumn = await pool.request().query(`
          SELECT COUNT(*) as Count
          FROM INFORMATION_SCHEMA.COLUMNS 
          WHERE TABLE_NAME = '${fix.table}' AND COLUMN_NAME = '${fix.oldColumn}'
        `);

        if (checkColumn.recordset[0].Count > 0) {
          // إعادة تسمية العمود
          await pool.request().query(`
            EXEC sp_rename '${fix.table}.${fix.oldColumn}', '${fix.newColumn}', 'COLUMN'
          `);

          results.push({
            table: fix.table,
            action: `تم تغيير ${fix.oldColumn} إلى ${fix.newColumn}`,
            status: 'نجح',
            description: fix.description
          });

        } else {
          results.push({
            table: fix.table,
            action: `العمود ${fix.oldColumn} غير موجود`,
            status: 'تم تخطي',
            description: fix.description
          });
        }

      } catch (error) {
        results.push({
          table: fix.table,
          action: `خطأ في تغيير ${fix.oldColumn}`,
          status: 'فشل',
          error: error.message,
          description: fix.description
        });

      }
    }

    return NextResponse.json({
      success: true,
      message: 'تم إصلاح مشاكل التسمية',
      results: results
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'حدث خطأ في إصلاح مشاكل التسمية',
      error: error.message
    }, { status: 500 });
  }
}
