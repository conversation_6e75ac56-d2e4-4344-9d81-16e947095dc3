import sql from 'mssql';

const config = {
  server: 'localhost\\DBOJESTA',  // اسم المثيل الصحيح كما هو مثبت على الجهاز
  database: 'emp',  // تأكد من استخدام الأحرف الصغيرة للتوافق مع اسم قاعدة البيانات
  user: 'sa',
  password: 'admin@123',
  options: {
    trustServerCertificate: true,
    encrypt: false,
    enableArithAbort: true,
    instanceName: 'DBOJESTA'  // إضافة اسم المثيل بشكل صريح
  },
  connectionTimeout: 30000,
  requestTimeout: 30000,
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000
  }
};

export async function getConnection() {
  try {

    const pool = await sql.connect(config);

    return pool;
  } catch (error) {

    throw error;
  }
}

export async function closeConnection() {
  try {
    await sql.close();

  } catch (error) {

    throw error;
  }
}

export { config };
export default sql;
