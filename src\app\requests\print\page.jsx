'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/MainLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import { 
  FiPrinter,
  FiDownload,
  FiFileText,
  FiCalendar,
  FiMapPin,
  FiClock,
  FiMoon,
  FiArrowLeft
} from 'react-icons/fi';

const PrintRequestsPage = () => {
  const router = useRouter();
  const { isArabic } = useLanguage();
  const [selectedForm, setSelectedForm] = useState('');
  const [formData, setFormData] = useState({});

  // أنواع النماذج المتاحة للطباعة
  const formTypes = [
    {
      id: 'leave',
      title: isArabic ? 'نموذج طلب إجازة' : 'Leave Request Form',
      icon: FiCalendar,
      color: 'blue'
    },
    {
      id: 'mission',
      title: isArabic ? 'نموذج طلب مأمورية' : 'Mission Request Form',
      icon: FiMapPin,
      color: 'green'
    },
    {
      id: 'permission',
      title: isArabic ? 'نموذج طلب إذن' : 'Permission Request Form',
      icon: FiClock,
      color: 'orange'
    },
    {
      id: 'night-shift',
      title: isArabic ? 'نموذج إذن الوردية الليلية' : 'Night Shift Permission Form',
      icon: FiMoon,
      color: 'purple'
    }
  ];

  // طباعة النموذج الفارغ
  const printBlankForm = (formType) => {
    const printWindow = window.open('', '_blank');
    let formContent = '';

    switch (formType) {
      case 'leave':
        formContent = generateLeaveForm();
        break;
      case 'mission':
        formContent = generateMissionForm();
        break;
      case 'permission':
        formContent = generatePermissionForm();
        break;
      case 'night-shift':
        formContent = generateNightShiftForm();
        break;
      default:
        return;
    }

    const printContent = `
      <!DOCTYPE html>
      <html dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>${getFormTitle(formType)}</title>
        <style>
          @page {
            size: A4;
            margin: 1cm;
          }
          body {
            font-family: 'Arial', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #000;
            margin: 0;
            padding: 20px;
            direction: rtl;
          }
          .header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 15px;
            margin-bottom: 20px;
          }
          .company-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
          }
          .form-title {
            font-size: 16px;
            font-weight: bold;
            background-color: #f0f0f0;
            padding: 8px;
            border: 1px solid #000;
            margin: 10px 0;
          }
          .form-section {
            margin-bottom: 20px;
            border: 1px solid #000;
            padding: 10px;
          }
          .section-title {
            font-weight: bold;
            background-color: #e0e0e0;
            padding: 5px;
            margin: -10px -10px 10px -10px;
            border-bottom: 1px solid #000;
          }
          .form-row {
            display: flex;
            margin-bottom: 10px;
            align-items: center;
          }
          .form-field {
            margin-left: 20px;
            flex: 1;
          }
          .field-label {
            font-weight: bold;
            margin-left: 10px;
            min-width: 100px;
          }
          .field-input {
            border-bottom: 1px solid #000;
            min-height: 20px;
            padding: 2px 5px;
            min-width: 150px;
            display: inline-block;
          }
          .checkbox {
            width: 15px;
            height: 15px;
            border: 1px solid #000;
            display: inline-block;
            margin-left: 5px;
          }
          .signature-section {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
          }
          .signature-box {
            border: 1px solid #000;
            padding: 10px;
            width: 200px;
            height: 80px;
            text-align: center;
          }
          .date-field {
            border-bottom: 1px solid #000;
            width: 100px;
            height: 20px;
            display: inline-block;
          }
          @media print {
            body { margin: 0; padding: 15px; }
            .no-print { display: none; }
          }
        </style>
      </head>
      <body>
        ${formContent}
      </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
  };

  const getFormTitle = (formType) => {
    const titles = {
      leave: 'نموذج طلب إجازة',
      mission: 'نموذج طلب مأمورية',
      permission: 'نموذج طلب إذن',
      'night-shift': 'نموذج إذن الوردية الليلية'
    };
    return titles[formType] || 'نموذج طلب';
  };

  const generateLeaveForm = () => {
    return `
      <div class="header">
        <div class="company-name">شركة [اسم الشركة]</div>
        <div>إدارة الموارد البشرية</div>
      </div>

      <div class="form-title">نموذج طلب إجازة</div>

      <div class="form-section">
        <div class="section-title">بيانات الموظف</div>
        <div class="form-row">
          <div class="form-field">
            <span class="field-label">اسم الموظف:</span>
            <span class="field-input"></span>
          </div>
          <div class="form-field">
            <span class="field-label">رقم الموظف:</span>
            <span class="field-input"></span>
          </div>
        </div>
        <div class="form-row">
          <div class="form-field">
            <span class="field-label">القسم:</span>
            <span class="field-input"></span>
          </div>
          <div class="form-field">
            <span class="field-label">المسمى الوظيفي:</span>
            <span class="field-input"></span>
          </div>
        </div>
      </div>

      <div class="form-section">
        <div class="section-title">تفاصيل الإجازة</div>
        <div class="form-row">
          <span class="field-label">نوع الإجازة:</span>
          <span class="checkbox"></span> سنوية
          <span class="checkbox"></span> مرضية
          <span class="checkbox"></span> طارئة
          <span class="checkbox"></span> أمومة
          <span class="checkbox"></span> أخرى: <span class="field-input" style="width: 100px;"></span>
        </div>
        <div class="form-row">
          <div class="form-field">
            <span class="field-label">من تاريخ:</span>
            <span class="date-field"></span>
          </div>
          <div class="form-field">
            <span class="field-label">إلى تاريخ:</span>
            <span class="date-field"></span>
          </div>
          <div class="form-field">
            <span class="field-label">عدد الأيام:</span>
            <span class="field-input" style="width: 50px;"></span>
          </div>
        </div>
        <div class="form-row">
          <span class="field-label">سبب الإجازة:</span>
        </div>
        <div class="field-input" style="width: 100%; height: 60px; border: 1px solid #000; margin-top: 5px;"></div>
      </div>

      <div class="form-section">
        <div class="section-title">معلومات إضافية</div>
        <div class="form-row">
          <div class="form-field">
            <span class="field-label">الموظف البديل:</span>
            <span class="field-input"></span>
          </div>
        </div>
        <div class="form-row">
          <div class="form-field">
            <span class="field-label">جهة الاتصال في الطوارئ:</span>
            <span class="field-input"></span>
          </div>
          <div class="form-field">
            <span class="field-label">رقم الهاتف:</span>
            <span class="field-input"></span>
          </div>
        </div>
      </div>

      <div class="signature-section">
        <div class="signature-box">
          <div style="margin-bottom: 40px;">توقيع الموظف</div>
          <div>التاريخ: <span class="date-field"></span></div>
        </div>
        <div class="signature-box">
          <div style="margin-bottom: 40px;">توقيع المدير المباشر</div>
          <div>التاريخ: <span class="date-field"></span></div>
        </div>
        <div class="signature-box">
          <div style="margin-bottom: 40px;">اعتماد إدارة الموارد البشرية</div>
          <div>التاريخ: <span class="date-field"></span></div>
        </div>
      </div>
    `;
  };

  const generateMissionForm = () => {
    return `
      <div class="header">
        <div class="company-name">شركة [اسم الشركة]</div>
        <div>إدارة الموارد البشرية</div>
      </div>

      <div class="form-title">نموذج طلب مأمورية</div>

      <div class="form-section">
        <div class="section-title">بيانات الموظف</div>
        <div class="form-row">
          <div class="form-field">
            <span class="field-label">اسم الموظف:</span>
            <span class="field-input"></span>
          </div>
          <div class="form-field">
            <span class="field-label">رقم الموظف:</span>
            <span class="field-input"></span>
          </div>
        </div>
        <div class="form-row">
          <div class="form-field">
            <span class="field-label">القسم:</span>
            <span class="field-input"></span>
          </div>
          <div class="form-field">
            <span class="field-label">المسمى الوظيفي:</span>
            <span class="field-input"></span>
          </div>
        </div>
      </div>

      <div class="form-section">
        <div class="section-title">تفاصيل المأمورية</div>
        <div class="form-row">
          <span class="field-label">نوع المأمورية:</span>
          <span class="checkbox"></span> عمل
          <span class="checkbox"></span> تدريب
          <span class="checkbox"></span> مؤتمر
          <span class="checkbox"></span> اجتماع
          <span class="checkbox"></span> أخرى: <span class="field-input" style="width: 100px;"></span>
        </div>
        <div class="form-row">
          <div class="form-field">
            <span class="field-label">الوجهة:</span>
            <span class="field-input"></span>
          </div>
        </div>
        <div class="form-row">
          <div class="form-field">
            <span class="field-label">عدد الأيام:</span>
            <span class="field-input" style="width: 50px;"></span>
          </div>
          <div class="form-field">
            <span class="field-label">من تاريخ:</span>
            <span class="date-field"></span>
          </div>
          <div class="form-field">
            <span class="field-label">إلى تاريخ:</span>
            <span class="date-field"></span>
          </div>
        </div>
        <div class="form-row">
          <span class="field-label">الغرض من المأمورية:</span>
        </div>
        <div class="field-input" style="width: 100%; height: 60px; border: 1px solid #000; margin-top: 5px;"></div>
      </div>

      <div class="form-section">
        <div class="section-title">وسائل النقل والإقامة</div>
        <div class="form-row">
          <span class="field-label">وسيلة النقل:</span>
          <span class="checkbox"></span> سيارة الشركة
          <span class="checkbox"></span> سيارة شخصية
          <span class="checkbox"></span> طيران
          <span class="checkbox"></span> أخرى: <span class="field-input" style="width: 100px;"></span>
        </div>
        <div class="form-row">
          <span class="field-label">الإقامة:</span>
          <span class="checkbox"></span> مطلوبة
          <span class="checkbox"></span> غير مطلوبة
        </div>
        <div class="form-row">
          <div class="form-field">
            <span class="field-label">السلفة المطلوبة:</span>
            <span class="field-input"></span> جنيه
          </div>
          <div class="form-field">
            <span class="field-label">المصروفات المتوقعة:</span>
            <span class="field-input"></span> جنيه
          </div>
        </div>
      </div>

      <div class="signature-section">
        <div class="signature-box">
          <div style="margin-bottom: 40px;">توقيع الموظف</div>
          <div>التاريخ: <span class="date-field"></span></div>
        </div>
        <div class="signature-box">
          <div style="margin-bottom: 40px;">توقيع المدير المباشر</div>
          <div>التاريخ: <span class="date-field"></span></div>
        </div>
        <div class="signature-box">
          <div style="margin-bottom: 40px;">اعتماد إدارة الموارد البشرية</div>
          <div>التاريخ: <span class="date-field"></span></div>
        </div>
      </div>
    `;
  };

  const generatePermissionForm = () => {
    return `
      <div class="header">
        <div class="company-name">شركة [اسم الشركة]</div>
        <div>إدارة الموارد البشرية</div>
      </div>

      <div class="form-title">نموذج طلب إذن</div>

      <div class="form-section">
        <div class="section-title">بيانات الموظف</div>
        <div class="form-row">
          <div class="form-field">
            <span class="field-label">اسم الموظف:</span>
            <span class="field-input"></span>
          </div>
          <div class="form-field">
            <span class="field-label">رقم الموظف:</span>
            <span class="field-input"></span>
          </div>
        </div>
        <div class="form-row">
          <div class="form-field">
            <span class="field-label">القسم:</span>
            <span class="field-input"></span>
          </div>
          <div class="form-field">
            <span class="field-label">المسمى الوظيفي:</span>
            <span class="field-input"></span>
          </div>
        </div>
      </div>

      <div class="form-section">
        <div class="section-title">تفاصيل الإذن</div>
        <div class="form-row">
          <div class="form-field">
            <span class="field-label">التاريخ:</span>
            <span class="date-field"></span>
          </div>
        </div>
        <div class="form-row">
          <div class="form-field">
            <span class="field-label">من الساعة:</span>
            <span class="field-input" style="width: 80px;"></span>
          </div>
          <div class="form-field">
            <span class="field-label">إلى الساعة:</span>
            <span class="field-input" style="width: 80px;"></span>
          </div>
          <div class="form-field">
            <span class="field-label">عدد الساعات:</span>
            <span class="field-input" style="width: 50px;"></span>
          </div>
        </div>
        <div class="form-row">
          <span class="field-label">سبب الإذن:</span>
        </div>
        <div class="field-input" style="width: 100%; height: 60px; border: 1px solid #000; margin-top: 5px;"></div>
      </div>

      <div class="signature-section">
        <div class="signature-box">
          <div style="margin-bottom: 40px;">توقيع الموظف</div>
          <div>التاريخ: <span class="date-field"></span></div>
        </div>
        <div class="signature-box">
          <div style="margin-bottom: 40px;">توقيع المدير المباشر</div>
          <div>التاريخ: <span class="date-field"></span></div>
        </div>
        <div class="signature-box">
          <div style="margin-bottom: 40px;">اعتماد إدارة الموارد البشرية</div>
          <div>التاريخ: <span class="date-field"></span></div>
        </div>
      </div>
    `;
  };

  const generateNightShiftForm = () => {
    return `
      <div class="header">
        <div class="company-name">شركة [اسم الشركة]</div>
        <div>إدارة الموارد البشرية</div>
      </div>

      <div class="form-title">نموذج إذن الوردية الليلية</div>

      <div class="form-section">
        <div class="section-title">بيانات الموظف</div>
        <div class="form-row">
          <div class="form-field">
            <span class="field-label">اسم الموظف:</span>
            <span class="field-input"></span>
          </div>
          <div class="form-field">
            <span class="field-label">رقم الموظف:</span>
            <span class="field-input"></span>
          </div>
        </div>
        <div class="form-row">
          <div class="form-field">
            <span class="field-label">القسم:</span>
            <span class="field-input"></span>
          </div>
          <div class="form-field">
            <span class="field-label">المسمى الوظيفي:</span>
            <span class="field-input"></span>
          </div>
        </div>
      </div>

      <div class="form-section">
        <div class="section-title">تفاصيل الوردية الليلية</div>
        <div class="form-row">
          <div class="form-field">
            <span class="field-label">من تاريخ:</span>
            <span class="date-field"></span>
          </div>
          <div class="form-field">
            <span class="field-label">إلى تاريخ:</span>
            <span class="date-field"></span>
          </div>
        </div>
        <div class="form-row">
          <div class="form-field">
            <span class="field-label">من الساعة:</span>
            <span class="field-input" style="width: 80px;"></span>
          </div>
          <div class="form-field">
            <span class="field-label">إلى الساعة:</span>
            <span class="field-input" style="width: 80px;"></span>
          </div>
        </div>
        <div class="form-row">
          <span class="field-label">سبب العمل الليلي:</span>
        </div>
        <div class="field-input" style="width: 100%; height: 60px; border: 1px solid #000; margin-top: 5px;"></div>
      </div>

      <div class="form-section">
        <div class="section-title">معلومات إضافية</div>
        <div class="form-row">
          <span class="field-label">وسيلة المواصلات:</span>
          <span class="checkbox"></span> سيارة الشركة
          <span class="checkbox"></span> مواصلات شخصية
          <span class="checkbox"></span> أخرى: <span class="field-input" style="width: 100px;"></span>
        </div>
        <div class="form-row">
          <div class="form-field">
            <span class="field-label">جهة الاتصال في الطوارئ:</span>
            <span class="field-input"></span>
          </div>
          <div class="form-field">
            <span class="field-label">رقم الهاتف:</span>
            <span class="field-input"></span>
          </div>
        </div>
      </div>

      <div class="signature-section">
        <div class="signature-box">
          <div style="margin-bottom: 40px;">توقيع الموظف</div>
          <div>التاريخ: <span class="date-field"></span></div>
        </div>
        <div class="signature-box">
          <div style="margin-bottom: 40px;">توقيع المدير المباشر</div>
          <div>التاريخ: <span class="date-field"></span></div>
        </div>
        <div class="signature-box">
          <div style="margin-bottom: 40px;">اعتماد إدارة الموارد البشرية</div>
          <div>التاريخ: <span class="date-field"></span></div>
        </div>
      </div>
    `;
  };

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-4xl mx-auto">
          {/* العنوان */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-200 mb-2">
                  {isArabic ? 'طباعة النماذج الورقية' : 'Print Paper Forms'}
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  {isArabic ? 'طباعة النماذج الفارغة للاستخدام الورقي' : 'Print blank forms for paper use'}
                </p>
              </div>
              <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <FiPrinter className="text-2xl text-white" />
              </div>
            </div>
          </div>

          {/* النماذج المتاحة للطباعة */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {formTypes.map((form) => (
              <div
                key={form.id}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow"
              >
                <div className={`w-12 h-12 rounded-lg flex items-center justify-center mb-4 bg-${form.color}-100 dark:bg-${form.color}-900/20`}>
                  <form.icon className={`text-xl text-${form.color}-600 dark:text-${form.color}-400`} />
                </div>
                
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">
                  {form.title}
                </h3>
                
                <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                  {isArabic ? 'نموذج ورقي جاهز للطباعة والاستخدام' : 'Ready-to-print paper form'}
                </p>
                
                <div className="flex gap-2">
                  <button
                    onClick={() => printBlankForm(form.id)}
                    className={`flex-1 bg-${form.color}-600 text-white px-4 py-2 rounded-lg hover:bg-${form.color}-700 transition-colors flex items-center justify-center gap-2 text-sm`}
                  >
                    <FiPrinter className="text-sm" />
                    {isArabic ? 'طباعة النموذج' : 'Print Form'}
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* معلومات إضافية */}
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 mt-6">
            <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-3">
              {isArabic ? 'تعليمات الطباعة' : 'Printing Instructions'}
            </h3>
            <ul className="text-blue-700 dark:text-blue-300 space-y-2 text-sm">
              <li>• {isArabic ? 'استخدم ورق A4 أبيض للحصول على أفضل النتائج' : 'Use white A4 paper for best results'}</li>
              <li>• {isArabic ? 'تأكد من ضبط الطابعة على الطباعة بجودة عالية' : 'Set printer to high quality printing'}</li>
              <li>• {isArabic ? 'النماذج مصممة للكتابة اليدوية بالقلم الأزرق أو الأسود' : 'Forms are designed for handwriting with blue or black pen'}</li>
              <li>• {isArabic ? 'يجب ملء جميع الحقول المطلوبة والحصول على التوقيعات اللازمة' : 'Fill all required fields and obtain necessary signatures'}</li>
            </ul>
          </div>

          {/* زر العودة */}
          <div className="flex justify-start mt-6">
            <button
              onClick={() => router.back()}
              className="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors flex items-center gap-2"
            >
              <FiArrowLeft />
              {isArabic ? 'رجوع' : 'Back'}
            </button>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default PrintRequestsPage;
