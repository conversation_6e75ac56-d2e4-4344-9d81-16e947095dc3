import { NextResponse } from 'next/server';
import sql from 'mssql';
import { config, getConnection } from '@/lib/db';

export async function POST(request) {
  try {
    const { code, password } = await request.json();

    if (!code || !password) {
      return NextResponse.json(
        { error: 'يجب إدخال رمز الموظف وكلمة المرور' },
        { status: 400 }
      );
    }

    const pool = await getConnection();
    const result = await pool.request()
      .input('code', code)
      .input('password', password)
      .query('SELECT * FROM LOGIN WHERE code = @code AND password = @password');

    const user = result.recordset[0];

    if (!user) {
      await pool.request()
        .input('description', `محاولة تسجيل دخول فاشلة لرمز الموظف: ${code}`)
        .query(`
          INSERT INTO activity_log 
          (action_type, entity_type, description)
          VALUES 
          ('LOGIN_FAILED', 'USER', @description)
        `);
      return NextResponse.json(
        { error: 'رمز الموظف أو كلمة المرور غير صحيحة' },
        { status: 401 }
      );
    }

    await pool.request()
      .input('code', code)
      .query(`
        INSERT INTO activity_log 
        (action_type, entity_type, description)
        VALUES 
        ('LOGIN_SUCCESS', 'USER', 'تم تسجيل الدخول بنجاح')
      `);

    return NextResponse.json({
      success: true,
      user: {
        code: user.code,
        name: user.name || 'مستخدم',
        role: user.role || 'employee'
      }
    });
  } catch (error) {

    return NextResponse.json(
      { error: 'حدث خطأ أثناء تسجيل الدخول' },
      { status: 500 }
    );
  }
}
