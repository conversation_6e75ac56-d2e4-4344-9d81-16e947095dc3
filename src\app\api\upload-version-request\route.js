import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

// رفع طلب إصدار جديد
export async function POST(request) {
  try {
    const formData = await request.formData();
    const file = formData.get('file');
    const month = formData.get('month');
    const year = formData.get('year');
    const type = formData.get('type') || 'carscost';

    if (!file || !month || !year) {
      return NextResponse.json({
        success: false,
        message: 'يرجى تحديد الملف والشهر والسنة'
      }, { status: 400 });
    }

    // التحقق من نوع الملف
    if (!file.name.toLowerCase().endsWith('.pdf')) {
      return NextResponse.json({
        success: false,
        message: 'يجب أن يكون الملف من نوع PDF'
      }, { status: 400 });
    }

    const pool = await getConnection();

    // تحويل رقم الشهر إلى اسم الشهر
    const monthNames = {
      '1': 'يناير', '2': 'فبراير', '3': 'مارس', '4': 'أبريل',
      '5': 'مايو', '6': 'يونيو', '7': 'يوليو', '8': 'أغسطس',
      '9': 'سبتمبر', '10': 'أكتوبر', '11': 'نوفمبر', '12': 'ديسمبر'
    };

    const monthName = monthNames[month] || month;

    // إنشاء اسم الملف بالتنسيق المطلوب
    const fileName = `${month}-${year}.pdf`;
    const relativePath = `archiv/${type}/${fileName}`;
    const fullPath = join(process.cwd(), relativePath);

    // إنشاء المجلد إذا لم يكن موجوداً
    const dirPath = join(process.cwd(), `archiv/${type}`);
    if (!existsSync(dirPath)) {
      await mkdir(dirPath, { recursive: true });
    }

    // التحقق من وجود طلب إصدار مسبق
    const existingQuery = `
      SELECT ID FROM Archive 
      WHERE Item = @Type AND Path LIKE '%${month}-${year}%'
    `;

    const existingRequest = pool.request();
    existingRequest.input('Type', sql.NVarChar, type);
    const existingResult = await existingRequest.query(existingQuery);

    if (existingResult.recordset.length > 0) {
      // تحديث الملف الموجود

    } else {
      // إضافة سجل جديد في جدول Archive
      const insertQuery = `
        INSERT INTO Archive (EmployeeID, Item, Path) 
        VALUES (@EmployeeID, @Item, @Path)
      `;

      const insertRequest = pool.request();
      insertRequest.input('EmployeeID', sql.NVarChar, 'SYSTEM');
      insertRequest.input('Item', sql.NVarChar, type);
      insertRequest.input('Path', sql.NVarChar, relativePath);

      await insertRequest.query(insertQuery);

    }

    // حفظ الملف
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(fullPath, buffer);

    return NextResponse.json({
      success: true,
      message: `تم رفع طلب إصدار ${monthName} ${year} بنجاح`,
      data: {
        fileName: fileName,
        path: relativePath,
        month: monthName,
        year: year,
        type: type
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'حدث خطأ في رفع طلب الإصدار',
      error: error.message
    }, { status: 500 });
  }
}

// جلب قائمة طلبات الإصدار
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'carscost';
    const year = searchParams.get('year');

    const pool = await getConnection();

    let whereClause = 'WHERE Item = @Type';
    if (year) {
      whereClause += ` AND Path LIKE '%${year}%'`;
    }

    const query = `
      SELECT 
        ID,
        Path,
        CreatedAt,
        CASE 
          WHEN Path LIKE '%1-${year || '____'}%' THEN 'يناير'
          WHEN Path LIKE '%2-${year || '____'}%' THEN 'فبراير'
          WHEN Path LIKE '%3-${year || '____'}%' THEN 'مارس'
          WHEN Path LIKE '%4-${year || '____'}%' THEN 'أبريل'
          WHEN Path LIKE '%5-${year || '____'}%' THEN 'مايو'
          WHEN Path LIKE '%6-${year || '____'}%' THEN 'يونيو'
          WHEN Path LIKE '%7-${year || '____'}%' THEN 'يوليو'
          WHEN Path LIKE '%8-${year || '____'}%' THEN 'أغسطس'
          WHEN Path LIKE '%9-${year || '____'}%' THEN 'سبتمبر'
          WHEN Path LIKE '%10-${year || '____'}%' THEN 'أكتوبر'
          WHEN Path LIKE '%11-${year || '____'}%' THEN 'نوفمبر'
          WHEN Path LIKE '%12-${year || '____'}%' THEN 'ديسمبر'
          ELSE 'غير محدد'
        END as MonthName,
        CASE 
          WHEN Path LIKE '%1-${year || '____'}%' THEN 1
          WHEN Path LIKE '%2-${year || '____'}%' THEN 2
          WHEN Path LIKE '%3-${year || '____'}%' THEN 3
          WHEN Path LIKE '%4-${year || '____'}%' THEN 4
          WHEN Path LIKE '%5-${year || '____'}%' THEN 5
          WHEN Path LIKE '%6-${year || '____'}%' THEN 6
          WHEN Path LIKE '%7-${year || '____'}%' THEN 7
          WHEN Path LIKE '%8-${year || '____'}%' THEN 8
          WHEN Path LIKE '%9-${year || '____'}%' THEN 9
          WHEN Path LIKE '%10-${year || '____'}%' THEN 10
          WHEN Path LIKE '%11-${year || '____'}%' THEN 11
          WHEN Path LIKE '%12-${year || '____'}%' THEN 12
          ELSE 0
        END as MonthNumber
      FROM Archive 
      ${whereClause}
      ORDER BY MonthNumber DESC, CreatedAt DESC
    `;

    const queryRequest = pool.request();
    queryRequest.input('Type', sql.NVarChar, type);
    const result = await queryRequest.query(query);

    return NextResponse.json({
      success: true,
      reports: result.recordset
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'حدث خطأ في جلب طلبات الإصدار',
      error: error.message
    }, { status: 500 });
  }
}
