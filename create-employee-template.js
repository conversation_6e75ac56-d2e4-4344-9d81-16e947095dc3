const ExcelJS = require('exceljs');
const fs = require('fs');

async function createEmployeeTemplate() {

  try {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('الموظفين');

    // تعريف العناوين (محدث للتصميم الجديد)
    const headers = [
      { key: 'A', value: 'كود الموظف', width: 15 },
      { key: 'B', value: 'الاسم الكامل', width: 25 },
      { key: 'C', value: 'المسمى الوظيفي', width: 20 },
      { key: 'D', value: 'القسم', width: 20 },
      { key: 'E', value: 'المدير المباشر', width: 25 },
      { key: 'F', value: 'الرقم القومي', width: 15 },
      { key: 'G', value: 'تاريخ الميلاد', width: 15 },
      { key: 'H', value: 'النوع', width: 10 },
      { key: 'I', value: 'المحافظة', width: 15 },
      { key: 'J', value: 'المنطقة', width: 20 },
      { key: 'K', value: 'الحالة الاجتماعية', width: 15 },
      { key: 'L', value: 'رقم الجوال', width: 15 },
      { key: 'M', value: 'البريد الإلكتروني', width: 25 },
      { key: 'N', value: 'رقم الطوارئ', width: 15 },
      { key: 'O', value: 'صلة القرابة', width: 15 },
      { key: 'P', value: 'تاريخ التعيين', width: 15 },
      { key: 'Q', value: 'تاريخ الانضمام', width: 15 },
      { key: 'R', value: 'حالة الموظف', width: 15 },
      { key: 'S', value: 'الخدمة العسكرية', width: 15 },
      { key: 'T', value: 'مغترب', width: 10 },
      { key: 'U', value: 'المؤهل', width: 15 },
      { key: 'V', value: 'الجامعة', width: 20 },
      { key: 'W', value: 'التخصص', width: 20 },
      { key: 'X', value: 'التقدير', width: 15 },
      { key: 'Y', value: 'سنة التخرج', width: 15 },
      { key: 'Z', value: 'التأمين الاجتماعي', width: 15 },
      { key: 'AA', value: 'رقم التأمين الاجتماعي', width: 20 },
      { key: 'AB', value: 'تاريخ التأمين الاجتماعي', width: 20 },
      { key: 'AC', value: 'التأمين الطبي', width: 15 },
      { key: 'AD', value: 'رقم التأمين الطبي', width: 20 }
    ];

    // إضافة العناوين
    const headerRow = worksheet.getRow(1);
    headers.forEach((header, index) => {
      const cell = headerRow.getCell(index + 1);
      cell.value = header.value;
      cell.font = { bold: true, color: { argb: 'FFFFFF' } };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '366092' }
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      
      // تعيين عرض العمود
      worksheet.getColumn(index + 1).width = header.width;
    });

    // إضافة صف مثال
    const exampleRow = worksheet.getRow(2);
    const exampleData = [
      '1001', // كود الموظف
      'أحمد محمد علي', // الاسم الكامل
      'مهندس مدني', // المسمى الوظيفي
      'الهندسة', // القسم
      'محمد أحمد', // المدير المباشر
      '12345678901234', // الرقم القومي
      '1990-01-15', // تاريخ الميلاد
      'ذكر', // النوع
      'القاهرة', // المحافظة
      'مدينة نصر', // المنطقة
      'متزوج', // الحالة الاجتماعية
      '01234567890', // رقم الجوال
      '<EMAIL>', // البريد الإلكتروني
      '01987654321', // رقم الطوارئ
      'أخ', // صلة القرابة
      '2020-01-01', // تاريخ التعيين
      '2020-01-15', // تاريخ الانضمام
      'سارى', // حالة الموظف
      'أدى الخدمة', // الخدمة العسكرية
      'لا', // مغترب (نعم/لا)
      'بكالوريوس', // المؤهل
      'جامعة القاهرة', // الجامعة
      'هندسة مدنية', // التخصص
      'جيد جداً', // التقدير
      '2015', // سنة التخرج
      'مؤمن', // التأمين الاجتماعي
      '123456789', // رقم التأمين الاجتماعي
      '2020-01-01', // تاريخ التأمين الاجتماعي
      'مؤمن', // التأمين الطبي
      '987654321' // رقم التأمين الطبي
    ];

    exampleData.forEach((value, index) => {
      const cell = exampleRow.getCell(index + 1);
      cell.value = value;
      cell.font = { color: { argb: '666666' }, italic: true };
      cell.alignment = { horizontal: 'center', vertical: 'middle' };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });

    // إضافة ورقة التعليمات
    const instructionsSheet = workbook.addWorksheet('التعليمات');
    
    const instructions = [
      ['📋 تعليمات استخدام قالب رفع بيانات الموظفين'],
      [''],
      ['🔹 الحقول المطلوبة (يجب ملؤها):'],
      ['   • كود الموظف: رقم فريد لكل موظف'],
      ['   • الاسم الكامل: الاسم الثلاثي أو الرباعي'],
      ['   • المسمى الوظيفي: الوظيفة الحالية'],
      ['   • القسم: القسم التابع له الموظف'],
      ['   • المدير المباشر: اسم المدير المباشر'],
      ['   • الرقم القومي: 14 رقم'],
      ['   • تاريخ الميلاد: بصيغة YYYY-MM-DD'],
      ['   • النوع: ذكر أو أنثى'],
      ['   • المحافظة: محافظة الإقامة'],
      ['   • الحالة الاجتماعية: أعزب، متزوج، مطلق، أرمل'],
      ['   • تاريخ التعيين: بصيغة YYYY-MM-DD'],
      ['   • تاريخ الانضمام: بصيغة YYYY-MM-DD'],
      ['   • حالة الموظف: سارى، منقول، مستقيل'],
      ['   • الخدمة العسكرية: أدى الخدمة، معفى، مؤجل'],
      ['   • مغترب: نعم أو لا'],
      [''],
      ['🔹 الحقول الاختيارية:'],
      ['   • المنطقة: منطقة الإقامة التفصيلية'],
      ['   • رقم الجوال: رقم الهاتف المحمول'],
      ['   • البريد الإلكتروني: عنوان البريد الإلكتروني'],
      ['   • رقم الطوارئ: رقم للتواصل في الطوارئ'],
      ['   • صلة القرابة: صلة القرابة لرقم الطوارئ'],
      ['   • المؤهل: المؤهل الدراسي'],
      ['   • الجامعة: الجامعة التي تخرج منها'],
      ['   • التخصص: التخصص الدراسي'],
      ['   • التقدير: تقدير التخرج'],
      ['   • سنة التخرج: سنة التخرج'],
      ['   • التأمين الاجتماعي: مؤمن أو غير مؤمن'],
      ['   • رقم التأمين الاجتماعي: رقم التأمين'],
      ['   • تاريخ التأمين الاجتماعي: تاريخ بداية التأمين'],
      ['   • التأمين الطبي: مؤمن أو غير مؤمن'],
      ['   • رقم التأمين الطبي: رقم التأمين الطبي'],
      [''],
      ['⚠️ ملاحظات مهمة:'],
      ['   • لا تحذف الصف الأول (العناوين)'],
      ['   • احذف الصف المثال قبل إدراج البيانات الحقيقية'],
      ['   • تأكد من صحة التواريخ (YYYY-MM-DD)'],
      ['   • تأكد من أن الرقم القومي 14 رقم'],
      ['   • لا تترك الحقول المطلوبة فارغة'],
      ['   • استخدم "نعم" أو "لا" في حقل المغترب'],
      ['   • استخدم "مؤمن" أو "غير مؤمن" في حقول التأمين'],
      [''],
      ['🏠 إدارة الشقق والسيارات:'],
      ['   • تم فصل بيانات الشقق والسيارات عن جدول الموظفين'],
      ['   • سيتم إدارة المستفيدين من خلال نماذج منفصلة'],
      ['   • هذا يضمن مرونة أكبر في إدارة الاستفادة'],
      [''],
      ['📊 الفرق بين المغترب والمقيم:'],
      ['   • المغترب: موظف من محافظة أخرى (نعم/لا)'],
      ['   • المقيم في شقة الشركة: يُدار من جدول المستفيدين'],
      ['   • هذان مفهومان منفصلان ومستقلان'],
      [''],
      ['✅ بعد الرفع:'],
      ['   • راجع النتائج والأخطاء إن وجدت'],
      ['   • تأكد من صحة البيانات المدرجة'],
      ['   • أضف بيانات الشقق والسيارات من النماذج المخصصة']
    ];

    instructions.forEach((instruction, rowIndex) => {
      const cell = instructionsSheet.getCell(rowIndex + 1, 1);
      cell.value = instruction[0];
      
      if (rowIndex === 0) {
        // العنوان الرئيسي
        cell.font = { bold: true, size: 16, color: { argb: '366092' } };
        cell.alignment = { horizontal: 'center' };
      } else if (instruction[0].startsWith('🔹') || instruction[0].startsWith('⚠️') || 
                 instruction[0].startsWith('🏠') || instruction[0].startsWith('📊') || 
                 instruction[0].startsWith('✅')) {
        // العناوين الفرعية
        cell.font = { bold: true, size: 12, color: { argb: '2F5597' } };
      } else if (instruction[0].startsWith('   •')) {
        // النقاط
        cell.font = { size: 10 };
        cell.alignment = { indent: 1 };
      }
    });

    // تعيين عرض العمود في ورقة التعليمات
    instructionsSheet.getColumn(1).width = 80;

    // حفظ الملف
    const fileName = 'employee_upload_template.xlsx';
    await workbook.xlsx.writeFile(fileName);

    const requiredFields = [
      'كود الموظف', 'الاسم الكامل', 'المسمى الوظيفي', 'القسم', 'المدير المباشر',
      'الرقم القومي', 'تاريخ الميلاد', 'النوع', 'المحافظة', 'الحالة الاجتماعية',
      'تاريخ التعيين', 'تاريخ الانضمام', 'حالة الموظف', 'الخدمة العسكرية', 'مغترب'
    ];
    requiredFields.forEach((field, index) => {

    });

    const optionalFields = [
      'المنطقة', 'رقم الجوال', 'البريد الإلكتروني', 'رقم الطوارئ', 'صلة القرابة',
      'المؤهل', 'الجامعة', 'التخصص', 'التقدير', 'سنة التخرج',
      'التأمين الاجتماعي', 'رقم التأمين الاجتماعي', 'تاريخ التأمين الاجتماعي',
      'التأمين الطبي', 'رقم التأمين الطبي'
    ];
    optionalFields.forEach((field, index) => {

    });

    console.log('   • المغترب ≠ المقيم في شقة الشركة (مفهومان منفصلان)');

  } catch (error) {

  }
}

createEmployeeTemplate().catch(console.error);
