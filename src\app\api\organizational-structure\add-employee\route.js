import { NextResponse } from 'next/server';
import sql from 'mssql';

// إعدادات قاعدة البيانات
const dbConfig = {
  user: 'SA',
  password: 'admin@123',
  server: 'localhost\\DBOJESTA',
  database: 'EMP',
  options: {
    encrypt: false,
    trustServerCertificate: true,
    enableArithAbort: true,
    instanceName: 'DBOJESTA'
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
  requestTimeout: 30000,
  connectionTimeout: 30000,
};

// POST - إضافة موظف إلى وحدة تنظيمية
export async function POST(request) {
  let pool;
  
  try {
    const body = await request.json();
    const { employeeCode, unitId, position } = body;

    if (!employeeCode || !unitId) {
      return NextResponse.json({
        success: false,
        error: 'كود الموظف ومعرف الوحدة مطلوبان'
      }, { status: 400 });
    }

    pool = await sql.connect(dbConfig);

    // التحقق من وجود الموظف
    const employeeResult = await pool.request()
      .input('employeeCode', employeeCode)
      .query('SELECT EmployeeName, JobTitle FROM Employees WHERE EmployeeCode = @employeeCode AND IsActive = 1');

    if (employeeResult.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الموظف غير موجود أو غير نشط'
      }, { status: 404 });
    }

    // التحقق من وجود الوحدة التنظيمية
    const unitResult = await pool.request()
      .input('unitId', unitId)
      .query('SELECT UnitName FROM OrganizationalUnits WHERE ID = @unitId AND IsActive = 1');

    if (unitResult.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الوحدة التنظيمية غير موجودة أو غير نشطة'
      }, { status: 404 });
    }

    // التحقق من عدم وجود الموظف في نفس الوحدة مسبقاً
    const existingResult = await pool.request()
      .input('employeeCode', employeeCode)
      .input('unitId', unitId)
      .query(`
        SELECT COUNT(*) as count 
        FROM EmployeeUnits 
        WHERE EmployeeCode = @employeeCode 
          AND UnitID = @unitId 
          AND IsActive = 1
      `);

    if (existingResult.recordset[0].count > 0) {
      return NextResponse.json({
        success: false,
        error: 'الموظف موجود بالفعل في هذه الوحدة التنظيمية'
      }, { status: 400 });
    }

    const employee = employeeResult.recordset[0];
    const unit = unitResult.recordset[0];

    // إضافة الموظف إلى الوحدة التنظيمية
    await pool.request()
      .input('employeeCode', employeeCode)
      .input('unitId', unitId)
      .input('position', position || employee.JobTitle || 'موظف')
      .query(`
        INSERT INTO EmployeeUnits (EmployeeCode, UnitID, Position, IsDirectManager)
        VALUES (@employeeCode, @unitId, @position, 0)
      `);

    return NextResponse.json({
      success: true,
      message: `تم إضافة ${employee.EmployeeName} إلى ${unit.UnitName} بنجاح`
    });

  } catch (error) {
    console.error('❌ خطأ في إضافة الموظف:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في إضافة الموظف: ' + error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      await pool.close();
    }
  }
}

// GET - جلب الموظفين في وحدة معينة
export async function GET(request) {
  let pool;
  
  try {
    const { searchParams } = new URL(request.url);
    const unitId = searchParams.get('unitId');

    if (!unitId) {
      return NextResponse.json({
        success: false,
        error: 'معرف الوحدة مطلوب'
      }, { status: 400 });
    }

    pool = await sql.connect(dbConfig);

    // جلب الموظفين في الوحدة
    const result = await pool.request()
      .input('unitId', unitId)
      .query(`
        SELECT 
          eu.EmployeeCode,
          emp.EmployeeName,
          eu.Position,
          eu.IsDirectManager,
          eu.AssignmentDate,
          emp.JobTitle,
          emp.Area
        FROM EmployeeUnits eu
        INNER JOIN Employees emp ON eu.EmployeeCode = emp.EmployeeCode
        WHERE eu.UnitID = @unitId 
          AND eu.IsActive = 1 
          AND emp.IsActive = 1
        ORDER BY eu.IsDirectManager DESC, eu.AssignmentDate
      `);

    return NextResponse.json({
      success: true,
      data: result.recordset,
      message: 'تم جلب الموظفين بنجاح'
    });

  } catch (error) {
    console.error('❌ خطأ في جلب الموظفين:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب الموظفين: ' + error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      await pool.close();
    }
  }
}

// DELETE - إزالة موظف من وحدة تنظيمية
export async function DELETE(request) {
  let pool;
  
  try {
    const { searchParams } = new URL(request.url);
    const employeeCode = searchParams.get('employeeCode');
    const unitId = searchParams.get('unitId');

    if (!employeeCode || !unitId) {
      return NextResponse.json({
        success: false,
        error: 'كود الموظف ومعرف الوحدة مطلوبان'
      }, { status: 400 });
    }

    pool = await sql.connect(dbConfig);

    // التحقق من وجود الموظف في الوحدة
    const existingResult = await pool.request()
      .input('employeeCode', employeeCode)
      .input('unitId', unitId)
      .query(`
        SELECT eu.IsDirectManager, emp.EmployeeName, ou.UnitName
        FROM EmployeeUnits eu
        INNER JOIN Employees emp ON eu.EmployeeCode = emp.EmployeeCode
        INNER JOIN OrganizationalUnits ou ON eu.UnitID = ou.ID
        WHERE eu.EmployeeCode = @employeeCode 
          AND eu.UnitID = @unitId 
          AND eu.IsActive = 1
      `);

    if (existingResult.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الموظف غير موجود في هذه الوحدة'
      }, { status: 404 });
    }

    const assignment = existingResult.recordset[0];

    // منع حذف المدير المباشر
    if (assignment.IsDirectManager) {
      return NextResponse.json({
        success: false,
        error: 'لا يمكن إزالة المدير المباشر من الوحدة. يجب تعيين مدير آخر أولاً.'
      }, { status: 400 });
    }

    // إزالة الموظف من الوحدة (soft delete)
    await pool.request()
      .input('employeeCode', employeeCode)
      .input('unitId', unitId)
      .query(`
        UPDATE EmployeeUnits 
        SET IsActive = 0 
        WHERE EmployeeCode = @employeeCode 
          AND UnitID = @unitId
      `);

    return NextResponse.json({
      success: true,
      message: `تم إزالة ${assignment.EmployeeName} من ${assignment.UnitName} بنجاح`
    });

  } catch (error) {
    console.error('❌ خطأ في إزالة الموظف:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في إزالة الموظف: ' + error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      await pool.close();
    }
  }
}
