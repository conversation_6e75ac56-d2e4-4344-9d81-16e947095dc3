'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import {
  FiDatabase,
  FiCheck,
  FiX,
  FiRefreshCw,
  FiPlay,
  FiTrash2,
  FiSettings,
  FiInfo,
  FiAlertTriangle,
  FiCheckCircle
} from 'react-icons/fi';

export default function DatabaseSetupPage() {
  const [databaseStatus, setDatabaseStatus] = useState(null);
  const [loading, setLoading] = useState(false);
  const [setupProgress, setSetupProgress] = useState([]);
  const [activeTab, setActiveTab] = useState('status');

  useEffect(() => {
    checkDatabaseStatus();
  }, []);

  // فحص حالة قاعدة البيانات
  const checkDatabaseStatus = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/setup-database', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'checkDatabase' })
      });

      const result = await response.json();
      if (result.success) {
        setDatabaseStatus(result.data);
      } else {

      }
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  // إعداد قاعدة البيانات الكاملة
  const setupCompleteDatabase = async () => {
    if (!confirm('هل أنت متأكد من إعداد قاعدة البيانات؟ سيتم حذف جميع البيانات التجريبية.')) {
      return;
    }

    try {
      setLoading(true);
      setSetupProgress([]);

      const response = await fetch('/api/setup-database', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'setupAll' })
      });

      const result = await response.json();
      if (result.success) {
        setSetupProgress(result.steps || []);
        alert('تم إعداد قاعدة البيانات بنجاح!');
        await checkDatabaseStatus();
      } else {
        alert('خطأ في إعداد قاعدة البيانات: ' + result.error);
      }
    } catch (error) {

      alert('حدث خطأ في إعداد قاعدة البيانات');
    } finally {
      setLoading(false);
    }
  };

  // تنظيف البيانات التجريبية فقط
  const cleanTestData = async () => {
    if (!confirm('هل أنت متأكد من حذف البيانات التجريبية؟')) {
      return;
    }

    try {
      setLoading(true);
      const response = await fetch('/api/setup-database', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'cleanTestData' })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم تنظيف البيانات التجريبية بنجاح!');
        await checkDatabaseStatus();
      } else {
        alert('خطأ في تنظيف البيانات: ' + result.error);
      }
    } catch (error) {

      alert('حدث خطأ في تنظيف البيانات');
    } finally {
      setLoading(false);
    }
  };

  // إنشاء الجداول المفقودة
  const createMissingTables = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/setup-database', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'createMissingTables' })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم إنشاء الجداول المفقودة بنجاح!');
        await checkDatabaseStatus();
      } else {
        alert('خطأ في إنشاء الجداول: ' + result.error);
      }
    } catch (error) {

      alert('حدث خطأ في إنشاء الجداول');
    } finally {
      setLoading(false);
    }
  };

  // أيقونة حالة الجدول
  const getTableStatusIcon = (table) => {
    if (!table.exists) {
      return <FiX className="w-5 h-5 text-red-500" />;
    }
    return <FiCheck className="w-5 h-5 text-green-500" />;
  };

  // لون حالة الجدول
  const getTableStatusColor = (table) => {
    if (!table.exists) {
      return 'bg-red-50 border-red-200 text-red-800';
    }
    if (table.records === 0) {
      return 'bg-yellow-50 border-yellow-200 text-yellow-800';
    }
    return 'bg-green-50 border-green-200 text-green-800';
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto p-6">
        {/* رأس الصفحة */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FiDatabase className="text-3xl text-blue-600" />
              <div>
                <h1 className="text-3xl font-bold text-gray-800 dark:text-white">
                  إعداد قاعدة البيانات
                </h1>
                <p className="text-gray-600 dark:text-gray-300">
                  إدارة وإعداد جداول قاعدة البيانات وتنظيف البيانات التجريبية
                </p>
              </div>
            </div>
            <button
              onClick={checkDatabaseStatus}
              disabled={loading}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2 disabled:opacity-50"
            >
              <FiRefreshCw className={loading ? 'animate-spin' : ''} />
              تحديث الحالة
            </button>
          </div>
        </div>

        {/* الإحصائيات العامة */}
        {databaseStatus && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border-l-4 border-blue-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
                    الجداول الموجودة
                  </p>
                  <p className="text-3xl font-bold text-blue-600">
                    {Object.values(databaseStatus.tableStatus).filter(t => t.exists).length}
                  </p>
                </div>
                <FiDatabase className="text-2xl text-blue-600" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border-l-4 border-green-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
                    إجمالي السجلات
                  </p>
                  <p className="text-3xl font-bold text-green-600">
                    {databaseStatus.totalRecords.toLocaleString()}
                  </p>
                </div>
                <FiCheckCircle className="text-2xl text-green-600" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border-l-4 border-yellow-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
                    بيانات تجريبية
                  </p>
                  <p className="text-3xl font-bold text-yellow-600">
                    {(databaseStatus.testDataRemaining.tempWorkers || 0) + 
                     (databaseStatus.testDataRemaining.apartments || 0)}
                  </p>
                </div>
                <FiAlertTriangle className="text-2xl text-yellow-600" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border-l-4 border-purple-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
                    حالة النظام
                  </p>
                  <p className={`text-lg font-bold ${
                    databaseStatus.databaseReady ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {databaseStatus.databaseReady ? 'جاهز' : 'غير جاهز'}
                  </p>
                </div>
                {databaseStatus.databaseReady ? (
                  <FiCheckCircle className="text-2xl text-green-600" />
                ) : (
                  <FiAlertTriangle className="text-2xl text-red-600" />
                )}
              </div>
            </div>
          </div>
        )}

        {/* أزرار الإجراءات */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
            إجراءات قاعدة البيانات
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={setupCompleteDatabase}
              disabled={loading}
              className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 flex items-center justify-center gap-2 disabled:opacity-50"
            >
              <FiPlay />
              إعداد كامل للنظام
            </button>
            
            <button
              onClick={createMissingTables}
              disabled={loading}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 flex items-center justify-center gap-2 disabled:opacity-50"
            >
              <FiSettings />
              إنشاء الجداول المفقودة
            </button>
            
            <button
              onClick={cleanTestData}
              disabled={loading}
              className="bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 flex items-center justify-center gap-2 disabled:opacity-50"
            >
              <FiTrash2 />
              حذف البيانات التجريبية
            </button>
          </div>
        </div>

        {/* التبويبات والمحتوى */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
          {/* التبويبات */}
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('status')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'status'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <FiInfo />
                  حالة الجداول
                </div>
              </button>

              <button
                onClick={() => setActiveTab('progress')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'progress'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <FiSettings />
                  سجل الإعداد
                </div>
              </button>
            </nav>
          </div>

          {/* محتوى التبويبات */}
          <div className="p-6">
            {activeTab === 'status' && databaseStatus && (
              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                  حالة جداول قاعدة البيانات
                </h3>

                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          اسم الجدول
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          الحالة
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          عدد السجلات
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          الوصف
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {Object.entries(databaseStatus.tableStatus).map(([tableName, table]) => (
                        <tr key={tableName} className={`${getTableStatusColor(table)} border-l-4`}>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center gap-2">
                              {getTableStatusIcon(table)}
                              <span className="text-sm font-medium">
                                {tableName}
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              table.exists
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {table.exists ? 'موجود' : 'مفقود'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm">
                            {table.exists ? (
                              <span className="font-medium">
                                {table.records?.toLocaleString() || 0}
                              </span>
                            ) : (
                              <span className="text-gray-400">-</span>
                            )}
                          </td>
                          <td className="px-6 py-4 text-sm">
                            {getTableDescription(tableName)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* معلومات البيانات التجريبية */}
                {(databaseStatus.testDataRemaining.tempWorkers > 0 ||
                  databaseStatus.testDataRemaining.apartments > 0) && (
                  <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <FiAlertTriangle className="text-yellow-600" />
                      <h4 className="font-semibold text-yellow-800">
                        تحذير: توجد بيانات تجريبية
                      </h4>
                    </div>
                    <ul className="text-sm text-yellow-700 space-y-1">
                      {databaseStatus.testDataRemaining.tempWorkers > 0 && (
                        <li>• {databaseStatus.testDataRemaining.tempWorkers} عامل مؤقت تجريبي</li>
                      )}
                      {databaseStatus.testDataRemaining.apartments > 0 && (
                        <li>• {databaseStatus.testDataRemaining.apartments} شقة تجريبية</li>
                      )}
                    </ul>
                    <p className="text-sm text-yellow-700 mt-2">
                      يُنصح بحذف هذه البيانات قبل البدء في استخدام النظام فعلياً.
                    </p>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'progress' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                  سجل عمليات الإعداد
                </h3>

                {setupProgress.length === 0 ? (
                  <div className="text-center py-8">
                    <FiSettings className="text-4xl text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500">لم يتم تشغيل أي عملية إعداد بعد</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {setupProgress.map((step, index) => (
                      <div
                        key={index}
                        className={`p-4 rounded-lg border-l-4 ${
                          step.success
                            ? 'bg-green-50 border-green-500 text-green-800'
                            : 'bg-red-50 border-red-500 text-red-800'
                        }`}
                      >
                        <div className="flex items-center gap-2 mb-2">
                          {step.success ? (
                            <FiCheckCircle className="text-green-600" />
                          ) : (
                            <FiX className="text-red-600" />
                          )}
                          <h4 className="font-semibold">
                            {getStepTitle(step.step)}
                          </h4>
                        </div>
                        <p className="text-sm">
                          {step.success ? step.message : step.error}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );

  // وصف الجداول
  function getTableDescription(tableName) {
    const descriptions = {
      'employee_data': 'بيانات الموظفين الأساسية',
      'Governorates': 'المحافظات المصرية',
      'Departments': 'الأقسام والإدارات',
      'JobTitles': 'المسميات الوظيفية',
      'ARCHIV': 'مسارات أرشيف المستندات',
      'login': 'بيانات تسجيل الدخول',
      'Apartments': 'بيانات الشقق',
      'ApartmentBeneficiaries': 'مستفيدي الشقق',
      'Cars': 'بيانات السيارات',
      'CarBeneficiaries': 'مستفيدي السيارات',
      'TempWorkers': 'العمالة المؤقتة',
      'Costs': 'التكاليف الشهرية',
      'daily_attendance': 'الحضور اليومي',
      'leave_balances': 'أرصدة الإجازات',
      'leave_requests': 'طلبات الإجازات',
      'SystemSettings': 'إعدادات النظام',
      'UserActions': 'سجل إجراءات المستخدمين',
      'Notifications': 'الإشعارات',
      'SystemAlerts': 'التنبيهات الذكية',
      'AlertTypes': 'أنواع التنبيهات'
    };
    return descriptions[tableName] || 'جدول النظام';
  }

  // عنوان خطوة الإعداد
  function getStepTitle(stepName) {
    const titles = {
      'createMissingTables': 'إنشاء الجداول المفقودة',
      'cleanTestData': 'تنظيف البيانات التجريبية',
      'setupRealData': 'إعداد البيانات الحقيقية',
      'setupNotificationSystems': 'إعداد أنظمة الإشعارات والتنبيهات'
    };
    return titles[stepName] || stepName;
  }
}
