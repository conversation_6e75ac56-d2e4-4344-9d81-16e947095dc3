import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function POST(request) {
  try {
    const body = await request.json();
    const { action } = body;

    const pool = await getConnection();

    switch (action) {
      case 'delete-mission':
        return await deleteMission(pool, body);
      case 'cancel-mission':
        return await cancelMission(pool, body);
      case 'update-mission':
        return await updateMission(pool, body);
      case 'get-missions':
        return await getMissions(pool, body);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في النظام: ' + error.message
    }, { status: 500 });
  }
}

// حذف المأمورية نهائياً (حتى لو كانت معتمدة)
async function deleteMission(pool, data) {
  try {
    const { requestId, deletedBy, reason } = data;

    // التحقق من وجود المأمورية
    const missionCheck = await pool.request()
      .input('requestId', sql.Int, requestId)
      .query(`
        SELECT ID, EmployeeCode, EmployeeName, RequestType, Status,
               StartDate, EndDate, Destination, MissionPurpose
        FROM PaperRequests
        WHERE ID = @requestId AND RequestType = 'mission'
      `);

    if (missionCheck.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'المأمورية غير موجودة'
      }, { status: 404 });
    }

    const mission = missionCheck.recordset[0];

    // حذف المأمورية من التمام اليومي أولاً

    const deleteAttendanceResult = await pool.request()
      .input('employeeCode', sql.NVarChar, mission.EmployeeCode)
      .input('startDate', sql.Date, mission.StartDate)
      .input('endDate', sql.Date, mission.EndDate)
      .query(`
        DELETE FROM DailyAttendance
        WHERE EmployeeCode = @employeeCode 
          AND AttendanceDate BETWEEN @startDate AND @endDate
          AND IsFromRequest = 1
          AND (Attendance = 'مأمورية' OR Attendance LIKE '%مأمورية%')
      `);

    // تسجيل عملية الحذف في سجل العمليات

    await logMissionDeletion(pool, mission, deletedBy, reason);

    // حذف المأمورية نهائياً من PaperRequests

    const deleteResult = await pool.request()
      .input('requestId', sql.Int, requestId)
      .query(`
        DELETE FROM PaperRequests
        WHERE ID = @requestId AND RequestType = 'mission'
      `);

    if (deleteResult.rowsAffected[0] === 0) {
      return NextResponse.json({
        success: false,
        error: 'فشل في حذف المأمورية'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف المأمورية نهائياً',
      deletedMission: {
        id: requestId,
        employeeName: mission.EmployeeName,
        destination: mission.Destination,
        deletedBy: deletedBy,
        deletedAt: new Date().toISOString()
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في حذف المأمورية: ' + error.message
    }, { status: 500 });
  }
}

// إلغاء المأمورية (بدون حذف نهائي)
async function cancelMission(pool, data) {
  try {
    const { requestId, cancelledBy, reason } = data;

    // التحقق من وجود المأمورية
    const missionCheck = await pool.request()
      .input('requestId', sql.Int, requestId)
      .query(`
        SELECT ID, EmployeeCode, EmployeeName, RequestType, Status,
               StartDate, EndDate, Destination
        FROM PaperRequests
        WHERE ID = @requestId AND RequestType = 'mission'
      `);

    if (missionCheck.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'المأمورية غير موجودة'
      }, { status: 404 });
    }

    const mission = missionCheck.recordset[0];

    // تحديث حالة المأمورية إلى ملغية
    await pool.request()
      .input('requestId', sql.Int, requestId)
      .input('cancelledBy', sql.NVarChar, cancelledBy)
      .input('reason', sql.NVarChar, reason)
      .query(`
        UPDATE PaperRequests
        SET 
          Status = N'ملغية',
          CancellationDate = GETDATE(),
          CancellationReason = @reason,
          CancelledBy = @cancelledBy
        WHERE ID = @requestId
      `);

    // حذف المأمورية من التمام اليومي
    await pool.request()
      .input('employeeCode', sql.NVarChar, mission.EmployeeCode)
      .input('startDate', sql.Date, mission.StartDate)
      .input('endDate', sql.Date, mission.EndDate)
      .query(`
        DELETE FROM DailyAttendance
        WHERE EmployeeCode = @employeeCode 
          AND AttendanceDate BETWEEN @startDate AND @endDate
          AND IsFromRequest = 1
          AND (Attendance = 'مأمورية' OR Attendance LIKE '%مأمورية%')
      `);

    return NextResponse.json({
      success: true,
      message: 'تم إلغاء المأمورية بنجاح',
      cancelledMission: {
        id: requestId,
        employeeName: mission.EmployeeName,
        destination: mission.Destination,
        cancelledBy: cancelledBy,
        cancelledAt: new Date().toISOString()
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في إلغاء المأمورية: ' + error.message
    }, { status: 500 });
  }
}

// تعديل المأمورية
async function updateMission(pool, data) {
  try {
    const { 
      requestId, 
      destination, 
      startDate, 
      endDate, 
      daysCount, 
      purpose, 
      transportMethod, 
      accommodationType,
      updatedBy 
    } = data;

    // التحقق من وجود المأمورية
    const missionCheck = await pool.request()
      .input('requestId', sql.Int, requestId)
      .query(`
        SELECT ID, EmployeeCode, StartDate, EndDate
        FROM PaperRequests
        WHERE ID = @requestId AND RequestType = 'mission'
      `);

    if (missionCheck.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'المأمورية غير موجودة'
      }, { status: 404 });
    }

    const oldMission = missionCheck.recordset[0];

    // تحديث بيانات المأمورية
    await pool.request()
      .input('requestId', sql.Int, requestId)
      .input('destination', sql.NVarChar, destination)
      .input('startDate', sql.Date, startDate)
      .input('endDate', sql.Date, endDate)
      .input('daysCount', sql.Int, daysCount)
      .input('purpose', sql.NVarChar, purpose)
      .input('transportMethod', sql.NVarChar, transportMethod)
      .input('accommodationType', sql.NVarChar, accommodationType)
      .input('updatedBy', sql.NVarChar, updatedBy)
      .query(`
        UPDATE PaperRequests
        SET 
          Destination = @destination,
          StartDate = @startDate,
          EndDate = @endDate,
          DaysCount = @daysCount,
          MissionPurpose = @purpose,
          TransportMethod = @transportMethod,
          AccommodationType = @accommodationType,
          LastModifiedBy = @updatedBy,
          LastModifiedDate = GETDATE()
        WHERE ID = @requestId
      `);

    // إذا تغيرت التواريخ، نحديث التمام اليومي
    if (oldMission.StartDate !== startDate || oldMission.EndDate !== endDate) {
      // حذف التمام القديم
      await pool.request()
        .input('employeeCode', sql.NVarChar, oldMission.EmployeeCode)
        .input('oldStartDate', sql.Date, oldMission.StartDate)
        .input('oldEndDate', sql.Date, oldMission.EndDate)
        .query(`
          DELETE FROM DailyAttendance
          WHERE EmployeeCode = @employeeCode 
            AND AttendanceDate BETWEEN @oldStartDate AND @oldEndDate
            AND IsFromRequest = 1
            AND (Attendance = 'مأمورية' OR Attendance LIKE '%مأمورية%')
        `);

      // إضافة التمام الجديد
      await addMissionToAttendance(pool, {
        employeeCode: oldMission.EmployeeCode,
        startDate: startDate,
        endDate: endDate,
        destination: destination
      });
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث المأمورية بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في تحديث المأمورية: ' + error.message
    }, { status: 500 });
  }
}

// جلب المأموريات
async function getMissions(pool, data) {
  try {
    const { employeeCode, status, limit = 50 } = data;

    let whereClause = "WHERE RequestType = 'mission'";
    const params = [];

    if (employeeCode) {
      whereClause += " AND EmployeeCode = @employeeCode";
      params.push({ name: 'employeeCode', type: sql.NVarChar, value: employeeCode });
    }

    if (status) {
      whereClause += " AND Status = @status";
      params.push({ name: 'status', type: sql.NVarChar, value: status });
    }

    const request = pool.request();
    params.forEach(param => {
      request.input(param.name, param.type, param.value);
    });
    request.input('limit', sql.Int, limit);

    const result = await request.query(`
      SELECT TOP (@limit)
        ID, EmployeeCode, EmployeeName, Department, JobTitle,
        Destination, StartDate, EndDate, DaysCount, MissionPurpose,
        TransportMethod, AccommodationType, Status, RequestDate,
        CancellationDate, CancellationReason, CancelledBy
      FROM PaperRequests
      ${whereClause}
      ORDER BY RequestDate DESC
    `);

    return NextResponse.json({
      success: true,
      data: result.recordset
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب المأموريات: ' + error.message
    }, { status: 500 });
  }
}

// دوال مساعدة

// تسجيل عملية حذف المأمورية
async function logMissionDeletion(pool, mission, deletedBy, reason) {
  try {
    await pool.request()
      .input('requestId', sql.Int, mission.ID)
      .input('employeeCode', sql.NVarChar, mission.EmployeeCode)
      .input('employeeName', sql.NVarChar, mission.EmployeeName)
      .input('destination', sql.NVarChar, mission.Destination)
      .input('startDate', sql.Date, mission.StartDate)
      .input('endDate', sql.Date, mission.EndDate)
      .input('deletedBy', sql.NVarChar, deletedBy)
      .input('reason', sql.NVarChar, reason)
      .query(`
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MissionDeletionLog' AND xtype='U')
        BEGIN
          CREATE TABLE MissionDeletionLog (
            ID INT IDENTITY(1,1) PRIMARY KEY,
            OriginalRequestID INT,
            EmployeeCode NVARCHAR(20),
            EmployeeName NVARCHAR(100),
            Destination NVARCHAR(200),
            StartDate DATE,
            EndDate DATE,
            DeletedBy NVARCHAR(100),
            DeletionReason NVARCHAR(500),
            DeletedAt DATETIME DEFAULT GETDATE()
          )
        END

        INSERT INTO MissionDeletionLog (
          OriginalRequestID, EmployeeCode, EmployeeName, Destination,
          StartDate, EndDate, DeletedBy, DeletionReason
        )
        VALUES (
          @requestId, @employeeCode, @employeeName, @destination,
          @startDate, @endDate, @deletedBy, @reason
        )
      `);

  } catch (error) {

    // لا نوقف العملية في حالة فشل التسجيل
  }
}

// إضافة المأمورية للتمام اليومي
async function addMissionToAttendance(pool, data) {
  try {
    const { employeeCode, startDate, endDate, destination } = data;

    // جلب اسم الموظف
    const employeeResult = await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .query(`
        SELECT EmployeeName FROM Employees WHERE EmployeeCode = @employeeCode
      `);

    const employeeName = employeeResult.recordset[0]?.EmployeeName || 'غير محدد';

    // إضافة أيام المأمورية للتمام
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
      const currentDate = date.toISOString().split('T')[0];
      
      await pool.request()
        .input('attendanceDate', sql.Date, currentDate)
        .input('employeeCode', sql.NVarChar, employeeCode)
        .input('employeeName', sql.NVarChar, employeeName)
        .input('notes', sql.NVarChar, `مأمورية إلى ${destination} - تنتهي في ${endDate}`)
        .query(`
          INSERT INTO DailyAttendance (
            AttendanceDate, EmployeeCode, EmployeeName,
            Attendance, attendanceStatus, Notes, IsFromRequest
          )
          VALUES (
            @attendanceDate, @employeeCode, @employeeName,
            'مأمورية', 'مأمورية', @notes, 1
          )
        `);
    }

  } catch (error) {

    throw error;
  }
}
