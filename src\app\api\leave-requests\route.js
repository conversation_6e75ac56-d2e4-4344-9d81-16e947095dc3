import { NextResponse } from 'next/server';
import sql from 'mssql';

// دالة لتوحيد بيانات طلب الإجازة
function normalizeLeaveRequestData(request) {
  return {
    ...request,
    // التسميات الموحدة الجديدة
    employeeCode: request.EmployeeID || request.employeeCode,
    employeeName: request.EmployeeName || request.employeeName,

    // الحفاظ على التسميات القديمة للتوافق
    EmployeeID: request.EmployeeID || request.employeeCode,
    EmployeeName: request.EmployeeName || request.employeeName
  };
}

// إعداد قاعدة البيانات
const dbConfig = {
  user: process.env.DB_USER || 'SA',
  password: process.env.DB_PASSWORD || 'admin@123',
  server: process.env.DB_SERVER || 'localhost\\DBOJESTA',
  database: process.env.DB_NAME || 'EMP',
  options: {
    encrypt: false,
    trustServerCertificate: true,
    enableArithAbort: true,
    instanceName: 'DBOJESTA'
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
  requestTimeout: 30000,
  connectionTimeout: 30000,
};

export async function POST(request) {
  let pool;

  try {
    const body = await request.json();
    const { action } = body;

    pool = await sql.connect(dbConfig);

    switch (action) {
      case 'create':
        return await createLeaveRequest(pool, body);
      case 'list':
        return await getLeaveRequests(pool, body);
      case 'update':
        return await updateLeaveRequest(pool, body);
      case 'approve':
        return await approveLeaveRequest(pool, body);
      case 'reject':
        return await rejectLeaveRequest(pool, body);
      case 'getBalance':
        return await getLeaveBalance(pool, body);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      try {
        await pool.close();
      } catch (closeError) {

      }
    }
  }
}

async function createLeaveRequest(pool, data) {
  try {

    // استخدام جدول PaperRequests الموحد بدلاً من إنشاء جدول منفصل

    await pool.request().query(checkTableQuery);

    // تحويل التاريخ من dd/mm/yyyy إلى yyyy-mm-dd
    const convertDate = (dateStr) => {
      if (!dateStr) return null;
      if (dateStr.includes('/')) {
        const [day, month, year] = dateStr.split('/');
        return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
      }
      return dateStr;
    };

    // إدراج طلب إجازة جديد
    const insertQuery = `
      INSERT INTO LeaveRequests (
        EmployeeCode, EmployeeName, Department, JobTitle, LeaveType,
        StartDate, EndDate, DaysCount, Reason, Status, RequestDate,
        LastLeaveDate, RemainingBalance
      ) VALUES (
        @employeeCode, @employeeName, @department, @jobTitle, @leaveType,
        @startDate, @endDate, @daysCount, @reason, @status, @requestDate,
        @lastLeaveDate, @remainingBalance
      )
    `;

    const result = await pool.request()
      .input('employeeCode', sql.NVarChar, data.employeeId)
      .input('employeeName', sql.NVarChar, data.employeeName)
      .input('department', sql.NVarChar, data.department || '')
      .input('jobTitle', sql.NVarChar, data.jobTitle || '')
      .input('leaveType', sql.NVarChar, data.leaveType)
      .input('startDate', sql.Date, convertDate(data.startDate))
      .input('endDate', sql.Date, convertDate(data.endDate))
      .input('daysCount', sql.Int, parseInt(data.totalDays) || 1)
      .input('reason', sql.NVarChar, data.reason || '')
      .input('status', sql.NVarChar, 'pending')
      .input('requestDate', sql.DateTime, new Date())
      .input('lastLeaveDate', sql.Date, data.lastLeaveDate ? convertDate(data.lastLeaveDate) : null)
      .input('remainingBalance', sql.Int, parseInt(data.remainingBalance) || 0)
      .query(insertQuery);

    return NextResponse.json({
      success: true,
      message: 'تم تقديم طلب الإجازة بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في إنشاء طلب الإجازة: ' + error.message
    }, { status: 500 });
  } finally {

  }
}

async function getLeaveRequests(pool, data) {
  try {
    let query = `
      SELECT 
        ID, EmployeeID as EmployeeCode, EmployeeName, Department, LeaveType,
        StartDate, EndDate, TotalDays, Reason, EmergencyContact,
        EmergencyPhone, ReplacementEmployee, Notes, Status,
        SubmittedAt, ApprovedBy, ApprovedAt, CreatedAt
      FROM LeaveRequests
    `;

    const conditions = [];
    const request = pool.request();

    if (data.employeeId) {
      conditions.push('EmployeeID = @employeeId');
      request.input('employeeId', sql.NVarChar, data.employeeId);
    }

    if (data.status) {
      conditions.push('Status = @status');
      request.input('status', sql.NVarChar, data.status);
    }

    if (data.leaveType) {
      conditions.push('LeaveType = @leaveType');
      request.input('leaveType', sql.NVarChar, data.leaveType);
    }

    if (data.startDate && data.endDate) {
      conditions.push('StartDate >= @startDate AND EndDate <= @endDate');
      request.input('startDate', sql.Date, data.startDate);
      request.input('endDate', sql.Date, data.endDate);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY SubmittedAt DESC';

    const result = await request.query(query);

    const normalizedRequests = result.recordset.map(normalizeLeaveRequestData);
    return NextResponse.json({
      success: true,
      requests: normalizedRequests
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب طلبات الإجازة: ' + error.message
    }, { status: 500 });
  }
}

async function updateLeaveRequest(pool, data) {
  try {
    const updateQuery = `
      UPDATE LeaveRequests 
      SET 
        LeaveType = @leaveType,
        StartDate = @startDate,
        EndDate = @endDate,
        TotalDays = @totalDays,
        Reason = @reason,
        EmergencyContact = @emergencyContact,
        EmergencyPhone = @emergencyPhone,
        ReplacementEmployee = @replacementEmployee,
        Notes = @notes
      WHERE ID = @id
    `;

    await pool.request()
      .input('id', sql.Int, data.id)
      .input('leaveType', sql.NVarChar, data.leaveType)
      .input('startDate', sql.Date, data.startDate)
      .input('endDate', sql.Date, data.endDate)
      .input('totalDays', sql.Int, data.totalDays)
      .input('reason', sql.NVarChar, data.reason)
      .input('emergencyContact', sql.NVarChar, data.emergencyContact || null)
      .input('emergencyPhone', sql.NVarChar, data.emergencyPhone || null)
      .input('replacementEmployee', sql.NVarChar, data.replacementEmployee || null)
      .input('notes', sql.NVarChar, data.notes || null)
      .query(updateQuery);

    return NextResponse.json({
      success: true,
      message: 'تم تحديث طلب الإجازة بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في تحديث طلب الإجازة: ' + error.message
    }, { status: 500 });
  }
}

async function approveLeaveRequest(pool, data) {
  try {
    const updateQuery = `
      UPDATE LeaveRequests 
      SET 
        Status = 'approved',
        ApprovedBy = @approvedBy,
        ApprovedAt = GETDATE()
      WHERE ID = @id
    `;

    await pool.request()
      .input('id', sql.Int, data.id)
      .input('approvedBy', sql.NVarChar, data.approvedBy)
      .query(updateQuery);

    return NextResponse.json({
      success: true,
      message: 'تم الموافقة على طلب الإجازة'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الموافقة على طلب الإجازة: ' + error.message
    }, { status: 500 });
  }
}

async function rejectLeaveRequest(pool, data) {
  try {
    const updateQuery = `
      UPDATE LeaveRequests 
      SET 
        Status = 'rejected',
        ApprovedBy = @rejectedBy,
        ApprovedAt = GETDATE(),
        Notes = CONCAT(ISNULL(Notes, ''), CHAR(13) + CHAR(10) + 'سبب الرفض: ' + @rejectionReason)
      WHERE ID = @id
    `;

    await pool.request()
      .input('id', sql.Int, data.id)
      .input('rejectedBy', sql.NVarChar, data.rejectedBy)
      .input('rejectionReason', sql.NVarChar, data.rejectionReason || 'لم يتم تحديد السبب')
      .query(updateQuery);

    return NextResponse.json({
      success: true,
      message: 'تم رفض طلب الإجازة'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في رفض طلب الإجازة: ' + error.message
    }, { status: 500 });
  }
}

async function getLeaveBalance(pool, data) {
  try {
    // Check if LeaveBalances table exists, create if not
    const checkTableQuery = `
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LeaveBalances' AND xtype='U')
      BEGIN
        CREATE TABLE LeaveBalances (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          EmployeeID NVARCHAR(50) NOT NULL,
          Year INT NOT NULL,
          AnnualLeave INT DEFAULT 30,
          SickLeave INT DEFAULT 15,
          EmergencyLeave INT DEFAULT 7,
          UsedAnnual INT DEFAULT 0,
          UsedSick INT DEFAULT 0,
          UsedEmergency INT DEFAULT 0,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),
          UNIQUE(EmployeeID, Year)
        )
      END
    `;
    
    await pool.request().query(checkTableQuery);

    const currentYear = new Date().getFullYear();
    
    // Get or create balance record for current year
    const balanceQuery = `
      IF NOT EXISTS (SELECT 1 FROM LeaveBalances WHERE EmployeeID = @employeeId AND Year = @year)
      BEGIN
        INSERT INTO LeaveBalances (EmployeeID, Year) VALUES (@employeeId, @year)
      END
      
      SELECT * FROM LeaveBalances WHERE EmployeeID = @employeeId AND Year = @year
    `;

    const result = await pool.request()
      .input('employeeId', sql.NVarChar, data.employeeId)
      .input('year', sql.Int, currentYear)
      .query(balanceQuery);

    return NextResponse.json({
      success: true,
      balance: result.recordset[0]
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب رصيد الإجازات: ' + error.message
    }, { status: 500 });
  }
}

export async function GET(request) {
  let pool;

  try {
    const { searchParams } = new URL(request.url);
    const employeeCode = searchParams.get('employeeCode');
    const status = searchParams.get('status');
    const leaveType = searchParams.get('leaveType');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;

    pool = await sql.connect(dbConfig);

    // بناء الاستعلام
    let query = `
      SELECT
        ID, EmployeeCode, EmployeeName, Department, JobTitle, LeaveType,
        StartDate, EndDate, DaysCount, Reason, Status,
        RequestDate, ApprovalDate, RejectionDate, ApprovalNotes,
        LastLeaveDate, RemainingBalance
      FROM LeaveRequests
    `;

    const conditions = [];
    const request = pool.request();

    // فلاتر البحث
    if (employeeCode && employeeCode !== 'all') {
      conditions.push('EmployeeCode = @employeeCode');
      request.input('employeeCode', sql.NVarChar, employeeCode);
    }

    if (status && status !== 'all') {
      conditions.push('Status = @status');
      request.input('status', sql.NVarChar, status);
    }

    if (leaveType && leaveType !== 'all') {
      conditions.push('LeaveType = @leaveType');
      request.input('leaveType', sql.NVarChar, leaveType);
    }

    if (startDate) {
      conditions.push('StartDate >= @startDate');
      request.input('startDate', sql.Date, startDate);
    }

    if (endDate) {
      conditions.push('EndDate <= @endDate');
      request.input('endDate', sql.Date, endDate);
    }

    // إضافة شروط البحث
    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    // ترتيب النتائج
    query += ' ORDER BY RequestDate DESC';

    // تنفيذ الاستعلام
    const result = await request.query(query);
    const allRequests = result.recordset;

    // حساب الترقيم
    const total = allRequests.length;
    const totalPages = Math.ceil(total / limit);
    const offset = (page - 1) * limit;
    const requests = allRequests.slice(offset, offset + limit);

    // تنسيق التواريخ
    const formattedRequests = requests.map(req => ({
      ...req,
      StartDate: req.StartDate ? req.StartDate.toISOString().split('T')[0] : null,
      EndDate: req.EndDate ? req.EndDate.toISOString().split('T')[0] : null,
      RequestDate: req.RequestDate ? req.RequestDate.toISOString() : null,
      ApprovalDate: req.ApprovalDate ? req.ApprovalDate.toISOString() : null,
      RejectionDate: req.RejectionDate ? req.RejectionDate.toISOString() : null,
      LastLeaveDate: req.LastLeaveDate ? req.LastLeaveDate.toISOString().split('T')[0] : null
    }));

    return NextResponse.json({
      success: true,
      data: {
        requests: formattedRequests,
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'حدث خطأ في جلب طلبات الإجازات',
      error: error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      try {
        await pool.close();
      } catch (closeError) {

      }
    }
  }
}
