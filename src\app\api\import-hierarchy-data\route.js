import { NextResponse } from 'next/server';
import { getConnection } from '@/lib/db';
import sql from 'mssql';

export async function POST(request) {
  try {

    const { hierarchyData } = await request.json();
    
    if (!hierarchyData || !Array.isArray(hierarchyData)) {
      return NextResponse.json({
        success: false,
        error: 'بيانات الهيكل الإداري مطلوبة'
      }, { status: 400 });
    }

    const pool = await getConnection();
    
    // التحقق من وجود أعمدة المديرين وإضافتها إذا لم تكن موجودة
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Employees' AND COLUMN_NAME = 'DirectManager1')
      BEGIN
        ALTER TABLE Employees ADD DirectManager1 NVARCHAR(20)
      END
      
      IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Employees' AND COLUMN_NAME = 'DirectManager2')
      BEGIN
        ALTER TABLE Employees ADD DirectManager2 NVARCHAR(20)
      END
      
      IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Employees' AND COLUMN_NAME = 'DirectManager3')
      BEGIN
        ALTER TABLE Employees ADD DirectManager3 NVARCHAR(20)
      END
      
      IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Employees' AND COLUMN_NAME = 'DirectManager4')
      BEGIN
        ALTER TABLE Employees ADD DirectManager4 NVARCHAR(20)
      END
    `);

    let successCount = 0;
    let errorCount = 0;
    const errors = [];

    // معالجة كل سجل في البيانات
    for (const record of hierarchyData) {
      try {
        const { employeeCode, manager1, manager2, manager3, manager4 } = record;
        
        if (!employeeCode) {
          errors.push(`كود الموظف مفقود في السجل: ${JSON.stringify(record)}`);
          errorCount++;
          continue;
        }

        // التحقق من وجود الموظف
        const checkEmployee = await pool.request()
          .input('employeeCode', sql.NVarChar, employeeCode)
          .query('SELECT EmployeeCode FROM Employees WHERE EmployeeCode = @employeeCode');

        if (checkEmployee.recordset.length === 0) {
          errors.push(`الموظف غير موجود: ${employeeCode}`);
          errorCount++;
          continue;
        }

        // تحديث بيانات المديرين
        await pool.request()
          .input('employeeCode', sql.NVarChar, employeeCode)
          .input('manager1', sql.NVarChar, manager1 || null)
          .input('manager2', sql.NVarChar, manager2 || null)
          .input('manager3', sql.NVarChar, manager3 || null)
          .input('manager4', sql.NVarChar, manager4 || null)
          .query(`
            UPDATE Employees 
            SET 
              DirectManager1 = @manager1,
              DirectManager2 = @manager2,
              DirectManager3 = @manager3,
              DirectManager4 = @manager4,
              UpdatedAt = GETDATE()
            WHERE EmployeeCode = @employeeCode
          `);

        successCount++;
        
      } catch (error) {

        errors.push(`خطأ في معالجة الموظف ${record.employeeCode}: ${error.message}`);
        errorCount++;
      }
    }

    return NextResponse.json({
      success: true,
      message: `تم استيراد بيانات الهيكل الإداري بنجاح`,
      stats: {
        total: hierarchyData.length,
        success: successCount,
        errors: errorCount
      },
      errors: errors.slice(0, 10) // عرض أول 10 أخطاء فقط
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// API لاستيراد البيانات من النص المنسق
export async function PUT(request) {
  try {
    const { textData } = await request.json();
    
    if (!textData) {
      return NextResponse.json({
        success: false,
        error: 'النص المطلوب استيراده مفقود'
      }, { status: 400 });
    }

    // تحليل النص وتحويله إلى بيانات منظمة
    const lines = textData.split('\n').filter(line => line.trim());
    const hierarchyData = [];

    for (let i = 1; i < lines.length; i++) { // تخطي السطر الأول (العناوين)
      const columns = lines[i].split('\t');
      
      if (columns.length >= 1) {
        const record = {
          employeeCode: columns[0]?.trim(),
          manager1: columns[1]?.trim() || null,
          manager2: columns[2]?.trim() || null,
          manager3: columns[3]?.trim() || null,
          manager4: columns[4]?.trim() || null
        };

        // تنظيف البيانات الفارغة
        if (record.manager1 === '') record.manager1 = null;
        if (record.manager2 === '') record.manager2 = null;
        if (record.manager3 === '') record.manager3 = null;
        if (record.manager4 === '') record.manager4 = null;

        if (record.employeeCode && record.employeeCode !== 'تعيين جديد') {
          hierarchyData.push(record);
        }
      }
    }

    // استدعاء دالة الاستيراد
    const importRequest = new Request(request.url, {
      method: 'POST',
      headers: request.headers,
      body: JSON.stringify({ hierarchyData })
    });

    return await POST(importRequest);

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// API للحصول على إحصائيات الهيكل الحالي
export async function GET() {
  try {
    const pool = await getConnection();
    
    const statsQuery = `
      SELECT 
        COUNT(*) as TotalEmployees,
        COUNT(CASE WHEN DirectManager1 IS NOT NULL THEN 1 END) as WithManager1,
        COUNT(CASE WHEN DirectManager2 IS NOT NULL THEN 1 END) as WithManager2,
        COUNT(CASE WHEN DirectManager3 IS NOT NULL THEN 1 END) as WithManager3,
        COUNT(CASE WHEN DirectManager4 IS NOT NULL THEN 1 END) as WithManager4,
        COUNT(CASE WHEN DirectManager1 IS NULL AND DirectManager2 IS NULL AND DirectManager3 IS NULL AND DirectManager4 IS NULL THEN 1 END) as TopLevel
      FROM Employees
      WHERE CurrentStatus IN (N'نشط', N'ساري', N'سارى', N'active', N'Active')
    `;
    
    const result = await pool.request().query(statsQuery);
    const stats = result.recordset[0];
    
    return NextResponse.json({
      success: true,
      stats
    });
    
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
