import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function GET(request) {
  try {
    const pool = await getConnection();

    const result = {
      LeaveRequests: {
        exists: false,
        count: 0,
        columns: [],
        sampleData: []
      },
      PaperRequests: {
        exists: false,
        count: 0,
        columns: [],
        sampleData: []
      }
    };

    // فحص جدول LeaveRequests
    try {
      const leaveTableCheck = await pool.request().query(`
        SELECT COUNT(*) as TableExists 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_NAME = 'LeaveRequests'
      `);

      if (leaveTableCheck.recordset[0].TableExists > 0) {
        result.LeaveRequests.exists = true;

        // عد السجلات
        const leaveCount = await pool.request().query(`SELECT COUNT(*) as Count FROM LeaveRequests`);
        result.LeaveRequests.count = leaveCount.recordset[0].Count;

        // جلب الأعمدة
        const leaveColumns = await pool.request().query(`
          SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
          FROM INFORMATION_SCHEMA.COLUMNS 
          WHERE TABLE_NAME = 'LeaveRequests'
          ORDER BY ORDINAL_POSITION
        `);
        result.LeaveRequests.columns = leaveColumns.recordset;

        // جلب عينة من البيانات
        const leaveSample = await pool.request().query(`
          SELECT TOP 10 * FROM LeaveRequests ORDER BY ID DESC
        `);
        result.LeaveRequests.sampleData = leaveSample.recordset;
      }
    } catch (error) {
      result.LeaveRequests.error = error.message;
    }

    // فحص جدول PaperRequests
    try {
      const paperTableCheck = await pool.request().query(`
        SELECT COUNT(*) as TableExists 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_NAME = 'PaperRequests'
      `);

      if (paperTableCheck.recordset[0].TableExists > 0) {
        result.PaperRequests.exists = true;

        // عد السجلات
        const paperCount = await pool.request().query(`SELECT COUNT(*) as Count FROM PaperRequests`);
        result.PaperRequests.count = paperCount.recordset[0].Count;

        // جلب الأعمدة
        const paperColumns = await pool.request().query(`
          SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
          FROM INFORMATION_SCHEMA.COLUMNS 
          WHERE TABLE_NAME = 'PaperRequests'
          ORDER BY ORDINAL_POSITION
        `);
        result.PaperRequests.columns = paperColumns.recordset;

        // جلب عينة من البيانات
        const paperSample = await pool.request().query(`
          SELECT TOP 10 * FROM PaperRequests ORDER BY ID DESC
        `);
        result.PaperRequests.sampleData = paperSample.recordset;
      }
    } catch (error) {
      result.PaperRequests.error = error.message;
    }

    // تحليل البيانات
    const analysis = {
      recommendation: '',
      reasons: []
    };

    if (result.LeaveRequests.exists && result.PaperRequests.exists) {
      if (result.LeaveRequests.count > result.PaperRequests.count) {
        analysis.recommendation = 'استخدام جدول LeaveRequests وحذف PaperRequests';
        analysis.reasons.push(`LeaveRequests يحتوي على ${result.LeaveRequests.count} سجل`);
        analysis.reasons.push(`PaperRequests يحتوي على ${result.PaperRequests.count} سجل فقط`);
      } else if (result.PaperRequests.count > result.LeaveRequests.count) {
        analysis.recommendation = 'استخدام جدول PaperRequests وحذف LeaveRequests';
        analysis.reasons.push(`PaperRequests يحتوي على ${result.PaperRequests.count} سجل`);
        analysis.reasons.push(`LeaveRequests يحتوي على ${result.LeaveRequests.count} سجل فقط`);
      } else {
        analysis.recommendation = 'فحص محتوى الجدولين لتحديد الأحدث';
        analysis.reasons.push('كلا الجدولين يحتوي على نفس عدد السجلات');
      }
    } else if (result.LeaveRequests.exists) {
      analysis.recommendation = 'استخدام جدول LeaveRequests فقط';
      analysis.reasons.push('جدول PaperRequests غير موجود');
    } else if (result.PaperRequests.exists) {
      analysis.recommendation = 'استخدام جدول PaperRequests فقط';
      analysis.reasons.push('جدول LeaveRequests غير موجود');
    }

    return NextResponse.json({
      success: true,
      tables: result,
      analysis: analysis,
      message: 'تم فحص الجدولين بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في مقارنة الجدولين: ' + error.message
    }, { status: 500 });
  }
}
