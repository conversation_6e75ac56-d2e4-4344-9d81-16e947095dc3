import { getConnection } from '@/utils/db';
import { NextResponse } from 'next/server';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const employeeCode = searchParams.get('employeeCode');

    if (!employeeCode) {
      return NextResponse.json({
        success: false,
        error: 'كود الموظف مطلوب'
      }, { status: 400 });
    }

    const pool = await getConnection();

    // جلب حالة الموظف من جميع الجداول المرتبطة
    const employeeStatus = await getEmployeeCompleteStatus(pool, employeeCode);

    return NextResponse.json({
      success: true,
      data: employeeStatus
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ في جلب حالة الموظف',
      details: error.message
    }, { status: 500 });
  }
}

export async function POST(request) {
  try {
    const { employeeCode, newStatus, reason, additionalData } = await request.json();

    if (!employeeCode || !newStatus) {
      return NextResponse.json({
        success: false,
        error: 'كود الموظف والحالة الجديدة مطلوبان'
      }, { status: 400 });
    }

    const pool = await getConnection();

    // تحديث حالة الموظف
    const result = await updateEmployeeStatus(pool, employeeCode, newStatus, reason, additionalData);

    return NextResponse.json({
      success: true,
      message: 'تم تحديث حالة الموظف بنجاح',
      data: result
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ في تحديث حالة الموظف',
      details: error.message
    }, { status: 500 });
  }
}

// دالة لجلب الحالة الكاملة للموظف
async function getEmployeeCompleteStatus(pool, employeeCode) {
  try {
    // 1. البيانات الأساسية
    const employeeResult = await pool.request()
      .input('EmployeeCode', employeeCode)
      .query(`
        SELECT
          EmployeeCode, EmployeeName, JobTitle, Department, CurrentStatus,
          IsResidentEmployee, CompanyHousing, HousingCode, TransportMethod
        FROM Employees
        WHERE EmployeeCode = @EmployeeCode
      `);

    if (employeeResult.recordset.length === 0) {
      throw new Error('الموظف غير موجود');
    }

    const employee = employeeResult.recordset[0];

    // 2. حالة الاستقالة
    const resignationResult = await pool.request()
      .input('EmployeeCode', employeeCode)
      .query(`
        SELECT TOP 1 ID, ResignationDate, LastWorkingDay, ResignationReason, IsActive
        FROM EmployeeResignations
        WHERE EmployeeCode = @EmployeeCode
        ORDER BY CreatedAt DESC
      `);

    // 3. حالة النقل
    const transferResult = await pool.request()
      .input('EmployeeCode', employeeCode)
      .query(`
        SELECT TOP 1 ID, TransferDate, NewDepartment, NewJobTitle, TransferReason, IsActive
        FROM EmployeeTransfers
        WHERE EmployeeCode = @EmployeeCode
        ORDER BY CreatedAt DESC
      `);

    // 4. حالة السكن
    const apartmentResult = await pool.request()
      .input('EmployeeCode', employeeCode)
      .query(`
        SELECT TOP 1
          ab.ID, ab.StartDate, ab.EndDate, ab.IsActive,
          a.ApartmentCode, a.LandlordName, a.Address
        FROM ApartmentBeneficiaries ab
        INNER JOIN Apartments a ON ab.ApartmentID = a.ID
        WHERE ab.EmployeeCode = @EmployeeCode
        ORDER BY ab.CreatedAt DESC
      `);

    // 5. حالة السيارة
    const carResult = await pool.request()
      .input('EmployeeCode', employeeCode)
      .query(`
        SELECT TOP 1
          cb.ID, cb.StartDate, cb.EndDate, cb.IsActive,
          c.CarCode, c.ContractorName, c.CarNumber, c.Route
        FROM CarBeneficiaries cb
        INNER JOIN Cars c ON cb.CarCode = c.CarCode
        WHERE cb.EmployeeCode = @EmployeeCode
        ORDER BY cb.CreatedAt DESC
      `);

    return {
      employeeCode: employee.EmployeeCode,
      employeeName: employee.EmployeeName,
      jobTitle: employee.JobTitle,
      department: employee.Department,
      currentStatus: employee.CurrentStatus,
      isResidentEmployee: employee.IsResidentEmployee,
      companyHousing: employee.CompanyHousing,
      housingCode: employee.HousingCode,
      transportMethod: employee.TransportMethod,

      // حالة الاستقالة
      resignation: resignationResult.recordset.length > 0 ? {
        id: resignationResult.recordset[0].ID,
        resignationDate: resignationResult.recordset[0].ResignationDate,
        lastWorkingDay: resignationResult.recordset[0].LastWorkingDay,
        reason: resignationResult.recordset[0].ResignationReason,
        isActive: resignationResult.recordset[0].IsActive
      } : null,

      // حالة النقل
      transfer: transferResult.recordset.length > 0 ? {
        id: transferResult.recordset[0].ID,
        transferDate: transferResult.recordset[0].TransferDate,
        newDepartment: transferResult.recordset[0].NewDepartment,
        newJobTitle: transferResult.recordset[0].NewJobTitle,
        reason: transferResult.recordset[0].TransferReason,
        isActive: transferResult.recordset[0].IsActive
      } : null,

      // حالة السكن
      apartment: apartmentResult.recordset.length > 0 ? {
        id: apartmentResult.recordset[0].ID,
        startDate: apartmentResult.recordset[0].StartDate,
        endDate: apartmentResult.recordset[0].EndDate,
        isActive: apartmentResult.recordset[0].IsActive,
        apartmentCode: apartmentResult.recordset[0].ApartmentCode,
        landlordName: apartmentResult.recordset[0].LandlordName,
        address: apartmentResult.recordset[0].Address
      } : null,

      // حالة السيارة
      car: carResult.recordset.length > 0 ? {
        id: carResult.recordset[0].ID,
        startDate: carResult.recordset[0].StartDate,
        endDate: carResult.recordset[0].EndDate,
        isActive: carResult.recordset[0].IsActive,
        carCode: carResult.recordset[0].CarCode,
        contractorName: carResult.recordset[0].ContractorName,
        carNumber: carResult.recordset[0].CarNumber,
        route: carResult.recordset[0].Route
      } : null
    };

  } catch (error) {
    throw error;
  }
}

// دالة لتحديث حالة الموظف
async function updateEmployeeStatus(pool, employeeCode, newStatus, reason, additionalData) {
  try {
    // التحقق من وجود الموظف
    const employeeCheck = await pool.request()
      .input('EmployeeCode', employeeCode)
      .query(`
        SELECT EmployeeCode, EmployeeName, Department, JobTitle, CurrentStatus
        FROM Employees
        WHERE EmployeeCode = @EmployeeCode
      `);

    if (employeeCheck.recordset.length === 0) {
      throw new Error('الموظف غير موجود');
    }

    const employee = employeeCheck.recordset[0];
    const oldStatus = employee.CurrentStatus;

    // بدء المعاملة
    const transaction = new pool.Transaction();
    await transaction.begin();

    try {
      // 1. تحديث حالة الموظف الأساسية
      await transaction.request()
        .input('EmployeeCode', employeeCode)
        .input('NewStatus', newStatus)
        .query(`
          UPDATE Employees
          SET CurrentStatus = @NewStatus, LastModified = GETDATE()
          WHERE EmployeeCode = @EmployeeCode
        `);

      // 2. معالجة الحالات المختلفة
      switch (newStatus) {
        case 'مستقيل':
          await handleResignation(transaction, employeeCode, employee, reason, additionalData);
          break;

        case 'منقول':
          await handleTransfer(transaction, employeeCode, employee, reason, additionalData);
          break;

        case 'ساري':
        case 'سارى':
        case 'نشط':
          await handleActivation(transaction, employeeCode, employee, reason, additionalData);
          break;

        default:
          // حالة أخرى - لا تحتاج معالجة خاصة
          break;
      }

      await transaction.commit();

      return {
        employeeCode,
        oldStatus,
        newStatus,
        updatedAt: new Date(),
        message: `تم تحديث حالة الموظف من ${oldStatus} إلى ${newStatus}`
      };

    } catch (error) {
      await transaction.rollback();
      throw error;
    }

  } catch (error) {
    throw error;
  }
}

// معالجة الاستقالة
async function handleResignation(transaction, employeeCode, employee, reason, additionalData) {
  // إضافة سجل الاستقالة
  await transaction.request()
    .input('EmployeeCode', employeeCode)
    .input('EmployeeName', employee.EmployeeName)
    .input('Department', employee.Department)
    .input('JobTitle', employee.JobTitle)
    .input('ResignationDate', additionalData?.resignationDate || new Date())
    .input('LastWorkingDay', additionalData?.lastWorkingDay || new Date())
    .input('ResignationReason', reason || 'غير محدد')
    .input('FinalSettlementAmount', additionalData?.finalSettlementAmount || 0)
    .input('CreatedBy', additionalData?.createdBy || 'النظام')
    .query(`
      INSERT INTO EmployeeResignations
      (EmployeeCode, EmployeeName, Department, JobTitle,
       ResignationDate, LastWorkingDay, ResignationReason,
       FinalSettlementAmount, CreatedBy)
      VALUES
      (@EmployeeCode, @EmployeeName, @Department, @JobTitle,
       @ResignationDate, @LastWorkingDay, @ResignationReason,
       @FinalSettlementAmount, @CreatedBy)
    `);

  // إزالة من الشقق
  await transaction.request()
    .input('EmployeeCode', employeeCode)
    .query(`
      UPDATE ApartmentBeneficiaries
      SET IsActive = 0, EndDate = GETDATE(), UpdatedAt = GETDATE()
      WHERE EmployeeCode = @EmployeeCode AND IsActive = 1
    `);

  // إزالة من السيارات
  await transaction.request()
    .input('EmployeeCode', employeeCode)
    .query(`
      UPDATE CarBeneficiaries
      SET IsActive = 0, EndDate = GETDATE(), UpdatedAt = GETDATE()
      WHERE EmployeeCode = @EmployeeCode AND IsActive = 1
    `);

  // تحديث بيانات السكن والمواصلات
  await transaction.request()
    .input('EmployeeCode', employeeCode)
    .query(`
      UPDATE Employees
      SET
        IsResidentEmployee = 0,
        CompanyHousing = NULL,
        HousingCode = NULL,
        TransportMethod = N'غير مسجل بسيارة',
        LastModified = GETDATE()
      WHERE EmployeeCode = @EmployeeCode
    `);
}

// معالجة النقل
async function handleTransfer(transaction, employeeCode, employee, reason, additionalData) {
  // إضافة سجل النقل
  await transaction.request()
    .input('EmployeeCode', employeeCode)
    .input('EmployeeName', employee.EmployeeName)
    .input('PreviousDepartment', employee.Department)
    .input('NewDepartment', additionalData?.newDepartment || employee.Department)
    .input('PreviousJobTitle', employee.JobTitle)
    .input('NewJobTitle', additionalData?.newJobTitle || employee.JobTitle)
    .input('ProjectOrDepartment', additionalData?.projectOrDepartment || additionalData?.newDepartment)
    .input('TransferDate', additionalData?.transferDate || new Date())
    .input('TransferReason', reason || 'غير محدد')
    .input('CreatedBy', additionalData?.createdBy || 'النظام')
    .query(`
      INSERT INTO EmployeeTransfers
      (EmployeeCode, EmployeeName, PreviousDepartment, NewDepartment,
       PreviousJobTitle, NewJobTitle, ProjectOrDepartment, TransferDate, TransferReason, CreatedBy)
      VALUES
      (@EmployeeCode, @EmployeeName, @PreviousDepartment, @NewDepartment,
       @PreviousJobTitle, @NewJobTitle, @ProjectOrDepartment, @TransferDate, @TransferReason, @CreatedBy)
    `);

  // تحديث القسم والمسمى الوظيفي
  if (additionalData?.newDepartment || additionalData?.newJobTitle) {
    await transaction.request()
      .input('EmployeeCode', employeeCode)
      .input('NewDepartment', additionalData?.newDepartment || employee.Department)
      .input('NewJobTitle', additionalData?.newJobTitle || employee.JobTitle)
      .query(`
        UPDATE Employees
        SET
          Department = @NewDepartment,
          JobTitle = @NewJobTitle,
          LastModified = GETDATE()
        WHERE EmployeeCode = @EmployeeCode
      `);
  }
}

// معالجة التفعيل
async function handleActivation(transaction, employeeCode, employee, reason, additionalData) {
  // إعادة تفعيل الموظف في الشقق إذا كان مقيم
  if (additionalData?.isResidentEmployee) {
    await transaction.request()
      .input('EmployeeCode', employeeCode)
      .input('ApartmentCode', additionalData?.apartmentCode)
      .input('StartDate', additionalData?.startDate || new Date())
      .query(`
        INSERT INTO ApartmentBeneficiaries
        (ApartmentID, EmployeeCode, EmployeeName, JobTitle, Department, StartDate, IsActive)
        SELECT
          a.ID, @EmployeeCode, e.EmployeeName, e.JobTitle, e.Department, @StartDate, 1
        FROM Apartments a
        CROSS JOIN Employees e
        WHERE a.ApartmentCode = @ApartmentCode AND e.EmployeeCode = @EmployeeCode
      `);

    // تحديث بيانات السكن
    await transaction.request()
      .input('EmployeeCode', employeeCode)
      .input('ApartmentCode', additionalData?.apartmentCode)
      .query(`
        UPDATE Employees
        SET
          IsResidentEmployee = 1,
          CompanyHousing = @ApartmentCode,
          LastModified = GETDATE()
        WHERE EmployeeCode = @EmployeeCode
      `);
  }

  // إعادة تفعيل الموظف في السيارات إذا كان مستفيد
  if (additionalData?.carCode) {
    await transaction.request()
      .input('EmployeeCode', employeeCode)
      .input('CarCode', additionalData?.carCode)
      .input('StartDate', additionalData?.startDate || new Date())
      .query(`
        INSERT INTO CarBeneficiaries
        (CarCode, EmployeeCode, EmployeeName, JobTitle, Department, StartDate, IsActive)
        SELECT
          @CarCode, @EmployeeCode, e.EmployeeName, e.JobTitle, e.Department, @StartDate, 1
        FROM Employees e
        WHERE e.EmployeeCode = @EmployeeCode
      `);

    // تحديث بيانات المواصلات
    await transaction.request()
      .input('EmployeeCode', employeeCode)
      .input('CarCode', additionalData?.carCode)
      .query(`
        UPDATE Employees
        SET
          TransportMethod = @CarCode,
          LastModified = GETDATE()
        WHERE EmployeeCode = @EmployeeCode
      `);
  }
}
