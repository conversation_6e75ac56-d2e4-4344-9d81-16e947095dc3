'use client';

import { useState, useEffect } from 'react';
import { 
  FiDollarSign, FiTrendingUp, FiTrendingDown, FiActivity, 
  FiRefreshCw, FiDownload, FiEye, FiCalendar, FiPercent 
} from 'react-icons/fi';
import MainLayout from '@/components/MainLayout';

export default function CustodyDetailsPage() {
  const [custodyData, setCustodyData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const fetchCustodyData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/custody-stats');
      const result = await response.json();

      if (result.success) {
        setCustodyData(result.data);
      } else {
        setError(result.error || 'فشل في جلب البيانات');
      }
    } catch (error) {

      setError('حدث خطأ في الاتصال بالخادم');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCustodyData();
  }, []);

  if (loading) {
    return (
      <MainLayout>
        <div className="max-w-7xl mx-auto p-6">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <span className="mr-3 text-gray-600">جاري تحميل بيانات العُهد المستديمة...</span>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout>
        <div className="max-w-7xl mx-auto p-6">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <div className="text-red-500 mr-2">⚠️</div>
              <span className="text-red-700">{error}</span>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto p-6">
        {/* العنوان */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                💰 تفاصيل العُهد المستديمة
              </h1>
              <p className="text-gray-600">
                عرض شامل لحالة العُهد المستديمة والتسويات المالية
              </p>
            </div>
            <button
              onClick={fetchCustodyData}
              className="flex items-center gap-2 bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg transition-colors"
            >
              <FiRefreshCw className="w-4 h-4" />
              تحديث البيانات
            </button>
          </div>
        </div>

        {custodyData && (
          <>
            {/* البطاقات الرئيسية */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {/* إجمالي العُهد */}
              <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl p-6 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-100 text-sm">إجمالي العُهد</p>
                    <p className="text-2xl font-bold">{formatCurrency(custodyData.totalAmount)}</p>
                  </div>
                  <FiDollarSign className="w-10 h-10 text-blue-200" />
                </div>
              </div>

              {/* الرصيد المتاح */}
              <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-xl p-6 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100 text-sm">الرصيد المتاح</p>
                    <p className="text-2xl font-bold">{formatCurrency(custodyData.availableBalance)}</p>
                    <p className="text-green-200 text-xs">{custodyData.availabilityPercentage}% من الإجمالي</p>
                  </div>
                  <FiTrendingUp className="w-10 h-10 text-green-200" />
                </div>
              </div>

              {/* قيد المراجعة */}
              <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl p-6 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-orange-100 text-sm">قيد المراجعة</p>
                    <p className="text-2xl font-bold">{formatCurrency(custodyData.pendingAmount)}</p>
                    <p className="text-orange-200 text-xs">{custodyData.utilizationPercentage}% من الإجمالي</p>
                  </div>
                  <FiActivity className="w-10 h-10 text-orange-200" />
                </div>
              </div>

              {/* المسويات المكتملة */}
              <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl p-6 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-100 text-sm">المسويات المكتملة</p>
                    <p className="text-2xl font-bold">{formatCurrency(custodyData.completedSettlements)}</p>
                    <p className="text-purple-200 text-xs">آخر تسوية: {custodyData.lastSettlementNumber}</p>
                  </div>
                  <FiTrendingDown className="w-10 h-10 text-purple-200" />
                </div>
              </div>
            </div>

            {/* الإحصائيات التفصيلية */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              {/* إحصائيات التسويات */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center gap-2">
                  <FiActivity className="w-5 h-5 text-blue-500" />
                  إحصائيات التسويات
                </h3>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <span className="text-gray-700">إجمالي التسويات</span>
                    <span className="font-bold text-gray-900">{custodyData.totalSettlements}</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                    <span className="text-gray-700">التسويات المكتملة</span>
                    <span className="font-bold text-green-700">{custodyData.completedCount}</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-4 bg-orange-50 rounded-lg">
                    <span className="text-gray-700">قيد المراجعة</span>
                    <span className="font-bold text-orange-700">{custodyData.pendingCount}</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-4 bg-red-50 rounded-lg">
                    <span className="text-gray-700">التسويات الملغية</span>
                    <span className="font-bold text-red-700">{custodyData.cancelledCount}</span>
                  </div>
                </div>
              </div>

              {/* إحصائيات شهرية */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center gap-2">
                  <FiCalendar className="w-5 h-5 text-green-500" />
                  الإحصائيات الشهرية
                </h3>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                    <span className="text-gray-700">تسويات هذا الشهر</span>
                    <span className="font-bold text-blue-700">{custodyData.thisMonthSettlements}</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg">
                    <span className="text-gray-700">مبلغ هذا الشهر</span>
                    <span className="font-bold text-green-700">{formatCurrency(custodyData.thisMonthAmount)}</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-4 bg-purple-50 rounded-lg">
                    <span className="text-gray-700">معدل دوران العُهد</span>
                    <span className="font-bold text-purple-700">{custodyData.turnoverRate}</span>
                  </div>
                  
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <span className="text-gray-700">آخر تحديث</span>
                    <span className="font-bold text-gray-700">
                      {custodyData.lastSettlementDate ? 
                        new Date(custodyData.lastSettlementDate).toLocaleDateString('ar-EG') : 
                        'غير محدد'
                      }
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* مؤشرات الأداء */}
            <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
              <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center gap-2">
                <FiPercent className="w-5 h-5 text-indigo-500" />
                مؤشرات الأداء
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* نسبة الاستخدام */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-gray-700">نسبة الاستخدام</span>
                    <span className="font-bold text-gray-900">{custodyData.utilizationPercentage}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div 
                      className="bg-orange-500 h-3 rounded-full transition-all duration-300"
                      style={{ width: `${custodyData.utilizationPercentage}%` }}
                    ></div>
                  </div>
                </div>

                {/* نسبة التوفر */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-gray-700">نسبة التوفر</span>
                    <span className="font-bold text-gray-900">{custodyData.availabilityPercentage}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div 
                      className="bg-green-500 h-3 rounded-full transition-all duration-300"
                      style={{ width: `${custodyData.availabilityPercentage}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>

            {/* التسويات الأخيرة */}
            {custodyData.recentSettlements && custodyData.recentSettlements.length > 0 && (
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center gap-2">
                  <FiEye className="w-5 h-5 text-gray-500" />
                  آخر التسويات
                </h3>
                
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-right py-3 px-4 font-medium text-gray-700">رقم التسوية</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-700">المبلغ</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-700">الفئة الرئيسية</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-700">الفئة الفرعية</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-700">الحالة</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-700">التاريخ</th>
                      </tr>
                    </thead>
                    <tbody>
                      {custodyData.recentSettlements.map((settlement, index) => (
                        <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                          <td className="py-3 px-4">{settlement.SettlementNumber}</td>
                          <td className="py-3 px-4 font-medium">{formatCurrency(settlement.SettlementValue)}</td>
                          <td className="py-3 px-4">{settlement.MainCategory}</td>
                          <td className="py-3 px-4">{settlement.SubCategory}</td>
                          <td className="py-3 px-4">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              settlement.Status === 'مكتمل' ? 'bg-green-100 text-green-800' :
                              settlement.Status === 'قيد المراجعة' ? 'bg-orange-100 text-orange-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {settlement.Status}
                            </span>
                          </td>
                          <td className="py-3 px-4">
                            {new Date(settlement.Date).toLocaleDateString('ar-EG')}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </MainLayout>
  );
}
