import fs from 'fs';
import sql from 'mssql';
import { NextResponse } from 'next/server';
import path from 'path';

// إعدادات قاعدة البيانات
const dbConfig = {
  user: 'SA',
  password: 'admin@123',
  server: 'localhost\\DBOJESTA',
  database: 'EMP',
  options: {
    encrypt: false,
    trustServerCertificate: true,
    enableArithAbort: true,
    instanceName: 'DBOJESTA'
  }
};

// دالة للتحقق من الملاحق في قاعدة البيانات
async function checkApartmentAnnexes() {
  try {
    const pool = await sql.connect(dbConfig);
    const result = await pool.request().query(`
      SELECT
        [الشهر] as Month,
        [السنة] as Year,
        [ملحق] as IsAnnex,
        [ملف_الملحق] as AnnexFile
      FROM APARTMENTCOST
      WHERE [ملحق] = 1
    `);

    await pool.close();
    return result.recordset;
  } catch (error) {
    console.error('❌ خطأ في جلب بيانات الملاحق:', error);
    return [];
  }
}

export async function GET(request) {
  try {
    const timestamp = new Date().toISOString();
    console.log(`📁 جلب طلبات الإصدار في ${timestamp}...`);

    // جلب بيانات الملاحق من قاعدة البيانات
    const apartmentAnnexes = await checkApartmentAnnexes();
    console.log(`📋 تم جلب ${apartmentAnnexes.length} ملحق من قاعدة البيانات`);

    // جلب الملفات الحقيقية من المسارات الصحيحة
    const requests = [];

    // المسارات الصحيحة من جدول ARCHIV (بدون slash في النهاية)
    const archivePaths = [
      {
        type: 'cars',
        name: 'تكاليف السيارات المؤجرة',
        path: 'E:\\web\\project\\archiv\\carscost'
      },
      {
        type: 'apartments',
        name: 'تكاليف الشقق المؤجرة',
        path: 'E:\\web\\project\\archiv\\housingcost'
      },
      {
        type: 'apartments-annex',
        name: 'ملاحق تكاليف الشقق',
        path: 'E:\\web\\project\\archiv\\apartments_annex'
      },
      {
        type: 'temp-workers',
        name: 'تكاليف العمالة المؤقتة',
        path: 'E:\\web\\project\\archiv\\3amala'
      }
    ];

    // جلب جميع الملفات من كل مجلد
    for (const folder of archivePaths) {
      try {
        console.log(`🔍 فحص مجلد: ${folder.path}`);
        if (fs.existsSync(folder.path)) {
          console.log(`✅ المجلد موجود: ${folder.type}`);
          const allFiles = fs.readdirSync(folder.path);
          console.log(`📁 إجمالي الملفات: ${allFiles.length}`);

          const files = allFiles
            .filter(file => file.toLowerCase().endsWith('.pdf'))
            .sort((a, b) => {
              // ترتيب الملفات حسب الشهر والسنة (الأحدث أولاً)
              const partsA = a.replace('.pdf', '').split('-');
              const partsB = b.replace('.pdf', '').split('-');
              const yearA = parseInt(partsA[1]) || 2024;
              const yearB = parseInt(partsB[1]) || 2024;
              const monthA = parseInt(partsA[0]) || 1;
              const monthB = parseInt(partsB[0]) || 1;

              if (yearA !== yearB) return yearB - yearA;
              return monthB - monthA;
            });

          console.log(`📄 ملفات PDF: ${files.length}`);

          for (const file of files) {
            const filePath = path.join(folder.path, file);
            const stats = fs.statSync(filePath);

            // استخراج الشهر والسنة
            const parts = file.replace('.pdf', '').split('-');
            const month = parseInt(parts[0]) || 1;
            const year = parseInt(parts[1]) || 2024;

            // التحقق من نوع الملف (أساسي أم ملحق)
            let fileType = folder.type;
            let typeName = folder.name;
            let isAnnex = false;

            // تحديد نوع الملف بناءً على المجلد
            if (folder.type === 'apartments-annex') {
              isAnnex = true;
            }
            requests.push({
              id: `${fileType}-${file}`,
              type: fileType,
              typeName: typeName,
              month: month,
              year: year,
              fileName: file,
              uploadDate: stats.mtime.toISOString().split('T')[0],
              fileSize: `${Math.round(stats.size / 1024)} KB`,
              fileSizeBytes: stats.size,
              lastModified: stats.mtime.toISOString(),
              status: 'مرفوع',
              isAnnex: isAnnex,
              source: 'file'
            });
          }
        } else {
          console.log(`❌ المجلد غير موجود: ${folder.path}`);
        }
      } catch (error) {
        console.log(`⚠️ خطأ في قراءة ${folder.type}:`, error.message);
      }
    }

    console.log(`✅ تم إنشاء ${requests.length} طلب إصدار تجريبي`);

    // ترتيب النتائج حسب التاريخ (الأحدث أولاً)
    requests.sort((a, b) => new Date(b.uploadDate) - new Date(a.uploadDate));

    // إحصائيات مفصلة
    const stats = {
      total: requests.length,
      cars: requests.filter(r => r.type === 'cars').length,
      apartments: requests.filter(r => r.type === 'apartments').length,
      apartmentsAnnex: requests.filter(r => r.type === 'apartments-annex').length,
      tempWorkers: requests.filter(r => r.type === 'temp-workers').length,
      folders: {
        cars: 'E:\\web\\project\\archiv\\carscost',
        apartments: 'E:\\web\\project\\archiv\\housingcost',
        apartmentsAnnex: 'E:\\web\\project\\archiv\\apartments_annex',
        tempWorkers: 'E:\\web\\project\\archiv\\3amala'
      },
      scanTime: timestamp
    };

    console.log(`📊 إحصائيات طلبات الإصدار:`, stats);

    const response = NextResponse.json({
      success: true,
      data: requests,
      stats: stats,
      timestamp: timestamp,
      lastUpdate: new Date().toISOString()
    });

    // إضافة headers لمنع الـ caching
    response.headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');
    response.headers.set('Surrogate-Control', 'no-store');

    return response;

  } catch (error) {
    console.error('❌ خطأ في API version-requests:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب طلبات الإصدار: ' + error.message
    }, { status: 500 });
  }
}
