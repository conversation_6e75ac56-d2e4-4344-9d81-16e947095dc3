'use client';

import { useTheme } from '@/contexts/ThemeContext';
import { useLanguage } from '@/contexts/LanguageContext';

const CostCard = ({
  title,
  value,
  icon: Icon,
  color = 'blue',
  href,
  description,
  trend,
  trendValue,
  isLoading = false,
  animationDelay = 0,
  lastSettlement,
  utilizationRate,
  additionalInfo
}) => {
  const { isDarkMode } = useTheme();
  const { isArabic } = useLanguage();

  // ألوان الإطار الدائر
  const borderColors = {
    blue: '#3B82F6',
    purple: '#8B5CF6',
    green: '#10B981',
    orange: '#F97316',
    red: '#EF4444'
  };

  const colorClasses = {
    blue: {
      bg: isDarkMode ? 'from-blue-600/20 to-blue-700/20' : 'from-blue-50 to-blue-100',
      border: 'border-blue-500/30',
      icon: 'text-blue-600',
      iconBg: isDarkMode ? 'bg-blue-600/20' : 'bg-blue-100',
      text: isDarkMode ? 'text-blue-400' : 'text-blue-600',
      hover: isDarkMode ? 'hover:from-blue-600/30 hover:to-blue-700/30' : 'hover:from-blue-100 hover:to-blue-200',
      shadow: 'shadow-blue-500/20'
    },
    purple: {
      bg: isDarkMode ? 'from-purple-600/20 to-purple-700/20' : 'from-purple-50 to-purple-100',
      border: 'border-purple-500/30',
      icon: 'text-purple-600',
      iconBg: isDarkMode ? 'bg-purple-600/20' : 'bg-purple-100',
      text: isDarkMode ? 'text-purple-400' : 'text-purple-600',
      hover: isDarkMode ? 'hover:from-purple-600/30 hover:to-purple-700/30' : 'hover:from-purple-100 hover:to-purple-200',
      shadow: 'shadow-purple-500/20'
    },
    green: {
      bg: isDarkMode ? 'from-green-600/20 to-green-700/20' : 'from-green-50 to-green-100',
      border: 'border-green-500/30',
      icon: 'text-green-600',
      iconBg: isDarkMode ? 'bg-green-600/20' : 'bg-green-100',
      text: isDarkMode ? 'text-green-400' : 'text-green-600',
      hover: isDarkMode ? 'hover:from-green-600/30 hover:to-green-700/30' : 'hover:from-green-100 hover:to-green-200',
      shadow: 'shadow-green-500/20'
    },
    orange: {
      bg: isDarkMode ? 'from-orange-600/20 to-orange-700/20' : 'from-orange-50 to-orange-100',
      border: 'border-orange-500/30',
      icon: 'text-orange-600',
      iconBg: isDarkMode ? 'bg-orange-600/20' : 'bg-orange-100',
      text: isDarkMode ? 'text-orange-400' : 'text-orange-600',
      hover: isDarkMode ? 'hover:from-orange-600/30 hover:to-orange-700/30' : 'hover:from-orange-100 hover:to-orange-200',
      shadow: 'shadow-orange-500/20'
    },
    red: {
      bg: isDarkMode ? 'from-red-600/20 to-red-700/20' : 'from-red-50 to-red-100',
      border: 'border-red-500/30',
      icon: 'text-red-600',
      iconBg: isDarkMode ? 'bg-red-600/20' : 'bg-red-100',
      text: isDarkMode ? 'text-red-400' : 'text-red-600',
      hover: isDarkMode ? 'hover:from-red-600/30 hover:to-red-700/30' : 'hover:from-red-100 hover:to-red-200',
      shadow: 'shadow-red-500/20'
    }
  };

  const currentColor = colorClasses[color] || colorClasses.blue;

  const CardContent = () => (
    <div
      className={`
        relative rounded-xl p-6 transition-all duration-300 ease-out
        bg-gradient-to-br ${currentColor.bg}
        border ${currentColor.border}
        shadow-lg ${currentColor.shadow} hover:shadow-xl hover:shadow-${color}-500/30
        transform hover:scale-105
        ${isDarkMode ? 'backdrop-blur-sm' : ''}
        group cursor-pointer
      `}
      style={{
        animationDelay: `${animationDelay}ms`,
        animation: 'fadeInUp 0.6s ease-out forwards'
      }}
    >
      {/* إطار ملون دائر - يظهر فقط عند hover */}
      <div
        className="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
        style={{
          background: `conic-gradient(from 0deg, transparent, ${borderColors[color]}, transparent)`,
          animation: 'rotating-border 2s linear infinite',
          mask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
          maskComposite: 'xor',
          WebkitMask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
          WebkitMaskComposite: 'xor',
          padding: '2px'
        }}
      ></div>

      <div className="relative z-10">
        {/* الرأس */}
        <div className="flex items-center justify-between mb-4">
          <div className={`p-3 rounded-lg ${currentColor.iconBg} group-hover:scale-110 transition-transform duration-300`}>
            <Icon className={`text-xl ${currentColor.icon}`} />
          </div>

          {trend && (
            <div className={`text-xs px-2 py-1 rounded-full ${
              trend === 'up' ? 'bg-green-100 text-green-700' :
              trend === 'down' ? 'bg-red-100 text-red-700' :
              'bg-gray-100 text-gray-700'
            }`}>
              {trendValue}
            </div>
          )}
        </div>

        {/* العنوان والقيمة */}
        <div className="mb-4">
          <h3 className={`text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            {title}
          </h3>

          {isLoading ? (
            <div className="animate-pulse">
              <div className={`h-8 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-200'} rounded mb-2`}></div>
              <div className={`h-4 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-200'} rounded w-3/4`}></div>
            </div>
          ) : (
            <>
              <div className={`text-2xl font-bold mb-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {value}
              </div>

              {/* معلومات إضافية للعُهد المستديمة */}
              {lastSettlement && (
                <div className="mb-2">
                  <div className={`text-xs font-medium ${currentColor.text} bg-${color}-500/10 px-2 py-1 rounded-full inline-block`}>
                    آخر تسوية: رقم {lastSettlement}
                  </div>
                </div>
              )}

              {/* معدل الاستخدام */}
              {utilizationRate !== undefined && (
                <div className="mb-2">
                  <div className="flex items-center justify-between text-xs mb-1">
                    <span className={isDarkMode ? 'text-gray-400' : 'text-gray-600'}>معدل الاستخدام</span>
                    <span className={`font-medium ${currentColor.text}`}>{utilizationRate}%</span>
                  </div>
                  <div className={`w-full ${isDarkMode ? 'bg-gray-700' : 'bg-gray-200'} rounded-full h-2`}>
                    <div
                      className={`bg-gradient-to-r from-${color}-500 to-${color}-400 h-2 rounded-full transition-all duration-1000`}
                      style={{ width: `${Math.min(utilizationRate, 100)}%` }}
                    ></div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>

        {/* الوصف */}
        {description && !isLoading && (
          <div className={`text-xs space-y-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {typeof description === 'string' ? (
              <div>{description}</div>
            ) : (
              description
            )}
          </div>
        )}

        {/* معلومات إضافية */}
        {additionalInfo && !isLoading && (
          <div className="mt-3 pt-3 border-t border-gray-200/20">
            <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {additionalInfo}
            </div>
          </div>
        )}
      </div>
    </div>
  );

  if (href) {
    return (
      <a href={href} className="block">
        <CardContent />
      </a>
    );
  }

  return <CardContent />;
};

export default CostCard;
