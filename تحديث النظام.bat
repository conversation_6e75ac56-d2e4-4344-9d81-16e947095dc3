@echo off
chcp 65001 >nul
title تحديث نظام إدارة التكاليف
color 0E

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    تحديث نظام إدارة التكاليف                  ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: الانتقال إلى مجلد المشروع
cd /d "E:\web\project"

echo 🔄 جاري تحديث النظام...
echo.

:: إيقاف أي خادم يعمل
echo [1/4] إيقاف الخادم إذا كان يعمل...
taskkill /f /im node.exe >nul 2>&1
echo ✅ تم إيقاف الخادم

:: تنظيف cache npm
echo [2/4] تنظيف cache npm...
npm cache clean --force >nul 2>&1
echo ✅ تم تنظيف cache

:: حذف node_modules وإعادة التثبيت
echo [3/4] إعادة تثبيت التبعيات...
if exist "node_modules" (
    echo 🗑️  حذف مجلد node_modules القديم...
    rmdir /s /q "node_modules"
)

if exist "package-lock.json" (
    echo 🗑️  حذف package-lock.json القديم...
    del "package-lock.json"
)

echo 📦 تثبيت التبعيات الجديدة...
npm install
if %errorlevel% neq 0 (
    echo ❌ خطأ في تثبيت التبعيات
    pause
    exit /b 1
)
echo ✅ تم تثبيت التبعيات بنجاح

:: فحص التبعيات
echo [4/4] فحص التبعيات...
npm audit fix >nul 2>&1
echo ✅ تم فحص وإصلاح التبعيات

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    تم تحديث النظام بنجاح                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🚀 يمكنك الآن تشغيل النظام باستخدام:
echo    - تشغيل النظام.cmd
echo    - start-system.bat
echo.

pause
