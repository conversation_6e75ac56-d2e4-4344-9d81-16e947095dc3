import { NextResponse } from 'next/server';
import { getConnection } from '@/utils/db';
import sql from 'mssql';

export async function POST(request) {
  try {
    const body = await request.json();
    const { action } = body;

    const pool = await getConnection();

    switch (action) {
      case 'setup':
        return await setupLeaveSystem(pool);
      case 'test':
        return await testLeaveSystem(pool);
      case 'stats':
        return await getSystemStats(pool);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  }
}

// إعداد نظام الإجازات
async function setupLeaveSystem(pool) {
  try {

    // 1. إنشاء جدول رصيد الإجازات
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LeaveBalances' AND xtype='U')
      BEGIN
        CREATE TABLE LeaveBalances (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          EmployeeCode NVARCHAR(20) NOT NULL UNIQUE,
          EmployeeName NVARCHAR(100) NOT NULL,
          JobTitle NVARCHAR(100) NOT NULL,
          Department NVARCHAR(100),
          RegularBalance INT DEFAULT 15,
          CasualBalance INT DEFAULT 6,
          UsedRegular INT DEFAULT 0,
          UsedCasual INT DEFAULT 0,
          RemainingRegular AS (RegularBalance - UsedRegular),
          RemainingCasual AS (CasualBalance - UsedCasual),
          Year INT DEFAULT YEAR(GETDATE()),
          LastLeaveDate DATE,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE()
        )
        
        CREATE INDEX IX_LeaveBalances_EmployeeCode ON LeaveBalances(EmployeeCode)
        CREATE INDEX IX_LeaveBalances_Year ON LeaveBalances(Year)
      END
    `);

    // 2. إنشاء جدول طلبات الإجازات
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LeaveRequests' AND xtype='U')
      BEGIN
        CREATE TABLE LeaveRequests (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          EmployeeCode NVARCHAR(20) NOT NULL,
          EmployeeName NVARCHAR(100) NOT NULL,
          JobTitle NVARCHAR(100) NOT NULL,
          Department NVARCHAR(100),
          Project NVARCHAR(100),
          LeaveType NVARCHAR(50) NOT NULL,
          StartDate DATE NOT NULL,
          EndDate DATE NOT NULL,
          DaysCount INT NOT NULL,
          Reason NVARCHAR(MAX),
          Status NVARCHAR(20) DEFAULT N'قيد المراجعة',
          RequestDate DATE DEFAULT GETDATE(),
          EmployeeSignature NVARCHAR(100),
          DirectManagerSignature NVARCHAR(100),
          ProjectManagerSignature NVARCHAR(100),
          HRManagerSignature NVARCHAR(100),
          DirectManagerApprovalDate DATE,
          ProjectManagerApprovalDate DATE,
          HRManagerApprovalDate DATE,
          Notes NVARCHAR(MAX),
          HRNotes NVARCHAR(MAX),
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE()
        )
        
        CREATE INDEX IX_LeaveRequests_EmployeeCode ON LeaveRequests(EmployeeCode)
        CREATE INDEX IX_LeaveRequests_Status ON LeaveRequests(Status)
        CREATE INDEX IX_LeaveRequests_LeaveType ON LeaveRequests(LeaveType)
        CREATE INDEX IX_LeaveRequests_Dates ON LeaveRequests(StartDate, EndDate)
      END
    `);

    // 3. إدراج بيانات رصيد الإجازات للموظفين الموجودين
    await pool.request().query(`
      INSERT INTO LeaveBalances (EmployeeCode, EmployeeName, JobTitle, Department)
      SELECT DISTINCT
        e.EmployeeID,
        e.FullName,
        e.JobTitle,
        e.Department
      FROM Employees e
      WHERE e.EmployeeID IS NOT NULL 
        AND e.EmployeeID != ''
        AND NOT EXISTS (
          SELECT 1 FROM LeaveBalances lb 
          WHERE lb.EmployeeCode = e.EmployeeID
        )
    `);

    // 4. الحصول على الإحصائيات
    const stats = await getSystemStats(pool);

    return NextResponse.json({
      success: true,
      message: 'تم إعداد نظام الإجازات بنجاح',
      data: stats.data
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في إعداد نظام الإجازات: ' + error.message
    }, { status: 500 });
  }
}

// اختبار نظام الإجازات
async function testLeaveSystem(pool) {
  try {
    // اختبار الجداول
    const tablesCheck = await pool.request().query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_NAME IN ('LeaveBalances', 'LeaveRequests')
    `);

    const tables = tablesCheck.recordset.map(t => t.TABLE_NAME);
    
    // اختبار البيانات
    const sampleEmployee = await pool.request().query(`
      SELECT TOP 1 * FROM LeaveBalances
    `);

    return NextResponse.json({
      success: true,
      data: {
        tablesCreated: tables,
        sampleEmployee: sampleEmployee.recordset[0] || null,
        systemReady: tables.length === 2
      }
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'فشل في اختبار النظام: ' + error.message
    }, { status: 500 });
  }
}

// الحصول على إحصائيات النظام
async function getSystemStats(pool) {
  try {
    // عد الموظفين
    const employeesCount = await pool.request().query(`
      SELECT COUNT(*) as Count FROM LeaveBalances
    `);

    // عد طلبات الإجازات
    const requestsCount = await pool.request().query(`
      SELECT COUNT(*) as Count FROM LeaveRequests
    `);

    // عد الطلبات حسب الحالة
    const statusStats = await pool.request().query(`
      SELECT Status, COUNT(*) as Count
      FROM LeaveRequests
      GROUP BY Status
    `);

    // عد الطلبات حسب النوع
    const typeStats = await pool.request().query(`
      SELECT LeaveType, COUNT(*) as Count
      FROM LeaveRequests
      GROUP BY LeaveType
    `);

    return NextResponse.json({
      success: true,
      data: {
        totalEmployees: employeesCount.recordset[0].Count,
        totalRequests: requestsCount.recordset[0].Count,
        statusBreakdown: statusStats.recordset,
        typeBreakdown: typeStats.recordset
      }
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب الإحصائيات: ' + error.message
    }, { status: 500 });
  }
}
