import { getConnection } from '@/utils/db';
import { NextResponse } from 'next/server';

export async function GET(request) {
  const { searchParams } = new URL(request.url);
  const month = parseInt(searchParams.get('month')) || new Date().getMonth() + 1;
  const year = parseInt(searchParams.get('year')) || new Date().getFullYear();

  let pool;

  try {

    pool = await getConnection();

    // حساب فترة التقفيل من 11 إلى 10
    let startDate, endDate;
    if (month === 1) {
      // يناير: من 11 ديسمبر السنة السابقة إلى 10 يناير
      startDate = new Date(year - 1, 11, 11); // 11 ديسمبر السنة السابقة
      endDate = new Date(year, 0, 10); // 10 يناير
    } else {
      // باقي الشهور: من 11 الشهر السابق إلى 10 الشهر الحالي
      startDate = new Date(year, month - 2, 11); // 11 من الشهر السابق
      endDate = new Date(year, month - 1, 10); // 10 من الشهر الحالي
    }

    console.log(`📅 فترة البحث: من ${startDate.toISOString().split('T')[0]} إلى ${endDate.toISOString().split('T')[0]}`);

    // جلب بيانات الحضور للفترة المحددة
    const attendanceQuery = `
      SELECT
        EmployeeCode,
        AttendanceDate,
        ISNULL(Attendance, '') as AttendanceStatus,
        ISNULL(Notes, '') as Notes
      FROM DailyAttendance
      WHERE AttendanceDate >= @startDate
        AND AttendanceDate <= @endDate
      ORDER BY EmployeeCode, AttendanceDate
    `;

    const result = await pool.request()
      .input('startDate', startDate)
      .input('endDate', endDate)
      .query(attendanceQuery);

    console.log(`📅 الفترة المطلوبة: من ${startDate.toISOString().split('T')[0]} إلى ${endDate.toISOString().split('T')[0]}`);

    // لا توجد بيانات تمام - عرض تقرير فارغ
    if (result.recordset.length === 0) {
      console.log('⚠️ لا توجد بيانات تمام للفترة المحددة');
    }

    // تنظيم البيانات حسب الموظف واليوم (من 1 إلى 30)
    const attendanceData = {};

    result.recordset.forEach(record => {
      const employeeCode = record.EmployeeCode;
      const recordDate = new Date(record.AttendanceDate);

      // حساب رقم اليوم في دورة التمام (من 1 إلى 30)
      let dayNumber;
      if (recordDate >= startDate && recordDate <= endDate) {
        const diffTime = recordDate.getTime() - startDate.getTime();
        dayNumber = Math.floor(diffTime / (1000 * 60 * 60 * 24)) + 1;
      } else {
        return; // تجاهل التواريخ خارج النطاق
      }

      if (!attendanceData[employeeCode]) {
        attendanceData[employeeCode] = {};
      }

      // تحويل حالة الحضور إلى رمز
      let code = '';
      const status = record.AttendanceStatus || '';

      switch(status.trim()) {
        case 'حضور':
          code = 'W';
          break;
        case 'غياب':
          code = 'Ab';
          break;
        case 'إجازة مرضية':
          code = 'S';
          break;
        case 'راحة':
          code = 'R';
          break;
        case 'إجازة رسمية':
          code = 'NH';
          break;
        case 'مأمورية':
          code = 'M';
          break;
        case 'إجازة إعتيادية':
        case 'إجازة اعتيادية':
          code = 'AL';
          break;
        case 'إجازة عارضة':
          code = 'CL';
          break;
        case 'إجازة بدون راتب':
        case 'إجازة بدون أجر':
          code = 'UL';
          break;
        case 'إجازة أمومة':
        case 'إجازة وضع':
          code = 'ML';
          break;
        case 'إجازة بدل':
        case 'بدل راحة':
          code = 'CR';
          break;
        case 'وردية ليلية':
          code = 'NS';
          break;
        case 'استقالة':
          code = 'R';
          break;
        case 'نقل':
          code = 'T';
          break;
        case '':
        case null:
        case undefined:
          code = ''; // لا يوجد بيانات
          break;
        default:
          if (status.includes('إجازة')) {
            code = 'AL'; // إجازة عامة
          } else if (status.trim() !== '') {
            code = 'W'; // افتراضي للحضور
          } else {
            code = ''; // فارغ
          }
      }

      attendanceData[employeeCode][dayNumber] = code;
    });

    return NextResponse.json({
      success: true,
      attendance: attendanceData,
      month,
      year
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب بيانات الحضور الشهري',
      details: error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      try {

        await pool.close();

      } catch (error) {

      }
    }
  }
}

export async function POST(request) {
  let pool;

  try {
    const body = await request.json();
    const { action } = body;

    pool = await getConnection();

    // إضافة حقول الاستقالة والنقل إذا لم تكن موجودة (بدون إغلاق الاتصال)
    try {
      await pool.request().query(`
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Employees' AND COLUMN_NAME = 'ResignationDate')
        ALTER TABLE Employees ADD ResignationDate DATE
      `);

      await pool.request().query(`
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Employees' AND COLUMN_NAME = 'TransferDate')
        ALTER TABLE Employees ADD TransferDate DATE
      `);

      await pool.request().query(`
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Employees' AND COLUMN_NAME = 'TransferDestination')
        ALTER TABLE Employees ADD TransferDestination NVARCHAR(200)
      `);

    } catch (alterError) {

    }

    switch (action) {
      case 'get-data':
        return await handleGetData(pool, body);
      case 'update-attendance':
        return await handleUpdateAttendance(pool, body);
      case 'save-data':
        return await handleSaveData(pool, body);
      case 'recalculate-stats':
        return await handleRecalculateStats(pool, body);
      case 'export-excel':
        return await handleExportExcel(pool, body);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير مدعوم'
        }, { status: 400 });
    }
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم',
      details: error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      try {

        await pool.close();

      } catch (error) {

      }
    }
  }
}

// دالة جلب البيانات
async function handleGetData(pool, { month, year }) {
  try {
    // حساب فترة التقفيل من 11 إلى 10 أولاً
    // ملاحظة: في JavaScript، الشهور تبدأ من 0 (يناير = 0، فبراير = 1، إلخ)
    let startDate, endDate;
    if (month === 1) {
      // يناير: من 11 ديسمبر السنة السابقة إلى 10 يناير
      startDate = new Date(year - 1, 11, 11); // 11 ديسمبر (الشهر 11)
      endDate = new Date(year, 0, 10); // 10 يناير (الشهر 0)
    } else {
      // باقي الشهور: من 11 الشهر السابق إلى 10 الشهر الحالي
      startDate = new Date(year, month - 2, 11); // 11 من الشهر السابق
      endDate = new Date(year, month - 1, 10); // 10 من الشهر الحالي
    }

    // تنسيق التواريخ بشكل صحيح (بدون تحويل UTC)
    const formatDate = (date) => {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    };

    console.log(`📅 الفترة المطلوبة: من ${formatDate(startDate)} إلى ${formatDate(endDate)}`);

    console.log(`🔍 startDate = new Date(${year}, ${month - 2}, 11) = ${startDate.toString()}`);
    console.log(`🔍 endDate = new Date(${year}, ${month - 1}, 10) = ${endDate.toString()}`);

    // جلب بيانات الموظفين (بدون فلترة معقدة في البداية)
    const employeesQuery = `
      SELECT
        EmployeeCode,
        EmployeeName,
        JobTitle,
        Department
      FROM Employees
      ORDER BY CAST(EmployeeCode AS INT)
    `;

    const employeesResult = await pool.request().query(employeesQuery);

    let employees = employeesResult.recordset;

    // التحقق من وجود موظفين
    if (employees.length === 0) {
      console.log('⚠️ لا يوجد موظفين في النظام');
    }

    // جلب بيانات الحضور للفترة المحددة
    const attendanceQuery = `
      SELECT
        EmployeeCode,
        AttendanceDate,
        ISNULL(Attendance, '') as AttendanceStatus
      FROM DailyAttendance
      WHERE AttendanceDate >= @startDate
        AND AttendanceDate <= @endDate
      ORDER BY EmployeeCode, AttendanceDate
    `;

    const attendanceResult = await pool.request()
      .input('startDate', startDate)
      .input('endDate', endDate)
      .query(attendanceQuery);

    // لا توجد بيانات تمام - عرض تقرير فارغ
    if (attendanceResult.recordset.length === 0) {
      console.log('⚠️ لا توجد بيانات تمام للفترة المحددة');
    }

    // تنظيم البيانات حسب الموظف واليوم
    const attendanceData = {};

    attendanceResult.recordset.forEach(record => {
      const employeeCode = record.EmployeeCode;
      const recordDate = new Date(record.AttendanceDate);

      // حساب رقم اليوم في دورة التمام
      const diffTime = recordDate.getTime() - startDate.getTime();
      const dayNumber = Math.floor(diffTime / (1000 * 60 * 60 * 24)) + 1;

      if (dayNumber >= 1 && dayNumber <= 31) {
        if (!attendanceData[employeeCode]) {
          attendanceData[employeeCode] = {};
        }

        // تحويل حالة الحضور إلى رمز موحد
        let code = '';
        const status = record.AttendanceStatus || '';

        // الرموز المعتمدة حسب المطلوب:
        // W: حضور | Ab: غياب | S: إجازة مرضية | R: راحة | NH: إجازة رسمية
        // CR: راحة بدل | M: مأمورية | AL: إجازة اعتيادية | CL: إجازة عارضة
        // UL: إجازة بدون أجر | ML: إجازة أمومة
        switch(status.trim()) {
          case 'حضور':
            code = 'W';
            break;
          case 'غياب':
            code = 'Ab';
            break;
          case 'إجازة مرضية':
            code = 'S';
            break;
          case 'راحة':
            code = 'R';
            break;
          case 'إجازة رسمية':
            code = 'NH';
            break;
          case 'مأمورية':
            code = 'M';
            break;
          case 'إجازة إعتيادية':
          case 'إجازة اعتيادية':
            code = 'AL';
            break;
          case 'إجازة عارضة':
            code = 'CL';
            break;
          case 'إجازة بدون راتب':
          case 'إجازة بدون أجر':
            code = 'UL';
            break;
          case 'إجازة أمومة':
          case 'إجازة وضع':
            code = 'ML';
            break;
          case 'إجازة بدل':
          case 'بدل راحة':
          case 'راحة بدل':
            code = 'CR';
            break;
          case 'استقالة':
            // حالات الاستقالة والنقل لا تستخدم الرموز المعتمدة
            // سيتم التعامل معها بشكل منفصل
            code = '';
            break;
          case 'نقل':
            // حالات النقل لا تستخدم الرموز المعتمدة
            // سيتم التعامل معها بشكل منفصل
            code = '';
            break;
          default:
            // التعامل مع الحالات الغير محددة
            if (status.includes('إجازة')) {
              code = 'AL'; // افتراضي للإجازات
            } else if (status.trim() !== '') {
              code = 'W'; // افتراضي للحضور
            } else {
              code = ''; // فارغ للحالات غير المحددة
            }
        }

        attendanceData[employeeCode][dayNumber] = code;
      }
    });

    // معالجة خاصة للموظفين المستقيلين والمنقولين والجدد
    employees.forEach(employee => {
      const employeeCode = employee.EmployeeCode;
      const resignationDate = employee.ResignationDate ? new Date(employee.ResignationDate) : null;
      const transferDate = employee.TransferDate ? new Date(employee.TransferDate) : null;
      const hireDate = employee.HireDate ? new Date(employee.HireDate) : null;
      const joinDate = employee.JoinDate ? new Date(employee.JoinDate) : hireDate;

      if (!attendanceData[employeeCode]) {
        attendanceData[employeeCode] = {};
      }

      // معالجة الموظفين المستقيلين
      if (resignationDate) {
        console.log(`🔍 معالجة استقالة الموظف ${employeeCode}: تاريخ الاستقالة ${resignationDate.toISOString().split('T')[0]}`);

        // إذا كانت الاستقالة خلال فترة التقرير الحالية
        if (resignationDate >= startDate && resignationDate <= endDate) {
          const resignationDayNumber = Math.floor((resignationDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

          // ملء الأيام بعد الاستقالة بـ "-" (لكن الموظف يبقى في الكشف)
          for (let day = resignationDayNumber + 1; day <= 31; day++) {
            attendanceData[employeeCode][day] = '-';
          }
          console.log(`✅ تم ملء الأيام ${resignationDayNumber + 1}-31 بـ "-" للموظف ${employeeCode} (الموظف يبقى في الكشف)`);
        }
        // إذا كانت الاستقالة قبل أو بعد فترة التقرير، يظهر الموظف عادي
        // ملاحظة: لا نحذف الموظف من الكشف خلال الفترة، الحذف يتم بين الفترات فقط
      }

      // معالجة الموظفين المنقولين
      if (transferDate) {
        console.log(`🔍 معالجة نقل الموظف ${employeeCode}: تاريخ النقل ${transferDate.toISOString().split('T')[0]}`);

        // إذا كان النقل خلال فترة التقرير الحالية
        if (transferDate >= startDate && transferDate <= endDate) {
          const transferDayNumber = Math.floor((transferDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

          // ملء الأيام بعد النقل بـ "-" (لكن الموظف يبقى في الكشف)
          for (let day = transferDayNumber + 1; day <= 31; day++) {
            attendanceData[employeeCode][day] = '-';
          }
          console.log(`✅ تم ملء الأيام ${transferDayNumber + 1}-31 بـ "-" للموظف ${employeeCode} (الموظف يبقى في الكشف)`);
        }
        // إذا كان النقل قبل أو بعد فترة التقرير، يظهر الموظف عادي
        // ملاحظة: لا نحذف الموظف من الكشف خلال الفترة، الحذف يتم بين الفترات فقط
      }

      // معالجة الموظفين الجدد
      if (joinDate) {
        console.log(`🔍 معالجة انضمام الموظف ${employeeCode}: تاريخ الانضمام ${joinDate.toISOString().split('T')[0]}`);

        // إذا كان تاريخ الانضمام خلال فترة التقرير
        if (joinDate > startDate && joinDate <= endDate) {
          const joinDayNumber = Math.floor((joinDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

          // ملء الأيام قبل الانضمام بـ "-"
          for (let day = 1; day < joinDayNumber; day++) {
            attendanceData[employeeCode][day] = '-';
          }

        }
        // إذا كان تاريخ الانضمام قبل أو بعد فترة التقرير، يظهر الموظف عادي
        // ملاحظة: الموظفين الذين لم ينضموا بعد سيتم استبعادهم من الاستعلام الأساسي
      }
    });

    // تنظيف قائمة الموظفين من المحذوفين
    const filteredEmployees = employees.filter(emp =>
      attendanceData[emp.EmployeeCode] !== undefined
    );

    return NextResponse.json({
      success: true,
      employees: filteredEmployees,
      attendanceData,
      statistics: {},
      period: {
        startDate: formatDate(startDate),
        endDate: formatDate(endDate),
        month: month,
        year: year
      }
    });

  } catch (error) {

    throw error;
  }
}

// دالة تحديث الحضور
async function handleUpdateAttendance(pool, { employeeCode, day, code, month, year }) {
  try {
    // هنا يمكن إضافة منطق تحديث الحضور في قاعدة البيانات
    // حالياً نعيد نجاح العملية فقط
    return NextResponse.json({
      success: true,
      message: 'تم تحديث الحضور بنجاح'
    });
  } catch (error) {

    throw error;
  }
}

// دالة حفظ البيانات
async function handleSaveData(pool, { attendanceData, month, year }) {
  try {
    return NextResponse.json({
      success: true,
      message: 'تم حفظ البيانات بنجاح'
    });
  } catch (error) {

    throw error;
  }
}

// دالة إعادة حساب الإحصائيات
async function handleRecalculateStats(pool, { month, year }) {
  try {
    // جلب عدد الموظفين
    const employeesCountResult = await pool.request().query(`
      SELECT COUNT(*) as TotalEmployees FROM Employees
    `);

    const employeesCount = employeesCountResult.recordset[0]?.TotalEmployees || 0;

    return NextResponse.json({
      success: true,
      employeesCount: employeesCount,
      message: `تم إعادة حساب الإحصائيات لـ ${employeesCount} موظف`
    });
  } catch (error) {

    throw error;
  }
}

// دالة تصدير Excel
async function handleExportExcel(pool, { month, year, employees, attendanceData }) {
  try {
    const XLSX = require('xlsx');

    // إنشاء البيانات للتصدير
    const exportData = [];

    // إضافة رأس الجدول
    const header = ['م', 'كود الموظف', 'اسم الموظف', 'المسمى الوظيفي'];

    // إضافة أعمدة الأيام
    const daysInPeriod = month === 1 ? 31 : new Date(year, month - 1, 0).getDate() - 10 + 10;
    for (let i = 1; i <= daysInPeriod; i++) {
      header.push(`يوم ${i}`);
    }
    header.push('إحصائيات');

    exportData.push(header);

    // إضافة بيانات الموظفين
    employees.forEach((employee, index) => {
      const employeeCode = employee.EmployeeCode || employee.EmployeeID;
      const employeeAttendance = attendanceData[employeeCode] || {};

      const row = [
        index + 1,
        employeeCode,
        employee.EmployeeName || employee.FullName,
        employee.JobTitle
      ];

      // إضافة بيانات الحضور لكل يوم
      for (let day = 1; day <= daysInPeriod; day++) {
        row.push(employeeAttendance[day] || '');
      }

      // حساب الإحصائيات بالرموز المعتمدة
      const stats = {
        W: 0,    // حضور
        Ab: 0,   // غياب
        S: 0,    // إجازة مرضية
        R: 0,    // راحة
        NH: 0,   // إجازة رسمية
        CR: 0,   // راحة بدل
        M: 0,    // مأمورية
        AL: 0,   // إجازة اعتيادية
        CL: 0,   // إجازة عارضة
        UL: 0,   // إجازة بدون أجر
        ML: 0,   // إجازة أمومة
        resignation: 0, // استقالة
        transfer: 0,    // نقل
        notJoined: 0,   // لم ينضم
        absent: 0       // غائب (رمز -)
      };

      Object.values(employeeAttendance).forEach(code => {
        switch(code) {
          case 'W': stats.W++; break;
          case 'Ab': stats.Ab++; break;
          case 'S': stats.S++; break;
          case 'R': stats.R++; break;
          case 'NH': stats.NH++; break;
          case 'CR': stats.CR++; break;
          case 'M': stats.M++; break;
          case 'AL': stats.AL++; break;
          case 'CL': stats.CL++; break;
          case 'UL': stats.UL++; break;
          case 'ML': stats.ML++; break;
          case 'استقالة':
          case 'مستقيل': stats.resignation++; break;
          case 'نقل':
          case 'منقول': stats.transfer++; break;
          case 'لم ينضم': stats.notJoined++; break;
          case '-': stats.absent++; break;
        }
      });

      // تكوين نص الإحصائيات
      let statsText = `W:${stats.W}, Ab:${stats.Ab}, R:${stats.R}, M:${stats.M}`;
      if (stats.S > 0) statsText += `, S:${stats.S}`;
      if (stats.NH > 0) statsText += `, NH:${stats.NH}`;
      if (stats.CR > 0) statsText += `, CR:${stats.CR}`;
      if (stats.AL > 0) statsText += `, AL:${stats.AL}`;
      if (stats.CL > 0) statsText += `, CL:${stats.CL}`;
      if (stats.UL > 0) statsText += `, UL:${stats.UL}`;
      if (stats.ML > 0) statsText += `, ML:${stats.ML}`;
      if (stats.resignation > 0) statsText += `, استقالة:${stats.resignation}`;
      if (stats.transfer > 0) statsText += `, نقل:${stats.transfer}`;
      if (stats.notJoined > 0) statsText += `, لم ينضم:${stats.notJoined}`;
      if (stats.absent > 0) statsText += `, غائب:${stats.absent}`;

      row.push(statsText);

      exportData.push(row);
    });

    // إنشاء ملف Excel
    const ws = XLSX.utils.aoa_to_sheet(exportData);
    const wb = XLSX.utils.book_new();

    // تنسيق الجدول
    const range = XLSX.utils.decode_range(ws['!ref']);

    // تطبيق الألوان على خلايا الحضور
    for (let R = 1; R <= range.e.r; R++) {
      for (let C = 4; C < 4 + daysInPeriod; C++) {
        const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
        if (ws[cellAddress]) {
          const value = ws[cellAddress].v;
          if (value) {
            ws[cellAddress].s = {
              fill: {
                fgColor: { rgb: getColorForCode(value) }
              }
            };
          }
        }
      }
    }

    XLSX.utils.book_append_sheet(wb, ws, 'كشف التمام الشهري');

    // تحويل إلى buffer
    const buffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });

    return new NextResponse(buffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="كشف_التمام_الشهري_${month}_${year}.xlsx"`
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في تصدير Excel'
    });
  }
}

// دالة إنشاء بيانات تجريبية للحضور
async function createSampleAttendanceData(pool, startDate, endDate) {
  try {
    // جلب بعض الموظفين
    const employeesResult = await pool.request().query(`
      SELECT TOP 10 EmployeeCode, EmployeeName, JobTitle, Department
      FROM Employees
      ORDER BY EmployeeCode
    `);

    if (employeesResult.recordset.length === 0) {

      return;
    }

    const employees = employeesResult.recordset;
    const attendanceTypes = ['حضور', 'غياب', 'إجازة إعتيادية', 'راحة', 'مأمورية'];

    // إنشاء بيانات حضور لكل موظف لكل يوم في الفترة
    for (const employee of employees) {
      let currentDate = new Date(startDate);
      const employeeResignationDate = employee.ResignationDate ? new Date(employee.ResignationDate) : null;
      const employeeTransferDate = employee.TransferDate ? new Date(employee.TransferDate) : null;

      while (currentDate <= endDate) {
        let attendanceType = '';
        let notes = '';

        // تحديد نوع الحضور حسب حالة الموظف
        if (employeeResignationDate && currentDate.getTime() === employeeResignationDate.getTime()) {
          attendanceType = 'استقالة';
          notes = `تم تقديم استقالة بتاريخ ${employeeResignationDate.toLocaleDateString('ar-EG')}`;
        } else if (employeeTransferDate && currentDate.getTime() === employeeTransferDate.getTime()) {
          attendanceType = 'نقل';
          notes = `تم نقله إلى ${employee.TransferDestination || 'مشروع آخر'} بتاريخ ${employeeTransferDate.toLocaleDateString('ar-EG')}`;
        } else if (employeeResignationDate && currentDate > employeeResignationDate) {
          // لا نضيف بيانات حضور بعد تاريخ الاستقالة
          currentDate.setDate(currentDate.getDate() + 1);
          continue;
        } else if (employeeTransferDate && currentDate > employeeTransferDate) {
          // لا نضيف بيانات حضور بعد تاريخ النقل
          currentDate.setDate(currentDate.getDate() + 1);
          continue;
        } else {
          // اختيار نوع حضور عشوائي (80% حضور، 20% أنواع أخرى)
          attendanceType = Math.random() < 0.8 ? 'حضور' :
            attendanceTypes[Math.floor(Math.random() * attendanceTypes.length)];
          notes = `بيانات تجريبية - ${attendanceType}`;
        }

        try {
          await pool.request()
            .input('attendanceDate', currentDate)
            .input('employeeCode', employee.EmployeeCode)
            .input('employeeName', employee.EmployeeName)
            .input('jobTitle', employee.JobTitle || '')
            .input('department', employee.Department || '')
            .input('attendance', attendanceType)
            .input('checkInTime', attendanceType === 'حضور' ? '08:00' : '')
            .input('checkOutTime', attendanceType === 'حضور' ? '17:00' : '')
            .input('notes', notes)
            .query(`
              INSERT INTO DailyAttendance
              (AttendanceDate, EmployeeCode, EmployeeName, JobTitle, Department, Attendance, attendanceStatus, CheckInTime, CheckOutTime, Notes)
              VALUES (@attendanceDate, @employeeCode, @employeeName, @jobTitle, @department, @attendance, @attendance, @checkInTime, @checkOutTime, @notes)
            `);
        } catch (insertError) {
          // تجاهل أخطاء التكرار
          if (!insertError.message.includes('UNIQUE')) {

          }
        }

        // الانتقال لليوم التالي
        currentDate.setDate(currentDate.getDate() + 1);
      }
    }

  } catch (error) {

  }
}

// دالة إنشاء موظفين تجريبيين
async function createSampleEmployees(pool) {
  try {
    const sampleEmployees = [
      { code: '1001', name: 'أحمد محمد علي', job: 'مهندس مدني', dept: 'الهندسة', status: 'نشط' },
      { code: '1002', name: 'فاطمة أحمد حسن', job: 'محاسبة', dept: 'المالية', status: 'نشط' },
      { code: '1003', name: 'محمد عبد الله سالم', job: 'فني كهرباء', dept: 'الصيانة', status: 'نشط' },
      { code: '1004', name: 'نورا سعد محمد', job: 'سكرتيرة', dept: 'الإدارة', status: 'نشط' },
      { code: '1005', name: 'خالد عمر يوسف', job: 'سائق', dept: 'النقل', status: 'نشط' },
      { code: '1006', name: 'مريم حسام الدين', job: 'ممرضة', dept: 'الطبي', status: 'نشط' },
      { code: '1007', name: 'عبد الرحمن طارق', job: 'حارس أمن', dept: 'الأمن', status: 'نشط' },
      { code: '1008', name: 'سارة محمود أحمد', job: 'مصممة جرافيك', dept: 'التسويق', status: 'مستقيل', resignationDate: new Date(2025, 0, 15) }, // استقالة في 15 يناير
      { code: '1009', name: 'يوسف إبراهيم علي', job: 'مبرمج', dept: 'تقنية المعلومات', status: 'منقول', transferDate: new Date(2025, 0, 20), transferDestination: 'مشروع الرياض' }, // نقل في 20 يناير
      { code: '1010', name: 'هدى عبد العزيز', job: 'موظفة استقبال', dept: 'خدمة العملاء', status: 'نشط' }
    ];

    for (const emp of sampleEmployees) {
      try {
        const request = pool.request()
          .input('employeeCode', emp.code)
          .input('employeeName', emp.name)
          .input('jobTitle', emp.job)
          .input('department', emp.dept)
          .input('hireDate', new Date())
          .input('status', emp.status);

        let query = `
          INSERT INTO Employees (EmployeeCode, EmployeeName, JobTitle, Department, HireDate, CurrentStatus`;
        let values = `VALUES (@employeeCode, @employeeName, @jobTitle, @department, @hireDate, @status`;

        // إضافة تاريخ الاستقالة إذا كان موجوداً
        if (emp.resignationDate) {
          request.input('resignationDate', emp.resignationDate);
          query += `, ResignationDate`;
          values += `, @resignationDate`;
        }

        // إضافة تاريخ النقل ووجهته إذا كان موجوداً
        if (emp.transferDate) {
          request.input('transferDate', emp.transferDate);
          query += `, TransferDate`;
          values += `, @transferDate`;

          if (emp.transferDestination) {
            request.input('transferDestination', emp.transferDestination);
            query += `, TransferDestination`;
            values += `, @transferDestination`;
          }
        }

        query += `) ` + values + `)`;

        await request.query(query);
      } catch (insertError) {
        // تجاهل أخطاء التكرار
        if (!insertError.message.includes('UNIQUE') && !insertError.message.includes('PRIMARY KEY')) {

        }
      }
    }

  } catch (error) {

  }
}

// دالة مساعدة للحصول على لون الرمز
function getColorForCode(code) {
  const colors = {
    // الرموز المعتمدة
    'W': 'FFFFFF',      // حضور - أبيض
    'Ab': 'FF4444',     // غياب - أحمر
    'S': '22C55E',      // إجازة مرضية - أخضر فاتح
    'R': 'FBBF24',      // راحة - أصفر
    'NH': '8B5CF6',     // إجازة رسمية - بنفسجي
    'CR': 'F59E0B',     // راحة بدل - برتقالي
    'M': 'EF4444',      // مأمورية - أحمر فاتح
    'AL': '3B82F6',     // إجازة اعتيادية - أزرق
    'CL': 'EC4899',     // إجازة عارضة - وردي
    'UL': '6B7280',     // إجازة بدون أجر - رمادي
    'ML': 'F472B6',     // إجازة أمومة - وردي فاتح

    // حالات خاصة
    'استقالة': 'DC2626',   // أحمر داكن
    'مستقيل': 'FCA5A5',    // أحمر فاتح
    'نقل': '059669',       // أخضر داكن
    'منقول': '86EFAC',     // أخضر فاتح
    'لم ينضم': 'D1D5DB',   // رمادي فاتح
    '-': 'F3F4F6'          // رمادي فاتح جداً للخانات الفارغة
  };

  return colors[code] || 'FFFFFF'; // افتراضي أبيض
}
