// تعطيل تحذيرات debug قبل استيراد mssql
if (typeof process !== 'undefined') {
  process.env.DEBUG = '';
  process.env.NODE_NO_WARNINGS = '1';
}

import sql from 'mssql';

const config = {
  server: process.env.DB_SERVER || 'localhost\\DBOJESTA',
  database: process.env.DB_NAME || 'emp',
  user: process.env.DB_USER || 'SA',
  password: process.env.DB_PASSWORD || 'admin@123',
  options: {
    trustServerCertificate: true,
    encrypt: false,
    enableArithAbort: true,
    instanceName: 'DBOJESTA'
  },
  connectionTimeout: 30000,
  requestTimeout: 30000,
  pool: {
    max: 5,
    min: 0,
    idleTimeoutMillis: 30000,
    acquireTimeoutMillis: 60000,
    createTimeoutMillis: 30000,
    destroyTimeoutMillis: 5000,
    reapIntervalMillis: 1000,
    createRetryIntervalMillis: 200
  }
};

let globalPool = null;
let isConnecting = false;

// دالة للحصول على اتصال بقاعدة البيانات
export async function getConnection() {
  try {
    // التحقق من حالة الاتصال الحالي
    if (globalPool && globalPool.connected) {
      return globalPool;
    }

    // منع إنشاء اتصالات متعددة في نفس الوقت
    if (isConnecting) {
      // انتظار حتى ينتهي الاتصال الحالي
      let attempts = 0;
      while (isConnecting && attempts < 50) {
        await new Promise(resolve => setTimeout(resolve, 100));
        attempts++;
      }

      if (globalPool && globalPool.connected) {
        return globalPool;
      }
    }

    // إنشاء اتصال جديد
    if (!globalPool || !globalPool.connected) {
      isConnecting = true;

      try {
        console.log('🔌 إنشاء اتصال جديد بقاعدة البيانات...');

        globalPool = await sql.connect(config);

        // تحديد الحد الأقصى للمستمعين لتجنب التحذيرات
        globalPool.setMaxListeners(20);

        // إضافة معالجات الأحداث
        globalPool.on('error', (err) => {
          console.error('❌ خطأ في pool الاتصال:', err.message);
          globalPool = null;
        });

        console.log('✅ تم إنشاء الاتصال بنجاح');
      } finally {
        isConnecting = false;
      }
    }

    return globalPool;
  } catch (error) {

    globalPool = null; // Reset pool on error
    throw error;
  }
}

// دالة لإغلاق الاتصال
export async function closeConnection() {
  try {
    if (globalPool) {
      await globalPool.close();
      globalPool = null;

    }
  } catch (error) {

    throw error;
  }
}

// دالة لتنفيذ الاستعلامات
export async function executeQuery(query, params = {}) {
  const pool = await getConnection();
  const request = pool.request();

  // إضافة المعلمات إلى الطلب
  Object.entries(params).forEach(([key, value]) => {
    request.input(key, value);
  });

  try {
    const result = await request.query(query);
    return result.recordset;
  } catch (error) {

    throw error;
  }
}

export { config, sql };

