import { NextResponse } from 'next/server';
import { getConnection } from '@/utils/db';

export async function GET() {
  let pool = null;

  try {
    pool = await getConnection();
    // جلب الأقسام المختلفة مع عدد الموظفين في كل قسم
    const departmentsResult = await pool.request().query(`
      SELECT
        ISNULL(Department, 'غير محدد') as department_name,
        COUNT(*) as employee_count
      FROM Employees WITH(NOLOCK)
      WHERE Department IS NOT NULL AND Department != ''
      GROUP BY Department
      ORDER BY employee_count DESC, Department ASC
    `);

    // جلب المحافظات المختلفة مع عدد الموظفين في كل محافظة
    const governoratesResult = await pool.request().query(`
      SELECT
        ISNULL(Governorate, 'غير محدد') as governorate_name,
        COUNT(*) as employee_count
      FROM Employees WITH(NOLOCK)
      WHERE Governorate IS NOT NULL AND Governorate != ''
      GROUP BY Governorate
      ORDER BY employee_count DESC, Governorate ASC
    `);

    // جلب المسميات الوظيفية المختلفة مع عدد الموظفين
    const jobTitlesResult = await pool.request().query(`
      SELECT
        ISNULL(JobTitle, 'غير محدد') as job_title,
        COUNT(*) as employee_count
      FROM Employees WITH(NOLOCK)
      WHERE JobTitle IS NOT NULL AND JobTitle != ''
      GROUP BY JobTitle
      ORDER BY employee_count DESC, JobTitle ASC
    `);

    // تجميع البيانات
    const departments = departmentsResult.recordset.map(dept => ({
      name: dept.department_name,
      count: dept.employee_count
    }));

    const governorates = governoratesResult.recordset.map(gov => ({
      name: gov.governorate_name,
      count: gov.employee_count
    }));

    const jobTitles = jobTitlesResult.recordset.map(job => ({
      name: job.job_title,
      count: job.employee_count
    }));

    const data = {
      departments: departments,
      governorates: governorates,
      jobTitles: jobTitles,
      lastUpdated: new Date().toISOString()
    };
    return NextResponse.json({
      success: true,
      data: data
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء جلب قائمة الأقسام والمحافظات',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });

  } finally {
  }
}

