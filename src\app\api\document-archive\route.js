import { NextResponse } from 'next/server';
import { getConnection } from '@/utils/db';
import fs from 'fs';
import path from 'path';

const ARCHIVE_BASE_PATH = 'E:\\webapp\\createxyz-project\\archiv';

// تعريف المجلدات الجديدة
const ARCHIVE_FOLDERS = {
  'resignation': {
    name: 'الاستقالات',
    path: path.join(ARCHIVE_BASE_PATH, 'Resignation'),
    icon: '📋',
    description: 'طلبات الاستقالة',
    color: '#dc2626'
  },
  'transfer': {
    name: 'النقل',
    path: path.join(ARCHIVE_BASE_PATH, 'Transfer'),
    icon: '🔄',
    description: 'طلبات النقل',
    color: '#2563eb'
  },
  'last_period': {
    name: 'بيان آخر فترة',
    path: path.join(ARCHIVE_BASE_PATH, 'last_period'),
    icon: '📊',
    description: 'بيان بأيام حضور آخر فترة',
    color: '#7c3aed'
  },
  'clearance': {
    name: 'إخلاء الطرف',
    path: path.join(ARCHIVE_BASE_PATH, 'Clearance'),
    icon: '✅',
    description: 'مستندات إخلاء الطرف',
    color: '#059669'
  },
  'pic': {
    name: 'الصور الشخصية',
    path: path.join(ARCHIVE_BASE_PATH, 'pic'),
    icon: '👤',
    description: 'الصور الشخصية للموظفين',
    color: '#ea580c'
  }
};

export async function GET() {
  try {
    const pool = await getConnection();

    // إنشاء جدول الأرشيف إذا لم يكن موجوداً
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DocumentArchive' AND xtype='U')
      CREATE TABLE DocumentArchive (
        ID int IDENTITY(1,1) PRIMARY KEY,
        FolderKey nvarchar(50) NOT NULL,
        FolderName nvarchar(100) NOT NULL,
        FolderPath nvarchar(500) NOT NULL,
        Icon nvarchar(10),
        Description nvarchar(200),
        Color nvarchar(20),
        IsActive bit DEFAULT 1,
        CreatedAt datetime DEFAULT GETDATE()
      )
    `);

    // إدراج المجلدات الجديدة
    for (const [key, folder] of Object.entries(ARCHIVE_FOLDERS)) {
      await pool.request()
        .input('FolderKey', key)
        .input('FolderName', folder.name)
        .input('FolderPath', folder.path)
        .input('Icon', folder.icon)
        .input('Description', folder.description)
        .input('Color', folder.color)
        .query(`
          IF NOT EXISTS (SELECT 1 FROM DocumentArchive WHERE FolderKey = @FolderKey)
          INSERT INTO DocumentArchive (FolderKey, FolderName, FolderPath, Icon, Description, Color)
          VALUES (@FolderKey, @FolderName, @FolderPath, @Icon, @Description, @Color)
        `);
    }

    // جلب جميع المجلدات
    const result = await pool.request().query(`
      SELECT * FROM DocumentArchive WHERE IsActive = 1 ORDER BY FolderName
    `);

    return NextResponse.json({
      success: true,
      folders: result.recordset
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'حدث خطأ في جلب مجلدات الأرشيف',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}

export async function POST(request) {
  try {
    const { action, employeeId, folderKey, documentType } = await request.json();
    const pool = await getConnection();

    switch (action) {
      case 'getEmployeeDocuments':
        if (!employeeId) {
          return NextResponse.json({
            success: false,
            error: 'معرف الموظف مطلوب'
          }, { status: 400 });
        }

        // جلب مجلدات الأرشيف
        const foldersResult = await pool.request().query(`
          SELECT * FROM DocumentArchive WHERE IsActive = 1
        `);

        const documents = [];

        // فحص وجود المستندات لكل مجلد
        for (const folder of foldersResult.recordset) {
          const filePath = path.join(folder.FolderPath, `${employeeId}.pdf`);
          const webPath = `/archiv/${folder.FolderKey}/${employeeId}.pdf`;
          
          // فحص وجود الملف
          const fileExists = fs.existsSync(filePath);

          documents.push({
            folderKey: folder.FolderKey,
            folderName: folder.FolderName,
            description: folder.Description,
            icon: folder.Icon,
            color: folder.Color,
            filePath: filePath,
            webPath: webPath,
            exists: fileExists,
            employeeId: employeeId
          });
        }

        return NextResponse.json({
          success: true,
          documents: documents
        });

      case 'getTransferDocuments':
        if (!employeeId) {
          return NextResponse.json({
            success: false,
            error: 'معرف الموظف مطلوب'
          }, { status: 400 });
        }

        const transferDocs = [];
        const transferPath = path.join(ARCHIVE_FOLDERS.transfer.path, `${employeeId}.pdf`);
        
        if (fs.existsSync(transferPath)) {
          transferDocs.push({
            type: 'transfer_request',
            name: 'طلب النقل',
            path: `/archiv/transfer/${employeeId}.pdf`,
            icon: '📋',
            color: '#2563eb'
          });
        }

        return NextResponse.json({
          success: true,
          documents: transferDocs
        });

      case 'getResignationDocuments':
        if (!employeeId) {
          return NextResponse.json({
            success: false,
            error: 'معرف الموظف مطلوب'
          }, { status: 400 });
        }

        const resignationDocs = [];
        
        // طلب الاستقالة
        const resignationPath = path.join(ARCHIVE_FOLDERS.resignation.path, `${employeeId}.pdf`);
        if (fs.existsSync(resignationPath)) {
          resignationDocs.push({
            type: 'resignation_request',
            name: 'طلب الاستقالة',
            path: `/archiv/resignation/${employeeId}.pdf`,
            icon: '📋',
            color: '#dc2626'
          });
        }

        // إخلاء الطرف
        const clearancePath = path.join(ARCHIVE_FOLDERS.clearance.path, `${employeeId}.pdf`);
        if (fs.existsSync(clearancePath)) {
          resignationDocs.push({
            type: 'clearance_form',
            name: 'إخلاء الطرف',
            path: `/archiv/clearance/${employeeId}.pdf`,
            icon: '✅',
            color: '#059669'
          });
        }

        // بيان آخر فترة
        const lastPeriodPath = path.join(ARCHIVE_FOLDERS.last_period.path, `${employeeId}.pdf`);
        if (fs.existsSync(lastPeriodPath)) {
          resignationDocs.push({
            type: 'attendance_report',
            name: 'بيان بأيام حضور آخر فترة',
            path: `/archiv/last_period/${employeeId}.pdf`,
            icon: '📊',
            color: '#7c3aed'
          });
        }

        return NextResponse.json({
          success: true,
          documents: resignationDocs
        });

      case 'uploadDocument':

        return NextResponse.json({
          success: false,
          error: 'وظيفة رفع المستندات قيد التطوير'
        });

      case 'deleteDocument':

        return NextResponse.json({
          success: false,
          error: 'وظيفة حذف المستندات قيد التطوير'
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'حدث خطأ في معالجة الطلب',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}
