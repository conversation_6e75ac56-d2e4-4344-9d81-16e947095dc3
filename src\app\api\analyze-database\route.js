import { NextResponse } from 'next/server';
import { getConnection } from '@/utils/db';

export async function GET(request) {
  try {

    const pool = await getConnection();
    
    // 1. فحص أعمدة جدول الموظفين بالتفصيل
    const employeesColumnsResult = await pool.request().query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'Employees'
      ORDER BY ORDINAL_POSITION
    `);

    employeesColumnsResult.recordset.forEach(col => {
      console.log(`  - ${col.COLUMN_NAME} (${col.DATA_TYPE})`);
    });
    
    // 2. اختبار أسماء الأعمدة المختلفة
    const possibleEmployeeCodeColumns = ['EmployeeCode', 'EmployeeID', 'ID', 'Code', 'EmpCode', 'EmpID'];
    const possibleEmployeeNameColumns = ['EmployeeName', 'FullName', 'Name', 'EmpName', 'EmployeeFullName'];
    
    let correctEmployeeCodeColumn = null;
    let correctEmployeeNameColumn = null;
    
    // اختبار أعمدة كود الموظف
    for (const colName of possibleEmployeeCodeColumns) {
      try {
        await pool.request().query(`SELECT TOP 1 ${colName} FROM Employees`);
        correctEmployeeCodeColumn = colName;

        break;
      } catch (error) {

      }
    }
    
    // اختبار أعمدة اسم الموظف
    for (const colName of possibleEmployeeNameColumns) {
      try {
        await pool.request().query(`SELECT TOP 1 ${colName} FROM Employees`);
        correctEmployeeNameColumn = colName;

        break;
      } catch (error) {

      }
    }
    
    // 3. جلب عينة من البيانات باستخدام الأعمدة الصحيحة
    let employeesSample = [];
    if (correctEmployeeCodeColumn && correctEmployeeNameColumn) {
      try {
        const sampleResult = await pool.request().query(`
          SELECT TOP 5 
            ${correctEmployeeCodeColumn} as EmployeeCode,
            ${correctEmployeeNameColumn} as EmployeeName,
            JobTitle,
            Department,
            CurrentStatus,
            CompanyHousing,
            SocialInsurance,
            MedicalInsurance
          FROM Employees
          ORDER BY ${correctEmployeeCodeColumn}
        `);
        employeesSample = sampleResult.recordset;

      } catch (error) {

      }
    }
    
    // 4. إحصائيات دقيقة للموظفين
    let employeeStats = {};
    if (correctEmployeeCodeColumn) {
      try {
        const statsResult = await pool.request().query(`
          SELECT
            COUNT(*) as total,
            COUNT(CASE WHEN CurrentStatus = N'ساري' OR CurrentStatus = N'نشط' OR CurrentStatus IS NULL THEN 1 END) as active,
            COUNT(CASE WHEN CurrentStatus = N'منقول' THEN 1 END) as transferred,
            COUNT(CASE WHEN CurrentStatus = N'مستقيل' THEN 1 END) as resigned,
            COUNT(CASE WHEN CompanyHousing = N'نعم' THEN 1 END) as resident,
            COUNT(CASE WHEN CompanyHousing = N'لا' OR CompanyHousing IS NULL THEN 1 END) as nonResident,
            COUNT(CASE WHEN SocialInsurance = N'مؤمن' AND MedicalInsurance = N'مؤمن' THEN 1 END) as bothInsured,
            COUNT(CASE WHEN SocialInsurance = N'مؤمن' AND (MedicalInsurance != N'مؤمن' OR MedicalInsurance IS NULL) THEN 1 END) as socialOnly,
            COUNT(CASE WHEN MedicalInsurance = N'مؤمن' AND (SocialInsurance != N'مؤمن' OR SocialInsurance IS NULL) THEN 1 END) as medicalOnly,
            COUNT(CASE WHEN (SocialInsurance != N'مؤمن' OR SocialInsurance IS NULL) AND (MedicalInsurance != N'مؤمن' OR MedicalInsurance IS NULL) THEN 1 END) as notInsured
          FROM Employees
        `);
        employeeStats = statsResult.recordset[0];

      } catch (error) {

      }
    }
    
    // 5. فحص جدول الشقق
    let apartmentStats = {};
    try {
      const apartmentStatsResult = await pool.request().query(`
        SELECT
          COUNT(*) as total,
          COUNT(CASE WHEN IsActive = 1 THEN 1 END) as active,
          ISNULL(SUM(CASE WHEN IsActive = 1 THEN RentAmount ELSE 0 END), 0) as totalRent
        FROM Apartments
      `);
      apartmentStats = apartmentStatsResult.recordset[0];

    } catch (error) {

    }
    
    // 6. فحص ربط الموظفين بالشقق
    let apartmentBeneficiariesStats = {};
    if (correctEmployeeCodeColumn) {
      try {
        const beneficiariesStatsResult = await pool.request().query(`
          SELECT
            COUNT(*) as total,
            COUNT(CASE WHEN IsActive = 1 THEN 1 END) as active,
            COUNT(DISTINCT EmployeeCode) as uniqueEmployees,
            COUNT(DISTINCT ApartmentID) as apartmentsWithBeneficiaries
          FROM ApartmentBeneficiaries
        `);
        apartmentBeneficiariesStats = beneficiariesStatsResult.recordset[0];

      } catch (error) {

      }
    }
    
    // 7. اختبار البحث عن موظف محدد
    let searchTest = null;
    if (correctEmployeeCodeColumn && correctEmployeeNameColumn) {
      try {
        const searchResult = await pool.request().query(`
          SELECT TOP 1
            ${correctEmployeeCodeColumn} as EmployeeCode,
            ${correctEmployeeNameColumn} as EmployeeName,
            JobTitle,
            Department
          FROM Employees
          WHERE ${correctEmployeeCodeColumn} = 1450
        `);
        searchTest = searchResult.recordset[0] || null;

      } catch (error) {

      }
    }
    
    return NextResponse.json({
      success: true,
      data: {
        correctColumns: {
          employeeCode: correctEmployeeCodeColumn,
          employeeName: correctEmployeeNameColumn
        },
        employeesColumns: employeesColumnsResult.recordset,
        employeesSample: employeesSample,
        employeeStats: employeeStats,
        apartmentStats: apartmentStats,
        apartmentBeneficiariesStats: apartmentBeneficiariesStats,
        searchTest: searchTest
      },
      message: 'تم تحليل قاعدة البيانات بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message,
      details: {
        name: error.name,
        code: error.code,
        state: error.state
      }
    }, { status: 500 });
  }
}
