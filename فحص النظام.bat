@echo off
chcp 65001 >nul
title فحص نظام إدارة التكاليف
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    فحص نظام إدارة التكاليف                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: الانتقال إلى مجلد المشروع
cd /d "E:\web\project"

echo 🔍 جاري فحص النظام...
echo.

:: فحص Node.js
echo [1/8] فحص Node.js...
node --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f %%i in ('node --version') do echo ✅ Node.js متاح - الإصدار: %%i
) else (
    echo ❌ Node.js غير مثبت
)

:: فحص npm
echo [2/8] فحص npm...
npm --version >nul 2>&1
if %errorlevel% equ 0 (
    for /f %%i in ('npm --version') do echo ✅ npm متاح - الإصدار: %%i
) else (
    echo ❌ npm غير متاح
)

:: فحص مجلد المشروع
echo [3/8] فحص مجلد المشروع...
if exist "E:\web\project" (
    echo ✅ مجلد المشروع موجود: E:\web\project
) else (
    echo ❌ مجلد المشروع غير موجود
)

:: فحص package.json
echo [4/8] فحص ملف package.json...
if exist "package.json" (
    echo ✅ ملف package.json موجود
) else (
    echo ❌ ملف package.json غير موجود
)

:: فحص node_modules
echo [5/8] فحص التبعيات...
if exist "node_modules" (
    echo ✅ مجلد node_modules موجود
) else (
    echo ⚠️  مجلد node_modules غير موجود - يحتاج تثبيت التبعيات
)

:: فحص ملفات المشروع الأساسية
echo [6/8] فحص ملفات المشروع...
if exist "src" (
    echo ✅ مجلد src موجود
) else (
    echo ❌ مجلد src غير موجود
)

if exist "next.config.js" (
    echo ✅ ملف next.config.js موجود
) else (
    echo ⚠️  ملف next.config.js غير موجود
)

:: فحص مجلدات البيانات
echo [7/8] فحص مجلدات البيانات...
if exist "archiv" (
    echo ✅ مجلد archiv موجود
    if exist "archiv\carscost" (
        echo   ✅ مجلد السيارات موجود
    ) else (
        echo   ❌ مجلد السيارات غير موجود
    )
    if exist "archiv\housingcost" (
        echo   ✅ مجلد الشقق موجود
    ) else (
        echo   ❌ مجلد الشقق غير موجود
    )
    if exist "archiv\apartments_annex" (
        echo   ✅ مجلد ملحقات الشقق موجود
    ) else (
        echo   ❌ مجلد ملحقات الشقق غير موجود
    )
    if exist "archiv\3amala" (
        echo   ✅ مجلد العمالة موجود
    ) else (
        echo   ❌ مجلد العمالة غير موجود
    )
) else (
    echo ❌ مجلد archiv غير موجود
)

:: فحص المنفذ
echo [8/8] فحص المنفذ 3001...
netstat -ano | findstr :3001 >nul
if %errorlevel% equ 0 (
    echo ⚠️  المنفذ 3001 مستخدم حالياً
) else (
    echo ✅ المنفذ 3001 متاح
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                        انتهى الفحص                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

:: اقتراحات بناءً على النتائج
echo 💡 اقتراحات:
if not exist "node_modules" (
    echo    - قم بتشغيل "تحديث النظام.bat" لتثبيت التبعيات
)
echo    - استخدم "تشغيل النظام.cmd" للتشغيل السريع
echo    - استخدم "start-system.bat" للتشغيل مع الفحص الشامل
echo.

pause
