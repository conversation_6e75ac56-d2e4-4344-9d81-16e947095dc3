import { NextResponse } from 'next/server';
import { sql, connectDB } from '@/config/database';
import { writeFile } from 'fs/promises';
import path from 'path';

export async function POST(request) {
  try {
    const formData = await request.formData();
    const count = formData.get('count');
    const cost = formData.get('cost');
    const month = formData.get('month');
    const year = formData.get('year');
    const isAnnex = formData.get('isAnnex') === 'true' ? 1 : 0;
    const file = formData.get('file');

    await connectDB();

    // حفظ الملف إذا تم رفعه
    let filePath = null;
    if (file) {
      const bytes = await file.arrayBuffer();
      const buffer = Buffer.from(bytes);
      
      // إنشاء اسم فريد للملف
      const fileName = `annex_${year}_${month}_${Date.now()}.pdf`;
      const uploadDir = path.join(process.cwd(), 'public', 'annexes');
      
      // إنشاء المجلد إذا لم يكن موجوداً
      await writeFile(path.join(uploadDir, fileName), buffer);
      filePath = `/annexes/${fileName}`;
    }

    // إضافة السجل في قاعدة البيانات
    const result = await sql.query`
      INSERT INTO APARTMENTCOST (
        [الشهر], 
        [السنة], 
        [العدد], 
        [القيمة الإيجارية], 
        [ملحق],
        [ملف_الملحق]
      )
      VALUES (
        ${month}, 
        ${year}, 
        ${count}, 
        ${cost}, 
        ${isAnnex},
        ${filePath}
      )
    `;

    return NextResponse.json({ 
      success: true,
      message: 'تم إضافة الملحق بنجاح',
      filePath: filePath
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: 'حدث خطأ في إضافة الملحق',
      error: error.message
    }, { status: 500 });
  }
} 