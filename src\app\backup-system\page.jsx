'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import { useTheme } from '@/contexts/ThemeContext';
import {
  FiDatabase,
  FiDownload,
  FiUpload,
  FiTrash2,
  FiRefreshCw,
  FiSave,
  FiHardDrive,
  FiClock,
  FiFileText,
  FiAlertTriangle,
  FiCheckCircle,
  FiInfo
} from 'react-icons/fi';

export default function BackupSystemPage() {
  const { isDarkMode } = useTheme();
  const [loading, setLoading] = useState(false);
  const [backups, setBackups] = useState([]);
  const [backupInfo, setBackupInfo] = useState(null);
  const [newBackupName, setNewBackupName] = useState('');
  const [selectedBackup, setSelectedBackup] = useState('');

  useEffect(() => {
    loadBackupData();
  }, []);

  // تحميل بيانات النسخ الاحتياطي
  const loadBackupData = async () => {
    setLoading(true);
    try {
      // جلب قائمة النسخ الاحتياطية
      const backupsResponse = await fetch('/api/backup-system', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'listBackups' })
      });
      const backupsResult = await backupsResponse.json();
      
      if (backupsResult.success) {
        setBackups(backupsResult.backups);
      }

      // جلب معلومات النسخ الاحتياطي
      const infoResponse = await fetch('/api/backup-system', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'getBackupInfo' })
      });
      const infoResult = await infoResponse.json();
      
      if (infoResult.success) {
        setBackupInfo(infoResult);
      }

    } catch (error) {

      alert('خطأ في تحميل البيانات');
    }
    setLoading(false);
  };

  // إنشاء نسخة احتياطية جديدة
  const createBackup = async (backupType = 'createBackup') => {
    if (!newBackupName.trim()) {
      alert('يرجى إدخال اسم للنسخة الاحتياطية');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/backup-system', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: backupType,
          backupName: newBackupName
        })
      });

      const result = await response.json();

      if (result.success) {
        const message = result.backupType === 'data_only'
          ? `تم إنشاء النسخة الاحتياطية البديلة بنجاح!\n\nنوع النسخة: تصدير البيانات\nعدد الجداول: ${result.tablesBackedUp}\nإجمالي السجلات: ${result.totalRecords}`
          : 'تم إنشاء النسخة الاحتياطية بنجاح!';

        alert(message);
        setNewBackupName('');
        loadBackupData();
      } else {
        alert('خطأ: ' + result.error);
      }

    } catch (error) {

      alert('خطأ في إنشاء النسخة الاحتياطية');
    }
    setLoading(false);
  };

  // استرداد نسخة احتياطية
  const restoreBackup = async () => {
    if (!selectedBackup) {
      alert('يرجى اختيار نسخة احتياطية للاسترداد');
      return;
    }

    const confirmRestore = confirm(
      '⚠️ تحذير: سيتم استبدال جميع البيانات الحالية بالنسخة الاحتياطية المختارة.\n\n' +
      'هذه العملية لا يمكن التراجع عنها!\n\n' +
      'هل أنت متأكد من المتابعة؟'
    );

    if (!confirmRestore) return;

    setLoading(true);
    try {
      const response = await fetch('/api/backup-system', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'restoreBackup',
          restoreFile: selectedBackup
        })
      });

      const result = await response.json();
      
      if (result.success) {
        alert('تم استرداد النسخة الاحتياطية بنجاح!');
        loadBackupData();
      } else {
        alert('خطأ: ' + result.error);
      }

    } catch (error) {

      alert('خطأ في استرداد النسخة الاحتياطية');
    }
    setLoading(false);
  };

  // حذف نسخة احتياطية
  const deleteBackup = async (backupName) => {
    const confirmDelete = confirm(`هل أنت متأكد من حذف النسخة الاحتياطية: ${backupName}؟`);
    if (!confirmDelete) return;

    setLoading(true);
    try {
      const response = await fetch('/api/backup-system', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'deleteBackup',
          backupName: backupName
        })
      });

      const result = await response.json();
      
      if (result.success) {
        alert('تم حذف النسخة الاحتياطية بنجاح');
        loadBackupData();
      } else {
        alert('خطأ: ' + result.error);
      }

    } catch (error) {

      alert('خطأ في حذف النسخة الاحتياطية');
    }
    setLoading(false);
  };

  // تصدير البيانات
  const exportData = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/backup-system', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'exportData' })
      });

      const result = await response.json();
      
      if (result.success) {
        alert('تم تصدير البيانات بنجاح!');
      } else {
        alert('خطأ: ' + result.error);
      }

    } catch (error) {

      alert('خطأ في تصدير البيانات');
    }
    setLoading(false);
  };

  // تنسيق التاريخ
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('ar-EG');
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto p-6">
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FiDatabase className="text-3xl text-blue-600" />
              <div>
                <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  نظام النسخ الاحتياطي والاسترداد
                </h1>
                <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                  إدارة النسخ الاحتياطية واسترداد البيانات
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              {/* أزرار سريعة */}
              <button
                onClick={async () => {
                  const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
                  setNewBackupName(`Quick_Backup_${timestamp}`);
                  await createBackup();
                }}
                disabled={loading}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
                title="إنشاء نسخة احتياطية سريعة"
              >
                <FiSave className="w-4 h-4" />
                نسخة سريعة
              </button>

              <button
                onClick={exportData}
                disabled={loading}
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
                title="تصدير البيانات"
              >
                <FiDownload className="w-4 h-4" />
                تصدير
              </button>

              <button
                onClick={loadBackupData}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
              >
                <FiRefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                تحديث
              </button>
            </div>
          </div>
        </div>

        {/* معلومات قاعدة البيانات */}
        {backupInfo && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
              <div className="flex items-center">
                <FiDatabase className="w-8 h-8 text-blue-500 mr-3" />
                <div>
                  <p className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>قاعدة البيانات</p>
                  <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {backupInfo.databaseInfo?.DatabaseName}
                  </p>
                </div>
              </div>
            </div>

            <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
              <div className="flex items-center">
                <FiFileText className="w-8 h-8 text-green-500 mr-3" />
                <div>
                  <p className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>إجمالي الموظفين</p>
                  <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {backupInfo.databaseInfo?.TotalEmployees || 0}
                  </p>
                </div>
              </div>
            </div>

            <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
              <div className="flex items-center">
                <FiHardDrive className="w-8 h-8 text-purple-500 mr-3" />
                <div>
                  <p className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>النسخ الاحتياطية</p>
                  <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {backups.length}
                  </p>
                </div>
              </div>
            </div>

            <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
              <div className="flex items-center">
                <FiClock className="w-8 h-8 text-orange-500 mr-3" />
                <div>
                  <p className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>آخر نسخة احتياطية</p>
                  <p className={`text-sm font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {backupInfo.lastBackup ? formatDate(backupInfo.lastBackup.CreatedAt) : 'لا توجد'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* إنشاء نسخة احتياطية */}
          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <h2 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-4 flex items-center gap-2`}>
              <FiSave className="text-green-600" />
              إنشاء نسخة احتياطية جديدة
            </h2>
            
            <div className="space-y-4">
              <div>
                <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                  اسم النسخة الاحتياطية
                </label>
                <input
                  type="text"
                  value={newBackupName}
                  onChange={(e) => setNewBackupName(e.target.value)}
                  placeholder="مثال: نسخة_احتياطية_يومية"
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    isDarkMode 
                      ? 'bg-gray-700 border-gray-600 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>
              
              <div className="grid grid-cols-1 gap-2">
                <button
                  onClick={() => createBackup('createBackup')}
                  disabled={loading || !newBackupName.trim()}
                  className="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors disabled:opacity-50"
                >
                  <FiSave className="w-4 h-4" />
                  نسخة احتياطية كاملة
                </button>

                <button
                  onClick={() => createBackup('createDataBackup')}
                  disabled={loading || !newBackupName.trim()}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors disabled:opacity-50"
                >
                  <FiFileText className="w-4 h-4" />
                  نسخة احتياطية للبيانات فقط
                </button>
              </div>
            </div>
          </div>

          {/* استرداد نسخة احتياطية */}
          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <h2 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-4 flex items-center gap-2`}>
              <FiUpload className="text-blue-600" />
              استرداد نسخة احتياطية
            </h2>
            
            <div className="space-y-4">
              <div>
                <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                  اختر نسخة احتياطية
                </label>
                <select
                  value={selectedBackup}
                  onChange={(e) => setSelectedBackup(e.target.value)}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                    isDarkMode 
                      ? 'bg-gray-700 border-gray-600 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                >
                  <option value="">اختر نسخة احتياطية...</option>
                  {backups.map((backup) => (
                    <option key={backup.name} value={backup.name}>
                      {backup.name} - {backup.sizeFormatted} - {formatDate(backup.created)}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className={`p-3 rounded-lg ${isDarkMode ? 'bg-red-900/20 border-red-800' : 'bg-red-50 border-red-200'} border`}>
                <div className="flex items-center gap-2 text-red-600">
                  <FiAlertTriangle className="w-4 h-4" />
                  <span className="text-sm font-medium">تحذير</span>
                </div>
                <p className="text-sm text-red-600 mt-1">
                  سيتم استبدال جميع البيانات الحالية. هذه العملية لا يمكن التراجع عنها!
                </p>
              </div>
              
              <button
                onClick={restoreBackup}
                disabled={loading || !selectedBackup}
                className="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors disabled:opacity-50"
              >
                <FiUpload className="w-4 h-4" />
                استرداد النسخة الاحتياطية
              </button>
            </div>
          </div>
        </div>

        {/* قائمة النسخ الاحتياطية */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border mt-6`}>
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'} flex items-center gap-2`}>
                <FiHardDrive className="text-purple-600" />
                النسخ الاحتياطية المتاحة
              </h2>
              <button
                onClick={exportData}
                disabled={loading}
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
              >
                <FiDownload className="w-4 h-4" />
                تصدير البيانات
              </button>
            </div>
          </div>

          <div className="p-6">
            {loading ? (
              <div className="flex justify-center items-center py-12">
                <FiRefreshCw className="w-6 h-6 animate-spin text-blue-500" />
                <span className="mr-2">جاري التحميل...</span>
              </div>
            ) : backups.length === 0 ? (
              <div className="text-center py-12">
                <FiHardDrive className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className={`text-lg ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                  لا توجد نسخ احتياطية
                </p>
                <p className={`text-sm ${isDarkMode ? 'text-slate-500' : 'text-gray-500'}`}>
                  قم بإنشاء نسخة احتياطية جديدة للبدء
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className={isDarkMode ? 'bg-gray-800' : 'bg-gray-50'}>
                    <tr>
                      <th className={`px-6 py-3 text-right text-xs font-medium uppercase tracking-wider ${
                        isDarkMode ? 'text-gray-300' : 'text-gray-500'
                      }`}>
                        اسم النسخة
                      </th>
                      <th className={`px-6 py-3 text-right text-xs font-medium uppercase tracking-wider ${
                        isDarkMode ? 'text-gray-300' : 'text-gray-500'
                      }`}>
                        الحجم
                      </th>
                      <th className={`px-6 py-3 text-right text-xs font-medium uppercase tracking-wider ${
                        isDarkMode ? 'text-gray-300' : 'text-gray-500'
                      }`}>
                        تاريخ الإنشاء
                      </th>
                      <th className={`px-6 py-3 text-right text-xs font-medium uppercase tracking-wider ${
                        isDarkMode ? 'text-gray-300' : 'text-gray-500'
                      }`}>
                        الإجراءات
                      </th>
                    </tr>
                  </thead>
                  <tbody className={`divide-y ${isDarkMode ? 'divide-gray-700 bg-gray-800' : 'divide-gray-200 bg-white'}`}>
                    {backups.map((backup) => (
                      <tr key={backup.name} className={isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'}>
                        <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${
                          isDarkMode ? 'text-white' : 'text-gray-900'
                        }`}>
                          <div className="flex items-center gap-2">
                            <FiDatabase className="w-4 h-4 text-blue-500" />
                            {backup.name.replace('.bak', '')}
                          </div>
                        </td>
                        <td className={`px-6 py-4 whitespace-nowrap text-sm ${
                          isDarkMode ? 'text-slate-300' : 'text-gray-600'
                        }`}>
                          {backup.sizeFormatted}
                        </td>
                        <td className={`px-6 py-4 whitespace-nowrap text-sm ${
                          isDarkMode ? 'text-slate-300' : 'text-gray-600'
                        }`}>
                          {formatDate(backup.created)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => setSelectedBackup(backup.name)}
                              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
                              title="اختيار للاسترداد"
                            >
                              <FiUpload className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => deleteBackup(backup.name)}
                              className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition-colors"
                              title="حذف"
                            >
                              <FiTrash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>

        {/* معلومات إضافية */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border mt-6 p-6`}>
          <h3 className={`text-lg font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-4 flex items-center gap-2`}>
            <FiInfo className="text-blue-600" />
            معلومات مهمة
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-blue-900/20 border-blue-800' : 'bg-blue-50 border-blue-200'} border`}>
              <div className="flex items-center gap-2 text-blue-600 mb-2">
                <FiCheckCircle className="w-4 h-4" />
                <span className="font-medium">النسخ الاحتياطي</span>
              </div>
              <ul className="text-sm text-blue-600 space-y-1">
                <li>• يتم إنشاء نسخة كاملة من قاعدة البيانات</li>
                <li>• يُنصح بإنشاء نسخة احتياطية يومية</li>
                <li>• النسخ محفوظة في مجلد backups</li>
              </ul>
            </div>

            <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-red-900/20 border-red-800' : 'bg-red-50 border-red-200'} border`}>
              <div className="flex items-center gap-2 text-red-600 mb-2">
                <FiAlertTriangle className="w-4 h-4" />
                <span className="font-medium">تحذيرات الاسترداد</span>
              </div>
              <ul className="text-sm text-red-600 space-y-1">
                <li>• سيتم حذف جميع البيانات الحالية</li>
                <li>• العملية لا يمكن التراجع عنها</li>
                <li>• تأكد من إنشاء نسخة احتياطية قبل الاسترداد</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
