import { NextResponse } from 'next/server';
import ExcelJS from 'exceljs';
import sqlite3 from 'sqlite3';
import { open } from 'sqlite';

// فتح قاعدة البيانات
async function openDb() {
  return open({
    filename: './database.db',
    driver: sqlite3.Database
  });
}

// التحقق من صحة البيانات
function validateAttendanceData(row, rowIndex) {
  const errors = [];
  
  // التحقق من كود الموظف
  if (!row.employeeCode || row.employeeCode.toString().trim() === '') {
    errors.push(`كود الموظف مطلوب`);
  }
  
  // التحقق من التاريخ
  if (!row.date) {
    errors.push(`التاريخ مطلوب`);
  } else {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(row.date.toString())) {
      errors.push(`التاريخ يجب أن يكون بصيغة YYYY-MM-DD`);
    }
  }
  
  // التحقق من نوع الحضور
  const validAttendanceTypes = ['حضور', 'غياب', 'إجازة', 'مرضي', 'مأمورية'];
  if (!row.attendanceType || !validAttendanceTypes.includes(row.attendanceType.toString().trim())) {
    errors.push(`نوع الحضور يجب أن يكون أحد القيم: ${validAttendanceTypes.join(', ')}`);
  }
  
  // التحقق من وقت الدخول والخروج (إذا كان موجود)
  const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
  if (row.checkIn && row.checkIn.toString().trim() !== '' && !timeRegex.test(row.checkIn.toString().trim())) {
    errors.push(`وقت الدخول يجب أن يكون بصيغة HH:MM`);
  }
  
  if (row.checkOut && row.checkOut.toString().trim() !== '' && !timeRegex.test(row.checkOut.toString().trim())) {
    errors.push(`وقت الخروج يجب أن يكون بصيغة HH:MM`);
  }
  
  return errors.map(error => ({
    row: rowIndex + 2, // +2 لأن الصف الأول هو العناوين والفهرس يبدأ من 0
    message: error
  }));
}

// قراءة ملف Excel
async function readExcelFile(buffer) {
  const workbook = new ExcelJS.Workbook();
  await workbook.xlsx.load(buffer);
  
  const worksheet = workbook.getWorksheet(1); // أول ورقة عمل
  if (!worksheet) {
    throw new Error('لم يتم العثور على ورقة عمل في الملف');
  }
  
  const data = [];
  const headers = [];
  
  // قراءة العناوين من الصف الأول
  const headerRow = worksheet.getRow(1);
  headerRow.eachCell((cell, colNumber) => {
    headers[colNumber] = cell.value?.toString().trim();
  });
  
  // قراءة البيانات
  worksheet.eachRow((row, rowNumber) => {
    if (rowNumber === 1) return; // تخطي صف العناوين
    
    const rowData = {};
    row.eachCell((cell, colNumber) => {
      const header = headers[colNumber];
      if (header) {
        let value = cell.value;
        
        // معالجة التواريخ
        if (value instanceof Date) {
          value = value.toISOString().split('T')[0];
        }
        
        rowData[getFieldKey(header)] = value?.toString().trim() || '';
      }
    });
    
    // إضافة الصف فقط إذا كان يحتوي على بيانات
    if (Object.values(rowData).some(val => val !== '')) {
      data.push(rowData);
    }
  });
  
  return data;
}

// تحويل أسماء الأعمدة إلى مفاتيح
function getFieldKey(header) {
  const fieldMap = {
    'كود الموظف': 'employeeCode',
    'اسم الموظف': 'employeeName',
    'القسم': 'department',
    'التاريخ': 'date',
    'نوع الحضور': 'attendanceType',
    'وقت الدخول': 'checkIn',
    'وقت الخروج': 'checkOut',
    'ملاحظات': 'notes'
  };
  
  return fieldMap[header] || header;
}

// التحقق من وجود الموظف في قاعدة البيانات
async function validateEmployeeExists(db, employeeCode) {
  const employee = await db.get(
    'SELECT EmployeeCode FROM employees WHERE EmployeeCode = ?',
    [employeeCode]
  );
  return !!employee;
}

// إدراج أو تحديث بيانات الحضور
async function insertOrUpdateAttendance(db, attendanceData) {
  const {
    employeeCode,
    employeeName,
    department,
    date,
    attendanceType,
    checkIn,
    checkOut,
    notes
  } = attendanceData;
  
  // التحقق من وجود السجل
  const existingRecord = await db.get(
    'SELECT id FROM daily_attendance WHERE employee_code = ? AND date = ?',
    [employeeCode, date]
  );
  
  if (existingRecord) {
    // تحديث السجل الموجود
    await db.run(`
      UPDATE daily_attendance 
      SET employee_name = ?, department = ?, attendance_type = ?, 
          check_in = ?, check_out = ?, notes = ?, updated_at = datetime('now')
      WHERE employee_code = ? AND date = ?
    `, [employeeName, department, attendanceType, checkIn || null, checkOut || null, notes || null, employeeCode, date]);
    
    return 'updated';
  } else {
    // إدراج سجل جديد
    await db.run(`
      INSERT INTO daily_attendance 
      (employee_code, employee_name, department, date, attendance_type, check_in, check_out, notes, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
    `, [employeeCode, employeeName, department, date, attendanceType, checkIn || null, checkOut || null, notes || null]);
    
    return 'inserted';
  }
}

export async function POST(request) {
  try {
    const formData = await request.formData();
    const file = formData.get('file');
    const isPreview = formData.get('preview') === 'true';
    
    if (!file) {
      return NextResponse.json({
        success: false,
        error: 'لم يتم اختيار ملف'
      }, { status: 400 });
    }
    
    // قراءة الملف
    const buffer = Buffer.from(await file.arrayBuffer());
    const data = await readExcelFile(buffer);
    
    if (data.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الملف فارغ أو لا يحتوي على بيانات صحيحة'
      }, { status: 400 });
    }
    
    // التحقق من صحة البيانات
    const validationErrors = [];
    data.forEach((row, index) => {
      const errors = validateAttendanceData(row, index);
      validationErrors.push(...errors);
    });
    
    // إذا كان طلب معاينة فقط
    if (isPreview) {
      return NextResponse.json({
        success: true,
        preview: data.slice(0, 10), // أول 10 سجلات للمعاينة
        validationErrors,
        totalRecords: data.length
      });
    }
    
    // إذا كانت هناك أخطاء في التحقق
    if (validationErrors.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'توجد أخطاء في البيانات',
        validationErrors
      }, { status: 400 });
    }
    
    // فتح قاعدة البيانات
    const db = await openDb();
    
    // إنشاء جدول التمام اليومي إذا لم يكن موجوداً
    await db.exec(`
      CREATE TABLE IF NOT EXISTS daily_attendance (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        employee_code TEXT NOT NULL,
        employee_name TEXT,
        department TEXT,
        date TEXT NOT NULL,
        attendance_type TEXT NOT NULL,
        check_in TEXT,
        check_out TEXT,
        notes TEXT,
        created_at TEXT,
        updated_at TEXT,
        UNIQUE(employee_code, date)
      )
    `);
    
    // معالجة البيانات
    const stats = {
      total: data.length,
      inserted: 0,
      updated: 0,
      errors: 0
    };
    
    const errors = [];
    
    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      
      try {
        // التحقق من وجود الموظف
        const employeeExists = await validateEmployeeExists(db, row.employeeCode);
        if (!employeeExists) {
          errors.push(`الصف ${i + 2}: كود الموظف ${row.employeeCode} غير موجود في النظام`);
          stats.errors++;
          continue;
        }
        
        // إدراج أو تحديث البيانات
        const operation = await insertOrUpdateAttendance(db, row);
        
        if (operation === 'inserted') {
          stats.inserted++;
        } else if (operation === 'updated') {
          stats.updated++;
        }
        
      } catch (error) {

        errors.push(`الصف ${i + 2}: ${error.message}`);
        stats.errors++;
      }
    }
    
    await db.close();
    
    return NextResponse.json({
      success: true,
      message: `تم معالجة ${stats.total} سجل بنجاح`,
      stats,
      errors: errors.length > 0 ? errors : undefined
    });
    
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في معالجة الملف',
      details: error.message
    }, { status: 500 });
  }
}
