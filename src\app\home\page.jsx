'use client';
import React from 'react';

function MainComponent() {
  const [selectedLang, setSelectedLang] = useState('ar');
  const dir = selectedLang === 'ar' ? 'rtl' : 'ltr';

  const navigationItems = [
    {
      title: { ar: 'إدارة الموظفين', en: 'Employee Management' },
      icon: 'fa-users',
      link: '/employees',
      description: {
        ar: 'إدارة بيانات الموظفين وملفاتهم الشخصية',
        en: 'Manage employee data and personal files',
      },
    },
    {
      title: { ar: 'تسجيل الحضور', en: 'Attendance' },
      icon: 'fa-clock',
      link: '/attendance',
      description: {
        ar: 'تسجيل الحضور والانصراف اليومي',
        en: 'Record daily attendance and departures',
      },
    },
    {
      title: { ar: 'إدارة الأصول', en: 'Asset Management' },
      icon: 'fa-building',
      link: '/assets',
      description: {
        ar: 'إدارة الشقق والسيارات والأصول المؤجرة',
        en: 'Manage apartments, vehicles, and rented assets',
      },
    },
    {
      title: { ar: 'إدارة الإجازات', en: 'Leave Management' },
      icon: 'fa-calendar',
      link: '/leaves',
      description: {
        ar: 'إدارة طلبات الإجازات والمغادرات',
        en: 'Manage leave requests and departures',
      },
    },
  ];

  return (
    <div
      dir={dir}
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
            {selectedLang === 'ar'
              ? 'نظام إدارة شؤون الموظفين'
              : 'Employee Management System'}
          </h1>
          <button
            onClick={() => setSelectedLang(selectedLang === 'ar' ? 'en' : 'ar')}
            className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
          >
            {selectedLang === 'ar' ? 'English' : 'العربية'}
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8">
          {navigationItems.map((item, index) => (
            <a
              key={index}
              href={item.link}
              className="bg-gray-50 dark:bg-gray-800 rounded-xl p-8 hover:shadow-lg transition-shadow duration-300 flex items-start space-x-6 rtl:space-x-reverse"
            >
              <div className="flex-shrink-0">
                <i
                  className={`fas ${item.icon} text-4xl text-blue-600 dark:text-blue-400`}
                ></i>
              </div>
              <div className="flex-grow">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
                  {item.title[selectedLang]}
                </h2>
                <p className="text-gray-600 dark:text-gray-300 text-lg">
                  {item.description[selectedLang]}
                </p>
              </div>
            </a>
          ))}
        </div>

        <div className="mt-16 text-center text-gray-600 dark:text-gray-400">
          <p>
            {selectedLang === 'ar'
              ? '© 2025 نظام إدارة شؤون الموظفين. جميع الحقوق محفوظة'
              : '© 2025 Employee Management System. All rights reserved'}
          </p>
        </div>
      </div>
    </div>
  );
}

export default MainComponent;
