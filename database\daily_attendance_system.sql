-- ===================================
-- نظام التمام اليومي وربط جهاز البصمة
-- ===================================

-- 1. جدول بيانات جهاز البصمة
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='BiometricData' AND xtype='U')
BEGIN
    CREATE TABLE BiometricData (
        ID INT IDENTITY(1,1) PRIMARY KEY,
        EmployeeCode NVARCHAR(20) NOT NULL,
        EmployeeName NVARCHAR(100),
        CheckTime DATETIME NOT NULL,
        CheckType NVARCHAR(20) NOT NULL, -- IN, OUT
        DeviceID NVARCHAR(50),
        DeviceName NVARCHAR(100),
        VerifyMode NVARCHAR(20), -- Fingerprint, Card, Password
        WorkCode NVARCHAR(20),
        IsProcessed BIT DEFAULT 0,
        ProcessedAt DATETIME,
        ImportedAt DATETIME DEFAULT GETDATE(),
        Notes NVARCHAR(500),
    )
END

-- 2. جدول التمام اليومي
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DailyAttendance' AND xtype='U')
BEGIN
    CREATE TABLE DailyAttendance (
        ID INT IDENTITY(1,1) PRIMARY KEY,
        AttendanceDate DATE NOT NULL,
        EmployeeCode NVARCHAR(20) NOT NULL,
        EmployeeName NVARCHAR(100) NOT NULL,
        JobTitle NVARCHAR(100),
        Department NVARCHAR(100),
        
        -- أوقات الحضور والانصراف
        CheckInTime TIME,
        CheckOutTime TIME,
        ActualCheckIn DATETIME, -- من جهاز البصمة
        ActualCheckOut DATETIME, -- من جهاز البصمة
        
        -- المؤثرات اليومية
        AttendanceStatus NVARCHAR(50) NOT NULL, -- حضور، غياب، إجازة، تأخير، وردية ليلية
        EffectType NVARCHAR(50), -- مالي، إداري
        EffectAmount DECIMAL(8,2) DEFAULT 0,
        
        -- تفاصيل إضافية
        LateMinutes INT DEFAULT 0,
        EarlyLeaveMinutes INT DEFAULT 0,
        OvertimeMinutes INT DEFAULT 0,
        WorkingHours DECIMAL(4,2) DEFAULT 0,
        
        -- معلومات الإجازة (إن وجدت)
        LeaveType NVARCHAR(50), -- سنوية، مرضية، عارضة، بدون راتب، أمومة
        LeaveStartDate DATE,
        LeaveEndDate DATE,
        LeaveReason NVARCHAR(500),
        
        -- ملاحظات ومعلومات إضافية
        Notes NVARCHAR(MAX),
        Remarks NVARCHAR(500),
        
        -- معلومات النظام
        IsManualEntry BIT DEFAULT 0, -- دخل يدوياً أم من البصمة
        CreatedBy NVARCHAR(100),
        CreatedAt DATETIME DEFAULT GETDATE(),
        UpdatedBy NVARCHAR(100),
        UpdatedAt DATETIME DEFAULT GETDATE(),
    )
    -- الفهارس بعد إنشاء الجدول
    CREATE INDEX IX_DailyAttendance_Date ON DailyAttendance(AttendanceDate);
    CREATE INDEX IX_DailyAttendance_Employee ON DailyAttendance(EmployeeCode);
    CREATE INDEX IX_DailyAttendance_Status ON DailyAttendance(AttendanceStatus);
    CREATE UNIQUE INDEX UX_DailyAttendance_Employee_Date ON DailyAttendance(EmployeeCode, AttendanceDate);
END

-- 3. جدول أنواع المؤثرات اليومية
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DailyEffectTypes' AND xtype='U')
BEGIN
    CREATE TABLE DailyEffectTypes (
        ID INT IDENTITY(1,1) PRIMARY KEY,
        EffectCode NVARCHAR(10) NOT NULL UNIQUE,
        EffectNameAr NVARCHAR(100) NOT NULL,
        EffectNameEn NVARCHAR(100),
        EffectCategory NVARCHAR(50) NOT NULL, -- مالي، إداري
        EffectType NVARCHAR(20) NOT NULL, -- إيجابي، سلبي، محايد
        DefaultAmount DECIMAL(8,2) DEFAULT 0,
        Description NVARCHAR(500),
        IsActive BIT DEFAULT 1
    )
    CREATE INDEX IX_DailyEffectTypes_Code ON DailyEffectTypes(EffectCode);
    CREATE INDEX IX_DailyEffectTypes_Category ON DailyEffectTypes(EffectCategory);
    -- إدراج البيانات الأساسية
    INSERT INTO DailyEffectTypes (EffectCode, EffectNameAr, EffectNameEn, EffectCategory, EffectType, Description) VALUES
    ('W', 'حضور', 'Present', 'إداري', 'محايد', 'حضور عادي في الوقت المحدد'),
    ('Ab', 'غياب', 'Absent', 'مالي', 'سلبي', 'غياب بدون عذر'),
    ('S', 'إجازة مرضية', 'Sick Leave', 'إداري', 'محايد', 'إجازة مرضية بشهادة طبية'),
    ('R', 'إجازة سنوية', 'Annual Leave', 'إداري', 'محايد', 'إجازة سنوية اعتيادية'),
    ('NH', 'إجازة رسمية', 'National Holiday', 'إداري', 'محايد', 'إجازة رسمية/عطلة'),
    ('CR', 'إجازة عارضة', 'Casual Leave', 'إداري', 'محايد', 'إجازة عارضة طارئة'),
    ('M', 'مأمورية', 'Mission', 'مالي', 'إيجابي', 'مأمورية رسمية'),
    ('AL', 'إجازة بدون راتب', 'Unpaid Leave', 'مالي', 'سلبي', 'إجازة بدون راتب'),
    ('CL', 'إجازة أمومة', 'Maternity Leave', 'إداري', 'محايد', 'إجازة أمومة'),
    ('UL', 'إجازة طارئة', 'Emergency Leave', 'إداري', 'محايد', 'إجازة طارئة'),
    ('ML', 'إجازة زواج', 'Marriage Leave', 'إداري', 'محايد', 'إجازة زواج'),
    ('L', 'تأخير', 'Late', 'مالي', 'سلبي', 'تأخير عن موعد العمل'),
    ('NS', 'وردية ليلية', 'Night Shift', 'مالي', 'إيجابي', 'عمل في الوردية الليلية'),
    ('OT', 'ساعات إضافية', 'Overtime', 'مالي', 'إيجابي', 'ساعات عمل إضافية')
END

-- 4. جدول إعدادات أوقات العمل
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='WorkTimeSettings' AND xtype='U')
BEGIN
    CREATE TABLE WorkTimeSettings (
        ID INT IDENTITY(1,1) PRIMARY KEY,
        SettingName NVARCHAR(100) NOT NULL,
        SettingValue NVARCHAR(200) NOT NULL,
        Description NVARCHAR(500),
        IsActive BIT DEFAULT 1,
        UpdatedAt DATETIME DEFAULT GETDATE()
    )
    
    -- إدراج الإعدادات الأساسية
    INSERT INTO WorkTimeSettings (SettingName, SettingValue, Description) VALUES
    ('WORK_START_TIME', '08:00', 'وقت بداية العمل الرسمي'),
    ('WORK_END_TIME', '17:00', 'وقت نهاية العمل الرسمي'),
    ('LUNCH_START_TIME', '12:00', 'وقت بداية استراحة الغداء'),
    ('LUNCH_END_TIME', '13:00', 'وقت نهاية استراحة الغداء'),
    ('LATE_TOLERANCE_MINUTES', '15', 'عدد دقائق التسامح في التأخير'),
    ('OVERTIME_START_AFTER_MINUTES', '30', 'بداية احتساب الساعات الإضافية بعد (دقيقة)'),
    ('NIGHT_SHIFT_START_TIME', '20:00', 'وقت بداية الوردية الليلية'),
    ('NIGHT_SHIFT_END_TIME', '06:00', 'وقت نهاية الوردية الليلية'),
    ('WEEKEND_DAYS', 'Friday,Saturday', 'أيام نهاية الأسبوع'),
    ('BIOMETRIC_SYNC_INTERVAL', '30', 'فترة مزامنة البصمة (دقيقة)')
END

-- 5. جدول ملخص الحضور الشهري
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MonthlyAttendanceSummary' AND xtype='U')
BEGIN
    CREATE TABLE MonthlyAttendanceSummary (
        ID INT IDENTITY(1,1) PRIMARY KEY,
        EmployeeCode NVARCHAR(20) NOT NULL,
        EmployeeName NVARCHAR(100) NOT NULL,
        Month INT NOT NULL,
        Year INT NOT NULL,
        
        -- إحصائيات الحضور
        TotalWorkingDays INT DEFAULT 0,
        PresentDays INT DEFAULT 0,
        AbsentDays INT DEFAULT 0,
        LateDays INT DEFAULT 0,
        LeavesDays INT DEFAULT 0,
        
        -- تفاصيل الإجازات
        AnnualLeaveDays INT DEFAULT 0,
        SickLeaveDays INT DEFAULT 0,
        CasualLeaveDays INT DEFAULT 0,
        UnpaidLeaveDays INT DEFAULT 0,
        
        -- الساعات
        TotalWorkingHours DECIMAL(6,2) DEFAULT 0,
        OvertimeHours DECIMAL(6,2) DEFAULT 0,
        LateHours DECIMAL(6,2) DEFAULT 0,
        
        -- المؤثرات المالية
        TotalPositiveEffects DECIMAL(10,2) DEFAULT 0,
        TotalNegativeEffects DECIMAL(10,2) DEFAULT 0,
        NetEffects DECIMAL(10,2) DEFAULT 0,
        
        -- معلومات النظام
        IsCalculated BIT DEFAULT 0,
        CalculatedAt DATETIME,
        CreatedAt DATETIME DEFAULT GETDATE(),
        UpdatedAt DATETIME DEFAULT GETDATE()
    )
    CREATE UNIQUE INDEX UX_MonthlyAttendance_Employee_Month ON MonthlyAttendanceSummary(EmployeeCode, Month, Year);
END

-- 6. إنشاء stored procedures للمعالجة

-- إجراء معالجة بيانات البصمة
IF NOT EXISTS (SELECT * FROM sys.procedures WHERE name = 'SP_ProcessBiometricData')
BEGIN
    EXEC('
    CREATE PROCEDURE SP_ProcessBiometricData
        @ProcessDate DATE = NULL
    AS
    BEGIN
        SET NOCOUNT ON
        
        IF @ProcessDate IS NULL
            SET @ProcessDate = CAST(GETDATE() AS DATE)
        
        -- معالجة بيانات البصمة وتحويلها لحضور يومي
        MERGE DailyAttendance AS target
        USING (
            SELECT 
                @ProcessDate as AttendanceDate,
                bd.EmployeeCode,
                e.EmployeeName as EmployeeName,
                e.JobTitle,
                e.Department,
                MIN(CASE WHEN bd.CheckType = ''IN'' THEN bd.CheckTime END) as FirstCheckIn,
                MAX(CASE WHEN bd.CheckType = ''OUT'' THEN bd.CheckTime END) as LastCheckOut
            FROM BiometricData bd
            INNER JOIN Employees e ON bd.EmployeeCode = e.EmployeeCode
            WHERE CAST(bd.CheckTime AS DATE) = @ProcessDate
                AND bd.IsProcessed = 0
            GROUP BY bd.EmployeeCode, e.EmployeeName, e.JobTitle, e.Department
        ) AS source ON target.EmployeeCode = source.EmployeeCode 
                    AND target.AttendanceDate = source.AttendanceDate
        
        WHEN MATCHED THEN
            UPDATE SET
                ActualCheckIn = source.FirstCheckIn,
                ActualCheckOut = source.LastCheckOut,
                AttendanceStatus = ''حضور'',
                UpdatedAt = GETDATE()
        
        WHEN NOT MATCHED THEN
            INSERT (AttendanceDate, EmployeeCode, EmployeeName, JobTitle, Department, 
                   ActualCheckIn, ActualCheckOut, AttendanceStatus, IsManualEntry)
            VALUES (source.AttendanceDate, source.EmployeeCode, source.EmployeeName, 
                   source.JobTitle, source.Department, source.FirstCheckIn, 
                   source.LastCheckOut, ''حضور'', 0);
        
        -- تحديث حالة المعالجة
        UPDATE BiometricData 
        SET IsProcessed = 1, ProcessedAt = GETDATE()
        WHERE CAST(CheckTime AS DATE) = @ProcessDate AND IsProcessed = 0
        
        SELECT @@ROWCOUNT as ProcessedRecords
    END
    ')
END
