import { NextResponse } from 'next/server';
import { getConnection } from '@/utils/db';

export async function GET(request) {
  try {

    const pool = await getConnection();
    
    // إحصائيات الموظفين
    const employeesResult = await pool.request().query(`
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN CurrentStatus = N'ساري' OR CurrentStatus = N'نشط' OR CurrentStatus IS NULL THEN 1 END) as active,
        COUNT(CASE WHEN CurrentStatus = N'منقول' THEN 1 END) as transferred,
        COUNT(CASE WHEN CurrentStatus = N'مستقيل' THEN 1 END) as resigned,
        COUNT(CASE WHEN CompanyHousing = N'نعم' THEN 1 END) as resident,
        COUNT(CASE WHEN CompanyHousing = N'لا' OR CompanyHousing IS NULL THEN 1 END) as nonResident,
        -- إحصائيات التأمين المفصلة
        COUNT(CASE WHEN SocialInsurance = N'مؤمن' AND MedicalInsurance = N'مؤمن' THEN 1 END) as bothInsured,
        COUNT(CASE WHEN SocialInsurance = N'مؤمن' AND (MedicalInsurance != N'مؤمن' OR MedicalInsurance IS NULL) THEN 1 END) as socialOnly,
        COUNT(CASE WHEN MedicalInsurance = N'مؤمن' AND (SocialInsurance != N'مؤمن' OR SocialInsurance IS NULL) THEN 1 END) as medicalOnly,
        COUNT(CASE WHEN (SocialInsurance != N'مؤمن' OR SocialInsurance IS NULL) AND (MedicalInsurance != N'مؤمن' OR MedicalInsurance IS NULL) THEN 1 END) as notInsured
      FROM Employees
    `);
    
    // إحصائيات الشقق
    let apartmentsResult;
    try {
      apartmentsResult = await pool.request().query(`
        SELECT
          COUNT(*) as total,
          COUNT(CASE WHEN IsActive = 1 THEN 1 END) as active,
          ISNULL(SUM(CASE WHEN IsActive = 1 THEN RentAmount ELSE 0 END), 0) as totalRent,
          -- عدد الشقق المشغولة
          (SELECT COUNT(DISTINCT a.ID) 
           FROM Apartments a 
           INNER JOIN ApartmentBeneficiaries ab ON a.ID = ab.ApartmentID 
           WHERE a.IsActive = 1 AND ab.IsActive = 1) as occupied,
          -- عدد الشقق الفارغة
          (SELECT COUNT(*) 
           FROM Apartments a 
           WHERE a.IsActive = 1 
           AND NOT EXISTS (SELECT 1 FROM ApartmentBeneficiaries ab WHERE ab.ApartmentID = a.ID AND ab.IsActive = 1)) as vacant
        FROM Apartments
      `);
    } catch (error) {

      apartmentsResult = { recordset: [{ total: 0, active: 0, totalRent: 0, occupied: 0, vacant: 0 }] };
    }
    
    // إحصائيات السيارات
    let carsResult;
    try {
      carsResult = await pool.request().query(`
        SELECT
          COUNT(*) as total,
          COUNT(CASE WHEN IsActive = 1 THEN 1 END) as active,
          ISNULL(SUM(CASE WHEN IsActive = 1 THEN RentAmount ELSE 0 END), 0) as totalRent
        FROM Cars
      `);
    } catch (error) {

      carsResult = { recordset: [{ total: 0, active: 0, totalRent: 0 }] };
    }
    
    // إحصائيات النقل والاستقالة
    let transfersResult;
    try {
      transfersResult = await pool.request().query(`
        SELECT
          COUNT(*) as totalTransfers,
          COUNT(CASE WHEN YEAR(TransferDate) = YEAR(GETDATE()) THEN 1 END) as thisYearTransfers
        FROM EmployeeTransfers
      `);
    } catch (error) {

      transfersResult = { recordset: [{ totalTransfers: 0, thisYearTransfers: 0 }] };
    }
    
    let resignationsResult;
    try {
      resignationsResult = await pool.request().query(`
        SELECT
          COUNT(*) as totalResignations,
          COUNT(CASE WHEN YEAR(ResignationDate) = YEAR(GETDATE()) THEN 1 END) as thisYearResignations
        FROM EmployeeResignations
      `);
    } catch (error) {

      resignationsResult = { recordset: [{ totalResignations: 0, thisYearResignations: 0 }] };
    }
    
    const stats = {
      employees: {
        total: employeesResult.recordset[0].total || 0,
        active: employeesResult.recordset[0].active || 0,
        transferred: employeesResult.recordset[0].transferred || 0,
        resigned: employeesResult.recordset[0].resigned || 0,
        resident: employeesResult.recordset[0].resident || 0,
        nonResident: employeesResult.recordset[0].nonResident || 0
      },
      insurance: {
        bothInsured: employeesResult.recordset[0].bothInsured || 0,
        socialOnly: employeesResult.recordset[0].socialOnly || 0,
        medicalOnly: employeesResult.recordset[0].medicalOnly || 0,
        notInsured: employeesResult.recordset[0].notInsured || 0
      },
      apartments: {
        total: apartmentsResult.recordset[0].total || 0,
        active: apartmentsResult.recordset[0].active || 0,
        occupied: apartmentsResult.recordset[0].occupied || 0,
        vacant: apartmentsResult.recordset[0].vacant || 0,
        totalRent: apartmentsResult.recordset[0].totalRent || 0
      },
      cars: {
        total: carsResult.recordset[0].total || 0,
        active: carsResult.recordset[0].active || 0,
        totalRent: carsResult.recordset[0].totalRent || 0
      },
      transfers: {
        total: transfersResult.recordset[0].totalTransfers || 0,
        thisYear: transfersResult.recordset[0].thisYearTransfers || 0
      },
      resignations: {
        total: resignationsResult.recordset[0].totalResignations || 0,
        thisYear: resignationsResult.recordset[0].thisYearResignations || 0
      }
    };

    return NextResponse.json({
      success: true,
      data: stats,
      message: 'تم جلب الإحصائيات بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message,
      details: {
        name: error.name,
        code: error.code,
        state: error.state
      }
    }, { status: 500 });
  }
}
