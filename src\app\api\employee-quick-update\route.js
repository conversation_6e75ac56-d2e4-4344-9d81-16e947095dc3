import { NextResponse } from 'next/server';
import sql from 'mssql';

const config = {
  user: 'sa',
  password: 'P@ssw0rd123',
  server: 'localhost',
  database: 'Employees',
  options: {
    encrypt: false,
    trustServerCertificate: true,
  },
};

export async function PUT(request) {
  let pool;
  
  try {
    const { employeeId, field, value } = await request.json();

    if (!employeeId || !field) {
      return NextResponse.json({
        success: false,
        message: 'بيانات غير مكتملة'
      }, { status: 400 });
    }

    // التحقق من الحقول المسموح تعديلها سريعاً
    const allowedFields = {
      'FullName': 'FullName',
      'JobTitle': 'JobTitle', 
      'Department': 'Department',
      'Mobile': 'Mobile',
      'Email': 'Email',
      'Governorate': 'Governorate',
      'MaritalStatus': 'MaritalStatus'
    };

    if (!allowedFields[field]) {
      return NextResponse.json({
        success: false,
        message: 'هذا الحقل غير مسموح تعديله سريعاً'
      }, { status: 400 });
    }

    // الاتصال بقاعدة البيانات
    pool = await sql.connect(config);

    // إنشاء استعلام التحديث
    const updateQuery = `UPDATE Employees SET ${allowedFields[field]} = @value WHERE EmployeeID = @employeeId`;

    const dbRequest = pool.request();
    dbRequest.input('employeeId', sql.VarChar, employeeId);
    
    // تحديد نوع البيانات حسب الحقل
    if (field === 'Mobile') {
      dbRequest.input('value', sql.VarChar, value);
    } else {
      dbRequest.input('value', sql.NVarChar, value);
    }

    // تنفيذ الاستعلام
    const result = await dbRequest.query(updateQuery);

    if (result.rowsAffected[0] > 0) {
      return NextResponse.json({
        success: true,
        message: 'تم تحديث البيانات بنجاح',
        field: field,
        value: value
      });
    } else {
      return NextResponse.json({
        success: false,
        message: 'لم يتم العثور على الموظف'
      }, { status: 404 });
    }

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'حدث خطأ في تحديث البيانات',
      error: error.message
    }, { status: 500 });

  } finally {
    if (pool) {
      try {
        await pool.close();
      } catch (closeError) {

      }
    }
  }
}
