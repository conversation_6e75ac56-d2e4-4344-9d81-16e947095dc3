'use client';

import MainLayout from '@/components/MainLayout';
import { useEffect, useState } from 'react';
import {
    FiBriefcase,
    FiChevronDown,
    FiChevronRight,
    FiDownload,
    FiEdit,
    FiEye,
    FiGrid, FiList,
    FiMapPin,
    FiPlus,
    FiRefreshCw,
    FiSearch,
    FiTarget,
    FiTrash2,
    FiUpload,
    FiUser,
    FiUserPlus,
    FiUsers
} from 'react-icons/fi';

export default function OrganizationalStructurePage() {
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState('table'); // 'table' or 'chart'
  const [departments, setDepartments] = useState([]);
  const [employees, setEmployees] = useState([]);
  const [allEmployees, setAllEmployees] = useState([]); // جميع الموظفين من قاعدة البيانات
  const [searchTerm, setSearchTerm] = useState('');
  const [employeeSearchTerm, setEmployeeSearchTerm] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [showEmployeeSearch, setShowEmployeeSearch] = useState(false);
  const [modalType, setModalType] = useState(''); // 'department', 'employee', 'assignment'
  const [editingItem, setEditingItem] = useState(null);
  const [expandedDepts, setExpandedDepts] = useState(new Set());
  const [selectedDepartment, setSelectedDepartment] = useState('');

  // بيانات النموذج
  const [formData, setFormData] = useState({
    departmentName: '',
    departmentCode: '',
    managerId: '',
    parentDepartmentId: '',
    description: '',
    employeeCode: '',
    employeeName: '',
    jobTitle: '',
    directManagerId: ''
  });

  // إحصائيات
  const [stats, setStats] = useState({
    totalDepartments: 0,
    totalEmployees: 0,
    managersCount: 0,
    levelsCount: 0
  });

  // تحميل البيانات
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      // تحميل الموظفين من قاعدة البيانات فقط في البداية
      const [allEmpsResponse] = await Promise.all([
        fetch('/api/org-structure?type=all-employees') // جميع الموظفين من قاعدة البيانات الأساسية
      ]);

      // تحميل جميع الموظفين للبحث والاختيار
      if (allEmpsResponse.ok) {
        const allEmpsData = await allEmpsResponse.json();
        setAllEmployees(allEmpsData.employees || []);

        // تعيين قوائم فارغة للأقسام والموظفين في البداية
        setDepartments([]);
        setEmployees([]);

        // حساب الإحصائيات
        setStats({
          totalDepartments: 0,
          totalEmployees: allEmpsData.employees?.length || 0,
          managersCount: 0,
          levelsCount: 1
        });
      }
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
    } finally {
      setLoading(false);
    }
  };

  // فتح النموذج
  const openModal = (type, item = null) => {
    setModalType(type);
    setEditingItem(item);
    setShowModal(true);

    if (item) {
      setFormData({
        departmentName: item.DepartmentName || '',
        departmentCode: item.DepartmentCode || '',
        managerId: item.ManagerId || '',
        parentDepartmentId: item.ParentDepartmentId || '',
        description: item.Description || '',
        employeeCode: item.EmployeeCode || '',
        employeeName: item.EmployeeName || '',
        jobTitle: item.JobTitle || '',
        directManagerId: item.DirectManagerId || ''
      });
    } else {
      setFormData({
        departmentName: '',
        departmentCode: '',
        managerId: '',
        parentDepartmentId: '',
        description: '',
        employeeCode: '',
        employeeName: '',
        jobTitle: '',
        directManagerId: ''
      });
    }
  };

  // حفظ البيانات
  const handleSave = async (e) => {
    e.preventDefault();

    try {
      const response = await fetch('/api/org-structure', {
        method: editingItem ? 'PUT' : 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: modalType,
          id: editingItem?.ID,
          ...formData
        })
      });

      if (response.ok) {
        alert(editingItem ? 'تم التحديث بنجاح' : 'تم الإضافة بنجاح');
        setShowModal(false);
        loadData();
      } else {
        const error = await response.json();
        alert('خطأ: ' + error.message);
      }
    } catch (error) {
      alert('خطأ في الحفظ: ' + error.message);
    }
  };

  // حذف عنصر
  const handleDelete = async (type, id) => {
    if (!confirm('هل أنت متأكد من الحذف؟')) return;

    try {
      const response = await fetch('/api/org-structure', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type, id })
      });

      if (response.ok) {
        alert('تم الحذف بنجاح');
        loadData();
      } else {
        const error = await response.json();
        alert('خطأ في الحذف: ' + error.message);
      }
    } catch (error) {
      alert('خطأ في الحذف: ' + error.message);
    }
  };

  // تبديل توسيع القسم
  const toggleDepartment = (deptId) => {
    const newExpanded = new Set(expandedDepts);
    if (newExpanded.has(deptId)) {
      newExpanded.delete(deptId);
    } else {
      newExpanded.add(deptId);
    }
    setExpandedDepts(newExpanded);
  };

  // فلترة البيانات
  const filteredDepartments = departments.filter(dept =>
    dept.DepartmentName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    dept.DepartmentCode?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredEmployees = employees.filter(emp =>
    emp.EmployeeName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    emp.EmployeeCode?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    emp.JobTitle?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // البحث في الموظفين
  const searchEmployees = (searchTerm) => {
    if (!searchTerm || searchTerm.length < 2) return [];

    return allEmployees.filter(emp =>
      emp.EmployeeName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      emp.EmployeeCode?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      emp.JobTitle?.toLowerCase().includes(searchTerm.toLowerCase())
    ).slice(0, 10); // أول 10 نتائج
  };

  // التحقق من صحة المدير المباشر
  const validateDirectManager = (managerCode) => {
    if (!managerCode) return null;

    const manager = allEmployees.find(emp =>
      emp.EmployeeCode === managerCode ||
      emp.EmployeeName?.toLowerCase().includes(managerCode.toLowerCase())
    );

    return manager || null;
  };

  // الحصول على تفاصيل الموظف
  const getEmployeeDetails = (employeeCode) => {
    return allEmployees.find(emp => emp.EmployeeCode === employeeCode);
  };

  // معالجة رفع ملف Excel
  const handleExcelUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', 'direct-managers');

    try {
      setLoading(true);
      const response = await fetch('/api/org-structure/import', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();
      if (result.success) {
        let message = `✅ ${result.message}`;
        if (result.details) {
          message += `\n\nتفاصيل العملية:`;
          message += `\n- تم التحديث بنجاح: ${result.details.successCount}`;
          if (result.details.errorCount > 0) {
            message += `\n- فشل في التحديث: ${result.details.errorCount}`;
            if (result.details.errors && result.details.errors.length > 0) {
              message += `\n\nأول الأخطاء:`;
              result.details.errors.slice(0, 3).forEach(error => {
                message += `\n- ${error}`;
              });
            }
          }
        }
        alert(message);
        loadData(); // إعادة تحميل البيانات
      } else {
        alert('❌ خطأ في الاستيراد: ' + result.error);
      }
    } catch (error) {
      alert('❌ خطأ في رفع الملف: ' + error.message);
    } finally {
      setLoading(false);
      // إعادة تعيين قيمة المدخل
      event.target.value = '';
    }
  };

  // بناء الهيكل الهرمي
  const buildHierarchy = () => {
    const hierarchy = [];
    const deptMap = new Map();

    // إنشاء خريطة الأقسام
    departments.forEach(dept => {
      deptMap.set(dept.ID, {
        ...dept,
        children: [],
        employees: employees.filter(emp => emp.DepartmentId === dept.ID)
      });
    });

    // بناء الهيكل الهرمي
    departments.forEach(dept => {
      const deptNode = deptMap.get(dept.ID);
      if (dept.ParentDepartmentId && deptMap.has(dept.ParentDepartmentId)) {
        deptMap.get(dept.ParentDepartmentId).children.push(deptNode);
      } else {
        hierarchy.push(deptNode);
      }
    });

    return hierarchy;
  };

  return (
    <MainLayout>
      <div className="max-w-full mx-auto px-4">
        {/* رأس الصفحة */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6 pulse-box">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FiUsers className="text-3xl text-blue-600" />
              <div>
                <h1 className="text-3xl font-bold text-gray-800">الهيكل التنظيمي</h1>
                <p className="text-gray-600 mt-1">إدارة شاملة للهيكل التنظيمي والأقسام والموظفين</p>
              </div>
            </div>

            <div className="flex gap-2">
              <button
                onClick={() => openModal('department')}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
              >
                <FiPlus />
                قسم جديد
              </button>
              <button
                onClick={() => openModal('employee')}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2"
              >
                <FiUserPlus />
                موظف جديد
              </button>
              <button
                onClick={() => openModal('assignment')}
                className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center gap-2"
              >
                <FiTarget />
                ربط مسؤولية
              </button>
            </div>
          </div>
        </div>

        {/* بطاقات الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-sm p-6 gentle-animated-box">
            <div className="flex items-center">
              <div className="bg-blue-100 p-3 rounded-full">
                <FiBriefcase className="text-blue-600 text-xl" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الأقسام</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalDepartments}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 gentle-animated-box">
            <div className="flex items-center">
              <div className="bg-green-100 p-3 rounded-full">
                <FiUsers className="text-green-600 text-xl" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الموظفين</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalEmployees}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 gentle-animated-box">
            <div className="flex items-center">
              <div className="bg-purple-100 p-3 rounded-full">
                <FiUser className="text-purple-600 text-xl" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">المديرين</p>
                <p className="text-2xl font-bold text-gray-900">{stats.managersCount}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 gentle-animated-box">
            <div className="flex items-center">
              <div className="bg-orange-100 p-3 rounded-full">
                <FiMapPin className="text-orange-600 text-xl" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">مستويات الهيكل</p>
                <p className="text-2xl font-bold text-gray-900">{stats.levelsCount}</p>
              </div>
            </div>
          </div>
        </div>

        {/* شريط الأدوات */}
        <div className="bg-white rounded-lg shadow-sm p-4 mb-6 gentle-animated-box">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {/* أزرار العرض */}
              <div className="flex bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('table')}
                  className={`px-4 py-2 rounded-md flex items-center gap-2 transition-colors ${
                    viewMode === 'table'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  <FiList />
                  عرض جدولي
                </button>
                <button
                  onClick={() => setViewMode('chart')}
                  className={`px-4 py-2 rounded-md flex items-center gap-2 transition-colors ${
                    viewMode === 'chart'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-800'
                  }`}
                >
                  <FiGrid />
                  مخطط هرمي
                </button>
              </div>

              {/* البحث */}
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="البحث في الهيكل التنظيمي..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-80"
                />
              </div>

              {/* زر عرض المديرين المباشرين */}
              <button
                onClick={() => setViewMode('managers')}
                className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-colors ${
                  viewMode === 'managers'
                    ? 'bg-purple-600 text-white'
                    : 'bg-purple-100 text-purple-700 hover:bg-purple-200'
                }`}
              >
                <FiUser />
                المديرين المباشرين
              </button>

              {/* فلتر الأقسام */}
              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">جميع الأقسام</option>
                {departments.map(dept => (
                  <option key={dept.ID} value={dept.ID}>
                    {dept.DepartmentName}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex items-center gap-2">
              <button
                onClick={loadData}
                disabled={loading}
                className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:opacity-50"
              >
                <FiRefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                تحديث
              </button>

              <button className="flex items-center gap-2 px-4 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200">
                <FiDownload className="w-4 h-4" />
                تصدير
              </button>

              <button
                onClick={() => document.getElementById('excel-upload').click()}
                className="flex items-center gap-2 px-4 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200"
              >
                <FiUpload className="w-4 h-4" />
                استيراد Excel
              </button>

              {/* مدخل ملف Excel مخفي */}
              <input
                id="excel-upload"
                type="file"
                accept=".xlsx,.xls"
                onChange={handleExcelUpload}
                className="hidden"
              />
            </div>
          </div>
        </div>

        {/* المحتوى الرئيسي */}
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">جاري التحميل...</p>
          </div>
        ) : (
          <div className="bg-white rounded-lg shadow-sm gentle-animated-box">
            {viewMode === 'managers' ? (
              // عرض المديرين المباشرين
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold text-gray-800">المديرين المباشرين</h2>
                  <div className="text-sm text-gray-600">
                    عرض هيكل المديرين المباشرين بالمستويات المتعددة
                  </div>
                </div>

                <div className="overflow-x-auto">
                  <table className="w-full min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[8%]">
                          مسلسل
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[10%]">
                          رقم الموظف
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[20%]">
                          اسم الموظف
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[12%]">
                          الوظيفة
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[12%]">
                          المدير المباشر 1
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[12%]">
                          المدير المباشر 2
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[12%]">
                          المدير المباشر 3
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[12%]">
                          المدير المباشر 4
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[8%]">
                          الإجراءات
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredEmployees.map((emp, index) => (
                        <tr key={emp.EmployeeCode} className="hover:bg-gray-50">
                          <td className="px-4 py-4 text-sm text-gray-900 text-center">
                            {index + 1}
                          </td>
                          <td className="px-4 py-4 text-sm text-gray-900 text-center">
                            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-mono">
                              {emp.EmployeeCode}
                            </span>
                          </td>
                          <td className="px-4 py-4 text-sm text-gray-900">
                            <div className="font-medium">{emp.EmployeeName}</div>
                          </td>
                          <td className="px-4 py-4 text-sm text-gray-900">
                            {emp.JobTitle}
                          </td>
                          <td className="px-4 py-4 text-sm text-gray-900">
                            {emp.DirectManagerCode1 ? (
                              <div>
                                <div className="font-medium text-xs">{emp.DirectManagerCode1}</div>
                                <div className="text-xs text-gray-600 truncate" title={emp.DirectManagerName1}>
                                  {emp.DirectManagerName1}
                                </div>
                              </div>
                            ) : (
                              <span className="text-gray-400 text-xs">-</span>
                            )}
                          </td>
                          <td className="px-4 py-4 text-sm text-gray-900">
                            {emp.DirectManagerCode2 ? (
                              <div>
                                <div className="font-medium text-xs">{emp.DirectManagerCode2}</div>
                                <div className="text-xs text-gray-600 truncate" title={emp.DirectManagerName2}>
                                  {emp.DirectManagerName2}
                                </div>
                              </div>
                            ) : (
                              <span className="text-gray-400 text-xs">-</span>
                            )}
                          </td>
                          <td className="px-4 py-4 text-sm text-gray-900">
                            {emp.DirectManagerCode3 ? (
                              <div>
                                <div className="font-medium text-xs">{emp.DirectManagerCode3}</div>
                                <div className="text-xs text-gray-600 truncate" title={emp.DirectManagerName3}>
                                  {emp.DirectManagerName3}
                                </div>
                              </div>
                            ) : (
                              <span className="text-gray-400 text-xs">-</span>
                            )}
                          </td>
                          <td className="px-4 py-4 text-sm text-gray-900">
                            {emp.DirectManagerCode4 ? (
                              <div>
                                <div className="font-medium text-xs">{emp.DirectManagerCode4}</div>
                                <div className="text-xs text-gray-600 truncate" title={emp.DirectManagerName4}>
                                  {emp.DirectManagerName4}
                                </div>
                              </div>
                            ) : (
                              <span className="text-gray-400 text-xs">-</span>
                            )}
                          </td>
                          <td className="px-4 py-4 text-sm font-medium">
                            <button
                              onClick={() => openModal('employee', emp)}
                              className="text-blue-600 hover:text-blue-900 bg-blue-50 hover:bg-blue-100 p-2 rounded"
                              title="تعديل"
                            >
                              <FiEdit className="w-4 h-4" />
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            ) : viewMode === 'table' ? (
              // العرض الجدولي
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold text-gray-800">العرض الجدولي</h2>
                  <div className="text-sm text-gray-600">
                    عرض {filteredDepartments.length} قسم و {filteredEmployees.length} موظف
                  </div>
                </div>

                <div className="overflow-x-auto">
                  <table className="w-full min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          القسم
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          كود القسم
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          المدير
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          عدد الموظفين
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          القسم الأب
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          الإجراءات
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredDepartments.map((dept) => {
                        const deptEmployees = employees.filter(emp => emp.DepartmentId === dept.ID);
                        // البحث عن المدير في قاعدة البيانات الكاملة
                        const manager = allEmployees.find(emp => emp.EmployeeCode === dept.ManagerId) ||
                                       employees.find(emp => emp.ID === dept.ManagerId);
                        const parentDept = departments.find(d => d.ID === dept.ParentDepartmentId);

                        return (
                          <tr key={dept.ID} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="flex items-center">
                                <div className="bg-blue-100 p-2 rounded-full mr-3">
                                  <FiBriefcase className="text-blue-600 text-sm" />
                                </div>
                                <div>
                                  <div className="text-sm font-medium text-gray-900">
                                    {dept.DepartmentName}
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    {dept.Description}
                                  </div>
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs font-mono">
                                {dept.DepartmentCode}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              {manager ? (
                                <div className="flex items-center">
                                  <div className="bg-green-100 p-1 rounded-full mr-2">
                                    <FiUser className="text-green-600 text-xs" />
                                  </div>
                                  <div>
                                    <div className="text-sm font-medium text-gray-900">
                                      {manager.EmployeeName}
                                    </div>
                                    <div className="text-sm text-gray-500">
                                      {manager.JobTitle}
                                    </div>
                                    {manager.EmployeeCode && (
                                      <div className="text-xs text-gray-400">
                                        كود: {manager.EmployeeCode}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              ) : (
                                <span className="text-gray-400 text-sm">غير محدد</span>
                              )}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-bold">
                                {deptEmployees.length}
                              </span>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              {parentDept ? (
                                <span className="text-sm text-gray-900">{parentDept.DepartmentName}</span>
                              ) : (
                                <span className="text-gray-400 text-sm">قسم رئيسي</span>
                              )}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <div className="flex gap-2">
                                <button
                                  onClick={() => openModal('department', dept)}
                                  className="text-blue-600 hover:text-blue-900 bg-blue-50 hover:bg-blue-100 p-2 rounded"
                                  title="تعديل"
                                >
                                  <FiEdit className="w-4 h-4" />
                                </button>
                                <button
                                  onClick={() => handleDelete('department', dept.ID)}
                                  className="text-red-600 hover:text-red-900 bg-red-50 hover:bg-red-100 p-2 rounded"
                                  title="حذف"
                                >
                                  <FiTrash2 className="w-4 h-4" />
                                </button>
                                <button
                                  onClick={() => {
                                    setSelectedDepartment(dept.ID);
                                    // عرض تفاصيل القسم
                                  }}
                                  className="text-green-600 hover:text-green-900 bg-green-50 hover:bg-green-100 p-2 rounded"
                                  title="عرض التفاصيل"
                                >
                                  <FiEye className="w-4 h-4" />
                                </button>
                              </div>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            ) : (
              // العرض الهرمي
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold text-gray-800">المخطط الهرمي</h2>
                  <div className="text-sm text-gray-600">
                    عرض هرمي للهيكل التنظيمي
                  </div>
                </div>

                <div className="space-y-4">
                  {buildHierarchy().map(dept => (
                    <DepartmentNode
                      key={dept.ID}
                      department={dept}
                      level={0}
                      expandedDepts={expandedDepts}
                      toggleDepartment={toggleDepartment}
                      onEdit={(dept) => openModal('department', dept)}
                      onDelete={(id) => handleDelete('department', id)}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* النماذج */}
        {showModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between p-6 border-b">
                <h3 className="text-xl font-bold text-gray-800">
                  {modalType === 'department' && (editingItem ? 'تعديل القسم' : 'إضافة قسم جديد')}
                  {modalType === 'employee' && (editingItem ? 'تعديل الموظف' : 'إضافة موظف جديد')}
                  {modalType === 'assignment' && 'ربط مسؤولية قسم'}
                </h3>
                <button
                  onClick={() => setShowModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <form onSubmit={handleSave} className="p-6 space-y-6">
                {modalType === 'department' && (
                  <>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          اسم القسم *
                        </label>
                        <input
                          type="text"
                          required
                          value={formData.departmentName}
                          onChange={(e) => setFormData({...formData, departmentName: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="أدخل اسم القسم"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          كود القسم *
                        </label>
                        <input
                          type="text"
                          required
                          value={formData.departmentCode}
                          onChange={(e) => setFormData({...formData, departmentCode: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="أدخل كود القسم"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          مدير القسم
                        </label>
                        <select
                          value={formData.managerId}
                          onChange={(e) => setFormData({...formData, managerId: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                          <option value="">اختر مدير القسم</option>
                          {allEmployees.map(emp => (
                            <option key={emp.EmployeeCode} value={emp.EmployeeCode}>
                              {emp.EmployeeName} - {emp.JobTitle} (كود: {emp.EmployeeCode})
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          القسم الأب
                        </label>
                        <select
                          value={formData.parentDepartmentId}
                          onChange={(e) => setFormData({...formData, parentDepartmentId: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                          <option value="">قسم رئيسي</option>
                          {departments.filter(d => d.ID !== editingItem?.ID).map(dept => (
                            <option key={dept.ID} value={dept.ID}>
                              {dept.DepartmentName}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        وصف القسم
                      </label>
                      <textarea
                        value={formData.description}
                        onChange={(e) => setFormData({...formData, description: e.target.value})}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="أدخل وصف القسم"
                      />
                    </div>
                  </>
                )}

                {modalType === 'employee' && (
                  <>
                    {/* البحث في الموظفين الموجودين */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                      <label className="block text-sm font-medium text-blue-800 mb-2">
                        البحث في الموظفين الموجودين
                      </label>
                      <div className="relative">
                        <input
                          type="text"
                          value={employeeSearchTerm}
                          onChange={(e) => {
                            setEmployeeSearchTerm(e.target.value);
                            setShowEmployeeSearch(e.target.value.length >= 2);
                          }}
                          className="w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="ابحث بالاسم أو الكود أو المسمى الوظيفي..."
                        />

                        {/* نتائج البحث */}
                        {showEmployeeSearch && employeeSearchTerm.length >= 2 && (
                          <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                            {searchEmployees(employeeSearchTerm).map((emp) => (
                              <div
                                key={emp.EmployeeCode}
                                onClick={() => {
                                  setFormData({
                                    ...formData,
                                    employeeCode: emp.EmployeeCode,
                                    employeeName: emp.EmployeeName,
                                    jobTitle: emp.JobTitle || ''
                                  });
                                  setEmployeeSearchTerm('');
                                  setShowEmployeeSearch(false);
                                }}
                                className="p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                              >
                                <div className="font-medium text-gray-900">{emp.EmployeeName}</div>
                                <div className="text-sm text-gray-600">كود: {emp.EmployeeCode}</div>
                                <div className="text-sm text-gray-500">{emp.JobTitle}</div>
                              </div>
                            ))}
                            {searchEmployees(employeeSearchTerm).length === 0 && (
                              <div className="p-3 text-gray-500 text-center">
                                لا توجد نتائج مطابقة
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          كود الموظف *
                        </label>
                        <div className="relative">
                          <input
                            type="text"
                            required
                            value={formData.employeeCode}
                            onChange={(e) => {
                              setFormData({...formData, employeeCode: e.target.value});
                              // التحقق التلقائي من بيانات الموظف
                              const empDetails = getEmployeeDetails(e.target.value);
                              if (empDetails) {
                                setFormData({
                                  ...formData,
                                  employeeCode: e.target.value,
                                  employeeName: empDetails.EmployeeName,
                                  jobTitle: empDetails.JobTitle || ''
                                });
                              }
                            }}
                            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                              getEmployeeDetails(formData.employeeCode)
                                ? 'border-green-300 bg-green-50'
                                : 'border-gray-300'
                            }`}
                            placeholder="أدخل كود الموظف"
                          />
                          {getEmployeeDetails(formData.employeeCode) && (
                            <div className="absolute left-2 top-1/2 transform -translate-y-1/2">
                              <span className="text-green-600 text-sm">✓</span>
                            </div>
                          )}
                        </div>

                        {/* رسالة تأكيد العثور على الموظف */}
                        {getEmployeeDetails(formData.employeeCode) && (
                          <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded text-sm text-green-800">
                            ✓ تم العثور على الموظف في قاعدة البيانات
                          </div>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          اسم الموظف *
                        </label>
                        <input
                          type="text"
                          required
                          value={formData.employeeName}
                          onChange={(e) => setFormData({...formData, employeeName: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="أدخل اسم الموظف"
                          readOnly={!!getEmployeeDetails(formData.employeeCode)}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          المسمى الوظيفي *
                        </label>
                        <input
                          type="text"
                          required
                          value={formData.jobTitle}
                          onChange={(e) => setFormData({...formData, jobTitle: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="أدخل المسمى الوظيفي"
                          readOnly={!!getEmployeeDetails(formData.employeeCode)}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          المدير المباشر
                        </label>
                        <select
                          value={formData.directManagerId}
                          onChange={(e) => setFormData({...formData, directManagerId: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                          <option value="">اختر المدير المباشر</option>
                          {allEmployees.filter(emp => emp.EmployeeCode !== formData.employeeCode).map(emp => (
                            <option key={emp.EmployeeCode} value={emp.EmployeeCode}>
                              {emp.EmployeeName} - {emp.JobTitle} (كود: {emp.EmployeeCode})
                            </option>
                          ))}
                        </select>
                      </div>

                      {/* عرض القسم المقترح */}
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          القسم
                        </label>
                        <select
                          value={formData.departmentId}
                          onChange={(e) => setFormData({...formData, departmentId: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                          <option value="">اختر القسم</option>
                          {departments.map(dept => (
                            <option key={dept.ID} value={dept.ID}>
                              {dept.DepartmentName} ({dept.DepartmentCode})
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                  </>
                )}

                {modalType === 'assignment' && (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        اختر القسم
                      </label>
                      <select
                        required
                        value={formData.departmentId}
                        onChange={(e) => setFormData({...formData, departmentId: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="">اختر القسم</option>
                        {departments.map(dept => (
                          <option key={dept.ID} value={dept.ID}>
                            {dept.DepartmentName} ({dept.DepartmentCode})
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        اختر المدير المسؤول
                      </label>
                      <select
                        required
                        value={formData.managerId}
                        onChange={(e) => setFormData({...formData, managerId: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="">اختر المدير</option>
                        {allEmployees.map(emp => (
                          <option key={emp.EmployeeCode} value={emp.EmployeeCode}>
                            {emp.EmployeeName} - {emp.JobTitle} (كود: {emp.EmployeeCode})
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                )}

                <div className="flex justify-end gap-3 pt-4 border-t">
                  <button
                    type="button"
                    onClick={() => setShowModal(false)}
                    className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
                  >
                    إلغاء
                  </button>
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                  >
                    {editingItem ? 'تحديث' : 'حفظ'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}

// مكون عرض القسم في الهيكل الهرمي
function DepartmentNode({ department, level, expandedDepts, toggleDepartment, onEdit, onDelete }) {
  const isExpanded = expandedDepts.has(department.ID);
  const hasChildren = department.children && department.children.length > 0;
  const hasEmployees = department.employees && department.employees.length > 0;

  return (
    <div className={`${level > 0 ? 'mr-8' : ''}`}>
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {hasChildren && (
              <button
                onClick={() => toggleDepartment(department.ID)}
                className="text-gray-500 hover:text-gray-700"
              >
                {isExpanded ? <FiChevronDown /> : <FiChevronRight />}
              </button>
            )}

            <div className="bg-blue-100 p-2 rounded-full">
              <FiBriefcase className="text-blue-600" />
            </div>

            <div>
              <h3 className="font-semibold text-gray-900">{department.DepartmentName}</h3>
              <p className="text-sm text-gray-600">{department.DepartmentCode}</p>
            </div>

            <div className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-bold">
              {department.employees.length} موظف
            </div>
          </div>

          <div className="flex gap-2">
            <button
              onClick={() => onEdit(department)}
              className="text-blue-600 hover:text-blue-900 bg-blue-50 hover:bg-blue-100 p-2 rounded"
            >
              <FiEdit className="w-4 h-4" />
            </button>
            <button
              onClick={() => onDelete(department.ID)}
              className="text-red-600 hover:text-red-900 bg-red-50 hover:bg-red-100 p-2 rounded"
            >
              <FiTrash2 className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* عرض الموظفين */}
        {hasEmployees && (
          <div className="mt-3 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
            {department.employees.map(employee => (
              <div key={employee.ID} className="bg-white border border-gray-200 rounded p-3">
                <div className="flex items-center gap-2">
                  <div className="bg-green-100 p-1 rounded-full">
                    <FiUser className="text-green-600 text-xs" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {employee.EmployeeName}
                    </p>
                    <p className="text-xs text-gray-500 truncate">
                      {employee.JobTitle}
                    </p>
                    <p className="text-xs text-gray-400">
                      كود: {employee.EmployeeCode}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* الأقسام الفرعية */}
      {isExpanded && hasChildren && (
        <div className="mr-4">
          {department.children.map(child => (
            <DepartmentNode
              key={child.ID}
              department={child}
              level={level + 1}
              expandedDepts={expandedDepts}
              toggleDepartment={toggleDepartment}
              onEdit={onEdit}
              onDelete={onDelete}
            />
          ))}
        </div>
      )}
    </div>
  );
}
