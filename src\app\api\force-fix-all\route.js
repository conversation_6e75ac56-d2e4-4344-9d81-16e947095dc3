import { NextResponse } from 'next/server';
import { getConnection } from '@/lib/db';

export async function GET() {
  try {

    const pool = await getConnection();

    // الخطوة 1: البحث عن جميع السجلات التي تحتوي على "حضور" ولكن يجب أن تكون "إجازة"

    const problemRecordsResult = await pool.request().query(`
      SELECT 
        EmployeeCode,
        EmployeeName,
        AttendanceDate,
        Attendance,
        Notes,
        MONTH(AttendanceDate) as Month,
        YEAR(AttendanceDate) as Year
      FROM DailyAttendance 
      WHERE (
        -- السجلات التي تحتوي على "حضور" ولكن الملاحظات تشير لإجازة
        (Attendance = N'حضور' AND (
          Notes LIKE N'%إجازة%' OR 
          Notes LIKE N'%معتمد%' OR
          Notes LIKE N'%اعتيادية%' OR
          Notes LIKE N'%عارضة%' OR
          Notes LIKE N'%مرضية%'
        )) OR
        -- أو السجلات التي تحتوي على كلمة "حضور" في الإجازة (خطأ مطبعي)
        (Attendance LIKE N'%حضور%' AND Attendance LIKE N'%إجازة%')
      )
      AND AttendanceDate >= DATEADD(YEAR, -3, GETDATE()) -- آخر 3 سنوات
      ORDER BY EmployeeCode, AttendanceDate
    `);

    const problemRecords = problemRecordsResult.recordset;

    if (problemRecords.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'لا توجد سجلات تحتاج إصلاح',
        data: { recordsFixed: 0 }
      });
    }

    // الخطوة 2: إصلاح كل سجل

    let fixedRecords = 0;
    const fixedDetails = [];

    for (const record of problemRecords) {
      try {
        // تحديد نوع الإجازة الصحيح
        let correctAttendance = 'إجازة اعتيادية';
        
        if (record.Notes) {
          if (record.Notes.includes('عارضة')) {
            correctAttendance = 'إجازة عارضة';
          } else if (record.Notes.includes('مرضية')) {
            correctAttendance = 'إجازة مرضية';
          } else if (record.Notes.includes('اعتيادية')) {
            correctAttendance = 'إجازة اعتيادية';
          }
        }

        // إصلاح السجل
        await pool.request()
          .input('employeeCode', record.EmployeeCode)
          .input('date', record.AttendanceDate)
          .input('attendance', correctAttendance)
          .input('notes', record.Notes || `تم الإصلاح القسري إلى ${correctAttendance}`)
          .query(`
            UPDATE DailyAttendance 
            SET 
              Attendance = @attendance,
              Notes = @notes,
              UpdatedAt = GETDATE()
            WHERE EmployeeCode = @employeeCode AND AttendanceDate = @date
          `);

        fixedRecords++;
        
        if (fixedRecords <= 20) {
          fixedDetails.push({
            employee: record.EmployeeName,
            date: record.AttendanceDate.toISOString().split('T')[0],
            from: record.Attendance,
            to: correctAttendance
          });
        }

        if (fixedRecords % 100 === 0) {

        }

      } catch (error) {

      }
    }

    // الخطوة 3: إعادة بناء الملخص الشهري بالكامل

    // حذف جميع الملخصات
    await pool.request().query(`DELETE FROM MonthlyAttendanceSummary`);

    // جلب جميع الموظفين والشهور المتأثرة
    const affectedMonthsResult = await pool.request().query(`
      SELECT DISTINCT 
        da.EmployeeCode,
        da.EmployeeName,
        MONTH(da.AttendanceDate) as Month,
        YEAR(da.AttendanceDate) as Year
      FROM DailyAttendance da
      WHERE da.AttendanceDate >= DATEADD(YEAR, -3, GETDATE())
      ORDER BY da.EmployeeCode, Year, Month
    `);

    const affectedMonths = affectedMonthsResult.recordset;

    let rebuiltSummaries = 0;

    for (const am of affectedMonths) {
      try {
        // حساب الإحصائيات الصحيحة
        const statsResult = await pool.request()
          .input('employeeCode', am.EmployeeCode)
          .input('month', am.Month)
          .input('year', am.Year)
          .query(`
            SELECT
              COUNT(*) as TotalWorkingDays,
              SUM(CASE WHEN Attendance = N'حضور' THEN 1 ELSE 0 END) as TotalPresent,
              SUM(CASE WHEN Attendance = N'غياب' THEN 1 ELSE 0 END) as TotalAbsent,
              SUM(CASE WHEN Attendance LIKE N'%إجازة%' THEN 1 ELSE 0 END) as TotalLeaves,
              SUM(CASE WHEN Attendance = N'مأمورية' THEN 1 ELSE 0 END) as TotalMissions,
              SUM(CASE WHEN Attendance LIKE N'%اعتيادية%' THEN 1 ELSE 0 END) as AnnualLeaves,
              SUM(CASE WHEN Attendance LIKE N'%عارضة%' THEN 1 ELSE 0 END) as CasualLeaves,
              SUM(CASE WHEN Attendance LIKE N'%مرضية%' THEN 1 ELSE 0 END) as SickLeaves
            FROM DailyAttendance
            WHERE EmployeeCode = @employeeCode
              AND MONTH(AttendanceDate) = @month
              AND YEAR(AttendanceDate) = @year
          `);

        if (statsResult.recordset.length > 0) {
          const stats = statsResult.recordset[0];
          
          if (stats.TotalWorkingDays > 0) {
            const attendancePercentage = (stats.TotalPresent / stats.TotalWorkingDays) * 100;

            // إدراج الملخص الجديد
            await pool.request()
              .input('employeeCode', am.EmployeeCode)
              .input('employeeName', am.EmployeeName)
              .input('month', am.Month)
              .input('year', am.Year)
              .input('totalWorkingDays', stats.TotalWorkingDays)
              .input('totalPresent', stats.TotalPresent)
              .input('totalAbsent', stats.TotalAbsent)
              .input('totalLeaves', stats.TotalLeaves)
              .input('totalMissions', stats.TotalMissions || 0)
              .input('annualLeaves', stats.AnnualLeaves || 0)
              .input('casualLeaves', stats.CasualLeaves || 0)
              .input('sickLeaves', stats.SickLeaves || 0)
              .input('attendancePercentage', attendancePercentage)
              .query(`
                INSERT INTO MonthlyAttendanceSummary (
                  EmployeeCode, EmployeeName, Department, JobTitle, Month, Year,
                  TotalWorkingDays, TotalPresent, TotalAbsent, TotalLeaves, TotalMissions,
                  TotalSickLeave, TotalUnpaidLeave, TotalNightShifts, AttendancePercentage
                )
                VALUES (
                  @employeeCode, @employeeName, '', '', @month, @year,
                  @totalWorkingDays, @totalPresent, @totalAbsent, @totalLeaves, @totalMissions,
                  @sickLeaves, 0, 0, @attendancePercentage
                )
              `);

            rebuiltSummaries++;
          }
        }

        if (rebuiltSummaries % 50 === 0) {

        }

      } catch (error) {

      }
    }

    // الخطوة 4: إحصائيات نهائية
    const finalStatsResult = await pool.request().query(`
      SELECT 
        COUNT(DISTINCT EmployeeCode) as UniqueEmployees,
        COUNT(*) as TotalSummaries,
        SUM(TotalLeaves) as TotalLeaveDays,
        SUM(TotalPresent) as TotalPresentDays,
        AVG(AttendancePercentage) as AvgAttendancePercentage
      FROM MonthlyAttendanceSummary
    `);

    const finalStats = finalStatsResult.recordset[0];

    return NextResponse.json({
      success: true,
      message: '💪 تم الإصلاح القسري الشامل بنجاح',
      data: {
        problemRecordsFound: problemRecords.length,
        recordsFixed: fixedRecords,
        summariesRebuilt: rebuiltSummaries,
        finalStats: {
          uniqueEmployees: finalStats.UniqueEmployees,
          totalSummaries: finalStats.TotalSummaries,
          totalLeaveDays: finalStats.TotalLeaveDays,
          totalPresentDays: finalStats.TotalPresentDays,
          avgAttendancePercentage: finalStats.AvgAttendancePercentage?.toFixed(2) + '%'
        },
        sampleFixedRecords: fixedDetails
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
