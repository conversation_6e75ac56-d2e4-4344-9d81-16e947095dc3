import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

/**
 * API موحد للموظفين - يدعم التسميات الجديدة والقديمة
 * يحول البيانات من قاعدة البيانات (employeeCode, EmployeeName)
 * إلى التسميات الموحدة (EmployeeCode, EmployeeName)
 */

// دالة لمعالجة التواريخ وتجنب 1970-01-01
function formatDate(dateValue) {
  if (!dateValue) return null;
  const date = new Date(dateValue);
  // التحقق من صحة التاريخ وأنه ليس 1970-01-01
  if (isNaN(date.getTime()) || date.getFullYear() < 1900) {
    return null;
  }
  return dateValue;
}

// دالة لتحويل بيانات الموظف إلى التنسيق الموحد
function normalizeEmployeeData(employee) {
  return {
    // التسميات الموحدة الجديدة
    employeeCode: employee.EmployeeCode,
    employeeName: employee.EmployeeName,

    // التسميات القديمة للتوافق مع الواجهات الموجودة
    EmployeeCode: employee.EmployeeCode,
    EmployeeName: employee.EmployeeName,

    // باقي البيانات
    id: employee.EmployeeCode,
    name: employee.EmployeeName,
    fullName: employee.EmployeeName,
    jobTitle: employee.JobTitle,
    JobTitle: employee.JobTitle,
    department: employee.Department,
    Department: employee.Department,
    directManager: employee.direct,
    hireDate: formatDate(employee.HireDate),
    birthDate: formatDate(employee.BirthDate),
    joinDate: formatDate(employee.JoinDate),
    HireDate: formatDate(employee.HireDate),
    BirthDate: formatDate(employee.BirthDate),
    JoinDate: formatDate(employee.JoinDate),
    nationalID: employee.NationalID,
    governorate: employee.Governorate,
    maritalStatus: employee.MaritalStatus,
    gender: employee.Gender,
    currentStatus: employee.CurrentStatus,
    CurrentStatus: employee.CurrentStatus, // إضافة للتوافق
    militaryService: employee.Mserv,
    isResidentEmployee: employee.IsResidentEmployee,
    companyHousing: employee.CompanyHousing,
    housingCode: employee.codeHousing,
    transportMethod: employee.TransportMethod,
    area: employee.area,
    mobile: employee.Mobile,
    email: employee.email,
    education: employee.Education,
    university: employee.University,
    major: employee.Major,
    grade: employee.Grade,
    batch: employee.Batch,
    emergencyNumber: employee.emrnum,
    kinship: employee.Kinship,
    socialInsurance: employee.SocialInsurance,
    socialInsuranceDate: employee.spcialInsDate,
    socialInsuranceNumber: employee.SocialInsureNum,
    medicalInsurance: employee.MedicalInsurance,
    medicalInsuranceNumber: employee.MedicalInsuranceNum,

    // للعرض
    displayText: `${employee.EmployeeName} (${employee.EmployeeCode})`
  };
}

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'list';
    const employeeCode = searchParams.get('employeeCode');
    const search = searchParams.get('search');
    const limitParam = searchParams.get('limit');
    // إذا كان limit=0 أو غير محدد، جلب جميع الموظفين
    const limit = limitParam && parseInt(limitParam) > 0 ? parseInt(limitParam) : null;
    const selectedDate = searchParams.get('date'); // إضافة دعم التاريخ

    console.log(`📊 Employees API called with limit: ${limitParam} (parsed: ${limit}), date: ${selectedDate}`);

    const pool = await getConnection();

    switch (action) {
      case 'search':
        return await searchEmployees(pool, search, limit);
      case 'getById':
        return await getEmployeeById(pool, employeeCode);
      case 'list':
      default:
        return await listEmployees(pool, limit, selectedDate);
    }

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في API الموظفين: ' + error.message
    }, { status: 500 });
  }
}

export async function POST(request) {
  try {
    const body = await request.json();
    const { action } = body;

    const pool = await getConnection();

    switch (action) {
      case 'search':
        return await searchEmployees(pool, body.searchTerm, body.limit || null);
      case 'getById':
        return await getEmployeeById(pool, body.employeeCode);
      case 'list':
        return await listEmployees(pool, body.limit || null, body.selectedDate);
      case 'create':
        return await createEmployee(pool, body);
      case 'update':
        return await updateEmployee(pool, body);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في API الموظفين: ' + error.message
    }, { status: 500 });
  }
}

// البحث في الموظفين
async function searchEmployees(pool, searchTerm, limit = null) {
  if (!searchTerm || searchTerm.trim().length === 0) {
    return NextResponse.json({
      success: true,
      data: []
    });
  }

  const cleanSearchTerm = searchTerm.trim();
  const isNumeric = /^\d+$/.test(cleanSearchTerm);
  const topClause = limit ? `TOP (${limit})` : '';

  let searchQuery;
  if (isNumeric) {
    searchQuery = `
      SELECT ${topClause}
        EmployeeCode, EmployeeName, JobTitle, Department, direct as DirectManager,
        HireDate, BirthDate, JoinDate, NationalID, Governorate,
        MaritalStatus, Gender, CurrentStatus, Mserv as MilitaryService,
        IsResidentEmployee,
        area as Area, Mobile, email as Email,
        Education, University, Major, Grade, Batch,
        emrnum as EmergencyNumber, Kinship, SocialInsurance, spcialInsDate as SocialInsuranceDate,
        SocialInsureNum as SocialInsuranceNumber, MedicalInsurance, MedicalInsuranceNum as MedicalInsuranceNumber
      FROM Employees
      WHERE EmployeeCode LIKE @SearchTerm + '%'
         OR CAST(EmployeeCode AS NVARCHAR) LIKE @SearchTerm + '%'
      ORDER BY EmployeeCode
    `;
  } else {
    searchQuery = `
      SELECT ${topClause}
        EmployeeCode, EmployeeName, JobTitle, Department, direct as DirectManager,
        HireDate, BirthDate, JoinDate, NationalID, Governorate,
        MaritalStatus, Gender, CurrentStatus, Mserv as MilitaryService,
        CASE
          WHEN EXISTS (
            SELECT 1 FROM ApartmentBeneficiaries ab
            WHERE ab.EmployeeCode = EmployeeCode
            AND ab.IsActive = 1
            AND (ab.EndDate IS NULL OR ab.EndDate >= GETDATE())
          ) THEN 1
          ELSE 0
        END as IsResidentEmployee,
        area as Area, Mobile, email as Email,
        Education, University, Major, Grade, Batch,
        emrnum as EmergencyNumber, Kinship, SocialInsurance, spcialInsDate as SocialInsuranceDate,
        SocialInsureNum as SocialInsuranceNumber, MedicalInsurance, MedicalInsuranceNum as MedicalInsuranceNumber
      FROM Employees
      WHERE EmployeeName LIKE '%' + @SearchTerm + '%'
         OR Department LIKE '%' + @SearchTerm + '%'
         OR JobTitle LIKE '%' + @SearchTerm + '%'
      ORDER BY EmployeeName
    `;
  }

  const request = pool.request().input('SearchTerm', sql.NVarChar, cleanSearchTerm);
  if (limit) {
    request.input('Limit', sql.Int, limit);
  }

  const result = await request.query(searchQuery);

  const employees = result.recordset.map(normalizeEmployeeData);

  return NextResponse.json({
    success: true,
    data: employees,
    searchType: isNumeric ? 'code' : 'name',
    query: cleanSearchTerm,
    totalFound: employees.length
  });
}

// جلب موظف بالكود
async function getEmployeeById(pool, employeeCode) {
  if (!employeeCode) {
    return NextResponse.json({
      success: false,
      error: 'كود الموظف مطلوب'
    }, { status: 400 });
  }

  const result = await pool.request()
    .input('employeeCode', sql.NVarChar, employeeCode)
    .query(`
      SELECT
        EmployeeCode, EmployeeName, JobTitle, Department, direct,
        HireDate, BirthDate, JoinDate, NationalID, Governorate,
        MaritalStatus, Gender, CurrentStatus, Mserv,
        e.IsResidentEmployee,
        area, Mobile, email,
        Education, University, Major, Grade, Batch,
        emrnum, Kinship, SocialInsurance, spcialInsDate,
        SocialInsureNum, MedicalInsurance, MedicalInsuranceNum
      FROM Employees e
      WHERE EmployeeCode = @employeeCode
         OR CAST(EmployeeCode AS NVARCHAR) = @employeeCode
    `);

  if (result.recordset.length === 0) {
    return NextResponse.json({
      success: false,
      error: 'الموظف غير موجود'
    }, { status: 404 });
  }

  const employee = normalizeEmployeeData(result.recordset[0]);

  return NextResponse.json({
    success: true,
    data: employee
  });
}

// جلب قائمة الموظفين
async function listEmployees(pool, limit = null, selectedDate = null) {
  // إذا لم يتم تمرير selectedDate، استخدم تاريخ اليوم
  const date = selectedDate || new Date().toISOString().split('T')[0];

  // إنشاء الاستعلام بدون حد أقصى أو مع حد حسب الطلب
  const topClause = limit ? `TOP (${limit})` : '';

  const result = await pool.request()
    .input('SelectedDate', sql.Date, date)
    .query(`
      SELECT ${topClause}
        e.EmployeeCode, e.EmployeeName, e.JobTitle, e.Department, e.direct,
        e.HireDate, e.BirthDate, e.JoinDate, e.NationalID, e.Governorate,
        e.MaritalStatus, e.Gender, e.CurrentStatus, e.Mserv,
        e.IsResidentEmployee,
        e.area, e.Mobile, e.email,
        e.Education, e.University, e.Major, e.Grade, e.Batch,
        e.emrnum, e.Kinship, e.SocialInsurance, e.spcialInsDate,
        e.SocialInsureNum, e.MedicalInsurance, e.MedicalInsuranceNum,
        r.ResignationDate, r.LastWorkingDay,
        t.TransferDate, t.NewDepartment,
        -- إضافة ترتيب هرمي للموظفين
        CASE
          WHEN e.JobTitle LIKE '%مدير إقليمي%' OR e.JobTitle LIKE '%Regional Manager%' THEN 1
          WHEN e.JobTitle LIKE '%مدير المكتب التقني%' OR e.JobTitle LIKE '%Technical Office Manager%' THEN 2
          WHEN e.JobTitle LIKE '%مدير مشروع%' OR e.JobTitle LIKE '%Project Manager%' THEN 3
          WHEN e.JobTitle LIKE '%مدير%' OR e.JobTitle LIKE '%Manager%' THEN 4
          ELSE 5
        END as HierarchyLevel
      FROM Employees e
      LEFT JOIN EmployeeResignations r ON e.EmployeeCode = r.EmployeeCode
      LEFT JOIN EmployeeTransfers t ON e.EmployeeCode = t.EmployeeCode
      WHERE
        (
          -- الموظفين النشطين
          e.CurrentStatus IN ('نشط', 'ساري', 'سارى')
          OR
          -- الموظفين المستقيلين: يظهرون حتى نهاية الفترة التي تم فيها تقديم الاستقالة
          (
            e.CurrentStatus = 'مستقيل'
            AND r.ResignationDate IS NOT NULL
            AND (
              -- حساب نهاية الفترة التي تحتوي على تاريخ الاستقالة
              -- إذا كانت الاستقالة من 11 إلى آخر الشهر، فالفترة تنتهي في 10 من الشهر التالي
              -- إذا كانت الاستقالة من 1 إلى 10، فالفترة تنتهي في 10 من نفس الشهر
              (
                -- الاستقالة في نفس السنة والشهر
                (
                  YEAR(r.ResignationDate) = YEAR(@SelectedDate)
                  AND MONTH(r.ResignationDate) = MONTH(@SelectedDate)
                  AND (
                    -- إذا كانت الاستقالة من 11-31، والتاريخ المحدد في نفس الشهر أو الشهر التالي
                    (DAY(r.ResignationDate) >= 11 AND MONTH(@SelectedDate) <= MONTH(r.ResignationDate) + 1)
                    OR
                    -- إذا كانت الاستقالة من 1-10، والتاريخ المحدد في نفس الشهر
                    (DAY(r.ResignationDate) <= 10 AND MONTH(@SelectedDate) = MONTH(r.ResignationDate))
                  )
                )
                OR
                -- الاستقالة في الشهر السابق من 11-31، والتاريخ الحالي في الشهر التالي حتى 10
                (
                  YEAR(r.ResignationDate) = YEAR(@SelectedDate)
                  AND MONTH(r.ResignationDate) = MONTH(@SelectedDate) - 1
                  AND DAY(r.ResignationDate) >= 11
                  AND DAY(@SelectedDate) <= 10
                )
                OR
                -- الاستقالة في ديسمبر من 11-31، والتاريخ الحالي في يناير التالي حتى 10
                (
                  YEAR(r.ResignationDate) = YEAR(@SelectedDate) - 1
                  AND MONTH(r.ResignationDate) = 12
                  AND DAY(r.ResignationDate) >= 11
                  AND MONTH(@SelectedDate) = 1
                  AND DAY(@SelectedDate) <= 10
                )
              )
            )
          )
          OR
          -- الموظفين المنقولين: يظهرون حتى نهاية الفترة التي تم فيها النقل
          (
            e.CurrentStatus = 'منقول'
            AND t.TransferDate IS NOT NULL
            AND (
              -- نفس منطق الاستقالة لكن للنقل
              (
                -- النقل في نفس السنة والشهر
                (
                  YEAR(t.TransferDate) = YEAR(@SelectedDate)
                  AND MONTH(t.TransferDate) = MONTH(@SelectedDate)
                  AND (
                    -- إذا كان النقل من 11-31، والتاريخ المحدد في نفس الشهر أو الشهر التالي
                    (DAY(t.TransferDate) >= 11 AND MONTH(@SelectedDate) <= MONTH(t.TransferDate) + 1)
                    OR
                    -- إذا كان النقل من 1-10، والتاريخ المحدد في نفس الشهر
                    (DAY(t.TransferDate) <= 10 AND MONTH(@SelectedDate) = MONTH(t.TransferDate))
                  )
                )
                OR
                -- النقل في الشهر السابق من 11-31، والتاريخ الحالي في الشهر التالي حتى 10
                (
                  YEAR(t.TransferDate) = YEAR(@SelectedDate)
                  AND MONTH(t.TransferDate) = MONTH(@SelectedDate) - 1
                  AND DAY(t.TransferDate) >= 11
                  AND DAY(@SelectedDate) <= 10
                )
                OR
                -- النقل في ديسمبر من 11-31، والتاريخ الحالي في يناير التالي حتى 10
                (
                  YEAR(t.TransferDate) = YEAR(@SelectedDate) - 1
                  AND MONTH(t.TransferDate) = 12
                  AND DAY(t.TransferDate) >= 11
                  AND MONTH(@SelectedDate) = 1
                  AND DAY(@SelectedDate) <= 10
                )
              )
            )
          )
        )
        -- فلترة الموظفين حسب تواريخ العمل
        AND (
          -- الموظفين الذين بدأوا العمل قبل أو في التاريخ المحدد
          (e.HireDate IS NULL OR e.HireDate <= @SelectedDate)
          AND (e.JoinDate IS NULL OR e.JoinDate <= @SelectedDate)
        )
      ORDER BY
        HierarchyLevel ASC,
        e.EmployeeName ASC
    `);

  const employees = result.recordset.map(employee => {
    const normalized = normalizeEmployeeData(employee);
    const selectedDateObj = new Date(date);

    // Add resignation and transfer information
    if (employee.ResignationDate) {
      normalized.resignationDate = formatDate(employee.ResignationDate);
      normalized.lastWorkingDay = formatDate(employee.LastWorkingDay);
      normalized.status = 'resigned';

      // إذا كان التاريخ المحدد هو تاريخ الاستقالة أو بعده، أضف الحضور التلقائي
      const resignationDate = new Date(employee.ResignationDate);
      if (selectedDateObj >= resignationDate) {
        normalized.defaultAttendance = 'استقالة';
        normalized.defaultNotes = `تم تقديم استقالة بتاريخ ${resignationDate.toLocaleDateString('ar-EG')}`;
        normalized.isResigned = true;
      }
    }

    if (employee.TransferDate) {
      normalized.transferDate = formatDate(employee.TransferDate);
      normalized.destinationDepartment = employee.NewDepartment;
      normalized.status = 'transferred';

      // إذا كان التاريخ المحدد هو تاريخ النقل أو بعده، أضف الحضور التلقائي
      const transferDate = new Date(employee.TransferDate);
      if (selectedDateObj >= transferDate) {
        normalized.defaultAttendance = 'نقل';
        normalized.defaultNotes = `تم نقله إلى ${employee.NewDepartment || 'قسم آخر'} بتاريخ ${transferDate.toLocaleDateString('ar-EG')}`;
        normalized.isTransferred = true;
      }
    }

    return normalized;
  });

  return NextResponse.json({
    success: true,
    data: employees
  });
}

// إنشاء موظف جديد
async function createEmployee(pool, data) {

  return NextResponse.json({
    success: false,
    error: 'إنشاء موظف جديد غير مدعوم حالياً'
  }, { status: 501 });
}

// تحديث بيانات موظف
async function updateEmployee(pool, data) {

  return NextResponse.json({
    success: false,
    error: 'تحديث بيانات الموظف غير مدعوم حالياً'
  }, { status: 501 });
}
