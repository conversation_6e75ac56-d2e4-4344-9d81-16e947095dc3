'use client';

import { useState, useEffect } from 'react';

export default function EditCostForm({ cost, onSave, onCancel }) {
  const [formData, setFormData] = useState({
    totalAmount: '',
    itemsCount: '',
    notes: '',
    isAttachment: false
  });
  const [loading, setLoading] = useState(false);

  const months = [
    { value: 1, label: 'يناير' },
    { value: 2, label: 'فبراير' },
    { value: 3, label: 'مارس' },
    { value: 4, label: 'أبريل' },
    { value: 5, label: 'مايو' },
    { value: 6, label: 'يونيو' },
    { value: 7, label: 'يوليو' },
    { value: 8, label: 'أغسطس' },
    { value: 9, label: 'سبتمبر' },
    { value: 10, label: 'أكتوبر' },
    { value: 11, label: 'نوفمبر' },
    { value: 12, label: 'ديسمبر' }
  ];

  const costTypeNames = {
    'carscost': 'تكاليف السيارات',
    'housingcost': 'تكاليف الشقق',
    '3amala': 'تكاليف العمالة المؤقتة'
  };

  useEffect(() => {
    if (cost) {
      setFormData({
        totalAmount: cost.TotalAmount?.toString() || '',
        itemsCount: cost.ItemsCount?.toString() || '',
        notes: cost.Notes || '',
        isAttachment: cost.details?.isAttachment || false
      });
    }
  }, [cost]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const calculateAverageCost = () => {
    const total = parseFloat(formData.totalAmount) || 0;
    const count = parseInt(formData.itemsCount) || 1;
    return count > 0 ? (total / count).toFixed(2) : '0.00';
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.totalAmount || !formData.itemsCount) {
      alert('يجب إدخال إجمالي التكلفة وعدد العناصر');
      return;
    }

    setLoading(true);
    try {
      const dataToSave = {
        totalAmount: parseFloat(formData.totalAmount),
        itemsCount: parseInt(formData.itemsCount),
        notes: formData.notes,
        isAttachment: cost.CostType === 'housingcost' ? formData.isAttachment : false,
        details: {
          averageCostPerItem: calculateAverageCost(),
          updatedBy: 'النظام',
          timestamp: new Date().toISOString(),
          isAttachment: cost.CostType === 'housingcost' ? formData.isAttachment : false
        }
      };

      await onSave(dataToSave);
    } catch (error) {
      alert('حدث خطأ في تحديث البيانات');
    } finally {
      setLoading(false);
    }
  };

  if (!cost) return null;

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-800">تعديل التكاليف</h2>
          <p className="text-gray-600">
            {costTypeNames[cost.CostType]} - {months.find(m => m.value === cost.Month)?.label} {cost.Year}
          </p>
        </div>
        <button
          onClick={onCancel}
          className="text-gray-400 hover:text-gray-600 text-2xl"
        >
          ✕
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* معلومات أساسية */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-semibold text-gray-800 mb-3">المعلومات الأساسية</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                نوع التكلفة
              </label>
              <input
                type="text"
                value={costTypeNames[cost.CostType]}
                disabled
                className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100 text-gray-600"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الشهر والسنة
              </label>
              <input
                type="text"
                value={`${months.find(m => m.value === cost.Month)?.label} ${cost.Year}`}
                disabled
                className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100 text-gray-600"
              />
            </div>
          </div>
        </div>

        {/* التكاليف */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              إجمالي التكلفة (جنيه مصري) *
            </label>
            <input
              type="number"
              name="totalAmount"
              value={formData.totalAmount}
              onChange={handleInputChange}
              step="0.01"
              min="0"
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              placeholder="أدخل إجمالي التكلفة"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              عدد العناصر *
            </label>
            <input
              type="number"
              name="itemsCount"
              value={formData.itemsCount}
              onChange={handleInputChange}
              min="1"
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              placeholder="أدخل عدد العناصر"
            />
          </div>
        </div>

        {/* متوسط التكلفة */}
        {formData.totalAmount && formData.itemsCount && (
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="flex items-center gap-2">
              <span className="text-blue-600 font-medium">متوسط التكلفة للعنصر الواحد:</span>
              <span className="text-blue-800 font-bold">
                {formatCurrency(calculateAverageCost())}
              </span>
            </div>
          </div>
        )}

        {/* خيار الملحق للشقق */}
        {cost.CostType === 'housingcost' && (
          <div className="bg-orange-50 p-4 rounded-lg">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                name="isAttachment"
                checked={formData.isAttachment}
                onChange={handleInputChange}
                className="rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
              <span className="text-orange-800 font-medium">هذا طلب إصدار ملحق</span>
            </label>
            <p className="text-xs text-orange-600 mt-1">
              سيتم حفظ الملف باسم: {cost.Month}-{cost.Year}{formData.isAttachment ? '-ملحق' : ''}.pdf
            </p>
          </div>
        )}

        {/* الملاحظات */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            ملاحظات إضافية
          </label>
          <textarea
            name="notes"
            value={formData.notes}
            onChange={handleInputChange}
            rows="3"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
            placeholder="أدخل أي ملاحظات إضافية..."
          />
        </div>

        {/* أزرار الإجراءات */}
        <div className="flex gap-4 pt-4 border-t">
          <button
            type="submit"
            disabled={loading}
            className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
          >
            {loading ? '🔄 جاري التحديث...' : '💾 حفظ التعديلات'}
          </button>
          <button
            type="button"
            onClick={onCancel}
            className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-medium"
          >
            إلغاء
          </button>
        </div>
      </form>
    </div>
  );
}
