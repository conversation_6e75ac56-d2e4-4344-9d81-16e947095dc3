import { NextResponse } from 'next/server';
import { getConnection } from '@/lib/db';
import sql from 'mssql';

export async function GET() {
  try {

    const pool = await getConnection();

    // أولاً: التحقق من وجود أعمدة المديرين المتعددة وإضافتها إذا لم تكن موجودة
    try {
      await pool.request().query(`
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Employees' AND COLUMN_NAME = 'DirectManager1')
        BEGIN
          ALTER TABLE Employees ADD DirectManager1 NVARCHAR(20)
        END

        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Employees' AND COLUMN_NAME = 'DirectManager2')
        BEGIN
          ALTER TABLE Employees ADD DirectManager2 NVARCHAR(20)
        END

        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Employees' AND COLUMN_NAME = 'DirectManager3')
        BEGIN
          ALTER TABLE Employees ADD DirectManager3 NVARCHAR(20)
        END

        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Employees' AND COLUMN_NAME = 'DirectManager4')
        BEGIN
          ALTER TABLE Employees ADD DirectManager4 NVARCHAR(20)
        END
      `);

    } catch (error) {

    }

    // جلب جميع الموظفين النشطين مع معلومات المديرين المتعددة
    const employeesQuery = `
      SELECT
        e.EmployeeCode,
        e.EmployeeName,
        e.JobTitle,
        e.Department,
        e.direct as DirectManager,
        e.DirectManager1,
        e.DirectManager2,
        e.DirectManager3,
        e.DirectManager4,
        e.CurrentStatus,
        e.HireDate,
        e.Governorate,
        e.Mobile,
        e.email
      FROM Employees e
      WHERE e.CurrentStatus IN (N'نشط', N'ساري', N'سارى', N'active', N'Active')
        AND e.EmployeeName IS NOT NULL
        AND e.EmployeeName != ''
      ORDER BY
        CASE
          WHEN e.JobTitle LIKE '%مدير المنطقة%' OR e.JobTitle LIKE '%Regional Manager%' THEN 1
          WHEN e.JobTitle LIKE '%مدير عام%' OR e.JobTitle LIKE '%General Manager%' THEN 2
          WHEN e.JobTitle LIKE '%مدير%' OR e.JobTitle LIKE '%Manager%' THEN 3
          WHEN e.JobTitle LIKE '%رئيس%' OR e.JobTitle LIKE '%Head%' THEN 4
          ELSE 5
        END,
        e.EmployeeName
    `;

    const result = await pool.request().query(employeesQuery);
    const employees = result.recordset;

    // استخراج الأقسام الفريدة
    const departments = [...new Set(employees.map(emp => emp.Department).filter(dept => dept))];

    // بناء الهيكل التنظيمي المحسن
    const organizationTree = buildEnhancedOrganizationTree(employees);

    // إحصائيات الهيكل
    const stats = {
      totalEmployees: employees.length,
      totalDepartments: departments.length,
      totalManagers: employees.filter(emp => emp.JobTitle.includes('مدير')).length,
      maxLevels: calculateMaxLevels(organizationTree)
    };

    return NextResponse.json({
      success: true,
      employees,
      departments,
      organizationTree,
      stats,
      message: `تم جلب ${employees.length} موظف بنجاح`
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message,
      employees: [],
      departments: [],
      organizationTree: [],
      stats: {}
    }, { status: 500 });
  }
}

// بناء الهيكل التنظيمي المحسن باستخدام المديرين المتعددة
function buildEnhancedOrganizationTree(employees) {

  // إنشاء خريطة للموظفين
  const employeeMap = new Map();
  employees.forEach(emp => {
    employeeMap.set(emp.EmployeeCode, {
      ...emp,
      children: [],
      level: 0,
      managerChain: []
    });
  });

  // تحديد السلسلة الإدارية لكل موظف والمستوى الصحيح
  employees.forEach(emp => {
    const employee = employeeMap.get(emp.EmployeeCode);
    const managerChain = [];

    // بناء السلسلة الإدارية من الأعلى إلى الأسفل
    if (emp.DirectManager4) managerChain.push(emp.DirectManager4); // مدير المنطقة
    if (emp.DirectManager3) managerChain.push(emp.DirectManager3); // المستوى الثاني
    if (emp.DirectManager2) managerChain.push(emp.DirectManager2); // المستوى الثالث
    if (emp.DirectManager1) managerChain.push(emp.DirectManager1); // المدير المباشر

    employee.managerChain = managerChain;
    employee.level = managerChain.length;
    employee.immediateManager = managerChain[managerChain.length - 1] || null;

    // تحديد المستوى بناءً على السلسلة الإدارية
    if (managerChain.length === 0) {
      employee.level = 0; // مدير المنطقة
    } else if (managerChain.length === 1) {
      employee.level = 1; // رؤساء الأقسام الرئيسية
    } else if (managerChain.length === 2) {
      employee.level = 2; // مديري الأقسام الفرعية
    } else if (managerChain.length === 3) {
      employee.level = 3; // رؤساء الوحدات
    } else {
      employee.level = 4; // الموظفين العاديين
    }
  });

  // بناء العلاقات الهرمية
  employees.forEach(emp => {
    const employee = employeeMap.get(emp.EmployeeCode);

    if (employee.immediateManager) {
      const manager = employeeMap.get(employee.immediateManager);
      if (manager) {
        // التأكد من عدم إضافة نفس الطفل مرتين
        if (!manager.children.find(child => child.EmployeeCode === employee.EmployeeCode)) {
          manager.children.push(employee);
        }
      }
    }
  });

  // العثور على مدير المنطقة (العقدة الجذرية الوحيدة)
  const areaManager = employees.find(emp =>
    emp.EmployeeCode === '1412' ||
    emp.JobTitle.includes('مدير المنطقة') ||
    (!emp.DirectManager1 && !emp.DirectManager2 && !emp.DirectManager3 && !emp.DirectManager4)
  );

  if (!areaManager) {

    return [];
  }

  const rootNode = employeeMap.get(areaManager.EmployeeCode);
  if (!rootNode) {

    return [];
  }

  // ترتيب الأطفال في كل مستوى حسب الأهمية
  const sortChildren = (node) => {
    if (node.children && node.children.length > 0) {
      node.children.sort((a, b) => {
        // ترتيب خاص لرؤساء الأقسام الرئيسية
        const getDepartmentPriority = (emp) => {
          if (emp.EmployeeCode === '5632') return 1; // محمد عبد الحكم - السلامة والصحة المهنية
          if (emp.EmployeeCode === '5754') return 2; // محمود حلمى - المساحة
          if (emp.EmployeeCode === '1414') return 3; // ابراهيم سيد - المكتب الفنى
          if (emp.EmployeeCode === '1428') return 4; // مدير آخر
          if (emp.JobTitle.includes('رئيس')) return 5;
          if (emp.JobTitle.includes('مدير')) return 6;
          return 7;
        };

        const priorityA = getDepartmentPriority(a);
        const priorityB = getDepartmentPriority(b);

        if (priorityA !== priorityB) {
          return priorityA - priorityB;
        }

        // ترتيب ثانوي بالاسم
        return a.EmployeeName.localeCompare(b.EmployeeName, 'ar');
      });

      // ترتيب الأطفال بشكل متكرر
      node.children.forEach(child => sortChildren(child));
    }
  };

  sortChildren(rootNode);

  return [rootNode]; // إرجاع مدير المنطقة فقط كعقدة جذرية
}

// حساب أقصى عدد مستويات في الهيكل
function calculateMaxLevels(tree) {
  let maxLevel = 0;

  function traverse(node, level = 0) {
    maxLevel = Math.max(maxLevel, level);
    if (node.children) {
      node.children.forEach(child => traverse(child, level + 1));
    }
  }

  tree.forEach(root => traverse(root));
  return maxLevel + 1;
}

// API لتحديث بيانات المديرين
export async function POST(request) {
  try {
    const { employeeCode, managers } = await request.json();

    const pool = await getConnection();

    const updateQuery = `
      UPDATE Employees
      SET
        DirectManager1 = @manager1,
        DirectManager2 = @manager2,
        DirectManager3 = @manager3,
        DirectManager4 = @manager4,
        UpdatedAt = GETDATE()
      WHERE EmployeeCode = @employeeCode
    `;

    await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .input('manager1', sql.NVarChar, managers.manager1 || null)
      .input('manager2', sql.NVarChar, managers.manager2 || null)
      .input('manager3', sql.NVarChar, managers.manager3 || null)
      .input('manager4', sql.NVarChar, managers.manager4 || null)
      .query(updateQuery);

    return NextResponse.json({
      success: true,
      message: 'تم تحديث بيانات المديرين بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
