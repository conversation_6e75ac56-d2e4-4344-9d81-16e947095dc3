import { NextResponse } from 'next/server';
import ExcelJS from 'exceljs';

export async function GET() {
  try {

    // إنشاء مصنف Excel جديد
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('الموظفين');

    // تعريف الأعمدة مع العناوين العربية (محدث للتصميم الجديد)
    const columns = [
      { header: 'كود الموظف *', key: 'employeeCode', width: 15 },
      { header: 'الاسم الكامل *', key: 'fullName', width: 25 },
      { header: 'المسمى الوظيفي *', key: 'jobTitle', width: 20 },
      { header: 'القسم *', key: 'department', width: 20 },
      { header: 'المدير المباشر *', key: 'directManager', width: 20 },
      { header: 'الرقم القومي *', key: 'nationalId', width: 15 },
      { header: 'تاريخ الميلاد *', key: 'birthDate', width: 15 },
      { header: 'النوع *', key: 'gender', width: 10 },
      { header: 'المحافظة *', key: 'governorate', width: 15 },
      { header: 'المنطقة', key: 'area', width: 15 },
      { header: 'الحالة الاجتماعية *', key: 'maritalStatus', width: 15 },
      { header: 'رقم الهاتف', key: 'mobile', width: 15 },
      { header: 'البريد الإلكتروني', key: 'email', width: 25 },
      { header: 'رقم الطوارئ', key: 'emergencyNumber', width: 15 },
      { header: 'صلة القرابة', key: 'kinship', width: 15 },
      { header: 'تاريخ التعيين *', key: 'hireDate', width: 15 },
      { header: 'تاريخ التواجد *', key: 'joinDate', width: 15 },
      { header: 'حالة الموظف *', key: 'employeeStatus', width: 15 },
      { header: 'الخدمة العسكرية *', key: 'militaryService', width: 15 },
      { header: 'مغترب *', key: 'isResident', width: 10 },
      // تم إزالة حقول السكن والمواصلات - سيتم إدارتها من جداول المستفيدين
      { header: 'المؤهل', key: 'education', width: 15 },
      { header: 'الجامعة', key: 'university', width: 20 },
      { header: 'التخصص', key: 'specialization', width: 20 },
      { header: 'التقدير', key: 'grade', width: 10 },
      { header: 'دفعة التخرج', key: 'graduationYear', width: 15 },
      // حقول التأمين الجديدة
      { header: 'التأمين الاجتماعي', key: 'socialInsurance', width: 15 },
      { header: 'الرقم التأميني', key: 'socialInsuranceNumber', width: 15 },
      { header: 'تاريخ التأمين', key: 'socialInsuranceDate', width: 15 },
      { header: 'التأمين الطبي', key: 'medicalInsurance', width: 15 },
      { header: 'رقم التأمين الطبي', key: 'medicalInsuranceNumber', width: 15 }
    ];

    // إضافة الأعمدة إلى ورقة العمل
    worksheet.columns = columns;

    // تنسيق صف العناوين
    const headerRow = worksheet.getRow(1);
    headerRow.height = 25;
    headerRow.font = { bold: true, size: 12, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4472C4' }
    };
    headerRow.alignment = { vertical: 'middle', horizontal: 'center' };

    // إضافة حدود للعناوين
    headerRow.eachCell((cell) => {
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    });

    // إضافة صف مثال مع البيانات النموذجية (محدث للتصميم الجديد)
    const exampleRow = worksheet.addRow({
      employeeCode: 'EMP001',
      fullName: 'أحمد محمد علي',
      jobTitle: 'مهندس',
      department: 'الكهرباء',
      directManager: 'محمد أحمد',
      nationalId: '12345678901234',
      birthDate: '1990-01-15',
      gender: 'ذكر',
      governorate: 'القاهرة',
      area: 'مدينة نصر',
      maritalStatus: 'متزوج',
      mobile: '01234567890',
      email: '<EMAIL>',
      emergencyNumber: '01987654321',
      kinship: 'أخ',
      hireDate: '2020-01-01',
      joinDate: '2020-01-15',
      employeeStatus: 'ساري',
      militaryService: 'مؤدى',
      isResident: 'نعم',
      // تم إزالة حقول السكن والمواصلات
      education: 'بكالوريوس',
      university: 'جامعة القاهرة',
      specialization: 'هندسة كهربائية',
      grade: 'جيد جداً',
      graduationYear: '2018',
      // حقول التأمين الجديدة
      socialInsurance: 'مؤمن',
      socialInsuranceNumber: '123456789',
      socialInsuranceDate: '2020-01-01',
      medicalInsurance: 'مؤمن',
      medicalInsuranceNumber: 'MED123456'
    });

    // تنسيق صف المثال
    exampleRow.font = { italic: true, color: { argb: '666666' } };
    exampleRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'F2F2F2' }
    };

    // إضافة حدود لصف المثال
    exampleRow.eachCell((cell) => {
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
      cell.alignment = { vertical: 'middle', horizontal: 'center' };
    });

    // إضافة ورقة عمل للتعليمات
    const instructionsSheet = workbook.addWorksheet('التعليمات');
    
    // إضافة التعليمات (محدث للتصميم الجديد)
    const instructions = [
      ['تعليمات استخدام النموذج المحدث:', ''],
      ['', ''],
      ['1. الحقول المطلوبة:', 'الحقول المميزة بعلامة (*) مطلوبة'],
      ['2. تنسيق التواريخ:', 'استخدم تنسيق YYYY-MM-DD (مثل: 2020-01-15)'],
      ['3. النوع:', 'ذكر أو أنثى'],
      ['4. الحالة الاجتماعية:', 'أعزب، متزوج، مطلق، أرمل'],
      ['5. حالة الموظف:', 'ساري، منقول، مستقيل'],
      ['6. الخدمة العسكرية:', 'مؤدى، معفى، مؤجل'],
      ['7. مغترب:', 'نعم أو لا'],
      ['8. التأمين الاجتماعي/الطبي:', 'مؤمن، غير مؤمن، أو اتركه فارغ'],
      ['', ''],
      ['تغييرات مهمة في النموذج الجديد:', ''],
      ['• تم إزالة حقول السكن والمواصلات', 'سيتم إدارتها من جداول المستفيدين'],
      ['• تم إضافة حقول التأمين الجديدة', 'التأمين الاجتماعي والطبي'],
      ['• تم تحديث أسماء الحقول', 'لتتماشى مع التصميم الجديد'],
      ['', ''],
      ['ملاحظات مهمة:', ''],
      ['• تأكد من صحة الرقم القومي (14 رقم)', ''],
      ['• تأكد من صحة أرقام الهواتف', ''],
      ['• تأكد من صحة عناوين البريد الإلكتروني', ''],
      ['• لا تترك الحقول المطلوبة فارغة', ''],
      ['• احذف صف المثال قبل إدخال البيانات الفعلية', ''],
      ['• بيانات الشقق والسيارات ستُدار منفصلة', '']
    ];

    instructions.forEach((instruction, index) => {
      const row = instructionsSheet.addRow(instruction);
      if (index === 0) {
        row.font = { bold: true, size: 14, color: { argb: '4472C4' } };
      } else if (instruction[0].includes('ملاحظات مهمة') || instruction[0].match(/^\d+\./)) {
        row.font = { bold: true };
      }
    });

    // تنسيق ورقة التعليمات
    instructionsSheet.columns = [
      { width: 30 },
      { width: 50 }
    ];

    // تجميد الصف الأول في ورقة الموظفين
    worksheet.views = [
      { state: 'frozen', ySplit: 1 }
    ];

    // تحويل المصنف إلى buffer
    const buffer = await workbook.xlsx.writeBuffer();

    // إرجاع الملف
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': 'attachment; filename="employee_template.xlsx"',
        'Content-Length': buffer.length.toString()
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'خطأ في إنشاء نموذج Excel',
      error: error.message
    }, { status: 500 });
  }
}
