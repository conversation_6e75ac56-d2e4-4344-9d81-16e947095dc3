'use client';

import MainLayout from '@/components/MainLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { FiArrowLeft, FiDownload, FiMapPin, FiPrinter, FiRefreshCw, FiSave, FiUser } from 'react-icons/fi';

const MissionRequestPage = () => {
  const router = useRouter();
  const { isArabic } = useLanguage();
  const [loading, setLoading] = useState(false);
  const [employeeSearchResults, setEmployeeSearchResults] = useState([]);
  const [showEmployeeSearch, setShowEmployeeSearch] = useState(false);
  const [formData, setFormData] = useState({
    employeeName: '',
    employeeId: '',
    department: '',
    jobTitle: '',
    missionType: '',
    missionPurpose: '',
    destination: '',
    startDate: '',
    endDate: '',
    startTime: '',
    endTime: '',
    totalDays: '',
    purpose: '',
    transportMethod: '',
    accommodationNeeded: false,
    advancePayment: '',
    expectedExpenses: '',
    accompaniedBy: '',
    emergencyContact: '',
    emergencyPhone: '',
    notes: ''
  });

  // أنواع المأموريات
  const missionTypes = [
    { value: 'full-day', label: isArabic ? 'مأمورية يوم كامل' : 'Full Day Mission' },
    { value: 'half-day', label: isArabic ? 'مأمورية نصف يوم' : 'Half Day Mission' },
    { value: 'late-attendance', label: isArabic ? 'مأمورية حضور متأخر' : 'Late Attendance Mission' }
  ];

  // أغراض المأموريات
  const missionPurposes = [
    { value: 'site-needs', label: isArabic ? 'حاجة الموقع' : 'Site Needs' },
    { value: 'document-delivery', label: isArabic ? 'تسليم أوراق للإدارة' : 'Document Delivery to Management' },
    { value: 'check-receipt', label: isArabic ? 'استلام شيك' : 'Check Receipt' },
    { value: 'check-payment', label: isArabic ? 'صرف شيك' : 'Check Payment' },
    { value: 'apartment-rent', label: isArabic ? 'إيجار شقة' : 'Apartment Rent' },
    { value: 'rent-payment', label: isArabic ? 'دفع إيجارات' : 'Rent Payment' },
    { value: 'apartment-delivery', label: isArabic ? 'تسليم شقة' : 'Apartment Delivery' },
    { value: 'other', label: isArabic ? 'أخرى' : 'Other' }
  ];

  // وسائل النقل
  const transportMethods = [
    { value: 'company-car', label: isArabic ? 'سيارة الشركة' : 'Company Car' },
    { value: 'employee-transport', label: isArabic ? 'انتقالات بمعرفة الموظف' : 'Employee Transport' }
  ];

  // جلب بيانات الموظف المسجل
  useEffect(() => {
    const userInfo = localStorage.getItem('userInfo');
    if (userInfo) {
      const user = JSON.parse(userInfo);
      setFormData(prev => ({
        ...prev,
        employeeName: user.EmployeeName || user.fullName || user.username,
        employeeId: user.EmployeeCode || user.employeeId || '',
        department: user.department || '',
        jobTitle: user.jobTitle || ''
      }));
    }
  }, []);

  // حساب تاريخ النهاية وعدد الأيام
  useEffect(() => {
    if (formData.startDate && formData.totalDays) {
      // تحويل التاريخ من dd/mm/yyyy إلى Date object
      const dateParts = formData.startDate.split('/');
      if (dateParts.length === 3) {
        const startDate = new Date(dateParts[2], dateParts[1] - 1, dateParts[0]);
        const endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + parseInt(formData.totalDays) - 1);

        // تحويل تاريخ النهاية إلى dd/mm/yyyy
        const endDay = endDate.getDate().toString().padStart(2, '0');
        const endMonth = (endDate.getMonth() + 1).toString().padStart(2, '0');
        const endYear = endDate.getFullYear();
        const formattedEndDate = `${endDay}/${endMonth}/${endYear}`;

        setFormData(prev => ({ ...prev, endDate: formattedEndDate }));
      }
    }
  }, [formData.startDate, formData.totalDays]);

  // البحث عن الموظفين من قاعدة البيانات
  const searchEmployees = async (searchTerm) => {
    if (!searchTerm || searchTerm.length < 2) {
      setEmployeeSearchResults([]);
      setShowEmployeeSearch(false);
      return;
    }

    try {
      const response = await fetch('/api/employee-live-search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          searchTerm: searchTerm,
          limit: 10
        })
      });

      const result = await response.json();
      if (result.success) {
        const employees = result.data || [];
        // تحويل البيانات للتنسيق المطلوب
        const formattedEmployees = employees.map(emp => ({
          employeeId: emp.EmployeeCode || emp.employeeCode,
          fullName: emp.EmployeeName || emp.employeeName,
          department: emp.Department || emp.department,
          jobTitle: emp.JobTitle || emp.jobTitle,
          nationalId: emp.NationalID || emp.nationalId || ''
        }));

        setEmployeeSearchResults(formattedEmployees);
        setShowEmployeeSearch(formattedEmployees.length > 0);
      } else {

        setEmployeeSearchResults([]);
        setShowEmployeeSearch(false);
      }
    } catch (error) {

      setEmployeeSearchResults([]);
      setShowEmployeeSearch(false);
    }
  };

  // اختيار موظف من نتائج البحث
  const selectEmployee = (employee) => {
    setFormData(prev => ({
      ...prev,
      employeeId: employee.employeeId,
      employeeName: employee.fullName,
      department: employee.department,
      jobTitle: employee.jobTitle
    }));
    setShowEmployeeSearch(false);
    setEmployeeSearchResults([]);
  };

  // دالة إفراغ الحقول
  const clearForm = () => {
    setFormData({
      employeeName: '',
      employeeId: '',
      department: '',
      jobTitle: '',
      missionType: '',
      missionPurpose: '',
      destination: '',
      startDate: '',
      endDate: '',
      startTime: '',
      endTime: '',
      totalDays: '',
      purpose: '',
      transportMethod: '',
      accommodationNeeded: false,
      advancePayment: '',
      expectedExpenses: '',
      accompaniedBy: '',
      emergencyContact: '',
      emergencyPhone: '',
      notes: ''
    });
    setShowEmployeeSearch(false);
    setEmployeeSearchResults([]);
  };

  // دالة تنسيق إدخال التاريخ
  const formatDateInput = (value) => {
    // إزالة جميع الأحرف غير الرقمية
    const numbers = value.replace(/\D/g, '');

    // تطبيق التنسيق dd/mm/yyyy
    if (numbers.length <= 2) {
      return numbers;
    } else if (numbers.length <= 4) {
      return numbers.slice(0, 2) + '/' + numbers.slice(2);
    } else if (numbers.length <= 8) {
      return numbers.slice(0, 2) + '/' + numbers.slice(2, 4) + '/' + numbers.slice(4);
    }
    return numbers.slice(0, 2) + '/' + numbers.slice(2, 4) + '/' + numbers.slice(4, 8);
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    // تنسيق التاريخ تلقائياً
    if (name === 'startDate' || name === 'endDate') {
      const formattedDate = formatDateInput(value);
      setFormData(prev => ({ ...prev, [name]: formattedDate }));
      return;
    }

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // البحث عن الموظفين عند كتابة كود الموظف
    if (name === 'employeeId') {
      searchEmployees(value);
      if (value !== formData.employeeId) {
        setFormData(prev => ({
          ...prev,
          employeeName: '',
          department: '',
          jobTitle: ''
        }));
      }
    }

    // حساب تاريخ النهاية عند تغيير عدد الأيام
    if (name === 'totalDays' && formData.startDate && value) {
      const dateParts = formData.startDate.split('/');
      if (dateParts.length === 3) {
        const startDate = new Date(dateParts[2], dateParts[1] - 1, dateParts[0]);
        const endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + parseInt(value) - 1);

        const endDay = endDate.getDate().toString().padStart(2, '0');
        const endMonth = (endDate.getMonth() + 1).toString().padStart(2, '0');
        const endYear = endDate.getFullYear();
        const formattedEndDate = `${endDay}/${endMonth}/${endYear}`;

        setFormData(prev => ({ ...prev, endDate: formattedEndDate }));
      }
    }
  };

  // دالة التحقق من الإجراءات الموجودة والقيود
  const checkExistingActions = async () => {
    try {
      const response = await fetch('/api/check-employee-actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          employeeCode: formData.employeeId,
          startDate: formData.startDate,
          endDate: formData.endDate,
          actionType: 'mission'
        })
      });

      const result = await response.json();
      return result;
    } catch (error) {

      return { success: false, error: 'خطأ في التحقق من الإجراءات' };
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // التحقق من الحقول المطلوبة
    if (!formData.employeeId || !formData.destination || !formData.startDate) {
      alert(isArabic ? 'يرجى ملء جميع الحقول المطلوبة' : 'Please fill all required fields');
      return;
    }

    setLoading(true);

    // التحقق من الإجراءات الموجودة والقيود
    const checkResult = await checkExistingActions();

    if (!checkResult.success) {
      alert(checkResult.error || 'خطأ في التحقق من الإجراءات');
      setLoading(false);
      return;
    }

    // إذا كان هناك تضارب
    if (checkResult.hasConflict) {
      const conflict = checkResult.conflictDetails;

      // رسالة التحذير
      const conflictMessage = `⚠️ تحذير: يوجد إجراء "${conflict.actionType}" مسجل بالفعل للموظف!\n\n` +
        `الموظف: ${formData.employeeName} (${formData.employeeId})\n` +
        `الإجراء الموجود: ${conflict.actionType}\n` +
        `الفترة: من ${conflict.startDate} إلى ${conflict.endDate}\n` +
        `الحالة: ${conflict.status}\n\n`;

      // إذا كان الإجراء معتمد، لا يمكن الاستبدال
      if (conflict.status === 'معتمد' || conflict.status === 'approved') {
        alert(conflictMessage + 'لا يمكن استبدال الإجراءات المعتمدة. يرجى اختيار فترة زمنية أخرى.');
        setLoading(false);
        return;
      }

      // إذا كان الإجراء قيد المراجعة، اعرض خيارات الاستبدال
      const userChoice = confirm(conflictMessage +
        'هل تريد استبدال الإجراء الموجود بالإجراء الجديد؟\n\n' +
        'اضغط "موافق" للاستبدال أو "إلغاء" للعودة وتعديل البيانات.');

      if (!userChoice) {
        setLoading(false);
        return;
      }

      // تنفيذ الاستبدال الفعلي

      try {
        const replaceResponse = await fetch('/api/replace-employee-action', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            conflictId: conflict.id,
            conflictSource: conflict.source,
            newRequestData: {
              requestType: 'mission',
              employeeCode: formData.employeeId,
              employeeName: formData.employeeName,
              startDate: formData.startDate,
              endDate: formData.endDate,
              destination: formData.destination,
              notes: formData.notes || ''
            }
          })
        });

        const replaceResult = await replaceResponse.json();

        if (replaceResult.success) {
          alert('تم استبدال الإجراء وتقديم طلب المأمورية بنجاح!');
          clearForm();
          setLoading(false);
          return;
        } else {
          alert('خطأ في الاستبدال: ' + replaceResult.error);
          setLoading(false);
          return;
        }
      } catch (replaceError) {

        alert('خطأ في تنفيذ الاستبدال');
        setLoading(false);
        return;
      }
    }

    // إذا كان هناك قيود (استقالة، نقل، تاريخ انضمام)
    if (checkResult.hasRestriction) {
      alert(checkResult.restrictionMessage);
      setLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/paper-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'create',
          requestType: 'mission',
          ...formData
        }),
      });

      const result = await response.json();

      if (response.ok) {
        alert(isArabic ? 'تم تقديم طلب المأمورية بنجاح!' : 'Mission request submitted successfully!');
        // إفراغ الحقول تحسباً لتسجيل جديد
        clearForm();
      } else {
        alert(result.message || (isArabic ? 'خطأ في تقديم الطلب' : 'Error submitting request'));
      }
    } catch (error) {

      alert(isArabic ? 'خطأ في الاتصال بالخادم' : 'Server connection error');
    } finally {
      setLoading(false);
    }
  };

  const downloadForm = async () => {
    try {
      const response = await fetch('/api/requests/download-form?type=mission');
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'نموذج_طلب_المأمورية.xlsx';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {

    }
  };

  // تحويل اللوجو إلى base64
  const getLogoBase64 = async () => {
    try {
      const response = await fetch('/logo.png');
      const blob = await response.blob();
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result.split(',')[1]);
        reader.readAsDataURL(blob);
      });
    } catch (error) {

      return '';
    }
  };

  // طباعة نموذج المأمورية المملوء
  const printFilledForm = async () => {
    // تحميل اللوجو أولاً قبل فتح نافذة الطباعة
    const logoBase64 = await getLogoBase64();

    // طباعة قيمة وسيلة النقل للتأكد

    // انتظار إضافي للتأكد من تحميل اللوجو
    await new Promise(resolve => setTimeout(resolve, 1000));

    // إضافة معرف فريد لإجبار إعادة التحميل
    const uniqueId = Date.now();
    const printWindow = window.open('', '_blank');

    const printContent = `
      <!DOCTYPE html>
      <html dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>نموذج طلب مأمورية - ${formData.employeeName}</title>
        <style>
          @page {
            size: A4;
            margin: 15mm;
          }
          body {
            font-family: 'Arial', sans-serif;
            font-size: 14px;
            line-height: 1.4;
            color: #000;
            margin: 0;
            padding: 0;
            direction: rtl;
            background: white;
          }

          .header {
            display: table;
            width: 100%;
            border: 2px solid #000;
            margin-bottom: 15px;
            border-collapse: collapse;
          }
          .header-row {
            display: table-row;
          }
          .logo-section {
            display: table-cell;
            width: 33.33%;
            border-left: 1px solid #000;
            text-align: center;
            vertical-align: middle;
            padding: 10px;
          }
          .logo-img {
            max-width: 100px;
            max-height: 70px;
            object-fit: contain;
          }
          .form-code {
            display: table-cell;
            width: 33.33%;
            border-left: 1px solid #000;
            text-align: center;
            vertical-align: middle;
            padding: 10px;
          }
          .form-code-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
          }
          .form-code-number {
            font-size: 12px;
            color: #666;
          }
          .company-info {
            display: table-cell;
            width: 33.33%;
            text-align: center;
            vertical-align: middle;
            padding: 10px;
          }
          .company-name-ar {
            font-size: 13px;
            font-weight: bold;
            margin-bottom: 3px;
          }
          .company-name-en {
            font-size: 10px;
            color: #666;
          }

          .form-title {
            background-color: #a8c5f0;
            text-align: center;
            padding: 8px;
            font-size: 14px;
            font-weight: bold;
            border: 1px solid #000;
            margin-bottom: 15px;
          }

          .form-row {
            display: flex;
            margin-bottom: 12px;
            align-items: center;
          }
          .field-group {
            display: flex;
            align-items: center;
            margin-left: 30px;
          }
          .field-label {
            font-weight: bold;
            margin-left: 8px;
            min-width: 80px;
          }
          .field-value {
            border-bottom: 1px solid #000;
            min-width: 150px;
            padding: 2px 5px;
            font-weight: normal;
          }

          .mission-section {
            border: 1px solid #000;
            padding: 15px;
            margin: 15px 0;
          }
          .section-title {
            background-color: #a8c5f0;
            text-align: center;
            padding: 5px;
            font-weight: bold;
            margin: -15px -15px 15px -15px;
            border-bottom: 1px solid #000;
          }

          .signatures {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
          }
          .signature-box {
            width: 150px;
            text-align: center;
          }
          .signature-title {
            font-weight: bold;
            margin-bottom: 30px;
            font-size: 10px;
          }
          .signature-line {
            border-bottom: 1px solid #000;
            height: 1px;
            margin-bottom: 20px;
          }

          @media print {
            body { margin: 0; padding: 10px; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="header-row">
            <div class="company-info">
              <div class="company-name-ar">شركة كونكورد للهندسة والمقاولات</div>
              <div class="company-name-en">Concord for Engineering & Contracting</div>
            </div>
            <div class="form-code">
              <div class="form-code-title">طلب مأمورية</div>
              <div class="form-code-number">HR-OP-01-F02</div>
            </div>
            <div class="logo-section">
              ${logoBase64 ?
                `<img src="data:image/png;base64,${logoBase64}" alt="Concord Logo" class="logo-img" />` :
                `<div style="background:#1e40af;color:white;padding:10px;text-align:center;font-weight:bold;font-size:10px;">CONCORD<br>COMPANY</div>`
              }
            </div>
          </div>
        </div>

        <!-- Employee Info Section - مطابق للنموذج الأصلي تماماً -->

        <!-- السطر الأول: التاريخ (يسار) فقط -->
        <div style="margin-bottom: 15px; padding: 8px 0;">
          <div style="text-align: left;">
            <span style="font-weight: bold;">التاريخ/</span>
            <span style="margin-left: 20px;">${new Date().toLocaleDateString('ar-EG', { day: '2-digit', month: '2-digit', year: 'numeric' }).replace(/\//g, ' / ')}</span>
          </div>
        </div>

        <!-- السطر الثاني: الاسم (يمين) والكود الوظيفي (وسط) -->
        <div style="margin-bottom: 15px; padding: 8px 0;">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="text-align: right;">
              <span style="font-weight: bold; font-size: 15px;">الإســـم/</span>
              <span style="margin-right: 20px; font-size: 15px;">${formData.employeeName || 'شريف احمد سليمان'}</span>
            </div>
            <div style="text-align: center; flex: 1;">
              <span style="font-weight: bold; font-size: 15px;">الكود الوظيفى /</span>
              <span style="margin-left: 20px; font-size: 15px;">${formData.employeeId || '5814'}</span>
            </div>
            <div style="width: 200px;"></div>
          </div>
        </div>

        <!-- السطر الثالث: الوظيفة (يمين) فقط -->
        <div style="margin-bottom: 15px; padding: 8px 0;">
          <div style="text-align: right;">
            <span style="font-weight: bold; font-size: 15px;">الوظيفة/</span>
            <span style="margin-right: 20px; font-size: 15px;">${formData.jobTitle || 'مهندس اول كهرباء'}</span>
          </div>
        </div>

        <!-- السطر الرابع: الإدارة/المشروع (يمين) فقط -->
        <div style="margin-bottom: 20px; padding: 8px 0;">
          <div style="text-align: right;">
            <span style="font-weight: bold; font-size: 15px;">الإدارة/المشروع/</span>
            <span style="margin-right: 20px; font-size: 15px;">مجمع مبانى أوجيستا</span>
          </div>
        </div>

        <!-- Mission Details Section -->
        <div style="border: 2px solid #000; margin: 20px 0; padding: 15px;">
          <div style="font-weight: bold; text-align: center; margin-bottom: 15px; font-size: 14px;">
            تفاصيل المأمورية
          </div>

          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <!-- عدد الأيام - أقصى اليسار في الكود = أقصى اليمين في العرض -->
            <div style="display: flex; align-items: center; gap: 10px;">
              <span style="font-weight: bold; font-size: 15px;">عدد الأيام:</span>
              <span style="border-bottom: 2px solid #000; padding: 5px 10px; min-width: 80px; text-align: center; font-size: 15px;">
                ${formData.totalDays || '____'} أيام
              </span>
            </div>
            <!-- من تاريخ - الوسط -->
            <div style="display: flex; align-items: center; gap: 10px;">
              <span style="font-weight: bold; font-size: 15px;">من تاريخ:</span>
              <span style="border-bottom: 2px solid #000; padding: 5px 10px; min-width: 100px; text-align: center; font-size: 15px;">
                ${formData.startDate || '__ / __ / ____'}
              </span>
            </div>
            <!-- إلى تاريخ - أقصى اليمين في الكود = أقصى اليسار في العرض -->
            <div style="display: flex; align-items: center; gap: 10px;">
              <span style="font-weight: bold; font-size: 15px;">إلى تاريخ:</span>
              <span style="border-bottom: 2px solid #000; padding: 5px 10px; min-width: 100px; text-align: center; font-size: 15px;">
                ${formData.endDate || '__ / __ / ____'}
              </span>
            </div>
          </div>

          <div style="margin-bottom: 15px;">
            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
              <span style="font-weight: bold;">مكان المأمورية:</span>
              <span style="border-bottom: 2px solid #000; padding: 5px 10px; flex: 1; text-align: center;">
                ${formData.destination || ''}
              </span>
            </div>
          </div>

          <div style="margin-bottom: 15px;">
            <div style="font-weight: bold; margin-bottom: 10px;">الغرض من المأمورية:</div>
            <div style="border: 2px solid #000; min-height: 80px; padding: 15px; line-height: 1.6;">
              ${formData.purpose || ''}
            </div>
          </div>

          <div style="margin-top: 15px;">
            <div style="display: flex; justify-content: flex-start; align-items: center; gap: 15px; flex-wrap: wrap;">
              <div style="font-weight: bold; margin-left: 10px;">
                وسيلة الانتقال:
              </div>
              <div style="display: flex; align-items: center; gap: 8px;">
                <span style="border: 2px solid #000; width: 15px; height: 15px; display: inline-block; text-align: center; line-height: 11px; font-size: 10px; ${formData.transportMethod === 'company-car' ? 'background: #000; color: white;' : ''}">
                  ${formData.transportMethod === 'company-car' ? '✓' : ''}
                </span>
                <span style="font-size: 12px;">سيارة الشركة</span>
              </div>
              <div style="display: flex; align-items: center; gap: 8px;">
                <span style="border: 2px solid #000; width: 15px; height: 15px; display: inline-block; text-align: center; line-height: 11px; font-size: 10px; ${formData.transportMethod === 'employee-transport' ? 'background: #000; color: white;' : ''}">
                  ${formData.transportMethod === 'employee-transport' ? '✓' : ''}
                </span>
                <span style="font-size: 12px;">انتقالات بمعرفة الموظف</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Signatures Section - مطابق للنموذج الأصلي -->
        <div style="margin-top: 40px;">
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="text-align: center; padding: 20px; width: 50%; vertical-align: top;">
                <div style="font-weight: bold; margin-bottom: 50px;">توقيع الموظف</div>
                <div style="border-bottom: 1px solid #000; width: 200px; margin: 0 auto;"></div>
              </td>
              <td style="text-align: center; padding: 20px; width: 50%; vertical-align: top;">
                <div style="font-weight: bold; margin-bottom: 50px;">اعتماد المدير المباشر</div>
                <div style="border-bottom: 1px solid #000; width: 200px; margin: 0 auto;"></div>
              </td>
            </tr>
            <tr>
              <td style="text-align: center; padding: 20px; width: 50%; vertical-align: top;">
                <div style="font-weight: bold; margin-bottom: 50px;">اعتماد مدير المشروع</div>
                <div style="border-bottom: 1px solid #000; width: 200px; margin: 0 auto;"></div>
              </td>
              <td style="text-align: center; padding: 20px; width: 50%; vertical-align: top;">
                <div style="font-weight: bold; margin-bottom: 50px;">اعتماد جهة المأمورية</div>
                <div style="border-bottom: 1px solid #000; width: 200px; margin: 0 auto;"></div>
              </td>
            </tr>
          </table>
        </div>
      </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();

    // انتظار تحميل المحتوى والصور قبل الطباعة
    printWindow.onload = () => {
      setTimeout(() => {
        printWindow.print();
      }, 500);
    };

    // احتياطي في حالة عدم تشغيل onload
    setTimeout(() => {
      printWindow.print();
    }, 1500);
  };

  const getMissionTypeLabel = (type) => {
    const types = {
      'work': 'مأمورية عمل',
      'training': 'مأمورية تدريب',
      'conference': 'حضور مؤتمر',
      'inspection': 'مأمورية تفتيش',
      'meeting': 'حضور اجتماع',
      'site-visit': 'زيارة موقع',
      'other': 'أخرى'
    };
    return types[type] || '................................';
  };

  const getTransportLabel = (method) => {
    const methods = {
      'company-car': 'سيارة الشركة',
      'personal-car': 'سيارة شخصية',
      'public-transport': 'مواصلات عامة',
      'flight': 'طيران',
      'train': 'قطار',
      'other': 'أخرى'
    };
    return methods[method] || '................................';
  };

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-4xl mx-auto">
          {/* العنوان */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6 pulse-box">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-200 mb-2">
                  {isArabic ? 'طلب مأمورية' : 'Mission Request'}
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  {isArabic ? 'تقديم طلب مأمورية عمل جديد' : 'Submit a new work mission request'}
                </p>
              </div>
              <div className="flex gap-3">
                <button
                  onClick={downloadForm}
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
                >
                  <FiDownload />
                  {isArabic ? 'تحميل النموذج' : 'Download Form'}
                </button>
                <button
                  onClick={printFilledForm}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
                >
                  <FiPrinter />
                  {isArabic ? 'طباعة النموذج' : 'Print Form'}
                </button>
                <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                  <FiMapPin className="text-2xl text-white" />
                </div>
              </div>
            </div>
          </div>

          {/* النموذج */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* بيانات الموظف */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 gentle-animated-box">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                <FiUser className="text-green-600" />
                {isArabic ? 'بيانات الموظف' : 'Employee Information'}
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="relative">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'كود الموظف *' : 'Employee Code *'}
                  </label>
                  <input
                    type="text"
                    name="employeeId"
                    value={formData.employeeId}
                    onChange={handleInputChange}
                    placeholder={isArabic ? 'ادخل كود الموظف للبحث...' : 'Enter employee code to search...'}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-gray-200"
                    autoComplete="off"
                    autoCorrect="off"
                    autoCapitalize="off"
                    spellCheck="false"
                  />

                  {/* نتائج البحث */}
                  {showEmployeeSearch && employeeSearchResults.length > 0 && (
                    <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                      {employeeSearchResults.map((employee, index) => (
                        <div
                          key={index}
                          onClick={() => selectEmployee(employee)}
                          className="p-3 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-200 dark:border-gray-600 last:border-b-0"
                        >
                          <div className="flex justify-between items-center">
                            <div>
                              <div className="font-medium text-gray-900 dark:text-gray-100">
                                {employee.employeeId} - {employee.fullName}
                              </div>
                              <div className="text-sm text-gray-600 dark:text-gray-400">
                                {employee.jobTitle} - {employee.department}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'اسم الموظف *' : 'Employee Name *'}
                  </label>
                  <input
                    type="text"
                    name="employeeName"
                    value={formData.employeeName}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-gray-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'القسم' : 'Department'}
                  </label>
                  <input
                    type="text"
                    name="department"
                    value={formData.department}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-gray-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'المسمى الوظيفي' : 'Job Title'}
                  </label>
                  <input
                    type="text"
                    name="jobTitle"
                    value={formData.jobTitle}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-gray-200"
                  />
                </div>
              </div>
            </div>

            {/* تفاصيل المأمورية */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 gentle-animated-box">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                <FiMapPin className="text-blue-600" />
                {isArabic ? 'تفاصيل المأمورية' : 'Mission Details'}
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'نوع المأمورية *' : 'Mission Type *'}
                  </label>
                  <select
                    name="missionType"
                    value={formData.missionType}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-gray-200"
                  >
                    <option value="">{isArabic ? 'اختر نوع المأمورية' : 'Select mission type'}</option>
                    {missionTypes.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'غرض المأمورية *' : 'Mission Purpose *'}
                  </label>
                  <select
                    name="missionPurpose"
                    value={formData.missionPurpose}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-gray-200"
                  >
                    <option value="">{isArabic ? 'اختر غرض المأمورية' : 'Select mission purpose'}</option>
                    {missionPurposes.map((purpose) => (
                      <option key={purpose.value} value={purpose.value}>
                        {purpose.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'الوجهة *' : 'Destination *'}
                  </label>
                  <input
                    type="text"
                    name="destination"
                    value={formData.destination}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-gray-200"
                    placeholder={isArabic ? 'مكان المأمورية' : 'Mission location'}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'تاريخ بداية المأمورية *' : 'Mission Start Date *'}
                  </label>
                  <input
                    type="text"
                    name="startDate"
                    value={formData.startDate}
                    onChange={handleInputChange}
                    required
                    placeholder="dd/mm/yyyy"
                    pattern="\d{2}/\d{2}/\d{4}"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-gray-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'عدد الأيام *' : 'Number of Days *'}
                  </label>
                  <input
                    type="number"
                    name="totalDays"
                    value={formData.totalDays}
                    onChange={handleInputChange}
                    required
                    min="1"
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-gray-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'تاريخ نهاية المأمورية' : 'Mission End Date'}
                  </label>
                  <input
                    type="text"
                    name="endDate"
                    value={formData.endDate}
                    onChange={handleInputChange}
                    placeholder="dd/mm/yyyy"
                    pattern="\d{2}/\d{2}/\d{4}"
                    readOnly
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-100 dark:bg-gray-600 dark:text-gray-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'وقت البداية' : 'Start Time'}
                  </label>
                  <input
                    type="time"
                    name="startTime"
                    value={formData.startTime}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-gray-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'وقت النهاية' : 'End Time'}
                  </label>
                  <input
                    type="time"
                    name="endTime"
                    value={formData.endTime}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-gray-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'وسيلة النقل' : 'Transport Method'}
                  </label>
                  <select
                    name="transportMethod"
                    value={formData.transportMethod}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-gray-200"
                  >
                    <option value="">{isArabic ? 'اختر وسيلة النقل' : 'Select transport method'}</option>
                    {transportMethods.map((method) => (
                      <option key={method.value} value={method.value}>
                        {method.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'الغرض من المأمورية (ملاحظات)' : 'Purpose of Mission (Notes)'}
                  </label>
                  <textarea
                    name="purpose"
                    value={formData.purpose}
                    onChange={handleInputChange}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-gray-200"
                    placeholder={isArabic ? 'مثال: تسليم طلب إصدار السيارات عن شهر 5' : 'Example: Deliver car issuance request for month 5'}
                  />
                </div>
              </div>
            </div>

            {/* معلومات مالية وإضافية */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 gentle-animated-box">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
                {isArabic ? 'معلومات مالية وإضافية' : 'Financial & Additional Information'}
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      name="accommodationNeeded"
                      checked={formData.accommodationNeeded}
                      onChange={handleInputChange}
                      className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                    />
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {isArabic ? 'يحتاج إقامة' : 'Accommodation Needed'}
                    </span>
                  </label>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'سلفة مطلوبة' : 'Advance Payment'}
                  </label>
                  <input
                    type="number"
                    name="advancePayment"
                    value={formData.advancePayment}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-gray-200"
                    placeholder={isArabic ? 'المبلغ بالجنيه' : 'Amount in EGP'}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'المصروفات المتوقعة' : 'Expected Expenses'}
                  </label>
                  <input
                    type="number"
                    name="expectedExpenses"
                    value={formData.expectedExpenses}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-gray-200"
                    placeholder={isArabic ? 'المبلغ بالجنيه' : 'Amount in EGP'}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'مرافق في المأمورية' : 'Accompanied By'}
                  </label>
                  <input
                    type="text"
                    name="accompaniedBy"
                    value={formData.accompaniedBy}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-gray-200"
                    placeholder={isArabic ? 'أسماء المرافقين' : 'Names of companions'}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'جهة الاتصال في الطوارئ' : 'Emergency Contact'}
                  </label>
                  <input
                    type="text"
                    name="emergencyContact"
                    value={formData.emergencyContact}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-gray-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'رقم هاتف الطوارئ' : 'Emergency Phone'}
                  </label>
                  <input
                    type="tel"
                    name="emergencyPhone"
                    value={formData.emergencyPhone}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-gray-200"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'ملاحظات' : 'Notes'}
                  </label>
                  <textarea
                    name="notes"
                    value={formData.notes}
                    onChange={handleInputChange}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-gray-200"
                    placeholder={isArabic ? 'ملاحظات إضافية...' : 'Additional notes...'}
                  />
                </div>
              </div>
            </div>

            {/* أزرار التحكم */}
            <div className="flex justify-end gap-4">
              <button
                type="button"
                onClick={() => router.back()}
                className="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors flex items-center gap-2"
              >
                <FiArrowLeft />
                {isArabic ? 'رجوع' : 'Back'}
              </button>

              <button
                type="button"
                onClick={clearForm}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
              >
                <FiRefreshCw />
                {isArabic ? 'طلب جديد' : 'New Request'}
              </button>

              <button
                type="submit"
                disabled={loading}
                className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <FiSave />
                )}
                {loading ? (isArabic ? 'جاري التقديم...' : 'Submitting...') : (isArabic ? 'تقديم الطلب' : 'Submit Request')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </MainLayout>
  );
};

export default MissionRequestPage;
