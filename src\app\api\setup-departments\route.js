import { getConnection } from '@/lib/db';
import { NextResponse } from 'next/server';

export async function POST() {
  try {
    const pool = await getConnection();

    // التحقق من وجود الجدول أولاً
    const tableCheck = await pool.request().query(`
      SELECT COUNT(*) as tableCount
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_NAME = 'Departments'
    `);

    if (tableCheck.recordset[0].tableCount === 0) {
      // إنشاء جدول الأقسام
      await pool.request().query(`
        CREATE TABLE Departments (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          DepartmentName NVARCHAR(255) NOT NULL,
          DepartmentCode NVARCHAR(50),
          ManagerCode NVARCHAR(50),
          ParentDepartmentID INT,
          Description NVARCHAR(500),
          Level INT DEFAULT 1,
          IsActive BIT DEFAULT 1,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE()
        )
      `);

      // إدراج بيانات افتراضية
      await pool.request().query(`
        INSERT INTO Departments (DepartmentName, DepartmentCode, ManagerCode, Description, Level) VALUES
        (N'إدارة التنفيذ', 'EXEC', '1428', N'إدارة تنفيذ المشاريع', 1),
        (N'إدارة المكتب الفني', 'TECH', '1414', N'المكتب الفني والتصميم', 1),
        (N'إدارة السلامة والصحة المهنية', 'SAFETY', '5632', N'السلامة والصحة المهنية', 1),
        (N'إدارة المساحة', 'SURVEY', '', N'أعمال المساحة والقياس', 1),
        (N'إدارة الأمن', 'SECURITY', '', N'الأمن والحراسة', 1),
        (N'إدارة تكنولوجيا المعلومات', 'IT', '', N'تكنولوجيا المعلومات والحاسوب', 1),
        (N'قسم التخطيط والمتابعة', 'PLANNING', '', N'التخطيط ومتابعة المشاريع', 2),
        (N'قسم الحصر والمستخلصات', 'QUANTITY', '', N'حصر الكميات والمستخلصات', 2),
        (N'قسم التسعير', 'PRICING', '', N'تسعير المشاريع', 2),
        (N'قسم المعماري', 'ARCH', '', N'التصميم المعماري', 2)
      `);
    }

    // التحقق من النتيجة
    const result = await pool.request().query(`
      SELECT COUNT(*) as count FROM Departments
    `);

    return NextResponse.json({
      success: true,
      message: 'تم إنشاء جدول الأقسام بنجاح',
      departmentsCount: result.recordset[0].count
    });

  } catch (error) {
    console.error('خطأ في إنشاء جدول الأقسام:', error);
    return NextResponse.json({
      success: false,
      message: 'فشل في إنشاء جدول الأقسام',
      error: error.message
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    const pool = await getConnection();

    // التحقق من الجداول الموجودة
    const tablesResult = await pool.request().query(`
      SELECT TABLE_NAME
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_TYPE = 'BASE TABLE'
      AND (TABLE_NAME = 'Department' OR TABLE_NAME = 'Departments')
    `);

    console.log('الجداول الموجودة:', tablesResult.recordset.map(t => t.TABLE_NAME));

    let departments = { recordset: [] };
    let tableName = '';

    // جرب جدول Department أولاً
    if (tablesResult.recordset.some(t => t.TABLE_NAME === 'Department')) {
      tableName = 'Department';
      try {
        // التحقق من أعمدة جدول Department
        const columnsResult = await pool.request().query(`
          SELECT COLUMN_NAME
          FROM INFORMATION_SCHEMA.COLUMNS
          WHERE TABLE_NAME = 'Department'
          ORDER BY ORDINAL_POSITION
        `);

        const columns = columnsResult.recordset.map(r => r.COLUMN_NAME);
        console.log('أعمدة جدول Department:', columns);

        // جلب البيانات من جدول Department
        departments = await pool.request().query(`
          SELECT * FROM Department
        `);

      } catch (error) {
        console.log('خطأ في جدول Department:', error.message);
      }
    }

    // إذا لم نجد بيانات، جرب جدول Departments
    if (departments.recordset.length === 0 && tablesResult.recordset.some(t => t.TABLE_NAME === 'Departments')) {
      tableName = 'Departments';
      try {
        departments = await pool.request().query(`
          SELECT * FROM Departments
        `);
      } catch (error) {
        console.log('خطأ في جدول Departments:', error.message);
      }
    }

    return NextResponse.json({
      success: true,
      tableExists: tablesResult.recordset.length > 0,
      tableName: tableName,
      departments: departments.recordset,
      departmentsCount: departments.recordset.length
    });

  } catch (error) {
    console.error('خطأ في جلب الأقسام:', error);
    return NextResponse.json({
      success: false,
      message: 'فشل في جلب الأقسام',
      error: error.message
    }, { status: 500 });
  }
}
