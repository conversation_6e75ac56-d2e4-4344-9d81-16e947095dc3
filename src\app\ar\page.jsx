'use client';
import React from 'react';

function MainComponent() {
  const navigationItems = [
    {
      title: 'إدارة الموظفين',
      icon: 'fa-users',
      link: '/employees',
      description: 'إدارة بيانات الموظفين وملفاتهم الشخصية',
    },
    {
      title: 'تسجيل الحضور',
      icon: 'fa-clock',
      link: '/attendance',
      description: 'تسجيل الحضور والانصراف اليومي',
    },
    {
      title: 'إدارة الأصول',
      icon: 'fa-building',
      link: '/assets',
      description: 'إدارة الشقق والسيارات والأصول المؤجرة',
    },
    {
      title: 'إدارة الإجازات',
      icon: 'fa-calendar',
      link: '/leaves',
      description: 'إدارة طلبات الإجازات والمغادرات',
    },
  ];

  return (
    <div
      dir="rtl"
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
            نظام إدارة شؤون الموظفين
          </h1>
          <a
            href="/"
            className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
          >
            English
          </a>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8">
          {navigationItems.map((item, index) => (
            <a
              key={index}
              href={item.link}
              className="bg-gray-50 dark:bg-gray-800 rounded-xl p-8 hover:shadow-lg transition-shadow duration-300 flex items-start space-x-reverse space-x-6"
            >
              <div className="flex-shrink-0">
                <i
                  className={`fas ${item.icon} text-4xl text-blue-600 dark:text-blue-400`}
                ></i>
              </div>
              <div className="flex-grow">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-3">
                  {item.title}
                </h2>
                <p className="text-gray-600 dark:text-gray-300 text-lg">
                  {item.description}
                </p>
              </div>
            </a>
          ))}
        </div>

        <div className="mt-16 text-center text-gray-600 dark:text-gray-400">
          <p>© 2025 نظام إدارة شؤون الموظفين. جميع الحقوق محفوظة</p>
        </div>
      </div>
    </div>
  );
}

export default MainComponent;
