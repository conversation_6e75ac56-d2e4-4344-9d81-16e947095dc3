'use client';

import { useState, useEffect } from 'react';
import DashboardCard from '@/components/DashboardCard.jsx';

const Icons = {
  Database: (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" {...props}>
      <path d="M12 1.5c-1.989 0-3.9.301-5.652.869L6 2.501v5.997l.55-.275a13.5 13.5 0 0 1 5.851-1.351c.506 0 1.006.042 1.5.125a.75.75 0 0 0 .25-1.479A15.03 15.03 0 0 0 12 5.25c-1.34 0-2.645.19-3.897.551A13.878 13.878 0 0 0 6 6.327V2.834a14.25 14.25 0 0 1 5.932-1.2.75.75 0 0 0 .075-1.498c-.312-.015-.626-.022-.943-.022L12 1.5ZM6 7.677v5.66c0 .218 0 .363.008.498.023.352.088.635.198.898.134.322.341.61.606.85.239.216.473.377.826.497A6.013 6.013 0 0 0 9.96 16.5c.35 0 .696-.03 1.036-.087a.75.75 0 0 0-.072-1.498c-.255.043-.514.065-.777.065a4.511 4.511 0 0 1-2.322-.642l-.04-.026a1.319 1.319 0 0 1-.153-.145c-.08-.086-.143-.187-.185-.301a1.754 1.754 0 0 1-.074-.365C7.367 13.426 7.363 13.334 7.36 13.2c-.003-.16-.01-.328-.01-.625v-4.3c0-.172 0-.29-.003-.399a2.04 2.04 0 0 0-.04-.425c-.067-.375-.21-.73-.417-1.042C6.677 6.202 6.463 6.046 6.221 5.9a3.995 3.995 0 0 0-.221-.121v1.897ZM9.75 10.5a.75.75 0 0 0 0 1.5h1.5a.75.75 0 0 0 0-1.5h-1.5Zm3.75 0a.75.75 0 0 0 0 1.5h1.5a.75.75 0 0 0 0-1.5h-1.5Zm3.75 0a.75.75 0 0 0 0 1.5h1.5a.75.75 0 0 0 0-1.5h-1.5Z" />
      <path d="M12 8.25a.75.75 0 0 1 .75.75v3.75a.75.75 0 0 1-1.5 0V9a.75.75 0 0 1 .75-.75ZM12 15a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-1.5 0V15.75A.75.75 0 0 1 12 15Z" />
    </svg>
  ),
  Server: (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" {...props}>
      <path d="M4.08 5.227A3 3 0 0 1 6.979 3H17.02a3 3 0 0 1 2.9 2.227l2.113 7.926A5.228 5.228 0 0 0 18.75 12H5.25a5.228 5.228 0 0 0-3.284 1.153L4.08 5.227Z" />
      <path fillRule="evenodd" d="M5.25 13.5a3.75 3.75 0 1 0 0 7.5h13.5a3.75 3.75 0 1 0 0-7.5H5.25Zm10.5 4.5a.75.75 0 1 0 0-********* 0 0 0 0 1.5Zm3.75-.75a.75.75 0 1 1-1.5 0 .75.75 0 0 1 1.5 0Z" clipRule="evenodd" />
    </svg>
  ),
  Api: (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" {...props}>
      <path fillRule="evenodd" d="M14.447 3.026a.75.75 0 0 1 .527.921l-4.5 16.5a.75.75 0 0 1-1.448-.394l4.5-16.5a.75.75 0 0 1 .921-.527ZM16.72 6.22a.75.75 0 0 1 1.06 0l5.25 5.25a.75.75 0 0 1 0 1.06l-5.25 5.25a.75.75 0 1 1-1.06-1.06L21.44 12l-4.72-4.72a.75.75 0 0 1 0-1.06Zm-9.44 0a.75.75 0 0 1 0 1.06L2.56 12l4.72 4.72a.75.75 0 0 1-1.06 1.06L.97 12.53a.75.75 0 0 1 0-1.06l5.25-5.25a.75.75 0 0 1 1.06 0Z" clipRule="evenodd" />
    </svg>
  ),
  Performance: (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" {...props}>
      <path fillRule="evenodd" d="M1.5 5.625c0-1.036.84-1.875 1.875-1.875h17.25c1.035 0 1.875.84 1.875 1.875v12.75c0 1.035-.84 1.875-1.875 1.875H3.375A1.875 1.875 0 0 1 1.5 18.375V5.625ZM21 9.375A.375.375 0 0 0 20.625 9h-7.5a.375.375 0 0 0-.375.375v1.5c0 .207.168.375.375.375h7.5a.375.375 0 0 0 .375-.375v-1.5Zm0 3.75a.375.375 0 0 0-.375-.375h-7.5a.375.375 0 0 0-.375.375v1.5c0 .207.168.375.375.375h7.5a.375.375 0 0 0 .375-.375v-1.5Zm0 3.75a.375.375 0 0 0-.375-.375h-7.5a.375.375 0 0 0-.375.375v1.5c0 .207.168.375.375.375h7.5a.375.375 0 0 0 .375-.375v-1.5ZM10.875 18.75a.375.375 0 0 0 .375-.375v-1.5a.375.375 0 0 0-.375-.375h-7.5a.375.375 0 0 0-.375.375v1.5c0 .207.168.375.375.375h7.5ZM3.375 15h7.5a.375.375 0 0 0 .375-.375v-1.5a.375.375 0 0 0-.375-.375h-7.5a.375.375 0 0 0-.375.375v1.5c0 .207.168.375.375.375Zm0-3.75h7.5a.375.375 0 0 0 .375-.375v-1.5A.375.375 0 0 0 10.875 9h-7.5A.375.375 0 0 0 3 9.375v1.5c0 .207.168.375.375.375Z" clipRule="evenodd" />
    </svg>
  ),
  Logs: (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" {...props}>
      <path fillRule="evenodd" d="M7.502 6h7.128A3.375 3.375 0 0 1 18 9.375v9.375a3 3 0 0 0 3-3V6.108c0-1.505-1.125-2.811-2.664-2.94a48.972 48.972 0 0 0-.673-.05A3 3 0 0 0 15 1.5h-1.5a3 3 0 0 0-2.663 1.618c-.225.015-.45.032-.673.05C8.662 3.295 7.554 4.542 7.502 6ZM13.5 3A1.5 1.5 0 0 0 12 4.5h4.5A1.5 1.5 0 0 0 15 3h-1.5Z" clipRule="evenodd" />
      <path fillRule="evenodd" d="M3 9.375C3 8.339 3.84 7.5 4.875 7.5h9.75c1.036 0 1.875.84 1.875 1.875v11.25c0 1.035-.84 1.875-1.875 1.875h-9.75A1.875 1.875 0 0 1 3 20.625V9.375ZM6 12a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V12Zm2.25 0a.75.75 0 0 1 .75-.75h3.75a.75.75 0 0 1 0 1.5H9a.75.75 0 0 1-.75-.75ZM6 15a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V15Zm2.25 0a.75.75 0 0 1 .75-.75h3.75a.75.75 0 0 1 0 1.5H9a.75.75 0 0 1-.75-.75ZM6 18a.75.75 0 0 1 .75-.75h.008a.75.75 0 0 1 .75.75v.008a.75.75 0 0 1-.75.75H6.75a.75.75 0 0 1-.75-.75V18Zm2.25 0a.75.75 0 0 1 .75-.75h3.75a.75.75 0 0 1 0 1.5H9a.75.75 0 0 1-.75-.75Z" clipRule="evenodd" />
    </svg>
  ),
  Security: (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" {...props}>
      <path fillRule="evenodd" d="M12 1.5a5.25 5.25 0 0 0-5.25 5.25v3a3 3 0 0 0-3 3v6.75a3 3 0 0 0 3 3h10.5a3 3 0 0 0 3-3v-6.75a3 3 0 0 0-3-3v-3c0-2.9-2.35-5.25-5.25-5.25Zm3.75 8.25v-3a3.75 3.75 0 1 0-7.5 0v3h7.5Z" clipRule="evenodd" />
    </svg>
  ),
  Backup: (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" {...props}>
      <path fillRule="evenodd" d="M12.963 2.286a.75.75 0 0 0-1.071-.136 9.742 9.742 0 0 0-3.539 6.176 7.547 7.547 0 0 1-1.705-1.715.75.75 0 0 0-1.152-.082A9 9 0 1 0 15.68 4.534a7.46 7.46 0 0 1-2.717-2.248ZM15.75 14.25a3.75 3.75 0 1 1-7.313-1.172c.628.465 1.35.81 2.133 1a5.99 5.99 0 0 1 1.925-3.546 3.75 3.75 0 0 1 3.255 3.718Z" clipRule="evenodd" />
    </svg>
  ),
  Users: (props) => (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" {...props}>
      <path d="M4.5 6.375a4.125 4.125 0 1 1 8.25 0 4.125 4.125 0 0 1-8.25 0ZM14.25 8.625a3.375 3.375 0 1 1 6.75 0 3.375 3.375 0 0 1-6.75 0ZM1.5 19.125a7.125 7.125 0 0 1 14.25 0v.003l-.001.119a.75.75 0 0 1-.363.63 13.067 13.067 0 0 1-6.761 1.873c-2.472 0-4.786-.684-6.76-1.873a.75.75 0 0 1-.364-.63l-.001-.122ZM17.25 19.128l-.001.144a2.25 2.25 0 0 1-.233.96 10.088 10.088 0 0 0 5.06-********** 0 0 0 .42-.643 4.875 4.875 0 0 0-6.957-4.611 8.586 8.586 0 0 1 1.71 5.157v.003Z" />
    </svg>
  )
};

const SystemHealthReviewPage = () => {
  const [healthStatus, setHealthStatus] = useState({
    database: { status: 'loading', value: '...', description: 'جاري التحقق من اتصال قاعدة البيانات', color: 'blue' },
    api: { status: 'loading', value: '...', description: 'جاري التحقق من حالة خدمة API', color: 'blue' },
    storage: { status: 'loading', value: '...', description: 'جاري التحقق من استخدام التخزين', color: 'blue' },
    memory: { status: 'loading', value: '...', description: 'جاري التحقق من استخدام الذاكرة', color: 'blue' },
    cpu: { status: 'loading', value: '...', description: 'جاري التحقق من تحميل وحدة المعالجة المركزية', color: 'blue' },
    jobQueue: { status: 'loading', value: '...', description: 'جاري التحقق من قائمة انتظار المهام الخلفية', color: 'blue' }
  });

  useEffect(() => {
    const simulateHealthCheck = () => {
      setTimeout(() => {
        setHealthStatus(prev => ({
          ...prev,
          database: {
            status: 'success',
            value: 'متصل',
            description: 'قاعدة البيانات تعمل بشكل طبيعي',
            color: 'green'
          }
        }));
      }, 1500);

      setTimeout(() => {
        setHealthStatus(prev => ({
          ...prev,
          api: {
            status: 'success',
            value: 'يعمل',
            description: 'خدمة API قيد التشغيل',
            color: 'green'
          }
        }));
      }, 2000);

      setTimeout(() => {
        setHealthStatus(prev => ({
          ...prev,
          storage: {
            status: 'warning',
            value: '75% مستخدم',
            description: 'استخدام التخزين مرتفع',
            color: 'yellow'
          }
        }));
      }, 2500);

      setTimeout(() => {
        setHealthStatus(prev => ({
          ...prev,
          memory: {
            status: 'success',
            value: '30% مستخدم',
            description: 'استخدام الذاكرة طبيعي',
            color: 'green'
          }
        }));
      }, 3000);

      setTimeout(() => {
        setHealthStatus(prev => ({
          ...prev,
          cpu: {
            status: 'error',
            value: '90% تحميل',
            description: 'تحميل وحدة المعالجة المركزية مرتفع جدا',
            color: 'red'
          }
        }));
      }, 3500);

      setTimeout(() => {
        setHealthStatus(prev => ({
          ...prev,
          jobQueue: {
            status: 'success',
            value: 'خامل',
            description: 'قائمة انتظار المهام الخلفية فارغة',
            color: 'green'
          }
        }));
      }, 4000);
    };

    simulateHealthCheck();
  }, []);

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold text-gray-800 dark:text-white mb-6">مراجعة صحة النظام</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <DashboardCard
          title="اتصال قاعدة البيانات"
          value={healthStatus.database.value}
          status={healthStatus.database.status}
          description={healthStatus.database.description}
          color={healthStatus.database.color}
        />
        <DashboardCard
          title="حالة خدمة API"
          value={healthStatus.api.value}
          status={healthStatus.api.status}
          description={healthStatus.api.description}
          color={healthStatus.api.color}
        />
        <DashboardCard
          title="استخدام التخزين"
          value={healthStatus.storage.value}
          status={healthStatus.storage.status}
          description={healthStatus.storage.description}
          color={healthStatus.storage.color}
        />
        <DashboardCard
          title="استخدام الذاكرة"
          value={healthStatus.memory.value}
          status={healthStatus.memory.status}
          description={healthStatus.memory.description}
          color={healthStatus.memory.color}
        />
        <DashboardCard
          title="تحميل وحدة المعالجة المركزية"
          value={healthStatus.cpu.value}
          status={healthStatus.cpu.status}
          description={healthStatus.cpu.description}
          color={healthStatus.cpu.color}
        />
        <DashboardCard
          title="قائمة انتظار المهام الخلفية"
          value={healthStatus.jobQueue.value}
          status={healthStatus.jobQueue.status}
          description={healthStatus.jobQueue.description}
          color={healthStatus.jobQueue.color}
        />
      </div>
    </div>
  );
};

export default SystemHealthReviewPage;
