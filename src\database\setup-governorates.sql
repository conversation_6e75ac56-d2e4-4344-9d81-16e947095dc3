-- إنشاء جدول المحافظات إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Governorates]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Governorates](
        [GovernorateId] [int] IDENTITY(1,1) PRIMARY KEY,
        [NameAr] [nvarchar](100) NOT NULL
    )
END
GO

-- حذف البيانات الموجودة إذا وجدت
TRUNCATE TABLE [dbo].[Governorates]
GO

-- إدخال بيانات المحافظات
INSERT INTO [dbo].[Governorates] ([NameAr])
VALUES 
    (N'القاهرة'),
    (N'الإسكندرية'),
    (N'بورسعيد'),
    (N'السويس'),
    (N'دمياط'),
    (N'الدقهلية'),
    (N'الشرقية'),
    (N'القليوبية'),
    (N'كفر الشيخ'),
    (N'الغربية'),
    (N'المنوفية'),
    (N'البحيرة'),
    (N'الفيوم'),
    (N'بني سويف'),
    (N'المنيا'),
    (N'أسيوط'),
    (N'سوهاج'),
    (N'قنا'),
    (N'الأقصر'),
    (N'أسوان'),
    (N'شمال سيناء'),
    (N'جنوب سيناء'),
    (N'البحر الأحمر'),
    (N'الوادي الجديد'),
    (N'مطروح'),
    (N'حلوان'),
    (N'6 أكتوبر'),
    (N'مرسى مطروح')
GO
