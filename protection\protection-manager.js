#!/usr/bin/env node
// مدير نظام الحماية - تحكم كامل في الحماية

const fs = require('fs');
const path = require('path');
const readline = require('readline');

class ProtectionManager {
  constructor() {
    this.configFile = path.join(__dirname, '../.protection-config.json');
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    this.loadConfig();
  }

  // تحميل التكوين
  loadConfig() {
    try {
      if (fs.existsSync(this.configFile)) {
        const config = fs.readFileSync(this.configFile, 'utf8');
        this.config = JSON.parse(config);
      } else {
        this.config = this.getDefaultConfig();
        this.saveConfig();
      }
    } catch (error) {
      console.error('خطأ في تحميل التكوين:', error.message);
      this.config = this.getDefaultConfig();
    }
  }

  // التكوين الافتراضي
  getDefaultConfig() {
    return {
      protection: {
        enabled: false,
        mode: 'development', // development, production, disabled
        intrusive: false,    // هل الحماية مؤثرة على النظام
        logging: true,
        monitoring: false
      },
      development: {
        allowFullAccess: true,
        skipLicenseCheck: true,
        enableDebugging: true
      },
      production: {
        requireLicense: true,
        strictMode: true,
        enableMonitoring: true
      },
      lastUpdated: new Date().toISOString()
    };
  }

  // حفظ التكوين
  saveConfig() {
    try {
      this.config.lastUpdated = new Date().toISOString();
      fs.writeFileSync(this.configFile, JSON.stringify(this.config, null, 2));
    } catch (error) {
      console.error('خطأ في حفظ التكوين:', error.message);
    }
  }

  // سؤال المستخدم
  async askQuestion(question) {
    return new Promise((resolve) => {
      this.rl.question(question, (answer) => {
        resolve(answer.trim());
      });
    });
  }

  // عرض الحالة الحالية
  showCurrentStatus() {
    console.log('\n🔐 حالة نظام الحماية الحالية:');
    console.log('================================');
    console.log(`الحالة: ${this.config.protection.enabled ? '✅ مفعل' : '❌ معطل'}`);
    console.log(`الوضع: ${this.config.protection.mode}`);
    console.log(`مؤثر على النظام: ${this.config.protection.intrusive ? '⚠️  نعم' : '✅ لا'}`);
    console.log(`التسجيل: ${this.config.protection.logging ? '✅ مفعل' : '❌ معطل'}`);
    console.log(`المراقبة: ${this.config.protection.monitoring ? '✅ مفعل' : '❌ معطل'}`);
    console.log(`آخر تحديث: ${new Date(this.config.lastUpdated).toLocaleString('ar-EG')}`);
  }

  // القائمة الرئيسية
  async showMainMenu() {
    this.showCurrentStatus();
    
    console.log('\n📋 خيارات إدارة الحماية:');
    console.log('========================');
    console.log('1. تفعيل الحماية غير المؤثرة (موصى به)');
    console.log('2. تعطيل الحماية تماماً');
    console.log('3. وضع التطوير (بدون حماية)');
    console.log('4. وضع الإنتاج (حماية كاملة)');
    console.log('5. إعدادات متقدمة');
    console.log('6. عرض السجلات');
    console.log('7. اختبار النظام');
    console.log('8. خروج');
    
    const choice = await this.askQuestion('\nاختر العملية (1-8): ');
    
    switch (choice) {
      case '1':
        await this.enableNonIntrusiveProtection();
        break;
      case '2':
        await this.disableProtection();
        break;
      case '3':
        await this.setDevelopmentMode();
        break;
      case '4':
        await this.setProductionMode();
        break;
      case '5':
        await this.advancedSettings();
        break;
      case '6':
        await this.viewLogs();
        break;
      case '7':
        await this.testSystem();
        break;
      case '8':
        console.log('👋 وداعاً!');
        this.rl.close();
        return;
      default:
        console.log('❌ اختيار غير صالح');
    }
    
    await this.showMainMenu();
  }

  // تفعيل الحماية غير المؤثرة
  async enableNonIntrusiveProtection() {
    console.log('\n🛡️  تفعيل الحماية غير المؤثرة...');
    
    this.config.protection.enabled = true;
    this.config.protection.mode = 'non-intrusive';
    this.config.protection.intrusive = false;
    this.config.protection.logging = true;
    this.config.protection.monitoring = true;
    
    this.saveConfig();
    
    console.log('✅ تم تفعيل الحماية غير المؤثرة بنجاح!');
    console.log('📝 هذا الوضع:');
    console.log('   - لا يؤثر على أي وظيفة في النظام');
    console.log('   - يراقب الاستخدام في الخلفية');
    console.log('   - يسجل الأحداث للمراجعة');
    console.log('   - مناسب للتطوير والإنتاج');
  }

  // تعطيل الحماية
  async disableProtection() {
    console.log('\n❌ تعطيل الحماية تماماً...');
    
    const confirm = await this.askQuestion('هل أنت متأكد؟ (y/n): ');
    
    if (confirm.toLowerCase() === 'y' || confirm.toLowerCase() === 'yes') {
      this.config.protection.enabled = false;
      this.config.protection.mode = 'disabled';
      this.config.protection.intrusive = false;
      this.config.protection.logging = false;
      this.config.protection.monitoring = false;
      
      this.saveConfig();
      
      console.log('✅ تم تعطيل الحماية تماماً');
      console.log('⚠️  تحذير: النظام غير محمي الآن');
    } else {
      console.log('❌ تم إلغاء العملية');
    }
  }

  // وضع التطوير
  async setDevelopmentMode() {
    console.log('\n🔧 تفعيل وضع التطوير...');
    
    this.config.protection.enabled = false;
    this.config.protection.mode = 'development';
    this.config.protection.intrusive = false;
    this.config.development.allowFullAccess = true;
    this.config.development.skipLicenseCheck = true;
    this.config.development.enableDebugging = true;
    
    this.saveConfig();
    
    console.log('✅ تم تفعيل وضع التطوير');
    console.log('📝 هذا الوضع:');
    console.log('   - وصول كامل لجميع الميزات');
    console.log('   - تجاهل فحص الترخيص');
    console.log('   - تفعيل أدوات التصحيح');
    console.log('   - مناسب للتطوير فقط');
  }

  // وضع الإنتاج
  async setProductionMode() {
    console.log('\n🏭 تفعيل وضع الإنتاج...');
    
    const confirm = await this.askQuestion('هل تريد حماية مؤثرة أم غير مؤثرة؟ (intrusive/non-intrusive): ');
    
    this.config.protection.enabled = true;
    this.config.protection.mode = 'production';
    this.config.protection.intrusive = confirm.toLowerCase().includes('intrusive');
    this.config.production.requireLicense = true;
    this.config.production.strictMode = true;
    this.config.production.enableMonitoring = true;
    
    this.saveConfig();
    
    console.log('✅ تم تفعيل وضع الإنتاج');
    console.log('📝 هذا الوضع:');
    console.log('   - فحص صارم للترخيص');
    console.log('   - مراقبة مستمرة');
    console.log('   - حماية من التلاعب');
    console.log(`   - ${this.config.protection.intrusive ? 'مؤثر على النظام' : 'غير مؤثر على النظام'}`);
  }

  // الإعدادات المتقدمة
  async advancedSettings() {
    console.log('\n⚙️  الإعدادات المتقدمة:');
    console.log('===================');
    console.log('1. تغيير إعدادات التسجيل');
    console.log('2. تغيير إعدادات المراقبة');
    console.log('3. إعادة تعيين التكوين');
    console.log('4. عرض التكوين الكامل');
    console.log('5. العودة للقائمة الرئيسية');
    
    const choice = await this.askQuestion('اختر (1-5): ');
    
    switch (choice) {
      case '1':
        const logging = await this.askQuestion('تفعيل التسجيل؟ (y/n): ');
        this.config.protection.logging = logging.toLowerCase() === 'y';
        this.saveConfig();
        console.log(`✅ تم ${this.config.protection.logging ? 'تفعيل' : 'تعطيل'} التسجيل`);
        break;
        
      case '2':
        const monitoring = await this.askQuestion('تفعيل المراقبة؟ (y/n): ');
        this.config.protection.monitoring = monitoring.toLowerCase() === 'y';
        this.saveConfig();
        console.log(`✅ تم ${this.config.protection.monitoring ? 'تفعيل' : 'تعطيل'} المراقبة`);
        break;
        
      case '3':
        const reset = await this.askQuestion('إعادة تعيين التكوين؟ (y/n): ');
        if (reset.toLowerCase() === 'y') {
          this.config = this.getDefaultConfig();
          this.saveConfig();
          console.log('✅ تم إعادة تعيين التكوين');
        }
        break;
        
      case '4':
        console.log('\n📋 التكوين الكامل:');
        console.log(JSON.stringify(this.config, null, 2));
        break;
        
      case '5':
        return;
        
      default:
        console.log('❌ اختيار غير صالح');
    }
    
    await this.advancedSettings();
  }

  // عرض السجلات
  async viewLogs() {
    console.log('\n📋 عرض السجلات:');
    console.log('================');
    
    const logsDir = path.join(__dirname, '../logs');
    
    if (!fs.existsSync(logsDir)) {
      console.log('📭 لا توجد سجلات');
      return;
    }
    
    const logFiles = fs.readdirSync(logsDir).filter(f => f.endsWith('.log') || f.endsWith('.json'));
    
    if (logFiles.length === 0) {
      console.log('📭 لا توجد ملفات سجلات');
      return;
    }
    
    console.log('ملفات السجلات المتاحة:');
    logFiles.forEach((file, index) => {
      console.log(`${index + 1}. ${file}`);
    });
    
    const choice = await this.askQuestion('اختر ملف السجل (رقم): ');
    const fileIndex = parseInt(choice) - 1;
    
    if (fileIndex >= 0 && fileIndex < logFiles.length) {
      const logFile = path.join(logsDir, logFiles[fileIndex]);
      const content = fs.readFileSync(logFile, 'utf8');
      
      console.log(`\n📄 محتوى ${logFiles[fileIndex]}:`);
      console.log('========================');
      console.log(content.slice(-1000)); // آخر 1000 حرف
    } else {
      console.log('❌ اختيار غير صالح');
    }
  }

  // اختبار النظام
  async testSystem() {
    console.log('\n🧪 اختبار النظام...');
    
    try {
      // اختبار الحماية غير المؤثرة
      const NonIntrusiveProtection = require('./non-intrusive-protection');
      const protection = new NonIntrusiveProtection();
      
      console.log('✅ نظام الحماية غير المؤثرة يعمل');
      
      // اختبار حماية الإنتاج
      const ProductionProtection = require('./production-only-protection');
      const prodProtection = new ProductionProtection();
      const licenseInfo = prodProtection.getLicenseInfo();
      
      console.log('✅ نظام حماية الإنتاج يعمل');
      console.log('📋 معلومات الترخيص:', licenseInfo);
      
      console.log('\n🎉 جميع الأنظمة تعمل بشكل صحيح!');
      
    } catch (error) {
      console.error('❌ خطأ في الاختبار:', error.message);
    }
  }

  // تشغيل المدير
  async run() {
    console.log('\n🔐 مدير نظام الحماية');
    console.log('====================');
    console.log('إدارة شاملة لجميع أنواع الحماية');
    console.log('✅ حماية غير مؤثرة على النظام');
    console.log('⚙️  تحكم كامل في الإعدادات');
    
    try {
      await this.showMainMenu();
    } catch (error) {
      console.error('❌ خطأ في المدير:', error.message);
    } finally {
      this.rl.close();
    }
  }
}

// تشغيل المدير إذا تم استدعاؤه مباشرة
if (require.main === module) {
  const manager = new ProtectionManager();
  manager.run().catch(console.error);
}

module.exports = ProtectionManager;
