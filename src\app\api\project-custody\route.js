import { NextResponse } from 'next/server';
import { getConnection } from '@/lib/db';
import sql from 'mssql';

export async function POST(request) {
  try {
    const { action, data, id } = await request.json();
    const pool = await getConnection();

    switch (action) {
      case 'getCustodyItems':
        return await getCustodyItems(pool, data);
      case 'addCustodyItem':
        return await addCustodyItem(pool, data);
      case 'updateCustodyItem':
        return await updateCustodyItem(pool, id, data);
      case 'deleteCustodyItem':
        return await deleteCustodyItem(pool, id);
      case 'getCustodyStatistics':
        return await getCustodyStatistics(pool, data);
      case 'getMainCategories':
        return await getMainCategories(pool);
      case 'getSubCategories':
        return await getSubCategories(pool, data);
      case 'addMainCategory':
        return await addMainCategory(pool, data);
      case 'addSubCategory':
        return await addSubCategory(pool, data);
      case 'settleCustody':
        return await settleCustody(pool, data);
      case 'addExpense':
        return await addExpense(pool, data);
      case 'getCustodyDetails':
        return await getCustodyDetails(pool, id);
      case 'exportCustodyData':
        return await exportCustodyData(pool, data);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  }
}

// إنشاء جداول عهد المشروع المتطورة
async function ensureProjectCustodyTables(pool) {
  try {
    // جدول البنود الرئيسية
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CustodyMainCategories' AND xtype='U')
      BEGIN
        CREATE TABLE CustodyMainCategories (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          CategoryName NVARCHAR(255) NOT NULL UNIQUE,
          CategoryCode NVARCHAR(50) UNIQUE,
          Description NVARCHAR(MAX),
          IsActive BIT DEFAULT 1,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),
          CreatedBy NVARCHAR(100) DEFAULT 'System'
        )
      END
    `);

    // جدول البنود الفرعية
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CustodySubCategories' AND xtype='U')
      BEGIN
        CREATE TABLE CustodySubCategories (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          MainCategoryID INT NOT NULL,
          SubCategoryName NVARCHAR(255) NOT NULL,
          SubCategoryCode NVARCHAR(50),
          Description NVARCHAR(MAX),
          IsActive BIT DEFAULT 1,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),
          CreatedBy NVARCHAR(100) DEFAULT 'System',
          FOREIGN KEY (MainCategoryID) REFERENCES CustodyMainCategories(ID)
        )
      END
    `);

    // جدول العهد (محدث)
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ProjectCustodyItems' AND xtype='U')
      BEGIN
        CREATE TABLE ProjectCustodyItems (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          MainCategoryID INT NOT NULL,
          SubCategoryID INT,
          CustodyType NVARCHAR(50) NOT NULL DEFAULT N'مستديمة', -- مستديمة أو مؤقتة
          ItemName NVARCHAR(255) NOT NULL,
          Description NVARCHAR(MAX),
          Amount DECIMAL(18,2) NOT NULL DEFAULT 0,
          Custodian NVARCHAR(255) NOT NULL,
          CustodianID NVARCHAR(50),
          IssueDate DATE NOT NULL,
          ExpectedSettlementDate DATE,
          ActualSettlementDate DATE,
          Status NVARCHAR(50) DEFAULT N'مُصرفة', -- مُصرفة، مُسوّاة، مُلغاة
          SettlementAmount DECIMAL(18,2) DEFAULT 0,
          RemainingAmount DECIMAL(18,2) DEFAULT 0,
          Notes NVARCHAR(MAX),
          InvoiceNumber NVARCHAR(100),
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),
          CreatedBy NVARCHAR(100) DEFAULT 'System',
          FOREIGN KEY (MainCategoryID) REFERENCES CustodyMainCategories(ID),
          FOREIGN KEY (SubCategoryID) REFERENCES CustodySubCategories(ID)
        )
      END
    `);

    // جدول تفاصيل التسوية
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CustodySettlements' AND xtype='U')
      BEGIN
        CREATE TABLE CustodySettlements (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          CustodyItemID INT NOT NULL,
          SettlementDate DATE NOT NULL,
          SettlementAmount DECIMAL(18,2) NOT NULL,
          ReceiptNumber NVARCHAR(100),
          Description NVARCHAR(MAX),
          AttachmentPath NVARCHAR(500),
          CreatedAt DATETIME DEFAULT GETDATE(),
          CreatedBy NVARCHAR(100) DEFAULT 'System',
          FOREIGN KEY (CustodyItemID) REFERENCES ProjectCustodyItems(ID)
        )
      END
    `);

    // جدول مصروفات العهد
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CustodyExpenses' AND xtype='U')
      BEGIN
        CREATE TABLE CustodyExpenses (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          CustodyItemID INT NOT NULL,
          ExpenseDate DATE NOT NULL,
          ExpenseAmount DECIMAL(18,2) NOT NULL,
          ExpenseDescription NVARCHAR(MAX),
          ReceiptNumber NVARCHAR(100),
          Supplier NVARCHAR(255),
          AttachmentPath NVARCHAR(500),
          CreatedAt DATETIME DEFAULT GETDATE(),
          CreatedBy NVARCHAR(100) DEFAULT 'System',
          FOREIGN KEY (CustodyItemID) REFERENCES ProjectCustodyItems(ID)
        )
      END
    `);

    // إدراج البنود الرئيسية الافتراضية
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM CustodyMainCategories WHERE CategoryCode = 'MEALS')
      BEGIN
        INSERT INTO CustodyMainCategories (CategoryName, CategoryCode, Description) VALUES
        (N'وجبات', 'MEALS', N'جميع أنواع الوجبات والضيافة'),
        (N'مشروبات وأدوات نظافة', 'DRINKS_CLEANING', N'المشروبات ومواد التنظيف'),
        (N'انتقالات', 'TRANSPORT', N'مصاريف الانتقالات والمواصلات'),
        (N'مشتريات', 'PURCHASES', N'المشتريات المختلفة'),
        (N'سيارات الشركة', 'COMPANY_CARS', N'مصاريف سيارات الشركة'),
        (N'شقق', 'APARTMENTS', N'مصاريف الشقق والإيجارات'),
        (N'خدمات موقع', 'SITE_SERVICES', N'خدمات الموقع والمرافق')
      END
    `);

    // إدراج البنود الفرعية الافتراضية
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM CustodySubCategories WHERE SubCategoryCode = 'MEALS_CONCORD')
      BEGIN
        -- بنود فرعية للوجبات
        INSERT INTO CustodySubCategories (MainCategoryID, SubCategoryName, SubCategoryCode)
        SELECT ID, N'وجبات كونكورد', 'MEALS_CONCORD' FROM CustodyMainCategories WHERE CategoryCode = 'MEALS'
        UNION ALL
        SELECT ID, N'وجبات استشاري', 'MEALS_CONSULTANT' FROM CustodyMainCategories WHERE CategoryCode = 'MEALS'
        UNION ALL
        SELECT ID, N'وجبات مالك', 'MEALS_OWNER' FROM CustodyMainCategories WHERE CategoryCode = 'MEALS'
        UNION ALL
        SELECT ID, N'قاعة اجتماعات', 'MEALS_MEETING' FROM CustodyMainCategories WHERE CategoryCode = 'MEALS'

        -- بنود فرعية للمشروبات
        UNION ALL
        SELECT ID, N'مشروبات كونكورد', 'DRINKS_CONCORD' FROM CustodyMainCategories WHERE CategoryCode = 'DRINKS_CLEANING'
        UNION ALL
        SELECT ID, N'مشروبات استشاري', 'DRINKS_CONSULTANT' FROM CustodyMainCategories WHERE CategoryCode = 'DRINKS_CLEANING'
        UNION ALL
        SELECT ID, N'مشروبات مالك', 'DRINKS_OWNER' FROM CustodyMainCategories WHERE CategoryCode = 'DRINKS_CLEANING'
        UNION ALL
        SELECT ID, N'قاعة اجتماعات', 'DRINKS_MEETING' FROM CustodyMainCategories WHERE CategoryCode = 'DRINKS_CLEANING'

        -- بنود فرعية للانتقالات
        UNION ALL
        SELECT ID, N'انتقالات استشاري', 'TRANSPORT_CONSULTANT' FROM CustodyMainCategories WHERE CategoryCode = 'TRANSPORT'
        UNION ALL
        SELECT ID, N'انتقالات مأموريات', 'TRANSPORT_MISSIONS' FROM CustodyMainCategories WHERE CategoryCode = 'TRANSPORT'
        UNION ALL
        SELECT ID, N'انتقالات بدل', 'TRANSPORT_ALLOWANCE' FROM CustodyMainCategories WHERE CategoryCode = 'TRANSPORT'
        UNION ALL
        SELECT ID, N'انتقالات بالخصم', 'TRANSPORT_DEDUCTION' FROM CustodyMainCategories WHERE CategoryCode = 'TRANSPORT'

        -- بنود فرعية للمشتريات
        UNION ALL
        SELECT ID, N'مشتريات موقع', 'PURCHASES_SITE' FROM CustodyMainCategories WHERE CategoryCode = 'PURCHASES'
        UNION ALL
        SELECT ID, N'مشتريات مالك', 'PURCHASES_OWNER' FROM CustodyMainCategories WHERE CategoryCode = 'PURCHASES'
        UNION ALL
        SELECT ID, N'مشتريات استشاري', 'PURCHASES_CONSULTANT' FROM CustodyMainCategories WHERE CategoryCode = 'PURCHASES'

        -- بنود فرعية لسيارات الشركة
        UNION ALL
        SELECT ID, N'جراج', 'CARS_GARAGE' FROM CustodyMainCategories WHERE CategoryCode = 'COMPANY_CARS'
        UNION ALL
        SELECT ID, N'جاز بدل', 'CARS_GAS_ALLOWANCE' FROM CustodyMainCategories WHERE CategoryCode = 'COMPANY_CARS'
        UNION ALL
        SELECT ID, N'مخالفة', 'CARS_VIOLATIONS' FROM CustodyMainCategories WHERE CategoryCode = 'COMPANY_CARS'
        UNION ALL
        SELECT ID, N'مأمورية', 'CARS_MISSIONS' FROM CustodyMainCategories WHERE CategoryCode = 'COMPANY_CARS'
        UNION ALL
        SELECT ID, N'ميزان', 'CARS_BALANCE' FROM CustodyMainCategories WHERE CategoryCode = 'COMPANY_CARS'
        UNION ALL
        SELECT ID, N'كارتات', 'CARS_CARDS' FROM CustodyMainCategories WHERE CategoryCode = 'COMPANY_CARS'

        -- بنود فرعية للشقق
        UNION ALL
        SELECT ID, N'عهدة مؤقتة لإيجار شقة', 'APARTMENTS_TEMP_RENT' FROM CustodyMainCategories WHERE CategoryCode = 'APARTMENTS'
        UNION ALL
        SELECT ID, N'صيانة', 'APARTMENTS_MAINTENANCE' FROM CustodyMainCategories WHERE CategoryCode = 'APARTMENTS'
        UNION ALL
        SELECT ID, N'مصاريف دورية', 'APARTMENTS_PERIODIC' FROM CustodyMainCategories WHERE CategoryCode = 'APARTMENTS'

        -- بنود فرعية لخدمات الموقع
        UNION ALL
        SELECT ID, N'كهرباء', 'SERVICES_ELECTRICITY' FROM CustodyMainCategories WHERE CategoryCode = 'SITE_SERVICES'
        UNION ALL
        SELECT ID, N'مياه', 'SERVICES_WATER' FROM CustodyMainCategories WHERE CategoryCode = 'SITE_SERVICES'
        UNION ALL
        SELECT ID, N'إنترنت', 'SERVICES_INTERNET' FROM CustodyMainCategories WHERE CategoryCode = 'SITE_SERVICES'
      END
    `);

  } catch (error) {

    throw error;
  }
}

// جلب جميع عناصر العهد مع الفلترة
async function getCustodyItems(pool, filters = {}) {
  try {
    await ensureProjectCustodyTables(pool);

    let whereClause = 'WHERE 1=1';
    const request = pool.request();

    // فلترة حسب البند الرئيسي
    if (filters.mainCategoryId) {
      whereClause += ' AND pci.MainCategoryID = @mainCategoryId';
      request.input('mainCategoryId', sql.Int, filters.mainCategoryId);
    }

    // فلترة حسب البند الفرعي
    if (filters.subCategoryId) {
      whereClause += ' AND pci.SubCategoryID = @subCategoryId';
      request.input('subCategoryId', sql.Int, filters.subCategoryId);
    }

    // فلترة حسب نوع العهدة
    if (filters.custodyType) {
      whereClause += ' AND pci.CustodyType = @custodyType';
      request.input('custodyType', sql.NVarChar, filters.custodyType);
    }

    // فلترة حسب الحالة
    if (filters.status) {
      whereClause += ' AND pci.Status = @status';
      request.input('status', sql.NVarChar, filters.status);
    }

    // فلترة حسب التاريخ
    if (filters.startDate) {
      whereClause += ' AND pci.IssueDate >= @startDate';
      request.input('startDate', sql.Date, filters.startDate);
    }

    if (filters.endDate) {
      whereClause += ' AND pci.IssueDate <= @endDate';
      request.input('endDate', sql.Date, filters.endDate);
    }

    // فلترة حسب المستلم
    if (filters.custodian) {
      whereClause += ' AND pci.Custodian LIKE @custodian';
      request.input('custodian', sql.NVarChar, `%${filters.custodian}%`);
    }

    const result = await request.query(`
      SELECT
        pci.ID as id,
        pci.MainCategoryID as mainCategoryId,
        pci.SubCategoryID as subCategoryId,
        pci.CustodyType as custodyType,
        pci.ItemName as itemName,
        pci.Description as description,
        pci.Amount as amount,
        pci.Custodian as custodian,
        pci.CustodianID as custodianId,
        pci.IssueDate as issueDate,
        pci.ExpectedSettlementDate as expectedSettlementDate,
        pci.ActualSettlementDate as actualSettlementDate,
        pci.Status as status,
        pci.SettlementAmount as settlementAmount,
        pci.RemainingAmount as remainingAmount,
        pci.Notes as notes,
        pci.InvoiceNumber as invoiceNumber,
        pci.CreatedAt as createdAt,
        pci.UpdatedAt as updatedAt,
        mc.CategoryName as mainCategoryName,
        mc.CategoryCode as mainCategoryCode,
        sc.SubCategoryName as subCategoryName,
        sc.SubCategoryCode as subCategoryCode,
        (SELECT SUM(ExpenseAmount) FROM CustodyExpenses WHERE CustodyItemID = pci.ID) as totalExpenses,
        (SELECT COUNT(*) FROM CustodySettlements WHERE CustodyItemID = pci.ID) as settlementCount
      FROM ProjectCustodyItems pci
      LEFT JOIN CustodyMainCategories mc ON pci.MainCategoryID = mc.ID
      LEFT JOIN CustodySubCategories sc ON pci.SubCategoryID = sc.ID
      ${whereClause}
      ORDER BY pci.CreatedAt DESC
    `);

    return NextResponse.json({
      success: true,
      data: result.recordset
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب عناصر العهد: ' + error.message
    }, { status: 500 });
  }
}

// إضافة عنصر عهد جديد
async function addCustodyItem(pool, data) {
  try {
    await ensureProjectCustodyTables(pool);

    const result = await pool.request()
      .input('mainCategoryId', sql.Int, data.mainCategoryId)
      .input('subCategoryId', sql.Int, data.subCategoryId || null)
      .input('custodyType', sql.NVarChar, data.custodyType || 'مستديمة')
      .input('itemName', sql.NVarChar, data.itemName)
      .input('description', sql.NVarChar, data.description || '')
      .input('amount', sql.Decimal(18,2), parseFloat(data.amount) || 0)
      .input('custodian', sql.NVarChar, data.custodian)
      .input('custodianId', sql.NVarChar, data.custodianId || '')
      .input('issueDate', sql.Date, data.issueDate || new Date())
      .input('expectedSettlementDate', sql.Date, data.expectedSettlementDate || null)
      .input('notes', sql.NVarChar, data.notes || '')
      .input('invoiceNumber', sql.NVarChar, data.invoiceNumber || '')
      .query(`
        INSERT INTO ProjectCustodyItems (
          MainCategoryID, SubCategoryID, CustodyType, ItemName, Description, Amount,
          Custodian, CustodianID, IssueDate, ExpectedSettlementDate, Notes, InvoiceNumber,
          RemainingAmount
        )
        VALUES (
          @mainCategoryId, @subCategoryId, @custodyType, @itemName, @description, @amount,
          @custodian, @custodianId, @issueDate, @expectedSettlementDate, @notes, @invoiceNumber,
          @amount
        )
      `);

    return NextResponse.json({
      success: true,
      message: 'تم إضافة عنصر العهد بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في إضافة عنصر العهد: ' + error.message
    }, { status: 500 });
  }
}

// جلب البنود الرئيسية
async function getMainCategories(pool) {
  try {
    await ensureProjectCustodyTables(pool);

    const result = await pool.request().query(`
      SELECT
        ID as id,
        CategoryName as categoryName,
        CategoryCode as categoryCode,
        Description as description,
        IsActive as isActive,
        CreatedAt as createdAt
      FROM CustodyMainCategories
      WHERE IsActive = 1
      ORDER BY CategoryName
    `);

    return NextResponse.json({
      success: true,
      data: result.recordset
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب البنود الرئيسية: ' + error.message
    }, { status: 500 });
  }
}

// جلب البنود الفرعية
async function getSubCategories(pool, filters = {}) {
  try {
    await ensureProjectCustodyTables(pool);

    let whereClause = 'WHERE sc.IsActive = 1';
    const request = pool.request();

    if (filters.mainCategoryId) {
      whereClause += ' AND sc.MainCategoryID = @mainCategoryId';
      request.input('mainCategoryId', sql.Int, filters.mainCategoryId);
    }

    const result = await request.query(`
      SELECT
        sc.ID as id,
        sc.MainCategoryID as mainCategoryId,
        sc.SubCategoryName as subCategoryName,
        sc.SubCategoryCode as subCategoryCode,
        sc.Description as description,
        sc.IsActive as isActive,
        sc.CreatedAt as createdAt,
        mc.CategoryName as mainCategoryName
      FROM CustodySubCategories sc
      LEFT JOIN CustodyMainCategories mc ON sc.MainCategoryID = mc.ID
      ${whereClause}
      ORDER BY mc.CategoryName, sc.SubCategoryName
    `);

    return NextResponse.json({
      success: true,
      data: result.recordset
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب البنود الفرعية: ' + error.message
    }, { status: 500 });
  }
}

// إضافة بند رئيسي جديد
async function addMainCategory(pool, data) {
  try {
    await ensureProjectCustodyTables(pool);

    const result = await pool.request()
      .input('categoryName', sql.NVarChar, data.categoryName)
      .input('categoryCode', sql.NVarChar, data.categoryCode || '')
      .input('description', sql.NVarChar, data.description || '')
      .query(`
        INSERT INTO CustodyMainCategories (CategoryName, CategoryCode, Description)
        VALUES (@categoryName, @categoryCode, @description)
      `);

    return NextResponse.json({
      success: true,
      message: 'تم إضافة البند الرئيسي بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في إضافة البند الرئيسي: ' + error.message
    }, { status: 500 });
  }
}

// إضافة بند فرعي جديد
async function addSubCategory(pool, data) {
  try {
    await ensureProjectCustodyTables(pool);

    const result = await pool.request()
      .input('mainCategoryId', sql.Int, data.mainCategoryId)
      .input('subCategoryName', sql.NVarChar, data.subCategoryName)
      .input('subCategoryCode', sql.NVarChar, data.subCategoryCode || '')
      .input('description', sql.NVarChar, data.description || '')
      .query(`
        INSERT INTO CustodySubCategories (MainCategoryID, SubCategoryName, SubCategoryCode, Description)
        VALUES (@mainCategoryId, @subCategoryName, @subCategoryCode, @description)
      `);

    return NextResponse.json({
      success: true,
      message: 'تم إضافة البند الفرعي بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في إضافة البند الفرعي: ' + error.message
    }, { status: 500 });
  }
}

// تسوية العهدة
async function settleCustody(pool, data) {
  try {
    await ensureProjectCustodyTables(pool);

    const transaction = new sql.Transaction(pool);
    await transaction.begin();

    try {
      // إضافة سجل التسوية
      await transaction.request()
        .input('custodyItemId', sql.Int, data.custodyItemId)
        .input('settlementDate', sql.Date, data.settlementDate || new Date())
        .input('settlementAmount', sql.Decimal(18,2), parseFloat(data.settlementAmount))
        .input('receiptNumber', sql.NVarChar, data.receiptNumber || '')
        .input('description', sql.NVarChar, data.description || '')
        .query(`
          INSERT INTO CustodySettlements (
            CustodyItemID, SettlementDate, SettlementAmount, ReceiptNumber, Description
          )
          VALUES (
            @custodyItemId, @settlementDate, @settlementAmount, @receiptNumber, @description
          )
        `);

      // تحديث العهدة
      const updateResult = await transaction.request()
        .input('custodyItemId', sql.Int, data.custodyItemId)
        .input('settlementAmount', sql.Decimal(18,2), parseFloat(data.settlementAmount))
        .input('actualSettlementDate', sql.Date, data.settlementDate || new Date())
        .query(`
          UPDATE ProjectCustodyItems
          SET
            SettlementAmount = SettlementAmount + @settlementAmount,
            RemainingAmount = Amount - (SettlementAmount + @settlementAmount),
            ActualSettlementDate = @actualSettlementDate,
            Status = CASE
              WHEN (Amount - (SettlementAmount + @settlementAmount)) <= 0 THEN N'مُسوّاة'
              ELSE N'مُصرفة'
            END,
            UpdatedAt = GETDATE()
          WHERE ID = @custodyItemId
        `);

      await transaction.commit();

      return NextResponse.json({
        success: true,
        message: 'تم تسوية العهدة بنجاح'
      });

    } catch (error) {
      await transaction.rollback();
      throw error;
    }

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في تسوية العهدة: ' + error.message
    }, { status: 500 });
  }
}

// إضافة مصروف للعهدة
async function addExpense(pool, data) {
  try {
    await ensureProjectCustodyTables(pool);

    const result = await pool.request()
      .input('custodyItemId', sql.Int, data.custodyItemId)
      .input('expenseDate', sql.Date, data.expenseDate || new Date())
      .input('expenseAmount', sql.Decimal(18,2), parseFloat(data.expenseAmount))
      .input('expenseDescription', sql.NVarChar, data.expenseDescription || '')
      .input('receiptNumber', sql.NVarChar, data.receiptNumber || '')
      .input('supplier', sql.NVarChar, data.supplier || '')
      .query(`
        INSERT INTO CustodyExpenses (
          CustodyItemID, ExpenseDate, ExpenseAmount, ExpenseDescription, ReceiptNumber, Supplier
        )
        VALUES (
          @custodyItemId, @expenseDate, @expenseAmount, @expenseDescription, @receiptNumber, @supplier
        )
      `);

    return NextResponse.json({
      success: true,
      message: 'تم إضافة المصروف بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في إضافة المصروف: ' + error.message
    }, { status: 500 });
  }
}

// جلب تفاصيل العهدة
async function getCustodyDetails(pool, id) {
  try {
    await ensureProjectCustodyTables(pool);

    // جلب بيانات العهدة
    const custodyResult = await pool.request()
      .input('id', sql.Int, id)
      .query(`
        SELECT
          pci.*,
          mc.CategoryName as mainCategoryName,
          sc.SubCategoryName as subCategoryName
        FROM ProjectCustodyItems pci
        LEFT JOIN CustodyMainCategories mc ON pci.MainCategoryID = mc.ID
        LEFT JOIN CustodySubCategories sc ON pci.SubCategoryID = sc.ID
        WHERE pci.ID = @id
      `);

    if (custodyResult.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'العهدة غير موجودة'
      }, { status: 404 });
    }

    // جلب التسويات
    const settlementsResult = await pool.request()
      .input('custodyItemId', sql.Int, id)
      .query(`
        SELECT * FROM CustodySettlements
        WHERE CustodyItemID = @custodyItemId
        ORDER BY SettlementDate DESC
      `);

    // جلب المصروفات
    const expensesResult = await pool.request()
      .input('custodyItemId', sql.Int, id)
      .query(`
        SELECT * FROM CustodyExpenses
        WHERE CustodyItemID = @custodyItemId
        ORDER BY ExpenseDate DESC
      `);

    return NextResponse.json({
      success: true,
      data: {
        custody: custodyResult.recordset[0],
        settlements: settlementsResult.recordset,
        expenses: expensesResult.recordset
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب تفاصيل العهدة: ' + error.message
    }, { status: 500 });
  }
}

// تحديث عنصر عهد
async function updateCustodyItem(pool, id, data) {
  try {
    const result = await pool.request()
      .input('id', sql.Int, id)
      .input('itemName', sql.NVarChar, data.itemName)
      .input('itemType', sql.NVarChar, data.itemType || '')
      .input('description', sql.NVarChar, data.description || '')
      .input('quantity', sql.Int, parseInt(data.quantity) || 1)
      .input('unitPrice', sql.Decimal(18,2), parseFloat(data.unitPrice) || 0)
      .input('totalValue', sql.Decimal(18,2), parseFloat(data.totalValue) || 0)
      .input('custodian', sql.NVarChar, data.custodian)
      .input('custodianId', sql.NVarChar, data.custodianId || '')
      .input('receiveDate', sql.Date, data.receiveDate || null)
      .input('expectedReturnDate', sql.Date, data.expectedReturnDate || null)
      .input('actualReturnDate', sql.Date, data.actualReturnDate || null)
      .input('status', sql.NVarChar, data.status || 'مستلم')
      .input('location', sql.NVarChar, data.location || '')
      .input('condition', sql.NVarChar, data.condition || 'جيد')
      .input('notes', sql.NVarChar, data.notes || '')
      .input('invoiceNumber', sql.NVarChar, data.invoiceNumber || '')
      .input('supplier', sql.NVarChar, data.supplier || '')
      .query(`
        UPDATE ProjectCustody SET
          ItemName = @itemName,
          ItemType = @itemType,
          Description = @description,
          Quantity = @quantity,
          UnitPrice = @unitPrice,
          TotalValue = @totalValue,
          Custodian = @custodian,
          CustodianID = @custodianId,
          ReceiveDate = @receiveDate,
          ExpectedReturnDate = @expectedReturnDate,
          ActualReturnDate = @actualReturnDate,
          Status = @status,
          Location = @location,
          ItemCondition = @condition,
          Notes = @notes,
          InvoiceNumber = @invoiceNumber,
          Supplier = @supplier,
          UpdatedAt = GETDATE()
        WHERE ID = @id
      `);

    return NextResponse.json({
      success: true,
      message: 'تم تحديث عنصر العهد بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث عنصر العهد: ' + error.message
    }, { status: 500 });
  }
}

// حذف عنصر عهد
async function deleteCustodyItem(pool, id) {
  try {
    const result = await pool.request()
      .input('id', sql.Int, id)
      .query(`
        DELETE FROM ProjectCustody WHERE ID = @id
      `);

    return NextResponse.json({
      success: true,
      message: 'تم حذف عنصر العهد بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في حذف عنصر العهد: ' + error.message
    }, { status: 500 });
  }
}

// جلب إحصائيات العهد
async function getCustodyStatistics(pool) {
  try {
    await ensureProjectCustodyTable(pool);

    const result = await pool.request().query(`
      SELECT 
        COUNT(*) as totalItems,
        SUM(TotalValue) as totalValue,
        SUM(CASE WHEN Status = N'مستلم' THEN 1 ELSE 0 END) as activeItems,
        SUM(CASE WHEN Status = N'مُسلم' THEN 1 ELSE 0 END) as returnedItems,
        SUM(CASE WHEN Status = N'تالف' THEN 1 ELSE 0 END) as damagedItems,
        SUM(CASE WHEN Status = N'مفقود' THEN 1 ELSE 0 END) as lostItems
      FROM ProjectCustody
    `);

    return NextResponse.json({
      success: true,
      data: result.recordset[0]
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب إحصائيات العهد: ' + error.message
    }, { status: 500 });
  }
}

// تصدير بيانات العهد
async function exportCustodyData(pool) {
  try {
    await ensureProjectCustodyTable(pool);

    const result = await pool.request().query(`
      SELECT * FROM ProjectCustody ORDER BY CreatedAt DESC
    `);

    return NextResponse.json({
      success: true,
      data: result.recordset,
      message: 'تم تصدير بيانات العهد بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في تصدير بيانات العهد: ' + error.message
    }, { status: 500 });
  }
}
