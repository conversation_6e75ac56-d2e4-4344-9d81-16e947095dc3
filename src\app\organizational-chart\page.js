'use client';

import MainLayout from '@/components/MainLayout';
import { useEffect, useState } from 'react';
import { FiDownload, FiFilter, FiMaximize, FiSearch, FiUser, FiUsers, FiZoomIn, FiZoomOut } from 'react-icons/fi';

export default function OrganizationalChart() {
  const [employees, setEmployees] = useState([]);
  const [organizationTree, setOrganizationTree] = useState([]);
  const [hierarchyTable, setHierarchyTable] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState('');
  const [departments, setDepartments] = useState([]);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [viewMode, setViewMode] = useState('chart'); // 'chart' أو 'table'

  // جلب بيانات الموظفين من نظام الهيكل الوظيفي الجديد
  const fetchEmployees = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/organizational-chart-data');
      const data = await response.json();

      if (data.success) {
        setEmployees(data.employees);
        setDepartments(data.departments);
        setOrganizationTree(data.organizationTree);
        setHierarchyTable(data.hierarchyTable || []);
        calculatePositions(data.organizationTree);
      } else {
        setError(data.error || 'فشل في جلب البيانات');
      }
    } catch (error) {
      console.error('خطأ في جلب البيانات:', error);
      setError('حدث خطأ في الاتصال بالخادم');
    } finally {
      setLoading(false);
    }
  };

  // تحديث البيانات
  const refreshData = async () => {
    await fetchEmployees();
  };

  // حساب مواقع العقد في المخطط الهرمي المحسن
  const calculatePositions = (nodes) => {
    if (!nodes || nodes.length === 0) return;

    const CARD_WIDTH = 180;
    const CARD_HEIGHT = 140;
    const HORIZONTAL_SPACING = 80;
    const VERTICAL_SPACING = 120;

    // دالة لحساب العرض المطلوب لعقدة وأطفالها
    const calculateSubtreeWidth = (node) => {
      if (!node.children || node.children.length === 0) {
        return CARD_WIDTH;
      }

      const childrenWidth = node.children.reduce((total, child) => {
        return total + calculateSubtreeWidth(child);
      }, 0) + (node.children.length - 1) * HORIZONTAL_SPACING;

      return Math.max(CARD_WIDTH, childrenWidth);
    };

    // دالة لتعيين المواقع
    const assignPositions = (node, x, y, level = 0) => {
      node.x = x;
      node.y = y;
      node.level = level;

      if (node.children && node.children.length > 0) {
        // حساب العرض الإجمالي للأطفال
        const totalChildrenWidth = node.children.reduce((total, child) => {
          return total + calculateSubtreeWidth(child);
        }, 0) + (node.children.length - 1) * HORIZONTAL_SPACING;

        // نقطة البداية للأطفال
        let currentX = x - totalChildrenWidth / 2;

        node.children.forEach(child => {
          const childWidth = calculateSubtreeWidth(child);
          const childX = currentX + childWidth / 2;
          const childY = y + CARD_HEIGHT + VERTICAL_SPACING;

          assignPositions(child, childX, childY, level + 1);
          currentX += childWidth + HORIZONTAL_SPACING;
        });
      }
    };

    // بدء التخطيط من مدير المنطقة في المنتصف
    if (nodes.length === 1) {
      const rootNode = nodes[0];
      const rootWidth = calculateSubtreeWidth(rootNode);
      const startX = Math.max(rootWidth / 2, 600); // ضمان مساحة كافية
      const startY = 100;

      assignPositions(rootNode, startX, startY, 0);
    } else {
      // في حالة وجود عدة عقد جذرية (لا يجب أن يحدث في هيكلنا)
      let currentX = 300;
      nodes.forEach(node => {
        assignPositions(node, currentX, 100, 0);
        currentX += calculateSubtreeWidth(node) + HORIZONTAL_SPACING;
      });
    }
  };

  // إنشاء صورة افتراضية للموظف
  const generateAvatar = (name) => {
    const initials = name.split(' ').map(n => n[0]).join('').substring(0, 2);
    const colors = [
      'bg-red-500', 'bg-blue-500', 'bg-green-500', 'bg-yellow-500',
      'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500'
    ];
    const colorIndex = name.charCodeAt(0) % colors.length;
    return { initials, color: colors[colorIndex] };
  };

  // مكون بطاقة الموظف المحسن
  const EmployeeCard = ({ employee, onClick }) => {
    const avatar = generateAvatar(employee.EmployeeName);
    const isSelected = selectedEmployee?.EmployeeCode === employee.EmployeeCode;

    // تحديد لون البطاقة حسب المستوى الوظيفي
    const getCardStyle = (jobTitle) => {
      if (jobTitle.includes('مدير المنطقة') || jobTitle.includes('Area Manager')) {
        return 'border-red-500 bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900 dark:to-red-800';
      }
      if (jobTitle.includes('مدير عام') || jobTitle.includes('General Manager')) {
        return 'border-purple-500 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900 dark:to-purple-800';
      }
      if (jobTitle.includes('مدير') || jobTitle.includes('Manager')) {
        return 'border-blue-500 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900 dark:to-blue-800';
      }
      if (jobTitle.includes('رئيس') || jobTitle.includes('Head')) {
        return 'border-teal-500 bg-gradient-to-br from-teal-50 to-teal-100 dark:from-teal-900 dark:to-teal-800';
      }
      return 'border-gray-300 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600';
    };

    return (
      <div
        className={`absolute cursor-pointer transition-all duration-300 hover:scale-105 ${
          isSelected ? 'ring-4 ring-yellow-400 shadow-2xl' : ''
        }`}
        style={{
          left: `${employee.x - 88}px`,
          top: `${employee.y}px`,
          transform: `scale(${zoomLevel})`,
          transformOrigin: 'center'
        }}
        onClick={() => onClick(employee)}
      >
        <div className={`w-44 border-2 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 ${getCardStyle(employee.JobTitle)}`}>

          {/* صورة الموظف */}
          <div className="flex justify-center pt-3 mb-2">
            <div className={`w-12 h-12 rounded-full ${avatar.color} flex items-center justify-center text-white font-bold text-sm shadow-lg border-3 border-white`}>
              {avatar.initials}
            </div>
          </div>

          {/* معلومات الموظف */}
          <div className="px-3 pb-3 text-center">
            {/* الاسم */}
            <h3 className={`font-bold text-xs mb-1 ${
              isDarkMode ? 'text-white' : 'text-gray-900'
            }`}>
              {employee.EmployeeName}
            </h3>

            {/* المسمى الوظيفي */}
            <p className={`text-xs font-medium ${
              isDarkMode ? 'text-gray-200' : 'text-gray-700'
            }`}>
              {employee.JobTitle}
            </p>

            {/* عدد المرؤوسين */}
            {employee.children && employee.children.length > 0 && (
              <div className={`mt-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                isDarkMode ? 'bg-gray-700 text-gray-200' : 'bg-white text-gray-700'
              } shadow-sm`}>
                <FiUsers className="w-3 h-3 mr-1" />
                {employee.children.length}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // رسم الخطوط الرابطة المحسنة
  const renderConnections = (nodes) => {
    const connections = [];
    const CARD_HEIGHT = 140;

    const addConnections = (node) => {
      if (node.children && node.children.length > 0) {
        const parentBottomY = (node.y + CARD_HEIGHT - 20) * zoomLevel;
        const childrenTopY = (node.children[0].y - 30) * zoomLevel;
        const middleY = parentBottomY + (childrenTopY - parentBottomY) / 2;

        // خط عمودي من الوالد إلى النقطة الوسطى
        connections.push(
          <line
            key={`parent-${node.EmployeeCode}`}
            x1={node.x * zoomLevel}
            y1={parentBottomY}
            x2={node.x * zoomLevel}
            y2={middleY}
            stroke={isDarkMode ? '#475569' : '#6b7280'}
            strokeWidth="3"
            strokeLinecap="round"
          />
        );

        // خط أفقي يربط جميع الأطفال (إذا كان هناك أكثر من طفل واحد)
        if (node.children.length > 1) {
          const firstChild = node.children[0];
          const lastChild = node.children[node.children.length - 1];

          connections.push(
            <line
              key={`horizontal-${node.EmployeeCode}`}
              x1={firstChild.x * zoomLevel}
              y1={middleY}
              x2={lastChild.x * zoomLevel}
              y2={middleY}
              stroke={isDarkMode ? '#475569' : '#6b7280'}
              strokeWidth="3"
              strokeLinecap="round"
            />
          );
        }

        // خطوط عمودية من النقطة الوسطى إلى كل طفل
        node.children.forEach(child => {
          connections.push(
            <line
              key={`child-${child.EmployeeCode}`}
              x1={child.x * zoomLevel}
              y1={middleY}
              x2={child.x * zoomLevel}
              y2={childrenTopY}
              stroke={isDarkMode ? '#475569' : '#6b7280'}
              strokeWidth="3"
              strokeLinecap="round"
            />
          );

          // إضافة نقطة في نهاية كل خط للتأكيد البصري
          connections.push(
            <circle
              key={`dot-${child.EmployeeCode}`}
              cx={child.x * zoomLevel}
              cy={childrenTopY}
              r="4"
              fill={isDarkMode ? '#64748b' : '#9ca3af'}
            />
          );

          // استدعاء متكرر للأطفال
          addConnections(child);
        });

        // نقطة في نقطة التفرع
        connections.push(
          <circle
            key={`junction-${node.EmployeeCode}`}
            cx={node.x * zoomLevel}
            cy={middleY}
            r="5"
            fill={isDarkMode ? '#3b82f6' : '#2563eb'}
          />
        );
      }
    };

    nodes.forEach(addConnections);
    return connections;
  };

  // رسم جميع الموظفين
  const renderAllEmployees = (nodes) => {
    const allEmployees = [];

    const addEmployee = (node) => {
      allEmployees.push(node);
      if (node.children) {
        node.children.forEach(addEmployee);
      }
    };

    nodes.forEach(addEmployee);
    return allEmployees;
  };

  useEffect(() => {
    fetchEmployees();
  }, []);

  // حساب أبعاد المخطط المحسنة
  const getChartDimensions = () => {
    const allEmployees = renderAllEmployees(organizationTree);
    if (allEmployees.length === 0) return { width: 1600, height: 1000 };

    const positions = allEmployees.map(emp => ({ x: emp.x || 0, y: emp.y || 0 }));
    const minX = Math.min(...positions.map(pos => pos.x)) - 200;
    const maxX = Math.max(...positions.map(pos => pos.x)) + 200;
    const minY = Math.min(...positions.map(pos => pos.y)) - 100;
    const maxY = Math.max(...positions.map(pos => pos.y)) + 300;

    const width = (maxX - minX) * zoomLevel;
    const height = (maxY - minY) * zoomLevel;

    return {
      width: Math.max(width, 1600),
      height: Math.max(height, 1000)
    };
  };

  const chartDimensions = getChartDimensions();

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto p-6">
        {/* العنوان */}
        <div className="mb-8">
          <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
            🏢 الهيكل الوظيفي للشركة
          </h1>
          <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            عرض شامل للهيكل التنظيمي بدءاً من مدير المنطقة وصولاً إلى جميع رؤساء الأقسام والموظفين
          </p>
          <div className={`mt-4 p-4 rounded-lg ${isDarkMode ? 'bg-slate-700' : 'bg-blue-50'}`}>
            <p className={`text-sm ${isDarkMode ? 'text-blue-300' : 'text-blue-700'}`}>
              💡 <strong>ملاحظة:</strong> البيانات محدثة تلقائياً من نظام الهيكل الوظيفي الجديد
            </p>
          </div>
        </div>

        {/* أدوات التحكم */}
        <div className={`${isDarkMode ? 'bg-slate-800' : 'bg-white'} rounded-xl p-6 shadow-lg mb-6`}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* البحث */}
            <div className="relative">
              <FiSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="البحث بالاسم أو المسمى الوظيفي..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`w-full pr-10 pl-4 py-3 border rounded-lg ${
                  isDarkMode
                    ? 'bg-slate-700 border-slate-600 text-white'
                    : 'bg-white border-gray-300'
                }`}
              />
            </div>

            {/* تصفية القسم */}
            <div className="relative">
              <FiFilter className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className={`w-full pr-10 pl-4 py-3 border rounded-lg ${
                  isDarkMode
                    ? 'bg-slate-700 border-slate-600 text-white'
                    : 'bg-white border-gray-300'
                }`}
              >
                <option value="">جميع الأقسام</option>
                {departments.map(dept => (
                  <option key={dept} value={dept}>{dept}</option>
                ))}
              </select>
            </div>

            {/* أدوات التحكم في المخطط */}
            <div className="flex gap-2 flex-wrap">
              {/* أزرار التبديل بين العرض الشجري والجدولي */}
              <div className="flex bg-gray-200 dark:bg-slate-700 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('chart')}
                  className={`px-4 py-2 rounded-md transition-colors ${
                    viewMode === 'chart'
                      ? 'bg-blue-500 text-white'
                      : 'text-gray-600 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-slate-600'
                  }`}
                >
                  🌳 العرض الشجري
                </button>
                <button
                  onClick={() => setViewMode('table')}
                  className={`px-4 py-2 rounded-md transition-colors ${
                    viewMode === 'table'
                      ? 'bg-blue-500 text-white'
                      : 'text-gray-600 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-slate-600'
                  }`}
                >
                  📋 العرض الجدولي
                </button>
              </div>

              {viewMode === 'chart' && (
                <>
                  <button
                    onClick={() => setZoomLevel(Math.min(zoomLevel + 0.1, 2))}
                    className="flex items-center gap-2 bg-blue-500 hover:bg-blue-600 text-white py-3 px-4 rounded-lg transition-colors"
                  >
                    <FiZoomIn className="w-4 h-4" />
                    تكبير
                  </button>
                  <button
                    onClick={() => setZoomLevel(Math.max(zoomLevel - 0.1, 0.5))}
                    className="flex items-center gap-2 bg-orange-500 hover:bg-orange-600 text-white py-3 px-4 rounded-lg transition-colors"
                  >
                    <FiZoomOut className="w-4 h-4" />
                    تصغير
                  </button>
                  <button
                    onClick={() => setZoomLevel(1)}
                    className="flex items-center gap-2 bg-gray-500 hover:bg-gray-600 text-white py-3 px-4 rounded-lg transition-colors"
                  >
                    <FiMaximize className="w-4 h-4" />
                    الحجم الطبيعي
                  </button>
                </>
              )}

              <button
                onClick={refreshData}
                disabled={loading}
                className="flex items-center gap-2 bg-purple-500 hover:bg-purple-600 disabled:bg-purple-300 text-white py-3 px-4 rounded-lg transition-colors"
              >
                <FiDownload className="w-4 h-4" />
                {loading ? 'جاري التحديث...' : 'تحديث البيانات'}
              </button>
            </div>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
          <div className={`${isDarkMode ? 'bg-slate-800' : 'bg-white'} rounded-lg p-4 shadow`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>إجمالي الموظفين</p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {employees.length}
                </p>
              </div>
              <FiUsers className="w-8 h-8 text-blue-500" />
            </div>
          </div>

          <div className={`${isDarkMode ? 'bg-slate-800' : 'bg-white'} rounded-lg p-4 shadow`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>عدد الأقسام</p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {departments.length}
                </p>
              </div>
              <FiFilter className="w-8 h-8 text-indigo-500" />
            </div>
          </div>

          <div className={`${isDarkMode ? 'bg-slate-800' : 'bg-white'} rounded-lg p-4 shadow`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>المديرين</p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {employees.filter(emp => emp.JobTitle.includes('مدير')).length}
                </p>
              </div>
              <FiUser className="w-8 h-8 text-purple-500" />
            </div>
          </div>

          <div className={`${isDarkMode ? 'bg-slate-800' : 'bg-white'} rounded-lg p-4 shadow`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>المستويات</p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {Math.max(...employees.map(emp => emp.level || 0)) + 1}
                </p>
              </div>
              <FiDownload className="w-8 h-8 text-orange-500" />
            </div>
          </div>

          <div className={`${isDarkMode ? 'bg-slate-800' : 'bg-white'} rounded-lg p-4 shadow`}>
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>سجلات الجدول</p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {hierarchyTable.length}
                </p>
              </div>
              <FiFilter className="w-8 h-8 text-green-500" />
            </div>
          </div>
        </div>

        {/* المخطط التنظيمي البصري أو الجدول */}
        <div className={`${isDarkMode ? 'bg-slate-800' : 'bg-white'} rounded-xl shadow-lg overflow-hidden`}>
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-slate-600">
            <h2 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              {viewMode === 'chart' ? 'المخطط التنظيمي' : 'جدول الهيكل الإداري'}
            </h2>
            <div className="flex items-center gap-4">
              {viewMode === 'chart' && (
                <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  مستوى التكبير: {Math.round(zoomLevel * 100)}%
                </span>
              )}
              {selectedEmployee && (
                <div className={`text-sm px-3 py-1 rounded-full ${
                  isDarkMode ? 'bg-blue-900 text-blue-300' : 'bg-blue-100 text-blue-800'
                }`}>
                  محدد: {selectedEmployee.EmployeeName}
                </div>
              )}
            </div>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-20">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
              <span className={`mr-3 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                جاري تحميل الهيكل الوظيفي...
              </span>
            </div>
          ) : error ? (
            <div className="text-center py-20">
              <p className="text-red-500 mb-4">❌ {error}</p>
              <button
                onClick={fetchEmployees}
                className="bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg"
              >
                إعادة المحاولة
              </button>
            </div>
          ) : viewMode === 'table' ? (
            // عرض الجدول الهرمي
            <div className="overflow-x-auto">
              <table className={`w-full ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                <thead className={`${isDarkMode ? 'bg-slate-700' : 'bg-gray-50'}`}>
                  <tr>
                    <th className="px-4 py-3 text-right text-sm font-medium">مسلسل</th>
                    <th className="px-4 py-3 text-right text-sm font-medium">رقم الموظف</th>
                    <th className="px-4 py-3 text-right text-sm font-medium">اسم الموظف</th>
                    <th className="px-4 py-3 text-right text-sm font-medium">الوظيفة</th>
                    <th className="px-4 py-3 text-right text-sm font-medium">كود المدير المباشر 1</th>
                    <th className="px-4 py-3 text-right text-sm font-medium">المدير المباشر 1</th>
                    <th className="px-4 py-3 text-right text-sm font-medium">كود المدير المباشر 2</th>
                    <th className="px-4 py-3 text-right text-sm font-medium">المدير المباشر 2</th>
                    <th className="px-4 py-3 text-right text-sm font-medium">كود المدير المباشر 3</th>
                    <th className="px-4 py-3 text-right text-sm font-medium">المدير المباشر 3</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-slate-600">
                  {hierarchyTable
                    .filter(row => {
                      const matchesSearch = !searchTerm ||
                        row.employeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        row.jobTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        row.employeeCode.includes(searchTerm);
                      return matchesSearch;
                    })
                    .map((row, index) => (
                      <tr
                        key={row.employeeCode}
                        className={`hover:bg-gray-50 dark:hover:bg-slate-700 cursor-pointer ${
                          selectedEmployee?.EmployeeCode === row.employeeCode
                            ? 'bg-blue-50 dark:bg-blue-900'
                            : ''
                        }`}
                        onClick={() => {
                          const emp = employees.find(e => e.EmployeeCode === row.employeeCode);
                          setSelectedEmployee(emp);
                        }}
                      >
                        <td className="px-4 py-3 text-sm">{row.serial}</td>
                        <td className="px-4 py-3 text-sm font-medium">{row.employeeCode}</td>
                        <td className="px-4 py-3 text-sm">{row.employeeName}</td>
                        <td className="px-4 py-3 text-sm">{row.jobTitle}</td>
                        <td className="px-4 py-3 text-sm">{row.manager1Code}</td>
                        <td className="px-4 py-3 text-sm">{row.manager1Name}</td>
                        <td className="px-4 py-3 text-sm">{row.manager2Code}</td>
                        <td className="px-4 py-3 text-sm">{row.manager2Name}</td>
                        <td className="px-4 py-3 text-sm">{row.manager3Code}</td>
                        <td className="px-4 py-3 text-sm">{row.manager3Name}</td>
                      </tr>
                    ))
                  }
                </tbody>
              </table>
            </div>
          ) : organizationTree.length === 0 ? (
            <div className="text-center py-20">
              <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                لا توجد بيانات للعرض
              </p>
            </div>
          ) : (
            <div className="relative overflow-auto" style={{ height: '70vh' }}>
              <div
                className="relative"
                style={{
                  width: `${chartDimensions.width}px`,
                  height: `${chartDimensions.height}px`,
                  minWidth: '100%',
                  minHeight: '100%'
                }}
              >
                {/* رسم الخطوط الرابطة */}
                <svg
                  className="absolute top-0 left-0 w-full h-full pointer-events-none"
                  style={{ zIndex: 1 }}
                >
                  {renderConnections(organizationTree)}
                </svg>

                {/* عرض بطاقات الموظفين */}
                <div className="relative" style={{ zIndex: 2 }}>
                  {renderAllEmployees(organizationTree)
                    .filter(emp => {
                      const matchesSearch = !searchTerm ||
                        emp.EmployeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        emp.JobTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        emp.EmployeeCode.includes(searchTerm);

                      const matchesDepartment = !selectedDepartment ||
                        emp.Department === selectedDepartment;

                      return matchesSearch && matchesDepartment;
                    })
                    .map(employee => (
                      <EmployeeCard
                        key={employee.EmployeeCode}
                        employee={employee}
                        onClick={setSelectedEmployee}
                      />
                    ))
                  }
                </div>
              </div>
            </div>
          )}
        </div>

        {/* معلومات الموظف المحدد */}
        {selectedEmployee && (
          <div className={`${isDarkMode ? 'bg-slate-800' : 'bg-white'} rounded-xl p-6 shadow-lg mt-6`}>
            <div className="flex items-center justify-between mb-4">
              <h3 className={`text-lg font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                معلومات الموظف
              </h3>
              <button
                onClick={() => setSelectedEmployee(null)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>الاسم</p>
                <p className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {selectedEmployee.EmployeeName}
                </p>
              </div>
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>المسمى الوظيفي</p>
                <p className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {selectedEmployee.JobTitle}
                </p>
              </div>
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>القسم</p>
                <p className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {selectedEmployee.Department}
                </p>
              </div>
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>كود الموظف</p>
                <p className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {selectedEmployee.EmployeeCode}
                </p>
              </div>
              {selectedEmployee.DirectManager && (
                <div>
                  <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>المدير المباشر</p>
                  <p className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {selectedEmployee.DirectManager}
                  </p>
                </div>
              )}
              {selectedEmployee.Mobile && (
                <div>
                  <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>رقم الهاتف</p>
                  <p className={`font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {selectedEmployee.Mobile}
                  </p>
                </div>
              )}
            </div>

            {selectedEmployee.children && selectedEmployee.children.length > 0 && (
              <div className="mt-4">
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'} mb-2`}>
                  المرؤوسين ({selectedEmployee.children.length})
                </p>
                <div className="flex flex-wrap gap-2">
                  {selectedEmployee.children.map(child => (
                    <span
                      key={child.EmployeeCode}
                      className={`px-3 py-1 rounded-full text-sm cursor-pointer transition-colors ${
                        isDarkMode
                          ? 'bg-slate-700 text-gray-300 hover:bg-slate-600'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                      onClick={() => setSelectedEmployee(child)}
                    >
                      {child.EmployeeName}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </MainLayout>
  );
}
