'use client';

import MainLayout from '@/components/MainLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { FiArrowLeft, FiDownload, FiMoon, FiPrinter, FiSave } from 'react-icons/fi';

const NightShiftRequestPage = () => {
  const router = useRouter();
  const { isArabic } = useLanguage();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    employeeName: '',
    employeeId: '',
    department: '',
    jobTitle: '',
    nationalId: '',
    shiftDate: '',
    startTime: '',
    endTime: '',
    totalHours: '',
    reason: '',
    workLocation: '',
    supervisorName: '',
    emergencyContact: '',
    notes: ''
  });

  // جلب بيانات الموظف المسجل
  useEffect(() => {
    const userInfo = localStorage.getItem('userInfo');
    if (userInfo) {
      const user = JSON.parse(userInfo);
      setFormData(prev => ({
        ...prev,
        employeeName: user.fullName || user.username,
        employeeId: user.employeeId || '',
        department: user.department || '',
        jobTitle: user.jobTitle || ''
      }));
    }
  }, []);

  // حساب عدد الساعات
  useEffect(() => {
    if (formData.startTime && formData.endTime) {
      const start = new Date(`2000-01-01T${formData.startTime}`);
      const end = new Date(`2000-01-01T${formData.endTime}`);
      let diffMs = end - start;

      // إذا كانت النهاية قبل البداية، فهذا يعني أن النوبة تمتد لليوم التالي
      if (diffMs < 0) {
        diffMs += 24 * 60 * 60 * 1000; // إضافة 24 ساعة
      }

      const diffHours = diffMs / (1000 * 60 * 60);
      setFormData(prev => ({ ...prev, totalHours: diffHours.toFixed(1) }));
    }
  }, [formData.startTime, formData.endTime]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch('/api/paper-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'create',
          requestType: 'night_shift',
          nightShiftDate: formData.shiftDate,
          nightShiftReason: formData.reason,
          ...formData
        }),
      });

      const result = await response.json();

      if (result.success) {
        alert(isArabic ? 'تم تقديم إخطار الوردية الليلية بنجاح!' : 'Night shift notification submitted successfully!');
        // لا نوجه المستخدم تلقائياً - يبقى في نفس الصفحة
      } else {
        alert(result.error || (isArabic ? 'خطأ في تقديم الإخطار' : 'Error submitting notification'));
      }
    } catch (error) {

      alert(isArabic ? 'خطأ في الاتصال بالخادم' : 'Server connection error');
    } finally {
      setLoading(false);
    }
  };

  const downloadForm = async () => {
    try {
      const response = await fetch('/api/requests/download-form?type=night-shift');
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'نموذج_طلب_النوبة_الليلية.xlsx';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {

    }
  };

  // تحويل اللوجو إلى base64
  const getLogoBase64 = async () => {
    try {
      const response = await fetch('/logo.png');
      const blob = await response.blob();
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result.split(',')[1]);
        reader.readAsDataURL(blob);
      });
    } catch (error) {

      return '';
    }
  };

  // طباعة نموذج النوبة الليلية المملوء
  const printFilledForm = async () => {
    // تحميل اللوجو أولاً قبل فتح نافذة الطباعة
    const logoBase64 = await getLogoBase64();

    // انتظار إضافي للتأكد من تحميل اللوجو
    await new Promise(resolve => setTimeout(resolve, 1000));

    const printWindow = window.open('', '_blank');

    const printContent = `
      <!DOCTYPE html>
      <html dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>نموذج طلب نوبة ليلية - ${formData.employeeName}</title>
        <style>
          @page {
            size: A4;
            margin: 15mm;
          }
          body {
            font-family: 'Arial', sans-serif;
            font-size: 11px;
            line-height: 1.2;
            color: #000;
            margin: 0;
            padding: 0;
            direction: rtl;
            background: white;
          }

          .header {
            display: table;
            width: 100%;
            border: 2px solid #000;
            margin-bottom: 15px;
            border-collapse: collapse;
          }
          .header-row {
            display: table-row;
          }
          .logo-section {
            display: table-cell;
            width: 33.33%;
            border-left: 1px solid #000;
            text-align: center;
            vertical-align: middle;
            padding: 10px;
          }
          .logo-img {
            max-width: 100px;
            max-height: 70px;
            object-fit: contain;
          }
          .form-code {
            display: table-cell;
            width: 33.33%;
            border-left: 1px solid #000;
            text-align: center;
            vertical-align: middle;
            padding: 10px;
          }
          .form-code-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
          }
          .form-code-number {
            font-size: 12px;
            color: #666;
          }
          .company-info {
            display: table-cell;
            width: 33.33%;
            text-align: center;
            vertical-align: middle;
            padding: 10px;
          }
          .company-name-ar {
            font-size: 13px;
            font-weight: bold;
            margin-bottom: 3px;
          }
          .company-name-en {
            font-size: 10px;
            color: #666;
          }

          .form-title {
            background-color: #a8c5f0;
            text-align: center;
            padding: 8px;
            font-size: 14px;
            font-weight: bold;
            border: 1px solid #000;
            margin-bottom: 15px;
          }

          .form-row {
            display: flex;
            margin-bottom: 12px;
            align-items: center;
          }
          .field-group {
            display: flex;
            align-items: center;
            margin-left: 30px;
          }
          .field-label {
            font-weight: bold;
            margin-left: 8px;
            min-width: 80px;
          }
          .field-value {
            border-bottom: 1px solid #000;
            min-width: 150px;
            padding: 2px 5px;
            font-weight: normal;
          }

          .signatures {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
          }
          .signature-box {
            width: 150px;
            text-align: center;
          }
          .signature-title {
            font-weight: bold;
            margin-bottom: 30px;
            font-size: 10px;
          }
          .signature-line {
            border-bottom: 1px solid #000;
            height: 1px;
            margin-bottom: 20px;
          }

          @media print {
            body { margin: 0; padding: 10px; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="header-row">
            <div class="company-info">
              <div class="company-name-ar">شركة كونكورد للهندسة والمقاولات</div>
              <div class="company-name-en">Concord for Engineering & Contracting</div>
            </div>
            <div class="form-code">
              <div class="form-code-title">إخطار وردية ليلية</div>
              <div class="form-code-number">HR-OP-05-F01</div>
            </div>
            <div class="logo-section">
              ${logoBase64 ?
                `<img src="data:image/png;base64,${logoBase64}" alt="Concord Logo" class="logo-img" />` :
                `<div style="background:#1e40af;color:white;padding:10px;text-align:center;font-weight:bold;font-size:10px;">CONCORD<br>COMPANY</div>`
              }
            </div>
          </div>
        </div>

        <!-- Employee Info Section -->
        <div style="margin-bottom: 20px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
            <div style="display: flex; align-items: center;">
              <span style="font-weight: bold; margin-left: 10px;">كود الوظيفي</span>
              <div style="border-bottom: 1px solid #000; width: 100px; text-align: center; padding: 3px;">
                ${formData.employeeId || '5528'}
              </div>
            </div>
            <div style="display: flex; align-items: center;">
              <span style="font-weight: bold; margin-left: 10px;">التاريخ</span>
              <div style="border-bottom: 1px solid #000; width: 120px; text-align: center; padding: 3px;">
                ${new Date().toLocaleDateString('ar-EG').replace(/\//g, '-')}
              </div>
            </div>
            <div style="display: flex; align-items: center; flex: 1; margin: 0 20px;">
              <span style="font-weight: bold; margin-left: 10px;">الاسم</span>
              <div style="border-bottom: 1px solid #000; flex: 1; text-align: center; padding: 3px;">
                ${formData.employeeName || 'عمر مصطفى محمد أحمد'}
              </div>
            </div>
          </div>
        </div>

        <div style="margin-bottom: 20px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
            <div style="display: flex; align-items: center; flex: 1;">
              <span style="font-weight: bold; margin-left: 10px;">إدارة/مشروع</span>
              <div style="border-bottom: 1px solid #000; flex: 1; text-align: center; padding: 3px;">
                ${formData.department || 'مشروع أوجيستا'}
              </div>
            </div>
            <div style="display: flex; align-items: center; flex: 1; margin-left: 20px;">
              <span style="font-weight: bold; margin-left: 10px;">الوظيفة</span>
              <div style="border-bottom: 1px solid #000; flex: 1; text-align: center; padding: 3px;">
                ${formData.jobTitle || 'مهندس برن جودة'}
              </div>
            </div>
          </div>
        </div>

        <!-- Night Shift Details Section -->
        <div style="border: 2px solid #000; margin: 20px 0; padding: 15px;">
          <div style="font-weight: bold; text-align: center; margin-bottom: 15px; font-size: 14px;">
            تفاصيل الوردية الليلية
          </div>

          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <div style="display: flex; align-items: center; gap: 10px;">
              <span style="font-weight: bold;">من الساعة:</span>
              <span style="border-bottom: 2px solid #000; padding: 5px 10px; min-width: 80px; text-align: center;">
                ${formData.startTime || ''}
              </span>
            </div>
            <div style="display: flex; align-items: center; gap: 10px;">
              <span style="font-weight: bold;">إلى الساعة:</span>
              <span style="border-bottom: 2px solid #000; padding: 5px 10px; min-width: 80px; text-align: center;">
                ${formData.endTime || ''}
              </span>
            </div>
            <div style="display: flex; align-items: center; gap: 10px;">
              <span style="font-weight: bold;">تاريخ الوردية:</span>
              <span style="border-bottom: 2px solid #000; padding: 5px 10px; min-width: 100px; text-align: center;">
                ${formData.shiftDate ? new Date(formData.shiftDate).toLocaleDateString('ar-EG') : new Date().toLocaleDateString('ar-EG')}
              </span>
            </div>
          </div>

          <div style="margin-bottom: 15px;">
            <div style="font-weight: bold; margin-bottom: 10px;">طبيعة العمل المطلوب:</div>
            <div style="border: 2px solid #000; min-height: 80px; padding: 15px; line-height: 1.6;">
              ${formData.reason || formData.nightShiftReason || ''}
            </div>
          </div>

          <div style="border-top: 1px solid #000; padding-top: 15px;">
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <div style="display: flex; align-items: center; gap: 20px;">
                <div style="display: flex; align-items: center; gap: 8px;">
                  <span style="border: 2px solid #000; width: 15px; height: 15px; display: inline-block; text-align: center; line-height: 11px; ${formData.transportMethod === 'company' ? 'background: #000; color: white;' : ''}">
                    ${formData.transportMethod === 'company' ? '✓' : ''}
                  </span>
                  <span>سيارة خدمة الموقع</span>
                </div>
                <div style="display: flex; align-items: center; gap: 8px;">
                  <span style="border: 2px solid #000; width: 15px; height: 15px; display: inline-block; text-align: center; line-height: 11px; ${formData.transportMethod === 'personal' ? 'background: #000; color: white;' : ''}">
                    ${formData.transportMethod === 'personal' ? '✓' : ''}
                  </span>
                  <span>مواصلات شخصية</span>
                </div>
              </div>
              <div style="font-weight: bold;">
                وسيلة النقل:
              </div>
            </div>
          </div>
        </div>

        <!-- Signatures Section -->
        <div style="margin-top: 40px; border: 1px solid #000; padding: 15px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
            <div style="text-align: center; width: 45%;">
              <div style="font-weight: bold; margin-bottom: 30px;">توقيع الموظف</div>
              <div style="border-bottom: 1px solid #000; width: 150px; margin: 0 auto;"></div>
            </div>
            <div style="text-align: center; width: 45%;">
              <div style="font-weight: bold; margin-bottom: 30px;">اعتماد الرئيس المباشر</div>
              <div style="border-bottom: 1px solid #000; width: 150px; margin: 0 auto;"></div>
            </div>
          </div>

          <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
            <div style="text-align: center; width: 45%;">
              <div style="font-weight: bold; margin-bottom: 30px;">المدير الإداري</div>
              <div style="border-bottom: 1px solid #000; width: 150px; margin: 0 auto;"></div>
            </div>
            <div style="text-align: center; width: 45%;">
              <div style="font-weight: bold; margin-bottom: 30px;">اعتماد مدير المشروع</div>
              <div style="border-bottom: 1px solid #000; width: 150px; margin: 0 auto;"></div>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();

    // انتظار تحميل المحتوى والصور قبل الطباعة
    printWindow.onload = () => {
      setTimeout(() => {
        printWindow.print();
      }, 500);
    };

    // احتياطي في حالة عدم تشغيل onload
    setTimeout(() => {
      printWindow.print();
    }, 1500);
  };

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-4xl mx-auto">
          {/* العنوان */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6 pulse-box">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-200 mb-2">
                  {isArabic ? 'طلب نوبة ليلية' : 'Night Shift Request'}
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  {isArabic ? 'تقديم طلب نوبة ليلية جديد' : 'Submit a new night shift request'}
                </p>
              </div>
              <div className="flex gap-3">
                <button
                  onClick={downloadForm}
                  className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors flex items-center gap-2"
                >
                  <FiDownload />
                  {isArabic ? 'تحميل النموذج' : 'Download Form'}
                </button>
                <button
                  onClick={printFilledForm}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
                >
                  <FiPrinter />
                  {isArabic ? 'طباعة النموذج' : 'Print Form'}
                </button>
                <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                  <FiMoon className="text-2xl text-white" />
                </div>
              </div>
            </div>
          </div>

          {/* النموذج */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 gentle-animated-box">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* بيانات الموظف */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'اسم الموظف' : 'Employee Name'}
                  </label>
                  <input
                    type="text"
                    name="employeeName"
                    value={formData.employeeName}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'رقم الموظف' : 'Employee ID'}
                  </label>
                  <input
                    type="text"
                    name="employeeId"
                    value={formData.employeeId}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    autoComplete="off"
                    autoCorrect="off"
                    autoCapitalize="off"
                    spellCheck="false"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'القسم' : 'Department'}
                  </label>
                  <input
                    type="text"
                    name="department"
                    value={formData.department}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'المسمى الوظيفي' : 'Job Title'}
                  </label>
                  <input
                    type="text"
                    name="jobTitle"
                    value={formData.jobTitle}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    required
                  />
                </div>
              </div>

              {/* تفاصيل النوبة */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
                  {isArabic ? 'تفاصيل النوبة الليلية' : 'Night Shift Details'}
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {isArabic ? 'تاريخ النوبة' : 'Shift Date'}
                    </label>
                    <input
                      type="date"
                      name="shiftDate"
                      value={formData.shiftDate}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {isArabic ? 'موقع العمل' : 'Work Location'}
                    </label>
                    <input
                      type="text"
                      name="workLocation"
                      value={formData.workLocation}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      placeholder={isArabic ? 'مثال: الموقع الرئيسي، المشروع الجديد...' : 'e.g., Main Site, New Project...'}
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {isArabic ? 'من الساعة' : 'From Time'}
                    </label>
                    <input
                      type="time"
                      name="startTime"
                      value={formData.startTime}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {isArabic ? 'إلى الساعة' : 'To Time'}
                    </label>
                    <input
                      type="time"
                      name="endTime"
                      value={formData.endTime}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      required
                    />
                  </div>
                </div>

                {formData.totalHours && (
                  <div className="mt-4 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                    <p className="text-sm text-purple-700 dark:text-purple-300">
                      {isArabic ? `إجمالي ساعات النوبة: ${formData.totalHours} ساعة` : `Total Shift Hours: ${formData.totalHours} hours`}
                    </p>
                  </div>
                )}

                <div className="mt-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'طبيعة العمل المطلوب' : 'Nature of Work Required'}
                  </label>
                  <textarea
                    name="reason"
                    value={formData.reason}
                    onChange={handleInputChange}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    placeholder={isArabic ? 'اكتب طبيعة العمل المطلوب أداؤه في النوبة الليلية...' : 'Describe the nature of work to be performed during night shift...'}
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'اسم المشرف' : 'Supervisor Name'}
                  </label>
                  <input
                    type="text"
                    name="supervisorName"
                    value={formData.supervisorName}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    placeholder={isArabic ? 'اسم المشرف المسؤول عن النوبة' : 'Name of supervisor responsible for the shift'}
                  />
                </div>
              </div>

              {/* الأزرار */}
              <div className="flex gap-4 pt-6 border-t">
                <button
                  type="button"
                  onClick={() => router.push('/requests')}
                  className="flex items-center gap-2 px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700 transition-colors"
                >
                  <FiArrowLeft />
                  {isArabic ? 'رجوع' : 'Back'}
                </button>

                <button
                  type="submit"
                  disabled={loading}
                  className="flex items-center gap-2 px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <FiSave />
                  {loading ? (isArabic ? 'جاري الحفظ...' : 'Saving...') : (isArabic ? 'حفظ الطلب' : 'Save Request')}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default NightShiftRequestPage;
