import { NextResponse } from 'next/server';
import { getConnection } from '@/lib/db';

export async function GET() {
  try {

    const pool = await getConnection();

    // جلب جميع الموظفين النشطين مع معلومات المديرين المتعددة
    const employeesQuery = `
      SELECT
        e.EmployeeCode,
        e.EmployeeName,
        e.JobTitle,
        e.Department,
        e.direct as DirectManager,
        e.DirectManager1,
        e.DirectManager2,
        e.DirectManager3,
        e.DirectManager4,
        e.CurrentStatus,
        e.HireDate,
        e.Governorate,
        e.Mobile,
        e.email
      FROM Employees e
      WHERE e.CurrentStatus IN (N'نشط', N'ساري', N'سارى', N'active', N'Active')
        AND e.EmployeeName IS NOT NULL
        AND e.EmployeeName != ''
      ORDER BY
        CASE
          WHEN e.JobTitle LIKE '%مدير المنطقة%' OR e.JobTitle LIKE '%Regional Manager%' THEN 1
          WHEN e.JobTitle LIKE '%مدير عام%' OR e.JobTitle LIKE '%General Manager%' THEN 2
          WHEN e.JobTitle LIKE '%مدير%' OR e.JobTitle LIKE '%Manager%' THEN 3
          WHEN e.JobTitle LIKE '%رئيس%' OR e.JobTitle LIKE '%Head%' THEN 4
          ELSE 5
        END,
        e.EmployeeName
    `;

    const result = await pool.request().query(employeesQuery);
    const employees = result.recordset;

    // استخراج الأقسام الفريدة
    const departments = [...new Set(employees.map(emp => emp.Department).filter(dept => dept))];

    // بناء الهيكل التنظيمي الهرمي
    const organizationTree = buildHierarchicalOrganizationTree(employees);

    // إحصائيات الهيكل
    const stats = {
      totalEmployees: employees.length,
      totalDepartments: departments.length,
      totalManagers: employees.filter(emp => emp.JobTitle.includes('مدير')).length,
      maxLevels: calculateMaxLevels(organizationTree)
    };

    // إنشاء جدول الهيكل الهرمي للعرض
    const hierarchyTable = buildHierarchyTable(employees);

    return NextResponse.json({
      success: true,
      employees,
      departments,
      organizationTree,
      hierarchyTable,
      stats,
      message: `تم جلب ${employees.length} موظف بنجاح`
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message,
      employees: [],
      departments: [],
      organizationTree: [],
      hierarchyTable: [],
      stats: {}
    }, { status: 500 });
  }
}

// بناء الهيكل التنظيمي الهرمي
function buildHierarchicalOrganizationTree(employees) {

  // إنشاء خريطة للموظفين
  const employeeMap = new Map();
  employees.forEach(emp => {
    employeeMap.set(emp.EmployeeCode, {
      ...emp,
      children: [],
      level: 0,
      managerChain: []
    });
  });

  // تحديد السلسلة الإدارية لكل موظف
  employees.forEach(emp => {
    const employee = employeeMap.get(emp.EmployeeCode);
    const managerChain = [];

    // بناء السلسلة الإدارية من الأعلى إلى الأسفل
    if (emp.DirectManager4) managerChain.push(emp.DirectManager4); // مدير المنطقة
    if (emp.DirectManager3) managerChain.push(emp.DirectManager3); // المستوى الثاني
    if (emp.DirectManager2) managerChain.push(emp.DirectManager2); // المستوى الثالث
    if (emp.DirectManager1) managerChain.push(emp.DirectManager1); // المدير المباشر

    employee.managerChain = managerChain;
    employee.level = managerChain.length;
    employee.immediateManager = managerChain[managerChain.length - 1] || null;
  });

  // بناء العلاقات الهرمية
  employees.forEach(emp => {
    const employee = employeeMap.get(emp.EmployeeCode);

    if (employee.immediateManager) {
      const manager = employeeMap.get(employee.immediateManager);
      if (manager) {
        // التأكد من عدم إضافة نفس الطفل مرتين
        if (!manager.children.find(child => child.EmployeeCode === employee.EmployeeCode)) {
          manager.children.push(employee);
        }
      }
    }
  });

  // العثور على مدير المنطقة (العقدة الجذرية)
  const areaManager = employees.find(emp =>
    emp.EmployeeCode === '1412' ||
    emp.JobTitle.includes('مدير المنطقة') ||
    (!emp.DirectManager1 && !emp.DirectManager2 && !emp.DirectManager3 && !emp.DirectManager4)
  );

  if (!areaManager) {

    return [];
  }

  const rootNode = employeeMap.get(areaManager.EmployeeCode);
  if (!rootNode) {

    return [];
  }

  // ترتيب الأطفال في كل مستوى
  const sortChildren = (node) => {
    if (node.children && node.children.length > 0) {
      node.children.sort((a, b) => {
        // ترتيب حسب المسمى الوظيفي ثم الاسم
        const getJobPriority = (jobTitle) => {
          if (jobTitle.includes('مدير عام')) return 1;
          if (jobTitle.includes('مدير')) return 2;
          if (jobTitle.includes('رئيس')) return 3;
          if (jobTitle.includes('مهندس اول')) return 4;
          if (jobTitle.includes('مهندس')) return 5;
          return 6;
        };

        const priorityA = getJobPriority(a.JobTitle);
        const priorityB = getJobPriority(b.JobTitle);

        if (priorityA !== priorityB) {
          return priorityA - priorityB;
        }

        return a.EmployeeName.localeCompare(b.EmployeeName, 'ar');
      });

      node.children.forEach(sortChildren);
    }
  };

  sortChildren(rootNode);

  return [rootNode];
}

// بناء جدول الهيكل الهرمي
function buildHierarchyTable(employees) {

  const hierarchyTable = [];

  employees.forEach((emp, index) => {
    // جلب أسماء المديرين
    const getManagerName = (managerCode) => {
      if (!managerCode) return '';
      const manager = employees.find(e => e.EmployeeCode === managerCode);
      return manager ? manager.EmployeeName : managerCode;
    };

    const row = {
      serial: index + 1,
      employeeCode: emp.EmployeeCode,
      employeeName: emp.EmployeeName,
      jobTitle: emp.JobTitle,
      manager1Code: emp.DirectManager1 || '',
      manager1Name: getManagerName(emp.DirectManager1),
      manager2Code: emp.DirectManager2 || '',
      manager2Name: getManagerName(emp.DirectManager2),
      manager3Code: emp.DirectManager3 || '',
      manager3Name: getManagerName(emp.DirectManager3),
      manager4Code: emp.DirectManager4 || '',
      manager4Name: getManagerName(emp.DirectManager4)
    };

    hierarchyTable.push(row);
  });

  // ترتيب الجدول حسب التسلسل الهرمي
  hierarchyTable.sort((a, b) => {
    // ترتيب حسب عدد المديرين (المستوى) ثم الاسم
    const levelA = [a.manager1Code, a.manager2Code, a.manager3Code, a.manager4Code].filter(m => m).length;
    const levelB = [b.manager1Code, b.manager2Code, b.manager3Code, b.manager4Code].filter(m => m).length;

    if (levelA !== levelB) {
      return levelA - levelB;
    }

    return a.employeeName.localeCompare(b.employeeName, 'ar');
  });

  return hierarchyTable;
}

// حساب أقصى عدد مستويات في الهيكل
function calculateMaxLevels(tree) {
  let maxLevel = 0;

  function traverse(node, level = 0) {
    maxLevel = Math.max(maxLevel, level);
    if (node.children) {
      node.children.forEach(child => traverse(child, level + 1));
    }
  }

  tree.forEach(root => traverse(root));
  return maxLevel + 1;
}
