'use client';

import React, { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import {
  FiAlertTriangle,
  FiCheckCircle,
  FiDatabase,
  FiCode,
  FiLink,
  FiRefreshCw,
  FiShield,
  FiTool,
  FiEye
} from 'react-icons/fi';

export default function ColumnImpactPage() {
  const { isArabic } = useLanguage();
  const [impactReport, setImpactReport] = useState(null);
  const [loading, setLoading] = useState(false);
  const [migrating, setMigrating] = useState(false);

  // جلب تقرير التأثير
  const fetchImpactReport = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/check-column-impact');
      const result = await response.json();
      
      if (result.success) {
        setImpactReport(result);
      } else {
        alert('خطأ في جلب التقرير: ' + result.message);
      }
    } catch (error) {

      alert('خطأ في الاتصال بالخادم');
    } finally {
      setLoading(false);
    }
  };

  // تنفيذ الترحيل الآمن
  const performSafeMigration = async () => {
    setMigrating(true);
    try {
      const response = await fetch('/api/check-column-impact', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'safe_migration' })
      });
      
      const result = await response.json();
      
      if (result.success) {
        alert('تم الترحيل الآمن بنجاح!\n\nالخطوات التالية:\n' + result.nextSteps.join('\n'));
        fetchImpactReport(); // إعادة جلب التقرير
      } else {
        alert('خطأ في الترحيل: ' + result.message);
      }
    } catch (error) {

      alert('خطأ في الاتصال بالخادم');
    } finally {
      setMigrating(false);
    }
  };

  useEffect(() => {
    fetchImpactReport();
  }, []);

  const getRiskColor = (level) => {
    switch (level) {
      case 'high': return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      case 'medium': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20';
      case 'low': return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
    }
  };

  return (
    <MainLayout>
      <div className="p-6 max-w-7xl mx-auto">
        {/* العنوان */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FiShield className="text-3xl text-orange-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                  {isArabic ? 'تحليل تأثير تغيير الأعمدة' : 'Column Change Impact Analysis'}
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  {isArabic ? 'فحص تأثير تغيير أسماء الأعمدة على النظام' : 'Analyze the impact of changing column names on the system'}
                </p>
              </div>
            </div>
            
            <div className="flex gap-3">
              <button
                onClick={fetchImpactReport}
                disabled={loading}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center gap-2"
              >
                <FiRefreshCw className={loading ? 'animate-spin' : ''} />
                {loading ? (isArabic ? 'جاري الفحص...' : 'Analyzing...') : (isArabic ? 'إعادة فحص' : 'Re-analyze')}
              </button>
              
              {impactReport && impactReport.impact.recommendations.length > 0 && (
                <button
                  onClick={performSafeMigration}
                  disabled={migrating}
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center gap-2"
                >
                  <FiTool className={migrating ? 'animate-spin' : ''} />
                  {migrating ? (isArabic ? 'جاري الترحيل...' : 'Migrating...') : (isArabic ? 'ترحيل آمن' : 'Safe Migration')}
                </button>
              )}
            </div>
          </div>
        </div>

        {loading && !impactReport && (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600"></div>
            <span className="mr-3 text-gray-600 dark:text-gray-400">
              {isArabic ? 'جاري تحليل التأثير...' : 'Analyzing impact...'}
            </span>
          </div>
        )}

        {impactReport && (
          <div className="space-y-6">
            {/* ملخص المخاطر */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center gap-3 mb-4">
                {impactReport.summary.riskLevel === 'high' ? (
                  <FiAlertTriangle className="text-2xl text-red-600" />
                ) : (
                  <FiCheckCircle className="text-2xl text-green-600" />
                )}
                <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
                  {isArabic ? 'ملخص المخاطر' : 'Risk Summary'}
                </h2>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                    {impactReport.summary.totalAPIs}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {isArabic ? 'APIs متأثرة' : 'Affected APIs'}
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                    {impactReport.summary.totalViews}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {isArabic ? 'Views متأثرة' : 'Affected Views'}
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                    {impactReport.summary.totalForeignKeys}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {isArabic ? 'Foreign Keys' : 'Foreign Keys'}
                  </p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                    {impactReport.summary.totalTableReferences}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {isArabic ? 'مراجع الجداول' : 'Table References'}
                  </p>
                </div>
              </div>

              <div className="mt-4 p-4 rounded-lg bg-gray-50 dark:bg-gray-700">
                <div className="flex items-center gap-2">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getRiskColor(impactReport.summary.riskLevel)}`}>
                    {impactReport.summary.riskLevel === 'high' ? (isArabic ? 'مخاطر عالية' : 'High Risk') : 
                     impactReport.summary.riskLevel === 'medium' ? (isArabic ? 'مخاطر متوسطة' : 'Medium Risk') :
                     (isArabic ? 'مخاطر منخفضة' : 'Low Risk')}
                  </span>
                  <span className="text-gray-600 dark:text-gray-400">
                    {impactReport.summary.riskLevel === 'high' ? 
                      (isArabic ? 'تغيير الأعمدة سيسبب مشاكل كبيرة' : 'Changing columns will cause major issues') :
                      (isArabic ? 'التغيير آمن نسبياً' : 'Changes are relatively safe')
                    }
                  </span>
                </div>
              </div>
            </div>

            {/* حالة جدول الموظفين */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
                  <FiDatabase className="text-blue-600" />
                  {isArabic ? 'حالة جدول الموظفين' : 'Employees Table Status'}
                </h3>
              </div>
              
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-3">
                      {isArabic ? 'الأعمدة الحالية' : 'Current Columns'}
                    </h4>
                    <div className="space-y-2">
                      {impactReport.impact.employeesTable.columns.map((col, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded">
                          <span className="font-mono text-sm">{col.COLUMN_NAME}</span>
                          <span className="text-xs text-gray-500">{col.DATA_TYPE}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-3">
                      {isArabic ? 'حالة التوافق' : 'Compatibility Status'}
                    </h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span>{isArabic ? 'يحتوي على EmployeeID' : 'Has EmployeeID'}</span>
                        {impactReport.impact.employeesTable.hasEmployeeID ? (
                          <FiCheckCircle className="text-green-600" />
                        ) : (
                          <FiAlertTriangle className="text-red-600" />
                        )}
                      </div>
                      <div className="flex items-center justify-between">
                        <span>{isArabic ? 'يحتوي على FullName' : 'Has FullName'}</span>
                        {impactReport.impact.employeesTable.hasFullName ? (
                          <FiCheckCircle className="text-green-600" />
                        ) : (
                          <FiAlertTriangle className="text-red-600" />
                        )}
                      </div>
                      <div className="flex items-center justify-between">
                        <span>{isArabic ? 'يحتوي على EmployeeCode' : 'Has EmployeeCode'}</span>
                        {impactReport.impact.employeesTable.hasEmployeeCode ? (
                          <FiCheckCircle className="text-green-600" />
                        ) : (
                          <FiAlertTriangle className="text-yellow-600" />
                        )}
                      </div>
                      <div className="flex items-center justify-between">
                        <span>{isArabic ? 'يحتوي على EmployeeName' : 'Has EmployeeName'}</span>
                        {impactReport.impact.employeesTable.hasEmployeeName ? (
                          <FiCheckCircle className="text-green-600" />
                        ) : (
                          <FiAlertTriangle className="text-yellow-600" />
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* التوصيات */}
            {impactReport.impact.recommendations.length > 0 && (
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
                <div className="flex items-center gap-2 mb-4">
                  <FiAlertTriangle className="text-yellow-600" />
                  <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">
                    {isArabic ? 'التوصيات الهامة' : 'Critical Recommendations'}
                  </h3>
                </div>
                
                <div className="space-y-4">
                  {impactReport.impact.recommendations.map((rec, index) => (
                    <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-yellow-200 dark:border-yellow-700">
                      <div className="flex items-center gap-2 mb-2">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${getRiskColor(rec.type)}`}>
                          {rec.type === 'critical' ? (isArabic ? 'حرج' : 'Critical') : rec.type}
                        </span>
                        <h4 className="font-medium text-gray-800 dark:text-gray-200">
                          {rec.action}
                        </h4>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                        {isArabic ? 'السبب:' : 'Reason:'} {rec.reason}
                      </p>
                      <div>
                        <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          {isArabic ? 'الخطوات المطلوبة:' : 'Required Steps:'}
                        </p>
                        <ol className="list-decimal list-inside text-sm text-gray-600 dark:text-gray-400 space-y-1">
                          {rec.steps.map((step, stepIndex) => (
                            <li key={stepIndex}>{step}</li>
                          ))}
                        </ol>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* APIs المتأثرة */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
                  <FiCode className="text-purple-600" />
                  {isArabic ? 'APIs المتأثرة' : 'Affected APIs'}
                </h3>
              </div>
              
              <div className="p-6">
                <div className="space-y-3">
                  {impactReport.impact.affectedAPIs.map((api, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <span className="font-mono text-sm">{api.endpoint}</span>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${getRiskColor(api.risk)}`}>
                        {api.risk === 'high' ? (isArabic ? 'مخاطر عالية' : 'High Risk') : api.risk}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
