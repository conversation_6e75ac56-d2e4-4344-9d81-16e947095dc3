import { NextResponse } from 'next/server';
import { getConnection } from '@/utils/db';

export async function GET() {
  let pool = null;

  try {

    pool = await getConnection();

    // جلب إجمالي الموظفين والنشطين

    const totalStatsResult = await pool.request().query(`
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN CurrentStatus = 'نشط' OR CurrentStatus = 'active' OR CurrentStatus IS NULL THEN 1 END) as active,
        COUNT(CASE WHEN CurrentStatus = 'غير نشط' OR CurrentStatus = 'inactive' THEN 1 END) as inactive
      FROM Employees WITH(NOLOCK)
    `);

    // جلب توزيع الموظفين حسب الأقسام

    const departmentsResult = await pool.request().query(`
      SELECT
        ISNULL(Department, 'غير محدد') as department_name,
        COUNT(*) as employee_count
      FROM Employees WITH(NOLOCK)
      GROUP BY Department
      ORDER BY employee_count DESC
    `);

    // جلب الموظفين المضافين حديثاً (آخر 30 يوم)

    const recentAdditionsResult = await pool.request().query(`
      SELECT TOP 10
        EmployeeID,
        FullName,
        ISNULL(Department, 'غير محدد') as Department,
        ISNULL(JoinDate, HireDate) as join_date,
        JobTitle
      FROM Employees WITH(NOLOCK)
      WHERE ISNULL(JoinDate, HireDate) IS NOT NULL
      ORDER BY ISNULL(JoinDate, HireDate) DESC
    `);

    // جلب إحصائيات إضافية والمغتربين

    const additionalStatsResult = await pool.request().query(`
      SELECT
        COUNT(CASE WHEN Gender = 'ذكر' OR Gender = 'male' OR Gender = 'M' THEN 1 END) as male_count,
        COUNT(CASE WHEN Gender = 'أنثى' OR Gender = 'female' OR Gender = 'F' THEN 1 END) as female_count,
        -- المغتربين: حسب حقل مغترب (نعم)
        COUNT(CASE WHEN مغترب = N'نعم' OR مغترب = 'نعم' OR مغترب = 'Yes' OR مغترب = 'yes' THEN 1 END) as expatriates_count,
        -- الموظفين المحليين: حسب حقل مغترب (لا أو فارغ)
        COUNT(CASE WHEN مغترب = N'لا' OR مغترب = 'لا' OR مغترب = 'No' OR مغترب = 'no' OR مغترب IS NULL OR مغترب = '' THEN 1 END) as locals_count,
        -- المقيمين في شقق الشركة (من جدول ApartmentBeneficiaries)
        COUNT(CASE WHEN EXISTS (
          SELECT 1 FROM ApartmentBeneficiaries ab
          WHERE ab.EmployeeCode = Employees.EmployeeCode
          AND ab.IsActive = 1
          AND (ab.EndDate IS NULL OR ab.EndDate >= GETDATE())
        ) THEN 1 END) as housed_count,
        -- المغتربين المقيمين في شقق الشركة
        COUNT(CASE WHEN (مغترب = N'نعم' OR مغترب = 'نعم' OR مغترب = 'Yes' OR مغترب = 'yes')
          AND EXISTS (
            SELECT 1 FROM ApartmentBeneficiaries ab
            WHERE ab.EmployeeCode = Employees.EmployeeCode
            AND ab.IsActive = 1
            AND (ab.EndDate IS NULL OR ab.EndDate >= GETDATE())
          ) THEN 1 END) as expatriates_company_housing,
        -- المغتربين غير المقيمين في شقق الشركة
        COUNT(CASE WHEN (مغترب = N'نعم' OR مغترب = 'نعم' OR مغترب = 'Yes' OR مغترب = 'yes')
          AND NOT EXISTS (
            SELECT 1 FROM ApartmentBeneficiaries ab
            WHERE ab.EmployeeCode = Employees.EmployeeCode
            AND ab.IsActive = 1
            AND (ab.EndDate IS NULL OR ab.EndDate >= GETDATE())
          ) THEN 1 END) as expatriates_private_housing,
        COUNT(DISTINCT ISNULL(Governorate, 'غير محدد')) as governorate_count
      FROM Employees WITH(NOLOCK)
    `);

    // جلب إحصائيات التأمينات

    const insuranceStatsResult = await pool.request().query(`
      SELECT
        COUNT(CASE WHEN SocialInsurance = 'مؤمن' OR SocialInsurance = '1' OR SocialInsurance = 'نعم' OR SocialInsurance = 'Yes' THEN 1 END) as social_insured_count,
        COUNT(CASE WHEN SocialInsurance = 'غير مؤمن' OR SocialInsurance = '0' OR SocialInsurance = 'لا' OR SocialInsurance = 'No' THEN 1 END) as social_uninsured_count,
        COUNT(CASE WHEN MedicalInsurance = 'مؤمن' OR MedicalInsurance = '1' OR MedicalInsurance = 'نعم' OR MedicalInsurance = 'Yes' THEN 1 END) as medical_insured_count,
        COUNT(CASE WHEN MedicalInsurance = 'غير مؤمن' OR MedicalInsurance = '0' OR MedicalInsurance = 'لا' OR MedicalInsurance = 'No' THEN 1 END) as medical_uninsured_count,
        COUNT(CASE WHEN (SocialInsurance = 'مؤمن' OR SocialInsurance = '1' OR SocialInsurance = 'نعم' OR SocialInsurance = 'Yes')
                   AND (MedicalInsurance = 'مؤمن' OR MedicalInsurance = '1' OR MedicalInsurance = 'نعم' OR MedicalInsurance = 'Yes') THEN 1 END) as both_insured_count,
        COUNT(CASE WHEN (SocialInsurance IS NULL OR SocialInsurance = '' OR SocialInsurance = 'غير محدد')
                   AND (MedicalInsurance IS NULL OR MedicalInsurance = '' OR MedicalInsurance = 'غير محدد') THEN 1 END) as no_insurance_data_count
      FROM Employees WITH(NOLOCK)
    `);

    // جلب أحدث 5 موظفين حسب تاريخ التواجد

    const latestByJoinDateResult = await pool.request().query(`
      SELECT TOP 5
        EmployeeID,
        FullName,
        ISNULL(Department, 'غير محدد') as Department,
        ISNULL(JoinDate, HireDate) as join_date,
        ISNULL(JobTitle, 'غير محدد') as JobTitle,
        CASE
          WHEN JoinDate IS NOT NULL THEN 'تاريخ التواجد'
          WHEN HireDate IS NOT NULL THEN 'تاريخ التعيين'
          ELSE 'غير محدد'
        END as date_type
      FROM Employees WITH(NOLOCK)
      WHERE ISNULL(JoinDate, HireDate) IS NOT NULL
      ORDER BY ISNULL(JoinDate, HireDate) DESC
    `);

    // تجميع البيانات
    const totalStats = totalStatsResult.recordset[0];
    const departments = departmentsResult.recordset.map(dept => ({
      name: dept.department_name,
      count: dept.employee_count
    }));

    const recentAdditions = latestByJoinDateResult.recordset.map(emp => ({
      id: emp.EmployeeID,
      name: emp.FullName,
      department: emp.Department,
      jobTitle: emp.JobTitle,
      date: emp.join_date,
      dateType: emp.date_type
    }));

    const additionalStats = additionalStatsResult.recordset[0];
    const insuranceStats = insuranceStatsResult.recordset[0];

    const stats = {
      total: totalStats.total || 0,
      active: totalStats.active || 0,
      inactive: totalStats.inactive || 0,
      male: additionalStats.male_count || 0,
      female: additionalStats.female_count || 0,
      // إحصائيات المغتربين والمحليين حسب حقل area
      expatriates: additionalStats.expatriates_count || 0, // إجمالي المغتربين (لديهم منطقة)
      locals: additionalStats.locals_count || 0, // إجمالي المحليين (بدون منطقة)
      // تفاصيل سكن المغتربين
      expatriatesPrivateHousing: additionalStats.expatriates_private_housing || 0, // مغتربين بسكن خاص
      expatriatesCompanyHousing: additionalStats.expatriates_company_housing || 0, // مغتربين بسكن شركة
      housed: additionalStats.housed_count || 0, // إجمالي المقيمين في شقق الشركة
      governorates: additionalStats.governorate_count || 0,
      // إحصائيات التأمينات
      socialInsured: insuranceStats.social_insured_count || 0,
      socialUninsured: insuranceStats.social_uninsured_count || 0,
      medicalInsured: insuranceStats.medical_insured_count || 0,
      medicalUninsured: insuranceStats.medical_uninsured_count || 0,
      bothInsured: insuranceStats.both_insured_count || 0,
      noInsuranceData: insuranceStats.no_insurance_data_count || 0,
      departments: departments,
      recentAdditions: recentAdditions,
      lastUpdated: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      data: stats
    });

  } catch (error) {

    // رسائل خطأ مخصصة
    let errorMessage = 'حدث خطأ أثناء جلب إحصائيات الموظفين';

    if (error.code === 'ECONNREFUSED') {
      errorMessage = 'خطأ في الاتصال بقاعدة البيانات';
    } else if (error.code === 'ELOGIN') {
      errorMessage = 'خطأ في تسجيل الدخول لقاعدة البيانات';
    } else if (error.number === 208) {
      errorMessage = 'جدول الموظفين غير موجود في قاعدة البيانات';
    }

    return NextResponse.json({
      success: false,
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? {
        message: error.message,
        code: error.code,
        state: error.state
      } : undefined
    }, { status: 500 });

  } finally {

  }
}
