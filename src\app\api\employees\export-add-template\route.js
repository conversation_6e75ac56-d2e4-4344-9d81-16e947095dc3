import { NextResponse } from 'next/server';
import ExcelJS from 'exceljs';

export async function GET() {
  try {

    // إنشاء ملف Excel
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('ملف الإضافة - موظفين جدد');

    // تعريف الأعمدة
    const columns = [
      { header: 'كود الموظف*', key: 'EmployeeCode', width: 15 },
      { header: 'الاسم الكامل*', key: 'EmployeeName', width: 25 },
      { header: 'المسمى الوظيفي*', key: 'JobTitle', width: 20 },
      { header: 'القسم*', key: 'Department', width: 20 },
      { header: 'المدير المباشر', key: 'DirectManager', width: 20 },
      { header: 'الرقم القومي*', key: 'NationalID', width: 18 },
      { header: 'تاريخ الميلاد*', key: 'BirthDate', width: 15 },
      { header: 'النوع*', key: 'Gender', width: 10 },
      { header: 'المحافظة*', key: 'Governorate', width: 15 },
      { header: 'المنطقة', key: 'Area', width: 15 },
      { header: 'الحالة الاجتماعية*', key: 'MaritalStatus', width: 15 },
      { header: 'رقم الجوال', key: 'Mobile', width: 15 },
      { header: 'البريد الإلكتروني', key: 'Email', width: 25 },
      { header: 'رقم الطوارئ', key: 'EmergencyNumber', width: 15 },
      { header: 'صلة القرابة', key: 'Kinship', width: 15 },
      { header: 'تاريخ التعيين', key: 'HireDate', width: 15 },
      { header: 'تاريخ الانضمام*', key: 'JoinDate', width: 15 },
      { header: 'حالة الموظف*', key: 'CurrentStatus', width: 15 },
      { header: 'الخدمة العسكرية', key: 'MilitaryService', width: 15 },
      { header: 'مغترب*', key: 'IsResidentEmployee', width: 10 },
      { header: 'المؤهل', key: 'Education', width: 15 },
      { header: 'الجامعة', key: 'University', width: 20 },
      { header: 'التخصص', key: 'Major', width: 20 },
      { header: 'التقدير', key: 'Grade', width: 15 },
      { header: 'سنة التخرج', key: 'Batch', width: 15 },
      { header: 'التأمين الاجتماعي', key: 'SocialInsurance', width: 15 },
      { header: 'رقم التأمين الاجتماعي', key: 'SocialInsuranceNumber', width: 20 },
      { header: 'تاريخ التأمين الاجتماعي', key: 'SocialInsuranceDate', width: 20 },
      { header: 'التأمين الطبي', key: 'MedicalInsurance', width: 15 },
      { header: 'رقم التأمين الطبي', key: 'MedicalInsuranceNumber', width: 20 }
    ];

    worksheet.columns = columns;

    // تنسيق رأس الجدول
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '28A745' } // أخضر للإضافة
    };
    headerRow.alignment = { horizontal: 'center', vertical: 'middle' };

    // إضافة صفوف فارغة للمثال
    const exampleRows = [
      {
        EmployeeCode: 'تعيين جديد',
        EmployeeName: 'أحمد محمد علي',
        JobTitle: 'محاسب',
        Department: 'المحاسبة',
        DirectManager: 'سارة أحمد',
        NationalID: '12345678901234',
        BirthDate: '1990-01-15',
        Gender: 'ذكر',
        Governorate: 'القاهرة',
        Area: 'مدينة نصر',
        MaritalStatus: 'متزوج',
        Mobile: '01234567890',
        Email: '<EMAIL>',
        EmergencyNumber: '01987654321',
        Kinship: 'زوجة',
        HireDate: '2024-01-01',
        JoinDate: '2024-01-15',
        CurrentStatus: 'يعمل',
        MilitaryService: 'أدى الخدمة',
        IsResidentEmployee: 'لا',
        Education: 'بكالوريوس',
        University: 'جامعة القاهرة',
        Major: 'محاسبة',
        Grade: 'جيد جداً',
        Batch: '2015',
        SocialInsurance: 'نعم',
        SocialInsuranceNumber: '123456789',
        SocialInsuranceDate: '2024-01-01',
        MedicalInsurance: 'نعم',
        MedicalInsuranceNumber: 'MED123456'
      },
      {
        EmployeeCode: '1002',
        EmployeeName: 'فاطمة محمود',
        JobTitle: 'مهندس برمجيات',
        Department: 'تكنولوجيا المعلومات',
        DirectManager: '',
        NationalID: '98765432109876',
        BirthDate: '1992-05-20',
        Gender: 'أنثى',
        Governorate: 'الجيزة',
        Area: '',
        MaritalStatus: 'أعزب',
        Mobile: '',
        Email: '',
        EmergencyNumber: '',
        Kinship: '',
        HireDate: '',
        JoinDate: '2024-02-01',
        CurrentStatus: 'يعمل',
        MilitaryService: '',
        IsResidentEmployee: 'نعم',
        Education: '',
        University: '',
        Major: '',
        Grade: '',
        Batch: '',
        SocialInsurance: '',
        SocialInsuranceNumber: '',
        SocialInsuranceDate: '',
        MedicalInsurance: '',
        MedicalInsuranceNumber: ''
      }
    ];

    // إضافة الصفوف المثال
    exampleRows.forEach((row, index) => {
      const addedRow = worksheet.addRow(row);
      
      // تلوين الصفوف بالتناوب
      if (index % 2 === 0) {
        addedRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'E8F5E8' }
        };
      }
      
      // محاذاة النص
      addedRow.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    // إضافة صفوف فارغة للإدخال
    for (let i = 0; i < 20; i++) {
      const emptyRow = worksheet.addRow({});
      emptyRow.alignment = { horizontal: 'center', vertical: 'middle' };
    }

    // إضافة حدود للجدول
    worksheet.eachRow((row, rowNumber) => {
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });
    });

    // إضافة ملاحظات في أعلى الملف
    worksheet.insertRow(1, []);
    worksheet.insertRow(1, ['ملف إضافة الموظفين الجدد']);
    worksheet.insertRow(2, []);
    worksheet.insertRow(3, ['ملاحظات مهمة:']);
    worksheet.insertRow(4, ['• الحقول المطلوبة مميزة بعلامة (*)']);
    worksheet.insertRow(5, ['• يمكن كتابة "تعيين جديد" في كود الموظف لإنشاء كود تلقائي']);
    worksheet.insertRow(6, ['• الحقول الاختيارية: المدير المباشر، تاريخ التعيين، الخدمة العسكرية']);
    worksheet.insertRow(7, ['• الصفوف الأولى تحتوي على أمثلة للتوضيح']);
    worksheet.insertRow(8, ['• احذف الأمثلة قبل رفع الملف أو استبدلها ببياناتك']);
    worksheet.insertRow(9, []);
    worksheet.insertRow(10, ['القيم المسموحة:']);
    worksheet.insertRow(11, ['• النوع: ذكر، أنثى']);
    worksheet.insertRow(12, ['• الحالة الاجتماعية: متزوج، أعزب، مطلق، أرمل']);
    worksheet.insertRow(13, ['• حالة الموظف: يعمل، إجازة، معار']);
    worksheet.insertRow(14, ['• الخدمة العسكرية: أدى الخدمة، معفى، مؤجل']);
    worksheet.insertRow(15, ['• مغترب: نعم، لا']);
    worksheet.insertRow(16, ['• التأمين الاجتماعي/الطبي: نعم، لا']);
    worksheet.insertRow(17, []);

    // تنسيق الملاحظات
    for (let i = 1; i <= 16; i++) {
      const noteRow = worksheet.getRow(i);
      noteRow.font = { bold: true, color: { argb: '0066CC' } };
      if (i === 1) {
        noteRow.font.size = 16;
        noteRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'E6F7E6' }
        };
      } else if (i === 3 || i === 10) {
        noteRow.font.size = 12;
        noteRow.font.color = { argb: '006600' };
      }
    }

    // تحويل الملف إلى buffer
    const buffer = await workbook.xlsx.writeBuffer();

    // إنشاء اسم الملف مع التاريخ
    const currentDate = new Date().toISOString().split('T')[0];
    const filename = `ملف_إضافة_موظفين_جدد_${currentDate}.xlsx`;

    // إرجاع الملف
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="${encodeURIComponent(filename)}"`,
        'Content-Length': buffer.length.toString(),
      },
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'خطأ في إنشاء ملف الإضافة',
      error: error.message
    }, { status: 500 });
  }
}
