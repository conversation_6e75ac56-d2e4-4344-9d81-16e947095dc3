import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

// دالة لمعالجة التواريخ وتجنب 1970-01-01
function formatDate(dateValue) {
  if (!dateValue) return null;
  const date = new Date(dateValue);
  // التحقق من صحة التاريخ وأنه ليس 1970-01-01
  if (isNaN(date.getTime()) || date.getFullYear() < 1900) {
    return null;
  }
  return dateValue;
}

// دالة لتوحيد بيانات الموظف
function normalizeEmployeeData(employee) {
  return {
    ...employee,
    // التسميات الموحدة الجديدة
    employeeCode: employee.EmployeeCode,
    employeeName: employee.EmployeeName,

    // التسميات الإضافية
    EmployeeCode: employee.EmployeeCode,
    EmployeeName: employee.EmployeeName,

    // معرفات موحدة للصور والمستندات
    EmployeeID: employee.EmployeeCode,
    FullName: employee.EmployeeName,

    // معالجة التواريخ
    HireDate: formatDate(employee.HireDate),
    BirthDate: formatDate(employee.BirthDate),
    JoinDate: formatDate(employee.JoinDate),

    // إضافة displayText للواجهات
    displayText: `${employee.EmployeeName} (${employee.EmployeeCode})`,

    // إضافة TransportInfo
    transportInfo: employee.TransportInfo
  };
}

// دعم GET للبحث الفردي (لصفحة التعديل)
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const searchType = searchParams.get('searchType');
    const employeeCode = searchParams.get('employeeCode') || searchParams.get('employeeId');
    const searchValue = searchParams.get('searchValue');

    if (searchType === 'individual' && (employeeCode || searchValue)) {
      const pool = await getConnection();
      const searchTerm = employeeCode || searchValue;

      // تحديد نوع البحث: رقم أم نص
      const isNumeric = /^\d+$/.test(searchTerm);

      let whereClause;
      let paramValue;

      if (isNumeric) {
        whereClause = 'EmployeeCode = @searchValue';
        paramValue = parseInt(searchTerm);
      } else {
        whereClause = 'EmployeeName LIKE @searchValue';
        paramValue = `%${searchTerm}%`;
      }

      const request = pool.request();
      request.input('searchValue', isNumeric ? sql.Int : sql.NVarChar, paramValue);

      const result = await request.query(`
        SELECT
          e.Num,
          e.EmployeeCode,
          e.EmployeeName,
          e.JobTitle,
          e.Department,
          e.direct as DirectManager,
          e.HireDate,
          e.BirthDate,
          e.JoinDate,
          e.NationalID,
          e.Governorate,
          e.MaritalStatus,
          e.Gender,
          e.CurrentStatus,
          e.Mserv as MilitaryService,
          CASE
            WHEN EXISTS (
              SELECT 1 FROM ApartmentBeneficiaries ab
              WHERE ab.EmployeeCode = e.EmployeeCode
              AND ab.IsActive = 1
              AND (ab.EndDate IS NULL OR ab.EndDate >= GETDATE())
            ) THEN 1
            ELSE 0
          END as IsResidentEmployee,
          e.area as Area,
          e.Mobile,
          e.email as Email,
          e.Education,
          e.University,
          e.Major,
          e.Grade,
          e.Batch,
          e.emrnum as EmergencyNumber,
          e.Kinship,
          e.SocialInsurance,
          e.spcialInsDate as SocialInsuranceDate,
          e.SocialInsureNum as SocialInsuranceNumber,
          e.MedicalInsurance,
          e.MedicalInsuranceNum as MedicalInsuranceNumber,
          '' as LandlordName
        FROM Employees e
        WHERE ${whereClause}
      `);

      if (result.recordset.length > 0) {
        const employee = result.recordset[0];

        // إضافة معرف موحد للصور والمستندات
        employee.EmployeeID = employee.EmployeeCode;
        employee.FullName = employee.EmployeeName;

        const normalizedEmployee = normalizeEmployeeData(employee);
        return NextResponse.json({
          success: true,
          employee: normalizedEmployee
        });
      }

      return NextResponse.json({
        success: false,
        message: 'لم يتم العثور على الموظف'
      });
    }

    return NextResponse.json({
      success: false,
      message: 'معاملات البحث غير صحيحة'
    });

  } catch (error) {

    return NextResponse.json(
      { success: false, message: error.message },
      { status: 500 }
    );
  }
}

export async function POST(req) {
  try {
    const { searchType, searchValue, department, jobTitle, governorate } = await req.json();

    const pool = await getConnection();
    let result;

    if (searchType === 'individual') {
      // البحث الفردي بالكود أو الاسم
      let whereClause;
      let paramValue;

      // تحديد نوع البحث: رقم أم نص
      const isNumeric = /^\d+$/.test(searchValue);

      if (isNumeric) {
        whereClause = 'EmployeeCode = @searchValue';
        paramValue = parseInt(searchValue);
      } else {
        whereClause = 'EmployeeName LIKE @searchValue';
        paramValue = `%${searchValue}%`;
      }

      const request = pool.request();
      request.input('searchValue', isNumeric ? sql.Int : sql.NVarChar, paramValue);

      result = await request.query(`
        SELECT
          e.Num,
          e.EmployeeCode,
          e.EmployeeName,
          e.JobTitle,
          e.Department,
          e.direct as DirectManager,
          e.HireDate,
          e.BirthDate,
          e.JoinDate,
          e.NationalID,
          e.Governorate,
          e.MaritalStatus,
          e.Gender,
          e.CurrentStatus,
          e.Mserv as MilitaryService,
          CASE
            WHEN EXISTS (
              SELECT 1 FROM ApartmentBeneficiaries ab
              WHERE ab.EmployeeCode = e.EmployeeCode
              AND ab.IsActive = 1
              AND (ab.EndDate IS NULL OR ab.EndDate >= GETDATE())
            ) THEN 1
            ELSE 0
          END as IsResidentEmployee,
          e.area as Area,
          e.Mobile,
          e.email as Email,
          e.Education,
          e.University,
          e.Major,
          e.Grade,
          e.Batch,
          e.emrnum as EmergencyNumber,
          e.Kinship,
          e.SocialInsurance,
          e.spcialInsDate as SocialInsuranceDate,
          e.SocialInsureNum as SocialInsuranceNumber,
          e.MedicalInsurance,
          e.MedicalInsuranceNum as MedicalInsuranceNumber,
          '' as LandlordName
        FROM Employees e
        WHERE ${whereClause}
      `);

      if (result.recordset.length > 0) {
        const employee = result.recordset[0];

        // إضافة معرف موحد للصور والمستندات
        employee.EmployeeID = employee.EmployeeCode;
        employee.FullName = employee.EmployeeName;

        // جلب بيانات الشقة إذا كان الموظف مستفيد
        try {
          const apartmentResult = await pool.request()
            .input('employeeCode', sql.NVarChar, employee.EmployeeCode.toString())
            .query(`
              SELECT TOP 1
                a.ApartmentCode,
                a.LandlordName,
                a.Address,
                a.RentAmount,
                ab.StartDate as BeneficiaryStartDate,
                ab.EndDate as BeneficiaryEndDate,
                ab.IsActive as IsBeneficiaryActive
              FROM ApartmentBeneficiaries ab
              INNER JOIN Apartments a ON ab.ApartmentCode = a.ApartmentCode
              WHERE ab.EmployeeCode = @employeeCode
                AND ab.IsActive = 1
              ORDER BY ab.StartDate DESC
            `);

          if (apartmentResult.recordset.length > 0) {
            employee.ApartmentInfo = apartmentResult.recordset[0];

          } else {

          }
        } catch (apartmentError) {

        }

        // جلب بيانات المواصلات إذا كان الموظف مسجل
        try {
          const transportResult = await pool.request()
            .input('employeeCode', sql.NVarChar, employee.EmployeeCode.toString())
            .query(`
              SELECT TOP 1
                c.CarCode,
                c.CarNumber,
                c.Route,
                c.ContractorName,
                cb.StartDate as TransportStartDate,
                cb.EndDate as TransportEndDate,
                cb.IsActive as IsTransportActive
              FROM CarBeneficiaries cb
              INNER JOIN Cars c ON cb.Carcode = c.ID
              WHERE cb.EmployeeCode = @employeeCode
                AND cb.IsActive = 1
              ORDER BY cb.StartDate DESC
            `);

          if (transportResult.recordset.length > 0) {
            employee.TransportInfo = transportResult.recordset[0];

          } else {

          }
        } catch (transportError) {

        }

        const normalizedEmployee = normalizeEmployeeData(employee);
        const response = NextResponse.json({
          success: true,
          employee: normalizedEmployee
        });

        // إضافة headers للـ UTF-8
        response.headers.set('Content-Type', 'application/json; charset=utf-8');

        return response;
      }

      return NextResponse.json({
        success: false,
        message: 'لم يتم العثور على الموظف'
      });
    }
    
    // البحث المجمع
    if (searchType === 'group') {
      const conditions = [];
      const inputs = new Map();

      if (department) {
        conditions.push('Department LIKE @department');
        inputs.set('department', `%${department}%`);
      }
      if (jobTitle) {
        conditions.push('JobTitle LIKE @jobTitle');
        inputs.set('jobTitle', `%${jobTitle}%`);
      }
      if (governorate) {
        conditions.push('Governorate LIKE @governorate');
        inputs.set('governorate', `%${governorate}%`);
      }

      const whereClause = conditions.length > 0 
        ? 'WHERE ' + conditions.join(' AND ')
        : '';

      const request = pool.request();
      inputs.forEach((value, key) => {
        request.input(key, sql.NVarChar, value);
      });

      result = await request.query(`
        SELECT
          e.Num,
          e.EmployeeCode,
          e.EmployeeName,
          e.JobTitle,
          e.Department,
          e.direct as DirectManager,
          e.HireDate,
          e.BirthDate,
          e.JoinDate,
          e.NationalID,
          e.Governorate,
          e.MaritalStatus,
          e.Gender,
          e.CurrentStatus,
          e.Mserv as MilitaryService,
          CASE
            WHEN EXISTS (
              SELECT 1 FROM ApartmentBeneficiaries ab
              WHERE ab.EmployeeCode = e.EmployeeCode
              AND ab.IsActive = 1
              AND (ab.EndDate IS NULL OR ab.EndDate >= GETDATE())
            ) THEN 1
            ELSE 0
          END as IsResidentEmployee,
          e.area as Area,
          e.Mobile,
          e.email as Email,
          e.Education,
          e.University,
          e.Major,
          e.Grade,
          e.Batch,
          e.emrnum as EmergencyNumber,
          e.Kinship,
          e.SocialInsurance,
          e.spcialInsDate as SocialInsuranceDate,
          e.SocialInsureNum as SocialInsuranceNumber,
          e.MedicalInsurance,
          e.MedicalInsuranceNum as MedicalInsuranceNumber,
          '' as LandlordName
        FROM Employees e
        ${whereClause}
        ORDER BY e.EmployeeName
      `);

      // إضافة بيانات الشقة والمواصلات لكل موظف
      const employeesWithDetails = await Promise.all(
        result.recordset.map(async (employee) => {
          // إضافة معرف موحد للتوافق مع الواجهات القديمة
          employee.EmployeeID = employee.EmployeeCode; // للتوافق فقط
          employee.FullName = employee.EmployeeName; // للتوافق فقط
          // جلب بيانات الشقة
          try {
            const apartmentResult = await pool.request()
              .input('employeeCode', sql.NVarChar, employee.EmployeeCode.toString())
              .query(`
                SELECT TOP 1
                  a.ApartmentCode,
                  a.LandlordName,
                  a.Address,
                  a.RentAmount,
                  ab.StartDate as BeneficiaryStartDate,
                  ab.EndDate as BeneficiaryEndDate,
                  ab.IsActive as IsBeneficiaryActive
                FROM ApartmentBeneficiaries ab
                INNER JOIN Apartments a ON ab.ApartmentCode = a.ApartmentCode
                WHERE ab.EmployeeCode = @employeeCode
                  AND ab.IsActive = 1
                ORDER BY ab.StartDate DESC
              `);

            if (apartmentResult.recordset.length > 0) {
              employee.ApartmentInfo = apartmentResult.recordset[0];
            }
          } catch (apartmentError) {

          }

          // جلب بيانات المواصلات
          try {
            const transportResult = await pool.request()
              .input('employeeCode', sql.NVarChar, employee.EmployeeCode.toString())
              .query(`
                SELECT TOP 1
                  c.CarCode,
                  c.CarNumber,
                  c.Route,
                  c.ContractorName,
                  cb.StartDate as TransportStartDate,
                  cb.EndDate as TransportEndDate,
                  cb.IsActive as IsTransportActive
                FROM CarBeneficiaries cb
                INNER JOIN Cars c ON cb.Carcode = c.ID
                WHERE cb.EmployeeCode = @employeeCode
                  AND cb.IsActive = 1
                ORDER BY cb.StartDate DESC
              `);

            if (transportResult.recordset.length > 0) {
              employee.TransportInfo = transportResult.recordset[0];
            }
          } catch (transportError) {

          }

          return normalizeEmployeeData(employee);
        })
      );

      return NextResponse.json({
        success: true,
        employees: employeesWithDetails,
        count: employeesWithDetails.length
      });
    }

    // البحث بالاسم للاقتراحات
    if (searchType === 'name') {
      result = await pool.request()
        .input('name', sql.NVarChar, `%${name}%`)
        .query(`
          SELECT TOP 10
            EmployeeCode,
            EmployeeName,
            JobTitle,
            Department
          FROM Employees
          WHERE EmployeeName LIKE @name
          ORDER BY EmployeeName
        `);

      const normalizedEmployees = result.recordset.map(normalizeEmployeeData);
      return NextResponse.json({
        success: true,
        employees: normalizedEmployees
      });
    }

    return NextResponse.json({
      success: false,
      message: 'نوع البحث غير صحيح'
    });

  } catch (error) {

    return NextResponse.json(
      { success: false, message: error.message },
      { status: 500 }
    );
  }
}
