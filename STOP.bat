@echo off
title Stop Cost Management System
color 0C

echo ========================================
echo    Stop Cost Management System
echo ========================================
echo.

echo Stopping Node.js processes...
taskkill /f /im node.exe >nul 2>&1

echo Stopping npm processes...
taskkill /f /im npm.cmd >nul 2>&1
taskkill /f /im npm >nul 2>&1

echo Freeing port 3001...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3001') do (
    taskkill /f /pid %%a >nul 2>&1
)

echo.
echo ========================================
echo System stopped successfully
echo ========================================
echo.
echo You can now restart using START.bat
echo.

pause
