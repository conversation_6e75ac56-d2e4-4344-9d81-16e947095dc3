'use client';

import React from 'react';
import { FiCalendar } from 'react-icons/fi';

const CustomDateInput = ({
  value,
  onChange,
  required = false,
  label = "تاريخ بداية الاستفادة *",
  id = "custom-date-input"
}) => {

  const handleChange = (e) => {
    let inputValue = e.target.value;

    // إزالة أي أحرف غير رقمية أو /
    inputValue = inputValue.replace(/[^\d/]/g, '');

    // إضافة / تلقائياً
    if (inputValue.length === 2 && !inputValue.includes('/')) {
      inputValue += '/';
    } else if (inputValue.length === 5 && inputValue.split('/').length === 2) {
      inputValue += '/';
    }

    // تحديد الطول الأقصى
    if (inputValue.length <= 10) {
      onChange({ target: { value: inputValue } });
    }
  };

  return (
    <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-6 rounded-xl border-2 border-purple-200 dark:border-purple-700 shadow-lg">
      <div className="flex items-center gap-3 mb-4">
        <div className="bg-purple-600 p-3 rounded-xl">
          <FiCalendar className="text-white text-xl" />
        </div>
        <div>
          <h4 className="text-lg font-semibold text-gray-800 dark:text-white">{label}</h4>
          <p className="text-sm text-gray-600 dark:text-gray-400">حدد تاريخ بداية الاستفادة</p>
        </div>
      </div>

      <div className="relative">
        <div className="relative">
          <input
            type="text"
            id={id}
            value={value}
            onChange={handleChange}
            required={required}
            className="w-full px-6 py-4 pr-16 border-2 border-purple-300 dark:border-purple-600 rounded-xl text-xl font-bold text-center focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm transition-all duration-200"
            dir="ltr"
            style={{
              textAlign: 'center',
              fontSize: '20px',
              fontFamily: 'monospace',
              letterSpacing: '2px'
            }}
            placeholder=""
          />

          {/* Placeholder مخصص */}
          {!value && (
            <div
              className="absolute inset-0 flex items-center justify-center pointer-events-none text-purple-400 dark:text-purple-300 text-xl font-bold"
              style={{
                fontFamily: 'Arial, sans-serif',
                letterSpacing: '1px'
              }}
            >
              يوم/شهر/سنة
            </div>
          )}

          {/* أيقونة التقويم */}
          <div className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-purple-600 p-2 rounded-lg">
            <FiCalendar className="text-white text-lg" />
          </div>
        </div>

        <div className="mt-4 bg-white dark:bg-gray-700 p-3 rounded-lg border border-purple-200 dark:border-purple-600">
          <p className="text-sm text-purple-600 dark:text-purple-400 text-center font-medium flex items-center justify-center gap-2">
            <span className="text-lg">📅</span>
            تنسيق التاريخ: يوم/شهر/سنة (مثال: 25/12/2024)
          </p>
        </div>

        <p className="text-xs text-gray-600 dark:text-gray-400 mt-3 flex items-center justify-center gap-2 bg-gray-50 dark:bg-gray-800 p-2 rounded-lg">
          <FiCalendar className="text-sm text-purple-600" />
          يرجى تحديد التاريخ الذي سيبدأ فيه الموظف الاستفادة من السيارة
        </p>
      </div>
    </div>
  );
};

export default CustomDateInput;
