import { NextResponse } from 'next/server';
import sql from 'mssql';

// إعدادات قاعدة البيانات
const config = {
  server: process.env.DB_SERVER || 'localhost',
  database: process.env.DB_NAME || 'EmployeeDB',
  user: process.env.DB_USER || 'sa',
  password: process.env.DB_PASSWORD || 'YourPassword123',
  options: {
    encrypt: false,
    trustServerCertificate: true,
    enableArithAbort: true,
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
};

export async function GET(request) {
  let pool;
  
  try {

    // الاتصال بقاعدة البيانات
    pool = await sql.connect(config);

    // إنشاء جدول أرصدة الإجازات إذا لم يكن موجوداً
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LeaveBalances' AND xtype='U')
      CREATE TABLE LeaveBalances (
        BalanceID int IDENTITY(1,1) PRIMARY KEY,
        EmployeeCode nvarchar(50) NOT NULL,
        EmployeeName nvarchar(255) NOT NULL,
        Department nvarchar(255),
        AnnualLeaveBalance int DEFAULT 21,
        AnnualLeaveUsed int DEFAULT 0,
        SickLeaveBalance int DEFAULT 15,
        SickLeaveUsed int DEFAULT 0,
        EmergencyLeaveBalance int DEFAULT 7,
        EmergencyLeaveUsed int DEFAULT 0,
        MaternityLeaveBalance int DEFAULT 90,
        MaternityLeaveUsed int DEFAULT 0,
        PaternityLeaveBalance int DEFAULT 3,
        PaternityLeaveUsed int DEFAULT 0,
        StudyLeaveBalance int DEFAULT 0,
        StudyLeaveUsed int DEFAULT 0,
        Year int DEFAULT YEAR(GETDATE()),
        CreatedAt datetime DEFAULT GETDATE(),
        UpdatedAt datetime DEFAULT GETDATE()
      )
    `);

    // التحقق من وجود بيانات، وإذا لم توجد، إنشاء بيانات تجريبية
    const existingData = await pool.request().query(`
      SELECT COUNT(*) as count FROM LeaveBalances
    `);

    if (existingData.recordset[0].count === 0) {

      // جلب بعض الموظفين من جدول الموظفين
      const employees = await pool.request().query(`
        SELECT TOP 10 EmployeeID, FullName, Department 
        FROM Employees 
        WHERE EmployeeID IS NOT NULL
      `);

      if (employees.recordset.length > 0) {
        for (const emp of employees.recordset) {
          await pool.request()
            .input('EmployeeID', sql.NVarChar, emp.EmployeeID)
            .input('EmployeeName', sql.NVarChar, emp.FullName)
            .input('Department', sql.NVarChar, emp.Department)
            .query(`
              INSERT INTO LeaveBalances (
                EmployeeID, EmployeeName, Department,
                AnnualLeaveBalance, AnnualLeaveUsed,
                SickLeaveBalance, SickLeaveUsed,
                EmergencyLeaveBalance, EmergencyLeaveUsed
              ) VALUES (
                @EmployeeID, @EmployeeName, @Department,
                21, ${Math.floor(Math.random() * 10)},
                15, ${Math.floor(Math.random() * 5)},
                7, ${Math.floor(Math.random() * 3)}
              )
            `);
        }
      } else {
        // إنشاء بيانات تجريبية افتراضية
        const sampleEmployees = [
          { id: 'EMP001', name: 'أحمد محمد علي', dept: 'الكهرباء' },
          { id: 'EMP002', name: 'فاطمة أحمد', dept: 'الإدارة' },
          { id: 'EMP003', name: 'محمد علي', dept: 'التنفيذ' },
          { id: 'EMP004', name: 'سارة محمود', dept: 'المساحة' },
          { id: 'EMP005', name: 'خالد أحمد', dept: 'الكهرباء' }
        ];

        for (const emp of sampleEmployees) {
          await pool.request()
            .input('EmployeeID', sql.NVarChar, emp.id)
            .input('EmployeeName', sql.NVarChar, emp.name)
            .input('Department', sql.NVarChar, emp.dept)
            .input('AnnualUsed', sql.Int, Math.floor(Math.random() * 10))
            .input('SickUsed', sql.Int, Math.floor(Math.random() * 5))
            .input('EmergencyUsed', sql.Int, Math.floor(Math.random() * 3))
            .query(`
              INSERT INTO LeaveBalances (
                EmployeeID, EmployeeName, Department,
                AnnualLeaveBalance, AnnualLeaveUsed,
                SickLeaveBalance, SickLeaveUsed,
                EmergencyLeaveBalance, EmergencyLeaveUsed
              ) VALUES (
                @EmployeeID, @EmployeeName, @Department,
                21, @AnnualUsed,
                15, @SickUsed,
                7, @EmergencyUsed
              )
            `);
        }
      }
    }

    // جلب أرصدة الإجازات
    const result = await pool.request().query(`
      SELECT 
        BalanceID,
        EmployeeID,
        EmployeeName,
        Department,
        AnnualLeaveBalance,
        AnnualLeaveUsed,
        (AnnualLeaveBalance - AnnualLeaveUsed) as AnnualLeaveRemaining,
        SickLeaveBalance,
        SickLeaveUsed,
        (SickLeaveBalance - SickLeaveUsed) as SickLeaveRemaining,
        EmergencyLeaveBalance,
        EmergencyLeaveUsed,
        (EmergencyLeaveBalance - EmergencyLeaveUsed) as EmergencyLeaveRemaining,
        MaternityLeaveBalance,
        MaternityLeaveUsed,
        (MaternityLeaveBalance - MaternityLeaveUsed) as MaternityLeaveRemaining,
        PaternityLeaveBalance,
        PaternityLeaveUsed,
        (PaternityLeaveBalance - PaternityLeaveUsed) as PaternityLeaveRemaining,
        StudyLeaveBalance,
        StudyLeaveUsed,
        (StudyLeaveBalance - StudyLeaveUsed) as StudyLeaveRemaining,
        Year,
        UpdatedAt
      FROM LeaveBalances
      WHERE Year = YEAR(GETDATE())
      ORDER BY EmployeeName
    `);

    return NextResponse.json({
      success: true,
      balances: result.recordset
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'خطأ في جلب أرصدة الإجازات',
      error: error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      await pool.close();

    }
  }
}
