'use client';

import React, { useState, useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { useLanguage } from '@/contexts/LanguageContext';
import MainLayout from '@/components/MainLayout';
import { 
  Calendar, 
  Download, 
  Filter, 
  Search, 
  FileText, 
  BarChart3, 
  Eye, 
  Printer,
  RefreshCw,
  CheckCircle,
  XCircle,
  Clock,
  Users,
  TrendingUp,
  AlertCircle,
  Plus,
  Minus
} from 'lucide-react';

export default function LeaveReportsPage() {
  const { isDarkMode } = useTheme();
  const { isRTL, isArabic } = useLanguage();
  
  const [loading, setLoading] = useState(false);
  const [selectedReport, setSelectedReport] = useState('requests');
  const [dateRange, setDateRange] = useState({
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });
  const [filters, setFilters] = useState({
    department: '',
    leaveType: '',
    status: ''
  });

  // أنواع تقارير الإجازات
  const leaveReports = [
    {
      id: 'requests',
      title: 'تقرير طلبات الإجازات',
      description: 'جميع طلبات الإجازات المقدمة',
      icon: FileText,
      color: 'blue'
    },
    {
      id: 'balances',
      title: 'تقرير أرصدة الإجازات',
      description: 'أرصدة الإجازات المتبقية للموظفين',
      icon: BarChart3,
      color: 'green'
    },
    {
      id: 'approved',
      title: 'تقرير الإجازات المعتمدة',
      description: 'الإجازات التي تم اعتمادها',
      icon: CheckCircle,
      color: 'green'
    },
    {
      id: 'rejected',
      title: 'تقرير الإجازات المرفوضة',
      description: 'الإجازات التي تم رفضها',
      icon: XCircle,
      color: 'red'
    },
    {
      id: 'statistics',
      title: 'إحصائيات الإجازات',
      description: 'إحصائيات شاملة عن الإجازات',
      icon: TrendingUp,
      color: 'purple'
    }
  ];

  // بيانات وهمية للتقارير
  const [reportData, setReportData] = useState({
    requests: [
      { id: 1, employeeName: 'أحمد محمد', employeeCode: '1001', leaveType: 'إجازة سنوية', startDate: '2025-01-15', endDate: '2025-01-20', days: 5, status: 'معتمدة', requestDate: '2025-01-10' },
      { id: 2, employeeName: 'فاطمة علي', employeeCode: '1002', leaveType: 'إجازة مرضية', startDate: '2025-01-18', endDate: '2025-01-19', days: 2, status: 'قيد المراجعة', requestDate: '2025-01-17' },
      { id: 3, employeeName: 'محمد حسن', employeeCode: '1003', leaveType: 'إجازة طارئة', startDate: '2025-01-22', endDate: '2025-01-22', days: 1, status: 'مرفوضة', requestDate: '2025-01-21' }
    ],
    balances: [
      { employeeName: 'أحمد محمد', employeeCode: '1001', annualLeave: 25, sickLeave: 10, emergencyLeave: 5, usedAnnual: 5, usedSick: 2, usedEmergency: 1 },
      { employeeName: 'فاطمة علي', employeeCode: '1002', annualLeave: 25, sickLeave: 10, emergencyLeave: 5, usedAnnual: 8, usedSick: 3, usedEmergency: 0 },
      { employeeName: 'محمد حسن', employeeCode: '1003', annualLeave: 25, sickLeave: 10, emergencyLeave: 5, usedAnnual: 12, usedSick: 1, usedEmergency: 2 }
    ],
    statistics: {
      totalRequests: 156,
      approvedRequests: 142,
      rejectedRequests: 14,
      pendingRequests: 8,
      totalLeaveDays: 1250,
      averageLeaveDays: 8.5,
      mostUsedLeaveType: 'إجازة سنوية'
    }
  });

  // تحميل البيانات
  const loadReportData = async () => {
    setLoading(true);
    try {
      // محاكاة تحميل البيانات
      setTimeout(() => {
        setLoading(false);
      }, 1000);
    } catch (error) {

      setLoading(false);
    }
  };

  useEffect(() => {
    loadReportData();
  }, [selectedReport, dateRange, filters]);

  // تصدير التقرير
  const exportReport = (format) => {

    // هنا يمكن إضافة منطق التصدير الفعلي
  };

  // طباعة التقرير
  const printReport = () => {
    window.print();
  };

  // رندر محتوى التقرير حسب النوع
  const renderReportContent = () => {
    switch (selectedReport) {
      case 'requests':
        return (
          <div className="overflow-x-auto">
            <table className={`min-w-full ${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg`}>
              <thead className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <tr>
                  <th className="px-4 py-3 text-right text-sm font-medium">كود الموظف</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">اسم الموظف</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">نوع الإجازة</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">تاريخ البداية</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">تاريخ النهاية</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">عدد الأيام</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">الحالة</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">تاريخ الطلب</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {reportData.requests.map((request) => (
                  <tr key={request.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-4 py-3 text-sm">{request.employeeCode}</td>
                    <td className="px-4 py-3 text-sm font-medium">{request.employeeName}</td>
                    <td className="px-4 py-3 text-sm">{request.leaveType}</td>
                    <td className="px-4 py-3 text-sm">{request.startDate}</td>
                    <td className="px-4 py-3 text-sm">{request.endDate}</td>
                    <td className="px-4 py-3 text-sm">{request.days}</td>
                    <td className="px-4 py-3 text-sm">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        request.status === 'معتمدة' ? 'bg-green-100 text-green-800' :
                        request.status === 'مرفوضة' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {request.status}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm">{request.requestDate}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        );

      case 'balances':
        return (
          <div className="overflow-x-auto">
            <table className={`min-w-full ${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg`}>
              <thead className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <tr>
                  <th className="px-4 py-3 text-right text-sm font-medium">كود الموظف</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">اسم الموظف</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">الإجازة السنوية</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">المستخدم</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">المتبقي</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">الإجازة المرضية</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">المستخدم</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">المتبقي</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {reportData.balances.map((balance, index) => (
                  <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-4 py-3 text-sm">{balance.employeeCode}</td>
                    <td className="px-4 py-3 text-sm font-medium">{balance.employeeName}</td>
                    <td className="px-4 py-3 text-sm">{balance.annualLeave}</td>
                    <td className="px-4 py-3 text-sm text-red-600">{balance.usedAnnual}</td>
                    <td className="px-4 py-3 text-sm text-green-600">{balance.annualLeave - balance.usedAnnual}</td>
                    <td className="px-4 py-3 text-sm">{balance.sickLeave}</td>
                    <td className="px-4 py-3 text-sm text-red-600">{balance.usedSick}</td>
                    <td className="px-4 py-3 text-sm text-green-600">{balance.sickLeave - balance.usedSick}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        );

      case 'statistics':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow`}>
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-blue-500" />
                <div className="mr-4">
                  <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي الطلبات</p>
                  <p className="text-2xl font-bold">{reportData.statistics.totalRequests}</p>
                </div>
              </div>
            </div>
            
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow`}>
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-500" />
                <div className="mr-4">
                  <p className="text-sm text-gray-600 dark:text-gray-400">الطلبات المعتمدة</p>
                  <p className="text-2xl font-bold">{reportData.statistics.approvedRequests}</p>
                </div>
              </div>
            </div>
            
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow`}>
              <div className="flex items-center">
                <XCircle className="h-8 w-8 text-red-500" />
                <div className="mr-4">
                  <p className="text-sm text-gray-600 dark:text-gray-400">الطلبات المرفوضة</p>
                  <p className="text-2xl font-bold">{reportData.statistics.rejectedRequests}</p>
                </div>
              </div>
            </div>
            
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} p-6 rounded-lg shadow`}>
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-500" />
                <div className="mr-4">
                  <p className="text-sm text-gray-600 dark:text-gray-400">قيد المراجعة</p>
                  <p className="text-2xl font-bold">{reportData.statistics.pendingRequests}</p>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return <div>التقرير غير متاح</div>;
    }
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto p-6">
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-sm p-6 mb-6`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Calendar className="h-8 w-8 text-purple-600" />
              <div>
                <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  تقارير الإجازات
                </h1>
                <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  تقارير شاملة عن طلبات الإجازات وأرصدة الموظفين
                </p>
              </div>
            </div>
            
            <div className="flex gap-2">
              <button
                onClick={() => exportReport('excel')}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                تصدير Excel
              </button>
              <button
                onClick={() => exportReport('pdf')}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                تصدير PDF
              </button>
              <button
                onClick={printReport}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
              >
                <Printer className="h-4 w-4" />
                طباعة
              </button>
            </div>
          </div>
        </div>

        {/* أنواع التقارير */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
          {leaveReports.map((report) => (
            <button
              key={report.id}
              onClick={() => setSelectedReport(report.id)}
              className={`p-4 rounded-lg border-2 transition-all ${
                selectedReport === report.id
                  ? `border-${report.color}-500 bg-${report.color}-50 dark:bg-${report.color}-900/20`
                  : `border-gray-200 dark:border-gray-700 ${isDarkMode ? 'bg-gray-800' : 'bg-white'} hover:border-${report.color}-300`
              }`}
            >
              <report.icon className={`h-8 w-8 text-${report.color}-500 mx-auto mb-2`} />
              <h3 className={`font-medium text-sm ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {report.title}
              </h3>
              <p className={`text-xs mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {report.description}
              </p>
            </button>
          ))}
        </div>

        {/* الفلاتر */}
        <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-sm p-6 mb-6`}>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">من تاريخ</label>
              <input
                type="date"
                value={dateRange.startDate}
                onChange={(e) => setDateRange({...dateRange, startDate: e.target.value})}
                className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-300'}`}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">إلى تاريخ</label>
              <input
                type="date"
                value={dateRange.endDate}
                onChange={(e) => setDateRange({...dateRange, endDate: e.target.value})}
                className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-300'}`}
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">نوع الإجازة</label>
              <select
                value={filters.leaveType}
                onChange={(e) => setFilters({...filters, leaveType: e.target.value})}
                className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-300'}`}
              >
                <option value="">جميع الأنواع</option>
                <option value="annual">إجازة سنوية</option>
                <option value="sick">إجازة مرضية</option>
                <option value="emergency">إجازة طارئة</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">الحالة</label>
              <select
                value={filters.status}
                onChange={(e) => setFilters({...filters, status: e.target.value})}
                className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-300'}`}
              >
                <option value="">جميع الحالات</option>
                <option value="approved">معتمدة</option>
                <option value="rejected">مرفوضة</option>
                <option value="pending">قيد المراجعة</option>
              </select>
            </div>
          </div>
        </div>

        {/* محتوى التقرير */}
        <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-sm p-6`}>
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
              <span className="mr-3">جاري تحميل التقرير...</span>
            </div>
          ) : (
            renderReportContent()
          )}
        </div>
      </div>
    </MainLayout>
  );
}
