const { getConnection, sql } = require('../src/utils/db');

async function createSampleCosts() {
  let pool;

  try {
    console.log('🔗 الاتصال بقاعدة البيانات...');
    pool = await getConnection();

    // إنشاء جدول MonthlyCosts إذا لم يكن موجوداً
    console.log('📋 إنشاء جدول MonthlyCosts...');
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MonthlyCosts' AND xtype='U')
      BEGIN
        CREATE TABLE MonthlyCosts (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          CostType NVARCHAR(50) NOT NULL,
          Month INT NOT NULL,
          Year INT NOT NULL,
          TotalAmount DECIMAL(15,2) NOT NULL DEFAULT 0,
          ItemsCount INT DEFAULT 0,
          AverageCostPerItem DECIMAL(10,2) DEFAULT 0,
          Details NVARCHAR(MAX),
          HasDocument BIT DEFAULT 0,
          DocumentPath NVARCHAR(500),
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),
          CreatedBy NVARCHAR(100),
          Notes NVARCHAR(MAX),
          UNIQUE(CostType, Month, Year)
        )
      END
      ELSE
      BEGIN
        -- إضافة العمود إذا لم يكن موجوداً
        IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('MonthlyCosts') AND name = 'AverageCostPerItem')
        BEGIN
          ALTER TABLE MonthlyCosts ADD AverageCostPerItem DECIMAL(10,2) DEFAULT 0
        END
      END
    `);

    // حذف البيانات الموجودة للشهر الحالي
    console.log('🗑️ حذف البيانات الموجودة...');
    await pool.request()
      .input('month', sql.Int, 1)
      .input('year', sql.Int, 2025)
      .query(`
        DELETE FROM MonthlyCosts
        WHERE Month = @month AND Year = @year
      `);

    // إنشاء بيانات تجريبية
    const sampleCosts = [
      {
        costType: 'carscost',
        month: 1,
        year: 2025,
        totalAmount: 50000,
        itemsCount: 10,
        notes: 'تكاليف السيارات - يناير 2025'
      },
      {
        costType: 'housingcost',
        month: 1,
        year: 2025,
        totalAmount: 75000,
        itemsCount: 15,
        notes: 'تكاليف الشقق - يناير 2025'
      },
      {
        costType: '3amala',
        month: 1,
        year: 2025,
        totalAmount: 30000,
        itemsCount: 8,
        notes: 'تكاليف العمالة المؤقتة - يناير 2025'
      }
    ];

    console.log('📝 إنشاء البيانات التجريبية...');
    for (const cost of sampleCosts) {
      const averageCostPerItem = cost.itemsCount > 0 ? (cost.totalAmount / cost.itemsCount) : 0;

      await pool.request()
        .input('costType', sql.NVarChar, cost.costType)
        .input('month', sql.Int, cost.month)
        .input('year', sql.Int, cost.year)
        .input('totalAmount', sql.Decimal(15, 2), cost.totalAmount)
        .input('itemsCount', sql.Int, cost.itemsCount)
        .input('averageCostPerItem', sql.Decimal(10, 2), averageCostPerItem)
        .input('notes', sql.NVarChar, cost.notes)
        .input('details', sql.NVarChar, JSON.stringify({
          createdBy: 'سكريبت البيانات التجريبية',
          timestamp: new Date().toISOString()
        }))
        .query(`
          INSERT INTO MonthlyCosts (
            CostType, Month, Year, TotalAmount, ItemsCount,
            AverageCostPerItem, Notes, Details, CreatedBy
          )
          VALUES (
            @costType, @month, @year, @totalAmount, @itemsCount,
            @averageCostPerItem, @notes, @details, 'سكريبت البيانات التجريبية'
          )
        `);

      console.log(`✅ تم إنشاء تكلفة ${cost.costType}: ${cost.totalAmount} جنيه`);
    }

    // التحقق من البيانات المنشأة
    console.log('🔍 التحقق من البيانات المنشأة...');
    const result = await pool.request()
      .input('month', sql.Int, 1)
      .input('year', sql.Int, 2025)
      .query(`
        SELECT
          CostType, TotalAmount, ItemsCount, AverageCostPerItem, Notes, CreatedAt
        FROM MonthlyCosts
        WHERE Month = @month AND Year = @year
        ORDER BY CostType
      `);

    console.log('\n📊 البيانات المنشأة:');
    result.recordset.forEach(record => {
      console.log(`- ${record.CostType}: ${record.TotalAmount} جنيه (${record.ItemsCount} عنصر)`);
    });

    console.log('\n🎉 تم إنشاء البيانات التجريبية بنجاح!');
    console.log('يمكنك الآن زيارة http://localhost:3001/costs/dashboard لرؤية البيانات');

  } catch (error) {
    console.error('❌ خطأ في إنشاء البيانات التجريبية:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    if (pool) {
      await pool.close();
    }
  }
}

// تشغيل السكريبت
if (require.main === module) {
  createSampleCosts();
}

module.exports = { createSampleCosts };
