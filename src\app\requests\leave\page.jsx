'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/MainLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import { FiCalendar, FiSave, FiArrowLeft, FiDownload, FiUser, FiPrinter, FiRefreshCw } from 'react-icons/fi';

const LeaveRequestPage = () => {
  const router = useRouter();
  const { isArabic } = useLanguage();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    employeeName: '',
    employeeId: '',
    department: '',
    jobTitle: '',
    nationalId: '',
    leaveType: '',
    startDate: '',
    endDate: '',
    totalDays: '',
    reason: '',
    emergencyContact: '',
    emergencyPhone: '',
    replacementEmployee: '',
    notes: '',
    lastLeaveDate: '',
    leaveBalance: ''
  });

  const [employeeSearchResults, setEmployeeSearchResults] = useState([]);
  const [showEmployeeSearch, setShowEmployeeSearch] = useState(false);

  // أنواع الإجازات (مطابقة للنموذج الأصلي)
  const leaveTypes = [
    { value: 'annual', label: isArabic ? 'إجازة إعتيادية' : 'Annual Leave' },
    { value: 'emergency', label: isArabic ? 'إجازة عارضة' : 'Emergency Leave' },
    { value: 'sick', label: isArabic ? 'إجازة مرضية' : 'Sick Leave' },
    { value: 'unpaid', label: isArabic ? 'إجازة بدون أجر' : 'Unpaid Leave' },
    { value: 'badal', label: isArabic ? 'إجازة بدل' : 'Compensation Leave' },
    { value: 'other', label: isArabic ? 'إجازة أخرى' : 'Other Leave' }
  ];

  // جلب بيانات الموظف المسجل
  // تم إزالة التحميل التلقائي للبيانات - النموذج يبدأ فارغاً

  // حساب تاريخ النهاية تلقائياً
  useEffect(() => {
    if (formData.startDate && formData.totalDays && formData.totalDays > 0) {
      // التحقق من صحة تنسيق التاريخ dd/mm/yyyy
      const dateRegex = /^(\d{2})\/(\d{2})\/(\d{4})$/;
      const match = formData.startDate.match(dateRegex);

      if (match) {
        const [, day, month, year] = match;
        const startDate = new Date(year, month - 1, day);

        // التحقق من صحة التاريخ
        if (startDate.getDate() == day && startDate.getMonth() == month - 1 && startDate.getFullYear() == year) {
          const endDate = new Date(startDate);
          endDate.setDate(startDate.getDate() + parseInt(formData.totalDays) - 1);

          const endDay = endDate.getDate().toString().padStart(2, '0');
          const endMonth = (endDate.getMonth() + 1).toString().padStart(2, '0');
          const endYear = endDate.getFullYear();

          setFormData(prev => ({
            ...prev,
            endDate: `${endDay}/${endMonth}/${endYear}`
          }));
        }
      }
    }
  }, [formData.startDate, formData.totalDays]);

  // البحث عن الموظفين من قاعدة البيانات
  const searchEmployees = async (searchTerm) => {
    if (!searchTerm || searchTerm.length < 2) {
      setEmployeeSearchResults([]);
      setShowEmployeeSearch(false);
      return;
    }

    try {
      const response = await fetch('/api/employee-live-search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          searchTerm: searchTerm,
          limit: 10
        })
      });

      const result = await response.json();
      if (result.success) {
        const employees = result.data || [];
        // تحويل البيانات للتنسيق المطلوب
        const formattedEmployees = employees.map(emp => ({
          employeeId: emp.EmployeeCode || emp.employeeCode,
          fullName: emp.EmployeeName || emp.employeeName,
          department: emp.Department || emp.department,
          jobTitle: emp.JobTitle || emp.jobTitle,
          nationalId: emp.NationalID || emp.nationalId || ''
        }));

        setEmployeeSearchResults(formattedEmployees);
        setShowEmployeeSearch(formattedEmployees.length > 0);
      } else {

        setEmployeeSearchResults([]);
        setShowEmployeeSearch(false);
      }
    } catch (error) {

      setEmployeeSearchResults([]);
      setShowEmployeeSearch(false);
    }
  };

  // اختيار موظف من نتائج البحث
  const selectEmployee = async (employee) => {
    setFormData(prev => ({
      ...prev,
      employeeName: employee.fullName,
      employeeId: employee.employeeId,
      department: employee.department,
      jobTitle: employee.jobTitle,
      nationalId: employee.nationalId
    }));

    // جلب معلومات الإجازة من قاعدة البيانات
    try {
      const response = await fetch(`/api/employees/${employee.employeeId}/leave-info`);
      const result = await response.json();

      if (result.success && result.data) {
        const leaveInfo = result.data;
        setFormData(prev => ({
          ...prev,
          lastLeaveDate: leaveInfo.lastLeaveDate,
          leaveBalance: `إعتيادي: ${leaveInfo.regularBalance} يوم | عارضة: ${leaveInfo.casualBalance} يوم`
        }));
      } else {
        // في حالة عدم وجود بيانات
        setFormData(prev => ({
          ...prev,
          lastLeaveDate: 'لا توجد إجازات سابقة',
          leaveBalance: 'إعتيادي: 15 يوم | عارضة: 6 أيام'
        }));
      }
    } catch (error) {

      // استخدام قيم افتراضية في حالة الخطأ
      setFormData(prev => ({
        ...prev,
        lastLeaveDate: 'غير متاح',
        leaveBalance: 'إعتيادي: 15 يوم | عارضة: 6 أيام'
      }));
    }

    setShowEmployeeSearch(false);
    setEmployeeSearchResults([]);

    // تحديث الرصيد إذا كان نوع الإجازة محدد
    if (formData.leaveType) {
      setTimeout(() => {
        updateLeaveBalance(formData.leaveType, employee.employeeId);
      }, 500);
    }
  };

  // تحديث رصيد الإجازة بناءً على النوع
  const updateLeaveBalance = async (leaveType, employeeId) => {
    if (!employeeId || !leaveType) {

      return;
    }

    try {

      const response = await fetch(`/api/leave-balance?employeeId=${employeeId}&leaveType=${leaveType}`);
      const result = await response.json();

      if (result.success) {
        setFormData(prev => ({
          ...prev,
          leaveBalance: result.balance
        }));

      } else {

        setFormData(prev => ({
          ...prev,
          leaveBalance: 'لم يتم العثور على رصيد الإجازة'
        }));
      }
    } catch (error) {

      setFormData(prev => ({
        ...prev,
        leaveBalance: 'خطأ في جلب الرصيد'
      }));
    }
  };

  // تنسيق التاريخ أثناء الكتابة
  const formatDateInput = (value) => {
    // إزالة كل شيء عدا الأرقام
    const numbers = value.replace(/\D/g, '');

    // تطبيق التنسيق dd/mm/yyyy
    if (numbers.length <= 2) {
      return numbers;
    } else if (numbers.length <= 4) {
      return numbers.slice(0, 2) + '/' + numbers.slice(2);
    } else {
      return numbers.slice(0, 2) + '/' + numbers.slice(2, 4) + '/' + numbers.slice(4, 8);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    // تنسيق التاريخ تلقائياً
    if (name === 'startDate') {
      const formattedDate = formatDateInput(value);
      setFormData(prev => ({ ...prev, [name]: formattedDate }));
      return; // منع التحديث المزدوج
    }

    // تحديث القيمة العادية
    setFormData(prev => ({ ...prev, [name]: value }));

    // البحث التلقائي عند كتابة كود الموظف فقط
    if (name === 'employeeId') {
      // مسح بيانات الموظف السابقة عند تغيير الكود
      setFormData(prev => ({
        ...prev,
        employeeName: '',
        department: '',
        jobTitle: '',
        lastLeaveDate: '',
        leaveBalance: ''
      }));

      // البحث عن الموظف الجديد
      searchEmployees(value);
    }

    // تحديث الرصيد عند تغيير نوع الإجازة
    if (name === 'leaveType') {
      // استخدام setTimeout للتأكد من تحديث formData أولاً
      setTimeout(() => {
        const currentEmployeeId = formData.employeeId;
        if (currentEmployeeId && value) {

          updateLeaveBalance(value, currentEmployeeId);
        } else {

        }
      }, 100);
    }
  };

  // دالة التحقق من الإجراءات الموجودة والقيود
  const checkExistingActions = async () => {
    try {
      const response = await fetch('/api/check-employee-actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          employeeCode: formData.employeeId,
          startDate: formData.startDate,
          endDate: formData.endDate,
          actionType: 'leave',
          leaveType: formData.leaveType
        })
      });

      const result = await response.json();
      return result;
    } catch (error) {

      return { success: false, error: 'خطأ في التحقق من الإجراءات' };
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // منع التقديم المتكرر
    if (loading) {
      return;
    }

    // التحقق من الحقول المطلوبة
    if (!formData.employeeId || !formData.leaveType || !formData.startDate || !formData.totalDays) {
      alert(isArabic ? 'يرجى ملء جميع الحقول المطلوبة' : 'Please fill all required fields');
      return;
    }

    setLoading(true);

    // التحقق من الإجراءات الموجودة والقيود
    const checkResult = await checkExistingActions();

    if (!checkResult.success) {
      alert(checkResult.error || 'خطأ في التحقق من الإجراءات');
      setLoading(false);
      return;
    }

    // إذا كان هناك تضارب
    if (checkResult.hasConflict) {
      const conflict = checkResult.conflictDetails;

      // رسالة التحذير
      const conflictMessage = `⚠️ تحذير: يوجد إجراء "${conflict.actionType}" مسجل بالفعل للموظف!\n\n` +
        `الموظف: ${formData.employeeName} (${formData.employeeId})\n` +
        `الإجراء الموجود: ${conflict.actionType}\n` +
        `الفترة: من ${conflict.startDate} إلى ${conflict.endDate}\n` +
        `الحالة: ${conflict.status}\n\n`;

      // إذا كان الإجراء معتمد، لا يمكن الاستبدال
      if (conflict.status === 'معتمد' || conflict.status === 'approved') {
        alert(conflictMessage + 'لا يمكن استبدال الإجراءات المعتمدة. يرجى اختيار فترة زمنية أخرى.');
        setLoading(false);
        return;
      }

      // إذا كان الإجراء قيد المراجعة، اعرض خيارات الاستبدال
      const userChoice = confirm(conflictMessage +
        'هل تريد استبدال الإجراء الموجود بالإجراء الجديد؟\n\n' +
        'اضغط "موافق" للاستبدال أو "إلغاء" للعودة وتعديل البيانات.');

      if (!userChoice) {
        setLoading(false);
        return;
      }

      // تنفيذ الاستبدال الفعلي

      try {
        const replaceResponse = await fetch('/api/replace-employee-action', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            conflictId: conflict.id,
            conflictSource: conflict.source,
            newRequestData: {
              requestType: 'leave',
              employeeCode: formData.employeeId,
              employeeName: formData.employeeName,
              startDate: formData.startDate,
              endDate: formData.endDate,
              leaveType: formData.leaveType,
              notes: formData.notes || ''
            }
          })
        });

        const replaceResult = await replaceResponse.json();

        if (replaceResult.success) {
          alert('تم استبدال الإجراء وتقديم طلب الإجازة بنجاح!');
          // إعادة تعيين النموذج
          setFormData({
            employeeName: '',
            employeeId: '',
            department: '',
            jobTitle: '',
            nationalId: '',
            leaveType: '',
            startDate: '',
            endDate: '',
            totalDays: '',
            reason: '',
            emergencyContact: '',
            emergencyPhone: '',
            replacementEmployee: '',
            lastLeaveDate: '',
            leaveBalance: '',
            notes: ''
          });
          setLoading(false);
          return;
        } else {
          alert('خطأ في الاستبدال: ' + replaceResult.error);
          setLoading(false);
          return;
        }
      } catch (replaceError) {

        alert('خطأ في تنفيذ الاستبدال');
        setLoading(false);
        return;
      }
    }

    // إذا كان هناك قيود (استقالة، نقل، تاريخ انضمام)
    if (checkResult.hasRestriction) {
      alert(checkResult.restrictionMessage);
      setLoading(false);
      return;
    }

    try {
      // إعداد البيانات للإرسال
      const submitData = {
        action: 'create',
        ...formData,
        submittedAt: new Date().toISOString()
      };

      // الحصول على كود المستخدم من localStorage
      const userInfo = localStorage.getItem('userInfo');
      const userCode = userInfo ? JSON.parse(userInfo).code || '1450' : '1450';

      const response = await fetch('/api/paper-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-code': userCode, // إرسال كود المستخدم
        },
        body: JSON.stringify({
          ...submitData,
          requestType: 'leave'
        }),
      });

      const result = await response.json();

      if (result.success) {
        alert(isArabic ? 'تم تقديم طلب الإجازة بنجاح!' : 'Leave request submitted successfully!');

        // إفراغ الحقول تلقائياً بعد النجاح
        setFormData({
          employeeName: '',
          employeeId: '',
          department: '',
          jobTitle: '',
          nationalId: '',
          leaveType: '',
          startDate: '',
          endDate: '',
          totalDays: '',
          reason: '',
          emergencyContact: '',
          emergencyPhone: '',
          replacementEmployee: '',
          lastLeaveDate: '',
          leaveBalance: '',
          notes: ''
        });

        // إخفاء نتائج البحث
        setShowEmployeeSearch(false);
        setEmployeeSearchResults([]);

        // لا نوجه المستخدم تلقائياً - يبقى في نفس الصفحة
      } else {
        // التحقق من وجود تضارب
        if (response.status === 409 && result.conflicts) {
          const conflict = result.conflicts[0];
          const conflictMessage = `⚠️ تحذير: يوجد تضارب!\n\n` +
            `لا يمكن تقديم طلب إجازة للموظف "${formData.employeeName}" في الفترة من ${formData.startDate} إلى ${formData.endDate}\n\n` +
            `السبب: يوجد ${conflict.type} مسجل لنفس الموظف في الفترة من ${conflict.startDate} إلى ${conflict.endDate}\n` +
            `حالة الطلب المتضارب: ${conflict.status}\n\n` +
            `يرجى اختيار فترة زمنية أخرى أو مراجعة الطلبات المسجلة مسبقاً.`;

          alert(conflictMessage);
        } else {
          alert(result.error || result.message || (isArabic ? 'خطأ في تقديم الطلب' : 'Error submitting request'));
        }
      }
    } catch (error) {

      alert(isArabic ? 'خطأ في الاتصال بالخادم' : 'Server connection error');
    } finally {
      setLoading(false);
    }
  };

  const downloadForm = async () => {
    try {
      const response = await fetch('/api/requests/download-form?type=leave');
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'نموذج_طلب_الإجازة.xlsx';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {

    }
  };

  // دالة إفراغ الحقول لإجازة جديدة
  const clearForm = () => {
    if (confirm(isArabic ? 'هل أنت متأكد من إفراغ جميع الحقول؟' : 'Are you sure you want to clear all fields?')) {
      setFormData({
        employeeId: '',
        employeeName: '',
        department: '',
        jobTitle: '',
        leaveType: '',
        startDate: '',
        endDate: '',
        daysCount: '',
        reason: '',
        lastLeaveDate: '',
        leaveBalance: ''
      });
    }
  };

  // تحويل اللوجو إلى base64
  const getLogoBase64 = async () => {
    try {
      // جرب مسارات مختلفة للوجو
      const logoPaths = ['/logo.png', '/public/logo.png', '/assets/logo.png'];

      for (const path of logoPaths) {
        try {
          const response = await fetch(path);
          if (response.ok) {
            const blob = await response.blob();
            return new Promise((resolve, reject) => {
              const reader = new FileReader();
              reader.onloadend = () => {
                const result = reader.result;
                if (result) {
                  resolve(result.split(',')[1]);
                } else {
                  reject('فشل في قراءة اللوجو');
                }
              };
              reader.onerror = () => reject('خطأ في قراءة الملف');
              reader.readAsDataURL(blob);
            });
          }
        } catch (err) {

          continue;
        }
      }

      throw new Error('لم يتم العثور على اللوجو في أي مسار');
    } catch (error) {

      return null;
    }
  };

  // تحويل التاريخ إلى تنسيق dd/mm/yyyy للطباعة
  const formatDateForPrint = (dateInput) => {
    if (!dateInput) return '';

    // تحويل إلى string إذا لم يكن كذلك
    const dateString = dateInput.toString();

    // إذا كان التاريخ بالفعل بتنسيق dd/mm/yyyy
    if (dateString.includes('/')) return dateString;

    // إذا كان بتنسيق yyyy-mm-dd أو Date object
    const date = new Date(dateInput);

    // التحقق من صحة التاريخ
    if (isNaN(date.getTime())) return '';

    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  // طباعة النموذج المملوء (مطابق 100% للنموذج الأصلي)
  const printFilledForm = async () => {
    // جلب البيانات التلقائية قبل الطباعة
    let lastLeaveDate = '';
    let remainingBalance = '';

    if (formData.employeeId && formData.leaveType) {
      try {
        // جلب تاريخ آخر إجازة معتمدة
        const lastLeaveResponse = await fetch(`/api/paper-requests?action=getLastApprovedLeave&employeeId=${formData.employeeId}`);
        if (lastLeaveResponse.ok) {
          const lastLeaveResult = await lastLeaveResponse.json();
          if (lastLeaveResult.success && lastLeaveResult.lastLeave) {
            lastLeaveDate = formatDateForPrint(lastLeaveResult.lastLeave.EndDate);
          }
        }

        // جلب الرصيد المتبقي
        const balanceResponse = await fetch(`/api/leave-balance?employeeId=${formData.employeeId}&leaveType=${formData.leaveType}`);
        if (balanceResponse.ok) {
          const balanceResult = await balanceResponse.json();
          if (balanceResult.success) {
            remainingBalance = balanceResult.balance || '';
          }
        }
      } catch (error) {

      }
    }

    // تحميل اللوجو أولاً قبل فتح نافذة الطباعة
    const logoBase64 = await getLogoBase64();

    // انتظار إضافي للتأكد من تحميل اللوجو
    await new Promise(resolve => setTimeout(resolve, 1000));

    const printWindow = window.open('', '_blank');

    // إضافة timestamp لمنع cache
    const timestamp = new Date().getTime();

    const printContent = `
      <!DOCTYPE html>
      <html dir="rtl">
      <!-- Updated: ${timestamp} - Fixed name/code order, spacing, notes alignment -->
      <head>
        <meta charset="UTF-8">
        <title>نموذج طلب إجازة - ${formData.employeeName}</title>
        <style>
          @page {
            size: A4 portrait;
            margin: 10mm;
          }

          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          body {
            font-family: 'Arial', sans-serif;
            font-size: 11px;
            line-height: 1.3;
            color: #000 !important;
            background: white !important;
            direction: rtl;
            text-align: right;
            width: 190mm;
            max-width: 190mm;
            margin: 0 auto;
            padding: 5mm;
          }

          /* Header Table - مطابق للنموذج الأصلي تماماً */
          .header-table {
            width: 100%;
            border-collapse: collapse;
            border: 2px solid #000;
            margin-bottom: 8px;
            table-layout: fixed;
          }

          .header-table td {
            border: 1px solid #000;
            padding: 6px;
            text-align: center;
            vertical-align: middle;
            height: 50px;
            font-size: 10px;
          }

          .logo-cell {
            width: 33.33%;
          }

          .logo-img {
            max-width: 100px;
            max-height: 70px;
            object-fit: contain;
          }

          .form-title-cell {
            width: 33.33%;
            font-weight: bold;
            text-align: center;
          }

          .company-cell {
            width: 33.33%;
            font-weight: bold;
          }

          .form-title {
            font-size: 14px;
            margin-bottom: 3px;
            font-weight: bold;
          }

          .form-code {
            font-size: 10px;
            color: #666;
          }

          .company-name-ar {
            font-size: 10px;
            margin-bottom: 2px;
            font-weight: bold;
          }

          .company-name-en {
            font-size: 8px;
            color: #666;
            font-style: italic;
          }

          /* Section Headers - مطابق للنموذج الأصلي */
          .section-header {
            background-color: #f0f0f0;
            text-align: center;
            padding: 6px;
            font-size: 11px;
            font-weight: bold;
            border: 1px solid #000;
            margin: 8px 0 5px 0;
          }

          /* Form Fields */
          .form-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            min-height: 25px;
            font-size: 10px;
          }

          .field-label {
            font-weight: bold;
            margin-left: 8px;
            min-width: 100px;
            font-size: 10px;
            text-align: right;
            flex-shrink: 0;
          }

          .field-value {
            border-bottom: 1px solid #000;
            padding: 2px 6px;
            min-width: 120px;
            text-align: center;
            font-size: 10px;
          }

          .full-width-field {
            border-bottom: 1px solid #000;
            padding: 2px 6px;
            flex: 1;
            text-align: right;
            font-size: 10px;
            margin-right: 0;
          }

          /* محاذاة خاصة للصفوف الكاملة العرض */
          .full-width-row {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            min-height: 20px;
            font-size: 10px;
          }

          .full-width-row .field-label {
            width: 100px;
            min-width: 100px;
            text-align: right;
            margin-left: 8px;
            flex-shrink: 0;
          }

          /* Leave Type Section */
          .leave-types-container {
            display: flex;
            margin: 5px 0;
            font-size: 10px;
            border: 1px solid #000 !important;
          }

          .leave-types-left {
            width: 50%;
            padding: 8px;
            border-right: 1px solid #000 !important;
          }

          .leave-types-right {
            width: 50%;
            padding: 8px;
          }

          .leave-option {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            font-size: 10px;
          }

          .checkbox {
            width: 10px;
            height: 10px;
            border: 1px solid #000;
            margin-left: 6px;
            display: inline-block;
            text-align: center;
            line-height: 8px;
            font-size: 7px;
          }

          .checkbox.checked {
            background-color: #000;
            color: white;
          }

          .leave-duration {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 5px 0;
            padding: 5px;
            border: 1px solid #000;
          }

          /* Signatures Section */
          .signatures-container {
            margin-top: 20px;
          }

          .signature-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
          }

          .signature-box {
            text-align: center;
            width: 200px;
          }

          .signature-title {
            font-weight: bold;
            font-size: 10px;
            margin-bottom: 5px;
          }

          .signature-line {
            border-bottom: 1px solid #000;
            height: 20px;
            margin-bottom: 5px;
          }

          /* HR Section */
          .hr-section {
            margin-top: 20px;
            border: 1px solid #000;
          }

          .hr-header {
            background-color: #a8c5f0;
            text-align: center;
            padding: 8px;
            font-weight: bold;
            font-size: 12px;
            border-bottom: 1px solid #000;
          }

          .hr-content {
            padding: 15px;
          }

          .notes-lines {
            line-height: 2;
            margin-bottom: 15px;
          }

          .hr-specialist {
            text-align: center;
            margin: 15px 0;
          }

          .hr-notes {
            font-size: 10px;
            border-top: 1px solid #000;
            padding-top: 10px;
            margin-top: 15px;
          }

          @media print {
            @page {
              size: A4 portrait;
              margin: 10mm;
            }

            body {
              margin: 0 !important;
              padding: 0 !important;
              width: 190mm !important;
              max-width: 190mm !important;
              font-size: 10px !important;
            }

            .no-print {
              display: none !important;
            }

            * {
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
            }
          }
        </style>
      </head>
      <body>
        <!-- Header Table - مطابق للنموذج الأصلي تماماً -->
        <table class="header-table">
          <tr>
            <td class="company-cell">
              <div class="company-name-ar">شركة كونكورد للهندسة والمقاولات</div>
              <div class="company-name-en">Concord for Engineering & Contracting</div>
            </td>
            <td class="form-title-cell">
              <div class="form-title">طلب إجازة</div>
              <div class="form-code">HR-OP-01-F01</div>
            </td>
            <td class="logo-cell">
              ${logoBase64 ?
                `<img src="data:image/png;base64,${logoBase64}" alt="Concord Logo" class="logo-img" />` :
                `<div style="background:#1e40af;color:white;padding:8px;text-align:center;font-weight:bold;font-size:10px;border-radius:4px;">CONCORD<br>COMPANY</div>`
              }
            </td>
          </tr>
        </table>

        <!-- Section Header -->
        <div class="section-header">بيانات الطلب</div>

        <!-- Employee Information - مطابق للنموذج الأصلي -->
        <div class="form-row" style="justify-content: space-between; align-items: center; margin-bottom: 10px;">
          <div style="display: flex; align-items: center;">
            <span class="field-label" style="margin-left: 8px;">الاسم:</span>
            <div class="field-value" style="width: 300px; text-align: center;">${formData.employeeName || ''}</div>
          </div>
          <div style="display: flex; align-items: center;">
            <span class="field-label" style="margin-left: 8px;">الكود الوظيفي:</span>
            <div class="field-value" style="width: 100px; text-align: center;">${formData.employeeId || ''}</div>
          </div>
        </div>

        <div class="full-width-row">
          <span class="field-label">الوظيفة:</span>
          <div class="full-width-field">${formData.jobTitle || ''}</div>
        </div>

        <div class="full-width-row">
          <span class="field-label">الإدارة / المشروع:</span>
          <div class="full-width-field">مشروع مجمع مبانى أوجيستا</div>
        </div>

        <!-- البيانات الإضافية المطلوبة -->
        <div class="form-row" style="justify-content: space-between; align-items: center; margin-bottom: 10px;">
          <div style="display: flex; align-items: center;">
            <span class="field-label" style="margin-left: 8px;">تاريخ الطباعة:</span>
            <div class="field-value" style="width: 120px; text-align: center;">${formatDateForPrint(new Date())}</div>
          </div>
          <div style="display: flex; align-items: center;">
            <span class="field-label" style="margin-left: 8px;">تاريخ آخر إجازة:</span>
            <div class="field-value" style="width: 120px; text-align: center;">${lastLeaveDate}</div>
          </div>
          <div style="display: flex; align-items: center;">
            <span class="field-label" style="margin-left: 8px;">الرصيد المتبقي:</span>
            <div class="field-value" style="width: 100px; text-align: center;">${remainingBalance}</div>
          </div>
        </div>

        <!-- Leave Types Section - مطابق للنموذج الأصلي مع إضافة الضلع الناقص -->
        <div class="leave-types-container" style="border: 1px solid #000;">
          <div class="leave-types-left" style="border-right: 1px solid #000;">
            <div class="leave-option">
              <span class="checkbox ${formData.leaveType === 'annual' ? 'checked' : ''}">
                ${formData.leaveType === 'annual' ? '■' : ''}
              </span>
              <span>إجازة إعتيادية</span>
            </div>
            <div class="leave-option">
              <span class="checkbox ${formData.leaveType === 'sick' ? 'checked' : ''}">
                ${formData.leaveType === 'sick' ? '■' : ''}
              </span>
              <span>إجازة مرضية / إصابة</span>
            </div>
          </div>
          <div class="leave-types-right">
            <div class="leave-option">
              <span class="checkbox ${formData.leaveType === 'emergency' ? 'checked' : ''}">
                ${formData.leaveType === 'emergency' ? '■' : ''}
              </span>
              <span>إجازة عارضة</span>
            </div>
            <div class="leave-option">
              <span class="checkbox ${formData.leaveType === 'unpaid' ? 'checked' : ''}">
                ${formData.leaveType === 'unpaid' ? '■' : ''}
              </span>
              <span>إجازة بدون أجر</span>
            </div>
          </div>
        </div>

        <div style="text-align: center; font-size: 10px; margin: 5px 0;">
          ( بدل - حج - عمرة - وضع - ولادة - وفاة - زواج )
        </div>
        <div style="text-align: center; font-size: 10px; margin-bottom: 10px;">
          ( إجازات أخرى )
        </div>

        <div class="leave-option" style="text-align: center; margin: 10px 0;">
          <span class="checkbox ${formData.leaveType === 'badal' ? 'checked' : ''}">
            ${formData.leaveType === 'badal' ? '■' : ''}
          </span>
          <span>إجازة بدل</span>
        </div>

        <!-- Leave Duration - مطابق للنموذج الأصلي -->
        <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
          <tr>
            <td style="border: 1px solid #000; padding: 8px; text-align: center; width: 33.33%;">
              <span style="font-weight: bold;">عدد الأيام:</span>
              <div style="margin-top: 5px;">
                <span style="border-bottom: 1px solid #000; padding: 2px 8px; margin: 0 5px;">${formData.totalDays || '2'}</span>
              </div>
            </td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center; width: 33.33%;">
              <span style="font-weight: bold;">إلى:</span>
              <div style="margin-top: 5px;">
                <span style="border-bottom: 1px solid #000; padding: 2px 8px; margin: 0 5px;">${formatDateForPrint(formData.endDate) || '11/06/2025'}</span>
              </div>
            </td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center; width: 33.33%;">
              <span style="font-weight: bold;">مدة الإجازة من:</span>
              <div style="margin-top: 5px;">
                <span style="border-bottom: 1px solid #000; padding: 2px 8px; margin: 0 5px;">${formatDateForPrint(formData.startDate) || '10/06/2025'}</span>
              </div>
            </td>
          </tr>
        </table>

        <!-- Signatures Section - مطابق للنموذج الأصلي -->
        <div style="margin: 15px 0;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
            <div style="text-align: center; width: 45%;">
              <div style="font-weight: bold; margin-bottom: 30px;">توقيع الموظف</div>
              <div style="border-bottom: 1px solid #000; width: 150px; margin: 0 auto;"></div>
            </div>
            <div style="text-align: center; width: 45%;">
              <div style="font-weight: bold; margin-bottom: 30px;">اعتماد الرئيس المباشر</div>
              <div style="border-bottom: 1px solid #000; width: 150px; margin: 0 auto;"></div>
            </div>
          </div>
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
            <div style="text-align: center; width: 45%;">
              <div style="font-weight: bold; margin-bottom: 30px;">المدير الإداري</div>
              <div style="border-bottom: 1px solid #000; width: 150px; margin: 0 auto;"></div>
            </div>
            <div style="text-align: center; width: 45%;">
              <div style="font-weight: bold; margin-bottom: 30px;">اعتماد مدير المشروع</div>
              <div style="border-bottom: 1px solid #000; width: 150px; margin: 0 auto;"></div>
            </div>
          </div>
        </div>

        <!-- HR Section - مطابق للنموذج الأصلي -->
        <div class="hr-section">
          <div class="hr-header">إدارة الموارد البشرية</div>
          <div class="hr-content">
            <!-- الملاحظات في الأعلى -->
            <div style="text-align: right; margin-bottom: 20px;">
              <div style="font-weight: bold; margin-bottom: 8px;">ملاحظات:</div>
              <div style="line-height: 1.8;">
                ................................................................................................................................................................................................<br>
                ................................................................................................................................................................................................
              </div>
            </div>

            <!-- توقيع أخصائي موارد بشرية تحت الملاحظات -->
            <div style="text-align: left; margin-bottom: 15px;">
              <div style="font-weight: bold; margin-bottom: 30px;">أخصائي موارد بشرية</div>
              <div style="border-bottom: 1px solid #000; width: 150px; height: 15px;"></div>
            </div>

            <!-- الملاحظات السفلية -->
            <div style="font-size: 10px; text-align: right; border-top: 1px solid #000; padding-top: 10px;">
              <div style="margin-bottom: 3px;">
                في حالة الإجازة المرضية يتم إرفاق التقرير الطبي.
              </div>
              <div>
                في حالة عدم وجود رصيد إجازات سنوية يتم احتساب الطلب إجازة بدون أجر.
              </div>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;

    if (printWindow && printWindow.document) {
      printWindow.document.write(printContent);
      printWindow.document.close();
    } else {

      return;
    }

    // انتظار تحميل المحتوى والصور قبل الطباعة
    printWindow.onload = () => {
      setTimeout(() => {
        printWindow.print();
      }, 500);
    };

    // احتياطي في حالة عدم تشغيل onload
    setTimeout(() => {
      printWindow.print();
    }, 1500);
  };

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-4xl mx-auto">
          {/* العنوان */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6 pulse-box">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-200 mb-2">
                  {isArabic ? 'طلب إجازة' : 'Leave Request'}
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  {isArabic ? 'تقديم طلب إجازة جديد' : 'Submit a new leave request'}
                </p>
              </div>
              <div className="flex gap-3">
                <button
                  onClick={printFilledForm}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
                >
                  <FiPrinter />
                  {isArabic ? 'طباعة طلب الإجازة' : 'Print Leave Request'}
                </button>

                <button
                  onClick={clearForm}
                  className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center gap-2"
                >
                  <FiRefreshCw />
                  {isArabic ? 'إجازة جديدة' : 'New Leave'}
                </button>

                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                  <FiCalendar className="text-2xl text-white" />
                </div>
              </div>
            </div>
          </div>

          {/* النموذج */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* بيانات الموظف */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 gentle-animated-box">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                <FiUser className="text-blue-600" />
                {isArabic ? 'بيانات الموظف' : 'Employee Information'}
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="relative">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'كود الموظف *' : 'Employee ID *'}
                  </label>
                  <input
                    type="text"
                    name="employeeId"
                    value={formData.employeeId}
                    onChange={handleInputChange}
                    required
                    placeholder={isArabic ? 'ابحث بكود الموظف...' : 'Search by employee ID...'}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
                    autoComplete="off"
                    autoCorrect="off"
                    autoCapitalize="off"
                    spellCheck="false"
                  />

                  {/* نتائج البحث */}
                  {showEmployeeSearch && employeeSearchResults.length > 0 && (
                    <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                      {employeeSearchResults.map((employee) => (
                        <div
                          key={employee.employeeId}
                          onClick={() => selectEmployee(employee)}
                          className="px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-200 dark:border-gray-600 last:border-b-0"
                        >
                          <div className="font-medium text-gray-900 dark:text-gray-100">
                            {employee.employeeId} - {employee.fullName}
                          </div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            {employee.department} | {employee.jobTitle}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'اسم الموظف' : 'Employee Name'}
                  </label>
                  <input
                    type="text"
                    name="employeeName"
                    value={formData.employeeName}
                    readOnly
                    placeholder={isArabic ? 'سيتم ملء الاسم تلقائياً بعد اختيار الموظف' : 'Name will be filled automatically after selecting employee'}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-600 dark:text-gray-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'القسم' : 'Department'}
                  </label>
                  <input
                    type="text"
                    name="department"
                    value={formData.department}
                    onChange={handleInputChange}
                    placeholder={isArabic ? 'أدخل اسم القسم...' : 'Enter department name...'}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'المسمى الوظيفي' : 'Job Title'}
                  </label>
                  <input
                    type="text"
                    name="jobTitle"
                    value={formData.jobTitle}
                    onChange={handleInputChange}
                    placeholder={isArabic ? 'أدخل المسمى الوظيفي...' : 'Enter job title...'}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
                  />
                </div>
              </div>
            </div>

            {/* تاريخ آخر إجازة */}
            {formData.lastLeaveDate && formData.lastLeaveDate !== 'لا توجد إجازات سابقة' && (
              <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 mb-6 border-l-4 border-green-500">
                <div className="flex items-center gap-2">
                  <FiCalendar className="text-green-600" />
                  <p className="text-sm text-green-700 dark:text-green-300 font-medium">
                    {isArabic ? `تاريخ آخر إجازة مسجلة: ${formData.lastLeaveDate}` : `Last registered leave date: ${formData.lastLeaveDate}`}
                  </p>
                </div>
              </div>
            )}

            {/* تفاصيل الإجازة */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 wave-box">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                <FiCalendar className="text-green-600" />
                {isArabic ? 'تفاصيل الإجازة' : 'Leave Details'}
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'نوع الإجازة *' : 'Leave Type *'}
                  </label>
                  <select
                    name="leaveType"
                    value={formData.leaveType}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
                  >
                    <option value="">{isArabic ? 'اختر نوع الإجازة' : 'Select leave type'}</option>
                    {leaveTypes.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'الرصيد المتبقي' : 'Remaining Balance'}
                  </label>
                  <div className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-blue-50 dark:bg-blue-900/20 min-h-[42px] flex items-center">
                    <span className="text-sm text-blue-700 dark:text-blue-300 font-medium">
                      {formData.leaveBalance || (isArabic ? 'اختر نوع الإجازة لعرض الرصيد' : 'Select leave type to show balance')}
                    </span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'تاريخ البداية *' : 'Start Date *'}
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      name="startDate"
                      value={formData.startDate}
                      onChange={handleInputChange}
                      required
                      placeholder="dd/mm/yyyy"
                      maxLength="10"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
                      style={{ direction: 'ltr' }}
                    />
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <FiCalendar className="text-gray-400" />
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">مثال: 15/03/2025</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'عدد الأيام *' : 'Number of Days *'}
                  </label>
                  <input
                    type="number"
                    name="totalDays"
                    value={formData.totalDays}
                    onChange={handleInputChange}
                    min="1"
                    max="365"
                    required
                    placeholder={isArabic ? 'أدخل عدد الأيام...' : 'Enter number of days...'}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'تاريخ النهاية (محسوب تلقائياً)' : 'End Date (Auto-calculated)'}
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      name="endDate"
                      value={formData.endDate}
                      readOnly
                      placeholder="dd/mm/yyyy"
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-600 dark:text-gray-200"
                      style={{ direction: 'ltr' }}
                    />
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <FiCalendar className="text-gray-400" />
                    </div>
                  </div>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'سبب الإجازة (اختياري)' : 'Reason for Leave (Optional)'}
                  </label>
                  <textarea
                    name="reason"
                    value={formData.reason}
                    onChange={handleInputChange}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
                    placeholder={isArabic ? 'اذكر سبب طلب الإجازة (اختياري)...' : 'Mention the reason for leave request (optional)...'}
                  />
                </div>
              </div>
            </div>

            {/* معلومات إضافية */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 gentle-animated-box">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
                {isArabic ? 'معلومات إضافية' : 'Additional Information'}
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'جهة الاتصال في الطوارئ' : 'Emergency Contact'}
                  </label>
                  <input
                    type="text"
                    name="emergencyContact"
                    value={formData.emergencyContact}
                    onChange={handleInputChange}
                    placeholder={isArabic ? 'اسم جهة الاتصال...' : 'Emergency contact name...'}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'رقم هاتف الطوارئ' : 'Emergency Phone'}
                  </label>
                  <input
                    type="tel"
                    name="emergencyPhone"
                    value={formData.emergencyPhone}
                    onChange={handleInputChange}
                    placeholder={isArabic ? 'رقم الهاتف...' : 'Phone number...'}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'الموظف البديل' : 'Replacement Employee'}
                  </label>
                  <input
                    type="text"
                    name="replacementEmployee"
                    value={formData.replacementEmployee}
                    onChange={handleInputChange}
                    placeholder={isArabic ? 'اسم الموظف البديل...' : 'Replacement employee name...'}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'ملاحظات' : 'Notes'}
                  </label>
                  <textarea
                    name="notes"
                    value={formData.notes}
                    onChange={handleInputChange}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
                    placeholder={isArabic ? 'ملاحظات إضافية...' : 'Additional notes...'}
                  />
                </div>
              </div>
            </div>

            {/* أزرار التحكم */}
            <div className="flex justify-end gap-4">
              <button
                type="button"
                onClick={() => router.back()}
                className="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors flex items-center gap-2"
              >
                <FiArrowLeft />
                {isArabic ? 'رجوع' : 'Back'}
              </button>
              
              <button
                type="submit"
                disabled={loading}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <FiSave />
                )}
                {loading ? (isArabic ? 'جاري التقديم...' : 'Submitting...') : (isArabic ? 'تقديم الطلب' : 'Submit Request')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </MainLayout>
  );
};

export default LeaveRequestPage;
