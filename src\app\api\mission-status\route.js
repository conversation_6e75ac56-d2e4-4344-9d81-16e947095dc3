/**
 * API للتحقق من حالة طلبات المأمورية
 */

import { NextResponse } from 'next/server';
import { getConnection } from '@/utils/db';

export async function GET(request) {
  try {

    const pool = await getConnection();
    
    // 1. فحص طلبات المأمورية في جدول MissionRequests
    const missionRequests = await pool.request().query(`
      SELECT 
        RequestID,
        EmployeeName,
        EmployeeID,
        MissionType,
        Destination,
        StartDate,
        EndDate,
        Status,
        SubmittedAt,
        ReviewedAt,
        ReviewedBy
      FROM MissionRequests 
      ORDER BY SubmittedAt DESC
    `);
    
    // 2. فحص إعدادات أنواع الطلبات
    const requestTypes = await pool.request().query(`
      SELECT TypeCode, TypeNameArabic, RequiresApproval 
      FROM RequestTypes 
      WHERE TypeNameArabic LIKE N'%مأمورية%' 
         OR TypeCode = 'mission'
      ORDER BY DisplayOrder
    `);
    
    // 3. فحص طلبات المأمورية في جدول PaperRequests (إن وجدت)
    const paperMissionRequests = await pool.request().query(`
      SELECT 
        ID,
        EmployeeName,
        LeaveType,
        Status,
        RequestDate,
        ApprovalDate,
        ApprovedBy
      FROM PaperRequests 
      WHERE LeaveType LIKE N'%مأمورية%' 
         OR LeaveType = 'mission'
      ORDER BY RequestDate DESC
    `);
    
    // 4. تحليل البيانات
    const analysis = {
      totalMissionRequests: missionRequests.recordset.length,
      pendingMissions: missionRequests.recordset.filter(r => r.Status === 'pending').length,
      approvedMissions: missionRequests.recordset.filter(r => r.Status === 'approved').length,
      rejectedMissions: missionRequests.recordset.filter(r => r.Status === 'rejected').length,
      
      paperMissionRequests: paperMissionRequests.recordset.length,
      paperPendingMissions: paperMissionRequests.recordset.filter(r => r.Status === 'قيد المراجعة').length,
      paperApprovedMissions: paperMissionRequests.recordset.filter(r => r.Status === 'معتمدة').length,
      
      missionTypeSettings: requestTypes.recordset
    };
    
    // 5. تحديد المشاكل
    const problems = [];
    
    // فحص إعدادات أنواع الطلبات
    requestTypes.recordset.forEach(type => {
      if (type.RequiresApproval) {
        problems.push(`نوع الطلب "${type.TypeNameArabic}" يتطلب موافقة (قد يسبب تأخير في المأموريات)`);
      }
    });
    
    // فحص الطلبات المعلقة
    if (analysis.pendingMissions > 0) {
      problems.push(`يوجد ${analysis.pendingMissions} طلب مأمورية معلق في جدول MissionRequests`);
    }
    
    if (analysis.paperPendingMissions > 0) {
      problems.push(`يوجد ${analysis.paperPendingMissions} طلب مأمورية معلق في جدول PaperRequests`);
    }
    
    return NextResponse.json({
      success: true,
      data: {
        missionRequests: missionRequests.recordset,
        paperMissionRequests: paperMissionRequests.recordset,
        requestTypes: requestTypes.recordset,
        analysis: analysis,
        problems: problems
      }
    });
    
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في فحص حالة المأمورية: ' + error.message
    }, { status: 500 });
  }
}

export async function POST(request) {
  try {
    const { action } = await request.json();

    const pool = await getConnection();
    
    if (action === 'auto-approve-missions') {
      // اعتماد جميع طلبات المأمورية المعلقة تلقائياً
      const updateResult = await pool.request().query(`
        UPDATE MissionRequests 
        SET Status = 'approved',
            ReviewedAt = GETDATE(),
            ReviewedBy = N'النظام - اعتماد تلقائي',
            ReviewComments = N'تم الاعتماد تلقائياً بواسطة النظام'
        WHERE Status = 'pending';
        
        SELECT @@ROWCOUNT as UpdatedCount;
      `);
      
      const updatedCount = updateResult.recordset[0].UpdatedCount;
      
      return NextResponse.json({
        success: true,
        message: `تم اعتماد ${updatedCount} طلب مأمورية تلقائياً`,
        data: { updatedCount }
      });
      
    } else if (action === 'fix-mission-settings') {
      // إصلاح إعدادات المأمورية لتكون معتمدة تلقائياً
      await pool.request().query(`
        UPDATE RequestTypes 
        SET RequiresApproval = 0 
        WHERE TypeNameArabic LIKE N'%مأمورية%' 
           OR TypeCode = 'mission';
           
        -- إضافة نوع المأمورية إذا لم يكن موجود
        IF NOT EXISTS (SELECT 1 FROM RequestTypes WHERE TypeCode = 'mission')
        BEGIN
          INSERT INTO RequestTypes (TypeCode, TypeNameArabic, TypeNameEnglish, RequiresApproval, AffectsBalance, DisplayOrder)
          VALUES ('mission', N'مأمورية', 'Mission', 0, 0, 8);
        END
      `);
      
      return NextResponse.json({
        success: true,
        message: 'تم إصلاح إعدادات المأمورية بنجاح'
      });
      
    } else {
      return NextResponse.json({
        success: false,
        error: 'إجراء غير معروف'
      }, { status: 400 });
    }
    
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في تنفيذ الإجراء: ' + error.message
    }, { status: 500 });
  }
}
