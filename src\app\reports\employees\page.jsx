'use client';

import React, { useState, useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { useLanguage } from '@/contexts/LanguageContext';
import MainLayout from '@/components/MainLayout';
import { 
  Users, 
  Download, 
  Filter, 
  Search, 
  Calendar, 
  FileText, 
  BarChart3, 
  Eye, 
  Printer,
  RefreshCw,
  Building,
  MapPin,
  GraduationCap,
  Shield,
  Home,
  UserCheck,
  UserX,
  TrendingUp
} from 'lucide-react';

export default function EmployeeReportsPage() {
  const { isDarkMode } = useTheme();
  const { isRTL, isArabic } = useLanguage();
  
  const [loading, setLoading] = useState(false);
  const [selectedReport, setSelectedReport] = useState('complete-list');
  const [filters, setFilters] = useState({
    department: '',
    governorate: '',
    status: 'all',
    dateFrom: '',
    dateTo: '',
    searchTerm: ''
  });
  const [reportData, setReportData] = useState([]);
  const [reportStats, setReportStats] = useState({});

  // أنواع تقارير الموظفين
  const employeeReports = [
    {
      id: 'complete-list',
      title: 'قائمة الموظفين الشاملة',
      description: 'تقرير شامل بجميع بيانات الموظفين',
      icon: Users,
      color: 'blue'
    },
    {
      id: 'by-department',
      title: 'تقرير حسب القسم',
      description: 'تقرير الموظفين مجمعين حسب الأقسام',
      icon: Building,
      color: 'green'
    },
    {
      id: 'by-governorate',
      title: 'تقرير حسب المحافظة',
      description: 'تقرير الموظفين مجمعين حسب المحافظات',
      icon: MapPin,
      color: 'purple'
    },
    {
      id: 'new-hires',
      title: 'الموظفين الجدد',
      description: 'تقرير الموظفين المعينين حديثاً',
      icon: UserCheck,
      color: 'emerald'
    },
    {
      id: 'departures',
      title: 'الموظفين المغادرين',
      description: 'تقرير الموظفين الذين تركوا العمل',
      icon: UserX,
      color: 'red'
    },
    {
      id: 'insurance',
      title: 'تقرير التأمينات',
      description: 'تقرير حالة التأمينات الاجتماعية والطبية',
      icon: Shield,
      color: 'indigo'
    },
    {
      id: 'housing',
      title: 'تقرير السكن',
      description: 'تقرير حالة السكن للموظفين',
      icon: Home,
      color: 'orange'
    },
    {
      id: 'qualifications',
      title: 'تقرير المؤهلات',
      description: 'تقرير المؤهلات العلمية للموظفين',
      icon: GraduationCap,
      color: 'teal'
    },
    {
      id: 'statistics',
      title: 'الإحصائيات العامة',
      description: 'إحصائيات شاملة عن الموظفين',
      icon: BarChart3,
      color: 'pink'
    }
  ];

  // تحميل بيانات التقرير
  useEffect(() => {
    loadReportData();
  }, [selectedReport, filters]);

  const loadReportData = async () => {
    setLoading(true);
    try {

      const response = await fetch('/api/employee-reports', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reportType: selectedReport,
          filters: filters
        })
      });

      const data = await response.json();

      if (data.success) {

        setReportData(data.data || []);
        setReportStats(data.stats || {});
      } else {

        setReportData([]);
        setReportStats({});
      }
    } catch (error) {

      setReportData([]);
      setReportStats({});
    } finally {
      setLoading(false);
    }
  };

  // تحديث قوائم الأقسام والمحافظات من البيانات الفعلية
  const [departments, setDepartments] = useState([]);
  const [governorates, setGovernorates] = useState([]);

  // تحميل قوائم الأقسام والمحافظات
  useEffect(() => {
    loadDepartmentsAndGovernorates();
  }, []);

  const loadDepartmentsAndGovernorates = async () => {
    try {

      const response = await fetch('/api/employee-lists');
      const data = await response.json();

      if (data.success) {

        setDepartments(data.data.departments || []);
        setGovernorates(data.data.governorates || []);
      } else {

        // قوائم افتراضية
        setDepartments(['الإدارة العامة', 'الموارد البشرية', 'المالية', 'التقنية', 'التسويق']);
        setGovernorates(['القاهرة', 'الجيزة', 'الإسكندرية', 'الدقهلية', 'الشرقية']);
      }
    } catch (error) {

      // قوائم افتراضية
      setDepartments(['الإدارة العامة', 'الموارد البشرية', 'المالية', 'التقنية', 'التسويق']);
      setGovernorates(['القاهرة', 'الجيزة', 'الإسكندرية', 'الدقهلية', 'الشرقية']);
    }
  };

  // تصدير التقرير
  const exportReport = (format) => {
    // سيتم تنفيذه لاحقاً
    alert(`جاري تصدير التقرير بصيغة ${format}...`);
  };

  // طباعة التقرير
  const printReport = () => {
    window.print();
  };

  // إعادة تعيين الفلاتر
  const resetFilters = () => {
    setFilters({
      department: '',
      governorate: '',
      status: 'all',
      dateFrom: '',
      dateTo: '',
      searchTerm: ''
    });
  };

  const selectedReportInfo = employeeReports.find(r => r.id === selectedReport);

  return (
    <MainLayout>
      <div className={`p-6 ${isDarkMode ? 'bg-[#0f172a] text-white' : 'bg-gray-50 text-gray-900'}`}>
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm p-6 mb-6`}>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <Users className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-2xl font-bold">تقارير الموظفين</h1>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  تقارير شاملة ومفصلة عن بيانات الموظفين
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <button
                onClick={() => exportReport('Excel')}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <Download className="h-4 w-4" />
                Excel
              </button>
              
              <button
                onClick={() => exportReport('PDF')}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <FileText className="h-4 w-4" />
                PDF
              </button>
              
              <button
                onClick={printReport}
                className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-colors ${
                  isDarkMode 
                    ? 'bg-gray-700 hover:bg-gray-600 text-white' 
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                }`}
              >
                <Printer className="h-4 w-4" />
                طباعة
              </button>
            </div>
          </div>
        </div>

        {/* اختيار نوع التقرير */}
        <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm p-6 mb-6`}>
          <h3 className="text-lg font-semibold mb-4">اختر نوع التقرير</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {employeeReports.map((report) => {
              const IconComponent = report.icon;
              return (
                <button
                  key={report.id}
                  onClick={() => setSelectedReport(report.id)}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    selectedReport === report.id
                      ? `border-${report.color}-500 bg-${report.color}-50 dark:bg-${report.color}-900/20`
                      : `border-gray-200 dark:border-gray-700 hover:border-${report.color}-300`
                  }`}
                >
                  <div className="flex flex-col items-center text-center gap-2">
                    <IconComponent className={`h-8 w-8 text-${report.color}-600`} />
                    <div>
                      <h4 className="font-medium text-sm">{report.title}</h4>
                      <p className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {report.description}
                      </p>
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        </div>

        {/* الفلاتر */}
        <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm p-6 mb-6`}>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">فلاتر التقرير</h3>
            <button
              onClick={resetFilters}
              className={`px-3 py-1 rounded text-sm transition-colors ${
                isDarkMode 
                  ? 'bg-gray-700 hover:bg-gray-600 text-white' 
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
              }`}
            >
              إعادة تعيين
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {/* البحث */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                البحث
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  value={filters.searchTerm}
                  onChange={(e) => setFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
                  placeholder="ابحث بالاسم أو الكود..."
                  className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                    isDarkMode 
                      ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400' 
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  }`}
                />
              </div>
            </div>

            {/* القسم */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                القسم
              </label>
              <select
                value={filters.department}
                onChange={(e) => setFilters(prev => ({ ...prev, department: e.target.value }))}
                className={`w-full p-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                  isDarkMode 
                    ? 'bg-gray-800 border-gray-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="">جميع الأقسام</option>
                {departments.map(dept => (
                  <option key={dept} value={dept}>{dept}</option>
                ))}
              </select>
            </div>

            {/* المحافظة */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                المحافظة
              </label>
              <select
                value={filters.governorate}
                onChange={(e) => setFilters(prev => ({ ...prev, governorate: e.target.value }))}
                className={`w-full p-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                  isDarkMode 
                    ? 'bg-gray-800 border-gray-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="">جميع المحافظات</option>
                {governorates.map(gov => (
                  <option key={gov} value={gov}>{gov}</option>
                ))}
              </select>
            </div>

            {/* الحالة */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                الحالة
              </label>
              <select
                value={filters.status}
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                className={`w-full p-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                  isDarkMode 
                    ? 'bg-gray-800 border-gray-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="all">جميع الحالات</option>
                <option value="active">نشط</option>
                <option value="inactive">غير نشط</option>
              </select>
            </div>

            {/* من تاريخ */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                من تاريخ
              </label>
              <input
                type="date"
                value={filters.dateFrom}
                onChange={(e) => setFilters(prev => ({ ...prev, dateFrom: e.target.value }))}
                className={`w-full p-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                  isDarkMode 
                    ? 'bg-gray-800 border-gray-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>

            {/* إلى تاريخ */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                إلى تاريخ
              </label>
              <input
                type="date"
                value={filters.dateTo}
                onChange={(e) => setFilters(prev => ({ ...prev, dateTo: e.target.value }))}
                className={`w-full p-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                  isDarkMode 
                    ? 'bg-gray-800 border-gray-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>
          </div>
        </div>

        {/* إحصائيات التقرير */}
        {selectedReport === 'statistics' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm p-6`}>
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Users className="h-8 w-8 text-blue-600" />
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} truncate`}>
                      إجمالي الموظفين
                    </dt>
                    <dd className={`text-lg font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {reportStats.totalEmployees || 0}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm p-6`}>
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <UserCheck className="h-8 w-8 text-green-600" />
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} truncate`}>
                      الموظفين النشطين
                    </dt>
                    <dd className={`text-lg font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {reportStats.activeEmployees || 0}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm p-6`}>
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Shield className="h-8 w-8 text-purple-600" />
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} truncate`}>
                      المؤمنين اجتماعياً
                    </dt>
                    <dd className={`text-lg font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {reportStats.socialInsured || 0}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm p-6`}>
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Home className="h-8 w-8 text-orange-600" />
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} truncate`}>
                      سكن الشركة
                    </dt>
                    <dd className={`text-lg font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {reportStats.companyHousing || 0}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* عرض التقرير */}
        <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm overflow-hidden`}>
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  {selectedReportInfo && <selectedReportInfo.icon className="h-5 w-5" />}
                  {selectedReportInfo?.title}
                </h3>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  عدد السجلات: {reportData.length}
                </p>
              </div>

              <button
                onClick={loadReportData}
                disabled={loading}
                className={`px-3 py-1 rounded flex items-center gap-2 transition-colors ${
                  isDarkMode
                    ? 'bg-gray-700 hover:bg-gray-600 text-white'
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                } disabled:opacity-50`}
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                تحديث
              </button>
            </div>
          </div>

          <div className="overflow-x-auto">
            {loading ? (
              <div className="p-8 text-center">
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="mr-2">جاري تحميل التقرير...</span>
                </div>
              </div>
            ) : reportData.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                لا توجد بيانات للعرض
              </div>
            ) : (
              <table className="w-full">
                <thead className={`${isDarkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
                  <tr>
                    <th className="px-4 py-3 text-right text-xs font-medium uppercase tracking-wider">
                      كود الموظف
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium uppercase tracking-wider">
                      الاسم الكامل
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium uppercase tracking-wider">
                      القسم
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium uppercase tracking-wider">
                      المحافظة
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium uppercase tracking-wider">
                      المسمى الوظيفي
                    </th>
                    <th className="px-4 py-3 text-center text-xs font-medium uppercase tracking-wider">
                      تاريخ التعيين
                    </th>
                    <th className="px-4 py-3 text-center text-xs font-medium uppercase tracking-wider">
                      الحالة
                    </th>
                    {selectedReport === 'insurance' && (
                      <>
                        <th className="px-4 py-3 text-center text-xs font-medium uppercase tracking-wider">
                          التأمين الاجتماعي
                        </th>
                        <th className="px-4 py-3 text-center text-xs font-medium uppercase tracking-wider">
                          التأمين الطبي
                        </th>
                      </>
                    )}
                    {selectedReport === 'housing' && (
                      <th className="px-4 py-3 text-center text-xs font-medium uppercase tracking-wider">
                        نوع السكن
                      </th>
                    )}
                    {selectedReport === 'qualifications' && (
                      <th className="px-4 py-3 text-center text-xs font-medium uppercase tracking-wider">
                        المؤهل العلمي
                      </th>
                    )}
                  </tr>
                </thead>
                <tbody className={`${isDarkMode ? 'bg-gray-900' : 'bg-white'} divide-y divide-gray-200 dark:divide-gray-700`}>
                  {reportData.map((employee) => (
                    <tr key={employee.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td className="px-4 py-3 text-sm font-medium">
                        {employee.employeeCode}
                      </td>
                      <td className="px-4 py-3 text-sm">
                        {employee.fullName}
                      </td>
                      <td className="px-4 py-3 text-sm">
                        {employee.department}
                      </td>
                      <td className="px-4 py-3 text-sm">
                        {employee.governorate}
                      </td>
                      <td className="px-4 py-3 text-sm">
                        {employee.jobTitle}
                      </td>
                      <td className="px-4 py-3 text-center text-sm">
                        {employee.hireDate ? new Date(employee.hireDate).toLocaleDateString('ar-EG') : '-'}
                      </td>
                      <td className="px-4 py-3 text-center">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          employee.status === 'نشط'
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                        }`}>
                          {employee.status}
                        </span>
                      </td>
                      {selectedReport === 'insurance' && (
                        <>
                          <td className="px-4 py-3 text-center">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              employee.socialInsurance === 'مؤمن'
                                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                            }`}>
                              {employee.socialInsurance}
                            </span>
                          </td>
                          <td className="px-4 py-3 text-center">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              employee.medicalInsurance === 'مؤمن'
                                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                            }`}>
                              {employee.medicalInsurance}
                            </span>
                          </td>
                        </>
                      )}
                      {selectedReport === 'housing' && (
                        <td className="px-4 py-3 text-center">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            employee.housing === 'سكن شركة'
                              ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                              : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
                          }`}>
                            {employee.housing}
                          </span>
                        </td>
                      )}
                      {selectedReport === 'qualifications' && (
                        <td className="px-4 py-3 text-center text-sm">
                          {employee.qualification}
                        </td>
                      )}
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
