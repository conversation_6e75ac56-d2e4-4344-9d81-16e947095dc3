-- ===================================
-- جدول إدارة الشقق
-- ===================================

-- 1. جدول الشقق الأساسي
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Apartments' AND xtype='U')
BEGIN
    CREATE TABLE Apartments (
        ID INT IDENTITY(1,1) PRIMARY KEY,
        ApartmentCode NVARCHAR(20) NOT NULL UNIQUE,
        LandlordName NVARCHAR(100) NOT NULL,
        Address NVARCHAR(500) NOT NULL,
        StartDate DATE NOT NULL,
        EndDate DATE,
        RentAmount DECIMAL(10,2) NOT NULL,
        InsuranceAmount DECIMAL(10,2) DEFAULT 0,
        CommissionAmount DECIMAL(10,2) DEFAULT 0,
        BacklogAmount DECIMAL(10,2) DEFAULT 0,
        Notes NVARCHAR(MAX),
        IsActive BIT DEFAULT 1,
        CreatedAt DATETIME DEFAULT GETDATE(),
        UpdatedAt DATETIME DEFAULT GETDATE()
    )
    
    -- إنشاء الفهارس
    CREATE INDEX IX_Apartments_Code ON Apartments(ApartmentCode)
    CREATE INDEX IX_Apartments_Active ON Apartments(IsActive)
    CREATE INDEX IX_Apartments_Dates ON Apartments(StartDate, EndDate)
END

-- 2. جدول المستفيدين من الشقق (ربط مع الموظفين)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ApartmentBeneficiaries' AND xtype='U')
BEGIN
    CREATE TABLE ApartmentBeneficiaries (
        ID INT IDENTITY(1,1) PRIMARY KEY,
        ApartmentID INT NOT NULL,
        EmployeeCode NVARCHAR(20) NOT NULL,
        EmployeeName NVARCHAR(100) NOT NULL,
        JobTitle NVARCHAR(100) NOT NULL,
        Department NVARCHAR(100),
        StartDate DATE NOT NULL,
        EndDate DATE,
        IsActive BIT DEFAULT 1,
        CreatedAt DATETIME DEFAULT GETDATE(),
        UpdatedAt DATETIME DEFAULT GETDATE(),
        
        -- المفاتيح الخارجية
        FOREIGN KEY (ApartmentID) REFERENCES Apartments(ID) ON DELETE CASCADE
    )
    
    -- إنشاء الفهارس
    CREATE INDEX IX_ApartmentBeneficiaries_ApartmentID ON ApartmentBeneficiaries(ApartmentID)
    CREATE INDEX IX_ApartmentBeneficiaries_EmployeeCode ON ApartmentBeneficiaries(EmployeeCode)
    CREATE INDEX IX_ApartmentBeneficiaries_Active ON ApartmentBeneficiaries(IsActive)
END

-- 3. إنشاء view شامل لعرض الشقق مع المستفيدين
IF NOT EXISTS (SELECT * FROM sys.views WHERE name = 'VW_ApartmentsWithBeneficiaries')
BEGIN
    EXEC('
    CREATE VIEW VW_ApartmentsWithBeneficiaries
    AS
    SELECT 
        a.ID as ApartmentID,
        a.ApartmentCode,
        a.LandlordName,
        a.Address,
        a.StartDate as ApartmentStartDate,
        a.EndDate as ApartmentEndDate,
        a.RentAmount,
        a.InsuranceAmount,
        a.BacklogAmount,
        a.Notes,
        a.IsActive as ApartmentActive,
        
        -- بيانات المستفيدين
        ab.ID as BeneficiaryID,
        ab.EmployeeCode,
        ab.EmployeeName,
        ab.JobTitle,
        ab.Department,
        ab.StartDate as BeneficiaryStartDate,
        ab.EndDate as BeneficiaryEndDate,
        ab.IsActive as BeneficiaryActive,
        
        -- حسابات إضافية
        DATEDIFF(MONTH, a.StartDate, ISNULL(a.EndDate, GETDATE())) as TotalMonths,
        (a.RentAmount * DATEDIFF(MONTH, a.StartDate, ISNULL(a.EndDate, GETDATE()))) as TotalRentPaid,
        
        -- عدد المستفيدين النشطين
        (SELECT COUNT(*) FROM ApartmentBeneficiaries ab2 
         WHERE ab2.ApartmentID = a.ID AND ab2.IsActive = 1) as ActiveBeneficiariesCount
         
    FROM Apartments a
    LEFT JOIN ApartmentBeneficiaries ab ON a.ID = ab.ApartmentID
    WHERE a.IsActive = 1
    ')
END

-- 4. إنشاء stored procedure لإضافة شقة جديدة مع المستفيدين
IF NOT EXISTS (SELECT * FROM sys.procedures WHERE name = 'SP_CreateApartmentWithBeneficiaries')
BEGIN
    EXEC('
    CREATE PROCEDURE SP_CreateApartmentWithBeneficiaries
        @ApartmentCode NVARCHAR(20),
        @LandlordName NVARCHAR(100),
        @Address NVARCHAR(500),
        @StartDate DATE,
        @EndDate DATE = NULL,
        @RentAmount DECIMAL(10,2),
        @InsuranceAmount DECIMAL(10,2) = 0,
        @BacklogAmount DECIMAL(10,2) = 0,
        @Notes NVARCHAR(MAX) = NULL,
        @BeneficiariesJson NVARCHAR(MAX) = NULL
    AS
    BEGIN
        SET NOCOUNT ON
        
        DECLARE @ApartmentID INT
        
        -- التحقق من عدم تكرار كود الشقة
        IF EXISTS (SELECT 1 FROM Apartments WHERE ApartmentCode = @ApartmentCode)
        BEGIN
            RAISERROR(N''كود الشقة موجود مسبقاً'', 16, 1)
            RETURN
        END
        
        BEGIN TRANSACTION
        
        TRY
            -- إدراج الشقة
            INSERT INTO Apartments (
                ApartmentCode, LandlordName, Address, StartDate, EndDate,
                RentAmount, InsuranceAmount, BacklogAmount, Notes
            )
            VALUES (
                @ApartmentCode, @LandlordName, @Address, @StartDate, @EndDate,
                @RentAmount, @InsuranceAmount, @BacklogAmount, @Notes
            )
            
            SET @ApartmentID = SCOPE_IDENTITY()
            
            -- إدراج المستفيدين إذا تم تمرير البيانات
            IF @BeneficiariesJson IS NOT NULL AND @BeneficiariesJson != ''''
            BEGIN
                -- هنا يمكن إضافة منطق تحليل JSON وإدراج المستفيدين
                -- سيتم تنفيذه عبر API
                PRINT N''تم إنشاء الشقة بنجاح''
            END
            
            COMMIT TRANSACTION
            
            SELECT @ApartmentID as NewApartmentID
            
        END TRY
        BEGIN CATCH
            ROLLBACK TRANSACTION
            THROW
        END CATCH
    END
    ')
END

-- 5. إنشاء stored procedure لإضافة مستفيد جديد للشقة
IF NOT EXISTS (SELECT * FROM sys.procedures WHERE name = 'SP_AddBeneficiaryToApartment')
BEGIN
    EXEC('
    CREATE PROCEDURE SP_AddBeneficiaryToApartment
        @ApartmentID INT,
        @EmployeeCode NVARCHAR(20),
        @StartDate DATE,
        @EndDate DATE = NULL
    AS
    BEGIN
        SET NOCOUNT ON
        
        DECLARE @EmployeeName NVARCHAR(100)
        DECLARE @JobTitle NVARCHAR(100)
        DECLARE @Department NVARCHAR(100)
        
        -- جلب بيانات الموظف
        SELECT 
            @EmployeeName = EmployeeName,
            @JobTitle = JobTitle,
            @Department = Department
        FROM Employees
        WHERE EmployeeCode = @EmployeeCode
        
        IF @EmployeeName IS NULL
        BEGIN
            RAISERROR(N''كود الموظف غير موجود'', 16, 1)
            RETURN
        END
        
        -- التحقق من عدم وجود الموظف في نفس الشقة
        IF EXISTS (
            SELECT 1 FROM ApartmentBeneficiaries 
            WHERE ApartmentID = @ApartmentID 
                AND EmployeeCode = @EmployeeCode 
                AND IsActive = 1
        )
        BEGIN
            RAISERROR(N''الموظف مسجل بالفعل في هذه الشقة'', 16, 1)
            RETURN
        END
        
        -- إدراج المستفيد
        INSERT INTO ApartmentBeneficiaries (
            ApartmentID, EmployeeCode, EmployeeName, JobTitle, Department,
            StartDate, EndDate
        )
        VALUES (
            @ApartmentID, @EmployeeCode, @EmployeeName, @JobTitle, @Department,
            @StartDate, @EndDate
        )
        
        SELECT SCOPE_IDENTITY() as NewBeneficiaryID
    END
    ')
END

-- 6. إنشاء stored procedure للحصول على إحصائيات الشقق
IF NOT EXISTS (SELECT * FROM sys.procedures WHERE name = 'SP_GetApartmentsStatistics')
BEGIN
    EXEC('
    CREATE PROCEDURE SP_GetApartmentsStatistics
    AS
    BEGIN
        SET NOCOUNT ON
        
        SELECT 
            -- إحصائيات عامة
            (SELECT COUNT(*) FROM Apartments WHERE IsActive = 1) as TotalActiveApartments,
            (SELECT COUNT(*) FROM Apartments WHERE IsActive = 0) as TotalInactiveApartments,
            (SELECT COUNT(*) FROM ApartmentBeneficiaries WHERE IsActive = 1) as TotalActiveBeneficiaries,
            
            -- إحصائيات مالية
            (SELECT SUM(RentAmount) FROM Apartments WHERE IsActive = 1) as TotalMonthlyRent,
            (SELECT SUM(InsuranceAmount) FROM Apartments WHERE IsActive = 1) as TotalInsurance,
            (SELECT SUM(BacklogAmount) FROM Apartments WHERE IsActive = 1) as TotalBacklog,
            
            -- الشقق التي تنتهي قريباً (خلال 30 يوم)
            (SELECT COUNT(*) FROM Apartments 
             WHERE IsActive = 1 
                AND EndDate IS NOT NULL 
                AND EndDate <= DATEADD(DAY, 30, GETDATE())) as ApartmentsExpiringSoon,
                
            -- الشقق بدون مستفيدين
            (SELECT COUNT(*) FROM Apartments a
             WHERE a.IsActive = 1 
                AND NOT EXISTS (
                    SELECT 1 FROM ApartmentBeneficiaries ab 
                    WHERE ab.ApartmentID = a.ID AND ab.IsActive = 1
                )) as ApartmentsWithoutBeneficiaries
    END
    ')
END

-- 7. إدراج بيانات تجريبية (اختيارية)
/*
INSERT INTO Apartments (ApartmentCode, LandlordName, Address, StartDate, RentAmount, InsuranceAmount, CommissionAmount, Notes)
VALUES
    (N'APT-001', N'أحمد محمد علي', N'شارع النيل، المعادي، القاهرة', '2024-01-01', 5000.00, 500.00, 250.00, N'شقة مفروشة 3 غرف'),
    (N'APT-002', N'فاطمة حسن', N'شارع الهرم، الجيزة', '2024-02-01', 4500.00, 450.00, 225.00, N'شقة مفروشة 2 غرف'),
    (N'APT-003', N'محمود السيد', N'مدينة نصر، القاهرة', '2024-03-01', 6000.00, 600.00, 300.00, N'شقة مفروشة 4 غرف')
*/

PRINT 'تم إنشاء جداول إدارة الشقق بنجاح'
