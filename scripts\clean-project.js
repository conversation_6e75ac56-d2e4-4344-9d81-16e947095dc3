#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const unlink = promisify(fs.unlink);
const rmdir = promisify(fs.rmdir);

// قائمة الملفات والمجلدات المؤقتة للحذف
const TEMP_PATTERNS = [
  // ملفات الاختبار والتصحيح
  /^test-.*\.(js|html|json)$/,
  /^debug-.*\.(js|html|json)$/,
  /^fix-.*\.(js|html|json)$/,
  /^check-.*\.(js|html|json)$/,
  /^verify-.*\.(js|html|json)$/,
  /^quick-.*\.(js|html|json)$/,
  /^simple-.*\.(js|html|json)$/,
  /^final-.*\.(js|html|json)$/,
  /^emergency-.*\.(js|html|json)$/,
  /^critical-.*\.(js|html|json)$/,
  /^auto-.*\.(js|html|json)$/,
  /^manual-.*\.(js|html|json)$/,
  /^run-.*\.(js|html|json)$/,
  /^sync-.*\.(js|html|json)$/,
  /^update-.*\.(js|html|json)$/,
  /^restore-.*\.(js|html|json)$/,
  /^reset-.*\.(js|html|json)$/,
  /^complete-.*\.(js|html|json)$/,
  /^unified-.*\.(js|html|json)$/,
  /^standalone-.*\.(js|html|json)$/,
  /^server-.*\.(js|html|json)$/,
  /^layout-.*\.(js|html|json)$/,
  
  // ملفات التوثيق المؤقتة
  /.*_GUIDE\.md$/,
  /.*_FIX\.md$/,
  /.*_SUMMARY\.md$/,
  /.*_DOCUMENTATION\.md$/,
  /.*_REPORT\.md$/,
  /.*_ANALYSIS\.md$/,
  /.*_README\.md$/,
  /.*_UPDATE\.md$/,
  /.*_FIXES\.md$/,
  
  // ملفات SQL المؤقتة
  /^add_.*\.sql$/,
  /^update_.*\.sql$/,
  /^fix_.*\.sql$/,
  /^create_.*\.sql$/,
  /^remove_.*\.sql$/,
  
  // ملفات أخرى
  /^.*\.bak$/,
  /^.*\.tmp$/,
  /^.*\.temp$/,
  /^.*\.old$/,
  /^.*\.backup$/,
  /^database-analysis\.json$/,
  /^webpack\.config\.js$/,
];

// مجلدات للتجاهل
const IGNORE_DIRS = [
  'node_modules',
  '.next',
  '.git',
  'build',
  'dist',
  'out',
  'coverage',
  '.vercel',
  'public/uploads',
  'archiv',
  'backups',
  'exports',
  'Augment-free',
  'oje',
  'ojesta',
  'templates'
];

// دالة للتحقق من أن الملف مؤقت
function isTempFile(filename) {
  return TEMP_PATTERNS.some(pattern => pattern.test(filename));
}

// دالة لحذف ملف
async function deleteFile(filePath) {
  try {
    await unlink(filePath);

    return true;
  } catch (error) {

    return false;
  }
}

// دالة للبحث عن الملفات المؤقتة وحذفها
async function cleanTempFiles(dir = '.') {
  const deletedFiles = [];
  
  try {
    const items = await readdir(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stats = await stat(fullPath);
      
      if (stats.isDirectory()) {
        // تجاهل المجلدات المحددة
        if (!IGNORE_DIRS.includes(item) && !item.startsWith('.')) {
          const subDeleted = await cleanTempFiles(fullPath);
          deletedFiles.push(...subDeleted);
        }
      } else if (stats.isFile()) {
        // فحص الملفات المؤقتة
        if (isTempFile(item)) {
          const success = await deleteFile(fullPath);
          if (success) {
            deletedFiles.push(fullPath);
          }
        }
      }
    }
  } catch (error) {

  }
  
  return deletedFiles;
}

// دالة لتنظيف console.log من ملفات JavaScript
async function cleanConsoleLogsFromFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // إزالة console.log statements
    const cleanedContent = content
      .replace(/^\s*console\.(log|warn|error|info|debug)\([^)]*\);\s*$/gm, '')
      .replace(/^\s*console\.(log|warn|error|info|debug)\([^)]*\);\s*\n/gm, '')
      .replace(/console\.(log|warn|error|info|debug)\([^)]*\);\s*/g, '')
      // إزالة التعليقات المؤقتة
      .replace(/^\s*\/\/ TODO:.*$/gm, '')
      .replace(/^\s*\/\/ FIXME:.*$/gm, '')
      .replace(/^\s*\/\/ DEBUG:.*$/gm, '')
      .replace(/^\s*\/\/ TEMP:.*$/gm, '')
      // إزالة الأسطر الفارغة المتتالية
      .replace(/\n\s*\n\s*\n/g, '\n\n');
    
    if (content !== cleanedContent) {
      fs.writeFileSync(filePath, cleanedContent);

      return true;
    }
    
    return false;
  } catch (error) {

    return false;
  }
}

// دالة لتنظيف ملفات JavaScript
async function cleanJavaScriptFiles(dir = '.') {
  const cleanedFiles = [];
  
  try {
    const items = await readdir(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stats = await stat(fullPath);
      
      if (stats.isDirectory()) {
        if (!IGNORE_DIRS.includes(item) && !item.startsWith('.')) {
          const subCleaned = await cleanJavaScriptFiles(fullPath);
          cleanedFiles.push(...subCleaned);
        }
      } else if (stats.isFile()) {
        if (item.endsWith('.js') || item.endsWith('.jsx') || item.endsWith('.ts') || item.endsWith('.tsx')) {
          const success = await cleanConsoleLogsFromFile(fullPath);
          if (success) {
            cleanedFiles.push(fullPath);
          }
        }
      }
    }
  } catch (error) {

  }
  
  return cleanedFiles;
}

// الدالة الرئيسية
async function main() {

  // 1. حذف الملفات المؤقتة

  const deletedFiles = await cleanTempFiles();

  // 2. تنظيف ملفات JavaScript

  const cleanedFiles = await cleanJavaScriptFiles();

}

// تشغيل السكريبت
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { cleanTempFiles, cleanJavaScriptFiles };
