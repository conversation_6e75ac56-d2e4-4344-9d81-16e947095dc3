'use client';
import React from 'react';
import BackButton from '@/components/back-button';

function MainComponent() {
  const [selectedLang, setSelectedLang] = React.useState('ar');
  const [selectedReport, setSelectedReport] = React.useState('');
  const [dateRange, setDateRange] = React.useState({ start: '', end: '' });
  const [selectedDepartment, setSelectedDepartment] = React.useState('all');
  const [selectedStatus, setSelectedStatus] = React.useState('');
  const [reportData, setReportData] = React.useState([]);
  const [reportStats, setReportStats] = React.useState({});
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState('');

  const dir = selectedLang === 'ar' ? 'rtl' : 'ltr';

  const loadReportData = async () => {
    setLoading(true);
    setError('');
    try {
      const response = await fetch('/api/report-generator', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'generate',
          reportType: selectedReport,
          dateRange: {
            start: dateRange.start,
            end: dateRange.end,
          },
          department: selectedDepartment,
          status: selectedStatus,
        }),
      });

      if (!response.ok) {
        throw new Error(
          selectedLang === 'ar'
            ? 'حدث خطأ أثناء تحميل التقرير'
            : 'Error loading report'
        );
      }

      const result = await response.json();
      if (!result.success) {
        throw new Error(
          result.error ||
            (selectedLang === 'ar'
              ? 'فشل في إنشاء التقرير'
              : 'Failed to generate report')
        );
      }

      setReportData(result.report?.details || []);
      setReportStats({
        totalEmployees: result.report?.summary?.totalEmployees || 0,
        presentPercentage: result.report?.summary?.presentPercentage || 0,
        absentPercentage: result.report?.summary?.absentPercentage || 0,
        leavePercentage: result.report?.summary?.leavePercentage || 0,
      });

      if (result.report?.details?.length > 0) {
        const stats = result.report.summary || {
          totalEmployees: 0,
          presentPercentage: 0,
          absentPercentage: 0,
          leavePercentage: 0,
        };
        initCharts(stats);
      }
    } catch (err) {

      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      dir={dir}
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          {selectedLang === 'ar' ? 'تقارير الموظفين' : 'Employee Reports'}
        </h1>
        <button
          onClick={() => setSelectedLang(selectedLang === 'ar' ? 'en' : 'ar')}
          className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
        >
          {selectedLang === 'ar' ? 'English' : 'العربية'}
        </button>
      </div>

      <div className="mb-6">
        <BackButton lang={selectedLang} />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <input
          type="text"
          value={selectedReport}
          onChange={(e) => setSelectedReport(e.target.value)}
          placeholder={selectedLang === 'ar' ? 'نوع التقرير' : 'Report Type'}
          className="p-2 border border-gray-300 rounded-md w-full"
          name="reportType"
        />
        <input
          type="date"
          value={dateRange.start}
          onChange={(e) =>
            setDateRange({ ...dateRange, start: e.target.value })
          }
          className="p-2 border border-gray-300 rounded-md w-full"
          name="startDate"
        />
        <input
          type="date"
          value={dateRange.end}
          onChange={(e) => setDateRange({ ...dateRange, end: e.target.value })}
          className="p-2 border border-gray-300 rounded-md w-full"
          name="endDate"
        />
        <input
          type="text"
          value={selectedDepartment}
          onChange={(e) => setSelectedDepartment(e.target.value)}
          placeholder={selectedLang === 'ar' ? 'القسم' : 'Department'}
          className="p-2 border border-gray-300 rounded-md w-full"
          name="department"
        />
        <input
          type="text"
          value={selectedStatus}
          onChange={(e) => setSelectedStatus(e.target.value)}
          placeholder={selectedLang === 'ar' ? 'الحالة' : 'Status'}
          className="p-2 border border-gray-300 rounded-md w-full"
          name="status"
        />
      </div>

      <button
        onClick={loadReportData}
        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
      >
        {selectedLang === 'ar' ? 'تحميل التقرير' : 'Load Report'}
      </button>

      {loading && (
        <div className="mt-4 text-center text-gray-600">
          {selectedLang === 'ar' ? 'جارٍ التحميل...' : 'Loading...'}
        </div>
      )}

      {error && <div className="mt-4 text-center text-red-600">{error}</div>}

      <div className="mt-8">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          {selectedLang === 'ar' ? 'إحصائيات التقرير' : 'Report Statistics'}
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {selectedLang === 'ar' ? 'إجمالي الموظفين' : 'Total Employees'}
            </h3>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              {reportStats.totalEmployees}
            </p>
          </div>
          <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {selectedLang === 'ar' ? 'نسبة الحضور' : 'Present Percentage'}
            </h3>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              {reportStats.presentPercentage}%
            </p>
          </div>
          <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {selectedLang === 'ar' ? 'نسبة الغياب' : 'Absent Percentage'}
            </h3>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              {reportStats.absentPercentage}%
            </p>
          </div>
          <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {selectedLang === 'ar' ? 'نسبة الإجازات' : 'Leave Percentage'}
            </h3>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              {reportStats.leavePercentage}%
            </p>
          </div>
        </div>
      </div>

      <div className="mt-8">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
          {selectedLang === 'ar' ? 'تفاصيل التقرير' : 'Report Details'}
        </h2>
        <div className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-100 dark:bg-gray-700">
              <tr>
                <th className="px-4 py-2 text-right">
                  {selectedLang === 'ar' ? 'رقم الموظف' : 'Employee ID'}
                </th>
                <th className="px-4 py-2 text-right">
                  {selectedLang === 'ar' ? 'الاسم' : 'Name'}
                </th>
                <th className="px-4 py-2 text-right">
                  {selectedLang === 'ar' ? 'القسم' : 'Department'}
                </th>
                <th className="px-4 py-2 text-right">
                  {selectedLang === 'ar' ? 'الحالة' : 'Status'}
                </th>
                <th className="px-4 py-2 text-right">
                  {selectedLang === 'ar' ? 'التاريخ' : 'Date'}
                </th>
              </tr>
            </thead>
            <tbody>
              {reportData.map((record, index) => (
                <tr key={index} className="border-b">
                  <td className="px-4 py-2">{record.employee_id}</td>
                  <td className="px-4 py-2">{record.employee_name}</td>
                  <td className="px-4 py-2">{record.department}</td>
                  <td className="px-4 py-2">{record.status}</td>
                  <td className="px-4 py-2">{record.date}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default MainComponent;
