'use client';

import MainLayout from '@/components/MainLayout';
import { useTheme } from '@/contexts/ThemeContext';
import { useEffect, useState } from 'react';

// استيراد الأيقونات - مؤقتاً نستخدم نص بدلاً من الأيقونات
const Icons = {
  FiUpload: () => '📤',
  FiSave: () => '💾',
  FiPrinter: () => '🖨️',
  FiDownload: () => '📥',
  FiEdit2: () => '✏️',
  FiCheck: () => '✅',
  FiX: () => '❌',
  FiArrowRight: () => '➡️',
  FiCalculator: () => '🧮'
};

const ATTENDANCE_OPTIONS = [
  { value: 'حضور', label: 'حضور', requiresTime: true },
  { value: 'غياب', label: 'غياب', requiresTime: false },
  { value: 'إجازة إعتيادية', label: 'إجازة إعتيادية', requiresTime: false },
  { value: 'إجازة عارضة', label: 'إجازة عارضة', requiresTime: false },
  { value: 'إجازة مرضية', label: 'إجازة مرضية', requiresTime: false },
  { value: 'إجازة بدون أجر', label: 'إجازة بدون أجر', requiresTime: false },
  { value: 'إجازة بدل', label: 'إجازة بدل', requiresTime: false },
  { value: 'إجازة سهر', label: 'إجازة سهر', requiresTime: false },
  { value: 'إجازة وفاة', label: 'إجازة وفاة', requiresTime: false },
  { value: 'إجازة زواج', label: 'إجازة زواج', requiresTime: false },
  { value: 'إجازة حج', label: 'إجازة حج', requiresTime: false },
  { value: 'إجازة امتحانات', label: 'إجازة امتحانات', requiresTime: false },
  { value: 'مأمورية', label: 'مأمورية', requiresTime: false },
  { value: 'وردية ليلية', label: 'وردية ليلية', requiresTime: true }
];

// دالة لجلب اسم اليوم بالعربية
const getArabicDayName = (dateStr) => {
  const days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
  const d = new Date(dateStr);
  return days[d.getDay()];
};

const formatDate = (dateStr) => {
  const d = new Date(dateStr);
  const day = String(d.getDate()).padStart(2, '0');
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const year = d.getFullYear();
  return `${day}/${month}/${year}`;
};

// دالة لتحويل dd/mm/yyyy إلى yyyy-mm-dd
const parseDateInput = (str) => {
  const match = str.match(/^(\d{2})\/(\d{2})\/(\d{4})$/);
  if (!match) return null;
  const [, day, month, year] = match;
  return `${year}-${month}-${day}`;
};

// أضف دالة ترتيب مخصصة:
const getManagerRank = (jobTitle = '') => {
  if (jobTitle.includes('مدير منطقة')) return 1;
  if (jobTitle.includes('مدير المكتب الفني')) return 2;
  if (jobTitle.includes('مدير مشروع')) return 3;
  if (jobTitle.startsWith('مدير')) return 4;
  return 5;
};

export default function DailyAttendance() {
  const { isDarkMode } = useTheme();
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [employees, setEmployees] = useState([]);
  const [attendanceData, setAttendanceData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [editRow, setEditRow] = useState(null);
  const [editAll, setEditAll] = useState(false);
  const [dateInput, setDateInput] = useState(formatDate(new Date().toISOString().split('T')[0]));
  const [bulkAttendance, setBulkAttendance] = useState('');
  const [showSavedAttendance, setShowSavedAttendance] = useState(false);
  const [approvedRequests, setApprovedRequests] = useState([]);
  const [resignations, setResignations] = useState([]);
  const [transfers, setTransfers] = useState([]);

  // دالة لجلب الطلبات المعتمدة للتاريخ المحدد
  const loadApprovedRequests = async (date) => {
    try {
      const response = await fetch(`/api/attendance-requests?date=${date}`);
      if (response.ok) {
        const result = await response.json();
        setApprovedRequests(result.data || []);

        // تسجيل تفصيلي للطلبات المعتمدة
        result.data?.forEach(req => {
          console.log(`📋 طلب معتمد: ${req.employeeName} (${req.employeeCode}) - ${req.attendanceType} - ${req.notes}`);
        });
      } else {

        setApprovedRequests([]);
      }
    } catch (error) {

      setApprovedRequests([]);
    }
  };

  // دالة مزامنة لجلب الطلبات المعتمدة وإرجاع البيانات مباشرة
  const loadApprovedRequestsSync = async (date) => {
    try {
      const response = await fetch(`/api/attendance-requests?date=${date}`);
      if (response.ok) {
        const result = await response.json();
        const requestsData = result.data || [];
        setApprovedRequests(requestsData); // تحديث الحالة أيضاً
        console.log('🔍 تم جلب الطلبات المعتمدة (مزامن):', requestsData);
        // تسجيل تفصيلي للطلبات المعتمدة
        requestsData.forEach(req => {
          console.log(`📋 طلب معتمد (مزامن): ${req.employeeName} (${req.employeeCode}) - ${req.attendanceType} - ${req.notes}`);
        });
        return requestsData;
      } else {
        console.error('خطأ في جلب الطلبات المعتمدة (مزامن)');
        setApprovedRequests([]);
        return [];
      }
    } catch (error) {
      console.error('خطأ في جلب الطلبات المعتمدة (مزامن):', error);
      setApprovedRequests([]);
      return [];
    }
  };

  // دوال التنقل بين الأيام - متتالية
  const goToPreviousDay = () => {
    const currentDate = new Date(selectedDate);
    currentDate.setDate(currentDate.getDate() - 1);
    const newDate = currentDate.toISOString().split('T')[0];
    setSelectedDate(newDate);
    setDateInput(formatDate(newDate));
    setShowSavedAttendance(false); // إعادة تعيين حالة العرض
    loadApprovedRequests(newDate); // جلب الطلبات المعتمدة للتاريخ الجديد
  };

  const goToNextDay = () => {
    const currentDate = new Date(selectedDate);
    currentDate.setDate(currentDate.getDate() + 1);
    const newDate = currentDate.toISOString().split('T')[0];
    setSelectedDate(newDate);
    setDateInput(formatDate(newDate));
    setShowSavedAttendance(false); // إعادة تعيين حالة العرض
    loadApprovedRequests(newDate); // جلب الطلبات المعتمدة للتاريخ الجديد
  };

  const goToToday = () => {
    const today = new Date().toISOString().split('T')[0];
    setSelectedDate(today);
    setDateInput(formatDate(today));
    setShowSavedAttendance(false); // إعادة تعيين حالة العرض
    loadApprovedRequests(today); // جلب الطلبات المعتمدة للتاريخ الجديد
  };

  // دالة للتنقل لتاريخ محدد
  const goToDate = (targetDate) => {
    setSelectedDate(targetDate);
    setDateInput(formatDate(targetDate));
    setShowSavedAttendance(false);
  };

  // دالة لإعادة تحميل البيانات
  const reloadData = async () => {
    setLoading(true);
    setShowSavedAttendance(false);

    try {
      // إعادة تحميل الموظفين والتمام
      const res = await fetch(`/api/employees-unified?date=${selectedDate}`);
      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }
      const apiData = await res.json();
      const data = apiData.data || apiData.employees || [];

      // جلب التمام من API
      const attendanceRes = await fetch(`/api/attendance?date=${selectedDate}`);
      let attendance = [];

      if (attendanceRes.ok) {
        const attendanceData = await attendanceRes.json();
        attendance = attendanceData.data || [];
      }

      setEmployees(data);
      const mappedData = data.map((emp, idx) => {
        const empCode = emp.EmployeeID || emp.employeeCode || emp.EmployeeCode;
        const found = attendance?.find(e =>
          String(e.employeeCode) === String(empCode) ||
          String(e.EmployeeCode) === String(empCode) ||
          String(e.EmployeeID) === String(empCode)
        );

        // Check if employee is resigned or transferred
        const isResigned = emp.status === 'resigned' && emp.lastWorkingDay;
        const isTransferred = emp.status === 'transferred' && emp.transferDate;

        let status = '';
        let notes = '';

        if (isResigned) {
          status = 'استقالة';
          notes = `تاريخ الاستقالة: ${formatDate(emp.resignationDate)} - آخر يوم عمل: ${formatDate(emp.lastWorkingDay)}`;
        } else if (isTransferred) {
          status = 'نقل';
          notes = `تاريخ النقل: ${formatDate(emp.transferDate)} - القسم الجديد: ${emp.destinationDepartment}`;
        }

        const result = found ? {
          ...emp,
          attendance: found.attendance,
          checkIn: found.checkIn || '',
          notes: found.notes || notes || ''
        } : {
          ...emp,
          seq: idx + 1,
          attendance: status || '',
          checkIn: '',
          notes: notes || ''
        };

        return result;
      });

      setAttendanceData(mappedData);

      const savedCount = attendance?.length || 0;
      if (savedCount > 0) {
        setShowSavedAttendance(true);
        alert(`تم إعادة تحميل البيانات - وُجد ${savedCount} سجل تمام محفوظ`);
      } else {
        alert('تم إعادة تحميل البيانات - لا توجد بيانات تمام محفوظة');
      }

    } catch (error) {

      alert('حدث خطأ أثناء إعادة تحميل البيانات: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // دالة لعرض التمام المحفوظ
  const loadSavedAttendance = async () => {
    setLoading(true);

    try {
      const response = await fetch(`/api/attendance?date=${selectedDate}`);

      if (response.ok) {
        const result = await response.json();
        const savedData = result.data || [];

        if (savedData.length === 0) {
          alert('لا توجد بيانات تمام محفوظة لهذا اليوم');
          setShowSavedAttendance(false);
          return;
        }

        // دمج البيانات المحفوظة مع بيانات الموظفين
        setAttendanceData(prev => {
          const updated = prev.map(emp => {
            const empCode = emp.EmployeeID || emp.employeeCode || emp.EmployeeCode;
            const saved = savedData.find(s =>
              String(s.employeeCode) === String(empCode) ||
              String(s.EmployeeCode) === String(empCode) ||
              String(s.EmployeeID) === String(empCode)
            );

            if (saved) {

              return {
                ...emp,
                attendance: saved.attendance,
                checkIn: saved.checkIn || '',
                notes: saved.notes || ''
              };
            }
            return emp;
          });

          return updated;
        });

        setShowSavedAttendance(true);
        alert(`تم تحميل ${savedData.length} سجل تمام محفوظ بنجاح`);
      } else {
        const errorText = await response.text();

        alert('لا توجد بيانات تمام محفوظة لهذا اليوم');
        setShowSavedAttendance(false);
      }
    } catch (error) {

      alert('حدث خطأ أثناء تحميل التمام المحفوظ: ' + error.message);
      setShowSavedAttendance(false);
    } finally {
      setLoading(false);
    }
  };

  // اختصارات لوحة المفاتيح
  useEffect(() => {
    const handleKeyPress = (e) => {
      // تجنب التفعيل عند الكتابة في حقول الإدخال
      if (e.target.tagName === 'INPUT' || e.target.tagName === 'SELECT' || e.target.tagName === 'TEXTAREA') {
        return;
      }

      if (e.key === 'ArrowLeft' || e.key === 'h') {
        e.preventDefault();
        goToPreviousDay();
      } else if (e.key === 'ArrowRight' || e.key === 'l') {
        e.preventDefault();
        goToNextDay();
      } else if (e.key === 't' || e.key === 'T') {
        e.preventDefault();
        goToToday();
      } else if (e.key === 'v' || e.key === 'V') {
        e.preventDefault();
        loadSavedAttendance();
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, []);

  // دالة لجلب التمام من API
  useEffect(() => {
    (async () => {
      setLoading(true);

      try {
        // جلب جميع الموظفين بدون حد أقصى
        const res = await fetch(`/api/employees-unified?date=${selectedDate}&limit=0`);
        if (!res.ok) {
          throw new Error(`HTTP error! status: ${res.status}`);
        }
        const apiData = await res.json();
        const data = apiData.data || apiData.employees || [];
        console.log(`👥 Loaded ${data.length} employees (all employees requested)`);

        // جلب التمام المحفوظ من API
        const attendanceRes = await fetch(`/api/attendance?date=${selectedDate}`);
        let attendance = [];

        if (attendanceRes.ok) {
          const attendanceData = await attendanceRes.json();
          attendance = attendanceData.data || [];

        } else {

        }

        // جلب الطلبات المعتمدة مباشرة

        const approvedRequestsData = await loadApprovedRequestsSync(selectedDate);

        // معالجة البيانات مع الطلبات المعتمدة
        processAttendanceData(data, attendance, approvedRequestsData);

      } catch (error) {

        alert('حدث خطأ أثناء جلب البيانات: ' + error.message);
        setLoading(false);
      }
    })();
  }, [selectedDate]);

  // دالة منفصلة لمعالجة بيانات التمام
  const processAttendanceData = (employeesData, attendanceData, approvedRequestsData = null) => {
    // استخدم البيانات الممررة أو الحالة الحالية
    const requestsToUse = approvedRequestsData || approvedRequests;

    setEmployees(employeesData);
    const mappedData = employeesData.map((emp, idx) => {
      // البحث عن التمام المحفوظ باستخدام جميع الحقول الممكنة
      const empCode = emp.EmployeeID || emp.employeeCode || emp.EmployeeCode;
      const found = attendanceData?.find(e =>
        String(e.employeeCode) === String(empCode) ||
        String(e.EmployeeCode) === String(empCode) ||
        String(e.EmployeeID) === String(empCode)
      );

      // البحث عن طلب معتمد للموظف
      const approvedRequest = requestsToUse.find(req =>
        String(req.employeeCode) === String(empCode)
      );

      let result;
      if (approvedRequest) {
        // إذا كان هناك طلب معتمد، استخدمه (أولوية عالية)
        // API يرسل البيانات مترجمة بالفعل، لا نحتاج ترجمة إضافية
        result = {
          ...emp,
          seq: idx + 1,
          attendance: approvedRequest.attendanceType, // البيانات مترجمة من API
          checkIn: found ? found.checkIn || '' : '',
          checkOut: found ? found.checkOut || '' : '',
          notes: approvedRequest.notes || '',
          isFromRequest: true
        };

      } else if (found) {
        // إذا كان هناك تمام محفوظ فقط، استخدمه
        result = {
          ...emp,
          seq: idx + 1,
          attendance: found.attendance,
          checkIn: found.checkIn || '',
          checkOut: found.checkOut || '',
          notes: found.notes || ''
        };

      } else {
        // لا يوجد تمام محفوظ أو طلب معتمد - تحقق من الحالات الافتراضية
        const defaultAttendance = emp.defaultAttendance || '';
        const defaultNotes = emp.defaultNotes || '';

        result = {
          ...emp,
          seq: idx + 1,
          attendance: defaultAttendance,
          checkIn: '',
          checkOut: '',
          notes: defaultNotes,
          isLocked: emp.isResigned || emp.isTransferred || false
        };
      }

      return result;
    });

    setAttendanceData(mappedData);

    // إذا وُجدت بيانات محفوظة أو طلبات معتمدة، اعرض المؤشر
    const savedCount = attendanceData?.length || 0;
    const approvedCount = requestsToUse?.length || 0;

    if (savedCount > 0 || approvedCount > 0) {
      setShowSavedAttendance(true);

    }

    setLoading(false);
  };

  // useEffect منفصل لمعالجة البيانات عند تغيير الطلبات المعتمدة
  // تم تعطيله مؤقتاً لحل مشكلة الظهور والاختفاء
  /*
  useEffect(() => {
    if (employees.length > 0) {

      // إعادة جلب التمام المحفوظ
      (async () => {
        try {
          const attendanceRes = await fetch(`/api/attendance?date=${selectedDate}`);
          let attendance = [];
          if (attendanceRes.ok) {
            const attendanceData = await attendanceRes.json();
            attendance = attendanceData.data || [];
          }
          processAttendanceData(employees, attendance);
        } catch (error) {

        }
      })();
    }
  }, [approvedRequests, employees.length]);
  */

  useEffect(() => {
    setDateInput(formatDate(selectedDate));
  }, [selectedDate]);

  // زر تحميل نموذج شيت البصمة
  const handleDownloadTemplate = () => {
    const csvContent = 'employeeCode,employeeName,checkIn\nEMP001,أحمد محمد,08:30\nEMP002,فاطمة علي,08:45\n';
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'daily_attendance_template.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // رفع شيت البصمة (csv)
  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    if (file.name.endsWith('.csv')) {
      const reader = new FileReader();
      reader.onload = (evt) => {
        if (evt.target && evt.target.result) {
          const text = evt.target.result.toString();
          const lines = text.split('\n').filter(Boolean);
          const header = lines[0].split(',');
          const rows = lines.slice(1).map((line) => {
            const [employeeCode, employeeName, checkIn] = line.split(',');
            return { employeeCode: employeeCode?.trim(), employeeName: employeeName?.trim(), checkIn: checkIn?.trim() };
          });
          setAttendanceData((prev) =>
            prev.map((emp) => {
              const found = rows.find((row) => row.employeeCode === emp.employeeCode);
              if (found) {
                return { ...emp, attendance: 'حضور', checkIn: found.checkIn || '' };
              }
              return emp;
            })
          );
        }
      };
      reader.readAsText(file);
    } else {
      alert('يرجى رفع ملف CSV فقط حالياً');
    }
  };

  // دالة للتحقق من إذا كان الإجراء يتطلب توقيت
  const doesAttendanceRequireTime = (attendance) => {
    if (!attendance) return false;
    const option = ATTENDANCE_OPTIONS.find(opt => opt.value === attendance);
    return option ? option.requiresTime : false;
  };

  // دالة لتحويل التوقيت إلى صيغة 12 ساعة
  const formatTime = (time) => {
    if (!time || time === '-') return '-';
    try {
      const [hours, minutes] = time.split(':');
      const date = new Date();
      date.setHours(parseInt(hours), parseInt(minutes));
      return date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      }).toLowerCase();
    } catch (error) {

      return time;
    }
  };

  // دالة لتحديد ما إذا كان الإجراء يحتاج إلى تظليل
  const shouldHighlightAttendance = (attendance) => {
    return attendance !== 'حضور' &&
           attendance !== 'وردية ليلية' &&
           attendance !== 'إجازة رسمية';
  };

  // تحديث عرض الصف في الجدول
  const getRowClassName = (attendance, isDarkMode) => {
    const baseClass = 'transition-colors';
    if (shouldHighlightAttendance(attendance)) {
      return `${baseClass} ${
        isDarkMode
          ? 'bg-gray-800 hover:bg-gray-700'
          : 'bg-gray-100 hover:bg-gray-200'
      }`;
    }
    return `${baseClass} ${
      isDarkMode
        ? 'hover:bg-gray-800'
        : 'hover:bg-gray-50'
    }`;
  };

  // تحديث دالة handleAttendanceChange
  const handleAttendanceChange = (employeeId, value) => {
    setAttendanceData(prev => prev.map(emp => {
      if ((emp.EmployeeID || emp.employeeCode || emp.EmployeeCode) === employeeId) {
        const requiresTime = doesAttendanceRequireTime(value);
        return {
          ...emp,
          attendance: value,
          checkIn: requiresTime ? emp.checkIn : '-',
          checkOut: requiresTime ? emp.checkOut : '-'
        };
      }
      return emp;
    }));
  };

  const handleCheckInChange = (employeeId, value) => {
    setAttendanceData(prev => prev.map(emp => {
      if ((emp.EmployeeID || emp.employeeCode || emp.EmployeeCode) === employeeId) {
        const requiresTime = doesAttendanceRequireTime(emp.attendance);
        if (!requiresTime) return emp;
        return {
          ...emp,
          checkIn: value
        };
      }
      return emp;
    }));
  };

  const handleCheckOutChange = (employeeId, value) => {
    setAttendanceData(prev => prev.map(emp => {
      if ((emp.EmployeeID || emp.employeeCode || emp.EmployeeCode) === employeeId) {
        const requiresTime = doesAttendanceRequireTime(emp.attendance);
        if (!requiresTime) return emp;
        return {
          ...emp,
          checkOut: value
        };
      }
      return emp;
    }));
  };

  const handleNotesChange = (employeeId, value) => {
    setAttendanceData((prev) =>
      prev.map((row) => {
        const empCode = row.EmployeeID || row.employeeCode || row.EmployeeCode;
        return String(empCode) === String(employeeId) ? { ...row, notes: value } : row;
      })
    );
  };

  // دالة التحقق من الإجراءات قبل الحفظ
  const checkAttendanceConflicts = async (attendanceToSave) => {
    try {
      const conflicts = [];

      for (const record of attendanceToSave) {
        if (record.attendance && record.attendance !== 'حضور' && record.attendance !== '' && record.attendance !== 'اختر') {
          const response = await fetch('/api/check-employee-actions', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              employeeCode: record.EmployeeCode,
              startDate: selectedDate,
              endDate: selectedDate,
              actionType: 'attendance'
            })
          });

          const result = await response.json();

          if (result.success && result.hasConflict) {
            conflicts.push({
              employeeCode: record.EmployeeCode,
              employeeName: record.EmployeeName,
              newAttendance: record.attendance,
              conflict: result.conflictDetails
            });
          } else if (result.success && result.hasRestriction) {
            conflicts.push({
              employeeCode: record.EmployeeCode,
              employeeName: record.EmployeeName,
              newAttendance: record.attendance,
              restriction: result.restrictionMessage
            });
          }
        }
      }

      return conflicts;
    } catch (error) {

      return [];
    }
  };

  const handleSave = async () => {

    // إنشاء بيانات للحفظ - بدون قيم افتراضية للتمام
    const testData = attendanceData.map(row => ({
      EmployeeCode: row.EmployeeID || row.employeeCode || row.EmployeeCode,
      EmployeeName: row.FullName || row.employeeName || row.EmployeeName,
      Department: row.Department || row.department || '',
      JobTitle: row.JobTitle || row.jobTitle || '',
      attendance: row.attendance, // لا نضع قيمة افتراضية هنا
      checkIn: row.checkIn || '',
      checkOut: row.checkOut || '',
      notes: row.notes || ''
    })).filter(row => {
      // فلترة أكثر دقة
      const hasBasicInfo = row.EmployeeCode && row.EmployeeName;
      const hasAttendance = row.attendance && row.attendance !== '' && row.attendance !== 'اختر';

      return hasBasicInfo && hasAttendance;
    });

    if (testData.length === 0) {
      alert('لا توجد بيانات صالحة للحفظ. جرب زر "اختبار سريع" أولاً أو تأكد من تسجيل التمام للموظفين');

      return;
    }

    // التحقق من التضارب والقيود

    const conflicts = await checkAttendanceConflicts(testData);

    if (conflicts.length > 0) {
      let conflictMessage = '⚠️ تحذير: تم العثور على تضارب في الإجراءات!\n\n';

      conflicts.forEach(conflict => {
        if (conflict.restriction) {
          conflictMessage += `❌ ${conflict.employeeName} (${conflict.employeeCode}):\n`;
          conflictMessage += `   ${conflict.restriction}\n\n`;
        } else if (conflict.conflict) {
          conflictMessage += `⚠️ ${conflict.employeeName} (${conflict.employeeCode}):\n`;
          conflictMessage += `   إجراء موجود: ${conflict.conflict.actionType}\n`;
          conflictMessage += `   الحالة: ${conflict.conflict.status}\n`;
          conflictMessage += `   الإجراء الجديد: ${conflict.newAttendance}\n\n`;
        }
      });

      // التحقق من وجود إجراءات معتمدة
      const hasApprovedConflicts = conflicts.some(c =>
        c.conflict && (c.conflict.status === 'معتمد' || c.conflict.status === 'approved')
      );

      if (hasApprovedConflicts) {
        alert(conflictMessage + 'لا يمكن حفظ التمام بسبب وجود إجراءات معتمدة متضاربة.');
        return;
      }

      // إذا كانت الإجراءات قيد المراجعة، اسأل المستخدم
      const userChoice = confirm(conflictMessage +
        'هل تريد المتابعة وحفظ التمام؟ سيتم استبدال الإجراءات قيد المراجعة.\n\n' +
        'اضغط "موافق" للمتابعة أو "إلغاء" للعودة وتعديل البيانات.');

      if (!userChoice) {
        return;
      }
    }

    // التأكد من وجود جدول التمام اليومي
    try {
      const tableCheckResponse = await fetch('/api/attendance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'ensure-table' })
      });
      console.log('Table check response:', await tableCheckResponse.text());
    } catch (tableError) {

    }

    try {
      const response = await fetch('/api/attendance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'save',
          date: selectedDate,
          attendanceData: testData
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const result = await response.json();
      if (result.success) {
        alert(`تم حفظ التمام بنجاح! (${result.savedRecords} سجل)`);
        setShowSavedAttendance(true);
      } else {
        alert('خطأ في حفظ التمام: ' + result.error);
      }
    } catch (error) {

      alert('حدث خطأ أثناء حفظ التمام: ' + error.message);
    }
  };

  // ترحيل التمام إلى كشف التمام الشهري
  const handleTransferToMonthlySheet = async () => {
    const date = new Date(selectedDate);
    const month = date.getMonth() + 1;
    const year = date.getFullYear();

    if (!confirm(`هل تريد ترحيل بيانات التمام لشهر ${month}/${year} إلى كشف التمام الشهري؟`)) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/attendance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'transfer-to-monthly-sheet',
          month,
          year
        }),
      });

      const result = await response.json();
      if (result.success) {
        alert(`تم ترحيل ${result.transferredCount} سجل بنجاح إلى كشف التمام الشهري`);
      } else {
        alert('خطأ في الترحيل: ' + result.error);
      }
    } catch (error) {

      alert('حدث خطأ أثناء الترحيل');
    } finally {
      setLoading(false);
    }
  };

  // حساب ملخص التمام الشهري
  const handleCalculateMonthlyAttendance = async () => {
    const date = new Date(selectedDate);
    const month = date.getMonth() + 1;
    const year = date.getFullYear();

    if (!confirm(`هل تريد حساب ملخص التمام الشهري لشهر ${month}/${year}؟`)) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/attendance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'calculate-monthly-attendance',
          month,
          year
        }),
      });

      const result = await response.json();
      if (result.success) {
        alert(`تم حساب ملخص التمام الشهري لـ ${result.employeesCount} موظف`);
      } else {
        alert('خطأ في الحساب: ' + result.error);
      }
    } catch (error) {

      alert('حدث خطأ أثناء الحساب');
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (idx) => setEditRow(idx);
  const handleCancelEdit = () => setEditRow(null);
  const handleConfirmEdit = (idx) => {
    if (window.confirm('هل أنت متأكد من حفظ التعديل؟')) {
      setEditRow(null);
      // يمكنك هنا إرسال التعديل إلى الـ API إذا أردت
    }
  };

  const handleExportExcel = () => {
    const headers = ['م', 'الكود', 'الاسم', 'المسمى الوظيفي', 'التمام', 'التوقيت', 'ملاحظات'];
    const rows = filteredData.map((emp, idx) => [
      idx + 1,
      emp.EmployeeCode || '',
      emp.EmployeeName || '',
      emp.JobTitle || '',
      emp.attendance || '',
      emp.checkIn || '',
      emp.notes || ''
    ]);
    const csvContent = [headers, ...rows].map(e => e.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `كشف_التمام_${selectedDate}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // عند تجهيز filteredData، أضف الترتيب المخصص:
  const sortedData = [...attendanceData].sort((a, b) => {
    const rankA = getManagerRank(a.JobTitle || a.jobTitle || '');
    const rankB = getManagerRank(b.JobTitle || b.jobTitle || '');
    if (rankA !== rankB) return rankA - rankB;
    // ترتيب إضافي حسب الكود إذا الرتبة متساوية
    return (a.EmployeeCode || a.employeeCode || 0) - (b.EmployeeCode || b.employeeCode || 0);
  });
  const periodEnd = (() => {
    // نهاية الفترة - اليوم العاشر من الشهر التالي
    const d = new Date(selectedDate);
    // إذا كان التاريخ بعد اليوم 10، نأخذ اليوم 10 من الشهر التالي
    if (d.getDate() > 10) {
      d.setMonth(d.getMonth() + 1);
    }
    d.setDate(10);
    return d.toISOString().split('T')[0];
  })();

  const filteredData = sortedData.filter((emp) => {
    const code = emp.EmployeeID || emp.employeeCode || emp.EmployeeCode;

    // تسجيل للتشخيص - مراقبة البيانات المعروضة
    if (emp.attendance && emp.attendance !== 'حضور' && emp.attendance !== '-') {
      console.log(`🎯 بيانات العرض: ${emp.EmployeeName || emp.FullName} (${code}) - التمام: "${emp.attendance}" - الملاحظات: "${emp.notes || 'لا توجد'}"`);
    }

    // تحقق من وجود استقالة أو نقل
    const resignation = resignations.find(r => String(r.EmployeeCode) === String(code));
    const transfer = transfers.find(t => String(t.EmployeeCode) === String(code));
    const today = selectedDate;

    // تسجيل للتشخيص للموظف إسلام جمعة
    if (emp.EmployeeName && emp.EmployeeName.includes('إسلام جمعة')) {
      console.log(`🔍 فحص الموظف إسلام جمعة:`, {
        code,
        today,
        periodEnd,
        resignation: resignation ? {
          resignDate: resignation.ResignationDate?.split('T')[0],
          lastWorkingDay: resignation.LastWorkingDay?.split('T')[0]
        } : null,
        employeeData: emp
      });
    }
    // منطق الاستقالة - يظهر الموظف المستقيل حتى نهاية الفترة الشهرية
    if (resignation && resignation.ResignationDate) {
      const resignDate = resignation.ResignationDate.split('T')[0];

      // تسجيل إضافي للتشخيص
      if (emp.EmployeeName && emp.EmployeeName.includes('إسلام جمعة')) {
        console.log(`📋 قرار العرض لإسلام جمعة:`, {
          today,
          resignDate,
          periodEnd,
          beforeResign: today < resignDate,
          inResignPeriod: today >= resignDate && today <= periodEnd,
          shouldShow: today < resignDate || (today >= resignDate && today <= periodEnd)
        });
      }

      // يظهر الموظف إذا كان التاريخ قبل الاستقالة أو في فترة الاستقالة حتى نهاية الفترة
      if (today < resignDate) return true; // يظهر عادي قبل الاستقالة
      if (today >= resignDate && today <= periodEnd) return true; // يظهر كمستقيل في الفترة
      return false; // لا يظهر بعد نهاية الفترة
    }
    // منطق النقل - حسب المتطلبات الجديدة
    if (transfer && transfer.TransferDate) {
      const transDate = transfer.TransferDate.split('T')[0];
      if (today < transDate) return true; // يظهر عادي قبل النقل
      if (today >= transDate && today <= periodEnd) return true; // يظهر كمنقول في الفترة
      return false; // لا يظهر بعد نهاية الفترة
    }

    // منطق الموظف الجديد - حسب المتطلبات الجديدة
    const joinDate = emp.JoinDate ? new Date(emp.JoinDate) : null;
    const selectedDateObj = new Date(selectedDate);

    if (joinDate) {
      // تحديد بداية الفترة التي انضم فيها الموظف
      const periodStart = (() => {
        const d = new Date(joinDate);
        // إذا كان تاريخ الانضمام بعد اليوم 10، فالفترة تبدأ من 11 من الشهر السابق
        if (d.getDate() > 10) {
          d.setDate(11);
        } else {
          // إذا كان قبل أو في اليوم 10، فالفترة تبدأ من 11 من الشهر الذي قبل الشهر السابق
          d.setMonth(d.getMonth() - 1);
          d.setDate(11);
        }
        return d.toISOString().split('T')[0];
      })();

      // الموظف يظهر فقط من بداية فترة انضمامه
      if (selectedDate < periodStart) return false;
    }

    // الموظف العادي أو البحث
    return (
      (emp.EmployeeName || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (emp.EmployeeCode || '').toString().includes(searchTerm) ||
      (emp.JobTitle || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
      (emp.Department || '').toLowerCase().includes(searchTerm.toLowerCase())
    );
  }).map((emp) => {
    // تعديل بند التمام والملاحظات إذا كان استقالة أو نقل
    const code = emp.EmployeeID || emp.employeeCode || emp.EmployeeCode;
    const resignation = resignations.find(r => String(r.EmployeeCode) === String(code));
    const transfer = transfers.find(t => String(t.EmployeeCode) === String(code));
    const today = selectedDate;
    // منطق الاستقالة - حسب المتطلبات الجديدة
    if (resignation && resignation.ResignationDate) {
      const resignDate = resignation.ResignationDate.split('T')[0];

      if (today >= resignDate) {
        return {
          ...emp,
          attendance: 'استقالة', // في التمام اليومي نستخدم "استقالة"
          notes: `تم تقديم استقالة بتاريخ ${formatDate(resignDate)}`,
          isLocked: true
        };
      }
    }

    // منطق النقل - حسب المتطلبات الجديدة
    if (transfer && transfer.TransferDate) {
      const transDate = transfer.TransferDate.split('T')[0];
      const destination = transfer.NewDepartment || transfer.destinationDepartment || 'قسم آخر';

      if (today >= transDate) {
        return {
          ...emp,
          attendance: 'نقل', // في التمام اليومي نستخدم "نقل"
          notes: `تم النقل إلى "${destination}" بتاريخ ${formatDate(transDate)}`,
          isLocked: true
        };
      }
    }

    // منطق الموظف الجديد - حسب المتطلبات الجديدة
    const joinDate = emp.JoinDate ? new Date(emp.JoinDate) : null;
    const selectedDateObj = new Date(selectedDate);

    if (joinDate && selectedDateObj < joinDate) {
      return {
        ...emp,
        attendance: '-',
        notes: `تعيين جديد بتاريخ ${formatDate(emp.JoinDate)}`,
        isLocked: true
      };
    }
    return emp;
  });

  const loadAttendanceData = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/attendance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'list',
          date: selectedDate,
          limit: 100
        })
      });

      const result = await response.json();
      if (result.success) {
        // جلب بيانات الاستقالات والنقل
        const resignationsResponse = await fetch('/api/resignations');
        const transfersResponse = await fetch('/api/transfers');
        const resignationsData = await resignationsResponse.json();
        const transfersData = await transfersResponse.json();

        const resignations = resignationsData.success ? resignationsData.data : [];
        const transfers = transfersData.success ? transfersData.data : [];

        // تعديل بيانات التمام بناءً على حالة الموظف - حسب المتطلبات الجديدة
        const modifiedData = result.data.map(emp => {
          const resignation = resignations.find(r => r.EmployeeID === emp.employeeCode);
          const transfer = transfers.find(t => t.EmployeeID === emp.employeeCode);
          const joinDate = emp.JoinDate ? new Date(emp.JoinDate) : null;
          const selectedDateObj = new Date(selectedDate);

          // منطق الاستقالة
          if (resignation && selectedDateObj >= new Date(resignation.ResignationDate)) {
            emp.attendance = 'استقالة'; // في التمام اليومي نستخدم "استقالة"
            emp.notes = `تم تقديم استقالة بتاريخ ${formatDate(resignation.ResignationDate)}`;
            emp.isLocked = true;
          }
          // منطق النقل
          else if (transfer && selectedDateObj >= new Date(transfer.TransferDate)) {
            const destination = transfer.NewDepartment || transfer.destinationDepartment || 'قسم آخر';
            emp.attendance = 'نقل'; // في التمام اليومي نستخدم "نقل"
            emp.notes = `تم النقل إلى "${destination}" بتاريخ ${formatDate(transfer.TransferDate)}`;
            emp.isLocked = true;
          }
          // منطق الموظف الجديد
          else if (joinDate && selectedDateObj < joinDate) {
            emp.attendance = '-';
            emp.notes = `تعيين جديد بتاريخ ${formatDate(emp.JoinDate)}`;
            emp.isLocked = true;
          }
          return emp;
        });

        setAttendanceData(modifiedData);
      }
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  // جلب بيانات الاستقالة والنقل عند تغيير التاريخ
  useEffect(() => {
    async function fetchResignationsAndTransfers() {
      try {
        const resResign = await fetch('/api/resignations');
        const resTrans = await fetch('/api/transfers');
        const resignData = await resResign.json();
        const transData = await resTrans.json();
        setResignations(resignData.data || []);
        setTransfers(transData.data || []);
      } catch (err) {
        setResignations([]);
        setTransfers([]);
      }
    }
    fetchResignationsAndTransfers();
  }, [selectedDate]);

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto">
        <div className={`${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-800'} rounded-lg shadow-sm p-6 mb-6`}>
          <div className="flex items-center gap-3">
            <span className="text-3xl">🕐</span>
            <div>
              <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>التمام اليومي</h1>
              <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'} mb-2`}>
                متابعة حضور وانصراف الموظفين ({filteredData.length} موظف)
              </p>
              {approvedRequests.length > 0 && (
                <div className="flex items-center gap-2 mt-2">
                  <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                    ✓ {approvedRequests.length} طلب معتمد لهذا اليوم
                  </span>
                </div>
              )}
              <div className="flex items-center gap-4 mt-2">
                <label htmlFor="date" className={`font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>اليوم:</label>

                {/* أسهم التنقل المحسنة */}
                <div className="flex items-center gap-1">
                  <button
                    onClick={goToPreviousDay}
                    className={`px-3 py-2 rounded-lg border-2 transition-all duration-200 font-bold ${
                      isDarkMode
                        ? 'bg-gray-700 border-gray-500 text-white hover:bg-gray-600 hover:border-gray-400 hover:scale-105'
                        : 'bg-white border-gray-400 text-gray-700 hover:bg-gray-50 hover:border-gray-500 hover:scale-105'
                    }`}
                    title="اليوم السابق (←)"
                  >
                    ← السابق
                  </button>

                  <div className="mx-2">
                    <input
                      id="date"
                      type="text"
                      value={dateInput}
                      onChange={e => {
                        setDateInput(e.target.value);
                        const parsed = parseDateInput(e.target.value);
                        if (parsed) {
                          setSelectedDate(parsed);
                          setShowSavedAttendance(false);
                        }
                      }}
                      className={`rounded-lg px-3 py-2 border-2 w-36 text-center font-medium ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-500 text-white focus:border-blue-400'
                          : 'bg-white border-gray-400 text-gray-800 focus:border-blue-500'
                      } focus:outline-none focus:ring-2 focus:ring-blue-300`}
                      placeholder="dd/mm/yyyy"
                      maxLength={10}
                      title="dd/mm/yyyy"
                      pattern="\d{2}/\d{2}/\d{4}"
                    />
                  </div>

                  <button
                    onClick={goToNextDay}
                    className={`px-3 py-2 rounded-lg border-2 transition-all duration-200 font-bold ${
                      isDarkMode
                        ? 'bg-gray-700 border-gray-500 text-white hover:bg-gray-600 hover:border-gray-400 hover:scale-105'
                        : 'bg-white border-gray-400 text-gray-700 hover:bg-gray-50 hover:border-gray-500 hover:scale-105'
                    }`}
                    title="اليوم التالي (→)"
                  >
                    التالي →
                  </button>

                  <button
                    onClick={goToToday}
                    className={`px-4 py-2 rounded-lg text-sm font-bold transition-all duration-200 ml-2 ${
                      isDarkMode
                        ? 'bg-blue-600 text-white hover:bg-blue-700 hover:scale-105'
                        : 'bg-blue-500 text-white hover:bg-blue-600 hover:scale-105'
                    } shadow-md hover:shadow-lg`}
                    title="العودة لليوم الحالي (T)"
                  >
                    🏠 اليوم
                  </button>
                </div>

                <div className="flex items-center gap-2">
                  <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-400'}`}>
                    {selectedDate && formatDate(selectedDate)}
                  </span>
                  {showSavedAttendance && (
                    <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                      تمام محفوظ
                    </span>
                  )}
                  <div className={`text-xs ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                    اختصارات: ← → (تنقل) | T (اليوم) | V (عرض محفوظ)
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* أزرار التنقل السريع المحسنة */}
        <div className="flex items-center gap-3 mb-4 flex-wrap">
          <span className={`text-sm font-bold ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            🗓️ تنقل سريع:
          </span>

          {/* أزرار الأيام القريبة */}
          {[-3, -2, -1, 0, 1, 2, 3].map(offset => {
            const targetDate = new Date();
            targetDate.setDate(targetDate.getDate() + offset);
            const dateStr = targetDate.toISOString().split('T')[0];
            const isToday = offset === 0;
            const isSelected = dateStr === selectedDate;

            let label = '';
            if (offset === -1) label = 'أمس';
            else if (offset === 0) label = 'اليوم';
            else if (offset === 1) label = 'غداً';
            else if (offset < 0) label = `${Math.abs(offset)} أيام`;
            else label = `+${offset} أيام`;

            return (
              <button
                key={offset}
                onClick={() => goToDate(dateStr)}
                className={`px-3 py-1 rounded-lg text-sm font-medium transition-all duration-200 ${
                  isSelected
                    ? isDarkMode
                      ? 'bg-blue-600 text-white border-2 border-blue-400 shadow-lg'
                      : 'bg-blue-500 text-white border-2 border-blue-300 shadow-lg'
                    : isToday
                    ? isDarkMode
                      ? 'bg-green-600 text-white hover:bg-green-700 border border-green-400'
                      : 'bg-green-500 text-white hover:bg-green-600 border border-green-300'
                    : isDarkMode
                    ? 'bg-gray-700 text-white hover:bg-gray-600 border border-gray-600'
                    : 'bg-gray-200 text-gray-800 hover:bg-gray-300 border border-gray-300'
                } hover:scale-105`}
                title={`${formatDate(dateStr)} - ${label}`}
              >
                {label}
              </button>
            );
          })}

          {/* زر التنقل لأسبوع سابق/تالي */}
          <div className="flex items-center gap-1 ml-4 border-r pr-4">
            <button
              onClick={() => {
                const weekAgo = new Date(selectedDate);
                weekAgo.setDate(weekAgo.getDate() - 7);
                goToDate(weekAgo.toISOString().split('T')[0]);
              }}
              className={`px-2 py-1 rounded text-xs transition-colors ${
                isDarkMode
                  ? 'bg-purple-600 text-white hover:bg-purple-700'
                  : 'bg-purple-500 text-white hover:bg-purple-600'
              }`}
              title="أسبوع سابق"
            >
              ⏪ أسبوع
            </button>
            <button
              onClick={() => {
                const weekLater = new Date(selectedDate);
                weekLater.setDate(weekLater.getDate() + 7);
                goToDate(weekLater.toISOString().split('T')[0]);
              }}
              className={`px-2 py-1 rounded text-xs transition-colors ${
                isDarkMode
                  ? 'bg-purple-600 text-white hover:bg-purple-700'
                  : 'bg-purple-500 text-white hover:bg-purple-600'
              }`}
              title="أسبوع تالي"
            >
              أسبوع ⏩
            </button>
          </div>
        </div>

        <div className="flex items-center gap-4 mb-4 flex-wrap">
          <label htmlFor="upload" className="flex items-center gap-2 cursor-pointer bg-blue-700 hover:bg-blue-800 px-3 py-2 rounded text-white transition-colors">
            <Icons.FiUpload /> رفع شيت البصمة
            <input id="upload" type="file" accept=".xlsx,.xls,.csv" onChange={handleFileUpload} className="hidden" />
          </label>
          <button onClick={handleDownloadTemplate} className={`px-3 py-2 rounded flex items-center gap-2 text-white transition-colors ${
            isDarkMode ? 'bg-gray-600 hover:bg-gray-700' : 'bg-gray-700 hover:bg-gray-800'
          }`}>
            <Icons.FiDownload /> تحميل نموذج شيت البصمة
          </button>

          {approvedRequests.length > 0 && (
            <button
              onClick={() => {
                const details = approvedRequests.map(req =>
                  `• ${req.employeeName} (${req.employeeCode}): ${req.attendanceType}\n  ${req.notes}`
                ).join('\n\n');
                alert(`الطلبات المعتمدة لتاريخ ${selectedDate}:\n\n${details}`);
              }}
              className="bg-blue-600 hover:bg-blue-700 px-3 py-2 rounded flex items-center gap-2 text-white transition-colors"
            >
              📋 طلبات معتمدة ({approvedRequests.length})
            </button>
          )}

          <button
            onClick={async () => {
              if (!confirm('هل تريد تحديث التمام من الطلبات المعتمدة؟')) return;

              try {
                const response = await fetch('/api/attendance', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    action: 'update-from-requests',
                    date: selectedDate
                  })
                });

                const result = await response.json();
                if (result.success) {
                  alert(`تم تحديث ${result.updatedCount || 0} سجل تمام من الطلبات المعتمدة`);
                  // إعادة تحميل البيانات
                  window.location.reload();
                } else {
                  alert('خطأ في التحديث: ' + result.error);
                }
              } catch (error) {
                alert('حدث خطأ: ' + error.message);
              }
            }}
            className="bg-purple-600 hover:bg-purple-700 px-3 py-2 rounded flex items-center gap-2 text-white transition-colors"
          >
            🔄 تحديث من الطلبات المعتمدة
          </button>

          <button
            onClick={async () => {
              try {
                const response = await fetch(`/api/attendance-requests?date=${selectedDate}`);
                const result = await response.json();

                if (result.success) {
                  const { data } = result;
                  const message = `
اختبار API الطلبات المعتمدة للتاريخ ${selectedDate}:

📊 إجمالي الطلبات: ${data.length}

${data.length > 0 ?
`🎯 تفاصيل الطلبات:
${data.map(req => `• ${req.employeeName} (${req.employeeCode}): ${req.attendanceType}\n  ملاحظات: ${req.notes}`).join('\n\n')}`
: '⚠️ لا توجد طلبات معتمدة لهذا التاريخ'}
                  `;
                  alert(message);

                } else {
                  alert('خطأ في الاختبار: ' + result.error);
                }
              } catch (error) {
                alert('حدث خطأ: ' + error.message);
              }
            }}
            className="bg-orange-600 hover:bg-orange-700 px-3 py-2 rounded flex items-center gap-2 text-white transition-colors"
          >
            🔍 اختبار API الطلبات
          </button>

          <button onClick={handleSave} className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded flex items-center gap-2 text-white transition-colors">
            <Icons.FiSave /> تسجيل التمام
          </button>

          <button
            onClick={reloadData}
            className="bg-gray-600 hover:bg-gray-700 px-3 py-2 rounded flex items-center gap-2 text-white transition-colors text-sm"
            disabled={loading}
          >
            🔄 إعادة تحميل
          </button>

          <button onClick={() => window.print()} className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded flex items-center gap-2 text-white transition-colors">
            <Icons.FiPrinter /> طباعة التمام اليومي
          </button>

          {/* أزرار الترحيل والحساب */}
          <div className="flex items-center gap-2 border-r pr-4 mr-4">
            <button
              onClick={handleTransferToMonthlySheet}
              className="bg-purple-600 hover:bg-purple-700 px-3 py-2 rounded flex items-center gap-2 text-white transition-colors text-sm"
              disabled={loading}
            >
              <Icons.FiArrowRight /> ترحيل لكشف التمام الشهري
            </button>
            <button
              onClick={handleCalculateMonthlyAttendance}
              className="bg-orange-600 hover:bg-orange-700 px-3 py-2 rounded flex items-center gap-2 text-white transition-colors text-sm"
              disabled={loading}
            >
              <Icons.FiCalculator /> حساب ملخص شهري
            </button>
          </div>

          <input
            type="text"
            placeholder="بحث بالاسم أو الكود أو القسم..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={`rounded px-2 py-1 border ml-4 ${
              isDarkMode
                ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                : 'bg-white border-gray-300 text-gray-800 placeholder-gray-500'
            }`}
          />
        </div>
        <div className="flex items-center gap-3 mb-4">
          <button
            onClick={handleExportExcel}
            className="bg-green-600 hover:bg-green-700 text-white font-bold py-1 px-3 rounded print:hidden"
          >
            تصدير التمام إلى Excel
          </button>
        </div>
        <div className="mb-2 flex gap-2">
          <button
            onClick={() => setEditAll(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded print:hidden"
            disabled={editAll}
          >
            تسجيل تمام جديد
          </button>
          {editAll && (
            <button
              onClick={() => setEditAll(false)}
              className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-1 px-3 rounded print:hidden"
            >
              إلغاء
            </button>
          )}
        </div>
        <div className="flex items-center gap-4 mt-2">
          <label htmlFor="bulkAttendance" className={`font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>تسجيل جماعي:</label>
          <select
            id="bulkAttendance"
            value={bulkAttendance}
            onChange={e => {
              setBulkAttendance(e.target.value);
              if (e.target.value) {
                setAttendanceData(prev => prev.map(row => ({
                  ...row,
                  attendance: e.target.value,
                  notes: e.target.value === 'إجازة إعتيادية' ? 'إجازة إعتيادية - تسجيل جماعي' :
                         e.target.value === 'إجازة رسمية' ? 'إجازة رسمية' :
                         e.target.value === 'راحة' ? 'راحة أسبوعية' :
                         e.target.value === 'حضور' ? 'تسجيل جماعي' : ''
                })));
                alert(`تم تسجيل ${e.target.value} لجميع الموظفين`);
              }
            }}
            className={`rounded px-3 py-2 border ${
              isDarkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-800'
            }`}
          >
            <option value="">اختر نوع التمام...</option>
            <option value="حضور">✅ حضور</option>
            <option value="راحة">🏠 راحة</option>
            <option value="إجازة رسمية">🏖️ إجازة رسمية</option>
            <option value="إجازة إعتيادية">📅 إجازة إعتيادية</option>
          </select>
          <button
            onClick={() => {
              setBulkAttendance('');
              setAttendanceData(prev => prev.map(row => ({
                ...row,
                attendance: row.isFromRequest ? row.attendance : '',
                notes: row.isFromRequest ? row.notes : ''
              })));
              alert('تم إلغاء التسجيل الجماعي (مع الحفاظ على الطلبات المعتمدة)');
            }}
            className={`px-4 py-2 rounded transition-colors ${
              isDarkMode
                ? 'bg-gray-600 hover:bg-gray-700 text-white'
                : 'bg-gray-500 hover:bg-gray-600 text-white'
            }`}
          >
            🗑️ إلغاء الكل
          </button>
        </div>
        <div className="print-area">
          {/* هيدر للطباعة محسن */}
          <div className="print-header print:block hidden" style={{
            width: '100%',
            marginBottom: 20,
            padding: '0 15px',
            pageBreakAfter: 'avoid'
          }}>
            {/* الصف الأول: اسم الشركة واللوجو */}
            <div style={{
              display: 'flex',
              alignItems: 'flex-start',
              justifyContent: 'space-between',
              width: '100%',
              marginBottom: 15
            }}>
              <div style={{
                textAlign: 'right',
                fontSize: 14,
                fontWeight: 'bold',
                lineHeight: 1.4,
                minWidth: 180
              }}>
                شركة كونكورد<br />للهندسة والمقاولات
              </div>
              <img
                src="/logo.png"
                alt="شعار الشركة"
                style={{
                  width: 100,
                  height: 65,
                  objectFit: 'contain'
                }}
              />
            </div>

            {/* العنوان الرئيسي */}
            <div style={{
              textAlign: 'center',
              fontWeight: 'bold',
              fontSize: 18,
              textDecoration: 'underline',
              margin: '10px 0',
              lineHeight: 1.3
            }}>
              كشف الحضور اليومي لمشروع مجمع مباني أوجيستا
            </div>

            {/* التاريخ */}
            <div style={{
              textAlign: 'center',
              fontWeight: 'normal',
              fontSize: 14,
              marginBottom: 15,
              lineHeight: 1.2
            }}>
              عن يوم {getArabicDayName(selectedDate)} الموافق {formatDate(selectedDate)}
            </div>
          </div>
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className={`${isDarkMode ? 'bg-gray-700 text-gray-200' : 'bg-gray-50 text-gray-700'} print:bg-white print:text-black`}>
              <tr>
                <th className={`px-4 py-3 text-right text-xs font-bold border-b w-12 ${isDarkMode ? 'border-gray-600' : 'border-gray-300'}`}>م</th>
                <th className={`px-4 py-3 text-right text-xs font-bold border-b w-24 ${isDarkMode ? 'border-gray-600' : 'border-gray-300'}`}>الكود</th>
                <th className={`px-4 py-3 text-right text-xs font-bold border-b w-48 ${isDarkMode ? 'border-gray-600' : 'border-gray-300'}`}>الاسم</th>
                <th className={`px-4 py-3 text-right text-xs font-bold border-b w-40 ${isDarkMode ? 'border-gray-600' : 'border-gray-300'}`}>المسمى الوظيفي</th>
                <th className={`px-4 py-3 text-right text-xs font-bold border-b w-32 ${isDarkMode ? 'border-gray-600' : 'border-gray-300'}`}>التمام</th>
                <th className={`px-4 py-3 text-right text-xs font-bold border-b w-24 ${isDarkMode ? 'border-gray-600' : 'border-gray-300'}`}>دخول</th>
                <th className={`px-4 py-3 text-right text-xs font-bold border-b w-40 ${isDarkMode ? 'border-gray-600' : 'border-gray-300'}`}>ملاحظات</th>
                <th className={`px-4 py-3 text-right text-xs font-bold border-b print:hidden w-20 ${isDarkMode ? 'border-gray-600' : 'border-gray-300'}`}>إجراء</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
              {filteredData.map((emp, idx) => {
                const code = emp.EmployeeID || emp.employeeCode || emp.EmployeeCode;
                const name = emp.FullName || emp.employeeName || emp.EmployeeName;
                const jobTitle = emp.JobTitle || emp.jobTitle || '-';
                const joinDate = emp.JoinDate || emp.JOINDATE;
                const isBeforeJoin = joinDate && selectedDate < joinDate.split('T')[0];
                const isEditing = editAll || editRow === idx;
                const hasAttendance = !!emp.attendance && emp.attendance !== '';

                // كسر الصفحة تلقائي حسب المساحة المتاحة
                const shouldBreakPage = false; // سيتم التحكم بواسطة CSS

                return (
                  <tr key={code} className={getRowClassName(emp.attendance, isDarkMode)}>
                    <td className={`px-4 py-2 text-right text-sm font-medium border-b ${
                      isDarkMode ? 'text-gray-200 border-gray-600' : 'text-gray-900 border-gray-200'
                    }`}>{idx + 1}</td>
                    <td className={`px-4 py-2 text-right text-sm font-bold border-b print:text-black ${
                      isDarkMode ? 'text-blue-400 border-gray-600' : 'text-blue-800 border-gray-200'
                    }`}>{code || <span style={{color:'red'}}>لا يوجد كود</span>}</td>
                    <td className={`px-4 py-2 text-right text-sm font-bold border-b print:text-black ${
                      isDarkMode ? 'text-gray-200 border-gray-600' : 'text-gray-900 border-gray-200'
                    }`}>{name || <span style={{color:'red'}}>لا يوجد اسم</span>}</td>
                    <td className={`px-4 py-2 text-right text-sm border-b ${
                      isDarkMode ? 'text-gray-300 border-gray-600' : 'text-gray-700 border-gray-200'
                    }`}>{jobTitle}</td>
                    <td
                      className={`px-4 py-2 text-sm border-b ${
                        isDarkMode ? 'border-gray-600' : 'border-gray-200'
                      } ${
                        !isEditing && emp.attendance && emp.attendance !== 'حضور' && emp.attendance !== 'إجازة رسمية' && emp.attendance !== 'راحة' && emp.attendance !== '' && emp.attendance !== '-'
                          ? isDarkMode
                            ? 'bg-blue-900 bg-opacity-30 print-attendance-action'
                            : 'bg-blue-50 print-attendance-action'
                          : ''
                      } ${
                        emp.isFromRequest
                          ? isDarkMode
                            ? 'bg-green-900 bg-opacity-40 border-green-700'
                            : 'bg-green-50 border-green-200'
                          : ''
                      }`}
                    >
                      {isEditing ? (
                        <select
                          value={emp.attendance}
                          onChange={(e) => handleAttendanceChange(emp.EmployeeID || emp.employeeCode || emp.EmployeeCode, e.target.value)}
                          className={`w-full rounded px-2 py-1 border ${
                            isDarkMode
                              ? 'bg-gray-700 border-gray-600 text-white'
                              : 'bg-white border-gray-300 text-black'
                          }`}
                          disabled={isBeforeJoin || emp.isLocked}
                        >
                          <option value="">اختر</option>
                          {ATTENDANCE_OPTIONS.map((opt) => (
                            <option key={opt.value} value={opt.value}>{opt.label}</option>
                          ))}
                        </select>
                      ) : (
                        <span className={`flex items-center gap-1 ${
                          emp.isFromRequest
                            ? isDarkMode
                              ? 'text-green-200 font-medium'
                              : 'text-green-800 font-medium'
                            : emp.attendance && emp.attendance !== 'حضور' && emp.attendance !== 'إجازة رسمية' && emp.attendance !== 'راحة' && emp.attendance !== '' && emp.attendance !== '-'
                              ? isDarkMode
                                ? 'text-blue-200 font-medium'
                                : 'text-blue-800 font-medium'
                              : isDarkMode
                                ? 'text-gray-200'
                                : 'text-gray-800'
                        }`}>
                          {emp.isFromRequest && <span className={`${isDarkMode ? 'text-green-300' : 'text-green-600'} print:text-black`} title="من طلب معتمد">✓</span>}
                          {emp.attendance || '-'}
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      {isEditing ? (
                        doesAttendanceRequireTime(emp.attendance) ? (
                          <input
                            type="time"
                            value={emp.checkIn || ''}
                            onChange={(e) => handleCheckInChange(emp.EmployeeID || emp.employeeCode || emp.EmployeeCode, e.target.value)}
                            className={`w-full rounded px-2 py-1 border ${
                              isDarkMode
                                ? 'bg-gray-700 border-gray-600 text-white'
                                : 'bg-white border-gray-300 text-black'
                            }`}
                            disabled={isBeforeJoin}
                          />
                        ) : (
                          <span className={isDarkMode ? 'text-gray-400' : 'text-gray-500'}>-</span>
                        )
                      ) : (
                        <span className={isDarkMode ? 'text-gray-200' : 'text-gray-800'}>
                          {doesAttendanceRequireTime(emp.attendance) ? formatTime(emp.checkIn) : '-'}
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      {isEditing || emp.isManualEntry ? (
                        <input
                          type="text"
                          value={emp.notes || ''}
                          onChange={(e) => handleNotesChange(emp.EmployeeID || emp.employeeCode || emp.EmployeeCode, e.target.value)}
                          className={`w-full rounded px-2 py-1 border ${
                            isDarkMode
                              ? 'bg-gray-700 border-gray-600 text-white'
                              : 'bg-white border-gray-300 text-black'
                          }`}
                        />
                      ) : (
                        <span className={isDarkMode ? 'text-gray-200' : 'text-gray-800'}>
                          {emp.notes || '-'}
                        </span>
                      )}
                    </td>
                    <td className={`px-4 py-2 text-right text-sm border-b print:hidden ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                      {!editAll && hasAttendance && !isEditing ? (
                        <button onClick={() => handleEdit(idx)} className="text-blue-600 hover:text-blue-800"><Icons.FiEdit2 /></button>
                      ) : null}
                      {!editAll && isEditing ? (
                        <>
                          <button onClick={() => handleConfirmEdit(idx)} className="text-green-600 hover:text-green-800 mx-1"><Icons.FiCheck /></button>
                          <button onClick={handleCancelEdit} className="text-red-600 hover:text-red-800 mx-1"><Icons.FiX /></button>
                        </>
                      ) : null}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
          {/* فوتر للطباعة محسن */}
          <div className="print-footer print:block hidden" style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'flex-end',
            padding: '0 15px',
            marginTop: 30,
            pageBreakBefore: 'avoid'
          }}>
            <div style={{
              textAlign: 'center',
              width: '30%',
              fontSize: 12,
              fontWeight: 'bold'
            }}>
              <div style={{ marginBottom: 40 }}>الشؤون الإدارية</div>
              <hr style={{
                border: 'none',
                borderBottom: '2px solid #000',
                margin: '0',
                width: '80%'
              }} />
            </div>

            <div style={{
              textAlign: 'center',
              width: '40%',
              fontSize: 11,
              color: '#666'
            }}>
              {/* مساحة فارغة للتوقيعات */}
            </div>

            <div style={{
              textAlign: 'center',
              width: '30%',
              fontSize: 12,
              fontWeight: 'bold'
            }}>
              <div style={{ marginBottom: 40 }}>مدير المنطقة</div>
              <hr style={{
                border: 'none',
                borderBottom: '2px solid #000',
                margin: '0',
                width: '80%'
              }} />
            </div>
          </div>
        </div>
        {loading && (
          <div className={`mt-4 text-center ${isDarkMode ? 'text-yellow-400' : 'text-yellow-600'}`}>
            <div className="flex items-center justify-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
              جاري التحميل...
            </div>
          </div>
        )}
      </div>
      <style jsx global>{`
      @media print {
        body * { visibility: hidden !important; }
        .print-area, .print-area * { visibility: visible !important; }
        .print-area {
          position: absolute !important;
          left: 50%;
          top: 0;
          width: 95vw;
          transform: translateX(-50%);
          background: white !important;
          color: black !important;
          z-index: 9999;
          padding: 10px;
          margin: 0;
        }
        .print\:hidden { display: none !important; }

        /* تحسين تنسيق الجدول للطباعة */
        table {
          background: white !important;
          color: black !important;
          page-break-inside: auto;
          border-collapse: collapse !important;
          width: 100% !important;
          table-layout: auto !important;
          font-size: 11px !important;
          margin: 0 auto !important;
          border: 2px solid #000 !important;
        }

        /* تحسين الخلايا - كل النصوص باللون الأسود */
        th, td {
          border: 1px solid #000 !important;
          padding: 2px 4px !important;
          font-size: 9px !important;
          text-align: center !important;
          vertical-align: top !important;
          color: #000 !important;
          line-height: 1.1 !important;
          word-wrap: break-word !important;
          background: white !important;
          height: auto !important;
          min-height: 16px !important;
          max-height: none !important;
          overflow: visible !important;
        }

        /* ضمان اللون الأسود لجميع النصوص والخلفية البيضاء */
        .print-area * {
          color: #000 !important;
          background: white !important;
        }

        /* إزالة أي خلفيات داكنة */
        .print-area, .print-area *, .print-area table, .print-area tbody, .print-area tr, .print-area td, .print-area th {
          background: white !important;
          background-color: white !important;
        }

        /* خلفيات بيضاء للطباعة */
        tbody tr td {
          background: white !important;
          color: #000 !important;
        }

        /* تظليل خفيف للإجراءات غير الحضور العادي والإجازة الرسمية والراحة */
        .print-attendance-action {
          background: #f8f8f8 !important;
          color: #000 !important;
        }

        /* تظليل للطلبات المعتمدة */
        .bg-green-50 {
          background: #f0f9ff !important;
        }

        .border-green-200 {
          border-color: #bfdbfe !important;
        }

        /* إزالة الخلفيات المتناوبة للطباعة */
        tbody tr:nth-child(even) td {
          background: white !important;
          color: #000 !important;
        }

        /* تطبيق التظليل على الصفوف المتناوبة أيضاً */
        tbody tr:nth-child(even) td.print-attendance-action {
          background: #f8f8f8 !important;
          color: #000 !important;
        }

        /* تحسين عرض الأعمدة مع autofit مرن */
        th:nth-child(1), td:nth-child(1) {
          width: auto !important;
          min-width: 30px !important;
          max-width: 50px !important;
          font-weight: bold !important;
          color: #000 !important;
          text-align: center !important;
        }  /* م */
        th:nth-child(2), td:nth-child(2) {
          width: auto !important;
          min-width: 60px !important;
          max-width: 80px !important;
          font-weight: bold !important;
          color: #000 !important;
          text-align: center !important;
        } /* الكود */
        th:nth-child(3), td:nth-child(3) {
          width: auto !important;
          min-width: 120px !important;
          text-align: right !important;
          color: #000 !important;
          white-space: normal !important;
          word-wrap: break-word !important;
          overflow-wrap: break-word !important;
        } /* الاسم */
        th:nth-child(4), td:nth-child(4) {
          width: auto !important;
          min-width: 100px !important;
          text-align: right !important;
          color: #000 !important;
          white-space: normal !important;
          word-wrap: break-word !important;
          overflow-wrap: break-word !important;
        } /* المسمى */
        th:nth-child(5), td:nth-child(5) {
          width: auto !important;
          min-width: 70px !important;
          max-width: 100px !important;
          color: #000 !important;
          text-align: center !important;
        } /* التمام */
        th:nth-child(6), td:nth-child(6) {
          width: auto !important;
          min-width: 50px !important;
          max-width: 70px !important;
          color: #000 !important;
          text-align: center !important;
        } /* دخول */
        th:nth-child(7), td:nth-child(7) {
          width: auto !important;
          min-width: 120px !important;
          color: #000 !important;
          text-align: right !important;
          white-space: normal !important;
          word-wrap: break-word !important;
          overflow-wrap: break-word !important;
        } /* ملاحظات */

        /* تثبيت هيدر الجدول في كل صفحة */
        @media print {
          thead {
            display: table-header-group !important;
            background: white !important;
            page-break-inside: avoid !important;
          }
          thead tr {
            page-break-after: avoid !important;
            page-break-inside: avoid !important;
            page-break-before: avoid !important;
          }
          thead th {
            background: #e8e8e8 !important;
            font-weight: bold !important;
            font-size: 11px !important;
            color: #000 !important;
            border: 1px solid #000 !important;
            padding: 4px 6px !important;
            page-break-inside: avoid !important;
          }
        }

        /* تحسين الهيدر للطباعة */

        .print-header {
          margin-bottom: 5px !important;
          page-break-after: avoid !important;
          text-align: center !important;
          background: white !important;
        }

        .print-header h2 {
          font-size: 13px !important;
          font-weight: bold !important;
          margin: 2px 0 !important;
          color: #000 !important;
          background: white !important;
        }

        .print-header p {
          font-size: 10px !important;
          margin: 1px 0 !important;
          color: #000 !important;
          background: white !important;
        }

        /* إزالة المساحات الزائدة */
        table {
          margin-top: 0 !important;
          margin-bottom: 0 !important;
        }

        /* تحسين الصفوف */
        tbody tr {
          page-break-inside: avoid !important;
          height: auto !important;
          background: white !important;
          margin: 0 !important;
          padding: 0 !important;
        }

        /* إزالة الخلفيات المتناوبة للطباعة */
        tbody tr:nth-child(even) {
          background: white !important;
        }

        /* تحسين الهيدر والفوتر */
        .print-footer {
          display: flex !important;
          justify-content: space-between !important;
          align-items: flex-end !important;
          margin-top: 20px !important;
          page-break-before: avoid !important;
        }

        /* إعدادات الصفحة محسنة - مدمجة أعلاه */

        /* تحسين استغلال المساحة */
        .print-area {
          max-height: none !important;
          overflow: visible !important;
        }

        /* تقليل المساحات الفارغة */
        .print-area > div {
          margin-bottom: 5px !important;
        }

        /* ضمان الألوان السوداء في الطباعة */
        * {
          -webkit-print-color-adjust: exact !important;
          color-adjust: exact !important;
          print-color-adjust: exact !important;
        }

        /* تأكيد اللون الأسود لجميع بيانات الموظفين */
        tbody tr td {
          color: #000 !important;
        }

        /* تأكيد اللون الأسود للنصوص داخل الخلايا */
        tbody tr td span {
          color: #000 !important;
        }

        /* تحسين كسر الصفحات */
        .page-break {
          page-break-before: always !important;
        }

        /* تحسين توزيع المحتوى على الصفحات - تلقائي */
        tbody tr {
          page-break-inside: avoid !important;
        }

        /* كسر الصفحة التلقائي */
        @page {
          size: A4 portrait;
          margin: 5mm 5mm 5mm 5mm;
          color: #000 !important;
          background: white !important;
          orphans: 3;
          widows: 3;
        }

        /* منع كسر الصفحة في أماكن غير مناسبة */
        .print-header, .print-footer {
          page-break-inside: avoid !important;
        }

        /* تحسين الكثافة للطباعة وإزالة الفراغات */
        tbody tr {
          height: auto !important;
          min-height: 16px !important;
          max-height: none !important;
        }

        /* إزالة المساحات الزائدة */
        .print-area {
          padding: 0 !important;
          margin: 0 !important;
        }

        /* تحسين المساحات بين العناصر */

        .print-footer {
          margin-top: 15px !important;
        }

        /* تحسين النصوص الطويلة */
        td:nth-child(3), td:nth-child(4) {
          white-space: normal !important;
          word-wrap: break-word !important;
          overflow-wrap: break-word !important;
        }

        /* تحسين خاص لعمود الملاحظات */
        td:nth-child(7) {
          white-space: normal !important;
          word-wrap: break-word !important;
          overflow-wrap: break-word !important;
          vertical-align: top !important;
          padding: 4px 6px !important;
          line-height: 1.3 !important;
          max-width: 200px !important;  /* زيادة العرض من 150px إلى 200px */
          min-width: 180px !important;  /* إضافة حد أدنى للعرض */
        }

        /* إزالة أي خلفيات داكنة نهائياً */
        body, html, .print-area {
          background: white !important;
          background-color: white !important;
        }

        /* تأكيد الخلفية البيضاء لجميع العناصر */
        * {
          background: white !important;
          background-color: white !important;
        }

        /* استثناء للتظليل الخفيف فقط */
        .print-attendance-action {
          background: #f8f8f8 !important;
          background-color: #f8f8f8 !important;
        }

        /* ضمان عدم وجود مساحات فارغة */
        .print-area table {
          page-break-inside: auto !important;
        }

        /* تحسين كسر الصفحات التلقائي */

        /* إزالة أي مساحات إضافية */
        .print-area * {
          margin: 0 !important;
          padding-top: 0 !important;
          padding-bottom: 0 !important;
        }

        /* استثناء للخلايا فقط */
        .print-area td, .print-area th {
          padding: 2px 4px !important;
        }

        /* تحسين خاص لعمود الملاحظات */
        .print-area td:nth-child(7), .print-area th:nth-child(7) {
          padding: 3px 5px !important;
        }

        /* تثبيت الهيدر بطريقة بديلة */
        @media print {
          thead {
            display: table-header-group !important;
            background: white !important;
            page-break-inside: avoid !important;
          }

          /* ضمان ظهور الهيدر في كل صفحة */
          thead tr th {
            background: #e8e8e8 !important;
            page-break-inside: avoid !important;
            font-size: 11px !important;
            font-weight: bold !important;
            padding: 5px 6px !important;
            height: 25px !important;
          }

          /* إظهار الهيدر المكرر في الطباعة */
          .repeated-header {
            display: table-row !important;
            page-break-after: avoid !important;
          }

          .page-break-row {
            display: table-row !important;
          }

          /* تحسين كسر الصفحات */
          tbody tr {
            page-break-inside: avoid !important;
          }

          /* تحسين عرض الهيدر الأساسي */
          .print-header {
            page-break-inside: avoid !important;
            page-break-after: avoid !important;
          }
        }
      }

      /* تحسين التباين للوضع العادي */
      .attendance-highlight {
        background: rgba(59, 130, 246, 0.08) !important;
        border-left: 3px solid rgba(59, 130, 246, 0.3) !important;
      }

      .dark .attendance-highlight {
        background: rgba(59, 130, 246, 0.15) !important;
        border-left: 3px solid rgba(59, 130, 246, 0.5) !important;
      }

      .approved-highlight {
        background: rgba(34, 197, 94, 0.08) !important;
        border-left: 3px solid rgba(34, 197, 94, 0.3) !important;
      }

      .dark .approved-highlight {
        background: rgba(34, 197, 94, 0.15) !important;
        border-left: 3px solid rgba(34, 197, 94, 0.5) !important;
      }

      /* تحسين النص للقراءة */
      .attendance-text-highlight {
        font-weight: 600 !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      .dark .attendance-text-highlight {
        text-shadow: 0 1px 2px rgba(255, 255, 255, 0.1);
      }

      tr:hover, tr:focus-within {
        background: #223366 !important;
        color: #fff !important;
      }
      td, th { transition: background 0.2s, color 0.2s; }
      `}</style>
      <style jsx>{`
        @media print {
          .print-table {
            width: 100%;
            border-collapse: collapse;
            direction: rtl;
          }

          .print-table th,
          .print-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: right;
          }

          .print-table th {
            background-color: #f0f0f0 !important;
            font-weight: bold;
          }

          .print-table td {
            background-color: white !important;
            color: black !important;
          }

          /* إخفاء عناصر التحكم عند الطباعة */
          .no-print {
            display: none !important;
          }

          /* إظهار النص بشكل واضح في الطباعة */
          .print-text {
            color: black !important;
          }
        }
      `}</style>

      {/* جدول الطباعة - مخفي في العرض العادي */}
      <div className="hidden print:block">
        <table className="print-table">
          <thead>
            <tr>
              <th>م</th>
              <th>الكود</th>
              <th>الاسم</th>
              <th>المسمى الوظيفي</th>
              <th>التمام</th>
              <th>دخول</th>
              <th>ملاحظات</th>
            </tr>
          </thead>
          <tbody>
            {employees.map((emp, idx) => (
              <tr key={emp.EmployeeID || emp.employeeCode || emp.EmployeeCode}>
                <td>{idx + 1}</td>
                <td>{emp.EmployeeID || emp.employeeCode || emp.EmployeeCode}</td>
                <td>{emp.name || emp.Name || emp.EmployeeName}</td>
                <td>{emp.jobTitle || emp.JobTitle || '-'}</td>
                <td>{emp.attendance || '-'}</td>
                <td>
                  {doesAttendanceRequireTime(emp.attendance)
                    ? formatTime(emp.checkIn) || '-'
                    : '-'}
                </td>
                <td>{emp.notes || '-'}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* جدول العرض العادي - يختفي عند الطباعة */}
      <div className="print:hidden">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          {/* ... الكود الحالي للجدول ... */}
        </table>
      </div>
    </MainLayout>
  );
}

