'use client';

import { useState } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { FiDatabase, FiSave, FiRefreshCw, FiX, FiDownload, FiSettings } from 'react-icons/fi';

export default function FloatingBackupButton() {
  const { isDarkMode } = useTheme();
  const [loading, setLoading] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  // إنشاء نسخة احتياطية سريعة
  const quickBackup = async (backupType = 'createDataBackup') => {
    setLoading(true);
    try {
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
      const backupName = `Quick_Backup_${timestamp}`;

      const response = await fetch('/api/backup-system', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: backupType,
          backupName: backupName
        })
      });

      const result = await response.json();

      if (result.success) {
        const message = result.backupType === 'data_only'
          ? `✅ تم إنشاء النسخة الاحتياطية السريعة بنجاح!\n📊 نوع: تصدير البيانات\n📁 جداول: ${result.tablesBackedUp}\n📝 سجلات: ${result.totalRecords}`
          : '✅ تم إنشاء النسخة الاحتياطية السريعة بنجاح!';

        alert(message);
        setIsExpanded(false);
      } else {
        alert('❌ خطأ: ' + result.error);
      }

    } catch (error) {

      alert('❌ خطأ في إنشاء النسخة الاحتياطية');
    }
    setLoading(false);
  };

  // تصدير البيانات سريع
  const quickExport = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/backup-system', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'exportData' })
      });

      const result = await response.json();
      
      if (result.success) {
        alert('✅ تم تصدير البيانات بنجاح!');
        setIsExpanded(false);
      } else {
        alert('❌ خطأ: ' + result.error);
      }

    } catch (error) {

      alert('❌ خطأ في تصدير البيانات');
    }
    setLoading(false);
  };

  // الانتقال إلى صفحة النسخ الاحتياطي الكاملة
  const goToBackupPage = () => {
    window.location.href = '/backup-system';
    setIsExpanded(false);
  };

  return (
    <div className="fixed bottom-6 left-6 z-50">
      {/* الأزرار المتوسعة */}
      {isExpanded && (
        <div className="mb-4 space-y-3">
          {/* زر إدارة النسخ الاحتياطي */}
          <button
            onClick={goToBackupPage}
            className={`w-12 h-12 rounded-full shadow-lg transition-all duration-300 transform hover:scale-110 ${
              isDarkMode
                ? 'bg-purple-600 hover:bg-purple-700 text-white'
                : 'bg-purple-500 hover:bg-purple-600 text-white'
            }`}
            title="إدارة النسخ الاحتياطي"
          >
            <FiSettings className="w-5 h-5 mx-auto" />
          </button>

          {/* زر تصدير البيانات */}
          <button
            onClick={quickExport}
            disabled={loading}
            className={`w-12 h-12 rounded-full shadow-lg transition-all duration-300 transform hover:scale-110 disabled:opacity-50 ${
              isDarkMode
                ? 'bg-blue-600 hover:bg-blue-700 text-white'
                : 'bg-blue-500 hover:bg-blue-600 text-white'
            }`}
            title="تصدير البيانات"
          >
            {loading ? (
              <FiRefreshCw className="w-5 h-5 mx-auto animate-spin" />
            ) : (
              <FiDownload className="w-5 h-5 mx-auto" />
            )}
          </button>

          {/* زر النسخة الاحتياطية السريعة */}
          <button
            onClick={quickBackup}
            disabled={loading}
            className={`w-12 h-12 rounded-full shadow-lg transition-all duration-300 transform hover:scale-110 disabled:opacity-50 ${
              isDarkMode
                ? 'bg-green-600 hover:bg-green-700 text-white'
                : 'bg-green-500 hover:bg-green-600 text-white'
            }`}
            title="نسخة احتياطية سريعة"
          >
            {loading ? (
              <FiRefreshCw className="w-5 h-5 mx-auto animate-spin" />
            ) : (
              <FiSave className="w-5 h-5 mx-auto" />
            )}
          </button>
        </div>
      )}

      {/* الزر الرئيسي */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className={`w-14 h-14 rounded-full shadow-xl transition-all duration-300 transform hover:scale-110 ${
          isExpanded
            ? isDarkMode
              ? 'bg-red-600 hover:bg-red-700 text-white rotate-45'
              : 'bg-red-500 hover:bg-red-600 text-white rotate-45'
            : isDarkMode
              ? 'bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 hover:from-blue-700 hover:via-blue-800 hover:to-blue-900 text-white'
              : 'bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 hover:from-blue-600 hover:via-blue-700 hover:to-blue-800 text-white'
        }`}
        title={isExpanded ? 'إغلاق' : 'النسخ الاحتياطي'}
      >
        {isExpanded ? (
          <FiX className="w-6 h-6 mx-auto" />
        ) : (
          <FiDatabase className="w-6 h-6 mx-auto" />
        )}
      </button>

      {/* نص توضيحي */}
      {!isExpanded && (
        <div className={`absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 rounded-lg text-xs whitespace-nowrap opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none ${
          isDarkMode ? 'bg-gray-800 text-white' : 'bg-gray-900 text-white'
        }`}>
          النسخ الاحتياطي
        </div>
      )}
    </div>
  );
}
