-- إدراج بيانات تجريبية للطلبات المعتمدة

-- التأكد من وجود جدول PaperRequests
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PaperRequests' AND xtype='U')
BEGIN
    CREATE TABLE PaperRequests (
        ID INT IDENTITY(1,1) PRIMARY KEY,
        RequestType NVARCHAR(50) NOT NULL,
        EmployeeCode NVARCHAR(20) NOT NULL,
        EmployeeName NVARCHAR(100) NOT NULL,
        Department NVARCHAR(100),
        JobTitle NVARCHAR(100),
        LeaveType NVARCHAR(50),
        StartDate DATE,
        EndDate DATE,
        DaysCount INT,
        LeaveReason NVARCHAR(MAX),
        Status NVARCHAR(20) DEFAULT N'قيد المراجعة',
        RequestDate DATETIME DEFAULT GETDATE(),
        ApprovalDate DATETIME,
        RejectionDate DATETIME,
        ApprovedBy NVARCHAR(100),
        ApprovalNotes NVARCHAR(MAX),
        MissionDestination NVARCHAR(200),
        MissionPurpose NVARCHAR(MAX),
        MissionDuration NVARCHAR(50),
        PermissionStartTime NVARCHAR(20),
        PermissionEndTime NVARCHAR(20),
        PermissionDuration NVARCHAR(50),
        PermissionReason NVARCHAR(MAX),
        NightShiftDate DATE,
        NightShiftReason NVARCHAR(MAX),
        CreatedAt DATETIME DEFAULT GETDATE(),
        UpdatedAt DATETIME DEFAULT GETDATE()
    )
END

-- حذف البيانات التجريبية السابقة
DELETE FROM PaperRequests WHERE EmployeeCode IN ('1288', '1414', '1428', '1242', '1426')

-- إدراج طلبات إجازة معتمدة لليوم
INSERT INTO PaperRequests (
    RequestType, EmployeeCode, EmployeeName, Department, JobTitle,
    LeaveType, StartDate, EndDate, DaysCount, LeaveReason,
    Status, RequestDate, ApprovalDate, ApprovedBy, ApprovalNotes
) VALUES 
-- إجازة إعتيادية
('leave', '1288', 'أحمد يوسف السيد', 'مدير منطقة', 'مدير منطقة', 
 'إعتيادية', GETDATE(), GETDATE(), 1, 'ظروف شخصية',
 N'معتمد', GETDATE()-1, GETDATE(), 'مدير الموارد البشرية', 'معتمد'),

-- إجازة عارضة  
('leave', '1414', 'إبراهيم محمد حسن', 'مدير الشئون الإدارية', 'مدير الشئون الإدارية',
 'عارضة', GETDATE(), GETDATE(), 1, 'ظروف طارئة',
 N'معتمد', GETDATE()-1, GETDATE(), 'مدير الموارد البشرية', 'معتمد'),

-- مأمورية
('mission', '1428', 'خالد محمد معروف', 'مدير تنفيذ', 'مدير تنفيذ',
 NULL, GETDATE(), GETDATE(), 1, NULL,
 N'معتمد', GETDATE()-1, GETDATE(), 'مدير المشروع', 'معتمد'),

-- إجازة مرضية
('leave', '1242', 'منذر جميل منذر درويش', 'مدير تنفيذ', 'مدير تنفيذ',
 'مرضية', GETDATE(), GETDATE()+1, 2, 'مرض',
 N'معتمد', GETDATE()-2, GETDATE()-1, 'مدير الموارد البشرية', 'معتمد'),

-- إجازة بدون أجر
('leave', '1426', 'شاهر محمد عبدالعزيز', 'مدير إدارة المشتريات', 'مدير إدارة المشتريات',
 'بدون أجر', GETDATE(), GETDATE(), 1, 'ظروف شخصية',
 N'معتمد', GETDATE()-1, GETDATE(), 'مدير الموارد البشرية', 'معتمد')

-- تحديث بيانات المأمورية
UPDATE PaperRequests 
SET MissionDestination = N'مكتب المقاولات - القاهرة',
    MissionPurpose = N'اجتماع مع العميل لمناقشة تطوير المشروع',
    MissionDuration = N'يوم واحد'
WHERE EmployeeCode = '1428' AND RequestType = 'mission'

-- عرض النتائج
SELECT 
    EmployeeCode,
    EmployeeName,
    RequestType,
    LeaveType,
    StartDate,
    EndDate,
    Status,
    CASE 
        WHEN RequestType = 'leave' AND LeaveType = 'إعتيادية' THEN N'إجازة اعتيادية'
        WHEN RequestType = 'leave' AND LeaveType = 'عارضة' THEN N'إجازة عارضة'
        WHEN RequestType = 'leave' AND LeaveType = 'مرضية' THEN N'إجازة مرضية'
        WHEN RequestType = 'leave' AND LeaveType = 'بدون أجر' THEN N'إجازة بدون أجر'
        WHEN RequestType = 'mission' THEN N'مأمورية'
        ELSE RequestType
    END as AttendanceType,
    CASE 
        WHEN RequestType = 'leave' THEN LeaveReason + N' معتمدة'
        WHEN RequestType = 'mission' THEN N'مأمورية - ' + ISNULL(MissionDestination, N'غير محدد')
        ELSE N'إجراء معتمد'
    END as Notes
FROM PaperRequests 
WHERE Status = N'معتمد' 
    AND GETDATE() BETWEEN StartDate AND ISNULL(EndDate, StartDate)
ORDER BY EmployeeCode
