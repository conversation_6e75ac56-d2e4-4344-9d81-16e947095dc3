import { NextResponse } from 'next/server';
import sql from 'mssql';
import ExcelJS from 'exceljs';

// إعدادات قاعدة البيانات
const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  server: process.env.DB_SERVER,
  database: process.env.DB_NAME,
  options: {
    encrypt: true,
    trustServerCertificate: true,
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
};

let pool;

export async function GET() {
  try {

    // الاتصال بقاعدة البيانات
    if (!pool) {

      pool = await sql.connect(dbConfig);

    }

    // استعلام لجلب جميع بيانات الموظفين (بأسماء الأعمدة الصحيحة من قاعدة البيانات)
    const query = `
      SELECT
        EmployeeCode,
        EmployeeName,
        JobTitle,
        Department,
        direct as DirectManager,
        NationalID,
        FORMAT(BirthDate, 'yyyy-MM-dd') as BirthDate,
        Gender,
        Governorate,
        area as Area,
        MaritalStatus,
        Mobile,
        email as Email,
        emrnum as EmergencyNumber,
        Kinship,
        FORMAT(HireDate, 'yyyy-MM-dd') as HireDate,
        FORMAT(JoinDate, 'yyyy-MM-dd') as JoinDate,
        CurrentStatus,
        Mserv as MilitaryService,
        IsResidentEmployee,
        Education,
        University,
        Major,
        Grade,
        Batch,
        SocialInsurance,
        SocialInsureNum as SocialInsuranceNumber,
        spcialInsDate as SocialInsuranceDate,
        MedicalInsurance,
        MedicalInsuranceNum as MedicalInsuranceNumber
      FROM Employees
      WHERE CurrentStatus != 'مستقيل' AND CurrentStatus != 'منقول'
      ORDER BY EmployeeCode
    `;

    const result = await pool.request().query(query);
    const employees = result.recordset;

    // إنشاء ملف Excel
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('ملف التحديث - البيانات المسجلة');

    // تعريف الأعمدة
    const columns = [
      { header: 'كود الموظف*', key: 'EmployeeCode', width: 15 },
      { header: 'الاسم الكامل*', key: 'EmployeeName', width: 25 },
      { header: 'المسمى الوظيفي*', key: 'JobTitle', width: 20 },
      { header: 'القسم*', key: 'Department', width: 20 },
      { header: 'المدير المباشر', key: 'DirectManager', width: 20 },
      { header: 'الرقم القومي*', key: 'NationalID', width: 18 },
      { header: 'تاريخ الميلاد*', key: 'BirthDate', width: 15 },
      { header: 'النوع*', key: 'Gender', width: 10 },
      { header: 'المحافظة*', key: 'Governorate', width: 15 },
      { header: 'المنطقة', key: 'Area', width: 15 },
      { header: 'الحالة الاجتماعية*', key: 'MaritalStatus', width: 15 },
      { header: 'رقم الجوال', key: 'Mobile', width: 15 },
      { header: 'البريد الإلكتروني', key: 'Email', width: 25 },
      { header: 'رقم الطوارئ', key: 'EmergencyNumber', width: 15 },
      { header: 'صلة القرابة', key: 'Kinship', width: 15 },
      { header: 'تاريخ التعيين', key: 'HireDate', width: 15 },
      { header: 'تاريخ الانضمام*', key: 'JoinDate', width: 15 },
      { header: 'حالة الموظف*', key: 'CurrentStatus', width: 15 },
      { header: 'الخدمة العسكرية', key: 'MilitaryService', width: 15 },
      { header: 'مغترب*', key: 'IsResidentEmployee', width: 10 },
      { header: 'المؤهل', key: 'Education', width: 15 },
      { header: 'الجامعة', key: 'University', width: 20 },
      { header: 'التخصص', key: 'Major', width: 20 },
      { header: 'التقدير', key: 'Grade', width: 15 },
      { header: 'سنة التخرج', key: 'Batch', width: 15 },
      { header: 'التأمين الاجتماعي', key: 'SocialInsurance', width: 15 },
      { header: 'رقم التأمين الاجتماعي', key: 'SocialInsuranceNumber', width: 20 },
      { header: 'تاريخ التأمين الاجتماعي', key: 'SocialInsuranceDate', width: 20 },
      { header: 'التأمين الطبي', key: 'MedicalInsurance', width: 15 },
      { header: 'رقم التأمين الطبي', key: 'MedicalInsuranceNumber', width: 20 }
    ];

    worksheet.columns = columns;

    // تنسيق رأس الجدول
    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '4472C4' }
    };
    headerRow.alignment = { horizontal: 'center', vertical: 'middle' };

    // إضافة البيانات
    employees.forEach((employee, index) => {
      const row = worksheet.addRow(employee);

      // تلوين الصفوف بالتناوب
      if (index % 2 === 0) {
        row.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'F2F2F2' }
        };
      }

      // محاذاة النص
      row.alignment = { horizontal: 'center', vertical: 'middle' };
    });

    // إضافة حدود للجدول
    worksheet.eachRow((row, rowNumber) => {
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });
    });

    // إضافة ملاحظات في أعلى الملف
    worksheet.insertRow(1, []);
    worksheet.insertRow(1, ['ملاحظات مهمة:']);
    worksheet.insertRow(2, ['• هذا ملف التحديث يحتوي على البيانات المسجلة حالياً في النظام']);
    worksheet.insertRow(3, ['• يمكنك تعديل أي حقل أو إضافة بيانات غير مسجلة']);
    worksheet.insertRow(4, ['• الحقول الفارغة في الملف سيتم تجاهلها (الاحتفاظ بالقيم الموجودة)']);
    worksheet.insertRow(5, ['• الحقول المملوءة سيتم تحديثها']);
    worksheet.insertRow(6, ['• الحقول المطلوبة مميزة بعلامة (*)']);
    worksheet.insertRow(7, []);

    // تنسيق الملاحظات
    for (let i = 1; i <= 6; i++) {
      const noteRow = worksheet.getRow(i);
      noteRow.font = { bold: true, color: { argb: '0066CC' } };
      if (i === 1) {
        noteRow.font.size = 14;
        noteRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'E6F3FF' }
        };
      }
    }

    // تحويل الملف إلى buffer
    const buffer = await workbook.xlsx.writeBuffer();

    // إنشاء اسم الملف مع التاريخ
    const currentDate = new Date().toISOString().split('T')[0];
    const filename = `ملف_التحديث_البيانات_المسجلة_${currentDate}.xlsx`;

    // إرجاع الملف
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="${encodeURIComponent(filename)}"`,
        'Content-Length': buffer.length.toString(),
      },
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'خطأ في تصدير ملف التحديث',
      error: error.message
    }, { status: 500 });
  }
}
