/**
 * دالة تنسيق التاريخ بصيغة dd/mm/yyyy
 * @param {string|Date} dateInput - التاريخ المراد تنسيقه
 * @returns {string} التاريخ بصيغة dd/mm/yyyy
 */
export function formatDateToDDMMYYYY(dateInput) {
  if (!dateInput) return '';
  
  const date = new Date(dateInput);
  if (isNaN(date.getTime())) return '';
  
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  
  return `${day}/${month}/${year}`;
}

/**
 * دالة تنسيق التاريخ والوقت بصيغة dd/mm/yyyy HH:mm
 * @param {string|Date} dateInput - التاريخ والوقت المراد تنسيقه
 * @returns {string} التاريخ والوقت بصيغة dd/mm/yyyy HH:mm
 */
export function formatDateTimeToDDMMYYYY(dateInput) {
  if (!dateInput) return '';
  
  const date = new Date(dateInput);
  if (isNaN(date.getTime())) return '';
  
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  
  return `${day}/${month}/${year} ${hours}:${minutes}`;
}

/**
 * دالة تحويل التاريخ من صيغة dd/mm/yyyy إلى yyyy-mm-dd
 * @param {string} dateString - التاريخ بصيغة dd/mm/yyyy
 * @returns {string} التاريخ بصيغة yyyy-mm-dd
 */
export function convertDDMMYYYYToISO(dateString) {
  if (!dateString || !dateString.includes('/')) return '';
  
  const parts = dateString.split('/');
  if (parts.length !== 3) return '';
  
  const day = parts[0].padStart(2, '0');
  const month = parts[1].padStart(2, '0');
  const year = parts[2];
  
  return `${year}-${month}-${day}`;
}

/**
 * دالة التحقق من صحة التاريخ بصيغة dd/mm/yyyy
 * @param {string} dateString - التاريخ المراد التحقق منه
 * @returns {boolean} true إذا كان التاريخ صحيح
 */
export function isValidDDMMYYYYDate(dateString) {
  if (!dateString || !dateString.includes('/')) return false;
  
  const parts = dateString.split('/');
  if (parts.length !== 3) return false;
  
  const day = parseInt(parts[0]);
  const month = parseInt(parts[1]);
  const year = parseInt(parts[2]);
  
  if (day < 1 || day > 31) return false;
  if (month < 1 || month > 12) return false;
  if (year < 1900 || year > 2100) return false;
  
  // التحقق من صحة التاريخ
  const date = new Date(year, month - 1, day);
  return date.getFullYear() === year && 
         date.getMonth() === month - 1 && 
         date.getDate() === day;
}

/**
 * دالة الحصول على التاريخ الحالي بصيغة dd/mm/yyyy
 * @returns {string} التاريخ الحالي بصيغة dd/mm/yyyy
 */
export function getCurrentDateDDMMYYYY() {
  return formatDateToDDMMYYYY(new Date());
}

/**
 * دالة إضافة أيام لتاريخ معين
 * @param {string} dateString - التاريخ بصيغة dd/mm/yyyy
 * @param {number} days - عدد الأيام المراد إضافتها
 * @returns {string} التاريخ الجديد بصيغة dd/mm/yyyy
 */
export function addDaysToDate(dateString, days) {
  if (!dateString) return '';
  
  const isoDate = convertDDMMYYYYToISO(dateString);
  if (!isoDate) return '';
  
  const date = new Date(isoDate);
  date.setDate(date.getDate() + days);
  
  return formatDateToDDMMYYYY(date);
}

/**
 * دالة حساب الفرق بين تاريخين بالأيام
 * @param {string} startDate - تاريخ البداية بصيغة dd/mm/yyyy
 * @param {string} endDate - تاريخ النهاية بصيغة dd/mm/yyyy
 * @returns {number} عدد الأيام بين التاريخين
 */
export function getDaysDifference(startDate, endDate) {
  if (!startDate || !endDate) return 0;
  
  const startISO = convertDDMMYYYYToISO(startDate);
  const endISO = convertDDMMYYYYToISO(endDate);
  
  if (!startISO || !endISO) return 0;
  
  const start = new Date(startISO);
  const end = new Date(endISO);
  
  const diffTime = Math.abs(end - start);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return diffDays;
}
