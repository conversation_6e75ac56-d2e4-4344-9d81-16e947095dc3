// نموذج طلب الإجازة المطابق للنموذج الأصلي
export const generateLeaveFormHTML = (formData, logoBase64) => {
  const formatDate = (date) => {
    if (!date) return '';
    return new Date(date).toLocaleDateString('ar-EG');
  };

  return `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>طلب إجازة</title>
  <style>
    @page {
      size: A4;
      margin: 20mm;
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Arial', sans-serif;
      font-size: 14px;
      line-height: 1.4;
      color: #000;
      direction: rtl;
      text-align: right;
    }
    
    /* Header Section */
    .header {
      border: 3px solid #000;
      margin-bottom: 20px;
      height: 90px;
      display: flex;
      align-items: stretch;
    }
    
    .logo-section {
      width: 100px;
      border-left: 2px solid #000;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10px;
    }
    
    .logo-img {
      max-width: 80px;
      max-height: 70px;
      object-fit: contain;
    }
    
    .form-title-section {
      width: 180px;
      border-left: 2px solid #000;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 10px;
      text-align: center;
    }
    
    .form-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 8px;
    }
    
    .form-code {
      font-size: 12px;
      color: #666;
      font-weight: bold;
    }
    
    .company-section {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 15px;
      text-align: center;
    }
    
    .company-name-ar {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 8px;
    }
    
    .company-name-en {
      font-size: 14px;
      color: #666;
      font-style: italic;
    }
    
    /* Form Body */
    .form-body {
      margin-top: 25px;
    }
    
    .section-title {
      background-color: #e6f3ff;
      border: 2px solid #000;
      text-align: center;
      padding: 10px;
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 20px;
    }
    
    .form-row {
      display: flex;
      align-items: center;
      margin-bottom: 18px;
      min-height: 35px;
    }
    
    .field-label {
      font-weight: bold;
      margin-left: 15px;
      min-width: 120px;
      font-size: 14px;
    }
    
    .field-value {
      border-bottom: 2px solid #000;
      padding: 8px 15px;
      min-width: 200px;
      text-align: center;
      flex: 1;
      font-size: 14px;
    }
    
    .date-row {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 25px;
    }
    
    .employee-info {
      margin-bottom: 30px;
    }
    
    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 18px;
    }
    
    .info-group {
      display: flex;
      align-items: center;
      gap: 15px;
    }
    
    /* Leave Type Section */
    .leave-type-container {
      border: 2px solid #000;
      margin: 25px 0;
      padding: 20px;
    }
    
    .leave-type-header {
      text-align: center;
      font-weight: bold;
      font-size: 16px;
      margin-bottom: 20px;
      text-decoration: underline;
    }
    
    .leave-options-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-bottom: 20px;
    }
    
    .leave-option {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 12px;
    }
    
    .checkbox {
      width: 20px;
      height: 20px;
      border: 2px solid #000;
      display: inline-block;
      text-align: center;
      line-height: 16px;
      font-weight: bold;
    }
    
    .checkbox.checked {
      background: #000;
      color: white;
    }
    
    .leave-dates {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 20px;
      padding-top: 15px;
      border-top: 1px solid #000;
    }
    
    /* Reason Section */
    .reason-section {
      margin: 25px 0;
    }
    
    .reason-title {
      background-color: #e6f3ff;
      border: 2px solid #000;
      text-align: center;
      padding: 8px;
      font-weight: bold;
      font-size: 14px;
      margin-bottom: 0;
    }
    
    .reason-box {
      border: 2px solid #000;
      border-top: none;
      min-height: 120px;
      padding: 15px;
      line-height: 1.6;
    }
    
    /* Signatures */
    .signatures-container {
      margin-top: 40px;
    }
    
    .signatures-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 30px;
    }
    
    .signature-box {
      text-align: center;
      width: 200px;
    }
    
    .signature-title {
      font-weight: bold;
      margin-bottom: 15px;
      font-size: 12px;
    }
    
    .signature-line {
      border-bottom: 2px solid #000;
      height: 50px;
      margin-bottom: 10px;
    }
    
    .hr-section {
      margin-top: 40px;
      border: 2px solid #000;
    }
    
    .hr-header {
      background-color: #e6f3ff;
      text-align: center;
      padding: 10px;
      font-weight: bold;
      font-size: 14px;
      border-bottom: 2px solid #000;
    }
    
    .hr-content {
      padding: 20px;
    }
    
    .notes-section {
      margin-bottom: 20px;
    }
    
    .notes-lines {
      line-height: 2;
      margin-bottom: 20px;
    }
    
    .footer-notes {
      border-top: 1px solid #000;
      padding-top: 15px;
      font-size: 12px;
      line-height: 1.6;
    }
    
    @media print {
      body { -webkit-print-color-adjust: exact; }
      .no-print { display: none; }
    }
  </style>
</head>
<body>
  <!-- Header -->
  <div class="header">
    <div class="logo-section">
      ${logoBase64 ? 
        `<img src="data:image/png;base64,${logoBase64}" alt="Company Logo" class="logo-img" />` : 
        `<div style="background:#1e40af;color:white;padding:15px;text-align:center;font-weight:bold;font-size:12px;border-radius:8px;">
          CONCORD<br><span style="font-size:10px;">COMPANY</span>
         </div>`
      }
    </div>
    <div class="form-title-section">
      <div class="form-title">طلب إجازة</div>
      <div class="form-code">HR-OP-01-F01</div>
    </div>
    <div class="company-section">
      <div class="company-name-ar">شركة كونكورد للهندسة والمقاولات</div>
      <div class="company-name-en">Concord for Engineering & Contracting</div>
    </div>
  </div>

  <div class="form-body">
    <!-- Date -->
    <div class="date-row">
      <div class="info-group">
        <span class="field-label">التاريخ:</span>
        <div class="field-value" style="width: 150px;">
          ${formatDate(new Date())}
        </div>
      </div>
    </div>

    <!-- Section Title -->
    <div class="section-title">بيانات الطلب</div>
    
    <!-- Employee Information -->
    <div class="employee-info">
      <div class="info-row">
        <div class="info-group">
          <span class="field-label">كود الموظف:</span>
          <div class="field-value" style="width: 120px;">
            ${formData.employeeId || ''}
          </div>
        </div>
        <div class="info-group" style="flex: 1; margin: 0 20px;">
          <span class="field-label">الاسم:</span>
          <div class="field-value">
            ${formData.employeeName || ''}
          </div>
        </div>
      </div>

      <div class="info-row">
        <div class="info-group" style="flex: 1; margin-left: 20px;">
          <span class="field-label">القسم:</span>
          <div class="field-value">
            ${formData.department || ''}
          </div>
        </div>
        <div class="info-group" style="flex: 1;">
          <span class="field-label">الوظيفة:</span>
          <div class="field-value">
            ${formData.jobTitle || ''}
          </div>
        </div>
      </div>

      <div class="form-row">
        <span class="field-label">تاريخ آخر إجازة مسجلة:</span>
        <div class="field-value" style="width: 150px;">
          ${formData.lastLeaveDate || ''}
        </div>
      </div>
    </div>

    <!-- Leave Type Section -->
    <div class="leave-type-container">
      <div class="leave-type-header">نوع الإجازة والرصيد المتبقي</div>
      
      <div class="leave-options-grid">
        <div>
          <div class="leave-option">
            <span class="checkbox ${formData.leaveType === 'annual' ? 'checked' : ''}">
              ${formData.leaveType === 'annual' ? '✓' : ''}
            </span>
            <span>إجازة اعتيادية</span>
          </div>
          <div class="leave-option">
            <span class="checkbox ${formData.leaveType === 'sick' ? 'checked' : ''}">
              ${formData.leaveType === 'sick' ? '✓' : ''}
            </span>
            <span>إجازة مرضية</span>
          </div>
        </div>
        <div>
          <div class="leave-option">
            <span class="checkbox ${formData.leaveType === 'emergency' ? 'checked' : ''}">
              ${formData.leaveType === 'emergency' ? '✓' : ''}
            </span>
            <span>إجازة عارضة</span>
          </div>
          <div class="leave-option">
            <span class="checkbox ${formData.leaveType === 'compensatory' ? 'checked' : ''}">
              ${formData.leaveType === 'compensatory' ? '✓' : ''}
            </span>
            <span>إجازة بدل</span>
          </div>
        </div>
      </div>

      <div class="leave-dates">
        <div class="info-group">
          <span class="field-label">من تاريخ:</span>
          <div class="field-value" style="width: 120px;">
            ${formatDate(formData.startDate)}
          </div>
        </div>
        <div class="info-group">
          <span class="field-label">إلى تاريخ:</span>
          <div class="field-value" style="width: 120px;">
            ${formatDate(formData.endDate)}
          </div>
        </div>
        <div class="info-group">
          <span class="field-label">عدد الأيام:</span>
          <div class="field-value" style="width: 80px;">
            ${formData.totalDays || ''} يوم
          </div>
        </div>
      </div>
    </div>

    <!-- Reason Section -->
    <div class="reason-section">
      <div class="reason-title">سبب طلب الإجازة</div>
      <div class="reason-box">
        ${formData.reason || ''}
      </div>
    </div>

    <!-- Signatures -->
    <div class="signatures-container">
      <div class="signatures-row">
        <div class="signature-box">
          <div class="signature-title">توقيع الموظف</div>
          <div class="signature-line"></div>
        </div>
        <div class="signature-box">
          <div class="signature-title">اعتماد الرئيس المباشر</div>
          <div class="signature-line"></div>
        </div>
      </div>

      <div class="signatures-row">
        <div class="signature-box">
          <div class="signature-title">المدير الإداري</div>
          <div class="signature-line"></div>
        </div>
        <div class="signature-box">
          <div class="signature-title">اعتماد مدير المشروع</div>
          <div class="signature-line"></div>
        </div>
      </div>

      <div style="text-align: center; margin-top: 20px;">
        <div class="signature-title">اعتماد مدير الموارد البشرية</div>
        <div class="signature-line" style="width: 250px; margin: 20px auto;"></div>
      </div>
    </div>

    <!-- HR Operations Section -->
    <div class="hr-section">
      <div class="hr-header">عمليات الموارد البشرية</div>
      <div class="hr-content">
        <div class="notes-section">
          <div style="display: flex; justify-content: space-between; align-items: flex-start;">
            <div style="flex: 1;">
              <div class="notes-lines">
                ................................................................................................................................................................................................<br>
                ................................................................................................................................................................................................<br>
                ................................................................................................................................................................................................
              </div>
            </div>
            <div style="font-weight: bold; margin-right: 20px;">
              ملاحظات:
            </div>
          </div>

          <div style="text-align: center; margin: 25px 0;">
            <div style="font-weight: bold; margin-bottom: 15px;">اختصاصي عمليات الموارد البشرية</div>
            <div style="border-bottom: 2px solid #000; width: 250px; margin: 0 auto; height: 40px;"></div>
          </div>
        </div>

        <div class="footer-notes">
          <div style="margin-bottom: 10px;">
            • في حالة الإجازة المرضية يتم إرفاق التقرير الطبي.
          </div>
          <div>
            • في حالة عدم وجود رصيد إجازات سنوية يتم احتساب الطلب إجازة بدون أجر.
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html>`;
};
