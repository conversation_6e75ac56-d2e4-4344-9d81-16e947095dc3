import { NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

// خدمة الملفات - لفتح طلبات الإصدار
export async function GET(request, { params }) {
  try {
    const { path } = params;
    const filePath = Array.isArray(path) ? path.join('/') : path;

    // تحديد المسار الكامل للملف
    const fullPath = join(process.cwd(), filePath);
    
    // التحقق من وجود الملف
    if (!existsSync(fullPath)) {

      // إنشاء ملف PDF تجريبي للعرض
      const mockPdfContent = generateMockPDF(filePath);
      
      return new NextResponse(mockPdfContent, {
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `inline; filename="${filePath.split('/').pop()}"`,
        },
      });
    }

    // قراءة الملف
    const fileBuffer = await readFile(fullPath);
    const fileExtension = filePath.split('.').pop()?.toLowerCase();
    
    // تحديد نوع المحتوى
    let contentType = 'application/octet-stream';
    switch (fileExtension) {
      case 'pdf':
        contentType = 'application/pdf';
        break;
      case 'jpg':
      case 'jpeg':
        contentType = 'image/jpeg';
        break;
      case 'png':
        contentType = 'image/png';
        break;
      case 'txt':
        contentType = 'text/plain';
        break;
      case 'doc':
        contentType = 'application/msword';
        break;
      case 'docx':
        contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
        break;
    }

    return new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `inline; filename="${filePath.split('/').pop()}"`,
      },
    });

  } catch (error) {

    // إنشاء ملف PDF تجريبي في حالة الخطأ
    const mockPdfContent = generateMockPDF(params.path?.join('/') || 'unknown');
    
    return new NextResponse(mockPdfContent, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': 'inline; filename="version-request.pdf"',
      },
    });
  }
}

// إنشاء محتوى PDF تجريبي
function generateMockPDF(filePath) {
  // استخراج معلومات من مسار الملف
  const fileName = filePath.split('/').pop() || 'unknown';
  const pathParts = fileName.split('-');
  const month = pathParts[0] || '1';
  const year = pathParts[1]?.replace('.pdf', '') || '2025';
  
  const monthNames = {
    '1': 'يناير', '2': 'فبراير', '3': 'مارس', '4': 'أبريل',
    '5': 'مايو', '6': 'يونيو', '7': 'يوليو', '8': 'أغسطس',
    '9': 'سبتمبر', '10': 'أكتوبر', '11': 'نوفمبر', '12': 'ديسمبر'
  };
  
  const monthName = monthNames[month] || 'غير محدد';
  
  // محتوى PDF بسيط (في الواقع سيكون محتوى PDF حقيقي)
  const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
/Resources <<
/Font <<
/F1 5 0 R
>>
>>
>>
endobj

4 0 obj
<<
/Length 200
>>
stream
BT
/F1 24 Tf
100 700 Td
(طلب إصدار تكاليف السيارات) Tj
0 -50 Td
/F1 18 Tf
(الشهر: ${monthName}) Tj
0 -30 Td
(السنة: ${year}) Tj
0 -50 Td
/F1 14 Tf
(هذا ملف تجريبي لعرض طلب الإصدار) Tj
0 -30 Td
(المسار: ${filePath}) Tj
ET
endstream
endobj

5 0 obj
<<
/Type /Font
/Subtype /Type1
/BaseFont /Helvetica
>>
endobj

xref
0 6
0000000000 65535 f 
0000000010 00000 n 
0000000079 00000 n 
0000000136 00000 n 
0000000271 00000 n 
0000000524 00000 n 
trailer
<<
/Size 6
/Root 1 0 R
>>
startxref
593
%%EOF`;

  return Buffer.from(pdfContent, 'utf-8');
}
