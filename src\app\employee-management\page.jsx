'use client';
import React from 'react';

import { useUpload } from '@/app/utilities/runtime-helpers';

function MainComponent() {
  const [selectedLang, setSelectedLang] = useState('ar');
  const [employees, setEmployees] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [activeTab, setActiveTab] = useState('basic');
  const [searchParams, setSearchParams] = useState({
    search: '',
    filters: {
      department: '',
      job_title: '',
      area: '',
      status: '',
    },
  });
  const [upload, { loading: uploading }] = useUpload();

  const dir = selectedLang === 'ar' ? 'rtl' : 'ltr';

  useEffect(() => {
    fetchEmployees();
  }, [searchParams]);

  const fetchEmployees = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/employee-data', {
        method: 'POST',
        body: JSON.stringify({
          action: 'list',
          data: searchParams,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch employees');
      }

      const result = await response.json();
      if (result.success) {
        setEmployees(result.data);
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      setError(error.message);

    } finally {
      setLoading(false);
    }
  };

  const handleDocumentUpload = async (file, type) => {
    try {
      const { url, error } = await upload({ file });
      if (error) throw new Error(error);

      await fetch('/api/employee-data', {
        method: 'POST',
        body: JSON.stringify({
          action: 'update',
          employeeId: selectedEmployee.employee_id,
          data: {
            documents: {
              ...selectedEmployee.documents,
              [type]: url,
            },
          },
        }),
      });

      setSelectedEmployee((prev) => ({
        ...prev,
        documents: {
          ...prev.documents,
          [type]: url,
        },
      }));
    } catch (error) {
      setError(error.message);
    }
  };

  return (
    <div
      dir={dir}
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          {selectedLang === 'ar' ? 'إدارة الموظفين' : 'Employee Management'}
        </h1>
        <button
          onClick={() => setSelectedLang(selectedLang === 'ar' ? 'en' : 'ar')}
          className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
        >
          {selectedLang === 'ar' ? 'English' : 'العربية'}
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-1 bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
          <div className="space-y-4">
            <input
              type="text"
              value={searchParams.search}
              onChange={(e) =>
                setSearchParams((prev) => ({ ...prev, search: e.target.value }))
              }
              placeholder={selectedLang === 'ar' ? 'بحث...' : 'Search...'}
              className="w-full p-2 border rounded-md"
              name="search"
            />

            <select
              value={searchParams.filters.department}
              onChange={(e) =>
                setSearchParams((prev) => ({
                  ...prev,
                  filters: { ...prev.filters, department: e.target.value },
                }))
              }
              className="w-full p-2 border rounded-md"
              name="department"
            >
              <option value="">
                {selectedLang === 'ar' ? 'القسم' : 'Department'}
              </option>
              <option value="IT">IT</option>
              <option value="HR">HR</option>
            </select>

            <select
              value={searchParams.filters.area}
              onChange={(e) =>
                setSearchParams((prev) => ({
                  ...prev,
                  filters: { ...prev.filters, area: e.target.value },
                }))
              }
              className="w-full p-2 border rounded-md"
              name="area"
            >
              <option value="">
                {selectedLang === 'ar' ? 'المنطقة' : 'Area'}
              </option>
              <option value="Cairo">Cairo</option>
              <option value="Alexandria">Alexandria</option>
            </select>

            <select
              value={searchParams.filters.status}
              onChange={(e) =>
                setSearchParams((prev) => ({
                  ...prev,
                  filters: { ...prev.filters, status: e.target.value },
                }))
              }
              className="w-full p-2 border rounded-md"
              name="status"
            >
              <option value="">
                {selectedLang === 'ar' ? 'الحالة' : 'Status'}
              </option>
              <option value="active">
                {selectedLang === 'ar' ? 'نشط' : 'Active'}
              </option>
              <option value="inactive">
                {selectedLang === 'ar' ? 'غير نشط' : 'Inactive'}
              </option>
            </select>
          </div>
        </div>

        <div className="lg:col-span-3">
          <div className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-4 py-2 text-right">
                      {selectedLang === 'ar' ? 'الكود' : 'Code'}
                    </th>
                    <th className="px-4 py-2 text-right">
                      {selectedLang === 'ar' ? 'الاسم' : 'Name'}
                    </th>
                    <th className="px-4 py-2 text-right">
                      {selectedLang === 'ar' ? 'القسم' : 'Department'}
                    </th>
                    <th className="px-4 py-2 text-right">
                      {selectedLang === 'ar' ? 'المنطقة' : 'Area'}
                    </th>
                    <th className="px-4 py-2 text-right">
                      {selectedLang === 'ar' ? 'الحالة' : 'Status'}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {loading ? (
                    <tr>
                      <td colSpan="5" className="text-center py-4">
                        {selectedLang === 'ar'
                          ? 'جاري التحميل...'
                          : 'Loading...'}
                      </td>
                    </tr>
                  ) : (
                    employees.map((emp) => (
                      <tr
                        key={emp.employee_id}
                        className="border-b cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700"
                        onClick={() => setSelectedEmployee(emp)}
                      >
                        <td className="px-4 py-2">{emp.code}</td>
                        <td className="px-4 py-2">{emp.employee_name}</td>
                        <td className="px-4 py-2">{emp.department}</td>
                        <td className="px-4 py-2">{emp.area}</td>
                        <td className="px-4 py-2">{emp.status}</td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {selectedEmployee && (
            <div className="mt-6 bg-white dark:bg-gray-800 rounded-lg p-4">
              <div className="flex gap-4 mb-4">
                {['basic', 'personal', 'work', 'documents'].map((tab) => (
                  <button
                    key={tab}
                    onClick={() => setActiveTab(tab)}
                    className={`px-4 py-2 rounded-md ${
                      activeTab === tab
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 dark:bg-gray-700'
                    }`}
                  >
                    {selectedLang === 'ar'
                      ? {
                          basic: 'معلومات أساسية',
                          personal: 'معلومات شخصية',
                          work: 'معلومات العمل',
                          documents: 'المستندات',
                        }[tab]
                      : {
                          basic: 'Basic Info',
                          personal: 'Personal Info',
                          work: 'Work Info',
                          documents: 'Documents',
                        }[tab]}
                  </button>
                ))}
              </div>

              {activeTab === 'documents' ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {[
                    'national_id',
                    'work_start',
                    'marital_status',
                    'subsistence',
                    'transportation',
                    'resignation',
                  ].map((docType) => (
                    <div key={docType} className="p-4 border rounded-lg">
                      <h3 className="font-bold mb-2">
                        {selectedLang === 'ar'
                          ? {
                              national_id: 'بطاقة الهوية',
                              work_start: 'طلب بدء العمل',
                              marital_status: 'الحالة الاجتماعية',
                              subsistence: 'بدل المعيشة',
                              transportation: 'بدل المواصلات',
                              resignation: 'الاستقالة',
                            }[docType]
                          : {
                              national_id: 'National ID',
                              work_start: 'Work Start Request',
                              marital_status: 'Marital Status',
                              subsistence: 'Subsistence Allowance',
                              transportation: 'Transportation Allowance',
                              resignation: 'Resignation',
                            }[docType]}
                      </h3>
                      <input
                        type="file"
                        onChange={(e) => {
                          if (e.target.files) {
                            handleDocumentUpload(e.target.files[0], docType);
                          }
                        }}
                        className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                        name={docType}
                      />
                      {selectedEmployee.documents?.[docType] && (
                        <a
                          href={selectedEmployee.documents[docType]}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline mt-2 block"
                        >
                          {selectedLang === 'ar'
                            ? 'عرض المستند'
                            : 'View Document'}
                        </a>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {activeTab === 'basic' && (
                    <>
                      <div className="p-2">
                        <strong>
                          {selectedLang === 'ar' ? 'الكود:' : 'Code:'}
                        </strong>{' '}
                        {selectedEmployee.code}
                      </div>
                      <div className="p-2">
                        <strong>
                          {selectedLang === 'ar' ? 'الاسم:' : 'Name:'}
                        </strong>{' '}
                        {selectedEmployee.employee_name}
                      </div>
                      <div className="p-2">
                        <strong>
                          {selectedLang === 'ar' ? 'القسم:' : 'Department:'}
                        </strong>{' '}
                        {selectedEmployee.department}
                      </div>
                      <div className="p-2">
                        <strong>
                          {selectedLang === 'ar' ? 'المنطقة:' : 'Area:'}
                        </strong>{' '}
                        {selectedEmployee.area}
                      </div>
                    </>
                  )}

                  {activeTab === 'personal' && (
                    <>
                      <div className="p-2">
                        <strong>
                          {selectedLang === 'ar'
                            ? 'تاريخ الميلاد:'
                            : 'Birth Date:'}
                        </strong>{' '}
                        {selectedEmployee.birth_date}
                      </div>
                      <div className="p-2">
                        <strong>
                          {selectedLang === 'ar'
                            ? 'الرقم القومي:'
                            : 'National ID:'}
                        </strong>{' '}
                        {selectedEmployee.national_id}
                      </div>
                      <div className="p-2">
                        <strong>
                          {selectedLang === 'ar' ? 'رقم الهاتف:' : 'Phone:'}
                        </strong>{' '}
                        {selectedEmployee.phone_number}
                      </div>
                      <div className="p-2">
                        <strong>
                          {selectedLang === 'ar' ? 'المؤهل:' : 'Education:'}
                        </strong>{' '}
                        {selectedEmployee.education_level}
                      </div>
                    </>
                  )}

                  {activeTab === 'work' && (
                    <>
                      <div className="p-2">
                        <strong>
                          {selectedLang === 'ar'
                            ? 'المسمى الوظيفي:'
                            : 'Job Title:'}
                        </strong>{' '}
                        {selectedEmployee.job_title}
                      </div>
                      <div className="p-2">
                        <strong>
                          {selectedLang === 'ar' ? 'خط السير:' : 'Bus Line:'}
                        </strong>{' '}
                        {selectedEmployee.bus_line}
                      </div>
                      <div className="p-2">
                        <strong>
                          {selectedLang === 'ar' ? 'الحالة:' : 'Status:'}
                        </strong>{' '}
                        {selectedEmployee.status}
                      </div>
                    </>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {error && (
        <div className="fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}
    </div>
  );
}

export default MainComponent;
