'use client';
import React, { useState } from 'react';

import { useUpload } from '@/app/utilities/runtime-helpers';

function MainComponent() {
  const [selectedLang, setSelectedLang] = useState('ar');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [upload, { loading: uploadLoading }] = useUpload();

  const [formData, setFormData] = useState({
    personalInfo: {
      firstName: '',
      lastName: '',
      dateOfBirth: '',
      nationality: '',
      idNumber: '',
      passportNumber: '',
      phone: '',
      email: '',
      address: '',
      gender: 'male',
    },
    employmentInfo: {
      employeeId: '',
      department: '',
      position: '',
      startDate: '',
      salary: '',
      contractType: 'full-time',
      bankName: '',
      bankAccount: '',
      workLocation: '',
    },
    documents: {
      idCard: null,
      passport: null,
      contract: null,
      photo: null,
    },
  });

  const dir = selectedLang === 'ar' ? 'rtl' : 'ltr';

  const handleInputChange = (section, field, value) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));
  };

  const handleFileUpload = async (section, field, file) => {
    try {
      const { url, error: uploadError } = await upload({ file });
      if (uploadError) throw new Error(uploadError);

      handleInputChange(section, field, url);
    } catch (err) {
      setError(
        selectedLang === 'ar'
          ? 'حدث خطأ أثناء رفع الملف'
          : 'Error uploading file'
      );

    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess(false);

    try {
      const response = await fetch('/api/data-service', {
        method: 'POST',
        body: JSON.stringify({
          table: 'employees',
          action: 'create',
          data: formData,
        }),
      });

      if (!response.ok) {
        throw new Error(
          selectedLang === 'ar'
            ? 'حدث خطأ أثناء إضافة الموظف'
            : 'Error adding employee'
        );
      }

      setSuccess(true);
      setFormData({
        personalInfo: {
          firstName: '',
          lastName: '',
          dateOfBirth: '',
          nationality: '',
          idNumber: '',
          passportNumber: '',
          phone: '',
          email: '',
          address: '',
          gender: 'male',
        },
        employmentInfo: {
          employeeId: '',
          department: '',
          position: '',
          startDate: '',
          salary: '',
          contractType: 'full-time',
          bankName: '',
          bankAccount: '',
          workLocation: '',
        },
        documents: {
          idCard: null,
          passport: null,
          contract: null,
          photo: null,
        },
      });
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      dir={dir}
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <a
            href="/employees"
            className="flex items-center text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
          >
            <i
              className={`fas fa-arrow-${
                selectedLang === 'ar' ? 'left' : 'right'
              } ml-2`}
            ></i>
            {selectedLang === 'ar' ? 'عودة' : 'Back'}
          </a>
          <button
            onClick={() => setSelectedLang(selectedLang === 'ar' ? 'en' : 'ar')}
            className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
          >
            {selectedLang === 'ar' ? 'English' : 'العربية'}
          </button>
        </div>

        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          {selectedLang === 'ar' ? 'إضافة موظف جديد' : 'Add New Employee'}
        </h1>

        <form onSubmit={handleSubmit} className="space-y-8">
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              {selectedLang === 'ar'
                ? 'المعلومات الشخصية'
                : 'Personal Information'}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  {selectedLang === 'ar' ? 'الاسم الأول' : 'First Name'}
                </label>
                <input
                  type="text"
                  name="firstName"
                  value={formData.personalInfo.firstName}
                  onChange={(e) =>
                    handleInputChange(
                      'personalInfo',
                      'firstName',
                      e.target.value
                    )
                  }
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                />
              </div>

              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  {selectedLang === 'ar' ? 'اسم العائلة' : 'Last Name'}
                </label>
                <input
                  type="text"
                  name="lastName"
                  value={formData.personalInfo.lastName}
                  onChange={(e) =>
                    handleInputChange(
                      'personalInfo',
                      'lastName',
                      e.target.value
                    )
                  }
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                />
              </div>

              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  {selectedLang === 'ar' ? 'تاريخ الميلاد' : 'Date of Birth'}
                </label>
                <input
                  type="date"
                  name="dateOfBirth"
                  value={formData.personalInfo.dateOfBirth}
                  onChange={(e) =>
                    handleInputChange(
                      'personalInfo',
                      'dateOfBirth',
                      e.target.value
                    )
                  }
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                />
              </div>

              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  {selectedLang === 'ar' ? 'الجنس' : 'Gender'}
                </label>
                <select
                  name="gender"
                  value={formData.personalInfo.gender}
                  onChange={(e) =>
                    handleInputChange('personalInfo', 'gender', e.target.value)
                  }
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="male">
                    {selectedLang === 'ar' ? 'ذكر' : 'Male'}
                  </option>
                  <option value="female">
                    {selectedLang === 'ar' ? 'أنثى' : 'Female'}
                  </option>
                </select>
              </div>

              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  {selectedLang === 'ar' ? 'الجنسية' : 'Nationality'}
                </label>
                <input
                  type="text"
                  name="nationality"
                  value={formData.personalInfo.nationality}
                  onChange={(e) =>
                    handleInputChange(
                      'personalInfo',
                      'nationality',
                      e.target.value
                    )
                  }
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                />
              </div>

              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  {selectedLang === 'ar' ? 'رقم الهوية' : 'ID Number'}
                </label>
                <input
                  type="text"
                  name="idNumber"
                  value={formData.personalInfo.idNumber}
                  onChange={(e) =>
                    handleInputChange(
                      'personalInfo',
                      'idNumber',
                      e.target.value
                    )
                  }
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                />
              </div>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              {selectedLang === 'ar'
                ? 'معلومات التوظيف'
                : 'Employment Information'}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  {selectedLang === 'ar' ? 'الرقم الوظيفي' : 'Employee ID'}
                </label>
                <input
                  type="text"
                  name="employeeId"
                  value={formData.employmentInfo.employeeId}
                  onChange={(e) =>
                    handleInputChange(
                      'employmentInfo',
                      'employeeId',
                      e.target.value
                    )
                  }
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                />
              </div>

              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  {selectedLang === 'ar' ? 'القسم' : 'Department'}
                </label>
                <input
                  type="text"
                  name="department"
                  value={formData.employmentInfo.department}
                  onChange={(e) =>
                    handleInputChange(
                      'employmentInfo',
                      'department',
                      e.target.value
                    )
                  }
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                />
              </div>

              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  {selectedLang === 'ar' ? 'المنصب' : 'Position'}
                </label>
                <input
                  type="text"
                  name="position"
                  value={formData.employmentInfo.position}
                  onChange={(e) =>
                    handleInputChange(
                      'employmentInfo',
                      'position',
                      e.target.value
                    )
                  }
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                />
              </div>

              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  {selectedLang === 'ar' ? 'تاريخ البدء' : 'Start Date'}
                </label>
                <input
                  type="date"
                  name="startDate"
                  value={formData.employmentInfo.startDate}
                  onChange={(e) =>
                    handleInputChange(
                      'employmentInfo',
                      'startDate',
                      e.target.value
                    )
                  }
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                />
              </div>

              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  {selectedLang === 'ar' ? 'الراتب' : 'Salary'}
                </label>
                <input
                  type="number"
                  name="salary"
                  value={formData.employmentInfo.salary}
                  onChange={(e) =>
                    handleInputChange(
                      'employmentInfo',
                      'salary',
                      e.target.value
                    )
                  }
                  className="w-full p-2 border border-gray-300 rounded-md"
                  required
                />
              </div>

              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  {selectedLang === 'ar' ? 'نوع العقد' : 'Contract Type'}
                </label>
                <select
                  name="contractType"
                  value={formData.employmentInfo.contractType}
                  onChange={(e) =>
                    handleInputChange(
                      'employmentInfo',
                      'contractType',
                      e.target.value
                    )
                  }
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="full-time">
                    {selectedLang === 'ar' ? 'دوام كامل' : 'Full Time'}
                  </option>
                  <option value="part-time">
                    {selectedLang === 'ar' ? 'دوام جزئي' : 'Part Time'}
                  </option>
                  <option value="contract">
                    {selectedLang === 'ar' ? 'عقد' : 'Contract'}
                  </option>
                </select>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              {selectedLang === 'ar' ? 'المستندات' : 'Documents'}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  {selectedLang === 'ar' ? 'صورة الهوية' : 'ID Card'}
                </label>
                <input
                  type="file"
                  name="idCard"
                  onChange={(e) =>
                    handleFileUpload('documents', 'idCard', e.target.files[0])
                  }
                  className="w-full p-2 border border-gray-300 rounded-md"
                  accept="image/*,.pdf"
                />
              </div>

              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  {selectedLang === 'ar' ? 'جواز السفر' : 'Passport'}
                </label>
                <input
                  type="file"
                  name="passport"
                  onChange={(e) =>
                    handleFileUpload('documents', 'passport', e.target.files[0])
                  }
                  className="w-full p-2 border border-gray-300 rounded-md"
                  accept="image/*,.pdf"
                />
              </div>

              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  {selectedLang === 'ar' ? 'العقد' : 'Contract'}
                </label>
                <input
                  type="file"
                  name="contract"
                  onChange={(e) =>
                    handleFileUpload('documents', 'contract', e.target.files[0])
                  }
                  className="w-full p-2 border border-gray-300 rounded-md"
                  accept=".pdf"
                />
              </div>

              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  {selectedLang === 'ar' ? 'الصورة الشخصية' : 'Photo'}
                </label>
                <input
                  type="file"
                  name="photo"
                  onChange={(e) =>
                    handleFileUpload('documents', 'photo', e.target.files[0])
                  }
                  className="w-full p-2 border border-gray-300 rounded-md"
                  accept="image/*"
                />
              </div>
            </div>
          </div>

          {error && (
            <div className="p-3 bg-red-100 text-red-700 rounded-md">
              {error}
            </div>
          )}

          {success && (
            <div className="p-3 bg-green-100 text-green-700 rounded-md">
              {selectedLang === 'ar'
                ? 'تمت إضافة الموظف بنجاح'
                : 'Employee added successfully'}
            </div>
          )}

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={loading || uploadLoading}
              className="bg-blue-600 text-white py-2 px-6 rounded-md hover:bg-blue-700 transition-colors disabled:bg-blue-300"
            >
              {loading || uploadLoading
                ? selectedLang === 'ar'
                  ? 'جاري الإضافة...'
                  : 'Adding...'
                : selectedLang === 'ar'
                  ? 'إضافة الموظف'
                  : 'Add Employee'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default MainComponent;
