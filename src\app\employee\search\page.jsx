"use client";
import React, { useState } from "react";

function MainComponent() {
  const [selectedLang, setSelectedLang] = useState("ar");
  const [employeeCode, setEmployeeCode] = useState("");
  const [employee, setEmployee] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [selectedDocument, setSelectedDocument] = useState(null);

  const dir = selectedLang === "ar" ? "rtl" : "ltr";

  const documentTypes = [
    { id: "nationalId", label: { ar: "الهوية الوطنية", en: "National ID" } },
    { id: "workReceipts", label: { ar: "إيصالات العمل", en: "Work Receipts" } },
    {
      id: "statusReports",
      label: { ar: "تقارير الحالة", en: "Status Reports" },
    },
    { id: "contracts", label: { ar: "العقود", en: "Contracts" } },
  ];

  const handleSearch = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");
    setEmployee(null);

    try {
      const response = await fetch("/api/data-service", {
        method: "POST",
        body: JSON.stringify({
          table: "employees",
          action: "search",
          employeeCode,
        }),
      });

      if (!response.ok) {
        throw new Error(
          selectedLang === "ar"
            ? "لم يتم العثور على الموظف"
            : "Employee not found"
        );
      }

      const data = await response.json();
      setEmployee(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      dir={dir}
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <a
            href="/employees"
            className="flex items-center text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
          >
            <i
              className={`fas fa-arrow-${
                selectedLang === "ar" ? "left" : "right"
              } ml-2`}
            ></i>
            {selectedLang === "ar" ? "عودة" : "Back"}
          </a>
          <button
            onClick={() => setSelectedLang(selectedLang === "ar" ? "en" : "ar")}
            className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
          >
            {selectedLang === "ar" ? "English" : "العربية"}
          </button>
        </div>

        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          {selectedLang === "ar" ? "البحث عن الموظفين" : "Employee Search"}
        </h1>

        <form onSubmit={handleSearch} className="mb-8">
          <div className="flex gap-4">
            <input
              type="text"
              name="employeeCode"
              value={employeeCode}
              onChange={(e) => setEmployeeCode(e.target.value)}
              placeholder={
                selectedLang === "ar"
                  ? "أدخل كود الموظف"
                  : "Enter Employee Code"
              }
              className="flex-1 p-3 border border-gray-300 rounded-md"
              required
            />
            <button
              type="submit"
              disabled={loading}
              className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors disabled:bg-blue-300"
            >
              {loading
                ? selectedLang === "ar"
                  ? "جاري البحث..."
                  : "Searching..."
                : selectedLang === "ar"
                ? "بحث"
                : "Search"}
            </button>
          </div>
        </form>

        {error && (
          <div className="p-4 bg-red-100 text-red-700 rounded-md mb-6">
            {error}
          </div>
        )}

        {employee && (
          <div className="bg-gray-50 dark:bg-gray-800 rounded-xl p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                  {selectedLang === "ar"
                    ? "معلومات الموظف"
                    : "Employee Information"}
                </h2>
                <div className="space-y-3">
                  <p className="text-gray-700 dark:text-gray-300">
                    <span className="font-semibold">
                      {selectedLang === "ar" ? "الاسم: " : "Name: "}
                    </span>
                    {employee.name}
                  </p>
                  <p className="text-gray-700 dark:text-gray-300">
                    <span className="font-semibold">
                      {selectedLang === "ar" ? "الكود: " : "Code: "}
                    </span>
                    {employee.code}
                  </p>
                  <p className="text-gray-700 dark:text-gray-300">
                    <span className="font-semibold">
                      {selectedLang === "ar" ? "القسم: " : "Department: "}
                    </span>
                    {employee.department}
                  </p>
                </div>
              </div>

              <div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                  {selectedLang === "ar" ? "الوثائق" : "Documents"}
                </h2>
                <div className="grid grid-cols-2 gap-4">
                  {documentTypes.map((doc) => (
                    <button
                      key={doc.id}
                      onClick={() => setSelectedDocument(doc.id)}
                      className={`p-3 rounded-md text-center transition-colors ${
                        selectedDocument === doc.id
                          ? "bg-blue-600 text-white"
                          : "bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-blue-100 dark:hover:bg-gray-600"
                      }`}
                    >
                      {doc.label[selectedLang]}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {selectedDocument && (
              <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">
                  {
                    documentTypes.find((d) => d.id === selectedDocument).label[
                      selectedLang
                    ]
                  }
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {employee.documents?.[selectedDocument]?.map((doc, index) => (
                    <div
                      key={index}
                      className="bg-white dark:bg-gray-700 rounded-lg p-4"
                    >
                      <img
                        src={doc.url}
                        alt={`${
                          documentTypes.find((d) => d.id === selectedDocument)
                            .label[selectedLang]
                        } ${index + 1}`}
                        className="w-full h-[200px] object-cover rounded-md mb-2"
                      />
                      <p className="text-sm text-gray-600 dark:text-gray-300">
                        {doc.date}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default MainComponent;