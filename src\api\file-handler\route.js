async function handler(request) {
  const { action, fileContent, fileType, fileName } = await request.json();

  try {
    if (action === 'downloadTemplate') {
      const file = {
        buffer: Buffer.from([
          0x50, 0x4b, 0x03, 0x04, 0x14, 0x00, 0x06, 0x00, 0x08, 0x00, 0x00,
          0x00, 0x21, 0x00,
        ]),
        filename: `template_${fileType}.xlsx`,
      };

      return {
        success: true,
        buffer: file.buffer,
        filename: file.filename,
        contentType:
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      };
    }

    if (action === 'upload') {
      if (!fileContent || !fileType) {
        throw new Error('Missing required fields');
      }

      if (fileType === 'pdf') {
        if (!fileName.toLowerCase().endsWith('.pdf')) {
          throw new Error('Invalid file type. Only PDF files are allowed.');
        }

        const { url, error } = await upload({ base64: fileContent });

        if (error) {
          throw new Error('Failed to upload PDF file');
        }

        return {
          success: true,
          message: 'تم رفع الملف بنجاح',
          url,
          fileName,
        };
      }

      if (fileType === 'attendance') {
        const records = JSON.parse(fileContent);
        const values = records.map((record) => [
          record.employeeId,
          record.employeeName,
          record.month,
          record.attendanceData,
          record.stats,
        ]);

        const insertedRecords = await sql(
          'INSERT INTO monthly_attendance (employee_id, employee_name, attendance_month, attendance_data, stats) VALUES ($1, $2, $3, $4, $5) RETURNING id, employee_id, employee_name, attendance_month',
          values
        );

        return {
          success: true,
          message: 'تم رفع بيانات الحضور بنجاح',
          data: insertedRecords,
        };
      }

      return {
        success: true,
        message: 'تم رفع الملف بنجاح',
      };
    }

    if (action === 'confirmUpload') {
      if (!fileContent || !fileType) {
        throw new Error('Missing required fields');
      }

      return {
        success: true,
        message: 'تم تأكيد الملف للرفع',
        fileSize: Buffer.from(fileContent, 'base64').length,
        fileName,
        fileType,
      };
    }

    throw new Error('Invalid action');
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
}
