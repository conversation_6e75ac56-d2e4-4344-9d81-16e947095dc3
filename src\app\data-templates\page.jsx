'use client';

import React, { useState } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { useLanguage } from '@/contexts/LanguageContext';
import MainLayout from '@/components/MainLayout';
import { 
  Download, 
  Upload, 
  FileSpreadsheet, 
  Users, 
  Clock, 
  UserCheck, 
  Calendar, 
  Building, 
  Car,
  CheckCircle,
  AlertCircle,
  FileText,
  Info,
  ExternalLink
} from 'lucide-react';

export default function DataTemplatesPage() {
  const { isDarkMode } = useTheme();
  const { isRTL, isArabic } = useLanguage();
  
  const [downloadingTemplate, setDownloadingTemplate] = useState('');

  // قوالب البيانات المتاحة
  const dataTemplates = [
    {
      id: 'daily-attendance',
      title: 'التمام اليومي للموظفين',
      description: 'نموذج لرفع بيانات الحضور والغياب اليومي للموظفين (يجب أن تكون الأعمدة بالإنجليزية كما هو موضح)',
      icon: Clock,
      color: 'blue',
      fileName: 'daily_attendance_template.xlsx',
      sampleData: [
        'employeeCode, employeeName, department, attendanceDate, attendanceStatus, checkInTime, checkOutTime, notes',
        'EMP001, Ahmed Mohamed Ali, General Administration, 2024-01-15, حضور, 08:30, 17:00, Normal working day',
        'EMP002, Fatma Saad, HR, 2024-01-15, غياب, , , Absent without excuse',
        'EMP003, Mohamed Hassan, IT, 2024-01-15, إجازة اعتيادية, , , Annual leave'
      ],
      uploadEndpoint: '/api/upload/daily-attendance',
      requiredFields: ['employeeCode', 'attendanceDate', 'attendanceStatus'],
      validationRules: [
        'employeeCode must exist in the system',
        'attendanceDate format: YYYY-MM-DD',
        'attendanceStatus: حضور، غياب، إجازة اعتيادية، إجازة مرضية، مأمورية',
        'checkInTime/checkOutTime format: HH:MM (if present)',
        'Columns must be exactly as shown and in the same order.'
      ]
    },
    {
      id: 'temp-workers-daily',
      title: 'التسجيل اليومي للعمالة المؤقتة',
      description: 'نموذج لتسجيل حضور وأعمال العمالة المؤقتة اليومية',
      icon: UserCheck,
      color: 'orange',
      fileName: 'temp_workers_daily_template.xlsx',
      sampleData: [
        'كود العامل، اسم العامل، نوع العمالة، التاريخ، ساعات العمل، نوع العمل، الموقع، ملاحظات',
        'TW001، أحمد محمد، خدمي، 2024-01-15، 8، تنظيف مكاتب، المبنى الرئيسي، عمل ممتاز',
        'TW002، محمد علي، إنتاجي، 2024-01-15، 10، صيانة كهرباء، الطابق الثاني، عمل إضافي'
      ],
      uploadEndpoint: '/api/upload/temp-workers-daily',
      requiredFields: ['كود العامل', 'التاريخ', 'ساعات العمل'],
      validationRules: [
        'كود العامل يجب أن يكون موجود في النظام',
        'التاريخ بصيغة YYYY-MM-DD',
        'ساعات العمل رقم من 1 إلى 12',
        'نوع العمالة: خدمي أو إنتاجي'
      ]
    },
    {
      id: 'employees-bulk',
      title: 'إضافة موظفين جماعية',
      description: 'نموذج لإضافة عدة موظفين جدد دفعة واحدة',
      icon: Users,
      color: 'green',
      fileName: 'employees_bulk_template.xlsx',
      sampleData: [
        'كود الموظف، الاسم الكامل، القسم، المسمى الوظيفي، المحافظة، تاريخ التعيين، الراتب، رقم الهاتف',
        'EMP100، أحمد محمد علي، الإدارة العامة، مدير إداري، القاهرة، 2024-01-15، 5000، 01234567890',
        'EMP101، فاطمة سعد حسن، الموارد البشرية، أخصائي موارد بشرية، الجيزة، 2024-01-20، 4500، 01234567891'
      ],
      uploadEndpoint: '/api/upload/employees-bulk',
      requiredFields: ['كود الموظف', 'الاسم الكامل', 'القسم', 'المسمى الوظيفي', 'تاريخ التعيين'],
      validationRules: [
        'كود الموظف يجب أن يكون فريد',
        'الاسم الكامل مطلوب',
        'تاريخ التعيين بصيغة YYYY-MM-DD',
        'الراتب رقم موجب',
        'رقم الهاتف 11 رقم'
      ]
    }
  ];

  // تحميل نموذج Excel
  const downloadTemplate = async (template) => {
    setDownloadingTemplate(template.id);
    try {
      const response = await fetch(`/api/templates/download/${template.id}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = template.fileName;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        alert('خطأ في تحميل النموذج');
      }
    } catch (error) {

      alert('خطأ في تحميل النموذج');
    } finally {
      setDownloadingTemplate('');
    }
  };

  return (
    <MainLayout>
      <div className={`p-6 ${isDarkMode ? 'bg-[#0f172a] text-white' : 'bg-gray-50 text-gray-900'}`}>
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm p-6 mb-6`}>
          <div className="flex items-center gap-3 mb-4">
            <FileSpreadsheet className="h-8 w-8 text-green-600" />
            <div>
              <h1 className="text-2xl font-bold">نماذج إدخال البيانات</h1>
              <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                تحميل نماذج Excel لإدخال البيانات ورفعها على النظام بشكل صحيح
              </p>
            </div>
          </div>

          {/* تعليمات الاستخدام */}
          <div className={`p-4 rounded-lg border-l-4 border-blue-500 ${
            isDarkMode ? 'bg-blue-900/20 border-blue-400' : 'bg-blue-50 border-blue-500'
          }`}>
            <div className="flex items-start gap-3">
              <Info className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h3 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">تعليمات الاستخدام:</h3>
                <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                  <li>• اختر النموذج المطلوب وحمله</li>
                  <li>• املأ البيانات حسب التعليمات المرفقة</li>
                  <li>• احفظ الملف بصيغة Excel (.xlsx)</li>
                  <li>• ارفع الملف من خلال صفحة الرفع المخصصة</li>
                  <li>• تأكد من صحة البيانات قبل الرفع</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* قائمة النماذج */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {dataTemplates.map((template) => {
            const IconComponent = template.icon;
            return (
              <div key={template.id} className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm overflow-hidden`}>
                <div className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className={`w-12 h-12 rounded-lg bg-${template.color}-100 dark:bg-${template.color}-900 flex items-center justify-center`}>
                        <IconComponent className={`h-6 w-6 text-${template.color}-600 dark:text-${template.color}-400`} />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold">{template.title}</h3>
                        <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          {template.description}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* بيانات تجريبية */}
                  <div className="mb-4">
                    <h4 className="text-sm font-medium mb-2">مثال على البيانات:</h4>
                    <div className={`p-3 rounded-lg text-xs font-mono ${
                      isDarkMode ? 'bg-gray-800 text-gray-300' : 'bg-gray-100 text-gray-700'
                    }`}>
                      {template.sampleData.map((line, index) => (
                        <div key={index} className={index === 0 ? 'font-bold border-b border-gray-400 pb-1 mb-1' : ''}>
                          {line}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* الحقول المطلوبة */}
                  <div className="mb-4">
                    <h4 className="text-sm font-medium mb-2">الحقول المطلوبة:</h4>
                    <div className="flex flex-wrap gap-2">
                      {template.requiredFields.map((field, index) => (
                        <span key={index} className={`px-2 py-1 rounded text-xs font-medium ${
                          isDarkMode 
                            ? 'bg-red-900 text-red-200' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {field}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* قواعد التحقق */}
                  <div className="mb-6">
                    <h4 className="text-sm font-medium mb-2">قواعد التحقق:</h4>
                    <ul className={`text-xs space-y-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {template.validationRules.map((rule, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 flex-shrink-0" />
                          {rule}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* أزرار الإجراءات */}
                  <div className="flex items-center gap-3">
                    <button
                      onClick={() => downloadTemplate(template)}
                      disabled={downloadingTemplate === template.id}
                      className={`flex-1 bg-${template.color}-600 hover:bg-${template.color}-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors disabled:opacity-50`}
                    >
                      <Download className="h-4 w-4" />
                      {downloadingTemplate === template.id ? 'جاري التحميل...' : 'تحميل النموذج'}
                    </button>
                    
                    <a
                      href={`/upload${template.uploadEndpoint.replace('/api/upload', '')}`}
                      className={`px-4 py-2 rounded-lg border transition-colors flex items-center gap-2 ${
                        isDarkMode 
                          ? 'border-gray-600 text-gray-300 hover:bg-gray-700' 
                          : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <Upload className="h-4 w-4" />
                      رفع الملف
                    </a>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* معلومات إضافية */}
        <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm p-6 mt-6`}>
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-orange-600" />
            ملاحظات مهمة
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-2">قبل رفع الملف:</h4>
              <ul className={`text-sm space-y-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                <li>• تأكد من ملء جميع الحقول المطلوبة</li>
                <li>• تحقق من صحة التواريخ والأرقام</li>
                <li>• استخدم الصيغ المحددة للبيانات</li>
                <li>• لا تغير أسماء الأعمدة</li>
              </ul>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">في حالة الأخطاء:</h4>
              <ul className={`text-sm space-y-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                <li>• راجع رسائل الخطأ بعناية</li>
                <li>• صحح البيانات وأعد الرفع</li>
                <li>• تواصل مع الدعم الفني عند الحاجة</li>
                <li>• احتفظ بنسخة احتياطية من البيانات</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
