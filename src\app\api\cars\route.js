import { getConnection } from '@/lib/db';
import sql from 'mssql';
import { NextResponse } from 'next/server';

// اكتشاف الأعمدة الصحيحة تلقائياً
async function discoverCorrectColumns(pool) {
  try {
    // أولاً، نحصل على أسماء الأعمدة من INFORMATION_SCHEMA
    const columnsResult = await pool.request().query(`
      SELECT COLUMN_NAME, DATA_TYPE
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'Employees'
      ORDER BY ORDINAL_POSITION
    `);

    let employeeCodeColumn = 'EmployeeCode'; // القيمة الافتراضية
    let employeeNameColumn = 'EmployeeName'; // القيمة الافتراضية

    // البحث عن عمود كود الموظف
    const codeColumns = columnsResult.recordset.filter(col =>
      col.COLUMN_NAME.toLowerCase().includes('employeecode') ||
      col.COLUMN_NAME.toLowerCase().includes('employee_code') ||
      col.COLUMN_NAME.toLowerCase() === 'employeeid' ||
      col.COLUMN_NAME.toLowerCase() === 'empcode'
    );

    if (codeColumns.length > 0) {
      employeeCodeColumn = codeColumns[0].COLUMN_NAME;
    }

    // البحث عن عمود اسم الموظف
    const nameColumns = columnsResult.recordset.filter(col =>
      col.COLUMN_NAME.toLowerCase().includes('employeename') ||
      col.COLUMN_NAME.toLowerCase().includes('employee_name') ||
      col.COLUMN_NAME.toLowerCase().includes('fullname') ||
      col.COLUMN_NAME.toLowerCase().includes('name')
    );

    if (nameColumns.length > 0) {
      employeeNameColumn = nameColumns[0].COLUMN_NAME;
    }

    return {
      employeeCodeColumn,
      employeeNameColumn
    };
  } catch (error) {

    return {
      employeeCodeColumn: 'EmployeeCode',
      employeeNameColumn: 'EmployeeName'
    };
  }
}

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'getAll';

    const pool = await getConnection();

    switch (action) {
      case 'getAll':
        return await getAllCars(pool, {
          includeInactive: searchParams.get('includeInactive') === 'true',
          search: searchParams.get('search') || ''
        });
      case 'getById':
        return await getCarById(pool, {
          carId: parseInt(searchParams.get('carId'))
        });
      case 'getStatistics':
        return await getStatistics(pool);
      case 'setup':
        return await setupCarTables(pool);
      default:
        return await getAllCars(pool, {});
    }
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  }
}

export async function POST(request) {
  try {
    const body = await request.json();
    const { action } = body;

    const pool = await getConnection();

    switch (action) {
      case 'create':
        return await createCar(pool, body);
      case 'update':
        return await updateCar(pool, body);
      case 'delete':
        return await deleteCar(pool, body);
      case 'getAll':
        return await getAllCars(pool, body);
      case 'getById':
        return await getCarById(pool, body);
      case 'addBeneficiary':
        return await addBeneficiary(pool, body);
      case 'removeBeneficiary':
        return await removeBeneficiary(pool, body);
      case 'checkEmployeeStatus':
        return await checkEmployeeStatus(pool, body);
      case 'getStatistics':
        return await getStatistics(pool);
      case 'setup':
        const setupResult = await setupCarTables(pool);
        return NextResponse.json(setupResult);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  }
}

// إعداد جداول السيارات
async function setupCarTables(pool) {
  try {

    // 1. إنشاء جدول السيارات
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Cars' AND xtype='U')
      BEGIN
        CREATE TABLE Cars (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          CarCode NVARCHAR(20) NOT NULL UNIQUE,
          ContractorName NVARCHAR(100) NOT NULL,
          CarNumber NVARCHAR(50) NOT NULL,
          CarType NVARCHAR(100) NOT NULL,
          CarModel NVARCHAR(100) NOT NULL,
          ManufactureYear INT,
          Route NVARCHAR(200) NOT NULL,
          RentAmount DECIMAL(10,2) NOT NULL,
          Notes NVARCHAR(MAX),
          IsActive BIT DEFAULT 1,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE()
        )

        CREATE INDEX IX_Cars_Code ON Cars(CarCode)
        CREATE INDEX IX_Cars_Active ON Cars(IsActive)
        CREATE INDEX IX_Cars_CarNumber ON Cars(CarNumber)
        CREATE INDEX IX_Cars_Route ON Cars(Route)
      END
    `);

    // 2. إنشاء جدول المستفيدين
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CarBeneficiaries' AND xtype='U')
      BEGIN
        CREATE TABLE CarBeneficiaries (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          CarCode NVARCHAR(20) NOT NULL,
          EmployeeCode NVARCHAR(20) NOT NULL,
          EmployeeName NVARCHAR(100) NOT NULL,
          JobTitle NVARCHAR(100) NOT NULL,
          Department NVARCHAR(100),
          StartDate DATE NOT NULL,
          EndDate DATE,
          IsActive BIT DEFAULT 1,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE()
        )

        CREATE INDEX IX_CarBeneficiaries_CarCode ON CarBeneficiaries(CarCode)
        CREATE INDEX IX_CarBeneficiaries_EmployeeCode ON CarBeneficiaries(EmployeeCode)
        CREATE INDEX IX_CarBeneficiaries_Active ON CarBeneficiaries(IsActive)
      END
      ELSE
      BEGIN
        -- التحقق من وجود مفتاح أساسي خاطئ وإصلاحه
        IF EXISTS (
          SELECT * FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
          WHERE TABLE_NAME = 'CarBeneficiaries'
          AND CONSTRAINT_TYPE = 'PRIMARY KEY'
          AND CONSTRAINT_NAME LIKE '%EmployeeCode%'
        )
        BEGIN
          -- حذف المفتاح الأساسي الخاطئ
          DECLARE @ConstraintName NVARCHAR(200)
          SELECT @ConstraintName = CONSTRAINT_NAME
          FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
          WHERE TABLE_NAME = 'CarBeneficiaries'
          AND CONSTRAINT_TYPE = 'PRIMARY KEY'

          EXEC('ALTER TABLE CarBeneficiaries DROP CONSTRAINT ' + @ConstraintName)

          -- إضافة المفتاح الأساسي الصحيح
          ALTER TABLE CarBeneficiaries ADD CONSTRAINT PK_CarBeneficiaries PRIMARY KEY (ID)
        END
      END
    `);

    return { success: true, message: 'تم إعداد جداول السيارات بنجاح' };

  } catch (error) {

    throw new Error('فشل في إعداد جداول السيارات: ' + error.message);
  }
}

// إنشاء سيارة جديدة
async function createCar(pool, data) {
  try {
    await setupCarTables(pool);

    const {
      carCode,
      contractorName,
      carNumber,
      carType,
      carModel,
      manufactureYear,
      route,
      rentAmount,
      notes,
      beneficiaries = []
    } = data;

    // التحقق من عدم تكرار كود السيارة
    const existingCar = await pool.request()
      .input('carCode', sql.NVarChar, carCode)
      .query('SELECT ID FROM Cars WHERE CarCode = @carCode');

    if (existingCar.recordset.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'كود السيارة موجود مسبقاً'
      }, { status: 400 });
    }

    // التحقق من عدم تكرار رقم السيارة
    const existingCarNumber = await pool.request()
      .input('carNumber', sql.NVarChar, carNumber)
      .query('SELECT ID FROM Cars WHERE CarNumber = @carNumber');

    if (existingCarNumber.recordset.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'رقم السيارة موجود مسبقاً'
      }, { status: 400 });
    }

    // إدراج السيارة
    const carResult = await pool.request()
      .input('carCode', sql.NVarChar, carCode)
      .input('contractorName', sql.NVarChar, contractorName)
      .input('carNumber', sql.NVarChar, carNumber)
      .input('carType', sql.NVarChar, carType)
      .input('carModel', sql.NVarChar, carModel)
      .input('manufactureYear', sql.Int, manufactureYear || null)
      .input('route', sql.NVarChar, route)
      .input('rentAmount', sql.Decimal(10, 2), rentAmount)
      .input('notes', sql.NVarChar, notes || null)
      .query(`
        INSERT INTO Cars (
          CarCode, ContractorName, CarNumber, CarType, CarModel,
          ManufactureYear, Route, RentAmount, Notes
        )
        OUTPUT INSERTED.ID
        VALUES (
          @carCode, @contractorName, @carNumber, @carType, @carModel,
          @manufactureYear, @route, @rentAmount, @notes
        )
      `);

    const carId = carResult.recordset[0].ID;

    // إدراج المستفيدين
    for (const beneficiary of beneficiaries) {
      await addBeneficiaryToCar(pool, carId, beneficiary);
    }

    return NextResponse.json({
      success: true,
      message: 'تم إنشاء السيارة بنجاح',
      data: { carId }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في إنشاء السيارة: ' + error.message
    }, { status: 500 });
  }
}

// إضافة مستفيد للسيارة
// دالة لتحويل التاريخ من dd/mm/yyyy إلى Date object
function parseDate(dateString) {
  if (!dateString) return null;

  // إذا كان التاريخ بصيغة dd/mm/yyyy
  if (dateString.includes('/')) {
    const parts = dateString.split('/');
    if (parts.length === 3) {
      const day = parseInt(parts[0]);
      const month = parseInt(parts[1]) - 1; // الشهر يبدأ من 0
      const year = parseInt(parts[2]);
      return new Date(year, month, day);
    }
  }

  // إذا كان التاريخ بصيغة أخرى، جرب التحويل المباشر
  return new Date(dateString);
}

async function addBeneficiaryToCar(pool, carId, beneficiary) {
  try {
    const { employeeCode, startDate } = beneficiary;

    // Get CarCode from CarID (No change here, as CarID is the ID from Cars table)
    const carResult = await pool.request()
      .input('carId', sql.Int, carId)
      .query('SELECT CarCode FROM Cars WHERE ID = @carId');

    if (carResult.recordset.length === 0) {
      throw new Error(`السيارة بالمعرف ${carId} غير موجودة.`);
    }
    const carCode = carResult.recordset[0].CarCode; // This is the actual carCode from Cars table

    // جلب بيانات الموظف (الاسم، الوظيفة، القسم)
    const { employeeCodeColumn, employeeNameColumn } = await discoverCorrectColumns(pool);

    const employeeResult = await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .query(`
        SELECT TOP 1
          ${employeeNameColumn} as EmployeeName,
          ISNULL(JobTitle, '') as JobTitle,
          ISNULL(Department, '') as Department
        FROM Employees
        WHERE ${employeeCodeColumn} = @employeeCode
           OR CAST(${employeeCodeColumn} AS NVARCHAR) = @employeeCode
      `);

    let employeeName = null;
    let jobTitle = null;
    let department = null;

    if (employeeResult.recordset.length > 0) {
      const employeeData = employeeResult.recordset[0];
      employeeName = employeeData.EmployeeName;
      jobTitle = employeeData.JobTitle;
      department = employeeData.Department;
    } else {
      // إذا لم يتم العثور على الموظف، يمكن التعامل معها هنا أو تركها null

    }

    // التحقق من وجود الموظف النشط في نفس السيارة
    const activeBeneficiaryInSameCar = await pool.request()
      .input('carCode', sql.NVarChar, carCode)
      .input('employeeCode', sql.NVarChar, employeeCode)
      .query(`
        SELECT ID FROM CarBeneficiaries
        WHERE CarCode = @carCode AND EmployeeCode = @employeeCode AND IsActive = 1
      `);

    if (activeBeneficiaryInSameCar.recordset.length > 0) {
      throw new Error(`الموظف ${employeeName || employeeCode} مسجل بالفعل كمستفيد نشط في هذه السيارة`);
    }

    // التحقق من وجود الموظف النشط في أي سيارة أخرى
    const activeBeneficiaryInOtherCar = await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .query(`
        SELECT cb.ID, cb.CarCode, c.ContractorName, c.CarNumber
        FROM CarBeneficiaries cb
        LEFT JOIN Cars c ON cb.CarCode = c.CarCode
        WHERE cb.EmployeeCode = @employeeCode AND cb.IsActive = 1
      `);

    if (activeBeneficiaryInOtherCar.recordset.length > 0) {
      const existingCar = activeBeneficiaryInOtherCar.recordset[0];
      throw new Error(`الموظف ${employeeName || employeeCode} مسجل بالفعل كمستفيد نشط في السيارة: ${existingCar.CarCode} - ${existingCar.ContractorName} (${existingCar.CarNumber}). يجب إنهاء استفادته من السيارة الحالية أولاً.`);
    }

    // التحقق من وجود الموظف المنتهي وإعادة تفعيله بدلاً من إنشاء سجل جديد
    const inactiveBeneficiary = await pool.request()
      .input('carCode', sql.NVarChar, carCode)
      .input('employeeCode', sql.NVarChar, employeeCode)
      .query(`
        SELECT ID FROM CarBeneficiaries
        WHERE CarCode = @carCode AND EmployeeCode = @employeeCode AND IsActive = 0
      `);

    if (inactiveBeneficiary.recordset.length > 0) {
      // إعادة تفعيل المستفيد الموجود
      const beneficiaryId = inactiveBeneficiary.recordset[0].ID;

      await pool.request()
        .input('beneficiaryId', sql.Int, beneficiaryId)
        .input('employeeName', sql.NVarChar, employeeName)
        .input('jobTitle', sql.NVarChar, jobTitle)
        .input('department', sql.NVarChar, department)
        .input('startDate', sql.Date, parseDate(startDate))
        .query(`
          UPDATE CarBeneficiaries SET
            EmployeeName = @employeeName,
            JobTitle = @jobTitle,
            Department = @department,
            StartDate = @startDate,
            EndDate = NULL,
            IsActive = 1,
            UpdatedAt = GETDATE()
          WHERE ID = @beneficiaryId
        `);

      return beneficiaryId;
    }

    // إدراج المستفيد
    const insertResult = await pool.request()
      .input('carCode', sql.NVarChar, carCode)
      .input('employeeCode', sql.NVarChar, employeeCode)
      .input('employeeName', sql.NVarChar, employeeName)
      .input('jobTitle', sql.NVarChar, jobTitle)
      .input('department', sql.NVarChar, department)
      .input('startDate', sql.Date, parseDate(startDate))
      .query(`
        INSERT INTO CarBeneficiaries (
          CarCode,
          EmployeeCode, EmployeeName, JobTitle, Department, StartDate, IsActive, CreatedAt, UpdatedAt
        )
        OUTPUT INSERTED.ID
        VALUES (
          @carCode,
          @employeeCode, @employeeName, @jobTitle, @department, @startDate, 1, GETDATE(), GETDATE()
        )
      `);

    const newBeneficiaryId = insertResult.recordset[0].ID;

    // يتم تحديث بيانات الموظف (TransportMethod) في دالة updateEmployeeTransportData
    // مع تمرير CarCode والمسار من جدول Cars وليس TransportID
    const carInfoResult = await pool.request()
      .input('carId', sql.Int, carId)
      .query('SELECT CarCode, Route FROM Cars WHERE ID = @carId');

    if (carInfoResult.recordset.length > 0) {
      const carInfo = carInfoResult.recordset[0];
      await updateEmployeeTransportData(pool, employeeCode, carInfo.CarCode, carInfo.Route);
    }

    return newBeneficiaryId;

  } catch (error) {

    throw error;
  }
}

// جلب جميع السيارات
async function getAllCars(pool, data) {
  try {
    await setupCarTables(pool);

    const { includeInactive = false, search = '' } = data;

    let whereClause = 'WHERE 1=1';
    if (!includeInactive) {
      whereClause += ' AND c.IsActive = 1';
    }
    if (search) {
      // استخدام المعلمات لتجنب SQL injection
      whereClause += ` AND (c.CarCode LIKE @search OR c.ContractorName LIKE @search OR c.CarNumber LIKE @search OR c.Route LIKE @search)`;
    }

    const query = `
      SELECT
        c.ID,
        c.CarCode,
        c.ContractorName,
        c.CarNumber,
        c.CarType,
        c.CarModel,
        c.ManufactureYear,
        c.Route,
        c.RentAmount,
        c.Notes,
        c.IsActive,
        c.CreatedAt,

        -- عدد المستفيدين النشطين
        (SELECT COUNT(*) FROM CarBeneficiaries cb
         WHERE cb.CarCode = c.CarCode AND cb.IsActive = 1) as ActiveBeneficiariesCount

      FROM Cars c
      ${whereClause}
      ORDER BY c.CreatedAt DESC
    `;

    const request = pool.request();
    if (search) {
      request.input('search', sql.NVarChar, `%${search}%`);
    }

    const result = await request.query(query);

    // جلب المستفيدين لكل سيارة
    for (let car of result.recordset) {
      const beneficiariesResult = await pool.request()
        .input('carCode', sql.NVarChar, car.CarCode)
        .query(`
          SELECT
            ID,
            EmployeeCode,
            EmployeeName,
            JobTitle,
            Department,
            StartDate,
            EndDate,
            IsActive
          FROM CarBeneficiaries
          WHERE CarCode = @carCode
          ORDER BY IsActive DESC, StartDate DESC
        `);

      car.beneficiaries = beneficiariesResult.recordset;
    }

    return NextResponse.json({
      success: true,
      data: result.recordset
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب السيارات: ' + error.message
    }, { status: 500 });
  }
}

// تحديث سيارة
async function updateCar(pool, data) {
  try {
    const {
      carId,
      carCode,
      contractorName,
      carNumber,
      carType,
      carModel,
      manufactureYear,
      route,
      rentAmount,
      notes,
      isActive = true
    } = data;

    // التحقق من وجود السيارة
    const existingCar = await pool.request()
      .input('carId', sql.Int, carId)
      .query('SELECT ID FROM Cars WHERE ID = @carId');

    if (existingCar.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'السيارة غير موجودة'
      }, { status: 404 });
    }

    // تحديث السيارة
    await pool.request()
      .input('carId', sql.Int, carId)
      .input('carCode', sql.NVarChar, carCode)
      .input('contractorName', sql.NVarChar, contractorName)
      .input('carNumber', sql.NVarChar, carNumber)
      .input('carType', sql.NVarChar, carType)
      .input('carModel', sql.NVarChar, carModel)
      .input('manufactureYear', sql.Int, manufactureYear || null)
      .input('route', sql.NVarChar, route)
      .input('rentAmount', sql.Decimal(10, 2), rentAmount)
      .input('notes', sql.NVarChar, notes || null)
      .input('isActive', sql.Bit, isActive)
      .query(`
        UPDATE Cars SET
          CarCode = @carCode,
          ContractorName = @contractorName,
          CarNumber = @carNumber,
          CarType = @carType,
          CarModel = @carModel,
          ManufactureYear = @manufactureYear,
          Route = @route,
          RentAmount = @rentAmount,
          Notes = @notes,
          IsActive = @isActive,
          UpdatedAt = GETDATE()
        WHERE ID = @carId
      `);

    return NextResponse.json({
      success: true,
      message: 'تم تحديث السيارة بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث السيارة: ' + error.message
    }, { status: 500 });
  }
}

// حذف سيارة
async function deleteCar(pool, data) {
  try {
    const { carId } = data;

    // جلب CarCode أولاً
    const carResult = await pool.request()
      .input('carId', sql.Int, carId)
      .query('SELECT CarCode FROM Cars WHERE ID = @carId');

    if (carResult.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'السيارة غير موجودة'
      }, { status: 404 });
    }

    const carCode = carResult.recordset[0].CarCode;

    // حذف المستفيدين أولاً
    await pool.request()
      .input('carCode', sql.NVarChar, carCode)
      .query('DELETE FROM CarBeneficiaries WHERE CarCode = @carCode');

    // حذف السيارة
    await pool.request()
      .input('carId', sql.Int, carId)
      .query('DELETE FROM Cars WHERE ID = @carId');

    return NextResponse.json({
      success: true,
      message: 'تم حذف السيارة بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في حذف السيارة: ' + error.message
    }, { status: 500 });
  }
}

// إضافة مستفيد جديد
async function addBeneficiary(pool, data) {
  try {
    const { carId, employeeCode, startDate } = data;

    // التحقق من صحة البيانات
    if (!carId || !employeeCode || !startDate) {
      throw new Error('البيانات المطلوبة مفقودة: كود السيارة، كود الموظف، وتاريخ البداية مطلوبة');
    }

    // التحقق من صحة التاريخ
    const parsedDate = parseDate(startDate);
    if (!parsedDate || isNaN(parsedDate.getTime())) {
      throw new Error(`تاريخ البداية غير صالح: ${startDate}. يرجى استخدام صيغة dd/mm/yyyy`);
    }

    await addBeneficiaryToCar(pool, carId, {
      employeeCode,
      startDate
    });

    return NextResponse.json({
      success: true,
      message: 'تم إضافة المستفيد بنجاح'
    });

  } catch (error) {
    console.error('خطأ في إضافة المستفيد:', error);

    return NextResponse.json({
      success: false,
      error: 'فشل في إضافة المستفيد: ' + error.message,
      details: {
        carId: data.carId,
        employeeCode: data.employeeCode,
        startDate: data.startDate,
        errorType: error.name,
        stack: error.stack
      }
    }, { status: 500 });
  }
}

// التحقق من حالة الموظف
async function checkEmployeeStatus(pool, data) {
  try {
    const { employeeCode } = data;

    // البحث عن الموظف في جدول المستفيدين النشطين
    const activeStatus = await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .query(`
        SELECT
          cb.ID,
          cb.CarCode,
          cb.StartDate,
          c.ContractorName,
          c.CarNumber,
          c.Route
        FROM CarBeneficiaries cb
        LEFT JOIN Cars c ON cb.CarCode = c.CarCode
        WHERE cb.EmployeeCode = @employeeCode AND cb.IsActive = 1
      `);

    if (activeStatus.recordset.length > 0) {
      const status = activeStatus.recordset[0];
      return NextResponse.json({
        success: true,
        data: {
          isActive: true,
          carCode: status.CarCode,
          contractorName: status.ContractorName,
          carNumber: status.CarNumber,
          route: status.Route,
          startDate: status.StartDate ? new Date(status.StartDate).toLocaleDateString('ar-EG') : 'غير محدد'
        }
      });
    } else {
      return NextResponse.json({
        success: true,
        data: {
          isActive: false
        }
      });
    }

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في التحقق من حالة الموظف: ' + error.message
    }, { status: 500 });
  }
}

// إزالة مستفيد
async function removeBeneficiary(pool, data) {
  try {
    const { beneficiaryId } = data;

    // جلب بيانات المستفيد قبل الحذف لتحديث حقل TransportMethod في جدول Employees
    const existingBeneficiary = await pool.request()
      .input('beneficiaryId', sql.Int, beneficiaryId)
      .query('SELECT EmployeeCode FROM CarBeneficiaries WHERE ID = @beneficiaryId');

    if (existingBeneficiary.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'المستفيد غير موجود'
      }, { status: 404 });
    }

    const employeeCode = existingBeneficiary.recordset[0].EmployeeCode;

    // تعطيل المستفيد بدلاً من حذفه
    await pool.request()
      .input('beneficiaryId', sql.Int, beneficiaryId)
      .query(`
        UPDATE CarBeneficiaries
        SET IsActive = 0, EndDate = GETDATE(), UpdatedAt = GETDATE()
        WHERE ID = @beneficiaryId
      `);

    // التحقق مما إذا كان هذا الموظف لم يعد مستفيدًا نشطًا من أي سيارة أخرى
    const activeBeneficiaries = await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .query(`
        SELECT TOP 1 ID FROM CarBeneficiaries
        WHERE EmployeeCode = @employeeCode AND IsActive = 1
      `);

    if (activeBeneficiaries.recordset.length === 0) {
      // إذا لم يعد مستفيدًا نشطًا، قم بمسح بيانات المواصلات في ملف الموظف
      await updateEmployeeTransportData(pool, employeeCode, null, null);
    } else {
      // إذا كان لا يزال مستفيدًا، قم بتحديث بياناته بناءً على أحدث سيارة نشطة
      const latestBeneficiary = await pool.request()
        .input('employeeCode', sql.NVarChar, employeeCode)
        .query(`
          SELECT TOP 1
            c.CarCode,
            c.Route
          FROM CarBeneficiaries cb
          INNER JOIN Cars c ON cb.CarCode = c.CarCode
          WHERE cb.EmployeeCode = @employeeCode AND cb.IsActive = 1
          ORDER BY cb.StartDate DESC
        `);
      if (latestBeneficiary.recordset.length > 0) {
        const carInfo = latestBeneficiary.recordset[0];
        await updateEmployeeTransportData(pool, employeeCode, carInfo.CarCode, carInfo.Route);
      }
    }

    return NextResponse.json({
      success: true,
      message: 'تم إزالة المستفيد بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في إزالة المستفيد: ' + error.message
    }, { status: 500 });
  }
}

// جلب الإحصائيات
async function getStatistics(pool) {
  try {
    await setupCarTables(pool);

    const statsResult = await pool.request().query(`
      SELECT
        (SELECT COUNT(*) FROM Cars WHERE IsActive = 1) as TotalActiveCars,
        (SELECT COUNT(*) FROM Cars WHERE IsActive = 0) as TotalInactiveCars,
        (SELECT COUNT(*) FROM CarBeneficiaries WHERE IsActive = 1) as TotalActiveBeneficiaries,
        (SELECT SUM(RentAmount) FROM Cars WHERE IsActive = 1) as TotalMonthlyRent,
        (SELECT AVG(RentAmount) FROM Cars WHERE IsActive = 1) as AverageRent,
        (SELECT COUNT(DISTINCT Route) FROM Cars WHERE IsActive = 1) as TotalRoutes,
        (SELECT COUNT(DISTINCT CarType) FROM Cars WHERE IsActive = 1) as TotalCarTypes,
        (SELECT COUNT(*) FROM Cars c
         WHERE c.IsActive = 1
           AND NOT EXISTS (
             SELECT 1 FROM CarBeneficiaries cb
             WHERE cb.CarCode = c.CarCode AND cb.IsActive = 1
           )) as CarsWithoutBeneficiaries
    `);

    return NextResponse.json({
      success: true,
      data: statsResult.recordset[0]
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب الإحصائيات: ' + error.message
    }, { status: 500 });
  }
}

// جلب سيارة بواسطة المعرف
async function getCarById(pool, data) {
  try {
    const { carId } = data;

    const carResult = await pool.request()
      .input('carId', sql.Int, carId)
      .query(`
        SELECT * FROM Cars WHERE ID = @carId
      `);

    if (carResult.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'السيارة غير موجودة'
      }, { status: 404 });
    }

    const car = carResult.recordset[0];

    // جلب المستفيدين
    const beneficiariesResult = await pool.request()
      .input('carCode', sql.NVarChar, car.CarCode)
      .query(`
        SELECT * FROM CarBeneficiaries
        WHERE CarCode = @carCode
        ORDER BY IsActive DESC, StartDate DESC
      `);

    car.beneficiaries = beneficiariesResult.recordset;

    return NextResponse.json({
      success: true,
      data: car
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب بيانات السيارة: ' + error.message
    }, { status: 500 });
  }
}

// تحديث بيانات المواصلات في ملف الموظف
async function updateEmployeeTransportData(pool, employeeCode, carCode, route) {
  try {
    // اكتشاف العمود الصحيح لكود الموظف
    const { employeeCodeColumn } = await discoverCorrectColumns(pool);

    // التحقق من وجود عمود TransportRoute في جدول Employees
    const columnsResult = await pool.request().query(`
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'Employees'
      AND COLUMN_NAME IN ('TransportRoute', 'TransportMethod')
    `);

    const hasTransportRoute = columnsResult.recordset.some(col => col.COLUMN_NAME === 'TransportRoute');
    const hasTransportMethod = columnsResult.recordset.some(col => col.COLUMN_NAME === 'TransportMethod');

    let updateQuery = `UPDATE Employees SET `;
    const updateFields = [];

    if (hasTransportMethod) {
      updateFields.push('TransportMethod = @carCode');
    }
    if (hasTransportRoute) {
      updateFields.push('TransportRoute = @route');
    }

    if (updateFields.length === 0) {

      return;
    }

    updateQuery += updateFields.join(', ');
    updateQuery += ` WHERE ${employeeCodeColumn} = @employeeCode`;

    await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .input('carCode', sql.NVarChar, carCode)
      .input('route', sql.NVarChar, route)
      .query(updateQuery);

  } catch (error) {

    // لا نرمي الخطأ هنا لأن هذا ليس أساسياً لعمل نظام السيارات

  }
}
