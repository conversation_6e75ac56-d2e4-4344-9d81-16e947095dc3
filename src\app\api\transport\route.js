import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

// تحديث بيانات الموظف في جدول Employees عند إضافة/إزالة من مواصلات
async function updateEmployeeTransportData(pool, employeeCode, carCode = null, route = null) {
  try {

    if (carCode) {
      // الموظف مسجل في مواصلات - تحديث البيانات
      await pool.request()
        .input('employeeCode', sql.NVarChar, employeeCode)
        .input('carCode', sql.NVarChar, carCode)
        .input('transportMethod', sql.NVarChar, route ? `باص الشركة - ${route}` : 'باص الشركة')
        .query(`
          UPDATE Employees 
          SET 
            TransportMethod = @transportMethod,
            UpdatedAt = GETDATE()
          WHERE EmployeeID = @employeeCode 
             OR CAST(EmployeeID AS NVARCHAR) = @employeeCode
        `);

    } else {
      // الموظف تم إزالته من المواصلات - مسح البيانات
      await pool.request()
        .input('employeeCode', sql.NVarChar, employeeCode)
        .query(`
          UPDATE Employees 
          SET 
            TransportMethod = NULL,
            UpdatedAt = GETDATE()
          WHERE EmployeeID = @employeeCode 
             OR CAST(EmployeeID AS NVARCHAR) = @employeeCode
        `);

    }
  } catch (error) {

  }
}

// إضافة موظف للمواصلات
async function addEmployeeToTransport(pool, transportId, employeeCode, startDate) {
  try {

    // التحقق من صحة كود الموظف
    if (!employeeCode || employeeCode.toString().trim() === '') {
      throw new Error('كود الموظف مطلوب ولا يمكن أن يكون فارغاً');
    }

    const cleanEmployeeCode = employeeCode.toString().trim();

    // البحث عن الموظف في جدول Employees
    const employeeResult = await pool.request()
      .input('employeeCode', sql.NVarChar, cleanEmployeeCode)
      .query(`
        SELECT TOP 1
          EmployeeID,
          FullName,
          JobTitle,
          Department
        FROM Employees
        WHERE EmployeeID = @employeeCode
           OR CAST(EmployeeID AS NVARCHAR) = @employeeCode
           OR LTRIM(RTRIM(CAST(EmployeeID AS NVARCHAR))) = @employeeCode
      `);

    if (employeeResult.recordset.length === 0) {
      throw new Error(`كود الموظف ${employeeCode} غير موجود في قاعدة البيانات`);
    }

    const employee = employeeResult.recordset[0];
    console.log(`✅ تم العثور على الموظف: ${employee.FullName} (${employee.EmployeeID})`);

    // التحقق من عدم وجود الموظف في نفس المواصلات
    const existingEmployee = await pool.request()
      .input('transportId', sql.Int, transportId)
      .input('employeeCode', sql.NVarChar, employeeCode)
      .query(`
        SELECT ID FROM TransportEmployees
        WHERE TransportID = @transportId
          AND EmployeeCode = @employeeCode
          AND IsActive = 1
      `);

    if (existingEmployee.recordset.length > 0) {
      throw new Error(`الموظف ${employee.FullName} مسجل بالفعل في هذه المواصلات`);
    }

    // إدراج الموظف في المواصلات
    const insertResult = await pool.request()
      .input('transportId', sql.Int, transportId)
      .input('employeeCode', sql.NVarChar, cleanEmployeeCode)
      .input('employeeName', sql.NVarChar, employee.FullName || 'غير محدد')
      .input('jobTitle', sql.NVarChar, employee.JobTitle || 'غير محدد')
      .input('department', sql.NVarChar, employee.Department || 'غير محدد')
      .input('startDate', sql.Date, new Date(startDate))
      .query(`
        INSERT INTO TransportEmployees (
          TransportID, EmployeeCode, EmployeeName, JobTitle, Department,
          StartDate, IsActive, CreatedAt, UpdatedAt
        )
        OUTPUT INSERTED.ID
        VALUES (
          @transportId, @employeeCode, @employeeName, @jobTitle, @department,
          @startDate, 1, GETDATE(), GETDATE()
        )
      `);

    const newEmployeeId = insertResult.recordset[0].ID;
    console.log(`✅ تم إضافة الموظف للمواصلات بنجاح: ${employee.FullName} (ID: ${newEmployeeId})`);

    // جلب بيانات المواصلات لتحديث ملف الموظف
    const transportResult = await pool.request()
      .input('transportId', sql.Int, transportId)
      .query('SELECT CarCode, Route FROM Transport WHERE ID = @transportId');
    
    if (transportResult.recordset.length > 0) {
      const transport = transportResult.recordset[0];
      // تحديث بيانات المواصلات في ملف الموظف
      await updateEmployeeTransportData(pool, cleanEmployeeCode, transport.CarCode, transport.Route);
    }

    return newEmployeeId;

  } catch (error) {

    throw error;
  }
}

// إزالة موظف من المواصلات
async function removeEmployeeFromTransport(pool, employeeTransportId) {
  try {
    // التحقق من وجود الموظف وجلب بياناته
    const existingEmployee = await pool.request()
      .input('employeeTransportId', sql.Int, employeeTransportId)
      .query('SELECT ID, EmployeeCode FROM TransportEmployees WHERE ID = @employeeTransportId');

    if (existingEmployee.recordset.length === 0) {
      throw new Error('الموظف غير موجود في المواصلات');
    }

    const employee = existingEmployee.recordset[0];

    // إزالة الموظف (تعطيل بدلاً من حذف)
    await pool.request()
      .input('employeeTransportId', sql.Int, employeeTransportId)
      .query(`
        UPDATE TransportEmployees
        SET IsActive = 0, EndDate = GETDATE(), UpdatedAt = GETDATE()
        WHERE ID = @employeeTransportId
      `);

    // تحديث بيانات المواصلات في ملف الموظف (مسح البيانات)
    await updateEmployeeTransportData(pool, employee.EmployeeCode, null, null);

    return true;

  } catch (error) {

    throw error;
  }
}

export async function POST(request) {
  try {
    const { action, ...data } = await request.json();
    const pool = await getConnection();

    switch (action) {
      case 'addEmployee':
        const { transportId, employeeCode, startDate } = data;
        await addEmployeeToTransport(pool, transportId, employeeCode, startDate);
        return NextResponse.json({
          success: true,
          message: 'تم إضافة الموظف للمواصلات بنجاح'
        });

      case 'removeEmployee':
        const { employeeTransportId } = data;
        await removeEmployeeFromTransport(pool, employeeTransportId);
        return NextResponse.json({
          success: true,
          message: 'تم إزالة الموظف من المواصلات بنجاح'
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
