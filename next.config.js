/** @type {import('next').NextConfig} */
const path = require('path');

const nextConfig = {
  experimental: {
    serverComponentsExternalPackages: ['mssql']
  },

  // إعدادات الأمان
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()',
          },
        ],
      },
    ];
  },

  // تحسين تحميل الخطوط
  optimizeFonts: false, // تعطيل تحسين الخطوط لتجنب مشاكل Google Fonts

  // إعدادات TypeScript
  typescript: {
    ignoreBuildErrors: true, // تجاهل أخطاء TypeScript أثناء البناء
  },

  // إعدادات ESLint
  eslint: {
    ignoreDuringBuilds: true, // تجاهل أخطاء ESLint أثناء البناء
  },

  // إزالة header "Powered by Next.js"
  poweredByHeader: false,
  webpack: (config, { isServer }) => {
    config.externals = [...config.externals, { canvas: 'canvas' }]; // required to make pdfjs work
    config.resolve.alias['@'] = path.resolve(__dirname, './src');

    // إصلاح مشكلة ESM packages
    if (isServer) {
      config.externals.push({
        'supports-color': 'supports-color',
        'debug': 'debug'
      });
    }

    return config;
  },
  pageExtensions: ['js', 'jsx', 'ts', 'tsx'],

  // إضافة transpilePackages لحل مشكلة ESM
  transpilePackages: ['supports-color']
};

module.exports = nextConfig;
