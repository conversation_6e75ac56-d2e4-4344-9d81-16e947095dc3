'use client';
import React, { useState } from 'react';
import Link from 'next/link';

export default function Sidebar({ lang = 'ar' }) {
  const [isCollapsed, setIsCollapsed] = useState(false);

  const menuItems = [
    {
      title: { ar: 'البحث عن موظف', en: 'Employee Search' },
      icon: 'fa-search',
      link: '/employees/search',
      subItems: []
    },
    {
      title: { ar: 'الموظفين', en: 'Employees' },
      icon: 'fa-users',
      link: '/employees',
      subItems: [
        { title: { ar: 'إضافة موظف', en: 'Add Employee' }, link: '/employees/add' },
        { title: { ar: 'قائمة الموظفين', en: 'Employee List' }, link: '/employees' },
        { title: { ar: 'إدارة الموظفين', en: 'Employee Management' }, link: '/employees/management' }
      ]
    },
    {
      title: { ar: 'الحضور والإجازات', en: 'Attendance & Leaves' },
      icon: 'fa-clock',
      link: '/attendance',
      subItems: [
        { title: { ar: 'الحضور اليومي', en: 'Daily Attendance' }, link: '/attendance/daily' },
        { title: { ar: 'رفع التمام اليومي', en: 'Upload Daily Attendance' }, link: '/attendance/upload' },
        { title: { ar: 'كشف التمام الشهري', en: 'Monthly Attendance Report' }, link: '/attendance/monthly-report' },
        { title: { ar: 'كشف الرواتب الشهرية', en: 'Monthly Payroll' }, link: '/payroll/monthly' }
      ]
    },
    {
      title: { ar: 'الإجازات', en: 'Leaves' },
      icon: 'fa-calendar',
      link: '/leaves',
      subItems: [
        { title: { ar: 'لوحة تحكم الإجازات', en: 'Leaves Dashboard' }, link: '/leaves-dashboard' },
        { title: { ar: 'طلب إجازة', en: 'Request Leave' }, link: '/leaves/request' },
        { title: { ar: 'رصيد الإجازات', en: 'Leave Balance' }, link: '/leaves/balance' }
      ]
    },
    {
      title: { ar: 'الأصول', en: 'Assets' },
      icon: 'fa-building',
      link: '/asset-management',
      subItems: [
        { title: { ar: 'إضافة شقة', en: 'Add Apartment' }, link: '/add-apartment' },
        { title: { ar: 'إدارة الأصول', en: 'Asset Management' }, link: '/asset-management' }
      ]
    },
    {
      title: { ar: 'التكاليف', en: 'Costs' },
      icon: 'fa-dollar-sign',
      link: '/costs',
      subItems: [
        { title: { ar: 'تكاليف الشقق', en: 'Apartment Costs' }, link: '/costs?tab=apartments' },
        { title: { ar: 'تكاليف السيارات', en: 'Car Costs' }, link: '/costs?tab=cars' },
        { title: { ar: 'تكاليف العمالة', en: 'Worker Costs' }, link: '/costs?tab=tempWorkers' }
      ]
    }
  ];

  return (
    <div className={`fixed top-0 right-0 h-full bg-white dark:bg-gray-800 shadow-lg transition-all duration-300 ${isCollapsed ? 'w-16' : 'w-64'}`}>
      <button
        onClick={() => setIsCollapsed(!isCollapsed)}
        className="absolute -left-3 top-1/2 transform -translate-y-1/2 bg-primary text-white rounded-full p-1 hover:bg-primary-dark"
      >
        <i className={`fas fa-chevron-${isCollapsed ? 'left' : 'right'}`} />
      </button>
      
      <div className="p-4">
        <div className="space-y-4">
          {menuItems.map((item, index) => (
            <div key={index} className="relative group">
              <Link
                href={item.link}
                className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <i className={`fas ${item.icon} text-primary`} />
                {!isCollapsed && (
                  <span className="text-gray-700 dark:text-gray-200">
                    {item.title[lang]}
                  </span>
                )}
              </Link>
              
              {!isCollapsed && item.subItems && (
                <div className="mt-2 space-y-1 pr-4">
                  {item.subItems.map((subItem, subIndex) => (
                    <Link
                      key={subIndex}
                      href={subItem.link}
                      className="block p-2 text-sm text-gray-600 dark:text-gray-400 hover:text-primary dark:hover:text-primary-light rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                    >
                      {subItem.title[lang]}
                    </Link>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}