import fs from 'fs';
import { NextResponse } from 'next/server';
import path from 'path';

// مسارات الأرشيف الأساسية
const ARCHIVE_PATHS = {
  employees: 'E:\\web\\project\\archiv\\scan\\employees',
  cars: 'E:\\web\\project\\archiv\\scan\\cars',
  apartments: 'E:\\web\\project\\archiv\\scan\\apartments',
  tempWorkers: 'E:\\web\\project\\archiv\\scan\\temp-workers'
};

// إنشاء المجلدات إذا لم تكن موجودة
const ensureDirectoryExists = (dirPath) => {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
};

// إنشاء جميع المجلدات الأساسية
Object.values(ARCHIVE_PATHS).forEach(ensureDirectoryExists);

export async function POST(request) {
  try {
    const {
      action,
      section,
      subCategory,
      searchTerm,
      categoryName,
      categoryType,
      code,
      documentType,
      dateFilter,
      selectedDate,
      selectedMonth,
      selectedYear
    } = await request.json();

    switch (action) {
      case 'list':
        return await listDocuments(section, subCategory, searchTerm, dateFilter, selectedDate, selectedMonth, selectedYear);

      case 'addCategory':
        return await addCategory(section, categoryName, categoryType);

      case 'createCodeFolder':
        return await createCodeFolder(section, code);

      case 'uploadDocument':
        return await uploadDocument(section, subCategory, code, documentType, selectedDate);

      default:
        return NextResponse.json({ success: false, error: 'إجراء غير صحيح' });
    }
  } catch (error) {
    console.error('خطأ في API:', error);
    return NextResponse.json({ success: false, error: error.message });
  }
}

// جلب قائمة المستندات
async function listDocuments(section, subCategory, searchTerm) {
  try {
    const basePath = ARCHIVE_PATHS[section];
    if (!basePath) {
      return NextResponse.json({ success: false, error: 'قسم غير صحيح' });
    }

    let documents = [];
    const sectionConfig = getSectionConfig(section);

    if (sectionConfig.type === 'category-based') {
      // للأقسام المبنية على الفئات (موظفين، عمالة مؤقتة)
      if (subCategory) {
        const categoryPath = path.join(basePath, subCategory);
        if (fs.existsSync(categoryPath)) {
          documents = await getDocumentsFromDirectory(categoryPath, searchTerm);
        }
      }
    } else {
      // للأقسام المبنية على الكود (سيارات، شقق)
      if (searchTerm) {
        const codePath = path.join(basePath, searchTerm);
        if (fs.existsSync(codePath)) {
          documents = await getDocumentsFromDirectory(codePath);
        }
      } else {
        // عرض جميع المجلدات (الأكواد)
        const folders = fs.readdirSync(basePath, { withFileTypes: true })
          .filter(dirent => dirent.isDirectory())
          .map(dirent => ({
            name: dirent.name,
            type: 'folder',
            path: path.join(basePath, dirent.name),
            date: fs.statSync(path.join(basePath, dirent.name)).mtime.toLocaleDateString('ar-EG')
          }));
        documents = folders;
      }
    }

    return NextResponse.json({ success: true, data: documents });
  } catch (error) {
    console.error('خطأ في جلب المستندات:', error);
    return NextResponse.json({ success: false, error: error.message });
  }
}

// إضافة فئة جديدة
async function addCategory(section, categoryName, categoryType) {
  try {
    const basePath = ARCHIVE_PATHS[section];

    if (categoryType === 'category-based') {
      // للأقسام المبنية على الفئات - إنشاء مجلد للفئة
      const categoryPath = path.join(basePath, categoryName);
      ensureDirectoryExists(categoryPath);
    } else {
      // للأقسام المبنية على الكود - حفظ نوع المستند في ملف إعدادات
      const configPath = path.join(basePath, 'document-types.json');
      let documentTypes = [];

      if (fs.existsSync(configPath)) {
        const configData = fs.readFileSync(configPath, 'utf8');
        documentTypes = JSON.parse(configData);
      }

      if (!documentTypes.includes(categoryName)) {
        documentTypes.push(categoryName);
        fs.writeFileSync(configPath, JSON.stringify(documentTypes, null, 2));
      }
    }

    return NextResponse.json({ success: true, message: 'تم إنشاء القسم بنجاح' });
  } catch (error) {
    console.error('خطأ في إضافة الفئة:', error);
    return NextResponse.json({ success: false, error: error.message });
  }
}

// إنشاء مجلد للكود
async function createCodeFolder(section, code) {
  try {
    const basePath = ARCHIVE_PATHS[section];
    const codePath = path.join(basePath, code);

    ensureDirectoryExists(codePath);

    return NextResponse.json({ success: true, message: 'تم إنشاء مجلد الكود بنجاح' });
  } catch (error) {
    console.error('خطأ في إنشاء مجلد الكود:', error);
    return NextResponse.json({ success: false, error: error.message });
  }
}

// رفع مستند
async function uploadDocument(section, subCategory, code, documentType) {
  try {
    // سيتم تطوير هذه الوظيفة لاحقاً لرفع الملفات
    return NextResponse.json({ success: true, message: 'سيتم تطوير رفع الملفات قريباً' });
  } catch (error) {
    console.error('خطأ في رفع المستند:', error);
    return NextResponse.json({ success: false, error: error.message });
  }
}

// جلب المستندات من مجلد
async function getDocumentsFromDirectory(dirPath, searchTerm = '') {
  try {
    if (!fs.existsSync(dirPath)) {
      return [];
    }

    const files = fs.readdirSync(dirPath, { withFileTypes: true });
    let documents = [];

    for (const file of files) {
      const filePath = path.join(dirPath, file.name);
      const stats = fs.statSync(filePath);

      // تطبيق فلتر البحث إذا كان موجوداً
      if (searchTerm && !file.name.toLowerCase().includes(searchTerm.toLowerCase())) {
        continue;
      }

      documents.push({
        name: file.name,
        type: file.isDirectory() ? 'folder' : 'file',
        size: file.isFile() ? formatFileSize(stats.size) : null,
        date: stats.mtime.toLocaleDateString('ar-EG'),
        path: filePath,
        extension: file.isFile() ? path.extname(file.name) : null
      });
    }

    return documents.sort((a, b) => new Date(b.date) - new Date(a.date));
  } catch (error) {
    console.error('خطأ في جلب المستندات من المجلد:', error);
    return [];
  }
}

// تنسيق حجم الملف
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// الحصول على إعدادات القسم
function getSectionConfig(section) {
  const configs = {
    employees: {
      title: 'مذكرات الموظفين',
      type: 'category-based',
      defaultCategories: [
        'تعديل راتب',
        'طلب مستحقات',
        'إيقاف مستحقات',
        'تعديل مسمى وظيفي',
        'إجازات',
        'تحقيقات'
      ]
    },
    cars: {
      title: 'مذكرات السيارات',
      type: 'code-based',
      documentTypes: [
        'طلب إيجار',
        'طلب إيقاف',
        'تجديد رخصة',
        'صيانة',
        'حوادث',
        'تأمين'
      ]
    },
    apartments: {
      title: 'مذكرات الشقق',
      type: 'code-based',
      documentTypes: [
        'عقد إيجار',
        'طلب إخلاء',
        'صيانة',
        'شكاوى',
        'تجديد عقد',
        'تحصيل'
      ]
    },
    tempWorkers: {
      title: 'مذكرات العمالة المؤقتة',
      type: 'category-based',
      defaultCategories: [
        'تعديل الفئة',
        'إيقاف مستحقات',
        'تحقيقات',
        'تعديل أجر',
        'إنهاء خدمة',
        'مكافآت'
      ]
    }
  };

  return configs[section] || {};
}

// إنشاء الفئات الافتراضية
export async function initializeDefaultCategories() {
  try {
    const categoryBasedSections = ['employees', 'tempWorkers'];

    for (const section of categoryBasedSections) {
      const config = getSectionConfig(section);
      const basePath = ARCHIVE_PATHS[section];

      for (const category of config.defaultCategories) {
        const categoryPath = path.join(basePath, category);
        ensureDirectoryExists(categoryPath);
      }
    }

    console.log('تم إنشاء الفئات الافتراضية بنجاح');
  } catch (error) {
    console.error('خطأ في إنشاء الفئات الافتراضية:', error);
  }
}

// تشغيل إنشاء الفئات الافتراضية عند بدء التطبيق
initializeDefaultCategories();
