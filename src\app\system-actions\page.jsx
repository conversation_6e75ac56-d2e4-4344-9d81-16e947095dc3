'use client';

import { useState, useEffect } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import MainLayout from '@/components/MainLayout';
import { 
  FiActivity, FiBell, FiAlertTriangle, FiUser, FiClock, 
  FiCheck, FiX, FiRefreshCw, FiFilter, FiEye 
} from 'react-icons/fi';

export default function SystemActionsPage() {
  const { isArabic } = useLanguage();
  const [activeTab, setActiveTab] = useState('actions');
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState({
    actions: [],
    notifications: [],
    alerts: [],
    statistics: {}
  });

  // جلب البيانات
  const fetchData = async () => {
    setLoading(true);
    try {
      // جلب الإجراءات الحديثة
      const actionsResponse = await fetch('/api/notifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getActions',
          limit: 50
        })
      });
      const actionsResult = await actionsResponse.json();

      // جلب الإشعارات
      const notificationsResponse = await fetch('/api/notifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getNotifications',
          limit: 50
        })
      });
      const notificationsResult = await notificationsResponse.json();

      // جلب الإشعارات الذكية
      const smartNotificationsResponse = await fetch('/api/smart-notifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'list',
          limit: 50
        })
      });
      const smartNotificationsResult = await smartNotificationsResponse.json();

      setData({
        actions: actionsResult.success ? actionsResult.data?.actions || [] : [],
        notifications: notificationsResult.success ? notificationsResult.data?.notifications || [] : [],
        smartNotifications: smartNotificationsResult.success ? smartNotificationsResult.notifications || [] : [],
        statistics: {
          totalActions: actionsResult.data?.pagination?.total || 0,
          unreadNotifications: notificationsResult.data?.unreadCount || 0,
          totalSmartNotifications: smartNotificationsResult.notifications?.length || 0
        }
      });

    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  // تنسيق التاريخ
  const formatDate = (dateString) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      return date.toLocaleString('ar-EG', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return dateString;
    }
  };

  // أيقونة حسب نوع الإجراء
  const getActionIcon = (actionType) => {
    if (actionType?.includes('LOGIN')) return <FiUser className="text-blue-500" />;
    if (actionType?.includes('LEAVE')) return <FiClock className="text-green-500" />;
    if (actionType?.includes('EMPLOYEE')) return <FiUser className="text-purple-500" />;
    if (actionType?.includes('APPROVE')) return <FiCheck className="text-green-500" />;
    if (actionType?.includes('REJECT')) return <FiX className="text-red-500" />;
    return <FiActivity className="text-gray-500" />;
  };

  // لون حسب الأولوية مع تباين عالي
  const getPriorityColor = (priority) => {
    switch (priority?.toLowerCase()) {
      case 'urgent': return 'text-red-900 bg-red-200 border border-red-300';
      case 'high': return 'text-orange-900 bg-orange-200 border border-orange-300';
      case 'medium': return 'text-yellow-900 bg-yellow-200 border border-yellow-300';
      case 'low': return 'text-green-900 bg-green-200 border border-green-300';
      default: return 'text-gray-900 bg-gray-200 border border-gray-300';
    }
  };

  return (
    <MainLayout>
      <div className="p-6 max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            {isArabic ? 'إجراءات وإشعارات النظام' : 'System Actions & Notifications'}
          </h1>
          <p className="text-lg font-medium text-gray-700 dark:text-gray-300">
            {isArabic ? 'متابعة جميع الإجراءات والإشعارات في النظام' : 'Monitor all system actions and notifications'}
          </p>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100">
                <FiActivity className="h-6 w-6 text-blue-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-bold text-gray-800 dark:text-gray-200">إجمالي الإجراءات</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">{data.statistics.totalActions}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100">
                <FiBell className="h-6 w-6 text-green-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-bold text-gray-800 dark:text-gray-200">الإشعارات غير المقروءة</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">{data.statistics.unreadNotifications}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100">
                <FiAlertTriangle className="h-6 w-6 text-purple-600" />
              </div>
              <div className="mr-4">
                <p className="text-sm font-bold text-gray-800 dark:text-gray-200">الإشعارات الذكية</p>
                <p className="text-3xl font-bold text-gray-900 dark:text-white">{data.statistics.totalSmartNotifications}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8" aria-label="Tabs">
              <button
                onClick={() => setActiveTab('actions')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'actions'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <FiActivity className="inline-block ml-2" />
                إجراءات المستخدمين
              </button>
              <button
                onClick={() => setActiveTab('notifications')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'notifications'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <FiBell className="inline-block ml-2" />
                الإشعارات العادية
              </button>
              <button
                onClick={() => setActiveTab('smart')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'smart'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <FiAlertTriangle className="inline-block ml-2" />
                الإشعارات الذكية
              </button>
            </nav>
          </div>

          <div className="p-6">
            {/* Refresh Button */}
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                {activeTab === 'actions' && 'إجراءات المستخدمين'}
                {activeTab === 'notifications' && 'الإشعارات العادية'}
                {activeTab === 'smart' && 'الإشعارات الذكية'}
              </h3>
              <button
                onClick={fetchData}
                disabled={loading}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                <FiRefreshCw className={`ml-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                تحديث
              </button>
            </div>

            {/* Content */}
            {loading ? (
              <div className="text-center py-8">
                <FiRefreshCw className="animate-spin h-8 w-8 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-500">جاري التحميل...</p>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Actions Tab */}
                {activeTab === 'actions' && (
                  <div className="space-y-3">
                    {data.actions.length === 0 ? (
                      <p className="text-gray-500 dark:text-gray-400 text-center py-8">لا توجد إجراءات</p>
                    ) : (
                      data.actions.map((action) => (
                        <div key={action.ID} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex items-start space-x-3">
                              <div className="flex-shrink-0">
                                {getActionIcon(action.ActionType)}
                              </div>
                              <div className="flex-1 min-w-0">
                                <p className="text-sm font-bold text-gray-900 dark:text-white">
                                  {action.ActionNameAr || action.ActionType}
                                </p>
                                <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mt-1">
                                  {action.ActionDescription}
                                </p>
                                <div className="flex items-center mt-2 text-xs font-medium text-gray-600 dark:text-gray-400">
                                  <span className="bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded text-gray-800 dark:text-white">{action.UserName || action.UserCode}</span>
                                  <span className="mx-2">•</span>
                                  <span>{formatDate(action.ActionDate)}</span>
                                  {action.TargetTable && (
                                    <>
                                      <span className="mx-2">•</span>
                                      <span className="bg-blue-100 dark:bg-blue-700 px-2 py-1 rounded text-blue-800 dark:text-white">{action.TargetTable}</span>
                                    </>
                                  )}
                                </div>
                              </div>
                            </div>
                            <div className="flex-shrink-0">
                              {action.IsSuccess ? (
                                <FiCheck className="h-5 w-5 text-green-500" />
                              ) : (
                                <FiX className="h-5 w-5 text-red-500" />
                              )}
                            </div>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                )}

                {/* Smart Notifications Tab */}
                {activeTab === 'smart' && (
                  <div className="space-y-3">
                    {data.smartNotifications.length === 0 ? (
                      <p className="text-gray-500 dark:text-gray-400 text-center py-8">لا توجد إشعارات ذكية</p>
                    ) : (
                      data.smartNotifications.map((notification) => (
                        <div key={notification.ID} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center justify-between mb-2">
                                <h4 className="text-sm font-bold text-gray-900 dark:text-white">
                                  {notification.Title}
                                </h4>
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-bold ${getPriorityColor(notification.Priority)}`}>
                                  {notification.Priority}
                                </span>
                              </div>
                              <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                {notification.Message}
                              </p>
                              <div className="flex items-center text-xs font-medium text-gray-600 dark:text-gray-400">
                                <span className="bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded text-gray-800 dark:text-white">{notification.EmployeeName || notification.EmployeeID}</span>
                                <span className="mx-2">•</span>
                                <span>{formatDate(notification.CreatedAt)}</span>
                                <span className="mx-2">•</span>
                                <span className={`px-2 py-1 rounded font-bold ${
                                  notification.Status === 'pending' ? 'bg-yellow-200 text-yellow-800 dark:bg-yellow-600 dark:text-white' :
                                  notification.Status === 'read' ? 'bg-green-200 text-green-800 dark:bg-green-600 dark:text-white' :
                                  'bg-gray-200 text-gray-800 dark:bg-gray-600 dark:text-white'
                                }`}>
                                  {notification.Status}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
