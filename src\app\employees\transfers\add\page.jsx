'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  FiArrowRight,
  FiUser,
  FiCalendar,
  FiFileText,
  FiSave,
  FiX,
  FiSearch,
  FiRefreshCw,
  FiBriefcase
} from 'react-icons/fi';

export default function AddTransferPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [employees, setEmployees] = useState([]);
  const [departments, setDepartments] = useState([]);
  const [jobTitles, setJobTitles] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedEmployee, setSelectedEmployee] = useState(null);

  const [formData, setFormData] = useState({
    employeeId: '',
    employeeName: '',
    previousDepartment: '',
    newDepartment: '',
    previousJobTitle: '',
    newJobTitle: '',
    transferDate: '',
    transferReason: '',
    projectOrDepartment: '',
    createdBy: 'المدير'
  });

  // جلب البيانات المطلوبة
  useEffect(() => {
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    if (!isLoggedIn) {
      router.push('/login');
      return;
    }

    fetchEmployees();
    fetchDepartments();
  }, [router]);

  // جلب قائمة الموظفين
  const fetchEmployees = async () => {
    try {
      const response = await fetch('/api/employee-search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          searchType: 'group',
          department: '',
          jobTitle: '',
          governorate: ''
        })
      });

      const data = await response.json();
      if (data.success) {
        setEmployees(data.employees || []);
      }
    } catch (error) {

    }
  };

  // جلب الأقسام والمسميات الوظيفية
  const fetchDepartments = async () => {
    try {
      const response = await fetch('/api/departments');
      const data = await response.json();

      if (data.success) {
        setDepartments(data.data.departments || []);
        setJobTitles(data.data.jobTitles || []);
      }
    } catch (error) {

    }
  };

  // تصفية الموظفين حسب البحث
  const filteredEmployees = employees.filter(emp =>
    emp.EmployeeName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    emp.EmployeeCode?.toString().includes(searchTerm) ||
    emp.Department?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // اختيار موظف
  const selectEmployee = (employee) => {
    setSelectedEmployee(employee);
    setFormData(prev => ({
      ...prev,
      employeeId: employee.EmployeeCode,
      employeeName: employee.EmployeeName,
      previousDepartment: employee.Department || '',
      previousJobTitle: employee.JobTitle || ''
    }));
    setSearchTerm('');
  };

  // معالجة تغيير البيانات
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // إرسال النموذج
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.employeeId || !formData.newDepartment || !formData.transferDate) {
      setError('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await fetch('/api/transfers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('تم إضافة النقل بنجاح!');
        setTimeout(() => {
          router.push('/employees/transfers');
        }, 2000);
      } else {
        setError(data.error || 'حدث خطأ في إضافة النقل');
      }
    } catch (error) {

      setError('حدث خطأ في إضافة النقل');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* رأس الصفحة */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <FiArrowRight className="text-2xl text-blue-600" />
              <h1 className="text-3xl font-bold text-gray-800">إضافة نقل جديد</h1>
            </div>
            <button
              onClick={() => router.push('/employees/transfers')}
              className="text-gray-600 hover:text-gray-800 flex items-center gap-2"
            >
              <FiX />
              إلغاء
            </button>
          </div>
        </div>

        {/* رسائل النجاح والخطأ */}
        {success && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <p className="text-green-800">✅ {success}</p>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <p className="text-red-800">❌ {error}</p>
          </div>
        )}

        {/* نموذج إضافة النقل */}
        <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-sm p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* اختيار الموظف */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                اختيار الموظف *
              </label>

              {selectedEmployee ? (
                <div className="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <FiUser className="text-blue-600" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{selectedEmployee.EmployeeName}</div>
                      <div className="text-sm text-gray-600">
                        #{selectedEmployee.EmployeeCode} - {selectedEmployee.Department}
                      </div>
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={() => {
                      setSelectedEmployee(null);
                      setFormData(prev => ({
                        ...prev,
                        employeeId: '',
                        employeeName: '',
                        previousDepartment: '',
                        previousJobTitle: ''
                      }));
                    }}
                    className="text-red-600 hover:text-red-800"
                  >
                    <FiX />
                  </button>
                </div>
              ) : (
                <div>
                  <div className="relative mb-4">
                    <FiSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      placeholder="ابحث عن موظف بالاسم أو الكود..."
                      className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>

                  {searchTerm && (
                    <div className="max-h-60 overflow-y-auto border border-gray-200 rounded-lg">
                      {filteredEmployees.length > 0 ? (
                        filteredEmployees.slice(0, 10).map((employee, index) => (
                          <button
                            key={index}
                            type="button"
                            onClick={() => selectEmployee(employee)}
                            className="w-full p-3 text-right hover:bg-gray-50 border-b border-gray-100 last:border-b-0 transition-colors"
                          >
                            <div className="flex items-center justify-between">
                              <div>
                                <div className="font-medium text-gray-900">{employee.EmployeeName}</div>
                                <div className="text-sm text-gray-600">
                                  {employee.Department} - {employee.JobTitle}
                                </div>
                              </div>
                              <div className="text-blue-600 font-bold">#{employee.EmployeeCode}</div>
                            </div>
                          </button>
                        ))
                      ) : (
                        <div className="p-4 text-center text-gray-500">
                          لا توجد نتائج للبحث
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* القسم السابق */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                القسم السابق
              </label>
              <input
                type="text"
                value={formData.previousDepartment}
                readOnly
                className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600"
              />
            </div>

            {/* القسم الجديد */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                القسم الجديد *
              </label>
              <select
                name="newDepartment"
                value={formData.newDepartment}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">اختر القسم الجديد</option>
                {departments.map((dept, index) => (
                  <option key={index} value={dept.name}>
                    {dept.name}
                  </option>
                ))}
              </select>
            </div>

            {/* المسمى السابق */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                المسمى الوظيفي السابق
              </label>
              <input
                type="text"
                value={formData.previousJobTitle}
                readOnly
                className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600"
              />
            </div>

            {/* المسمى الجديد */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                المسمى الوظيفي الجديد
              </label>
              <select
                name="newJobTitle"
                value={formData.newJobTitle}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">اختر المسمى الجديد (اختياري)</option>
                {jobTitles.map((job, index) => (
                  <option key={index} value={job.name}>
                    {job.name}
                  </option>
                ))}
              </select>
            </div>

            {/* المشروع أو الإدارة المنقول إليها */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                المشروع أو الإدارة المنقول إليها
              </label>
              <div className="relative">
                <FiBriefcase className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  name="projectOrDepartment"
                  value={formData.projectOrDepartment}
                  onChange={handleInputChange}
                  placeholder="اسم المشروع أو الإدارة..."
                  className="w-full pr-10 pl-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* تاريخ النقل */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                تاريخ النقل *
              </label>
              <input
                type="date"
                name="transferDate"
                value={formData.transferDate}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* سبب النقل */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                سبب النقل
              </label>
              <textarea
                name="transferReason"
                value={formData.transferReason}
                onChange={handleInputChange}
                rows="4"
                placeholder="اكتب سبب النقل..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* أزرار الإجراءات */}
          <div className="flex gap-4 mt-8">
            <button
              type="submit"
              disabled={loading}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2 disabled:opacity-50"
            >
              {loading ? <FiRefreshCw className="animate-spin" /> : <FiSave />}
              {loading ? 'جاري الحفظ...' : 'حفظ النقل'}
            </button>

            <button
              type="button"
              onClick={() => router.push('/employees/transfers')}
              className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-2"
            >
              <FiX />
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
