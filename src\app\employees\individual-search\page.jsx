// Simple Design: 2025-06-18T11:18:41.081Z
// Unified Design: 2025-06-18T11:09:38.709Z
// Contrast Fixed: 2025-06-18T11:02:07.138Z
// Updated: 2025-06-18T10:17:59.592Z
'use client';

import React, { useState, useEffect, useRef } from 'react';
import { FiSearch, FiUser, FiEdit, FiTrash2, FiArchive, FiX } from 'react-icons/fi';

export default function IndividualEmployeeSearch() {
  const [searchQuery, setSearchQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [employeePhoto, setEmployeePhoto] = useState(null);
  const [employeeDocuments, setEmployeeDocuments] = useState([]);
  const [employeeAssets, setEmployeeAssets] = useState(null);
  const [showDocuments, setShowDocuments] = useState(false);
  const [showAssets, setShowAssets] = useState(false);
  const [loading, setLoading] = useState(false);
  const [assetsLoading, setAssetsLoading] = useState(false);
  const [error, setError] = useState('');

  const searchInputRef = useRef(null);
  const suggestionsRef = useRef(null);

  // البحث التفاعلي
  useEffect(() => {
    const searchTimeout = setTimeout(async () => {
      if (searchQuery.trim().length > 0) {
        try {
          const response = await fetch('/api/employee-live-search', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json; charset=utf-8',
              'Accept': 'application/json; charset=utf-8'
            },
            body: JSON.stringify({ searchTerm: searchQuery, limit: 8 })
          });

          const data = await response.json();
          if (data.success) {
            // تحويل البيانات إلى تنسيق الاقتراحات
            const formattedSuggestions = (data.data || []).map(emp => ({
              id: emp.employeeCode || emp.EmployeeCode,
              name: emp.employeeName || emp.EmployeeName,
              jobTitle: emp.jobTitle || emp.JobTitle || 'غير محدد',
              department: emp.department || emp.Department || 'غير محدد',
              displayText: `${emp.employeeName || emp.EmployeeName} (${emp.employeeCode || emp.EmployeeCode})`
            }));
            setSuggestions(formattedSuggestions || []);
            setShowSuggestions(formattedSuggestions && formattedSuggestions.length > 0);
          } else {
            setSuggestions([]);
            setShowSuggestions(false);
          }
        } catch (error) {

          setSuggestions([]);
          setShowSuggestions(false);
        }
      } else {
        setSuggestions([]);
        setShowSuggestions(false);
      }
    }, 300);

    return () => clearTimeout(searchTimeout);
  }, [searchQuery]);

  // إخفاء الاقتراحات عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target) &&
          searchInputRef.current && !searchInputRef.current.contains(event.target)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // اختيار موظف من الاقتراحات
  const selectEmployee = async (suggestion) => {
    setLoading(true);
    setError('');
    setShowSuggestions(false);
    setSearchQuery(suggestion.displayText);

    try {
      // جلب بيانات الموظف الكاملة باستخدام API محدث
      const response = await fetch('/api/employee-search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
          'Accept': 'application/json; charset=utf-8'
        },
        body: JSON.stringify({
          searchType: 'individual',
          searchValue: suggestion.id
        })
      });

      const data = await response.json();

      if (data.success) {
        let employee;
        if (data.employee) {
          employee = data.employee;
        } else if (data.employees && data.employees.length > 0) {
          employee = data.employees[0];
        } else {
          throw new Error('لم يتم العثور على بيانات الموظف');
        }

        setSelectedEmployee(employee);

        // جلب الصورة الشخصية
        await fetchEmployeePhoto(employee.EmployeeCode || employee.Num || employee.EmployeeID);

        // جلب أصول الموظف (السيارات والشقق)
        await fetchEmployeeAssets(employee.EmployeeCode || employee.Num || employee.EmployeeID);
      } else {
        setError(data.message || 'لم يتم العثور على بيانات الموظف');
      }
    } catch (error) {
      setError('خطأ في جلب بيانات الموظف: ' + error.message);

    } finally {
      setLoading(false);
    }
  };

  // جلب الصورة الشخصية
  const fetchEmployeePhoto = async (employeeCode) => {
    try {
      const response = await fetch('/api/employee-archive', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getPhoto',
          employeeId: employeeCode
        })
      });

      const data = await response.json();
      if (data.success && data.photoPath) {
        setEmployeePhoto(data.photoPath);
      }
    } catch (error) {

    }
  };

  // جلب أصول الموظف (السيارات والشقق)
  const fetchEmployeeAssets = async (employeeCode) => {
    try {
      setAssetsLoading(true);
      const response = await fetch(`/api/employee-assets?employeeCode=${employeeCode}`);
      const data = await response.json();

      if (data.success) {
        setEmployeeAssets(data.data);

      } else {

      }
    } catch (error) {

    } finally {
      setAssetsLoading(false);
    }
  };

  // جلب مستندات الموظف
  const fetchEmployeeDocuments = async (employeeCode) => {
    try {
      setLoading(true);
      const response = await fetch('/api/employee-archive', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getDocuments',
          employeeId: employeeCode
        })
      });

      const data = await response.json();
      if (data.success) {
        setEmployeeDocuments(data.documents);
        setShowDocuments(true);
      } else {
        setError('خطأ في جلب المستندات');
      }
    } catch (error) {
      setError('خطأ في جلب المستندات');

    } finally {
      setLoading(false);
    }
  };

  // مسح البحث
  const clearSearch = () => {
    setSearchQuery('');
    setSelectedEmployee(null);
    setEmployeePhoto(null);
    setEmployeeAssets(null);
    setSuggestions([]);
    setShowSuggestions(false);
    setShowDocuments(false);
    setShowAssets(false);
    setEmployeeDocuments([]);
    setError('');
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* رأس الصفحة */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-3xl font-bold text-gray-800 flex items-center gap-3">
              <FiSearch className="text-blue-600" />
              البحث عن موظف
            </h1>
          </div>

          {/* شريط البحث */}
          <div className="relative">
            <div className="relative">
              <input
                ref={searchInputRef}
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="ابحث بالكود أو الاسم..."
                className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
              />
              <FiSearch className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-xl" />
              {searchQuery && (
                <button
                  onClick={clearSearch}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <FiX className="text-xl" />
                </button>
              )}
            </div>

            {/* الاقتراحات */}
            {showSuggestions && suggestions && suggestions.length > 0 && (
              <div
                ref={suggestionsRef}
                className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-50 mt-1 max-h-80 overflow-y-auto"
              >
                {(suggestions || []).map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => selectEmployee(suggestion)}
                    className="w-full px-4 py-3 text-right hover:bg-blue-50 border-b border-gray-100 last:border-b-0 transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-semibold text-gray-800">{suggestion.name}</div>
                        <div className="text-sm text-gray-600">{suggestion.jobTitle} - {suggestion.department}</div>
                      </div>
                      <div className="text-blue-600 font-bold">#{suggestion.id}</div>
                    </div>
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* رسالة نوع البحث */}
          {searchQuery && (
            <div className="mt-2 text-sm text-gray-600">
              {/^\d+$/.test(searchQuery.trim()) ?
                '🔢 البحث بالكود' :
                '📝 البحث بالاسم'
              }
            </div>
          )}
        </div>

        {/* رسائل الخطأ */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mb-6">
            {error}
          </div>
        )}

        {/* مؤشر التحميل */}
        {loading && (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="mt-2 text-gray-600">جاري التحميل...</p>
          </div>
        )}

        {/* بيانات الموظف */}
        {selectedEmployee && !loading && (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* الصورة الشخصية والأزرار */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="text-center mb-6">
                  {employeePhoto ? (
                    <img
                      src={`/${employeePhoto}`}
                      alt="صورة الموظف"
                      className="w-32 h-32 rounded-full mx-auto object-cover border-4 border-blue-100"
                      onError={(e) => {
                        e.target.style.display = 'none';
                        e.target.nextSibling.style.display = 'flex';
                      }}
                    />
                  ) : null}
                  <div
                    className="w-32 h-32 rounded-full mx-auto bg-gray-200 flex items-center justify-center border-4 border-blue-100"
                    style={{ display: employeePhoto ? 'none' : 'flex' }}
                  >
                    <FiUser className="text-4xl text-gray-400" />
                  </div>
                  <h3 className="mt-4 text-xl font-bold text-gray-800">{selectedEmployee.EmployeeName}</h3>
                  <p className="text-gray-600">#{selectedEmployee.EmployeeCode}</p>
                </div>

                {/* الأزرار */}
                <div className="space-y-3">
                  <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2">
                    <FiEdit />
                    تعديل البيانات
                  </button>

                  <button
                    onClick={() => fetchEmployeeDocuments(selectedEmployee.EmployeeCode || selectedEmployee.Num)}
                    className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center gap-2"
                  >
                    <FiArchive />
                    أرشيف المستندات
                  </button>

                  <button
                    onClick={() => setShowAssets(true)}
                    className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors flex items-center justify-center gap-2"
                  >
                    🏠🚗
                    السيارات والشقق
                  </button>

                  <button className="w-full bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors flex items-center justify-center gap-2">
                    <FiTrash2 />
                    حذف الموظف
                  </button>
                </div>
              </div>
            </div>

            {/* بيانات الموظف */}
            <div className="lg:col-span-3">
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h2 className="text-2xl font-bold text-gray-800 mb-6">بيانات الموظف</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {/* البيانات الأساسية */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-blue-600 border-b border-blue-200 pb-2">البيانات الأساسية</h3>
                    <div><span className="font-medium">الكود:</span> {selectedEmployee.EmployeeCode}</div>
                    <div><span className="font-medium">الاسم:</span> {selectedEmployee.EmployeeName}</div>
                    <div><span className="font-medium">المسمى الوظيفي:</span> {selectedEmployee.JobTitle}</div>
                    <div><span className="font-medium">القسم:</span> {selectedEmployee.Department}</div>
                    <div><span className="font-medium">المدير المباشر:</span> {selectedEmployee.DirectManager}</div>
                  </div>

                  {/* البيانات الشخصية */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-green-600 border-b border-green-200 pb-2">البيانات الشخصية</h3>
                    <div><span className="font-medium">الرقم القومي:</span> {selectedEmployee.NationalID}</div>
                    <div><span className="font-medium">تاريخ الميلاد:</span> {selectedEmployee.BirthDate ? new Date(selectedEmployee.BirthDate).toLocaleDateString('ar-EG') : 'غير محدد'}</div>
                    <div><span className="font-medium">النوع:</span> {selectedEmployee.Gender}</div>
                    <div><span className="font-medium">المحافظة:</span> {selectedEmployee.Governorate}</div>
                    <div><span className="font-medium">الحالة الاجتماعية:</span> {selectedEmployee.MaritalStatus}</div>
                  </div>

                  {/* بيانات الاتصال */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-purple-600 border-b border-purple-200 pb-2">بيانات الاتصال</h3>
                    <div><span className="font-medium">الجوال:</span> {selectedEmployee.Mobile}</div>
                    <div><span className="font-medium">البريد الإلكتروني:</span> {selectedEmployee.Email || 'غير محدد'}</div>
                    <div><span className="font-medium">رقم الطوارئ:</span> {selectedEmployee.EmergencyNumber || 'غير محدد'}</div>
                    <div><span className="font-medium">صلة القرابة:</span> {selectedEmployee.Kinship || 'غير محدد'}</div>
                  </div>
                </div>

                {/* الصف الثاني - التأمينات والسكن والمواصلات */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-6">
                  {/* التأمينات */}
                  <div className="space-y-3">
                    <h3 className="text-lg font-semibold text-orange-600 border-b border-orange-200 pb-2">التأمينات</h3>

                    <div>
                      <span className="font-medium">التأمين الاجتماعي:</span>
                      <span className={`ml-2 px-2 py-1 rounded text-sm font-medium ${
                        (employeeAssets?.insurance?.socialInsurance && employeeAssets.insurance.socialInsurance !== 'غير محدد') ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {(employeeAssets?.insurance?.socialInsurance && employeeAssets.insurance.socialInsurance !== 'غير محدد') ? employeeAssets.insurance.socialInsurance : 'غير محدد'}
                      </span>
                    </div>
                    <div><span className="font-medium">الرقم التأميني:</span> {employeeAssets?.insurance?.socialInsuranceNumber || 'غير محدد'}</div>
                    <div><span className="font-medium">تاريخ التأمين:</span> {employeeAssets?.insurance?.socialInsuranceDate ? new Date(employeeAssets.insurance.socialInsuranceDate).toLocaleDateString('ar-EG') : 'غير محدد'}</div>
                    <div>
                      <span className="font-medium">التأمين الطبي:</span>
                      <span className={`ml-2 px-2 py-1 rounded text-sm font-medium ${
                        (employeeAssets?.insurance?.medicalInsurance && employeeAssets.insurance.medicalInsurance !== 'غير محدد') ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {(employeeAssets?.insurance?.medicalInsurance && employeeAssets.insurance.medicalInsurance !== 'غير محدد') ? employeeAssets.insurance.medicalInsurance : 'غير محدد'}
                      </span>
                    </div>
                    <div><span className="font-medium">رقم التأمين الطبي:</span> {employeeAssets?.insurance?.medicalInsuranceNumber || 'غير محدد'}</div>
                  </div>

                  {/* السكن */}
                  <div className="space-y-3">
                    <h3 className="text-lg font-semibold text-teal-600 border-b border-teal-200 pb-2">بيانات السكن</h3>

                    <div>
                      <span className="font-medium">وسيلة المواصلات:</span>
                      <span className={`ml-2 px-2 py-1 rounded text-sm font-medium ${
                        employeeAssets?.apartments && employeeAssets.apartments.length > 0 ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'
                      }`}>
                        {employeeAssets?.apartments && employeeAssets.apartments.length > 0 ? 'سكن الشركة' : 'سكن خاص'}
                      </span>
                    </div>
                    <div><span className="font-medium">كود السكن:</span> <span className="font-bold text-blue-600">{employeeAssets?.apartments && employeeAssets.apartments.length > 0 ? employeeAssets.apartments[0].apartmentCode : 'غير محدد'}</span></div>

                    {employeeAssets?.apartments && employeeAssets.apartments.length > 0 && (
                      <>
                        <div><span className="font-medium">اسم المؤجر:</span> <span className="text-green-600 font-medium">{employeeAssets.apartments[0].landlordName}</span></div>
                        <div><span className="font-medium">العنوان:</span> {employeeAssets.apartments[0].address}</div>
                        <div><span className="font-medium">الإيجار الشهري:</span> <span className="text-blue-600 font-bold">{employeeAssets.apartments[0].rentAmount} جنيه</span></div>
                        {employeeAssets.apartments[0].startDate && (
                          <div><span className="font-medium">تاريخ بداية الاستفادة:</span> <span className="text-purple-600">{new Date(employeeAssets.apartments[0].startDate).toLocaleDateString('ar-EG')}</span></div>
                        )}
                      </>
                    )}
                  </div>

                  {/* المواصلات */}
                  <div className="space-y-3">
                    <h3 className="text-lg font-semibold text-indigo-600 border-b border-indigo-200 pb-2">بيانات المواصلات</h3>

                    <div>
                      <span className="font-medium">وسيلة المواصلات:</span>
                      <span className={`ml-2 px-2 py-1 rounded text-sm font-medium ${
                        employeeAssets?.cars && employeeAssets.cars.length > 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {employeeAssets?.cars && employeeAssets.cars.length > 0 ? 'مسجل بسيارة' : 'غير مسجل بسيارة'}
                      </span>
                    </div>
                    <div><span className="font-medium">كود السيارة:</span> <span className="font-bold text-blue-600">{employeeAssets?.cars && employeeAssets.cars.length > 0 ? employeeAssets.cars[0].carCode : 'غير محدد'}</span></div>

                    {employeeAssets?.cars && employeeAssets.cars.length > 0 && (
                      <>
                        <div><span className="font-medium">خط السير:</span> <span className="text-green-600 font-medium">{employeeAssets.cars[0].route}</span></div>
                        <div><span className="font-medium">اسم المقاول:</span> <span className="text-orange-600 font-medium">{employeeAssets.cars[0].contractorName}</span></div>
                        <div><span className="font-medium">رقم السيارة:</span> <span className="text-blue-600 font-bold">{employeeAssets.cars[0].carNumber}</span></div>
                        <div><span className="font-medium">نوع السيارة:</span> <span className="text-purple-600">{employeeAssets.cars[0].carType}</span></div>
                        {employeeAssets.cars[0].startDate && (
                          <div><span className="font-medium">تاريخ بداية الاستفادة:</span> <span className="text-purple-600">{new Date(employeeAssets.cars[0].startDate).toLocaleDateString('ar-EG')}</span></div>
                        )}
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* نافذة الأصول (السيارات والشقق) */}
        {showAssets && employeeAssets && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
              <div className="flex items-center justify-between p-6 border-b">
                <h3 className="text-xl font-bold text-gray-800">أصول الموظف: {selectedEmployee?.EmployeeName || selectedEmployee?.FullName}</h3>
                <button
                  onClick={() => setShowAssets(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <FiX className="text-2xl" />
                </button>
              </div>

              <div className="p-6 overflow-y-auto max-h-[70vh]">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* السيارات */}
                  <div className="bg-blue-50 rounded-lg p-6">
                    <h4 className="text-lg font-bold text-blue-800 mb-4 flex items-center gap-2">
                      🚗 السيارات المسجلة ({employeeAssets.cars?.length || 0})
                    </h4>
                    {employeeAssets.cars && employeeAssets.cars.length > 0 ? (
                      <div className="space-y-4">
                        {employeeAssets.cars.map((car, index) => (
                          <div key={index} className="bg-white rounded-lg p-4 border border-blue-200">
                            <div className="grid grid-cols-2 gap-3 text-sm">
                              <div><span className="font-medium">كود السيارة:</span> <span className="text-blue-600 font-bold">{car.carCode}</span></div>
                              <div><span className="font-medium">رقم السيارة:</span> {car.carNumber}</div>
                              <div><span className="font-medium">اسم الخط:</span> <span className="text-green-600 font-medium">{car.route}</span></div>
                              <div><span className="font-medium">المقاول:</span> {car.contractorName}</div>
                              <div><span className="font-medium">نوع السيارة:</span> {car.carType}</div>
                              <div><span className="font-medium">الموديل:</span> {car.carModel} ({car.manufactureYear})</div>
                              <div><span className="font-medium">تاريخ البداية:</span> {car.startDate ? new Date(car.startDate).toLocaleDateString('ar-EG') : 'غير محدد'}</div>
                              <div><span className="font-medium">الحالة:</span>
                                <span className={`ml-1 px-2 py-1 rounded text-xs ${car.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                  {car.isActive ? 'نشط' : 'غير نشط'}
                                </span>
                              </div>
                            </div>
                            {car.notes && (
                              <div className="mt-3 p-2 bg-gray-50 rounded text-sm">
                                <span className="font-medium">ملاحظات:</span> {car.notes}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <div className="text-4xl mb-2">🚗</div>
                        <p>لا توجد سيارات مسجلة لهذا الموظف</p>
                      </div>
                    )}
                  </div>

                  {/* الشقق */}
                  <div className="bg-green-50 rounded-lg p-6">
                    <h4 className="text-lg font-bold text-green-800 mb-4 flex items-center gap-2">
                      🏠 الشقق المسجلة ({employeeAssets.apartments?.length || 0})
                    </h4>
                    {employeeAssets.apartments && employeeAssets.apartments.length > 0 ? (
                      <div className="space-y-4">
                        {employeeAssets.apartments.map((apt, index) => (
                          <div key={index} className="bg-white rounded-lg p-4 border border-green-200">
                            <div className="grid grid-cols-1 gap-3 text-sm">
                              <div><span className="font-medium">كود الشقة:</span> <span className="text-green-600 font-bold">{apt.apartmentCode}</span></div>
                              <div><span className="font-medium">المالك:</span> {apt.landlordName}</div>
                              <div><span className="font-medium">العنوان:</span> {apt.address}</div>
                              <div className="grid grid-cols-2 gap-3">
                                <div><span className="font-medium">الإيجار:</span> {apt.rentAmount} جنيه</div>
                                <div><span className="font-medium">التأمين:</span> {apt.insuranceAmount || 0} جنيه</div>
                              </div>
                              <div className="grid grid-cols-2 gap-3">
                                <div><span className="font-medium">تاريخ البداية:</span> {apt.startDate ? new Date(apt.startDate).toLocaleDateString('ar-EG') : 'غير محدد'}</div>
                                <div><span className="font-medium">الحالة:</span>
                                  <span className={`ml-1 px-2 py-1 rounded text-xs ${apt.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                    {apt.isActive ? 'نشط' : 'غير نشط'}
                                  </span>
                                </div>
                              </div>
                            </div>
                            {apt.notes && (
                              <div className="mt-3 p-2 bg-gray-50 rounded text-sm">
                                <span className="font-medium">ملاحظات:</span> {apt.notes}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <div className="text-4xl mb-2">🏠</div>
                        <p>لا توجد شقق مسجلة لهذا الموظف</p>
                      </div>
                    )}
                  </div>
                </div>

                {/* ملخص الأصول */}
                <div className="mt-6 bg-gray-50 rounded-lg p-4">
                  <h4 className="text-lg font-bold text-gray-800 mb-3">ملخص الأصول</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                    <div className="bg-blue-100 rounded-lg p-3">
                      <div className="text-2xl font-bold text-blue-600">{employeeAssets.cars?.length || 0}</div>
                      <div className="text-sm text-blue-800">سيارات</div>
                    </div>
                    <div className="bg-green-100 rounded-lg p-3">
                      <div className="text-2xl font-bold text-green-600">{employeeAssets.apartments?.length || 0}</div>
                      <div className="text-sm text-green-800">شقق</div>
                    </div>
                    <div className="bg-orange-100 rounded-lg p-3">
                      <div className="text-2xl font-bold text-orange-600">{employeeAssets.insurance?.socialInsurance ? '✓' : '✗'}</div>
                      <div className="text-sm text-orange-800">تأمين اجتماعي</div>
                    </div>
                    <div className="bg-purple-100 rounded-lg p-3">
                      <div className="text-2xl font-bold text-purple-600">{employeeAssets.insurance?.medicalInsurance ? '✓' : '✗'}</div>
                      <div className="text-sm text-purple-800">تأمين طبي</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* نافذة المستندات */}
        {showDocuments && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
              <div className="flex items-center justify-between p-6 border-b">
                <h3 className="text-xl font-bold text-gray-800">أرشيف مستندات {selectedEmployee?.EmployeeName || selectedEmployee?.FullName}</h3>
                <button
                  onClick={() => setShowDocuments(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <FiX className="text-2xl" />
                </button>
              </div>

              <div className="p-6 overflow-y-auto max-h-[70vh]">
                {employeeDocuments.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {employeeDocuments.map((doc, index) => (
                      <div key={index} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div className="flex items-center gap-3 mb-3">
                          <i className={`fas ${doc.Icon} text-2xl`} style={{ color: doc.Color }}></i>
                          <div>
                            <h4 className="font-semibold text-gray-800">{doc.DocumentType}</h4>
                            <p className="text-sm text-gray-600">{doc.Path}</p>
                          </div>
                        </div>
                        <button className="w-full bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 transition-colors">
                          عرض المستند
                        </button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <FiArchive className="text-6xl text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-600">لا توجد مستندات مؤرشفة لهذا الموظف</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
