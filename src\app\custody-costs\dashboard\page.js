'use client';

import { useState, useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import MainLayout from '@/components/MainLayout';
import {
  BarChart3,
  PieChart,
  TrendingUp,
  Calendar,
  Filter,
  RefreshCw,
  DollarSign,
  FileText
} from 'lucide-react';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, LineElement, PointElement, ArcElement, Title, Tooltip, Legend, Filler } from 'chart.js';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { Bar, Line, Doughnut } from 'react-chartjs-2';

ChartJS.register(CategoryScale, LinearScale, BarElement, LineElement, PointElement, ArcElement, Title, Tooltip, Legend, Filler, ChartDataLabels);

export default function CustodyCostsDashboardSimple() {
  const { isDarkMode } = useTheme();

  const [loading, setLoading] = useState(true);
  const [costs, setCosts] = useState([]);
  const [selectedMainCategory, setSelectedMainCategory] = useState(null);
  const [selectedDoughnutCategory, setSelectedDoughnutCategory] = useState(null);
  const [categories, setCategories] = useState([]);

  // فلاتر
  const [filters, setFilters] = useState({
    mainCategoryId: '',
    subCategoryId: '',
    month: '',
    year: new Date().getFullYear().toString()
  });

  // إحصائيات
  const [stats, setStats] = useState({
    totalAmount: 0,
    totalCount: 0,
    avgAmount: 0,
    byCategory: [],
    byMonth: []
  });

  // بيانات وهمية للرسوم البيانية (بالجنيه المصري)
  const [chartData, setChartData] = useState({
    monthlyTrend: {
      labels: ['وجبات', 'مشروبات ونظافة', 'مواصلات', 'مشتريات', 'شقق', 'سيارات', 'خدمات'],
      datasets: [
        {
          label: 'التكاليف الشهرية (ج.م)',
          data: [45000, 32000, 38000, 28000, 65000, 78000, 42000],
          backgroundColor: [
            'rgba(239, 68, 68, 0.8)',   // أحمر - وجبات
            'rgba(249, 115, 22, 0.8)',  // برتقالي - مشروبات
            'rgba(234, 179, 8, 0.8)',   // أصفر - مواصلات
            'rgba(34, 197, 94, 0.8)',   // أخضر - مشتريات
            'rgba(59, 130, 246, 0.8)',  // أزرق - شقق
            'rgba(139, 92, 246, 0.8)',  // بنفسجي - سيارات
            'rgba(236, 72, 153, 0.8)'   // وردي - خدمات
          ],
          borderColor: [
            'rgb(239, 68, 68)',
            'rgb(249, 115, 22)',
            'rgb(234, 179, 8)',
            'rgb(34, 197, 94)',
            'rgb(59, 130, 246)',
            'rgb(139, 92, 246)',
            'rgb(236, 72, 153)'
          ],
          borderWidth: 2
        }
      ]
    },
    categoryDistribution: {
      labels: ['وجبات', 'مشروبات ونظافة', 'مواصلات', 'مشتريات', 'شقق', 'سيارات', 'خدمات'],
      datasets: [
        {
          data: [360000, 255000, 285000, 225000, 540000, 660000, 330000],
          backgroundColor: [
            '#ef4444', '#f97316', '#eab308', '#22c55e', '#3b82f6', '#8b5cf6', '#ec4899'
          ],
          borderWidth: 2,
          borderColor: '#ffffff'
        }
      ]
    },
    custodyTurnover: {
      labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'],
      datasets: [
        {
          label: 'معدل دوران العُهدة',
          data: [1.4, 1.6, 1.5, 1.9, 1.7, 2.1, 2.2, 1.8, 1.6, 1.9, 2.0, 2.3],
          backgroundColor: 'rgba(139, 92, 246, 0.8)',
          borderColor: 'rgb(139, 92, 246)',
          borderWidth: 2
        }
      ]
    }
  });

  useEffect(() => {
    fetchInitialData();
  }, []);

  useEffect(() => {
    if (costs.length > 0) {
      updateStats();
    }
  }, [costs, filters]);

  // مراقبة تغيير البند المختار
  useEffect(() => {
    if (costs.length > 0) {
      if (selectedMainCategory) {
        updateSubCategoryChart(selectedMainCategory);
      } else {
        updateMainCategoryChart();
      }
    }
  }, [selectedMainCategory]);

  // مراقبة تغيير البند المختار في الرسم الدائري
  useEffect(() => {
    if (costs.length > 0) {
      if (selectedDoughnutCategory) {
        updateDoughnutSubCategoryChart(selectedDoughnutCategory);
      } else {
        updateDoughnutMainCategoryChart();
      }
    }
  }, [selectedDoughnutCategory]);

  // جلب البيانات الأولية
  const fetchInitialData = async () => {
    try {
      setLoading(true);

      const [costsRes, categoriesRes] = await Promise.all([
        fetch('/api/project-custody-costs?action=getCosts&limit=1000'),
        fetch('/api/project-custody-costs?action=getCategories')
      ]);

      const [costsData, categoriesData] = await Promise.all([
        costsRes.json(),
        categoriesRes.json()
      ]);

      if (costsData.success) {
        setCosts(costsData.costs || []);
      }

      if (categoriesData.success) {
        setCategories(categoriesData.data?.main || []);
      }

    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  // فلترة التكاليف
  const getFilteredCosts = () => {
    return costs.filter(cost => {
      // فلتر البند الرئيسي
      if (filters.mainCategoryId && cost.MainCategoryID != filters.mainCategoryId) {
        return false;
      }

      // فلتر البند الفرعي
      if (filters.subCategoryId && cost.SubCategoryID != filters.subCategoryId) {
        return false;
      }

      // فلتر الشهر والسنة
      if (filters.month || filters.year) {
        const costDate = new Date(cost.CostDate);
        const costYear = costDate.getFullYear().toString();
        const costMonth = (costDate.getMonth() + 1).toString().padStart(2, '0');

        if (filters.year && costYear !== filters.year) {
          return false;
        }

        if (filters.month && costMonth !== filters.month) {
          return false;
        }
      }

      return true;
    });
  };

  // تحديث الإحصائيات
  const updateStats = () => {
    const filteredCosts = getFilteredCosts();

    // إحصائيات عامة
    const totalAmount = filteredCosts.reduce((sum, cost) => sum + (parseFloat(cost.Amount) || 0), 0);
    const totalCount = filteredCosts.length;
    const avgAmount = totalCount > 0 ? totalAmount / totalCount : 0;

    // إحصائيات حسب البند الرئيسي
    const categoryStats = {};
    filteredCosts.forEach(cost => {
      const categoryName = cost.MainCategoryName || 'غير محدد';
      categoryStats[categoryName] = (categoryStats[categoryName] || 0) + (parseFloat(cost.Amount) || 0);
    });

    const byCategory = Object.entries(categoryStats).map(([name, amount]) => ({
      name,
      amount,
      percentage: totalAmount > 0 ? ((amount / totalAmount) * 100).toFixed(1) : 0
    })).sort((a, b) => b.amount - a.amount);

    // إحصائيات حسب الشهر
    const monthStats = {};
    const months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    months.forEach(month => monthStats[month] = 0);

    filteredCosts.forEach(cost => {
      const costDate = new Date(cost.CostDate);
      const monthIndex = costDate.getMonth();
      const monthName = months[monthIndex];
      monthStats[monthName] += (parseFloat(cost.Amount) || 0);
    });

    const byMonth = months.map(month => ({
      month,
      amount: monthStats[month]
    }));

    setStats({
      totalAmount,
      totalCount,
      avgAmount,
      byCategory,
      byMonth
    });

    // تحديث بيانات الرسوم البيانية
    updateChartData(filteredCosts, byCategory, byMonth);

    // تحديث الرسم البياني للبنود إذا لم يكن هناك بند مختار
    if (!selectedMainCategory) {
      updateMainCategoryChart();
    }

    // تحديث الرسم البياني الدائري إذا لم يكن هناك بند مختار
    if (!selectedDoughnutCategory) {
      updateDoughnutMainCategoryChart();
    }
  };

  // التعامل مع النقر على الأعمدة
  const handleBarClick = (event, elements) => {
    if (elements.length > 0) {
      const clickedIndex = elements[0].index;
      const currentLabels = chartData.monthlyTrend.labels;
      const clickedCategoryName = currentLabels[clickedIndex];

      if (selectedMainCategory === clickedCategoryName) {
        // إذا كان البند مختار بالفعل، إلغاء الاختيار
        setSelectedMainCategory(null);
      } else {
        // اختيار البند وعرض البنود الفرعية
        setSelectedMainCategory(clickedCategoryName);
      }
    }
  };

  // التعامل مع النقر على الرسم البياني الدائري
  const handleDoughnutClick = (event, elements) => {
    if (elements.length > 0) {
      const clickedIndex = elements[0].index;
      const currentLabels = chartData.categoryDistribution.labels;
      const clickedCategoryName = currentLabels[clickedIndex];

      if (selectedDoughnutCategory === clickedCategoryName) {
        // إذا كان البند مختار بالفعل، إلغاء الاختيار
        setSelectedDoughnutCategory(null);
      } else {
        // اختيار البند وعرض البنود الفرعية
        setSelectedDoughnutCategory(clickedCategoryName);
      }
    }
  };

  // تحديث الرسم البياني للبنود الرئيسية
  const updateMainCategoryChart = () => {
    const filteredCosts = getFilteredCosts();

    // حساب التكاليف لكل بند رئيسي
    const mainCategoryTotals = {};
    const mainCategoryLabels = [];
    const mainCategoryColors = [
      'rgba(239, 68, 68, 0.8)',   // أحمر
      'rgba(249, 115, 22, 0.8)',  // برتقالي
      'rgba(234, 179, 8, 0.8)',   // أصفر
      'rgba(34, 197, 94, 0.8)',   // أخضر
      'rgba(59, 130, 246, 0.8)',  // أزرق
      'rgba(139, 92, 246, 0.8)',  // بنفسجي
      'rgba(236, 72, 153, 0.8)',  // وردي
      'rgba(156, 163, 175, 0.8)'  // رمادي
    ];

    // تجميع التكاليف حسب البند الرئيسي
    filteredCosts.forEach(cost => {
      const mainCategoryName = cost.MainCategoryName || 'غير محدد';
      if (!mainCategoryTotals[mainCategoryName]) {
        mainCategoryTotals[mainCategoryName] = 0;
      }
      mainCategoryTotals[mainCategoryName] += parseFloat(cost.Amount || 0);
    });

    // تحويل إلى arrays للرسم البياني
    const labels = Object.keys(mainCategoryTotals);
    const data = Object.values(mainCategoryTotals);
    const colors = labels.map((_, index) => mainCategoryColors[index % mainCategoryColors.length]);
    const borderColors = colors.map(color => color.replace('0.8)', '1)'));

    setChartData(prev => ({
      ...prev,
      monthlyTrend: {
        labels: labels.length > 0 ? labels : ['لا توجد بيانات'],
        datasets: [{
          label: 'التكاليف المسجلة (ج.م)',
          data: data.length > 0 ? data : [0],
          backgroundColor: colors,
          borderColor: borderColors,
          borderWidth: 2
        }]
      }
    }));
  };

  // تحديث الرسم البياني للبنود الفرعية
  const updateSubCategoryChart = (mainCategoryName) => {
    const filteredCosts = getFilteredCosts();

    // فلترة التكاليف للبند الرئيسي المختار
    const categoryFilteredCosts = filteredCosts.filter(cost =>
      cost.MainCategoryName === mainCategoryName
    );

    // حساب التكاليف لكل بند فرعي
    const subCategoryTotals = {};
    categoryFilteredCosts.forEach(cost => {
      const subCategoryName = cost.SubCategoryName || 'غير محدد';
      if (!subCategoryTotals[subCategoryName]) {
        subCategoryTotals[subCategoryName] = 0;
      }
      subCategoryTotals[subCategoryName] += parseFloat(cost.Amount || 0);
    });

    // ألوان متدرجة للبنود الفرعية
    const subCategoryColors = [
      'rgba(239, 68, 68, 0.8)', 'rgba(220, 38, 38, 0.8)', 'rgba(185, 28, 28, 0.8)',
      'rgba(249, 115, 22, 0.8)', 'rgba(234, 88, 12, 0.8)', 'rgba(194, 65, 12, 0.8)',
      'rgba(234, 179, 8, 0.8)', 'rgba(202, 138, 4, 0.8)', 'rgba(161, 98, 7, 0.8)',
      'rgba(34, 197, 94, 0.8)', 'rgba(22, 163, 74, 0.8)', 'rgba(21, 128, 61, 0.8)',
      'rgba(59, 130, 246, 0.8)', 'rgba(37, 99, 235, 0.8)', 'rgba(29, 78, 216, 0.8)',
      'rgba(139, 92, 246, 0.8)', 'rgba(124, 58, 237, 0.8)', 'rgba(109, 40, 217, 0.8)',
      'rgba(236, 72, 153, 0.8)', 'rgba(219, 39, 119, 0.8)', 'rgba(190, 24, 93, 0.8)'
    ];

    // تحويل إلى arrays للرسم البياني
    const labels = Object.keys(subCategoryTotals);
    const data = Object.values(subCategoryTotals);
    const colors = labels.map((_, index) => subCategoryColors[index % subCategoryColors.length]);
    const borderColors = colors.map(color => color.replace('0.8)', '1)'));

    setChartData(prev => ({
      ...prev,
      monthlyTrend: {
        labels: labels.length > 0 ? labels : ['لا توجد بيانات فرعية'],
        datasets: [{
          label: `البنود الفرعية - ${mainCategoryName} (ج.م)`,
          data: data.length > 0 ? data : [0],
          backgroundColor: colors,
          borderColor: borderColors,
          borderWidth: 2
        }]
      }
    }));
  };

  // تحديث الرسم البياني الدائري للبنود الرئيسية
  const updateDoughnutMainCategoryChart = () => {
    const filteredCosts = getFilteredCosts();

    // حساب التكاليف لكل بند رئيسي
    const mainCategoryTotals = {};
    filteredCosts.forEach(cost => {
      const mainCategoryName = cost.MainCategoryName || 'غير محدد';
      if (!mainCategoryTotals[mainCategoryName]) {
        mainCategoryTotals[mainCategoryName] = 0;
      }
      mainCategoryTotals[mainCategoryName] += parseFloat(cost.Amount || 0);
    });

    // ألوان محسنة للبنود الرئيسية (ألوان داكنة للوضوح)
    const mainCategoryColors = [
      '#dc2626', '#ea580c', '#d97706', '#16a34a',
      '#2563eb', '#7c3aed', '#c2185b', '#475569'
    ];

    // تحويل إلى arrays للرسم البياني
    const labels = Object.keys(mainCategoryTotals);
    const data = Object.values(mainCategoryTotals);
    const colors = labels.map((_, index) => mainCategoryColors[index % mainCategoryColors.length]);

    setChartData(prev => ({
      ...prev,
      categoryDistribution: {
        labels: labels.length > 0 ? labels : ['لا توجد بيانات'],
        datasets: [{
          data: data.length > 0 ? data : [1],
          backgroundColor: colors,
          borderWidth: 2,
          borderColor: '#ffffff'
        }]
      }
    }));
  };

  // تحديث الرسم البياني الدائري للبنود الفرعية
  const updateDoughnutSubCategoryChart = (mainCategoryName) => {
    const filteredCosts = getFilteredCosts();

    // فلترة التكاليف للبند الرئيسي المختار
    const categoryFilteredCosts = filteredCosts.filter(cost =>
      cost.MainCategoryName === mainCategoryName
    );

    // حساب التكاليف لكل بند فرعي
    const subCategoryTotals = {};
    categoryFilteredCosts.forEach(cost => {
      const subCategoryName = cost.SubCategoryName || 'غير محدد';
      if (!subCategoryTotals[subCategoryName]) {
        subCategoryTotals[subCategoryName] = 0;
      }
      subCategoryTotals[subCategoryName] += parseFloat(cost.Amount || 0);
    });

    // ألوان محسنة للبنود الفرعية (تباين أفضل للنصوص)
    const subCategoryColors = [
      '#dc2626', '#b91c1c', '#991b1b', '#7f1d1d', '#ef4444',
      '#ea580c', '#c2410c', '#9a3412', '#7c2d12', '#f97316',
      '#d97706', '#b45309', '#92400e', '#78350f', '#f59e0b',
      '#16a34a', '#15803d', '#166534', '#14532d', '#22c55e',
      '#2563eb', '#1d4ed8', '#1e40af', '#1e3a8a', '#3b82f6',
      '#7c3aed', '#6d28d9', '#5b21b6', '#4c1d95', '#8b5cf6'
    ];

    // تحويل إلى arrays للرسم البياني
    const labels = Object.keys(subCategoryTotals);
    const data = Object.values(subCategoryTotals);
    const colors = labels.map((_, index) => subCategoryColors[index % subCategoryColors.length]);

    setChartData(prev => ({
      ...prev,
      categoryDistribution: {
        labels: labels.length > 0 ? labels : ['لا توجد بيانات فرعية'],
        datasets: [{
          data: data.length > 0 ? data : [1],
          backgroundColor: colors,
          borderWidth: 2,
          borderColor: '#ffffff'
        }]
      }
    }));
  };

  // تحديث بيانات الرسوم البيانية
  const updateChartData = (filteredCosts, byCategory, byMonth) => {
    const months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
    const custodyAmount = 240000; // مبلغ العُهدة الشهرية

    // بيانات الاتجاه الشهري
    const monthlyData = months.map(month => {
      const monthData = byMonth.find(m => m.month === month);
      return monthData ? monthData.amount : 0;
    });

    // حساب معدل دوران العُهدة لكل شهر
    const turnoverData = monthlyData.map(monthlyAmount => {
      return custodyAmount > 0 ? parseFloat((monthlyAmount / custodyAmount).toFixed(2)) : 0;
    });

    // بيانات توزيع الفئات
    const categoryLabels = byCategory.slice(0, 7).map(cat => cat.name);
    const categoryData = byCategory.slice(0, 7).map(cat => cat.amount);
    const categoryColors = ['#ef4444', '#f97316', '#eab308', '#22c55e', '#3b82f6', '#8b5cf6', '#ec4899'];

    setChartData(prev => ({
      ...prev,
      monthlyTrend: {
        ...prev.monthlyTrend,
        datasets: [{
          ...prev.monthlyTrend.datasets[0],
          data: monthlyData
        }]
      },
      categoryDistribution: {
        labels: categoryLabels.length > 0 ? categoryLabels : ['لا توجد بيانات'],
        datasets: [{
          data: categoryData.length > 0 ? categoryData : [1],
          backgroundColor: categoryColors.slice(0, categoryLabels.length),
          borderWidth: 2,
          borderColor: '#ffffff'
        }]
      },
      custodyTurnover: {
        ...prev.custodyTurnover,
        datasets: [{
          ...prev.custodyTurnover.datasets[0],
          data: turnoverData
        }]
      }
    }));
  };

  // تنسيق العملة
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  // ألوان الرسوم البيانية
  const chartColors = [
    '#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899'
  ];

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto p-6">
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <BarChart3 className="text-3xl text-blue-600" />
              <div>
                <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  لوحة تحكم تكاليف العُهد
                </h1>
                <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                  تحليل وإحصائيات تكاليف العُهد المستديمة والمؤقتة
                </p>
              </div>
            </div>
            <button
              onClick={fetchInitialData}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              تحديث البيانات
            </button>
          </div>
        </div>

        {/* الفلاتر */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="flex items-center gap-2 mb-4">
            <Filter className="text-lg text-blue-600" />
            <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              فلاتر التحليل
            </h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* البند الرئيسي */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                البند الرئيسي
              </label>
              <select
                value={filters.mainCategoryId}
                onChange={(e) => {
                  setFilters({
                    ...filters,
                    mainCategoryId: e.target.value,
                    subCategoryId: ''
                  });
                }}
                className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
              >
                <option value="">جميع البنود الرئيسية</option>
                {categories.map(category => (
                  <option key={category.ID} value={category.ID}>
                    {category.CategoryName}
                  </option>
                ))}
              </select>
            </div>

            {/* البند الفرعي */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                البند الفرعي
              </label>
              <select
                value={filters.subCategoryId}
                onChange={(e) => setFilters({...filters, subCategoryId: e.target.value})}
                className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                disabled={!filters.mainCategoryId}
              >
                <option value="">جميع البنود الفرعية</option>
                {filters.mainCategoryId && categories
                  .find(cat => cat.ID == filters.mainCategoryId)?.SubCategories
                  ?.map(subCat => (
                    <option key={subCat.ID} value={subCat.ID}>
                      {subCat.CategoryName}
                    </option>
                  ))}
              </select>
            </div>

            {/* السنة */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                السنة
              </label>
              <select
                value={filters.year}
                onChange={(e) => setFilters({...filters, year: e.target.value})}
                className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
              >
                <option value="">جميع السنوات</option>
                <option value="2024">2024</option>
                <option value="2025">2025</option>
                <option value="2026">2026</option>
              </select>
            </div>

            {/* الشهر */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                الشهر
              </label>
              <select
                value={filters.month}
                onChange={(e) => setFilters({...filters, month: e.target.value})}
                className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
              >
                <option value="">جميع الشهور</option>
                <option value="01">يناير</option>
                <option value="02">فبراير</option>
                <option value="03">مارس</option>
                <option value="04">أبريل</option>
                <option value="05">مايو</option>
                <option value="06">يونيو</option>
                <option value="07">يوليو</option>
                <option value="08">أغسطس</option>
                <option value="09">سبتمبر</option>
                <option value="10">أكتوبر</option>
                <option value="11">نوفمبر</option>
                <option value="12">ديسمبر</option>
              </select>
            </div>
          </div>
        </div>

        {/* الإحصائيات السريعة */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
                <DollarSign className="w-8 h-8 text-blue-600" />
              </div>
              <div className="mr-4">
                <p className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>إجمالي التكاليف</p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {formatCurrency(stats.totalAmount)}
                </p>
              </div>
            </div>
          </div>

          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-green-100 dark:bg-green-900">
                <FileText className="w-8 h-8 text-green-600" />
              </div>
              <div className="mr-4">
                <p className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>عدد التكاليف</p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {stats.totalCount.toLocaleString('ar-EG')}
                </p>
              </div>
            </div>
          </div>

          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-orange-100 dark:bg-orange-900">
                <TrendingUp className="w-8 h-8 text-orange-600" />
              </div>
              <div className="mr-4">
                <p className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>متوسط التكلفة</p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {formatCurrency(stats.avgAmount)}
                </p>
              </div>
            </div>
          </div>

          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900">
                <RefreshCw className="w-8 h-8 text-purple-600" />
              </div>
              <div className="mr-4">
                <p className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>معدل دوران العُهدة</p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {stats.totalAmount > 0 ? ((stats.totalAmount / 240000).toFixed(2)) : '0.00'}x
                </p>
                <p className={`text-xs ${isDarkMode ? 'text-slate-500' : 'text-gray-500'}`}>
                  العُهدة: 240,000 ج.م
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* التحليلات */}
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <RefreshCw className="w-8 h-8 animate-spin text-blue-500 mr-3" />
            <span className={`text-lg ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>جاري تحميل البيانات...</span>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* التكاليف حسب البند الرئيسي */}
            <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
              <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                التكاليف حسب البند الرئيسي
              </h3>
              <div className="space-y-4">
                {stats.byCategory.map((category, index) => (
                  <div key={category.name} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div
                        className="w-4 h-4 rounded"
                        style={{ backgroundColor: chartColors[index % chartColors.length] }}
                      ></div>
                      <span className={`text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                        {category.name}
                      </span>
                    </div>
                    <div className="text-right">
                      <div className={`text-sm font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {formatCurrency(category.amount)}
                      </div>
                      <div className={`text-xs ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>
                        {category.percentage}%
                      </div>
                    </div>
                  </div>
                ))}
                {stats.byCategory.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    لا توجد بيانات للعرض
                  </div>
                )}
              </div>
            </div>

            {/* التكاليف حسب الشهر */}
            <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
              <h3 className={`text-lg font-semibold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                التكاليف حسب الشهر
              </h3>
              <div className="space-y-3">
                {stats.byMonth.map((monthData, index) => (
                  <div key={monthData.month} className="flex items-center justify-between">
                    <span className={`text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                      {monthData.month}
                    </span>
                    <div className="flex items-center gap-3">
                      <div className={`text-sm font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {formatCurrency(monthData.amount)}
                      </div>
                      <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{
                            width: `${stats.totalAmount > 0 ? (monthData.amount / stats.totalAmount) * 100 : 0}%`
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* الرسوم البيانية */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
          {/* الرسم البياني للبنود */}
          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center justify-between mb-4">
              <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {selectedMainCategory ? `البنود الفرعية - ${selectedMainCategory}` : 'البنود الرئيسية'}
              </h3>
              {selectedMainCategory && (
                <button
                  onClick={() => setSelectedMainCategory(null)}
                  className="text-sm bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-lg"
                >
                  العودة للبنود الرئيسية
                </button>
              )}
            </div>
            <div className="h-80">
              <Bar
                data={chartData.monthlyTrend}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  onClick: handleBarClick,
                  plugins: {
                    // إضافة القيم داخل الأعمدة
                    datalabels: {
                      display: true,
                      color: '#ffffff',
                      font: {
                        weight: 'bold',
                        size: 12,
                        family: 'Arial, sans-serif'
                      },
                      anchor: 'center',
                      align: 'center',
                      formatter: function(value) {
                        return value.toLocaleString('ar-EG') + '\nج.م';
                      },
                      textStrokeColor: '#000000',
                      textStrokeWidth: 1
                    },
                    legend: {
                      position: 'top',
                      labels: {
                        color: isDarkMode ? '#e5e7eb' : '#374151',
                        font: { family: 'Arial, sans-serif', size: 14, weight: 'bold' }
                      }
                    },
                    tooltip: {
                      backgroundColor: isDarkMode ? '#1f2937' : '#ffffff',
                      titleColor: isDarkMode ? '#e5e7eb' : '#374151',
                      bodyColor: isDarkMode ? '#e5e7eb' : '#374151',
                      borderColor: isDarkMode ? '#374151' : '#e5e7eb',
                      borderWidth: 1,
                      titleFont: { family: 'Arial, sans-serif', size: 14, weight: 'bold' },
                      bodyFont: { family: 'Arial, sans-serif', size: 13, weight: 'normal' },
                      callbacks: {
                        label: function(context) {
                          return `${context.dataset.label}: ${context.parsed.y.toLocaleString('ar-EG')} ج.م`;
                        },
                        afterLabel: function(context) {
                          if (!selectedMainCategory) {
                            return 'انقر للعرض التفصيلي';
                          }
                          return '';
                        }
                      }
                    }
                  },
                  scales: {
                    x: {
                      ticks: {
                        color: isDarkMode ? '#e5e7eb' : '#374151',
                        maxRotation: 45,
                        minRotation: 0,
                        font: { family: 'Arial, sans-serif', size: 12, weight: 'bold' }
                      },
                      grid: { color: isDarkMode ? '#374151' : '#e5e7eb' }
                    },
                    y: {
                      ticks: {
                        color: isDarkMode ? '#e5e7eb' : '#374151',
                        font: { family: 'Arial, sans-serif', size: 12, weight: 'bold' },
                        callback: function(value) {
                          return value.toLocaleString('ar-EG') + ' ج.م';
                        }
                      },
                      grid: { color: isDarkMode ? '#374151' : '#e5e7eb' },
                      beginAtZero: true
                    }
                  }
                }}
              />
            </div>
            {!selectedMainCategory && (
              <div className={`mt-4 p-3 rounded-lg ${isDarkMode ? 'bg-slate-800' : 'bg-blue-50'}`}>
                <p className={`text-xs ${isDarkMode ? 'text-slate-300' : 'text-blue-700'}`}>
                  💡 انقر على أي عمود لعرض البنود الفرعية
                </p>
              </div>
            )}
          </div>

          {/* الرسم البياني لتوزيع الفئات */}
          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center justify-between mb-4">
              <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {selectedDoughnutCategory ? `البنود الفرعية - ${selectedDoughnutCategory}` : 'توزيع التكاليف حسب الفئة'}
              </h3>
              {selectedDoughnutCategory && (
                <button
                  onClick={() => setSelectedDoughnutCategory(null)}
                  className="text-sm bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded-lg"
                >
                  العودة للبنود الرئيسية
                </button>
              )}
            </div>
            <div className="h-80">
              <Doughnut
                data={chartData.categoryDistribution}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  onClick: handleDoughnutClick,
                  plugins: {
                    legend: {
                      position: 'right',
                      labels: {
                        color: isDarkMode ? '#e5e7eb' : '#374151',
                        font: { family: 'Arial, sans-serif', size: 13, weight: 'bold' },
                        padding: 15,
                        usePointStyle: true,
                        generateLabels: function(chart) {
                          const data = chart.data;
                          if (data.labels.length && data.datasets.length) {
                            return data.labels.map((label, i) => {
                              const value = data.datasets[0].data[i];
                              const total = data.datasets[0].data.reduce((a, b) => a + b, 0);
                              const percentage = ((value / total) * 100).toFixed(1);
                              return {
                                text: `${label}: ${value.toLocaleString('ar-EG')} ج.م (${percentage}%)`,
                                fillStyle: data.datasets[0].backgroundColor[i],
                                strokeStyle: data.datasets[0].backgroundColor[i],
                                lineWidth: 0,
                                pointStyle: 'circle',
                                hidden: false,
                                index: i
                              };
                            });
                          }
                          return [];
                        }
                      }
                    },
                    tooltip: {
                      backgroundColor: isDarkMode ? '#1f2937' : '#ffffff',
                      titleColor: isDarkMode ? '#e5e7eb' : '#374151',
                      bodyColor: isDarkMode ? '#e5e7eb' : '#374151',
                      borderColor: isDarkMode ? '#374151' : '#e5e7eb',
                      borderWidth: 1,
                      titleFont: { family: 'Arial, sans-serif', size: 14, weight: 'bold' },
                      bodyFont: { family: 'Arial, sans-serif', size: 13, weight: 'normal' },
                      callbacks: {
                        label: function(context) {
                          const total = context.dataset.data.reduce((a, b) => a + b, 0);
                          const percentage = ((context.parsed / total) * 100).toFixed(1);
                          return `${context.label}: ${context.parsed.toLocaleString('ar-EG')} ج.م (${percentage}%)`;
                        },
                        afterLabel: function(context) {
                          if (!selectedDoughnutCategory) {
                            return 'انقر للعرض التفصيلي';
                          }
                          return '';
                        }
                      }
                    },
                    // إضافة النصوص داخل الرسم البياني
                    datalabels: {
                      display: function(context) {
                        const value = context.dataset.data[context.dataIndex];
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = (value / total) * 100;
                        return percentage > 8; // عرض النص فقط إذا كانت النسبة أكبر من 8%
                      },
                      color: function(context) {
                        // تحديد لون النص بناءً على سطوع لون الخلفية
                        const bgColor = context.dataset.backgroundColor[context.dataIndex];

                        // التحقق من وجود اللون وأنه string
                        if (!bgColor || typeof bgColor !== 'string') {
                          return '#ffffff'; // لون افتراضي
                        }

                        // تحويل اللون إلى RGB وحساب السطوع
                        const hex = bgColor.replace('#', '');

                        // التحقق من صحة اللون hex
                        if (hex.length !== 6) {
                          return '#ffffff'; // لون افتراضي
                        }

                        const r = parseInt(hex.substr(0, 2), 16);
                        const g = parseInt(hex.substr(2, 2), 16);
                        const b = parseInt(hex.substr(4, 2), 16);

                        // التحقق من صحة القيم
                        if (isNaN(r) || isNaN(g) || isNaN(b)) {
                          return '#ffffff'; // لون افتراضي
                        }

                        // حساب السطوع باستخدام معادلة luminance
                        const brightness = (r * 299 + g * 587 + b * 114) / 1000;

                        // إذا كان اللون فاتح (سطوع > 128)، استخدم نص أسود، وإلا استخدم نص أبيض
                        return brightness > 128 ? '#000000' : '#ffffff';
                      },
                      font: {
                        weight: 'bold',
                        size: 12,
                        family: 'Arial, sans-serif'
                      },
                      textAlign: 'center',
                      formatter: function(value, context) {
                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                        const percentage = ((value / total) * 100).toFixed(1);
                        const label = context.chart.data.labels[context.dataIndex];

                        // تقصير اسم البند إذا كان طويلاً
                        const shortLabel = label.length > 6 ? label.substring(0, 6) + '...' : label;

                        return [`${shortLabel}`, `${percentage}%`, `${value.toLocaleString('ar-EG')} ج.م`];
                      },
                      textStrokeColor: function(context) {
                        // إضافة حدود للنص لتحسين الوضوح
                        const bgColor = context.dataset.backgroundColor[context.dataIndex];

                        // التحقق من وجود اللون وأنه string
                        if (!bgColor || typeof bgColor !== 'string') {
                          return '#000000'; // لون افتراضي للحدود
                        }

                        // تحويل اللون إلى RGB وحساب السطوع
                        const hex = bgColor.replace('#', '');

                        // التحقق من صحة اللون hex
                        if (hex.length !== 6) {
                          return '#000000'; // لون افتراضي للحدود
                        }

                        const r = parseInt(hex.substr(0, 2), 16);
                        const g = parseInt(hex.substr(2, 2), 16);
                        const b = parseInt(hex.substr(4, 2), 16);

                        // التحقق من صحة القيم
                        if (isNaN(r) || isNaN(g) || isNaN(b)) {
                          return '#000000'; // لون افتراضي للحدود
                        }

                        const brightness = (r * 299 + g * 587 + b * 114) / 1000;

                        // إذا كان النص أسود، استخدم حدود بيضاء، وإلا استخدم حدود سوداء
                        return brightness > 128 ? '#ffffff' : '#000000';
                      },
                      textStrokeWidth: 2
                    }
                  }
                }}
              />
            </div>
            {!selectedDoughnutCategory && (
              <div className={`mt-4 p-3 rounded-lg ${isDarkMode ? 'bg-slate-800' : 'bg-green-50'}`}>
                <p className={`text-xs ${isDarkMode ? 'text-slate-300' : 'text-green-700'}`}>
                  💡 انقر على أي قطعة لعرض البنود الفرعية
                </p>
              </div>
            )}
          </div>
        </div>

        {/* الرسم البياني لمعدل دوران العُهدة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mt-6`}>
          <div className="flex items-center justify-between mb-4">
            <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              معدل دوران العُهدة الشهرية
            </h3>
            <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
              العُهدة الشهرية: 240,000 ج.م
            </div>
          </div>
          <div className="h-80">
            <Bar
              data={chartData.custodyTurnover}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: 'top',
                    labels: {
                      color: isDarkMode ? '#e5e7eb' : '#374151',
                      font: { family: 'Arial', size: 12 }
                    }
                  },
                  tooltip: {
                    backgroundColor: isDarkMode ? '#1f2937' : '#ffffff',
                    titleColor: isDarkMode ? '#e5e7eb' : '#374151',
                    bodyColor: isDarkMode ? '#e5e7eb' : '#374151',
                    borderColor: isDarkMode ? '#374151' : '#e5e7eb',
                    borderWidth: 1,
                    callbacks: {
                      label: function(context) {
                        const turnoverRate = context.parsed.y;
                        const totalSpent = (turnoverRate * 240000).toLocaleString('ar-EG');
                        return [
                          `معدل الدوران: ${turnoverRate}`,
                          `إجمالي المصروف: ${totalSpent} ج.م`
                        ];
                      }
                    }
                  }
                },
                scales: {
                  x: {
                    ticks: { color: isDarkMode ? '#9ca3af' : '#6b7280' },
                    grid: { color: isDarkMode ? '#374151' : '#e5e7eb' }
                  },
                  y: {
                    ticks: {
                      color: isDarkMode ? '#9ca3af' : '#6b7280',
                      callback: function(value) {
                        return value.toFixed(1) + 'x';
                      }
                    },
                    grid: { color: isDarkMode ? '#374151' : '#e5e7eb' },
                    beginAtZero: true,
                    max: 3
                  }
                }
              }}
            />
          </div>

          {/* شرح معدل الدوران */}
          <div className={`mt-4 p-4 rounded-lg ${isDarkMode ? 'bg-slate-800' : 'bg-blue-50'}`}>
            <h4 className={`text-sm font-semibold mb-2 ${isDarkMode ? 'text-blue-300' : 'text-blue-800'}`}>
              📊 شرح معدل دوران العُهدة:
            </h4>
            <div className={`text-xs ${isDarkMode ? 'text-slate-300' : 'text-blue-700'} space-y-1`}>
              <p>• <strong>معدل الدوران = إجمالي التكاليف المصروفة ÷ مبلغ العُهدة الشهرية</strong></p>
              <p>• معدل 1.0 = تم صرف مبلغ العُهدة مرة واحدة</p>
              <p>• معدل 2.0 = تم صرف مبلغ العُهدة مرتين (كفاءة عالية)</p>
              <p>• معدل أعلى = استخدام أكثر كفاءة للعُهدة</p>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
