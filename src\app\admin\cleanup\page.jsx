'use client';

import { useState } from 'react';
import MainLayout from '@/components/MainLayout';
import { FiTrash2, FiRefreshCw, FiAlertTriangle, FiCheck } from 'react-icons/fi';

export default function CleanupPage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);

  const cleanupDuplicates = async () => {
    if (!confirm('هل أنت متأكد من تنظيف الطلبات المكررة؟ سيتم الاحتفاظ بأقدم طلب وحذف الباقي.')) {
      return;
    }

    try {
      setLoading(true);
      setResult(null);
      
      const response = await fetch('/api/paper-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'cleanup-duplicates'
        }),
      });

      const data = await response.json();
      setResult(data);

      if (data.success) {
        alert(`تم تنظيف ${data.cleaned} طلب مكرر بنجاح`);
      } else {
        alert(data.error || 'خطأ في تنظيف الطلبات المكررة');
      }
    } catch (error) {

      alert('خطأ في الاتصال بالخادم');
      setResult({
        success: false,
        error: 'خطأ في الاتصال بالخادم'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-4xl mx-auto">
          {/* العنوان */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-200 mb-2">
                  تنظيف الطلبات المكررة
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  إزالة الطلبات المكررة من قاعدة البيانات
                </p>
              </div>
              <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-red-600 rounded-xl flex items-center justify-center shadow-lg">
                <FiTrash2 className="text-2xl text-white" />
              </div>
            </div>
          </div>

          {/* تحذير */}
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
            <div className="flex items-start gap-3">
              <FiAlertTriangle className="text-yellow-600 dark:text-yellow-400 mt-1 flex-shrink-0" />
              <div>
                <h3 className="text-yellow-800 dark:text-yellow-200 font-medium mb-1">
                  تحذير مهم
                </h3>
                <p className="text-yellow-700 dark:text-yellow-300 text-sm">
                  هذه العملية ستحذف الطلبات المكررة نهائياً من قاعدة البيانات. سيتم الاحتفاظ بأقدم طلب وحذف الباقي.
                  تأكد من عمل نسخة احتياطية قبل المتابعة.
                </p>
              </div>
            </div>
          </div>

          {/* زر التنظيف */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
            <div className="text-center">
              <button
                onClick={cleanupDuplicates}
                disabled={loading}
                className="bg-orange-500 hover:bg-orange-600 disabled:bg-gray-400 text-white px-8 py-3 rounded-lg font-medium transition-colors flex items-center gap-2 mx-auto"
              >
                {loading ? (
                  <>
                    <FiRefreshCw className="animate-spin" />
                    جاري التنظيف...
                  </>
                ) : (
                  <>
                    <FiTrash2 />
                    تنظيف الطلبات المكررة
                  </>
                )}
              </button>
            </div>
          </div>

          {/* النتائج */}
          {result && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                {result.success ? (
                  <FiCheck className="text-green-600" />
                ) : (
                  <FiAlertTriangle className="text-red-600" />
                )}
                نتائج التنظيف
              </h2>
              
              {result.success ? (
                <div className="space-y-3">
                  <div className="flex justify-between items-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <span className="text-green-800 dark:text-green-200">الطلبات المحذوفة:</span>
                    <span className="font-bold text-green-600 dark:text-green-400">{result.cleaned}</span>
                  </div>
                  
                  {result.duplicatesFound && (
                    <div className="flex justify-between items-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <span className="text-blue-800 dark:text-blue-200">مجموعات الطلبات المكررة:</span>
                      <span className="font-bold text-blue-600 dark:text-blue-400">{result.duplicatesFound}</span>
                    </div>
                  )}
                  
                  <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <p className="text-gray-700 dark:text-gray-300 text-sm">
                      {result.message}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                  <p className="text-red-700 dark:text-red-300">
                    {result.error}
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
