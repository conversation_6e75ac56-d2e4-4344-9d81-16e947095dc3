'use client';

import CostCardClean from '@/components/CostCardClean';
import DashboardCard from '@/components/DashboardCard';
import MainLayout from '@/components/MainLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTheme } from '@/contexts/ThemeContext';
import { useEffect, useState } from 'react';
import {
    FiActivity,
    FiArrowRight,
    FiCalendar,
    FiGlobe,
    FiHome,
    FiLogOut, FiRefreshCw,
    FiTruck,
    FiUsers
} from 'react-icons/fi';

export default function Dashboard() {
  const { isDarkMode } = useTheme();
  const { isArabic } = useLanguage();
  const [currentTime, setCurrentTime] = useState('');
  const [stats, setStats] = useState({
    loading: true,
    employees: { total: 0, active: 0, expatriates: 0 },
    apartments: { total: 0, occupied: 0, vacant: 0, revenue: 0 },
    cars: { total: 0, rented: 0, available: 0, revenue: 0 },
    allCosts: { total: 0, breakdown: {} },
    tempWorkers: { count: 0, cost: 0 },
    tempWorkersCost: { total: 0 },
    transfers: { total: 0, thisYear: 0 },
    resignations: { total: 0, thisYear: 0 },
    alerts: { total: 0, unread: 0 },
    leaves: { totalRequests: 0, pending: 0 },
    insurance: { bothInsured: 0, socialInsured: 0, medicalInsured: 0, notInsured: 0 },
    custody: {
      totalAmount: 240000,
      availableBalance: 154887.57,
      pendingAmount: 87112.43,
      completedSettlements: 3720,
      lastSettlementNumber: 25,
      utilizationPercentage: 36,
      availabilityPercentage: 64
    }
  });

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  // جلب بيانات العُهد المستديمة
  const fetchCustodyStats = async () => {
    try {

      const response = await fetch('/api/custody-stats');

      if (response.ok) {
        const result = await response.json();

        if (result.success) {
          setStats(prev => ({
            ...prev,
            custody: result.data
          }));
        }
      }
    } catch (error) {

    }
  };

  const fetchDashboardStats = async () => {
    setStats(prev => ({ ...prev, loading: true }));

    try {
      // جلب البيانات من API الداش بورد الرئيسي
      const response = await fetch('/api/main-dashboard-stats');

      if (response.ok) {
        const result = await response.json();

        const data = result.data || result; // التعامل مع البنية المختلفة للاستجابة

        setStats(prev => ({
          ...prev,
          loading: false,
          employees: data.employees || { total: 0, active: 0, expatriates: 0 },
          apartments: data.apartments || { total: 0, occupied: 0, vacant: 0, revenue: 0 },
          cars: data.cars || { total: 0, rented: 0, available: 0, revenue: 0 },
          allCosts: data.allCosts || { total: 0, breakdown: {} },
          tempWorkers: data.tempWorkers || { count: 0, cost: 0 },
          tempWorkersCost: data.tempWorkersCost || { total: 0 },
          transfers: data.transfers || { total: 0, thisYear: 0 },
          resignations: data.resignations || { total: 0, thisYear: 0 },
          alerts: data.alerts || { total: 0, unread: 0 },
          leaves: data.leaves || { totalRequests: 0, pending: 0 },
          insurance: data.insurance || { bothInsured: 0, socialInsured: 0, medicalInsured: 0, notInsured: 0 }
        }));
      } else {
        throw new Error('فشل في جلب البيانات من الخادم');
      }
    } catch (error) {

      // في حالة حدوث خطأ، استخدام بيانات تجريبية
      setStats(prev => ({
        ...prev,
        loading: false,
        employees: { total: 81, active: 78, expatriates: 30 },
        apartments: { total: 7, occupied: 5, vacant: 2, revenue: 320000 },
        cars: { total: 11, rented: 9, available: 2, revenue: 955000 },
        allCosts: { total: 2125000, breakdown: { tempWorkers: 850000, carsCosts: 600000, apartmentsCosts: 675000 } },
        tempWorkers: { count: 25, cost: 850000, latestMonth: 'يونيو', latestYear: '2025' },
        tempWorkersCost: { total: 850000 },
        transfers: { total: 30, thisYear: 12 },
        resignations: { total: 15, thisYear: 8 },
        alerts: { total: 25, unread: 5 },
        leaves: { totalRequests: 18, pending: 3 },
        insurance: { bothInsured: 36, socialInsured: 69, medicalInsured: 37, notInsured: 11 }
      }));
    }
  };

  useEffect(() => {
    fetchDashboardStats();
    fetchCustodyStats();
    const timer = setInterval(() => {
      setCurrentTime(new Date().toLocaleString(isArabic ? 'ar-EG' : 'en-US'));
    }, 1000);
    return () => clearInterval(timer);
  }, [isArabic]);

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto">
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-slate-800' : 'bg-white'} rounded-xl p-6 shadow-lg mb-6 border-l-4 border-blue-500`}>
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
                📊 لوحة التحكم الرئيسية
              </h1>
              <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                نظرة شاملة على جميع البيانات والإحصائيات
              </p>
            </div>
            <div className="text-right">
              <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} mb-1 space-y-1`}>
                <div>آخر تحديث: {currentTime}</div>
                <div>حالة قاعدة البيانات: <span className="text-green-500">متصلة ✓</span></div>
                <div>آخر تحديث للبيانات: {new Date().toLocaleDateString('ar-EG', { month: 'long', year: 'numeric' })}</div>
              </div>
              <button
                onClick={fetchDashboardStats}
                disabled={stats.loading}
                className={`${isDarkMode ? 'bg-slate-700 hover:bg-slate-600 text-white' : 'bg-blue-50 hover:bg-blue-100 text-blue-700'} p-2 rounded-lg transition-colors disabled:opacity-50 flex items-center gap-2 mt-2`}
              >
                <FiRefreshCw className={`${stats.loading ? 'animate-spin' : ''}`} />
                <span className="text-sm">تحديث البيانات</span>
              </button>
            </div>
          </div>
        </div>

        {/* قسم الموظفين */}
        <div className="mb-6">
          <h2 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'} mb-4 flex items-center gap-2`}>
            👥 إحصائيات الموظفين
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* إجمالي الموظفين */}
            <DashboardCard
              title="إجمالي الموظفين"
              value={stats.employees.total}
              icon={FiUsers}
              color="blue"
              href="/employees"
              description={`نشط: ${stats.employees.active} | غير نشط: ${stats.employees.total - stats.employees.active}`}
              trend="up"
              trendValue={`${stats.employees.active} نشط`}
              onClick={() => {}}
              isLoading={stats.loading}
              animationDelay={0}
            />

            {/* المغتربين */}
            <DashboardCard
              title="المغتربين"
              value={stats.employees.expatriates}
              icon={FiGlobe}
              color="yellow"
              href="/employees?filter=expatriates"
              description={`النسبة: ${stats.employees.total > 0 ? Math.round((stats.employees.expatriates / stats.employees.total) * 100) : 0}% | محليين: ${stats.employees.total - stats.employees.expatriates}`}
              trend="neutral"
              trendValue={`${stats.employees.total > 0 ? Math.round((stats.employees.expatriates / stats.employees.total) * 100) : 0}%`}
              onClick={() => {}}
              isLoading={stats.loading}
              animationDelay={100}
            />

            {/* الاستقالات */}
            <DashboardCard
              title="الاستقالات"
              value={stats.resignations.total}
              icon={FiLogOut}
              color="red"
              href="/employees/resignations"
              description={`هذا العام: ${stats.resignations.thisYear} | معدل الدوران: ${stats.employees.total > 0 ? ((stats.resignations.thisYear / stats.employees.total) * 100).toFixed(1) : 0}%`}
              trend="down"
              trendValue={`${stats.resignations.thisYear} هذا العام`}
              onClick={() => {}}
              isLoading={stats.loading}
              animationDelay={200}
            />

            {/* النقل */}
            <DashboardCard
              title="حالات النقل"
              value={stats.transfers.total}
              icon={FiArrowRight}
              color="blue"
              href="/employees/transfers"
              description={`هذا العام: ${stats.transfers.thisYear}`}
              trend="neutral"
              trendValue={`${stats.transfers.thisYear} هذا العام`}
              onClick={() => {}}
              isLoading={stats.loading}
              animationDelay={300}
            />
          </div>
        </div>

        {/* قسم التكاليف والأصول */}
        <div className="mb-6">
          <h2 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'} mb-4 flex items-center gap-2`}>
            💰 التكاليف والأصول
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {/* العُهد المستديمة */}
            <CostCardClean
              title="العُهد المستديمة"
              value={formatCurrency(stats.custody?.availableBalance || 154887.57)}
              icon={FiActivity}
              color="blue"
              href="/custody-costs"
              description={
                <div className="space-y-1">
                  <div className="text-xs font-medium">إجمالي العُهد: {formatCurrency(stats.custody?.totalAmount || 240000)}</div>
                  <div className="text-xs">الرصيد المتاح: {formatCurrency(stats.custody?.availableBalance || 154887.57)}</div>
                  <div className="text-xs">قيد المراجعة: {formatCurrency(stats.custody?.pendingAmount || 87112.43)}</div>
                </div>
              }
              lastSettlement={stats.custody?.lastSettlementNumber || 25}
              utilizationRate={stats.custody?.utilizationPercentage || 36}
              trend={(stats.custody?.utilizationPercentage || 36) > 50 ? "down" : "up"}
              trendValue={`${stats.custody?.utilizationPercentage || 36}% مستخدم`}
              isLoading={stats.loading}
              additionalInfo={`آخر تحديث: ${new Date().toLocaleDateString('ar-EG', { month: 'long' })}`}
            />
            {/* إجمالي التكاليف */}
            <CostCardClean
              title="إجمالي التكاليف"
              value={formatCurrency(
                (stats.allCosts?.breakdown?.tempWorkers || 0) +
                (stats.allCosts?.breakdown?.carsCosts || 0) +
                (stats.allCosts?.breakdown?.apartmentsCosts || 0)
              )}
              icon={FiActivity}
              color="purple"
              href="/costs"
              description={
                <div className="space-y-1">
                  <div className="text-xs font-medium">للبنود الثلاث الرئيسية</div>
                  <div className="text-xs">شقق • سيارات • عمالة مؤقتة</div>
                  <div className="text-xs">جميع البنود المسجلة</div>
                </div>
              }
              lastSettlement={0}
              utilizationRate={0}
              trend="neutral"
              trendValue="البنود الثلاث"
              isLoading={stats.loading}
              additionalInfo={`آخر تحديث: ${new Date().toLocaleDateString('ar-EG', { month: 'long' })}`}
            />

            {/* الشقق */}
            <CostCardClean
              title="إجمالي الشقق"
              value={`${stats.apartments.total} شقة`}
              icon={FiHome}
              color="green"
              href="/apartments"
              description={
                <div className="space-y-1">
                  <div className="text-xs font-medium">مؤجرة: {stats.apartments.occupied} شقة</div>
                  <div className="text-xs">عدد المستفيدين: {stats.apartments.occupied * 2}</div>
                  <div className="text-xs">متاحة: {stats.apartments.total - stats.apartments.occupied}</div>
                </div>
              }
              lastSettlement={0}
              utilizationRate={stats.apartments.total > 0 ? Math.round((stats.apartments.occupied / stats.apartments.total) * 100) : 0}
              trend="neutral"
              trendValue={`${stats.apartments.occupied} مؤجرة`}
              isLoading={stats.loading}
              additionalInfo={`آخر تحديث: ${new Date().toLocaleDateString('ar-EG', { month: 'long' })}`}
            />

            {/* السيارات */}
            <CostCardClean
              title="إجمالي السيارات"
              value={`${stats.cars.total} سيارة`}
              icon={FiTruck}
              color="orange"
              href="/cars"
              description={
                <div className="space-y-1">
                  <div className="text-xs font-medium">مؤجرة: {stats.cars.rented} سيارة</div>
                  <div className="text-xs">عدد المستفيدين: {stats.cars.rented * 3}</div>
                  <div className="text-xs">متاحة: {stats.cars.total - stats.cars.rented}</div>
                </div>
              }
              lastSettlement={0}
              utilizationRate={stats.cars.total > 0 ? Math.round((stats.cars.rented / stats.cars.total) * 100) : 0}
              trend="neutral"
              trendValue={`${stats.cars.rented} مؤجرة`}
              isLoading={stats.loading}
              additionalInfo={`آخر تحديث: ${new Date().toLocaleDateString('ar-EG', { month: 'long' })}`}
            />

            {/* العمالة المؤقتة */}
            <CostCardClean
              title="العمالة المؤقتة"
              value={`${stats.tempWorkers?.count || 0} عامل`}
              icon={FiUsers}
              color="red"
              href="/temp-workers"
              description={
                <div className="space-y-1">
                  <div className="text-xs font-medium">التكلفة الإجمالية:</div>
                  <div className="text-xs">{formatCurrency(stats.allCosts?.breakdown?.tempWorkers || 0)}</div>
                  <div className="text-xs">عدد العمال: {stats.tempWorkers?.count || 0}</div>
                </div>
              }
              lastSettlement={0}
              utilizationRate={0}
              trend="neutral"
              trendValue={`${stats.tempWorkers?.count || 0} عامل`}
              isLoading={stats.loading}
              additionalInfo={
                stats.tempWorkers?.latestMonth && stats.tempWorkers?.latestYear
                  ? `آخر شهر: ${stats.tempWorkers.latestMonth} ${stats.tempWorkers.latestYear}`
                  : `آخر تحديث: ${new Date().toLocaleDateString('ar-EG', { month: 'long' })}`
              }
            />
          </div>
        </div>

        {/* قسم التنبيهات والإجازات */}
        <div className="mb-6">
          <h2 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'} mb-4 flex items-center gap-2`}>
            🔔 التنبيهات والإجازات
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* التنبيهات */}
            <DashboardCard
              title="إجمالي التنبيهات"
              value={stats.alerts.total}
              icon={FiActivity}
              color="red"
              href="/alerts"
              description={`غير مقروءة: ${stats.alerts.unread} | مقروءة: ${stats.alerts.total - stats.alerts.unread}`}
              trend="down"
              trendValue={`${stats.alerts.unread} غير مقروءة`}
              onClick={() => {}}
              isLoading={stats.loading}
              animationDelay={800}
            />

            {/* طلبات الإجازات */}
            <DashboardCard
              title="طلبات الإجازات"
              value={stats.leaves.totalRequests}
              icon={FiCalendar}
              color="blue"
              href="/leaves"
              description={`قيد المراجعة: ${stats.leaves.pending} | معتمدة: ${stats.leaves.totalRequests - stats.leaves.pending}`}
              trend="up"
              trendValue={`${stats.leaves.pending} قيد المراجعة`}
              onClick={() => {}}
              isLoading={stats.loading}
              animationDelay={900}
            />
          </div>
        </div>

        {/* إحصائيات التأمينات */}
        <div className="mb-6">
          <h2 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'} mb-4 flex items-center gap-2`}>
            🛡️ إحصائيات التأمينات
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* مؤمن الاثنين */}
            <DashboardCard
              title="مؤمن الاثنين"
              value={stats.insurance.bothInsured}
              icon={FiActivity}
              color="green"
              href="/insurance?type=both"
              description={`تأمين اجتماعي + طبي (${stats.employees.total > 0 ? ((stats.insurance.bothInsured / stats.employees.total) * 100).toFixed(1) : 0}%)`}
              trend="up"
              trendValue={`${stats.employees.total > 0 ? ((stats.insurance.bothInsured / stats.employees.total) * 100).toFixed(1) : 0}%`}
              onClick={() => {}}
              isLoading={stats.loading}
              animationDelay={1000}
            />

            {/* التأمين الاجتماعي */}
            <DashboardCard
              title="التأمين الاجتماعي"
              value={stats.insurance.socialInsured}
              icon={FiUsers}
              color="blue"
              href="/insurance?type=social"
              description={`إجمالي المؤمن عليهم (${stats.employees.total > 0 ? ((stats.insurance.socialInsured / stats.employees.total) * 100).toFixed(1) : 0}%)`}
              trend="neutral"
              trendValue={`${stats.employees.total > 0 ? ((stats.insurance.socialInsured / stats.employees.total) * 100).toFixed(1) : 0}%`}
              onClick={() => {}}
              isLoading={stats.loading}
              animationDelay={1100}
            />

            {/* التأمين الطبي */}
            <DashboardCard
              title="التأمين الطبي"
              value={stats.insurance.medicalInsured}
              icon={FiActivity}
              color="purple"
              href="/insurance?type=medical"
              description={`إجمالي المؤمن عليهم (${stats.employees.total > 0 ? ((stats.insurance.medicalInsured / stats.employees.total) * 100).toFixed(1) : 0}%)`}
              trend="neutral"
              trendValue={`${stats.employees.total > 0 ? ((stats.insurance.medicalInsured / stats.employees.total) * 100).toFixed(1) : 0}%`}
              onClick={() => {}}
              isLoading={stats.loading}
              animationDelay={1200}
            />

            {/* غير مؤمن */}
            <DashboardCard
              title="غير مؤمن"
              value={stats.insurance.notInsured}
              icon={FiActivity}
              color="red"
              href="/insurance?type=none"
              description={`بدون تأمين (${stats.employees.total > 0 ? ((stats.insurance.notInsured / stats.employees.total) * 100).toFixed(1) : 0}%)`}
              trend="down"
              trendValue={`${stats.employees.total > 0 ? ((stats.insurance.notInsured / stats.employees.total) * 100).toFixed(1) : 0}%`}
              onClick={() => {}}
              isLoading={stats.loading}
              animationDelay={1300}
            />
          </div>
        </div>

        {/* الرسوم البيانية - تصميم حديث */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          {/* رسم بياني للتكاليف الشهرية */}
          <div className={`lg:col-span-2 ${isDarkMode ? 'bg-slate-800' : 'bg-white'} rounded-xl p-6 shadow-lg`}>
            <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-4 flex items-center gap-2`}>
              <div className="p-2 rounded-lg bg-purple-500/20">
                <span className="text-purple-500">📊</span>
              </div>
              إجمالي التكاليف التفصيلي
            </h3>
            <div className="space-y-4">
              {stats.loading ? (
                <div className="text-center py-8 text-gray-500">
                  <div className="animate-spin w-8 h-8 border-4 border-purple-500 border-t-transparent rounded-full mx-auto mb-2"></div>
                  جاري التحميل...
                </div>
              ) : (
                <>
                  {(() => {
                    // حساب مجموع التكاليف الثلاث المعروضة فقط
                    const apartmentsCosts = stats.allCosts?.breakdown?.apartmentsCosts || 0;
                    const carsCosts = stats.allCosts?.breakdown?.carsCosts || 0;
                    const tempWorkersCosts = stats.allCosts?.breakdown?.tempWorkers || 0;
                    const displayedTotal = apartmentsCosts + carsCosts + tempWorkersCosts;

                    return (
                      <>
                        <div className="relative">
                          <div className="flex justify-between items-center mb-2">
                            <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>تكاليف الشقق</span>
                            <span className="text-sm font-medium text-emerald-600">
                              {formatCurrency(apartmentsCosts)}
                            </span>
                          </div>
                          <div className={`w-full ${isDarkMode ? 'bg-slate-700' : 'bg-gray-200'} rounded-full h-3 overflow-hidden`}>
                            <div className="bg-gradient-to-r from-emerald-500 to-emerald-400 h-3 rounded-full transition-all duration-1000 shadow-lg"
                                 style={{
                                   width: `${displayedTotal ? Math.min((apartmentsCosts / displayedTotal * 100), 100) : 0}%`
                                 }}></div>
                          </div>
                          <div className="text-xs text-emerald-600 mt-1 font-medium">
                            {displayedTotal ? ((apartmentsCosts / displayedTotal * 100).toFixed(1)) : 0}%
                          </div>
                        </div>

                        <div className="relative">
                          <div className="flex justify-between items-center mb-2">
                            <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>تكاليف السيارات</span>
                            <span className="text-sm font-medium text-blue-600">
                              {formatCurrency(carsCosts)}
                            </span>
                          </div>
                          <div className={`w-full ${isDarkMode ? 'bg-slate-700' : 'bg-gray-200'} rounded-full h-3 overflow-hidden`}>
                            <div className="bg-gradient-to-r from-blue-500 to-blue-400 h-3 rounded-full transition-all duration-1000 shadow-lg"
                                 style={{
                                   width: `${displayedTotal ? Math.min((carsCosts / displayedTotal * 100), 100) : 0}%`
                                 }}></div>
                          </div>
                          <div className="text-xs text-blue-600 mt-1 font-medium">
                            {displayedTotal ? ((carsCosts / displayedTotal * 100).toFixed(1)) : 0}%
                          </div>
                        </div>

                        <div className="relative">
                          <div className="flex justify-between items-center mb-2">
                            <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                              تكاليف العمالة المؤقتة
                            </span>
                            <span className="text-sm font-medium text-orange-600">
                              {formatCurrency(tempWorkersCosts)}
                            </span>
                          </div>
                          <div className={`w-full ${isDarkMode ? 'bg-slate-700' : 'bg-gray-200'} rounded-full h-3 overflow-hidden`}>
                            <div className="bg-gradient-to-r from-orange-500 to-orange-400 h-3 rounded-full transition-all duration-1000 shadow-lg"
                                 style={{
                                   width: `${displayedTotal ? Math.min((tempWorkersCosts / displayedTotal * 100), 100) : 0}%`
                                 }}></div>
                          </div>
                          <div className="text-xs text-orange-600 mt-1 font-medium">
                            {displayedTotal ? ((tempWorkersCosts / displayedTotal * 100).toFixed(1)) : 0}%
                          </div>
                        </div>
                      </>
                    );
                  })()}
                </>
              )}
            </div>
          </div>

          {/* شارت التكاليف الدائري */}
          <div className={`${isDarkMode ? 'bg-slate-800' : 'bg-white'} rounded-xl p-6 shadow-lg`}>
            <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-4 flex items-center gap-2`}>
              <div className="p-2 rounded-lg bg-cyan-500/20">
                <span className="text-cyan-500">🎯</span>
              </div>
              توزيع التكاليف
            </h3>
            <div className="space-y-6">
              {stats.loading ? (
                <div className="text-center py-8 text-gray-500">
                  <div className="animate-pulse w-32 h-32 bg-gray-300 rounded-full mx-auto"></div>
                </div>
              ) : (
                <>
                  {/* شارت دائري محسن */}
                  {(() => {
                    // حساب مجموع التكاليف الثلاث المعروضة فقط
                    const apartmentsCosts = stats.allCosts?.breakdown?.apartmentsCosts || 0;
                    const carsCosts = stats.allCosts?.breakdown?.carsCosts || 0;
                    const tempWorkersCosts = stats.allCosts?.breakdown?.tempWorkers || 0;
                    const displayedTotal = apartmentsCosts + carsCosts + tempWorkersCosts;

                    const apartmentsPercentage = displayedTotal ? (apartmentsCosts / displayedTotal * 100) : 0;
                    const carsPercentage = displayedTotal ? (carsCosts / displayedTotal * 100) : 0;
                    const tempWorkersPercentage = displayedTotal ? (tempWorkersCosts / displayedTotal * 100) : 0;

                    return (
                      <div className="relative w-40 h-40 mx-auto">
                        <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                          {/* تكاليف الشقق */}
                          <circle
                            cx="50" cy="50" r="35"
                            fill="transparent"
                            stroke="#10B981"
                            strokeWidth="10"
                            strokeDasharray={`${apartmentsPercentage.toFixed(1)} ${100 - apartmentsPercentage}`}
                            strokeDashoffset="0"
                            className="drop-shadow-lg"
                          />
                          {/* تكاليف السيارات */}
                          <circle
                            cx="50" cy="50" r="35"
                            fill="transparent"
                            stroke="#3B82F6"
                            strokeWidth="10"
                            strokeDasharray={`${carsPercentage.toFixed(1)} ${100 - carsPercentage}`}
                            strokeDashoffset={`-${apartmentsPercentage.toFixed(1)}`}
                            className="drop-shadow-lg"
                          />
                          {/* العمالة المؤقتة */}
                          <circle
                            cx="50" cy="50" r="35"
                            fill="transparent"
                            stroke="#F97316"
                            strokeWidth="10"
                            strokeDasharray={`${tempWorkersPercentage.toFixed(1)} ${100 - tempWorkersPercentage}`}
                            strokeDashoffset={`-${(apartmentsPercentage + carsPercentage).toFixed(1)}`}
                            className="drop-shadow-lg"
                          />
                        </svg>
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="text-center">
                            <div className={`text-lg font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                              {formatCurrency(displayedTotal).replace('ج.م.', '').trim()}
                            </div>
                            <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>إجمالي</div>
                          </div>
                        </div>
                      </div>
                    );
                  })()}

                  {/* مفاتيح الألوان */}
                  {(() => {
                    // حساب مجموع التكاليف الثلاث المعروضة فقط
                    const apartmentsCosts = stats.allCosts?.breakdown?.apartmentsCosts || 0;
                    const carsCosts = stats.allCosts?.breakdown?.carsCosts || 0;
                    const tempWorkersCosts = stats.allCosts?.breakdown?.tempWorkers || 0;
                    const displayedTotal = apartmentsCosts + carsCosts + tempWorkersCosts;

                    return (
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-emerald-500 rounded-full shadow-sm"></div>
                            <span className={`text-xs ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>شقق</span>
                          </div>
                          <span className="text-xs font-medium text-emerald-600">
                            {displayedTotal ? ((apartmentsCosts / displayedTotal * 100).toFixed(1)) : 0}%
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-blue-500 rounded-full shadow-sm"></div>
                            <span className={`text-xs ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>سيارات</span>
                          </div>
                          <span className="text-xs font-medium text-blue-600">
                            {displayedTotal ? ((carsCosts / displayedTotal * 100).toFixed(1)) : 0}%
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="w-3 h-3 bg-orange-500 rounded-full shadow-sm"></div>
                            <span className={`text-xs ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>عمالة مؤقتة</span>
                          </div>
                          <span className="text-xs font-medium text-orange-600">
                            {displayedTotal ? ((tempWorkersCosts / displayedTotal * 100).toFixed(1)) : 0}%
                          </span>
                        </div>
                      </div>
                    );
                  })()}
                </>
              )}
            </div>
          </div>
        </div>

        {/* إجراءات سريعة */}
        <div className="mb-6">
          <h2 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'} mb-4 flex items-center gap-2`}>
            ⚡ إجراءات سريعة
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <a href="/employees/add" className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-4 rounded-xl hover:from-blue-700 hover:to-blue-800 transition-all duration-300 text-center group shadow-lg hover:shadow-xl transform hover:scale-105">
              <FiUsers className="text-2xl mx-auto mb-2 group-hover:scale-110 transition-transform" />
              <div className="font-medium">إضافة موظف</div>
              <div className="text-xs opacity-80 mt-1">إضافة موظف جديد للنظام</div>
            </a>

            <a href="/apartments/add" className="bg-gradient-to-r from-emerald-600 to-emerald-700 text-white p-4 rounded-xl hover:from-emerald-700 hover:to-emerald-800 transition-all duration-300 text-center group shadow-lg hover:shadow-xl transform hover:scale-105">
              <FiHome className="text-2xl mx-auto mb-2 group-hover:scale-110 transition-transform" />
              <div className="font-medium">إضافة شقة</div>
              <div className="text-xs opacity-80 mt-1">تسجيل شقة جديدة</div>
            </a>

            <a href="/cars/add" className="bg-gradient-to-r from-orange-600 to-orange-700 text-white p-4 rounded-xl hover:from-orange-700 hover:to-orange-800 transition-all duration-300 text-center group shadow-lg hover:shadow-xl transform hover:scale-105">
              <FiTruck className="text-2xl mx-auto mb-2 group-hover:scale-110 transition-transform" />
              <div className="font-medium">إضافة سيارة</div>
              <div className="text-xs opacity-80 mt-1">تسجيل سيارة جديدة</div>
            </a>

            <a href="/reports" className="bg-gradient-to-r from-purple-600 to-purple-700 text-white p-4 rounded-xl hover:from-purple-700 hover:to-purple-800 transition-all duration-300 text-center group shadow-lg hover:shadow-xl transform hover:scale-105">
              <FiActivity className="text-2xl mx-auto mb-2 group-hover:scale-110 transition-transform" />
              <div className="font-medium">عرض التقارير</div>
              <div className="text-xs opacity-80 mt-1">تقارير شاملة ومفصلة</div>
            </a>
          </div>
        </div>

        {/* الأقسام السفلية - تخطيط متطور */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* إدارة النقل والاستقالة */}
          <div className={`${isDarkMode ? 'bg-slate-800' : 'bg-white'} rounded-xl p-6 shadow-lg`}>
            <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-4 flex items-center gap-2`}>
              <div className="p-2 rounded-lg bg-red-500/20">
                <span className="text-red-500">🔄</span>
              </div>
              إدارة النقل والاستقالة
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <a href="/employees/transfers" className="bg-gradient-to-r from-indigo-600 to-indigo-700 text-white p-4 rounded-xl hover:from-indigo-700 hover:to-indigo-800 transition-all duration-300 text-center group shadow-lg hover:shadow-xl transform hover:scale-105">
                <FiArrowRight className="text-2xl mx-auto mb-2 group-hover:scale-110 transition-transform" />
                <div className="font-medium">حالات النقل</div>
                <div className="text-xs opacity-80 mt-1">عرض وإدارة نقل الموظفين</div>
              </a>

              <a href="/employees/resignations" className="bg-gradient-to-r from-red-600 to-red-700 text-white p-4 rounded-xl hover:from-red-700 hover:to-red-800 transition-all duration-300 text-center group shadow-lg hover:shadow-xl transform hover:scale-105">
                <FiLogOut className="text-2xl mx-auto mb-2 group-hover:scale-110 transition-transform" />
                <div className="font-medium">حالات الاستقالة</div>
                <div className="text-xs opacity-80 mt-1">عرض وإدارة استقالات الموظفين</div>
              </a>

              <a href="/employees/transfers/add" className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-300 text-center group shadow-lg hover:shadow-xl transform hover:scale-105">
                <div className="text-2xl mx-auto mb-2 group-hover:scale-110 transition-transform">➕</div>
                <div className="font-medium">إضافة نقل</div>
                <div className="text-xs opacity-80 mt-1">تسجيل نقل موظف جديد</div>
              </a>

              <a href="/employees/resignations/add" className="bg-gradient-to-r from-orange-500 to-orange-600 text-white p-4 rounded-xl hover:from-orange-600 hover:to-orange-700 transition-all duration-300 text-center group shadow-lg hover:shadow-xl transform hover:scale-105">
                <div className="text-2xl mx-auto mb-2 group-hover:scale-110 transition-transform">❌</div>
                <div className="font-medium">إضافة استقالة</div>
                <div className="text-xs opacity-80 mt-1">تسجيل استقالة موظف</div>
              </a>
            </div>
          </div>

          {/* أرشيف المستندات الجديد */}
          <div className={`${isDarkMode ? 'bg-slate-800' : 'bg-white'} rounded-xl p-6 shadow-lg`}>
            <div className="flex justify-between items-center mb-4">
              <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'} flex items-center gap-2`}>
                <div className="p-2 rounded-lg bg-emerald-500/20">
                  <span className="text-emerald-500">📁</span>
                </div>
                أرشيف المستندات
              </h3>
              <span className={`text-xs px-2 py-1 rounded-full ${isDarkMode ? 'bg-emerald-500/20 text-emerald-400' : 'bg-emerald-100 text-emerald-700'}`}>
                جديد
              </span>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <a href="/documents/employee-files" className="bg-gradient-to-r from-teal-600 to-teal-700 text-white p-4 rounded-xl hover:from-teal-700 hover:to-teal-800 transition-all duration-300 text-center group shadow-lg hover:shadow-xl transform hover:scale-105">
                <div className="text-2xl mx-auto mb-2 group-hover:scale-110 transition-transform">👤</div>
                <div className="font-medium">ملفات الموظفين</div>
                <div className="text-xs opacity-80 mt-1">أرشيف مستندات الموظفين</div>
              </a>

              <a href="/documents/contracts" className="bg-gradient-to-r from-cyan-600 to-cyan-700 text-white p-4 rounded-xl hover:from-cyan-700 hover:to-cyan-800 transition-all duration-300 text-center group shadow-lg hover:shadow-xl transform hover:scale-105">
                <div className="text-2xl mx-auto mb-2 group-hover:scale-110 transition-transform">📄</div>
                <div className="font-medium">العقود</div>
                <div className="text-xs opacity-80 mt-1">أرشيف العقود والاتفاقيات</div>
              </a>

              <a href="/documents/reports" className="bg-gradient-to-r from-violet-600 to-violet-700 text-white p-4 rounded-xl hover:from-violet-700 hover:to-violet-800 transition-all duration-300 text-center group shadow-lg hover:shadow-xl transform hover:scale-105">
                <div className="text-2xl mx-auto mb-2 group-hover:scale-110 transition-transform">📊</div>
                <div className="font-medium">التقارير المحفوظة</div>
                <div className="text-xs opacity-80 mt-1">أرشيف التقارير السابقة</div>
              </a>

              <a href="/documents/upload" className="bg-gradient-to-r from-rose-600 to-rose-700 text-white p-4 rounded-xl hover:from-rose-700 hover:to-rose-800 transition-all duration-300 text-center group shadow-lg hover:shadow-xl transform hover:scale-105">
                <div className="text-2xl mx-auto mb-2 group-hover:scale-110 transition-transform">⬆️</div>
                <div className="font-medium">رفع مستند</div>
                <div className="text-xs opacity-80 mt-1">إضافة مستند جديد</div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
