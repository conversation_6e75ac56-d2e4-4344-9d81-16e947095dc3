'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import { useTheme } from '@/contexts/ThemeContext';
import {
  FiBarChart,
  FiRefreshCw,
  FiTrendingUp,
  FiPieChart
} from 'react-icons/fi';

export default function CustodyChartsPage() {
  const { isDarkMode } = useTheme();
  const [loading, setLoading] = useState(false);
  const [chartData, setChartData] = useState({});
  const [mainCategories, setMainCategories] = useState([]);
  const [subCategories, setSubCategories] = useState([]);
  
  // فلاتر
  const [selectedMainCategory, setSelectedMainCategory] = useState('');
  const [selectedSubCategory, setSelectedSubCategory] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [chartType, setChartType] = useState('bar');

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    if (selectedMainCategory) {
      loadSubCategories();
    } else {
      setSubCategories([]);
      setSelectedSubCategory('');
    }
  }, [selectedMainCategory]);

  useEffect(() => {
    loadChartData();
  }, [selectedMainCategory, selectedSubCategory, startDate, endDate]);

  // تحميل البيانات الأولية
  const loadInitialData = async () => {
    setLoading(true);
    try {
      // تحميل البنود الرئيسية
      const categoriesResponse = await fetch('/api/project-custody', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'getMainCategories' })
      });

      const categoriesResult = await categoriesResponse.json();
      if (categoriesResult.success) {
        setMainCategories(categoriesResult.data);
      }

      // تحميل بيانات الرسوم البيانية
      await loadChartData();

    } catch (error) {

    }
    setLoading(false);
  };

  // تحميل البنود الفرعية
  const loadSubCategories = async () => {
    try {
      const response = await fetch('/api/project-custody', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getSubCategories',
          data: { mainCategoryId: selectedMainCategory }
        })
      });

      const result = await response.json();
      if (result.success) {
        setSubCategories(result.data);
      }
    } catch (error) {

    }
  };

  // تحميل بيانات الرسوم البيانية
  const loadChartData = async () => {
    try {
      const filters = {};
      if (selectedMainCategory) filters.mainCategoryId = selectedMainCategory;
      if (selectedSubCategory) filters.subCategoryId = selectedSubCategory;
      if (startDate) filters.startDate = startDate;
      if (endDate) filters.endDate = endDate;

      const response = await fetch('/api/project-custody', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getCustodyItems',
          data: filters
        })
      });

      const result = await response.json();
      if (result.success) {
        processChartData(result.data);
      }
    } catch (error) {

    }
  };

  // معالجة بيانات الرسوم البيانية
  const processChartData = (data) => {
    // رسم بياني للبنود الرئيسية
    const mainCategoryData = {};
    data.forEach(item => {
      const category = item.mainCategoryName || 'غير محدد';
      if (!mainCategoryData[category]) {
        mainCategoryData[category] = { count: 0, amount: 0 };
      }
      mainCategoryData[category].count += 1;
      mainCategoryData[category].amount += item.amount || 0;
    });

    // رسم بياني للبنود الفرعية
    const subCategoryData = {};
    data.forEach(item => {
      if (item.subCategoryName) {
        const subCategory = item.subCategoryName;
        if (!subCategoryData[subCategory]) {
          subCategoryData[subCategory] = { count: 0, amount: 0 };
        }
        subCategoryData[subCategory].count += 1;
        subCategoryData[subCategory].amount += item.amount || 0;
      }
    });

    // رسم بياني للحالات
    const statusData = {};
    data.forEach(item => {
      const status = item.status || 'غير محدد';
      if (!statusData[status]) {
        statusData[status] = { count: 0, amount: 0 };
      }
      statusData[status].count += 1;
      statusData[status].amount += item.amount || 0;
    });

    // رسم بياني زمني (شهري)
    const monthlyData = {};
    data.forEach(item => {
      if (item.issueDate) {
        const month = new Date(item.issueDate).toLocaleDateString('ar-EG', { 
          year: 'numeric', 
          month: 'long' 
        });
        if (!monthlyData[month]) {
          monthlyData[month] = { count: 0, amount: 0 };
        }
        monthlyData[month].count += 1;
        monthlyData[month].amount += item.amount || 0;
      }
    });

    setChartData({
      mainCategories: mainCategoryData,
      subCategories: subCategoryData,
      statuses: statusData,
      monthly: monthlyData
    });
  };

  // إعدادات الألوان للوضع المظلم/الفاتح
  const getChartColors = () => {
    if (isDarkMode) {
      return {
        background: [
          'rgba(59, 130, 246, 0.8)',
          'rgba(16, 185, 129, 0.8)',
          'rgba(245, 158, 11, 0.8)',
          'rgba(239, 68, 68, 0.8)',
          'rgba(139, 92, 246, 0.8)',
          'rgba(236, 72, 153, 0.8)',
          'rgba(6, 182, 212, 0.8)',
          'rgba(34, 197, 94, 0.8)'
        ],
        border: [
          'rgba(59, 130, 246, 1)',
          'rgba(16, 185, 129, 1)',
          'rgba(245, 158, 11, 1)',
          'rgba(239, 68, 68, 1)',
          'rgba(139, 92, 246, 1)',
          'rgba(236, 72, 153, 1)',
          'rgba(6, 182, 212, 1)',
          'rgba(34, 197, 94, 1)'
        ],
        text: '#e2e8f0',
        grid: '#374151'
      };
    } else {
      return {
        background: [
          'rgba(59, 130, 246, 0.6)',
          'rgba(16, 185, 129, 0.6)',
          'rgba(245, 158, 11, 0.6)',
          'rgba(239, 68, 68, 0.6)',
          'rgba(139, 92, 246, 0.6)',
          'rgba(236, 72, 153, 0.6)',
          'rgba(6, 182, 212, 0.6)',
          'rgba(34, 197, 94, 0.6)'
        ],
        border: [
          'rgba(59, 130, 246, 1)',
          'rgba(16, 185, 129, 1)',
          'rgba(245, 158, 11, 1)',
          'rgba(239, 68, 68, 1)',
          'rgba(139, 92, 246, 1)',
          'rgba(236, 72, 153, 1)',
          'rgba(6, 182, 212, 1)',
          'rgba(34, 197, 94, 1)'
        ],
        text: '#374151',
        grid: '#e5e7eb'
      };
    }
  };

  // إعدادات الرسم البياني العامة
  const getChartOptions = (title) => {
    const colors = getChartColors();
    return {
      responsive: true,
      plugins: {
        legend: {
          position: 'top',
          labels: {
            color: colors.text,
            font: {
              family: 'Cairo, sans-serif'
            }
          }
        },
        title: {
          display: true,
          text: title,
          color: colors.text,
          font: {
            family: 'Cairo, sans-serif',
            size: 16,
            weight: 'bold'
          }
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            color: colors.text
          },
          grid: {
            color: colors.grid
          }
        },
        x: {
          ticks: {
            color: colors.text
          },
          grid: {
            color: colors.grid
          }
        }
      }
    };
  };

  // تنسيق المبلغ
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG').format(amount) + ' جنيه';
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto p-6">
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FiBarChart className="text-3xl text-blue-600" />
              <div>
                <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  الرسوم البيانية لعهد المشروع
                </h1>
                <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                  تحليل بصري لبيانات العهد والتكاليف
                </p>
              </div>
            </div>
            <button
              onClick={loadChartData}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
            >
              <FiRefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              تحديث
            </button>
          </div>
        </div>

        {/* فلاتر */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {/* البند الرئيسي */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                البند الرئيسي
              </label>
              <select
                value={selectedMainCategory}
                onChange={(e) => setSelectedMainCategory(e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  isDarkMode 
                    ? 'bg-gray-700 border-gray-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="">جميع البنود</option>
                {mainCategories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.categoryName}
                  </option>
                ))}
              </select>
            </div>

            {/* البند الفرعي */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                البند الفرعي
              </label>
              <select
                value={selectedSubCategory}
                onChange={(e) => setSelectedSubCategory(e.target.value)}
                disabled={!selectedMainCategory}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  isDarkMode 
                    ? 'bg-gray-700 border-gray-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                } disabled:opacity-50`}
              >
                <option value="">جميع البنود الفرعية</option>
                {subCategories.map((subCategory) => (
                  <option key={subCategory.id} value={subCategory.id}>
                    {subCategory.subCategoryName}
                  </option>
                ))}
              </select>
            </div>

            {/* تاريخ البداية */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                من تاريخ
              </label>
              <input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  isDarkMode 
                    ? 'bg-gray-700 border-gray-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>

            {/* تاريخ النهاية */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                إلى تاريخ
              </label>
              <input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  isDarkMode 
                    ? 'bg-gray-700 border-gray-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>

            {/* نوع الرسم البياني */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                نوع الرسم
              </label>
              <select
                value={chartType}
                onChange={(e) => setChartType(e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  isDarkMode 
                    ? 'bg-gray-700 border-gray-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="bar">أعمدة</option>
                <option value="pie">دائري</option>
                <option value="line">خطي</option>
              </select>
            </div>
          </div>
        </div>

        {/* الرسوم البيانية */}
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <FiRefreshCw className="w-8 h-8 animate-spin text-blue-500" />
            <span className="mr-3 text-lg">جاري تحميل البيانات...</span>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* رسم بياني للبنود الرئيسية */}
            {chartData.mainCategories && Object.keys(chartData.mainCategories).length > 0 && (
              <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
                <h3 className={`text-lg font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  التوزيع حسب البنود الرئيسية
                </h3>
                <div className="h-80 flex items-center justify-center">
                  <div className="text-center">
                    <FiBarChart className="w-16 h-16 mx-auto mb-4 text-blue-500" />
                    <p className={`text-lg ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      رسم بياني للبنود الرئيسية
                    </p>
                    <div className="mt-4 space-y-2">
                      {Object.entries(chartData.mainCategories).map(([category, data]) => (
                        <div key={category} className="flex justify-between items-center">
                          <span className={`text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                            {category}
                          </span>
                          <span className={`text-sm font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                            {formatCurrency(data.amount)}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* رسم بياني للبنود الفرعية */}
            {chartData.subCategories && Object.keys(chartData.subCategories).length > 0 && (
              <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
                <h3 className={`text-lg font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  التوزيع حسب البنود الفرعية
                </h3>
                <div className="h-80 flex items-center justify-center">
                  <div className="text-center">
                    <FiPieChart className="w-16 h-16 mx-auto mb-4 text-green-500" />
                    <p className={`text-lg ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      رسم بياني للبنود الفرعية
                    </p>
                    <div className="mt-4 space-y-2 max-h-48 overflow-y-auto">
                      {Object.entries(chartData.subCategories).map(([category, data]) => (
                        <div key={category} className="flex justify-between items-center">
                          <span className={`text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                            {category}
                          </span>
                          <span className={`text-sm font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                            {formatCurrency(data.amount)}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* رسم بياني للحالات */}
            {chartData.statuses && Object.keys(chartData.statuses).length > 0 && (
              <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
                <h3 className={`text-lg font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  التوزيع حسب الحالة
                </h3>
                <div className="h-80 flex items-center justify-center">
                  <div className="text-center">
                    <FiPieChart className="w-16 h-16 mx-auto mb-4 text-orange-500" />
                    <p className={`text-lg ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      رسم بياني للحالات
                    </p>
                    <div className="mt-4 space-y-2">
                      {Object.entries(chartData.statuses).map(([status, data]) => (
                        <div key={status} className="flex justify-between items-center">
                          <span className={`text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                            {status}
                          </span>
                          <span className={`text-sm font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                            {formatCurrency(data.amount)}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* رسم بياني زمني */}
            {chartData.monthly && Object.keys(chartData.monthly).length > 0 && (
              <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
                <h3 className={`text-lg font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  التوزيع الزمني (شهري)
                </h3>
                <div className="h-80 flex items-center justify-center">
                  <div className="text-center">
                    <FiTrendingUp className="w-16 h-16 mx-auto mb-4 text-purple-500" />
                    <p className={`text-lg ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      رسم بياني زمني
                    </p>
                    <div className="mt-4 space-y-2 max-h-48 overflow-y-auto">
                      {Object.entries(chartData.monthly).map(([month, data]) => (
                        <div key={month} className="flex justify-between items-center">
                          <span className={`text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                            {month}
                          </span>
                          <span className={`text-sm font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                            {formatCurrency(data.amount)}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {/* إحصائيات سريعة */}
        {chartData.mainCategories && (
          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mt-6`}>
            <h3 className={`text-lg font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              إحصائيات سريعة
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className={`text-2xl font-bold ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`}>
                  {Object.values(chartData.mainCategories).reduce((sum, item) => sum + item.count, 0)}
                </div>
                <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                  إجمالي العهد
                </div>
              </div>
              <div className="text-center">
                <div className={`text-2xl font-bold ${isDarkMode ? 'text-green-400' : 'text-green-600'}`}>
                  {formatCurrency(Object.values(chartData.mainCategories).reduce((sum, item) => sum + item.amount, 0))}
                </div>
                <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                  إجمالي المبالغ
                </div>
              </div>
              <div className="text-center">
                <div className={`text-2xl font-bold ${isDarkMode ? 'text-orange-400' : 'text-orange-600'}`}>
                  {Object.keys(chartData.mainCategories).length}
                </div>
                <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                  البنود النشطة
                </div>
              </div>
              <div className="text-center">
                <div className={`text-2xl font-bold ${isDarkMode ? 'text-purple-400' : 'text-purple-600'}`}>
                  {chartData.subCategories ? Object.keys(chartData.subCategories).length : 0}
                </div>
                <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                  البنود الفرعية
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
