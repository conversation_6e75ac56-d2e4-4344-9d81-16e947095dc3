import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

// دالة تنسيق التاريخ بالعربية
function formatDateArabic(dateString) {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  } catch (error) {

    return dateString;
  }
}

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');

    if (!date) {
      return NextResponse.json({ error: 'التاريخ مطلوب' }, { status: 400 });
    }

    const pool = await getConnection();

    // جلب الطلبات المعتمدة من جدول PaperRequests
    const paperRequestsQuery = `
      SELECT 
        EmployeeCode,
        EmployeeName,
        RequestType,
        LeaveType,
        StartDate,
        EndDate,
        DaysCount,
        Status,
        MissionDestination,
        MissionPurpose,
        PermissionStartTime,
        PermissionEndTime,
        NightShiftDate
      FROM PaperRequests 
      WHERE Status = N'معتمدة'
        AND (
          (RequestType = 'leave' AND @date BETWEEN StartDate AND EndDate)
          OR (RequestType = 'mission' AND @date BETWEEN StartDate AND EndDate)
          OR (RequestType = 'permission' AND CAST(@date AS DATE) = CAST(StartDate AS DATE))
          OR (RequestType = 'night_shift' AND CAST(@date AS DATE) = CAST(NightShiftDate AS DATE))
        )
    `;

    const paperRequests = await pool.request()
      .input('date', sql.Date, date)
      .query(paperRequestsQuery);

    // ملاحظة: تم إزالة الاعتماد على جدول LeaveRequests المفقود
    // جميع الطلبات الآن في جدول PaperRequests

    // دمج النتائج وتنسيقها
    const allRequests = [];

    // معالجة طلبات PaperRequests
    paperRequests.recordset.forEach(req => {
      let attendanceType = '';
      let notes = '';

      switch (req.RequestType) {
        case 'leave':
          // تنظيف نوع الإجازة من المسافات الزائدة
          const cleanLeaveType = req.LeaveType ? req.LeaveType.trim().toLowerCase() : '';

          switch (cleanLeaveType) {
            case 'إعتيادية':
            case 'اعتيادية':
            case 'annual':
            case 'عادية':
              attendanceType = 'إجازة إعتيادية';
              notes = `تنتهي في ${formatDateArabic(req.EndDate)}`;
              break;
            case 'عارضة':
            case 'emergency':
            case 'casual':
            case 'طارئة':
              attendanceType = 'إجازة عارضة';
              notes = `تنتهي في ${formatDateArabic(req.EndDate)}`;
              break;
            case 'مرضية':
            case 'sick':
            case 'مرض':
              attendanceType = 'إجازة مرضية';
              notes = `تنتهي في ${formatDateArabic(req.EndDate)}`;
              break;
            case 'بدون أجر':
            case 'بدون راتب':
            case 'unpaid':
              attendanceType = 'إجازة بدون أجر';
              notes = `تنتهي في ${formatDateArabic(req.EndDate)}`;
              break;
            case 'بدل':
            case 'compensatory':
            case 'تعويضية':
              attendanceType = 'إجازة بدل';
              notes = `تنتهي في ${formatDateArabic(req.EndDate)}`;
              break;
            case 'أمومة':
            case 'maternity':
              attendanceType = 'إجازة أمومة';
              notes = `تنتهي في ${formatDateArabic(req.EndDate)}`;
              break;
            case 'أبوة':
            case 'paternity':
              attendanceType = 'إجازة أبوة';
              notes = `تنتهي في ${formatDateArabic(req.EndDate)}`;
              break;
            case 'وفاة':
            case 'death':
            case 'عزاء':
              attendanceType = 'إجازة وفاة';
              notes = `تنتهي في ${formatDateArabic(req.EndDate)}`;
              break;
            case 'زواج':
            case 'marriage':
              attendanceType = 'إجازة زواج';
              notes = `تنتهي في ${formatDateArabic(req.EndDate)}`;
              break;
            case 'حج':
            case 'hajj':
            case 'عمرة':
              attendanceType = 'إجازة حج';
              notes = `تنتهي في ${formatDateArabic(req.EndDate)}`;
              break;
            case 'امتحانات':
            case 'exams':
            case 'دراسة':
              attendanceType = 'إجازة امتحانات';
              notes = `تنتهي في ${formatDateArabic(req.EndDate)}`;
              break;
            default:
              // للأنواع غير المعروفة، استخدم النوع كما هو مع تنظيف
              const displayType = req.LeaveType || 'غير محدد';
              attendanceType = `إجازة ${displayType}`;
              notes = `تنتهي في ${formatDateArabic(req.EndDate)}`;
              console.log(`⚠️ نوع إجازة غير معروف: "${req.LeaveType}" (منظف: "${cleanLeaveType}")`);
          }
          break;

        case 'mission':
          attendanceType = 'مأمورية';
          const destination = req.MissionDestination || 'غير محدد';
          if (req.StartDate && req.EndDate) {
            const startDate = formatDateArabic(req.StartDate);
            const endDate = formatDateArabic(req.EndDate);
            if (startDate === endDate) {
              notes = `مأمورية إلى ${destination}`;
            } else {
              notes = `مأمورية إلى ${destination} - تنتهي في ${endDate}`;
            }
          } else {
            notes = `مأمورية إلى ${destination}`;
          }
          break;

        case 'permission':
          attendanceType = 'إذن';
          const startTime = req.PermissionStartTime || '';
          const endTime = req.PermissionEndTime || '';
          if (startTime && endTime) {
            notes = `إذن من ${startTime} إلى ${endTime}`;
          } else {
            notes = 'إذن معتمد';
          }
          break;

        case 'night_shift':
          attendanceType = 'وردية ليلية';
          notes = 'وردية ليلية معتمدة';
          break;

        default:
          attendanceType = 'إجراء معتمد';
          notes = `${req.RequestType} معتمد`;

      }

      // التحقق من صحة البيانات قبل الإضافة
      if (attendanceType && req.EmployeeCode) {
        allRequests.push({
          employeeCode: req.EmployeeCode,
          employeeName: req.EmployeeName || 'غير محدد',
          attendanceType,
          notes,
          requestType: req.RequestType,
          leaveType: req.LeaveType,
          startDate: req.StartDate,
          endDate: req.EndDate,
          source: 'PaperRequests'
        });

      } else {

      }
    });

    // إحصائيات سريعة
    const stats = {
      total: allRequests.length,
      leaves: allRequests.filter(r => r.requestType === 'leave').length,
      missions: allRequests.filter(r => r.requestType === 'mission').length,
      permissions: allRequests.filter(r => r.requestType === 'permission').length,
      nightShifts: allRequests.filter(r => r.requestType === 'night_shift').length
    };

    return NextResponse.json({
      success: true,
      data: allRequests,
      count: allRequests.length,
      date: date,
      stats: stats
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'حدث خطأ في جلب طلبات التمام',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}
