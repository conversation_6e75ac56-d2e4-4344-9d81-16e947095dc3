/**
 * Font Loading Utility
 * Handles font loading with fallbacks and error handling
 */

// System font stacks for different use cases
export const fontStacks = {
  // Primary sans-serif stack
  sans: [
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'Noto Sans',
    'sans-serif',
    'Apple Color Emoji',
    'Segoe UI Emoji',
    'Segoe UI Symbol',
    'Noto Color Emoji'
  ].join(', '),

  // Arabic-optimized font stack
  arabic: [
    'Segoe UI',
    'Tahoma',
    'Arial Unicode MS',
    'Lucida Grande',
    'sans-serif'
  ].join(', '),

  // Monospace font stack
  mono: [
    'ui-monospace',
    'SFMono-Regular',
    'Monaco',
    'Consolas',
    'Liberation Mono',
    'Courier New',
    'monospace'
  ].join(', '),

  // Display font stack (for headings)
  display: [
    'system-ui',
    '-apple-system',
    'BlinkMacSystemFont',
    'Segoe UI',
    'Roboto',
    'Helvetica Neue',
    'Arial',
    'sans-serif'
  ].join(', ')
};

// Font loading status
let fontLoadingStatus = {
  loaded: false,
  error: false,
  loading: false
};

/**
 * Apply system fonts immediately without external dependencies
 */
export function applySystemFonts() {
  if (typeof document === 'undefined') return;

  const style = document.createElement('style');
  style.textContent = `
    :root {
      --font-sans: ${fontStacks.sans};
      --font-arabic: ${fontStacks.arabic};
      --font-mono: ${fontStacks.mono};
      --font-display: ${fontStacks.display};
    }
    
    html, body {
      font-family: var(--font-sans);
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    
    [lang="ar"], .arabic-text {
      font-family: var(--font-arabic);
    }
    
    code, pre, .font-mono {
      font-family: var(--font-mono);
    }
    
    h1, h2, h3, h4, h5, h6, .font-display {
      font-family: var(--font-display);
    }
  `;
  
  document.head.appendChild(style);
  fontLoadingStatus.loaded = true;
}

/**
 * Check if fonts are available
 */
export function checkFontAvailability(fontName) {
  if (typeof document === 'undefined') return false;

  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  
  // Test with a fallback font
  context.font = '12px monospace';
  const fallbackWidth = context.measureText('abcdefghijklmnopqrstuvwxyz').width;
  
  // Test with the target font
  context.font = `12px ${fontName}, monospace`;
  const targetWidth = context.measureText('abcdefghijklmnopqrstuvwxyz').width;
  
  return fallbackWidth !== targetWidth;
}

/**
 * Get the best available font for the current system
 */
export function getBestAvailableFont(type = 'sans') {
  if (typeof window === 'undefined') return fontStacks[type];

  const testFonts = {
    sans: ['Segoe UI', 'system-ui', '-apple-system', 'Roboto'],
    arabic: ['Segoe UI', 'Tahoma', 'Arial Unicode MS'],
    mono: ['SFMono-Regular', 'Monaco', 'Consolas']
  };

  const fontsToTest = testFonts[type] || testFonts.sans;
  
  for (const font of fontsToTest) {
    if (checkFontAvailability(font)) {
      return font;
    }
  }
  
  return fontStacks[type];
}

/**
 * Initialize font loading
 */
export function initializeFonts() {
  if (typeof window === 'undefined') return Promise.resolve();

  return new Promise((resolve) => {
    if (fontLoadingStatus.loaded) {
      resolve();
      return;
    }

    fontLoadingStatus.loading = true;

    // Apply system fonts immediately
    applySystemFonts();

    // Add font loading class to body
    document.body.classList.add('font-loaded');
    document.body.classList.remove('font-loading');

    fontLoadingStatus.loading = false;
    fontLoadingStatus.loaded = true;
    
    resolve();
  });
}

/**
 * Font loading hook for React components
 */
export function useFontLoader() {
  if (typeof window !== 'undefined' && !fontLoadingStatus.loaded) {
    initializeFonts();
  }
  
  return fontLoadingStatus;
}

// Auto-initialize fonts when module loads in browser
if (typeof window !== 'undefined') {
  // Use requestIdleCallback if available, otherwise setTimeout
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => initializeFonts());
  } else {
    setTimeout(() => initializeFonts(), 0);
  }
}

export default {
  fontStacks,
  applySystemFonts,
  checkFontAvailability,
  getBestAvailableFont,
  initializeFonts,
  useFontLoader
};
