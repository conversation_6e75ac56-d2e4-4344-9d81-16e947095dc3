/* Font Fallbacks - Critical CSS for immediate font loading */

/* Critical font definitions that load immediately */
/* System font variables */
:root {
    --font-system: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', 
                   Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
    --font-arabic: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', 'Lucida Grande', sans-serif;
    --font-mono: ui-monospace, SFMono-Regular, 'Monaco', 'Consolas', 
                 'Liberation Mono', 'Courier New', monospace;
  }

  /* Base font application */
  html {
    font-family: var(--font-system);
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    font-family: var(--font-system);
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  }

  /* Arabic text optimization */
  [lang="ar"], 
  [dir="rtl"], 
  .arabic-text {
    font-family: var(--font-arabic);
    text-align: right;
    direction: rtl;
  }

  /* Code and monospace */
  code, 
  pre, 
  kbd, 
  samp,
  .font-mono {
    font-family: var(--font-mono);
  }

/* Headings */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-system);
  font-weight: 600;
}

/* Prevent FOUT (Flash of Unstyled Text) */
.font-loading {
  visibility: hidden;
}

.font-loaded {
  visibility: visible;
}

/* Font display optimizations */
@supports (font-display: swap) {
  * {
    font-display: swap;
  }
}

/* OS-specific optimizations */

/* Windows */
@media screen and (-ms-high-contrast: active), 
       screen and (-ms-high-contrast: none) {
  :root {
    --font-system: 'Segoe UI', Tahoma, Arial, sans-serif;
  }
}

/* macOS */
@supports (-webkit-appearance: none) {
  :root {
    --font-system: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
}

/* Linux */
@media screen and (min-resolution: 1dppx) {
  :root {
    --font-system: 'Ubuntu', 'Liberation Sans', 'DejaVu Sans', Arial, sans-serif;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), 
       (min-resolution: 192dpi) {
  body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  * {
    font-smooth: never;
    -webkit-font-smoothing: subpixel-antialiased;
  }
}

/* Font loading states */
.fonts-loading * {
  font-family: var(--font-system) !important;
}

.fonts-loaded * {
  transition: font-family 0.1s ease-out;
}

/* Emergency fallback for critical text */
.critical-text {
  font-family: Arial, sans-serif !important;
  font-display: block;
}

/* Utility classes for font stacks */
.font-system {
  font-family: var(--font-system) !important;
}

.font-arabic {
  font-family: var(--font-arabic) !important;
}

.font-mono {
  font-family: var(--font-mono) !important;
}

/* Performance optimizations */
.font-optimized {
  font-kerning: auto;
  font-variant-ligatures: common-ligatures;
  font-variant-numeric: oldstyle-nums;
  text-rendering: optimizeLegibility;
}

/* Accessibility improvements */
@media (prefers-contrast: high) {
  body {
    font-weight: 500;
  }
}

/* Print styles */
@media print {
  * {
    font-family: 'Times New Roman', serif !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
}
