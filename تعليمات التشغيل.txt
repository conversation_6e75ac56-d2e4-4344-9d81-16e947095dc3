🚀 تعليمات تشغيل نظام إدارة التكاليف
========================================

📁 الملفات المتاحة للتشغيل:
========================================

1️⃣ تشغيل النظام.cmd
   - الملف الأسرع والأبسط
   - انقر مرتين لتشغيل النظام فوراً
   - يفتح المتصفح تلقائياً

2️⃣ start-system.bat
   - ملف شامل مع فحص شامل للنظام
   - يتحقق من Node.js و npm
   - يثبت التبعيات إذا لم تكن موجودة
   - مناسب للتشغيل الأول

3️⃣ start-system.ps1
   - ملف PowerShell متقدم
   - واجهة ملونة وجميلة
   - فحص شامل مع رسائل واضحة
   - يفتح المتصفح تلقائياً بعد 5 ثوان

📋 طريقة الاستخدام:
========================================

الطريقة السريعة:
1. انقر مرتين على "تشغيل النظام.cmd"
2. انتظر حتى يبدأ الخادم
3. سيفتح المتصفح تلقائياً على http://localhost:3001

الطريقة الشاملة (للمرة الأولى):
1. انقر مرتين على "start-system.bat"
2. اتبع الرسائل على الشاشة
3. انتظر حتى يكتمل التثبيت والتشغيل

🔧 متطلبات النظام:
========================================
- Node.js (الإصدار 16 أو أحدث)
- npm (يأتي مع Node.js)
- Windows 10/11
- متصفح ويب حديث

🌐 روابط النظام:
========================================
- الصفحة الرئيسية: http://localhost:3001
- لوحة التحكم: http://localhost:3001/dashboard
- إدارة التكاليف: http://localhost:3001/costs/dashboard
- طلبات الإصدار: http://localhost:3001/costs/version-requests

⚠️ ملاحظات مهمة:
========================================
- تأكد من إغلاق أي خادم آخر يعمل على نفس المنفذ
- لا تغلق نافذة الأوامر أثناء استخدام النظام
- لإيقاف الخادم اضغط Ctrl + C في نافذة الأوامر

🔄 إعادة التشغيل:
========================================
إذا توقف النظام أو حدث خطأ:
1. أغلق نافذة الأوامر
2. انقر مرتين على أي من ملفات التشغيل مرة أخرى

📞 الدعم الفني:
========================================
في حالة وجود مشاكل:
1. تأكد من تثبيت Node.js
2. تأكد من وجود ملفات المشروع في E:\web\project
3. جرب تشغيل "start-system.bat" للفحص الشامل

🎯 نصائح للاستخدام الأمثل:
========================================
- استخدم "تشغيل النظام.cmd" للاستخدام اليومي
- استخدم "start-system.bat" عند حدوث مشاكل
- احتفظ بنسخة احتياطية من ملفات التشغيل
- لا تحذف مجلد node_modules

تم إنشاء هذه الملفات لتسهيل تشغيل النظام
تاريخ الإنشاء: يوليو 2025
