'use client';

import React, { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import {
  FiDatabase,
  FiTable,
  FiUsers,
  FiHome,
  FiTruck,
  FiAlertTriangle,
  FiCheckCircle,
  FiRefreshCw,
  FiDownload,
  FiSettings
} from 'react-icons/fi';

export default function DatabaseAuditPage() {
  const { isArabic } = useLanguage();
  const [auditReport, setAuditReport] = useState(null);
  const [loading, setLoading] = useState(false);
  const [fixing, setFixing] = useState(false);

  // جلب التقرير
  const fetchAuditReport = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/generate-audit-report');
      const result = await response.json();
      
      if (result.success) {
        setAuditReport(result.report);
      } else {
        alert('خطأ في جلب التقرير: ' + result.message);
      }
    } catch (error) {

      alert('خطأ في الاتصال بالخادم');
    } finally {
      setLoading(false);
    }
  };

  // إصلاح مشاكل التسمية
  const fixNamingIssues = async () => {
    setFixing(true);
    try {
      const response = await fetch('/api/generate-audit-report', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'fix_naming' })
      });
      
      const result = await response.json();
      
      if (result.success) {
        alert('تم إصلاح مشاكل التسمية بنجاح');
        fetchAuditReport(); // إعادة جلب التقرير
      } else {
        alert('خطأ في إصلاح المشاكل: ' + result.message);
      }
    } catch (error) {

      alert('خطأ في الاتصال بالخادم');
    } finally {
      setFixing(false);
    }
  };

  useEffect(() => {
    fetchAuditReport();
  }, []);

  return (
    <MainLayout>
      <div className="p-6 max-w-7xl mx-auto">
        {/* العنوان */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FiDatabase className="text-3xl text-blue-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                  {isArabic ? 'مراجعة قاعدة البيانات' : 'Database Audit'}
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  {isArabic ? 'مراجعة شاملة لجميع الجداول وأسماء الأعمدة' : 'Comprehensive review of all tables and column names'}
                </p>
              </div>
            </div>
            
            <div className="flex gap-3">
              <button
                onClick={fetchAuditReport}
                disabled={loading}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center gap-2"
              >
                <FiRefreshCw className={loading ? 'animate-spin' : ''} />
                {loading ? (isArabic ? 'جاري التحديث...' : 'Refreshing...') : (isArabic ? 'تحديث' : 'Refresh')}
              </button>
              
              {auditReport && auditReport.recommendations.length > 0 && (
                <button
                  onClick={fixNamingIssues}
                  disabled={fixing}
                  className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center gap-2"
                >
                  <FiSettings className={fixing ? 'animate-spin' : ''} />
                  {fixing ? (isArabic ? 'جاري الإصلاح...' : 'Fixing...') : (isArabic ? 'إصلاح المشاكل' : 'Fix Issues')}
                </button>
              )}
            </div>
          </div>
        </div>

        {loading && !auditReport && (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <span className="mr-3 text-gray-600 dark:text-gray-400">
              {isArabic ? 'جاري تحليل قاعدة البيانات...' : 'Analyzing database...'}
            </span>
          </div>
        )}

        {auditReport && (
          <div className="space-y-6">
            {/* الملخص */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                <div className="flex items-center gap-3">
                  <FiTable className="text-2xl text-blue-600" />
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {isArabic ? 'إجمالي الجداول' : 'Total Tables'}
                    </p>
                    <p className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                      {auditReport.summary.totalTables}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                <div className="flex items-center gap-3">
                  <FiUsers className="text-2xl text-green-600" />
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {isArabic ? 'جداول الموظفين' : 'Employee Tables'}
                    </p>
                    <p className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                      {auditReport.summary.employeeRelatedTables}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                <div className="flex items-center gap-3">
                  <FiHome className="text-2xl text-purple-600" />
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {isArabic ? 'جداول الشقق' : 'Apartment Tables'}
                    </p>
                    <p className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                      {auditReport.summary.apartmentRelatedTables}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                <div className="flex items-center gap-3">
                  <FiTruck className="text-2xl text-orange-600" />
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {isArabic ? 'جداول السيارات' : 'Car Tables'}
                    </p>
                    <p className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                      {auditReport.summary.carRelatedTables}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
                <div className="flex items-center gap-3">
                  {auditReport.summary.totalIssues > 0 ? (
                    <FiAlertTriangle className="text-2xl text-red-600" />
                  ) : (
                    <FiCheckCircle className="text-2xl text-green-600" />
                  )}
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {isArabic ? 'المشاكل' : 'Issues'}
                    </p>
                    <p className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                      {auditReport.summary.totalIssues}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* التوصيات */}
            {auditReport.recommendations.length > 0 && (
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
                <div className="flex items-center gap-2 mb-4">
                  <FiAlertTriangle className="text-yellow-600" />
                  <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">
                    {isArabic ? 'التوصيات' : 'Recommendations'}
                  </h3>
                </div>
                
                <div className="space-y-3">
                  {auditReport.recommendations.map((rec, index) => (
                    <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-yellow-200 dark:border-yellow-700">
                      <h4 className="font-medium text-gray-800 dark:text-gray-200 mb-2">
                        {rec.type}
                      </h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {isArabic ? 'التسميات الحالية:' : 'Current variations:'} {rec.variations.join(', ')}
                      </p>
                      <p className="text-sm text-green-600 dark:text-green-400">
                        {isArabic ? 'التسمية المقترحة:' : 'Recommended:'} <strong>{rec.recommended}</strong>
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* الجداول المرتبطة بالموظفين */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
                  <FiUsers className="text-green-600" />
                  {isArabic ? 'الجداول المرتبطة بالموظفين' : 'Employee-Related Tables'}
                </h3>
              </div>
              
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'اسم الجدول' : 'Table Name'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'عمود كود الموظف' : 'Employee Code Column'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'عمود اسم الموظف' : 'Employee Name Column'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'الحالة' : 'Status'}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {auditReport.employeeRelatedTables.map((table, index) => (
                      <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                          {table.table}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          <span className={`px-2 py-1 rounded text-xs ${
                            ['EmployeeCode', 'EmployeeID'].includes(table.employeeCodeColumn)
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
                              : table.employeeCodeColumn === 'غير موجود'
                              ? 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300'
                              : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300'
                          }`}>
                            {table.employeeCodeColumn}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          <span className={`px-2 py-1 rounded text-xs ${
                            ['EmployeeName', 'FullName'].includes(table.employeeNameColumn)
                              ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
                              : table.employeeNameColumn === 'غير موجود'
                              ? 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300'
                              : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300'
                          }`}>
                            {table.employeeNameColumn}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          {(['EmployeeCode', 'EmployeeID'].includes(table.employeeCodeColumn) && 
                            ['EmployeeName', 'FullName'].includes(table.employeeNameColumn)) ? (
                            <span className="flex items-center gap-1 text-green-600">
                              <FiCheckCircle />
                              {isArabic ? 'سليم' : 'OK'}
                            </span>
                          ) : (
                            <span className="flex items-center gap-1 text-yellow-600">
                              <FiAlertTriangle />
                              {isArabic ? 'يحتاج مراجعة' : 'Needs Review'}
                            </span>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
