'use client';

import MainLayout from '@/components/MainLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { FiArrowLeft, FiClock, FiDownload, FiPrinter, FiSave } from 'react-icons/fi';

const PermissionRequestPage = () => {
  const router = useRouter();
  const { isArabic } = useLanguage();
  const [loading, setLoading] = useState(false);
  const [employeeSearchResults, setEmployeeSearchResults] = useState([]);
  const [showEmployeeSearch, setShowEmployeeSearch] = useState(false);
  const [formData, setFormData] = useState({
    employeeName: '',
    employeeId: '',
    department: '',
    jobTitle: '',
    nationalId: '',
    permissionType: '',
    startTime: '',
    endTime: '',
    totalHours: '',
    reason: '',
    emergencyContact: '',
    emergencyPhone: '',
    replacementEmployee: '',
    notes: ''
  });

  // أنواع الإذن
  const permissionTypes = [
    { value: 'personal', label: isArabic ? 'إذن شخصي' : 'Personal Permission' },
    { value: 'medical', label: isArabic ? 'إذن طبي' : 'Medical Permission' },
    { value: 'emergency', label: isArabic ? 'إذن طارئ' : 'Emergency Permission' },
    { value: 'official', label: isArabic ? 'إذن رسمي' : 'Official Permission' }
  ];

  // جلب بيانات الموظف المسجل
  useEffect(() => {
    const userInfo = localStorage.getItem('userInfo');
    if (userInfo) {
      const user = JSON.parse(userInfo);
      setFormData(prev => ({
        ...prev,
        employeeName: user.fullName || user.username,
        employeeId: user.employeeId || '',
        department: user.department || '',
        jobTitle: user.jobTitle || ''
      }));
    }
  }, []);

  // حساب عدد الساعات
  useEffect(() => {
    if (formData.startTime && formData.endTime) {
      const start = new Date(`2000-01-01T${formData.startTime}`);
      const end = new Date(`2000-01-01T${formData.endTime}`);
      const diffMs = end - start;
      const diffHours = Math.abs(diffMs / (1000 * 60 * 60));
      setFormData(prev => ({ ...prev, totalHours: diffHours.toFixed(1) }));
    }
  }, [formData.startTime, formData.endTime]);

  // البحث عن الموظفين من قاعدة البيانات
  const searchEmployees = async (searchTerm) => {
    if (!searchTerm || searchTerm.length < 2) {
      setEmployeeSearchResults([]);
      setShowEmployeeSearch(false);
      return;
    }

    try {
      const response = await fetch('/api/employee-live-search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          searchTerm: searchTerm,
          limit: 10
        })
      });

      const result = await response.json();
      if (result.success) {
        const employees = result.data || [];
        // تحويل البيانات للتنسيق المطلوب
        const formattedEmployees = employees.map(emp => ({
          employeeId: emp.EmployeeCode || emp.employeeCode,
          fullName: emp.EmployeeName || emp.employeeName,
          department: emp.Department || emp.department,
          jobTitle: emp.JobTitle || emp.jobTitle,
          nationalId: emp.NationalID || emp.nationalId || ''
        }));

        setEmployeeSearchResults(formattedEmployees);
        setShowEmployeeSearch(formattedEmployees.length > 0);
      } else {

        setEmployeeSearchResults([]);
        setShowEmployeeSearch(false);
      }
    } catch (error) {

      setEmployeeSearchResults([]);
      setShowEmployeeSearch(false);
    }
  };

  // اختيار موظف من نتائج البحث
  const selectEmployee = (employee) => {
    setFormData(prev => ({
      ...prev,
      employeeId: employee.employeeId,
      employeeName: employee.fullName,
      department: employee.department,
      jobTitle: employee.jobTitle
    }));
    setShowEmployeeSearch(false);
    setEmployeeSearchResults([]);
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // البحث عن الموظفين عند كتابة كود الموظف
    if (name === 'employeeId') {
      searchEmployees(value);
      if (value !== formData.employeeId) {
        setFormData(prev => ({
          ...prev,
          employeeName: '',
          department: '',
          jobTitle: ''
        }));
      }
    }
  };

  // دالة التحقق من الإجراءات الموجودة والقيود
  const checkExistingActions = async () => {
    try {
      // للأذونات، نستخدم نفس التاريخ للبداية والنهاية
      const currentDate = new Date().toLocaleDateString('en-GB').split('/').reverse().join('-');

      const response = await fetch('/api/check-employee-actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          employeeCode: formData.employeeId,
          startDate: currentDate,
          endDate: currentDate,
          actionType: 'permission'
        })
      });

      const result = await response.json();
      return result;
    } catch (error) {

      return { success: false, error: 'خطأ في التحقق من الإجراءات' };
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // التحقق من الحقول المطلوبة
    if (!formData.employeeId || !formData.permissionType || !formData.startTime || !formData.endTime) {
      alert(isArabic ? 'يرجى ملء جميع الحقول المطلوبة' : 'Please fill all required fields');
      return;
    }

    setLoading(true);

    // التحقق من الإجراءات الموجودة والقيود
    const checkResult = await checkExistingActions();

    if (!checkResult.success) {
      alert(checkResult.error || 'خطأ في التحقق من الإجراءات');
      setLoading(false);
      return;
    }

    // إذا كان هناك تضارب
    if (checkResult.hasConflict) {
      const conflict = checkResult.conflictDetails;

      // رسالة التحذير
      const conflictMessage = `⚠️ تحذير: يوجد إجراء "${conflict.actionType}" مسجل بالفعل للموظف!\n\n` +
        `الموظف: ${formData.employeeName} (${formData.employeeId})\n` +
        `الإجراء الموجود: ${conflict.actionType}\n` +
        `التاريخ: ${conflict.startDate}\n` +
        `الحالة: ${conflict.status}\n\n`;

      // إذا كان الإجراء معتمد، لا يمكن الاستبدال
      if (conflict.status === 'معتمد' || conflict.status === 'approved') {
        alert(conflictMessage + 'لا يمكن استبدال الإجراءات المعتمدة. يرجى اختيار تاريخ آخر.');
        setLoading(false);
        return;
      }

      // إذا كان الإجراء قيد المراجعة، اعرض خيارات الاستبدال
      const userChoice = confirm(conflictMessage +
        'هل تريد استبدال الإجراء الموجود بالإجراء الجديد؟\n\n' +
        'اضغط "موافق" للاستبدال أو "إلغاء" للعودة وتعديل البيانات.');

      if (!userChoice) {
        setLoading(false);
        return;
      }

      // تنفيذ الاستبدال الفعلي

      try {
        const replaceResponse = await fetch('/api/replace-employee-action', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            conflictId: conflict.id,
            conflictSource: conflict.source,
            newRequestData: {
              requestType: 'permission',
              employeeCode: formData.employeeId,
              employeeName: formData.employeeName,
              startDate: new Date().toLocaleDateString('en-GB').split('/').reverse().join('-'),
              endDate: new Date().toLocaleDateString('en-GB').split('/').reverse().join('-'),
              permissionType: formData.permissionType,
              reason: formData.reason,
              notes: `من ${formData.startTime} إلى ${formData.endTime}`
            }
          })
        });

        const replaceResult = await replaceResponse.json();

        if (replaceResult.success) {
          alert('تم استبدال الإجراء وتقديم طلب الإذن بنجاح!');
          // إعادة تعيين النموذج
          setFormData({
            employeeName: '',
            employeeId: '',
            department: '',
            jobTitle: '',
            permissionType: '',
            startTime: '',
            endTime: '',
            reason: ''
          });
          setLoading(false);
          return;
        } else {
          alert('خطأ في الاستبدال: ' + replaceResult.error);
          setLoading(false);
          return;
        }
      } catch (replaceError) {

        alert('خطأ في تنفيذ الاستبدال');
        setLoading(false);
        return;
      }
    }

    // إذا كان هناك قيود (استقالة، نقل، تاريخ انضمام)
    if (checkResult.hasRestriction) {
      alert(checkResult.restrictionMessage);
      setLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/paper-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'create',
          requestType: 'permission',
          ...formData
        }),
      });

      const result = await response.json();

      if (response.ok) {
        alert(isArabic ? 'تم تقديم طلب الإذن بنجاح!' : 'Permission request submitted successfully!');
        // لا نوجه المستخدم تلقائياً - يبقى في نفس الصفحة
      } else {
        alert(result.message || (isArabic ? 'خطأ في تقديم الطلب' : 'Error submitting request'));
      }
    } catch (error) {

      alert(isArabic ? 'خطأ في الاتصال بالخادم' : 'Server connection error');
    } finally {
      setLoading(false);
    }
  };

  const downloadForm = async () => {
    try {
      const response = await fetch('/api/requests/download-form?type=permission');
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'نموذج_طلب_الإذن.xlsx';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {

    }
  };

  // تحويل اللوجو إلى base64
  const getLogoBase64 = async () => {
    try {
      const response = await fetch('/logo.png');
      const blob = await response.blob();
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result.split(',')[1]);
        reader.readAsDataURL(blob);
      });
    } catch (error) {

      return '';
    }
  };

  // طباعة نموذج الإذن المملوء
  const printFilledForm = async () => {
    // تحميل اللوجو أولاً قبل فتح نافذة الطباعة
    const logoBase64 = await getLogoBase64();

    // انتظار إضافي للتأكد من تحميل اللوجو
    await new Promise(resolve => setTimeout(resolve, 1000));

    const printWindow = window.open('', '_blank');

    const printContent = `
      <!DOCTYPE html>
      <html dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>نموذج طلب إذن - ${formData.employeeName}</title>
        <style>
          @page {
            size: A4;
            margin: 15mm;
          }
          body {
            font-family: 'Arial', sans-serif;
            font-size: 11px;
            line-height: 1.2;
            color: #000;
            margin: 0;
            padding: 0;
            direction: rtl;
            background: white;
          }

          .header {
            display: table;
            width: 100%;
            border: 2px solid #000;
            margin-bottom: 15px;
            border-collapse: collapse;
          }
          .header-row {
            display: table-row;
          }
          .logo-section {
            display: table-cell;
            width: 33.33%;
            border-left: 1px solid #000;
            text-align: center;
            vertical-align: middle;
            padding: 10px;
          }
          .logo-img {
            max-width: 100px;
            max-height: 70px;
            object-fit: contain;
          }
          .form-code {
            display: table-cell;
            width: 33.33%;
            border-left: 1px solid #000;
            text-align: center;
            vertical-align: middle;
            padding: 10px;
          }
          .form-code-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
          }
          .form-code-number {
            font-size: 12px;
            color: #666;
          }
          .company-info {
            display: table-cell;
            width: 33.33%;
            text-align: center;
            vertical-align: middle;
            padding: 10px;
          }
          .company-name-ar {
            font-size: 13px;
            font-weight: bold;
            margin-bottom: 3px;
          }
          .company-name-en {
            font-size: 10px;
            color: #666;
          }

          .form-title {
            background-color: #a8c5f0;
            text-align: center;
            padding: 8px;
            font-size: 14px;
            font-weight: bold;
            border: 1px solid #000;
            margin-bottom: 15px;
          }

          .form-row {
            display: flex;
            margin-bottom: 12px;
            align-items: center;
          }
          .field-group {
            display: flex;
            align-items: center;
            margin-left: 30px;
          }
          .field-label {
            font-weight: bold;
            margin-left: 8px;
            min-width: 80px;
          }
          .field-value {
            border-bottom: 1px solid #000;
            min-width: 150px;
            padding: 2px 5px;
            font-weight: normal;
          }

          .signatures {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
          }
          .signature-box {
            width: 150px;
            text-align: center;
          }
          .signature-title {
            font-weight: bold;
            margin-bottom: 30px;
            font-size: 10px;
          }
          .signature-line {
            border-bottom: 1px solid #000;
            height: 1px;
            margin-bottom: 20px;
          }

          @media print {
            body { margin: 0; padding: 10px; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="header-row">
            <div class="company-info">
              <div class="company-name-ar">شركة كونكورد للهندسة والمقاولات</div>
              <div class="company-name-en">Concord for Engineering & Contracting</div>
            </div>
            <div class="form-code">
              <div class="form-code-title">طلب إذن</div>
              <div class="form-code-number">HR-OP-02-F01</div>
            </div>
            <div class="logo-section">
              ${logoBase64 ?
                `<img src="data:image/png;base64,${logoBase64}" alt="Concord Logo" class="logo-img" />` :
                `<div style="background:#1e40af;color:white;padding:10px;text-align:center;font-weight:bold;font-size:10px;">CONCORD<br>COMPANY</div>`
              }
            </div>
          </div>
        </div>

        <!-- Employee Info Section -->
        <div style="margin-bottom: 20px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
            <div style="display: flex; align-items: center;">
              <span style="font-weight: bold; margin-left: 10px;">كود الوظيفي</span>
              <div style="border-bottom: 1px solid #000; width: 100px; text-align: center; padding: 3px;">
                ${formData.employeeId || '5528'}
              </div>
            </div>
            <div style="display: flex; align-items: center;">
              <span style="font-weight: bold; margin-left: 10px;">التاريخ</span>
              <div style="border-bottom: 1px solid #000; width: 120px; text-align: center; padding: 3px;">
                ${new Date().toLocaleDateString('ar-EG').replace(/\//g, '-')}
              </div>
            </div>
            <div style="display: flex; align-items: center; flex: 1; margin: 0 20px;">
              <span style="font-weight: bold; margin-left: 10px;">الاسم</span>
              <div style="border-bottom: 1px solid #000; flex: 1; text-align: center; padding: 3px;">
                ${formData.employeeName || 'عمر مصطفى محمد أحمد'}
              </div>
            </div>
          </div>
        </div>

        <div style="margin-bottom: 20px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
            <div style="display: flex; align-items: center; flex: 1;">
              <span style="font-weight: bold; margin-left: 10px;">إدارة/مشروع</span>
              <div style="border-bottom: 1px solid #000; flex: 1; text-align: center; padding: 3px;">
                ${formData.department || 'مشروع أوجيستا'}
              </div>
            </div>
            <div style="display: flex; align-items: center; flex: 1; margin-left: 20px;">
              <span style="font-weight: bold; margin-left: 10px;">الوظيفة</span>
              <div style="border-bottom: 1px solid #000; flex: 1; text-align: center; padding: 3px;">
                ${formData.jobTitle || 'مهندس برن جودة'}
              </div>
            </div>
          </div>
        </div>

        <!-- Permission Details Section -->
        <div style="border: 2px solid #000; margin: 20px 0; padding: 15px;">
          <div style="font-weight: bold; text-align: center; margin-bottom: 15px; font-size: 14px;">
            نوع الإذن المطلوب
          </div>

          <div style="display: flex; justify-content: space-around; margin-bottom: 20px;">
            <div style="display: flex; align-items: center; gap: 8px;">
              <span style="border: 2px solid #000; width: 15px; height: 15px; display: inline-block; text-align: center; line-height: 11px; ${formData.permissionType === 'late' ? 'background: #000; color: white;' : ''}">
                ${formData.permissionType === 'late' ? '✓' : ''}
              </span>
              <span>إذن تأخير</span>
            </div>
            <div style="display: flex; align-items: center; gap: 8px;">
              <span style="border: 2px solid #000; width: 15px; height: 15px; display: inline-block; text-align: center; line-height: 11px; ${formData.permissionType === 'early' ? 'background: #000; color: white;' : ''}">
                ${formData.permissionType === 'early' ? '✓' : ''}
              </span>
              <span>إذن انصراف مبكر</span>
            </div>
            <div style="display: flex; align-items: center; gap: 8px;">
              <span style="border: 2px solid #000; width: 15px; height: 15px; display: inline-block; text-align: center; line-height: 11px; ${formData.permissionType === 'exit' ? 'background: #000; color: white;' : ''}">
                ${formData.permissionType === 'exit' ? '✓' : ''}
              </span>
              <span>إذن خروج أثناء العمل</span>
            </div>
          </div>

          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
            <div style="display: flex; align-items: center; gap: 10px;">
              <span style="font-weight: bold;">من الساعة:</span>
              <span style="border-bottom: 2px solid #000; padding: 5px 10px; min-width: 80px; text-align: center;">
                ${formData.startTime || ''}
              </span>
            </div>
            <div style="display: flex; align-items: center; gap: 10px;">
              <span style="font-weight: bold;">إلى الساعة:</span>
              <span style="border-bottom: 2px solid #000; padding: 5px 10px; min-width: 80px; text-align: center;">
                ${formData.endTime || ''}
              </span>
            </div>
            <div style="display: flex; align-items: center; gap: 10px;">
              <span style="font-weight: bold;">التاريخ:</span>
              <span style="border-bottom: 2px solid #000; padding: 5px 10px; min-width: 100px; text-align: center;">
                ${new Date().toLocaleDateString('ar-EG')}
              </span>
            </div>
          </div>

          <div style="margin-bottom: 15px;">
            <div style="font-weight: bold; margin-bottom: 10px;">السبب:</div>
            <div style="border: 2px solid #000; min-height: 80px; padding: 15px; line-height: 1.6;">
              ${formData.reason || ''}
            </div>
          </div>
        </div>

        <!-- Signatures Section -->
        <div style="margin-top: 40px; border: 1px solid #000; padding: 15px;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
            <div style="text-align: center; width: 45%;">
              <div style="font-weight: bold; margin-bottom: 30px;">توقيع الموظف</div>
              <div style="border-bottom: 1px solid #000; width: 150px; margin: 0 auto;"></div>
            </div>
            <div style="text-align: center; width: 45%;">
              <div style="font-weight: bold; margin-bottom: 30px;">اعتماد الرئيس المباشر</div>
              <div style="border-bottom: 1px solid #000; width: 150px; margin: 0 auto;"></div>
            </div>
          </div>

          <div style="text-align: center; margin-top: 20px;">
            <div style="font-weight: bold; margin-bottom: 30px;">اعتماد مدير الموارد البشرية</div>
            <div style="border-bottom: 1px solid #000; width: 200px; margin: 0 auto;"></div>
          </div>
        </div>
      </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();

    // انتظار تحميل المحتوى والصور قبل الطباعة
    printWindow.onload = () => {
      setTimeout(() => {
        printWindow.print();
      }, 500);
    };

    // احتياطي في حالة عدم تشغيل onload
    setTimeout(() => {
      printWindow.print();
    }, 1500);
  };

  const getPermissionTypeLabel = (type) => {
    const types = {
      'personal': 'إذن شخصي',
      'medical': 'إذن طبي',
      'emergency': 'إذن طارئ',
      'official': 'إذن رسمي'
    };
    return types[type] || '................................';
  };

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-4xl mx-auto">
          {/* العنوان */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6 pulse-box">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-200 mb-2">
                  {isArabic ? 'طلب إذن' : 'Permission Request'}
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  {isArabic ? 'تقديم طلب إذن جديد' : 'Submit a new permission request'}
                </p>
              </div>
              <div className="flex gap-3">
                <button
                  onClick={downloadForm}
                  className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors flex items-center gap-2"
                >
                  <FiDownload />
                  {isArabic ? 'تحميل النموذج' : 'Download Form'}
                </button>
                <button
                  onClick={printFilledForm}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
                >
                  <FiPrinter />
                  {isArabic ? 'طباعة النموذج' : 'Print Form'}
                </button>
                <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
                  <FiClock className="text-2xl text-white" />
                </div>
              </div>
            </div>
          </div>

          {/* النموذج */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 gentle-animated-box">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* بيانات الموظف */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'اسم الموظف' : 'Employee Name'}
                  </label>
                  <input
                    type="text"
                    name="employeeName"
                    value={formData.employeeName}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    required
                  />
                </div>

                <div className="relative">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'كود الموظف *' : 'Employee Code *'}
                  </label>
                  <input
                    type="text"
                    name="employeeId"
                    value={formData.employeeId}
                    onChange={handleInputChange}
                    placeholder={isArabic ? 'ادخل كود الموظف للبحث...' : 'Enter employee code to search...'}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    autoComplete="off"
                    autoCorrect="off"
                    autoCapitalize="off"
                    spellCheck="false"
                    required
                  />

                  {/* نتائج البحث */}
                  {showEmployeeSearch && employeeSearchResults.length > 0 && (
                    <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                      {employeeSearchResults.map((employee, index) => (
                        <div
                          key={index}
                          onClick={() => selectEmployee(employee)}
                          className="p-3 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-200 dark:border-gray-600 last:border-b-0"
                        >
                          <div className="flex justify-between items-center">
                            <div>
                              <div className="font-medium text-gray-900 dark:text-gray-100">
                                {employee.employeeId} - {employee.fullName}
                              </div>
                              <div className="text-sm text-gray-600 dark:text-gray-400">
                                {employee.jobTitle} - {employee.department}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'القسم' : 'Department'}
                  </label>
                  <input
                    type="text"
                    name="department"
                    value={formData.department}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'المسمى الوظيفي' : 'Job Title'}
                  </label>
                  <input
                    type="text"
                    name="jobTitle"
                    value={formData.jobTitle}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    required
                  />
                </div>
              </div>

              {/* تفاصيل الإذن */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">
                  {isArabic ? 'تفاصيل الإذن' : 'Permission Details'}
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {isArabic ? 'نوع الإذن' : 'Permission Type'}
                    </label>
                    <select
                      name="permissionType"
                      value={formData.permissionType}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      required
                    >
                      <option value="">{isArabic ? 'اختر نوع الإذن' : 'Select Permission Type'}</option>
                      {permissionTypes.map(type => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {isArabic ? 'من الساعة' : 'From Time'}
                    </label>
                    <input
                      type="time"
                      name="startTime"
                      value={formData.startTime}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {isArabic ? 'إلى الساعة' : 'To Time'}
                    </label>
                    <input
                      type="time"
                      name="endTime"
                      value={formData.endTime}
                      onChange={handleInputChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      required
                    />
                  </div>
                </div>

                {formData.totalHours && (
                  <div className="mt-4 p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                    <p className="text-sm text-orange-700 dark:text-orange-300">
                      {isArabic ? `إجمالي ساعات الإذن: ${formData.totalHours} ساعة` : `Total Permission Hours: ${formData.totalHours} hours`}
                    </p>
                  </div>
                )}

                <div className="mt-6">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'سبب الإذن' : 'Reason for Permission'}
                  </label>
                  <textarea
                    name="reason"
                    value={formData.reason}
                    onChange={handleInputChange}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                    placeholder={isArabic ? 'اكتب سبب طلب الإذن...' : 'Write the reason for permission request...'}
                    required
                  />
                </div>
              </div>

              {/* الأزرار */}
              <div className="flex gap-4 pt-6 border-t">
                <button
                  type="button"
                  onClick={() => router.push('/requests')}
                  className="flex items-center gap-2 px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700 transition-colors"
                >
                  <FiArrowLeft />
                  {isArabic ? 'رجوع' : 'Back'}
                </button>

                <button
                  type="submit"
                  disabled={loading}
                  className="flex items-center gap-2 px-6 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <FiSave />
                  {loading ? (isArabic ? 'جاري الحفظ...' : 'Saving...') : (isArabic ? 'حفظ الطلب' : 'Save Request')}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default PermissionRequestPage;
