async function handler({
  reportType,
  filters,
  format,
  month,
  year,
  employeeId,
}) {
  if (!reportType) {
    return { error: 'Report type is required' };
  }

  let reportData = [];
  const startDate = new Date(year, month - 1, 1);
  const endDate = new Date(year, month, 0);

  try {
    switch (reportType) {
      case 'attendance':
        reportData = await sql`
          SELECT 
            ma.employee_id,
            ma.employee_name,
            ma.department,
            ma.attendance_data,
            ma.total_present,
            ma.total_absent,
            ma.total_leave,
            ma.total_late_minutes,
            ma.total_overtime_hours
          FROM Employees emp
          LEFT JOIN Attendance att ON emp.EmployeeID = att.EmployeeID
          WHERE 
            att.AttendanceDate >= ${startDate}
            AND att.AttendanceDate <= ${endDate}
            ${employeeId ? sql`AND emp.EmployeeID = ${employeeId}` : sql``}
        `;
        break;

      case 'leaves':
        reportData = await sql`
          SELECT 
            l.*,
            emp.FullName as employee_name,
            emp.Department as department
          FROM Leaves l
          JOIN Employees emp ON l.EmployeeID = emp.EmployeeID
          WHERE 
            l.start_date >= ${startDate}
            AND l.end_date <= ${endDate}
            ${employeeId ? sql`AND l.employee_id = ${employeeId}` : sql``}
        `;
        break;

      case 'costs':
        reportData = await sql`
          SELECT *
          FROM monthly_costs
          WHERE month >= ${startDate}
            AND month <= ${endDate}
        `;
        break;

      case 'employees':
        reportData = await sql`
          SELECT 
            employee_id,
            employee_name,
            department,
            job_title,
            hire_date,
            phone_number,
            education_level,
            national_id
          FROM Employees
          WHERE is_active = true
          ${employeeId ? sql`AND employee_id = ${employeeId}` : sql``}
        `;
        break;

      default:
        return { error: 'Invalid report type' };
    }

    return {
      success: true,
      data: reportData,
      reportType,
      period: {
        month,
        year,
      },
    };
  } catch (error) {
    return {
      error: 'Failed to generate report',
      details: error.message,
    };
  }
}
