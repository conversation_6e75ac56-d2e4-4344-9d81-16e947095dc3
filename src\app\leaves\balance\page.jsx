'use client';
import React from 'react';

function MainComponent() {
  const [selectedLang, setSelectedLang] = useState('ar');
  const [employeeId, setEmployeeId] = useState('');
  const [yearFilter, setYearFilter] = useState(new Date().getFullYear());
  const [leaveBalances, setLeaveBalances] = useState([
    {
      id: 1,
      type: 'annual',
      total: 30,
      used: 15,
      remaining: 15,
      pending: 2,
    },
    {
      id: 2,
      type: 'sick',
      total: 14,
      used: 5,
      remaining: 9,
      pending: 0,
    },
    {
      id: 3,
      type: 'emergency',
      total: 7,
      used: 2,
      remaining: 5,
      pending: 1,
    },
  ]);

  const dir = selectedLang === 'ar' ? 'rtl' : 'ltr';

  const exportToExcel = async () => {
    try {
      const response = await fetch('/api/excel', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'export',
          data: leaveBalances,
          template: {
            type: selectedLang === 'ar' ? 'نوع الإجازة' : 'Leave Type',
            total: selectedLang === 'ar' ? 'الرصيد الكلي' : 'Total Balance',
            used: selectedLang === 'ar' ? 'المستخدم' : 'Used',
            remaining: selectedLang === 'ar' ? 'المتبقي' : 'Remaining',
            pending: selectedLang === 'ar' ? 'قيد الانتظار' : 'Pending',
          },
        }),
      });

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error);
      }
    } catch (error) {

    }
  };

  const getLeaveTypeName = (type) => {
    const types = {
      annual: selectedLang === 'ar' ? 'سنوية' : 'Annual',
      sick: selectedLang === 'ar' ? 'مرضية' : 'Sick',
      emergency: selectedLang === 'ar' ? 'طارئة' : 'Emergency',
    };
    return types[type] || type;
  };

  return (
    <div
      dir={dir}
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {selectedLang === 'ar' ? 'رصيد الإجازات' : 'Leave Balance'}
          </h1>
          <button
            onClick={() => setSelectedLang(selectedLang === 'ar' ? 'en' : 'ar')}
            className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
          >
            {selectedLang === 'ar' ? 'English' : 'العربية'}
          </button>
        </div>

        <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <input
              type="text"
              value={employeeId}
              onChange={(e) => setEmployeeId(e.target.value)}
              placeholder={
                selectedLang === 'ar' ? 'الرقم الوظيفي' : 'Employee ID'
              }
              className="p-2 border border-gray-300 rounded-md"
              name="employeeId"
            />
            <select
              value={yearFilter}
              onChange={(e) => setYearFilter(e.target.value)}
              className="p-2 border border-gray-300 rounded-md"
              name="year"
            >
              <option value="2025">2025</option>
              <option value="2024">2024</option>
              <option value="2023">2023</option>
            </select>
            <button
              onClick={exportToExcel}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              {selectedLang === 'ar' ? 'تصدير إلى Excel' : 'Export to Excel'}
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {leaveBalances.map((balance) => (
            <div
              key={balance.id}
              className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow"
            >
              <h3 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">
                {getLeaveTypeName(balance.type)}
              </h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-300">
                    {selectedLang === 'ar' ? 'الرصيد الكلي' : 'Total Balance'}
                  </span>
                  <span className="font-bold">{balance.total}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-300">
                    {selectedLang === 'ar' ? 'المستخدم' : 'Used'}
                  </span>
                  <span className="font-bold text-red-600">{balance.used}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-300">
                    {selectedLang === 'ar' ? 'المتبقي' : 'Remaining'}
                  </span>
                  <span className="font-bold text-green-600">
                    {balance.remaining}
                  </span>
                </div>
                {balance.pending > 0 && (
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">
                      {selectedLang === 'ar' ? 'قيد الانتظار' : 'Pending'}
                    </span>
                    <span className="font-bold text-yellow-600">
                      {balance.pending}
                    </span>
                  </div>
                )}
              </div>
              <div className="mt-4 bg-gray-100 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full"
                  style={{ width: `${(balance.used / balance.total) * 100}%` }}
                ></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default MainComponent;
