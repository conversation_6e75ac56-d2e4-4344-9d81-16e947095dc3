async function handler({ method, employeeId, filters, data, documentData }) {
  // List employees with filtering
  if (method === 'GET' && !employeeId) {
    let queryStr = 'SELECT * FROM Employees WHERE 1=1';
    const values = [];
    let paramCount = 1;

    if (filters) {
      if (filters.department) {
        queryStr += ` AND department = $${paramCount}`;
        values.push(filters.department);
        paramCount++;
      }

      if (filters.area) {
        queryStr += ` AND area = $${paramCount}`;
        values.push(filters.area);
        paramCount++;
      }

      if (filters.search) {
        queryStr += ` AND (
          employee_name ILIKE $${paramCount} 
          OR employee_id ILIKE $${paramCount}
          OR code ILIKE $${paramCount}
        )`;
        values.push(`%${filters.search}%`);
        paramCount++;
      }
    }

    return await sql(queryStr, values);
  }

  // Get single employee details
  if (method === 'GET' && employeeId) {
    const [employee, documents] = await sql.transaction([
      sql`
        SELECT * FROM Employees
        WHERE EmployeeCode = @employeeId
      `,
      sql`
        SELECT * FROM employee_documents 
        WHERE employee_id = ${employeeId} AND status = 'active'
      `,
    ]);

    return {
      ...employee[0],
      documents: documents,
    };
  }

  // Update employee information
  if (method === 'PUT' && employeeId && data) {
    let setClause = [];
    const values = [];
    let paramCount = 1;

    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        setClause.push(`${key} = $${paramCount}`);
        values.push(value);
        paramCount++;
      }
    });

    values.push(employeeId);
    const queryStr = `
      UPDATE Employees 
      SET ${setClause.join(', ')}, updated_at = CURRENT_TIMESTAMP 
      WHERE employee_id = $${paramCount} 
      RETURNING *
    `;

    return await sql(queryStr, values);
  }

  // Manage employee documents
  if (method === 'POST' && employeeId && documentData) {
    const { document_url, category_id, document_type_id } = documentData;

    return await sql`
      INSERT INTO employee_documents (
        employee_id, 
        category_id, 
        document_type_id, 
        document_url, 
        status
      ) 
      VALUES (
        ${employeeId}, 
        ${category_id}, 
        ${document_type_id}, 
        ${document_url}, 
        'active'
      ) 
      RETURNING *
    `;
  }

  return null;
}
