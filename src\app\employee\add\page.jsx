"use client";
import React from "react";

import { useUpload } from '@/app/utilities/runtime-helpers';

function MainComponent() {
  const [formData, setFormData] = useState({
    name: "",
    code: "",
    department: "",
    position: "",
  });

  const [documents, setDocuments] = useState(null);
  const [photo, setPhoto] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState(false);
  const [upload, { loading: uploadLoading }] = useUpload();

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handlePhotoUpload = async (e) => {
    const file = e.target.files[0];
    if (file) {
      const { url, error } = await upload({ file });
      if (error) {
        setError("فشل في رفع الصورة");
        return;
      }
      setPhoto(url);
    }
  };

  const handleDocumentsUpload = async (e) => {
    const file = e.target.files[0];
    if (file) {
      const { url, error } = await upload({ file });
      if (error) {
        setError("فشل في رفع المستندات");
        return;
      }
      setDocuments(url);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");
    setSuccess(false);

    try {
      const response = await fetch("/api/employees", {
        method: "POST",
        body: JSON.stringify({
          ...formData,
          photo,
          documents,
        }),
      });

      if (!response.ok) {
        throw new Error("فشل في إضافة الموظف");
      }

      setSuccess(true);
      setFormData({
        name: "",
        code: "",
        department: "",
        position: "",
      });
      setPhoto(null);
      setDocuments(null);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      dir="rtl"
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="max-w-3xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <a
            href="/employees"
            className="flex items-center text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
          >
            <i className="fas fa-arrow-right ml-2"></i>
            عودة
          </a>
        </div>

        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          إضافة موظف جديد
        </h1>

        <form
          onSubmit={handleSubmit}
          className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                اسم الموظف
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                الكود
              </label>
              <input
                type="text"
                name="code"
                value={formData.code}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                القسم
              </label>
              <input
                type="text"
                name="department"
                value={formData.department}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                الوظيفة
              </label>
              <input
                type="text"
                name="position"
                value={formData.position}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              />
            </div>
          </div>

          <div className="mt-6 space-y-4">
            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                الصورة الشخصية
              </label>
              <input
                type="file"
                accept="image/*"
                onChange={handlePhotoUpload}
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                المستندات
              </label>
              <input
                type="file"
                accept=".pdf,.doc,.docx"
                onChange={handleDocumentsUpload}
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>
          </div>

          {error && (
            <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-md">
              {error}
            </div>
          )}

          {success && (
            <div className="mt-4 p-3 bg-green-100 text-green-700 rounded-md">
              تم إضافة الموظف بنجاح
            </div>
          )}

          <div className="mt-6">
            <button
              type="submit"
              disabled={loading || uploadLoading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors disabled:bg-blue-300"
            >
              {loading || uploadLoading ? "جاري الحفظ..." : "حفظ البيانات"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default MainComponent;