import { NextResponse } from 'next/server';
import sql from 'mssql';

// إعدادات قاعدة البيانات
const config = {
  server: process.env.DB_SERVER || 'localhost',
  database: process.env.DB_NAME || 'EmployeeDB',
  user: process.env.DB_USER || 'sa',
  password: process.env.DB_PASSWORD || 'YourPassword123',
  options: {
    encrypt: false,
    trustServerCertificate: true,
    enableArithAbort: true,
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
};

export async function GET(request) {
  let pool;
  
  try {

    // الاتصال بقاعدة البيانات
    pool = await sql.connect(config);

    // إحصائيات عامة للطلبات
    const totalStats = await pool.request().query(`
      SELECT 
        COUNT(*) as totalRequests,
        COUNT(CASE WHEN Status = 'pending' THEN 1 END) as pendingRequests,
        COUNT(CASE WHEN Status = 'approved' THEN 1 END) as approvedRequests,
        COUNT(CASE WHEN Status = 'rejected' THEN 1 END) as rejectedRequests
      FROM LeaveRequests
    `);

    // إحصائيات حسب نوع الطلب
    const typeStats = await pool.request().query(`
      SELECT 
        LeaveType,
        COUNT(*) as count,
        COUNT(CASE WHEN Status = 'approved' THEN 1 END) as approved,
        COUNT(CASE WHEN Status = 'pending' THEN 1 END) as pending,
        COUNT(CASE WHEN Status = 'rejected' THEN 1 END) as rejected
      FROM LeaveRequests
      GROUP BY LeaveType
      ORDER BY count DESC
    `);

    // إحصائيات شهرية
    const monthlyStats = await pool.request().query(`
      SELECT 
        YEAR(SubmittedAt) as year,
        MONTH(SubmittedAt) as month,
        COUNT(*) as count,
        COUNT(CASE WHEN Status = 'approved' THEN 1 END) as approved
      FROM LeaveRequests
      WHERE SubmittedAt >= DATEADD(month, -6, GETDATE())
      GROUP BY YEAR(SubmittedAt), MONTH(SubmittedAt)
      ORDER BY year DESC, month DESC
    `);

    // إحصائيات الموظفين الأكثر طلباً
    const topEmployees = await pool.request().query(`
      SELECT TOP 10
        EmployeeName,
        Department,
        COUNT(*) as totalRequests,
        COUNT(CASE WHEN Status = 'approved' THEN 1 END) as approvedRequests
      FROM LeaveRequests
      GROUP BY EmployeeName, Department
      ORDER BY totalRequests DESC
    `);

    // إحصائيات الأقسام
    const departmentStats = await pool.request().query(`
      SELECT 
        Department,
        COUNT(*) as totalRequests,
        COUNT(CASE WHEN Status = 'approved' THEN 1 END) as approvedRequests,
        COUNT(CASE WHEN Status = 'pending' THEN 1 END) as pendingRequests
      FROM LeaveRequests
      WHERE Department IS NOT NULL
      GROUP BY Department
      ORDER BY totalRequests DESC
    `);

    // متوسط أيام الإجازة
    const averageDays = await pool.request().query(`
      SELECT 
        LeaveType,
        AVG(CAST(TotalDays as FLOAT)) as averageDays,
        MIN(TotalDays) as minDays,
        MAX(TotalDays) as maxDays
      FROM LeaveRequests
      WHERE TotalDays IS NOT NULL AND TotalDays > 0
      GROUP BY LeaveType
    `);

    // إحصائيات الوقت (متوسط وقت الموافقة)
    const timeStats = await pool.request().query(`
      SELECT 
        AVG(DATEDIFF(day, SubmittedAt, ReviewedAt)) as avgApprovalDays,
        MIN(DATEDIFF(day, SubmittedAt, ReviewedAt)) as minApprovalDays,
        MAX(DATEDIFF(day, SubmittedAt, ReviewedAt)) as maxApprovalDays
      FROM LeaveRequests
      WHERE ReviewedAt IS NOT NULL AND Status = 'approved'
    `);

    // إحصائيات هذا الشهر مقارنة بالشهر الماضي
    const currentMonthStats = await pool.request().query(`
      SELECT 
        COUNT(CASE WHEN MONTH(SubmittedAt) = MONTH(GETDATE()) AND YEAR(SubmittedAt) = YEAR(GETDATE()) THEN 1 END) as thisMonth,
        COUNT(CASE WHEN MONTH(SubmittedAt) = MONTH(DATEADD(month, -1, GETDATE())) AND YEAR(SubmittedAt) = YEAR(DATEADD(month, -1, GETDATE())) THEN 1 END) as lastMonth
      FROM LeaveRequests
      WHERE SubmittedAt >= DATEADD(month, -2, GETDATE())
    `);

    const stats = {
      total: totalStats.recordset[0],
      byType: typeStats.recordset,
      monthly: monthlyStats.recordset,
      topEmployees: topEmployees.recordset,
      byDepartment: departmentStats.recordset,
      averageDays: averageDays.recordset,
      timeStats: timeStats.recordset[0],
      currentMonth: currentMonthStats.recordset[0]
    };

    return NextResponse.json({
      success: true,
      stats: stats
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'خطأ في جلب إحصائيات الطلبات',
      error: error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      await pool.close();

    }
  }
}
