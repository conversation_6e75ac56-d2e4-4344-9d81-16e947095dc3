// Simple Design: 2025-06-18T11:18:41.079Z
// Unified Design: 2025-06-18T11:09:38.704Z
// Contrast Fixed: 2025-06-18T11:02:07.133Z
'use client';

import { useTheme } from '@/contexts/ThemeContext';
import { useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';
import {
    FiArchive,
    FiArrowRight,
    FiDownload,
    FiEdit,
    FiFilter,
    FiHome,
    FiLogOut,
    FiPrinter,
    FiRefreshCw,
    FiSearch,
    FiTrash2,
    FiUser,
    FiUsers,
    FiX
} from 'react-icons/fi';
import * as XLSX from 'xlsx';

export default function EmployeeSearch() {
  const router = useRouter();
  const { isDarkMode, themeClasses } = useTheme();

  // حالات البحث الفردي
  const [searchType, setSearchType] = useState('individual'); // individual أو group
  const [searchQuery, setSearchQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [employeePhoto, setEmployeePhoto] = useState(null);
  const [employeeDocuments, setEmployeeDocuments] = useState([]);
  const [showDocuments, setShowDocuments] = useState(false);

  // حالات البحث المجمع
  const [groupSearchFilters, setGroupSearchFilters] = useState({
    department: '',
    jobTitle: '',
    governorate: ''
  });
  const [groupResults, setGroupResults] = useState([]);
  const [showGroupResults, setShowGroupResults] = useState(false);

  // قوائم الأقسام والمحافظات
  const [departments, setDepartments] = useState([]);
  const [governorates, setGovernorates] = useState([]);
  const [jobTitles, setJobTitles] = useState([]);
  const [loadingLists, setLoadingLists] = useState(false);

  // حالات عامة
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // حالات التعديل المباشر
  const [editingEmployee, setEditingEmployee] = useState(null);
  const [editFormData, setEditFormData] = useState({});
  const [saving, setSaving] = useState(false);

  // حالات تغيير الحالة
  const [showTransferModal, setShowTransferModal] = useState(false);
  const [showResignModal, setShowResignModal] = useState(false);
  const [transferData, setTransferData] = useState({
    employeeId: '',
    employeeName: '',
    currentDepartment: '',
    newDepartment: '',
    newProject: '',
    transferDate: new Date().toISOString().split('T')[0],
    reason: '',
    notes: ''
  });
  const [resignData, setResignData] = useState({
    employeeId: '',
    employeeName: '',
    department: '',
    resignationDate: new Date().toISOString().split('T')[0],
    reason: '',
    notes: ''
  });

  // حالة الشريط الجانبي
  const [sidebarExpanded, setSidebarExpanded] = useState(false);

  const searchInputRef = useRef(null);
  const suggestionsRef = useRef(null);

  // البحث التفاعلي للبحث الفردي
  useEffect(() => {
    if (searchType !== 'individual') return;

    const searchTimeout = setTimeout(async () => {
      if (searchQuery.trim().length > 0) {
        try {
          const response = await fetch('/api/employee-live-search', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ searchTerm: searchQuery, limit: 8 })
          });

          const data = await response.json();
          if (data.success) {
            // تحويل البيانات إلى التنسيق المطلوب للاقتراحات
            const formattedSuggestions = (data.data || []).map(emp => ({
              id: emp.employeeCode || emp.EmployeeCode || emp.EmployeeID,
              employeeId: emp.employeeCode || emp.EmployeeCode || emp.EmployeeID,
              name: emp.employeeName || emp.EmployeeName || emp.fullName || emp.FullName,
              jobTitle: emp.jobTitle || emp.JobTitle,
              department: emp.department || emp.Department,
              displayText: `${emp.employeeName || emp.EmployeeName || emp.fullName || emp.FullName} (${emp.employeeCode || emp.EmployeeCode || emp.EmployeeID})`,
              searchType: /^\d+$/.test(searchQuery.trim()) ? 'code' : 'name'
            }));
            setSuggestions(formattedSuggestions);
            setShowSuggestions(true);
          }
        } catch (error) {

        }
      } else {
        setSuggestions([]);
        setShowSuggestions(false);
      }
    }, 300);

    return () => clearTimeout(searchTimeout);
  }, [searchQuery, searchType]);

  // إخفاء الاقتراحات عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target) &&
          searchInputRef.current && !searchInputRef.current.contains(event.target)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // اختيار موظف من الاقتراحات (البحث الفردي)
  const selectEmployee = async (suggestion) => {
    setLoading(true);
    setError('');
    setShowSuggestions(false);
    setSearchQuery(suggestion.displayText);

    try {
      const response = await fetch('/api/employee-search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          searchType: 'individual',
          searchValue: suggestion.id
        })
      });

      const data = await response.json();

      if (data.success && data.employee) {

        setSelectedEmployee(data.employee);
        await fetchEmployeePhoto(data.employee.EmployeeID || data.employee.EmployeeCode);
        await fetchEmployeeAssets(data.employee.EmployeeCode || data.employee.EmployeeID);
      } else {

        setError(data.message || 'لم يتم العثور على بيانات الموظف');
      }
    } catch (error) {
      setError('خطأ في جلب بيانات الموظف: ' + error.message);

    } finally {
      setLoading(false);
    }
  };

  // جلب الصورة الشخصية
  const fetchEmployeePhoto = async (employeeCode) => {

    try {
      const response = await fetch('/api/employee-archive', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getPhoto',
          employeeId: employeeCode
        })
      });

      const data = await response.json();

      if (data.success && data.photoPath) {

        setEmployeePhoto(data.photoPath);
      } else {

        setEmployeePhoto(null); // لا تعرض صورة افتراضية
      }
    } catch (error) {

      setEmployeePhoto(null);
    }
  };

  // جلب بيانات الأصول (السيارات والشقق)
  const fetchEmployeeAssets = async (employeeCode) => {

    try {
      const response = await fetch(`/api/employee-assets?employeeCode=${employeeCode}`);
      const data = await response.json();

      if (data.success) {
        // تحديث بيانات الموظف المحدد مع معلومات الأصول
        setSelectedEmployee(prev => ({
          ...prev,
          // معلومات السيارات
          TransportInfo: data.data.cars && data.data.cars.length > 0 ? {
            CarCode: data.data.cars[0].carCode,
            Route: data.data.cars[0].route,
            ContractorName: data.data.cars[0].contractorName,
            CarNumber: data.data.cars[0].carNumber,
            CarType: data.data.cars[0].carType
          } : null,
          // معلومات الشقق
          ApartmentInfo: data.data.apartments && data.data.apartments.length > 0 ? {
            ApartmentCode: data.data.apartments[0].apartmentCode,
            LandlordName: data.data.apartments[0].landlordName,
            Address: data.data.apartments[0].address,
            RentAmount: data.data.apartments[0].rentAmount
          } : null
        }));

      } else {

      }
    } catch (error) {

    }
  };

  // جلب مستندات الموظف
  const fetchEmployeeDocuments = async (employeeCode) => {

    try {
      setLoading(true);
      const response = await fetch('/api/employee-archive', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getDocuments',
          employeeId: employeeCode
        })
      });

      const data = await response.json();

      if (data.success) {

        setEmployeeDocuments(data.documents);
        setShowDocuments(true);
      } else {

        setError('خطأ في جلب المستندات: ' + (data.error || 'خطأ غير معروف'));
      }
    } catch (error) {

      setError('خطأ في جلب المستندات: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // البحث المجمع
  const performGroupSearch = async () => {
    setLoading(true);
    setError('');
    setShowGroupResults(false);

    try {
      const response = await fetch('/api/employee-search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          searchType: 'group',
          department: groupSearchFilters.department,
          jobTitle: groupSearchFilters.jobTitle,
          governorate: groupSearchFilters.governorate
        })
      });

      const data = await response.json();

      if (data.success) {

        setGroupResults(data.employees || []);
        setShowGroupResults(true);
      } else {

        setError(data.message || 'لم يتم العثور على نتائج');
      }
    } catch (error) {
      setError('خطأ في البحث: ' + error.message);

    } finally {
      setLoading(false);
    }
  };

  // تصدير إلى Excel
  const exportToExcel = () => {
    if (groupResults.length === 0) {
      alert('لا توجد بيانات للتصدير');
      return;
    }

    const exportData = groupResults.map(emp => ({
      'الكود': emp.EmployeeCode,
      'الاسم': emp.EmployeeName,
      'المسمى الوظيفي': emp.JobTitle,
      'القسم': emp.Department,
      'المحافظة': emp.Governorate,
      'الجوال': emp.Mobile,
      'البريد الإلكتروني': emp.Email,
      'تاريخ التعيين': emp.HireDate ? new Date(emp.HireDate).toLocaleDateString('ar-EG') : '',
      'النوع': emp.Gender,
      'الحالة الاجتماعية': emp.MaritalStatus
    }));

    const ws = XLSX.utils.json_to_sheet(exportData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'الموظفين');

    const fileName = `employees_${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(wb, fileName);
  };

  // بدء التعديل المباشر
  const startEditing = (employee) => {
    setEditingEmployee(employee.EmployeeID);

    // دالة مساعدة لتجنب القيم الفارغة - فقط إذا كانت القيمة موجودة فعلاً
    const safeValue = (value) => {
      if (value === null || value === undefined) return '';
      return String(value);
    };

    setEditFormData({
      FullName: safeValue(employee.FullName),
      JobTitle: safeValue(employee.JobTitle),
      Department: safeValue(employee.Department),
      DirectManager: safeValue(employee.DirectManager),
      NationalID: safeValue(employee.NationalID),
      BirthDate: employee.BirthDate ? employee.BirthDate.split('T')[0] : '',
      Gender: safeValue(employee.Gender),
      Governorate: safeValue(employee.Governorate),
      Area: safeValue(employee.Area),
      MaritalStatus: safeValue(employee.MaritalStatus),
      Mobile: safeValue(employee.Mobile),
      Email: safeValue(employee.Email),
      EmergencyNumber: safeValue(employee.EmergencyNumber || employee.emrnum),
      Nationality: safeValue(employee.Nationality),
      Kinship: safeValue(employee.Kinship),
      Education: safeValue(employee.Education),
      University: safeValue(employee.University),
      Major: safeValue(employee.Major),
      Grade: safeValue(employee.Grade),
      Batch: safeValue(employee.Batch),
      IsResidentEmployee: employee.IsResidentEmployee || false,
      CompanyHousing: safeValue(employee.CompanyHousing),
      HousingCode: safeValue(employee.HousingCode),
      TransportMethod: safeValue(employee.TransportMethod),
      SocialInsurance: safeValue(employee.SocialInsurance),
      SocialInsuranceNumber: safeValue(employee.SocialInsureNum),
      SocialInsuranceDate: employee.spcialInsDate ? employee.spcialInsDate.split('T')[0] : '',
      MedicalInsurance: safeValue(employee.MedicalInsurance),
      MedicalInsuranceNumber: safeValue(employee.MedicalInsuranceNum),
      HireDate: employee.HireDate ? employee.HireDate.split('T')[0] : '',
      JoinDate: employee.JoinDate ? employee.JoinDate.split('T')[0] : '',
      Notes: safeValue(employee.Notes)
    });
  };

  // إلغاء التعديل
  const cancelEditing = () => {
    setEditingEmployee(null);
    setEditFormData({});
  };

  // حفظ التعديلات
  const saveEditing = async () => {
    if (!editingEmployee && !selectedEmployee) return;

    const employeeId = editingEmployee || selectedEmployee.EmployeeID;

    setSaving(true);
    try {
      const response = await fetch('/api/employee-update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          employeeId: employeeId,
          employeeData: editFormData
        }),
      });

      const data = await response.json();

      if (data.success) {
        // تحديث البيانات في الجدول
        setGroupResults(prevResults =>
          prevResults.map(emp =>
            emp.EmployeeID === employeeId
              ? { ...emp, ...editFormData }
              : emp
          )
        );

        // تحديث البيانات في البحث الفردي إذا كان نفس الموظف
        if (selectedEmployee && selectedEmployee.EmployeeID === employeeId) {
          setSelectedEmployee(prev => ({ ...prev, ...editFormData }));
        }

        setEditingEmployee(null);
        setEditFormData({});

        // إظهار رسالة نجاح
        alert('✅ تم حفظ التعديلات بنجاح!');
      } else {
        alert('❌ خطأ: ' + data.message);
      }
    } catch (error) {

      alert('❌ حدث خطأ في حفظ التعديلات');
    } finally {
      setSaving(false);
    }
  };

  // تحديث بيانات موظف في الجدول
  const updateEmployeeInTable = async (employeeId, field, value) => {
    try {
      // إرسال التحديث للخادم
      const response = await fetch('/api/employee-update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          employeeId: employeeId,
          employeeData: { [field]: value }
        }),
      });

      const data = await response.json();

      if (data.success) {
        // تحديث البيانات في الجدول محلياً
        setGroupResults(prevResults =>
          prevResults.map(emp =>
            emp.EmployeeID === employeeId
              ? { ...emp, [field]: value }
              : emp
          )
        );

        // إظهار رسالة نجاح مؤقتة

      } else {
        alert('❌ خطأ في حفظ التعديل: ' + data.message);
      }
    } catch (error) {

      alert('❌ حدث خطأ في حفظ التعديل');
    }
  };

  // مكون التعديل السريع
  const QuickEditCell = ({ value, employeeId, field, onUpdate, type = 'text' }) => {
    const [isEditing, setIsEditing] = useState(false);
    const [editValue, setEditValue] = useState(value || '');
    const [isSaving, setIsSaving] = useState(false);

    const handleSave = async () => {
      if (editValue === value) {
        setIsEditing(false);
        return;
      }

      setIsSaving(true);
      await onUpdate(employeeId, field, editValue);
      setIsSaving(false);
      setIsEditing(false);
    };

    const handleCancel = () => {
      setEditValue(value || '');
      setIsEditing(false);
    };

    const handleKeyPress = (e) => {
      if (e.key === 'Enter') {
        handleSave();
      } else if (e.key === 'Escape') {
        handleCancel();
      }
    };

    if (isEditing) {
      return (
        <div className="flex items-center gap-2">
          <input
            type={type}
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            onKeyDown={handleKeyPress}
            onBlur={handleSave}
            autoFocus
            disabled={isSaving}
            className="w-full px-2 py-1 border border-blue-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {isSaving && (
            <FiRefreshCw className="text-blue-500 animate-spin text-sm" />
          )}
        </div>
      );
    }

    return (
      <div
        onClick={() => setIsEditing(true)}
        className="cursor-pointer hover:bg-blue-50 px-2 py-1 rounded transition-colors group"
        title="اضغط للتعديل"
      >
        <span className="group-hover:text-blue-600">
          {value || '-'}
        </span>
        <FiEdit className="inline-block mr-1 opacity-0 group-hover:opacity-100 text-blue-500 text-xs transition-opacity" />
      </div>
    );
  };

  // مسح البحث
  const clearSearch = () => {
    setSearchQuery('');
    setSelectedEmployee(null);
    setEmployeePhoto(null);
    setSuggestions([]);
    setShowSuggestions(false);
    setShowDocuments(false);
    setEmployeeDocuments([]);
    setGroupResults([]);
    setShowGroupResults(false);
    setGroupSearchFilters({ department: '', jobTitle: '', governorate: '' });
    setError('');
  };

  // معالجة نقل الموظف
  const handleTransferEmployee = (employee) => {
    setTransferData({
      employeeId: employee.EmployeeID,
      employeeName: employee.FullName,
      currentDepartment: employee.Department,
      newDepartment: '',
      newProject: '',
      transferDate: new Date().toISOString().split('T')[0],
      reason: '',
      notes: ''
    });
    setShowTransferModal(true);
  };

  // معالجة استقالة الموظف
  const handleResignEmployee = (employee) => {
    setResignData({
      employeeId: employee.EmployeeID,
      employeeName: employee.FullName,
      department: employee.Department,
      resignationDate: new Date().toISOString().split('T')[0],
      reason: '',
      notes: ''
    });
    setShowResignModal(true);
  };

  // تأكيد النقل
  const confirmTransfer = async () => {
    try {
      setLoading(true);

      // إعداد البيانات للـ API
      const transferPayload = {
        employeeId: transferData.employeeId,
        employeeName: transferData.employeeName,
        previousDepartment: transferData.currentDepartment,
        newDepartment: transferData.newDepartment,
        previousJobTitle: selectedEmployee?.JobTitle || '',
        newJobTitle: selectedEmployee?.JobTitle || '',
        projectOrDepartment: transferData.newProject || transferData.newDepartment,
        transferDate: transferData.transferDate,
        transferReason: transferData.reason,
        createdBy: 'النظام'
      };

      // إضافة سجل النقل (سيقوم API بتحديث حالة الموظف تلقائياً)
      const transferResponse = await fetch('/api/transfers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(transferPayload)
      });

      const transferResult = await transferResponse.json();

      if (transferResult.success) {
        // تحديث البيانات المحلية
        if (selectedEmployee && selectedEmployee.EmployeeID === transferData.employeeId) {
          setSelectedEmployee(prev => ({
            ...prev,
            CurrentStatus: 'منقول',
            Department: transferData.newDepartment
          }));
        }

        alert('✅ تم نقل الموظف بنجاح!\n\n📊 سيتم تحديث إحصائيات النقل في الداش بورد\n🔄 حالة الموظف: منقول');
        setShowTransferModal(false);
      } else {
        throw new Error(transferResult.error || 'فشل في إضافة سجل النقل');
      }
    } catch (error) {

      alert('❌ حدث خطأ في نقل الموظف: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // تأكيد الاستقالة
  const confirmResignation = async () => {
    try {
      setLoading(true);

      // إعداد البيانات للـ API
      const resignPayload = {
        employeeId: resignData.employeeId,
        employeeName: resignData.employeeName,
        department: resignData.department,
        jobTitle: selectedEmployee?.JobTitle || '',
        resignationDate: resignData.resignationDate,
        lastWorkingDay: resignData.resignationDate, // نفس تاريخ الاستقالة افتراضياً
        resignationReason: resignData.reason,
        finalSettlementAmount: 0, // سيتم تحديده لاحقاً
        createdBy: 'النظام'
      };

      // إضافة سجل الاستقالة (سيقوم API بتحديث حالة الموظف تلقائياً)
      const resignResponse = await fetch('/api/resignations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(resignPayload)
      });

      const resignResult = await resignResponse.json();

      if (resignResult.success) {
        // تحديث البيانات المحلية
        if (selectedEmployee && selectedEmployee.EmployeeID === resignData.employeeId) {
          setSelectedEmployee(prev => ({ ...prev, CurrentStatus: 'مستقيل' }));
        }

        alert('✅ تم تسجيل استقالة الموظف بنجاح!\n\n📊 سيتم تحديث إحصائيات الاستقالة في الداش بورد\n📋 حالة الموظف: مستقيل');
        setShowResignModal(false);
      } else {
        throw new Error(resignResult.error || 'فشل في إضافة سجل الاستقالة');
      }
    } catch (error) {

      alert('❌ حدث خطأ في تسجيل الاستقالة: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // فحص تسجيل الدخول وجلب القوائم
  useEffect(() => {
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    if (!isLoggedIn) {
      router.push('/login');
      return;
    }

    // جلب قوائم الأقسام والمحافظات
    fetchDepartmentsAndGovernorates();
  }, [router]);

  // جلب قوائم الأقسام والمحافظات
  const fetchDepartmentsAndGovernorates = async () => {
    try {
      setLoadingLists(true);

      const response = await fetch('/api/departments');
      const result = await response.json();

      if (result.success) {

        setDepartments(result.data.departments || []);
        setGovernorates(result.data.governorates || []);
        setJobTitles(result.data.jobTitles || []);
      } else {

      }
    } catch (error) {

    } finally {
      setLoadingLists(false);
    }
  };

  return (
    <div className={`min-h-screen ${themeClasses.bg.primary} flex`}>
      {/* الشريط الجانبي القابل للطي */}
      <div
        className={`fixed right-0 top-0 h-full ${themeClasses.bg.secondary} shadow-lg z-40 transition-all duration-300 ease-in-out ${
          sidebarExpanded ? 'w-80' : 'w-16'
        }`}
        onMouseEnter={() => setSidebarExpanded(true)}
        onMouseLeave={() => setSidebarExpanded(false)}
      >
        <div className="p-4">
          {/* أيقونة القائمة */}
          <div className="flex items-center justify-center mb-6">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <FiUsers className="text-white text-lg" />
            </div>
            {sidebarExpanded && (
              <span className="mr-3 font-bold text-gray-800">نظام الموظفين</span>
            )}
          </div>

          {/* عناصر القائمة */}
          <nav className="space-y-2">
            <div className={`flex items-center p-3 rounded-lg bg-blue-50 text-blue-600`}>
              <FiSearch className="text-lg" />
              {sidebarExpanded && <span className="mr-3">البحث عن موظف</span>}
            </div>
            <button
              onClick={() => router.push('/employees/add')}
              className={`w-full flex items-center p-3 rounded-lg ${themeClasses.bg.hover} ${themeClasses.text.secondary} transition-colors`}
            >
              <FiUser className="text-lg" />
              {sidebarExpanded && <span className="mr-3">إضافة موظف</span>}
            </button>
            <button
              onClick={() => router.push('/employees/dashboard')}
              className={`w-full flex items-center p-3 rounded-lg ${themeClasses.bg.hover} ${themeClasses.text.secondary} transition-colors`}
            >
              <FiUsers className="text-lg" />
              {sidebarExpanded && <span className="mr-3">لوحة التحكم</span>}
            </button>
            <button
              onClick={() => router.push('/employees/transfers')}
              className={`w-full flex items-center p-3 rounded-lg ${themeClasses.bg.hover} ${themeClasses.text.secondary} transition-colors`}
            >
              <FiArrowRight className="text-lg" />
              {sidebarExpanded && <span className="mr-3">حالات النقل</span>}
            </button>
            <button
              onClick={() => router.push('/employees/resignations')}
              className={`w-full flex items-center p-3 rounded-lg ${themeClasses.bg.hover} ${themeClasses.text.secondary} transition-colors`}
            >
              <FiLogOut className="text-lg" />
              {sidebarExpanded && <span className="mr-3">حالات الاستقالة</span>}
            </button>
            <button
              onClick={() => router.push('/employees/archive')}
              className={`w-full flex items-center p-3 rounded-lg ${themeClasses.bg.hover} ${themeClasses.text.secondary} transition-colors`}
            >
              <FiArchive className="text-lg" />
              {sidebarExpanded && <span className="mr-3">أرشيف المستندات</span>}
            </button>
            <button
              onClick={() => router.push('/dashboard')}
              className={`w-full flex items-center p-3 rounded-lg ${themeClasses.bg.hover} ${themeClasses.text.secondary} transition-colors`}
            >
              <FiHome className="text-lg" />
              {sidebarExpanded && <span className="mr-3">الصفحة الرئيسية</span>}
            </button>
          </nav>
        </div>
      </div>

      {/* المحتوى الرئيسي */}
      <div className={`flex-1 transition-all duration-300 ease-in-out ${
        sidebarExpanded ? 'mr-80' : 'mr-16'
      } p-4`}>
        <div className="w-full max-w-none mx-auto">
        {/* رأس الصفحة */}
        <div className={`${themeClasses.bg.secondary} rounded-lg shadow-sm p-6 mb-6`}>
          <div className="flex items-center justify-between mb-6">
            <h1 className={`text-3xl font-bold ${themeClasses.text.primary} flex items-center gap-3`}>
              <FiSearch className="text-blue-600" />
              البحث عن الموظفين
            </h1>

            {/* أزرار نوع البحث */}
            <div className="flex gap-2">
              <button
                onClick={() => {
                  setSearchType('individual');
                  clearSearch();
                }}
                className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-colors ${
                  searchType === 'individual'
                    ? 'bg-blue-600 text-white'
                    : `${themeClasses.button.secondary}`
                }`}
              >
                <FiUser />
                بحث فردي
              </button>
              <button
                onClick={() => {
                  setSearchType('group');
                  clearSearch();
                }}
                className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-colors ${
                  searchType === 'group'
                    ? 'bg-green-600 text-white'
                    : `${themeClasses.button.secondary}`
                }`}
              >
                <FiUsers />
                بحث مجمع
              </button>
            </div>
          </div>

          {/* البحث الفردي */}
          {searchType === 'individual' && (
            <div className="relative">
              <div className="relative">
                <input
                  ref={searchInputRef}
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="ابحث بالكود أو الاسم..."
                  className="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-lg"
                  autoComplete="off"
                  autoCorrect="off"
                  autoCapitalize="off"
                  spellCheck="false"
                />
                <FiSearch className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-xl" />
                {searchQuery && (
                  <button
                    onClick={clearSearch}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    <FiX className="text-xl" />
                  </button>
                )}
              </div>

              {/* الاقتراحات */}
              {showSuggestions && suggestions && suggestions.length > 0 && (
                <div
                  ref={suggestionsRef}
                  className="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-50 mt-1 max-h-80 overflow-y-auto"
                >
                  {(suggestions || []).map((suggestion, index) => (
                    <button
                      key={index}
                      onClick={() => selectEmployee(suggestion)}
                      className="w-full px-4 py-3 text-right hover:bg-blue-50 border-b border-gray-100 last:border-b-0 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-semibold text-gray-800">{suggestion.name}</div>
                          <div className="text-sm text-gray-600">{suggestion.jobTitle} - {suggestion.department}</div>
                        </div>
                        <div className="text-blue-600 font-bold">#{suggestion.id}</div>
                      </div>
                    </button>
                  ))}
                </div>
              )}

              {/* رسالة نوع البحث */}
              {searchQuery && (
                <div className="mt-2 text-sm text-gray-600">
                  {/^\d+$/.test(searchQuery.trim()) ?
                    '🔢 البحث بالكود' :
                    '📝 البحث بالاسم'
                  }
                </div>
              )}
            </div>
          )}

          {/* البحث المجمع */}
          {searchType === 'group' && (
            <div className="space-y-4">
              {loadingLists && (
                <div className="text-center py-4">
                  <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
                  <p className="mt-2 text-sm text-gray-600">جاري تحميل القوائم...</p>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className={`block text-sm font-medium ${themeClasses.text.primary} mb-2`}>
                    القسم {departments.length > 0 && `(${departments.length} قسم)`}
                  </label>
                  <select
                    value={groupSearchFilters.department}
                    onChange={(e) => setGroupSearchFilters({...groupSearchFilters, department: e.target.value})}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent ${themeClasses.bg.secondary} ${themeClasses.text.primary}`}
                    disabled={loadingLists}
                  >
                    <option value="">جميع الأقسام</option>
                    {departments.map((dept, index) => (
                      <option key={index} value={dept.name}>
                        {dept.name} ({dept.count} موظف)
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className={`block text-sm font-medium ${themeClasses.text.primary} mb-2`}>
                    المسمى الوظيفي {jobTitles.length > 0 && `(${jobTitles.length} مسمى)`}
                  </label>
                  <select
                    value={groupSearchFilters.jobTitle}
                    onChange={(e) => setGroupSearchFilters({...groupSearchFilters, jobTitle: e.target.value})}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent ${themeClasses.bg.secondary} ${themeClasses.text.primary}`}
                    disabled={loadingLists}
                  >
                    <option value="">جميع المسميات الوظيفية</option>
                    {jobTitles.map((job, index) => (
                      <option key={index} value={job.name}>
                        {job.name} ({job.count} موظف)
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className={`block text-sm font-medium ${themeClasses.text.primary} mb-2`}>
                    المحافظة {governorates.length > 0 && `(${governorates.length} محافظة)`}
                  </label>
                  <select
                    value={groupSearchFilters.governorate}
                    onChange={(e) => setGroupSearchFilters({...groupSearchFilters, governorate: e.target.value})}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent ${themeClasses.bg.secondary} ${themeClasses.text.primary}`}
                    disabled={loadingLists}
                  >
                    <option value="">جميع المحافظات</option>
                    {governorates.map((gov, index) => (
                      <option key={index} value={gov.name}>
                        {gov.name} ({gov.count} موظف)
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={performGroupSearch}
                  disabled={loading}
                  className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2 disabled:opacity-50"
                >
                  <FiFilter />
                  {loading ? 'جاري البحث...' : 'بحث'}
                </button>

                <button
                  onClick={clearSearch}
                  className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-2"
                >
                  <FiX />
                  مسح
                </button>
              </div>
            </div>
          )}
        </div>

        {/* رسائل الخطأ */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg mb-6">
            {error}
          </div>
        )}

        {/* مؤشر التحميل */}
        {loading && (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p className="mt-2 text-gray-600">جاري التحميل...</p>
          </div>
        )}

        {/* نتائج البحث الفردي */}
        {selectedEmployee && !loading && searchType === 'individual' && (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* الصورة الشخصية والأزرار */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="text-center mb-6">
                  {employeePhoto ? (
                    <img
                      src={employeePhoto.startsWith('/') ? employeePhoto : `/${employeePhoto}`}
                      alt="صورة الموظف"
                      className="w-32 h-32 rounded-full mx-auto object-cover border-4 border-blue-100"
                      onError={(e) => {
                        e.target.style.display = 'none';
                        e.target.nextSibling.style.display = 'flex';
                      }}
                    />
                  ) : null}
                  <div
                    className="w-32 h-32 rounded-full mx-auto bg-gray-200 flex items-center justify-center border-4 border-blue-100"
                    style={{ display: employeePhoto ? 'none' : 'flex' }}
                  >
                    <FiUser className="text-4xl text-gray-400" />
                  </div>
                  <h3 className="mt-4 text-xl font-bold text-gray-800">{selectedEmployee.EmployeeName}</h3>
                  <p className="text-gray-600">#{selectedEmployee.EmployeeCode}</p>
                </div>

                {/* الأزرار */}
                <div className="space-y-3">
                  {editingEmployee === selectedEmployee.EmployeeID ? (
                    <>
                      <button
                        onClick={saveEditing}
                        disabled={saving}
                        className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center gap-2 disabled:opacity-50"
                      >
                        <FiEdit />
                        {saving ? 'جاري الحفظ...' : 'حفظ التعديلات'}
                      </button>
                      <button
                        onClick={cancelEditing}
                        disabled={saving}
                        className="w-full bg-gray-600 text-white py-2 px-4 rounded-lg hover:bg-gray-700 transition-colors flex items-center justify-center gap-2 disabled:opacity-50"
                      >
                        <FiX />
                        إلغاء التعديل
                      </button>
                    </>
                  ) : (
                    <>
                      <button
                        onClick={() => startEditing(selectedEmployee)}
                        className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
                      >
                        <FiEdit />
                        تعديل مباشر
                      </button>
                      <button
                        onClick={() => {
                          // التوجه إلى صفحة تعديل الموظف
                          if (confirm(`هل تريد فتح صفحة التعديل الكاملة للموظف ${selectedEmployee.FullName}؟`)) {
                            // التوجه إلى صفحة التعديل
                            window.location.href = `/employees/edit/${selectedEmployee.EmployeeID}`;
                          }
                        }}
                        className="w-full bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 transition-colors flex items-center justify-center gap-2"
                      >
                        <FiEdit />
                        تعديل شامل
                      </button>
                    </>
                  )}

                  <button
                    onClick={() => fetchEmployeeDocuments(selectedEmployee.EmployeeID)}
                    className="w-full bg-green-600 text-white py-2 px-4 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center gap-2"
                  >
                    <FiArchive />
                    أرشيف المستندات
                  </button>

                  {/* أزرار تغيير الحالة - تظهر فقط للموظفين العاملين فعلياً */}
                  {(selectedEmployee?.CurrentStatus === 'سارى' || selectedEmployee?.CurrentStatus === 'نشط') && (
                    <>
                      <button
                        onClick={() => handleTransferEmployee(selectedEmployee)}
                        className="w-full bg-indigo-600 text-white py-2 px-4 rounded-lg hover:bg-indigo-700 transition-colors flex items-center justify-center gap-2"
                        title="نقل الموظف - سيتم تحديث الحالة إلى 'منقول' وإضافة سجل في جدول النقل"
                      >
                        <FiArrowRight />
                        نقل الموظف
                      </button>

                      <button
                        onClick={() => handleResignEmployee(selectedEmployee)}
                        className="w-full bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors flex items-center justify-center gap-2"
                        title="استقالة الموظف - سيتم تحديث الحالة إلى 'مستقيل' وإضافة سجل في جدول الاستقالة"
                      >
                        <FiLogOut />
                        استقالة الموظف
                      </button>
                    </>
                  )}

                  <button
                    onClick={() => {
                      // إنشاء صفحة طباعة لبيانات الموظف
                      const printContent = `
                        <!DOCTYPE html>
                        <html dir="rtl" lang="ar">
                        <head>
                          <meta charset="UTF-8">
                          <title>بيانات الموظف - ${selectedEmployee.FullName}</title>
                          <style>
                            @page {
                              size: A4;
                              margin: 15mm;
                            }
                            body {
                              font-family: 'Arial', sans-serif;
                              margin: 0;
                              direction: rtl;
                              text-align: right;
                              font-size: 12px;
                              line-height: 1.4;
                            }
                            .header {
                              display: flex;
                              align-items: center;
                              justify-content: space-between;
                              border-bottom: 3px solid #333;
                              padding-bottom: 15px;
                              margin-bottom: 20px;
                            }
                            .header-info {
                              flex: 1;
                              text-align: center;
                            }
                            .employee-photo {
                              width: 120px;
                              height: 150px;
                              border: 2px solid #333;
                              border-radius: 8px;
                              object-fit: cover;
                              margin-left: 20px;
                            }
                            .photo-placeholder {
                              width: 120px;
                              height: 150px;
                              border: 2px solid #333;
                              border-radius: 8px;
                              display: flex;
                              align-items: center;
                              justify-content: center;
                              background-color: #f5f5f5;
                              color: #666;
                              font-size: 14px;
                              margin-left: 20px;
                            }
                            .main-content {
                              display: grid;
                              grid-template-columns: 1fr 1fr;
                              gap: 15px;
                            }
                            .section {
                              margin-bottom: 15px;
                              border: 1px solid #ddd;
                              padding: 10px;
                              border-radius: 5px;
                              break-inside: avoid;
                            }
                            .section-title {
                              font-size: 14px;
                              font-weight: bold;
                              color: #333;
                              border-bottom: 1px solid #ccc;
                              padding-bottom: 5px;
                              margin-bottom: 8px;
                              background-color: #f8f9fa;
                              padding: 5px;
                              text-align: center;
                            }
                            .data-row {
                              margin-bottom: 4px;
                              display: flex;
                              justify-content: space-between;
                              border-bottom: 1px dotted #eee;
                              padding: 2px 0;
                            }
                            .label {
                              font-weight: bold;
                              color: #555;
                              min-width: 40%;
                            }
                            .value {
                              color: #333;
                              text-align: left;
                            }
                            .full-width {
                              grid-column: 1 / -1;
                            }
                            @media print {
                              body { margin: 0; }
                              .no-print { display: none; }
                              .section { page-break-inside: avoid; }
                            }
                          </style>
                        </head>
                        <body>
                          <div class="header">
                            <div class="header-info">
                              <h1 style="margin: 0; font-size: 24px; color: #333;">بيانات الموظف</h1>
                              <h2 style="margin: 5px 0; font-size: 20px; color: #555;">${selectedEmployee.EmployeeName}</h2>
                              <p style="margin: 2px 0; font-size: 14px;">كود الموظف: <strong>${selectedEmployee.EmployeeCode}</strong></p>
                              <p style="margin: 2px 0; font-size: 12px; color: #666;">تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}</p>
                            </div>
                            ${employeePhoto ?
                              `<img src="${window.location.origin}/${employeePhoto}" alt="صورة الموظف" class="employee-photo" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" />
                               <div class="photo-placeholder" style="display: none;">لا توجد صورة</div>` :
                              `<div class="photo-placeholder">لا توجد صورة</div>`
                            }
                          </div>

                          <div class="main-content">

                            <!-- العمود الأول -->
                            <div class="section">
                              <div class="section-title">البيانات الأساسية</div>
                              <div class="data-row"><span class="label">الكود:</span> <span class="value">${selectedEmployee.EmployeeID}</span></div>
                              <div class="data-row"><span class="label">الاسم:</span> <span class="value">${selectedEmployee.FullName}</span></div>
                              <div class="data-row"><span class="label">المسمى الوظيفي:</span> <span class="value">${selectedEmployee.JobTitle}</span></div>
                              <div class="data-row"><span class="label">القسم:</span> <span class="value">${selectedEmployee.Department}</span></div>
                              <div class="data-row"><span class="label">المدير المباشر:</span> <span class="value">${selectedEmployee.DirectManager || 'غير محدد'}</span></div>
                              <div class="data-row"><span class="label">الحالة الوظيفية:</span> <span class="value">${selectedEmployee.CurrentStatus || 'غير محدد'}</span></div>
                            </div>

                            <div class="section">
                              <div class="section-title">البيانات الشخصية</div>
                              <div class="data-row"><span class="label">الرقم القومي:</span> <span class="value">${selectedEmployee.NationalID}</span></div>
                              <div class="data-row"><span class="label">تاريخ الميلاد:</span> <span class="value">${selectedEmployee.BirthDate ? new Date(selectedEmployee.BirthDate).toLocaleDateString('ar-EG') : 'غير محدد'}</span></div>
                              <div class="data-row"><span class="label">النوع:</span> <span class="value">${selectedEmployee.Gender === 'M' ? 'ذكر' : selectedEmployee.Gender === 'F' ? 'أنثى' : selectedEmployee.Gender}</span></div>
                              <div class="data-row"><span class="label">المحافظة:</span> <span class="value">${selectedEmployee.Governorate}</span></div>
                              <div class="data-row"><span class="label">المنطقة:</span> <span class="value">${selectedEmployee.Area || 'غير محدد'}</span></div>
                              <div class="data-row"><span class="label">الحالة الاجتماعية:</span> <span class="value">${selectedEmployee.MaritalStatus}</span></div>
                              <div class="data-row"><span class="label">الخدمة العسكرية:</span> <span class="value">${selectedEmployee.MilitaryService || 'غير محدد'}</span></div>
                            </div>

                            <div class="section">
                              <div class="section-title">البيانات الدراسية</div>
                              <div class="data-row"><span class="label">المؤهل التعليمي:</span> <span class="value">${selectedEmployee.Education || 'غير محدد'}</span></div>
                              <div class="data-row"><span class="label">الجامعة:</span> <span class="value">${selectedEmployee.University || 'غير محدد'}</span></div>
                              <div class="data-row"><span class="label">التخصص:</span> <span class="value">${selectedEmployee.Major || 'غير محدد'}</span></div>
                              <div class="data-row"><span class="label">التقدير:</span> <span class="value">${selectedEmployee.Grade || 'غير محدد'}</span></div>
                              <div class="data-row"><span class="label">سنة التخرج:</span> <span class="value">${selectedEmployee.Batch || 'غير محدد'}</span></div>
                            </div>

                            <div class="section">
                              <div class="section-title">بيانات الاتصال</div>
                              <div class="data-row"><span class="label">الجوال:</span> <span class="value">${selectedEmployee.Mobile}</span></div>
                              <div class="data-row"><span class="label">البريد الإلكتروني:</span> <span class="value">${selectedEmployee.Email || 'غير محدد'}</span></div>
                              <div class="data-row"><span class="label">رقم الطوارئ:</span> <span class="value">${selectedEmployee.EmergencyNumber || 'غير محدد'}</span></div>
                              <div class="data-row"><span class="label">صلة القرابة:</span> <span class="value">${selectedEmployee.Kinship || 'غير محدد'}</span></div>
                            </div>

                            <!-- العمود الثاني -->
                            <div class="section">
                              <div class="section-title">السكن والمواصلات</div>
                              <div class="data-row"><span class="label">موظف مقيم:</span> <span class="value">${selectedEmployee.IsResidentEmployee ? 'نعم' : 'لا'}</span></div>
                              <div class="data-row"><span class="label">سكن الشركة:</span> <span class="value">${selectedEmployee.CompanyHousing || 'غير محدد'}</span></div>
                              <div class="data-row"><span class="label">كود السكن:</span> <span class="value">${selectedEmployee.HousingCode || 'غير محدد'}</span></div>
                              <div class="data-row"><span class="label">وسيلة النقل:</span> <span class="value">${selectedEmployee.TransportMethod || 'غير محدد'}</span></div>
                            </div>

                            <div class="section">
                              <div class="section-title">التأمينات</div>
                              <div class="data-row"><span class="label">التأمين الاجتماعي:</span> <span class="value">${selectedEmployee.SocialInsurance === 'مؤمن' ? 'مؤمن' : selectedEmployee.SocialInsurance === 'غير مؤمن' ? 'غير مؤمن' : 'غير محدد'}</span></div>
                              <div class="data-row"><span class="label">الرقم التأميني:</span> <span class="value">${selectedEmployee.SocialInsuranceNumber || 'غير محدد'}</span></div>
                              <div class="data-row"><span class="label">تاريخ التأمين:</span> <span class="value">${selectedEmployee.SocialInsuranceDate ? new Date(selectedEmployee.SocialInsuranceDate).toLocaleDateString('ar-EG') : 'غير محدد'}</span></div>
                              <div class="data-row"><span class="label">مؤمن عليه طبي:</span> <span class="value">${selectedEmployee.MedicalInsurance === 'مؤمن' ? 'مؤمن' : selectedEmployee.MedicalInsurance === 'غير مؤمن' ? 'غير مؤمن' : 'غير محدد'}</span></div>
                              <div class="data-row"><span class="label">رقم الوثيقة الطبية:</span> <span class="value">${selectedEmployee.MedicalInsuranceNumber || 'غير محدد'}</span></div>
                            </div>

                            <div class="section">
                              <div class="section-title">التواريخ المهمة</div>
                              <div class="data-row"><span class="label">تاريخ التعيين:</span> <span class="value">${selectedEmployee.HireDate ? new Date(selectedEmployee.HireDate).toLocaleDateString('ar-EG') : 'غير محدد'}</span></div>
                              <div class="data-row"><span class="label">تاريخ الالتحاق:</span> <span class="value">${selectedEmployee.JoinDate ? new Date(selectedEmployee.JoinDate).toLocaleDateString('ar-EG') : 'غير محدد'}</span></div>
                            </div>

                            <!-- قسم الملاحظات بعرض كامل -->
                            <div class="section full-width">
                              <div class="section-title">معلومات إضافية</div>
                              <div class="data-row"><span class="label">ملاحظات:</span> <span class="value">${selectedEmployee.Notes || 'لا توجد ملاحظات'}</span></div>
                            </div>
                          </div>
                        </body>
                        </html>
                      `;

                      // فتح نافذة جديدة للطباعة
                      const printWindow = window.open('', '_blank');
                      if (printWindow) {
                        printWindow.document.write(printContent);
                        printWindow.document.close();

                        // انتظار تحميل المحتوى ثم الطباعة
                        setTimeout(() => {
                          printWindow.print();
                          // إغلاق النافذة بعد الطباعة
                          setTimeout(() => {
                            printWindow.close();
                          }, 1000);
                        }, 500);
                      } else {
                        alert('تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة.');
                      }
                    }}
                    className="w-full bg-purple-600 text-white py-2 px-4 rounded-lg hover:bg-purple-700 transition-colors flex items-center justify-center gap-2"
                  >
                    <FiPrinter />
                    طباعة البيانات
                  </button>

                  <button
                    onClick={() => {
                      // تأكيد حذف الموظف
                      if (confirm(`⚠️ تحذير خطير!\n\nهل أنت متأكد من حذف الموظف ${selectedEmployee.FullName}؟\n\nكود الموظف: ${selectedEmployee.EmployeeID}\n\nهذا الإجراء سيحذف:\n- جميع بيانات الموظف\n- جميع المستندات المرتبطة\n- جميع السجلات التاريخية\n\nهذا الإجراء لا يمكن التراجع عنه!`)) {
                        if (confirm(`تأكيد نهائي!\n\nاكتب "نعم" للتأكيد النهائي لحذف الموظف ${selectedEmployee.FullName}`)) {
                          alert(`تم تأكيد حذف الموظف: ${selectedEmployee.FullName}\n\nملاحظة: وظيفة الحذف الفعلية ستحتاج لتطوير API خاص مع إجراءات أمان إضافية.`);
                        }
                      }
                    }}
                    className="w-full bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors flex items-center justify-center gap-2"
                  >
                    <FiTrash2 />
                    حذف الموظف
                  </button>
                </div>
              </div>
            </div>

            {/* بيانات الموظف */}
            <div className="lg:col-span-3">
              <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold text-gray-800">بيانات الموظف</h2>
                  {editingEmployee === selectedEmployee.EmployeeID && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg px-4 py-2">
                      <span className="text-blue-800 font-medium">🔧 وضع التعديل نشط</span>
                    </div>
                  )}
                </div>

                {/* الصف الأول: البيانات الأساسية والشخصية والدراسية */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                  {/* البيانات الأساسية */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-blue-600 border-b border-blue-200 pb-2">البيانات الأساسية</h3>
                    <div><span className="font-medium">الكود:</span> {selectedEmployee.EmployeeID}</div>

                    <div className="flex items-center gap-2">
                      <span className="font-medium">الاسم:</span>
                      {editingEmployee === selectedEmployee.EmployeeCode ? (
                        <input
                          type="text"
                          value={editFormData.EmployeeName || ''}
                          onChange={(e) => setEditFormData(prev => ({ ...prev, EmployeeName: e.target.value }))}
                          className="flex-1 px-2 py-1 border border-blue-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      ) : (
                        <span>{selectedEmployee.FullName}</span>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      <span className="font-medium">المسمى الوظيفي:</span>
                      {editingEmployee === selectedEmployee.EmployeeID ? (
                        <input
                          type="text"
                          value={editFormData.JobTitle || ''}
                          onChange={(e) => setEditFormData(prev => ({ ...prev, JobTitle: e.target.value }))}
                          className="flex-1 px-2 py-1 border border-blue-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      ) : (
                        <span>{selectedEmployee.JobTitle}</span>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      <span className="font-medium">القسم:</span>
                      {editingEmployee === selectedEmployee.EmployeeID ? (
                        <input
                          type="text"
                          value={editFormData.Department || ''}
                          onChange={(e) => setEditFormData(prev => ({ ...prev, Department: e.target.value }))}
                          className="flex-1 px-2 py-1 border border-blue-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      ) : (
                        <span>{selectedEmployee.Department}</span>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      <span className="font-medium">المدير المباشر:</span>
                      {editingEmployee === selectedEmployee.EmployeeID ? (
                        <input
                          type="text"
                          value={editFormData.DirectManager || ''}
                          onChange={(e) => setEditFormData(prev => ({ ...prev, DirectManager: e.target.value }))}
                          className="flex-1 px-2 py-1 border border-blue-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                        />
                      ) : (
                        <span>{selectedEmployee.DirectManager || 'غير محدد'}</span>
                      )}
                    </div>

                    <div><span className="font-medium">الحالة الوظيفية:</span> <span className="font-bold text-blue-600">{selectedEmployee.CurrentStatus || 'غير محدد'}</span></div>
                  </div>

                  {/* البيانات الشخصية */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-green-600 border-b border-green-200 pb-2">البيانات الشخصية</h3>

                    <div className="flex items-center gap-2">
                      <span className="font-medium">الرقم القومي:</span>
                      {editingEmployee === selectedEmployee.EmployeeID ? (
                        <input
                          type="text"
                          value={editFormData.NationalID || ''}
                          onChange={(e) => setEditFormData(prev => ({ ...prev, NationalID: e.target.value }))}
                          className="flex-1 px-2 py-1 border border-green-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500"
                          maxLength="14"
                        />
                      ) : (
                        <span>{selectedEmployee.NationalID || 'غير محدد'}</span>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      <span className="font-medium">تاريخ الميلاد:</span>
                      {editingEmployee === selectedEmployee.EmployeeID ? (
                        <input
                          type="date"
                          value={editFormData.BirthDate ? editFormData.BirthDate.split('T')[0] : ''}
                          onChange={(e) => setEditFormData(prev => ({ ...prev, BirthDate: e.target.value }))}
                          className="flex-1 px-2 py-1 border border-green-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500"
                        />
                      ) : (
                        <span>{selectedEmployee.BirthDate ? new Date(selectedEmployee.BirthDate).toLocaleDateString('ar-EG') : 'غير محدد'}</span>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      <span className="font-medium">النوع:</span>
                      {editingEmployee === selectedEmployee.EmployeeID ? (
                        <select
                          value={editFormData.Gender || ''}
                          onChange={(e) => setEditFormData(prev => ({ ...prev, Gender: e.target.value }))}
                          className="flex-1 px-2 py-1 border border-green-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500"
                        >
                          <option value="">اختر النوع</option>
                          <option value="M">ذكر</option>
                          <option value="F">أنثى</option>
                        </select>
                      ) : (
                        <span>{selectedEmployee.Gender === 'M' ? 'ذكر' : selectedEmployee.Gender === 'F' ? 'أنثى' : 'غير محدد'}</span>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      <span className="font-medium">الجنسية:</span>
                      {editingEmployee === selectedEmployee.EmployeeID ? (
                        <input
                          type="text"
                          value={editFormData.Nationality || ''}
                          onChange={(e) => setEditFormData(prev => ({ ...prev, Nationality: e.target.value }))}
                          className="flex-1 px-2 py-1 border border-green-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500"
                        />
                      ) : (
                        <span>{selectedEmployee.Nationality || 'غير محدد'}</span>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      <span className="font-medium">الحالة الاجتماعية:</span>
                      {editingEmployee === selectedEmployee.EmployeeID ? (
                        <select
                          value={editFormData.MaritalStatus || ''}
                          onChange={(e) => setEditFormData(prev => ({ ...prev, MaritalStatus: e.target.value }))}
                          className="flex-1 px-2 py-1 border border-green-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500"
                        >
                          <option value="">اختر الحالة</option>
                          <option value="أعزب">أعزب</option>
                          <option value="متزوج">متزوج</option>
                          <option value="مطلق">مطلق</option>
                          <option value="أرمل">أرمل</option>
                        </select>
                      ) : (
                        <span>{selectedEmployee.MaritalStatus || 'غير محدد'}</span>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      <span className="font-medium">المحافظة:</span>
                      {editingEmployee === selectedEmployee.EmployeeID ? (
                        <input
                          type="text"
                          value={editFormData.Governorate || ''}
                          onChange={(e) => setEditFormData(prev => ({ ...prev, Governorate: e.target.value }))}
                          className="flex-1 px-2 py-1 border border-green-300 rounded focus:outline-none focus:ring-2 focus:ring-green-500"
                        />
                      ) : (
                        <span>{selectedEmployee.Governorate || 'غير محدد'}</span>
                      )}
                    </div>
                  </div>

                  {/* البيانات الدراسية */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-orange-600 border-b border-orange-200 pb-2">البيانات الدراسية</h3>

                    <div className="flex items-center gap-2">
                      <span className="font-medium">المؤهل التعليمي:</span>
                      {editingEmployee === selectedEmployee.EmployeeID ? (
                        <input
                          type="text"
                          value={editFormData.Education || ''}
                          onChange={(e) => setEditFormData(prev => ({ ...prev, Education: e.target.value }))}
                          className="flex-1 px-2 py-1 border border-orange-300 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
                          placeholder="المؤهل التعليمي"
                        />
                      ) : (
                        <span>{selectedEmployee.Education || 'غير محدد'}</span>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      <span className="font-medium">الجامعة:</span>
                      {editingEmployee === selectedEmployee.EmployeeID ? (
                        <input
                          type="text"
                          value={editFormData.University || ''}
                          onChange={(e) => setEditFormData(prev => ({ ...prev, University: e.target.value }))}
                          className="flex-1 px-2 py-1 border border-orange-300 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
                          placeholder="الجامعة"
                        />
                      ) : (
                        <span>{selectedEmployee.University || 'غير محدد'}</span>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      <span className="font-medium">التخصص:</span>
                      {editingEmployee === selectedEmployee.EmployeeID ? (
                        <input
                          type="text"
                          value={editFormData.Major || ''}
                          onChange={(e) => setEditFormData(prev => ({ ...prev, Major: e.target.value }))}
                          className="flex-1 px-2 py-1 border border-orange-300 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
                          placeholder="التخصص"
                        />
                      ) : (
                        <span>{selectedEmployee.Major || 'غير محدد'}</span>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      <span className="font-medium">التقدير:</span>
                      {editingEmployee === selectedEmployee.EmployeeID ? (
                        <select
                          value={editFormData.Grade || ''}
                          onChange={(e) => setEditFormData(prev => ({ ...prev, Grade: e.target.value }))}
                          className="flex-1 px-2 py-1 border border-orange-300 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
                        >
                          <option value="">اختر التقدير</option>
                          <option value="ممتاز">ممتاز</option>
                          <option value="جيد جداً">جيد جداً</option>
                          <option value="جيد">جيد</option>
                          <option value="مقبول">مقبول</option>
                        </select>
                      ) : (
                        <span>{selectedEmployee.Grade || 'غير محدد'}</span>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      <span className="font-medium">سنة التخرج:</span>
                      {editingEmployee === selectedEmployee.EmployeeID ? (
                        <input
                          type="text"
                          value={editFormData.Batch || ''}
                          onChange={(e) => setEditFormData(prev => ({ ...prev, Batch: e.target.value }))}
                          className="flex-1 px-2 py-1 border border-orange-300 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
                          placeholder="سنة التخرج"
                          maxLength="4"
                        />
                      ) : (
                        <span>{selectedEmployee.Batch || 'غير محدد'}</span>
                      )}
                    </div>
                  </div>
                </div>

                {/* الصف الثاني: بيانات السكن والمواصلات والاتصال */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                  {/* بيانات السكن */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-teal-600 border-b border-teal-200 pb-2">بيانات السكن</h3>
                    <div><span className="font-medium">موظف مقيم:</span> {selectedEmployee.IsResidentEmployee ? 'نعم' : 'لا'}</div>
                    <div>
                      <span className="font-medium">وسيلة المواصلات:</span>
                      <span className={`ml-2 px-2 py-1 rounded text-sm font-medium ${
                        selectedEmployee.ApartmentInfo ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'
                      }`}>
                        {selectedEmployee.ApartmentInfo ? 'سكن الشركة' : 'سكن خاص'}
                      </span>
                    </div>
                    <div><span className="font-medium">كود السكن:</span> <span className="font-bold text-blue-600">{selectedEmployee.ApartmentInfo?.ApartmentCode || selectedEmployee.HousingCode || 'غير محدد'}</span></div>
                    {selectedEmployee.ApartmentInfo && (
                      <>
                        <div><span className="font-medium">اسم المؤجر:</span> <span className="text-green-600 font-medium">{selectedEmployee.ApartmentInfo.LandlordName}</span></div>
                        <div><span className="font-medium">العنوان:</span> {selectedEmployee.ApartmentInfo.Address}</div>
                        <div><span className="font-medium">الإيجار الشهري:</span> <span className="text-blue-600 font-bold">{selectedEmployee.ApartmentInfo.RentAmount} جنيه</span></div>
                      </>
                    )}
                  </div>

                  {/* بيانات المواصلات */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-green-600 border-b border-green-200 pb-2">بيانات المواصلات</h3>
                    <div>
                      <span className="font-medium">وسيلة المواصلات:</span>
                      <span className={`ml-2 px-2 py-1 rounded text-sm font-medium ${
                        selectedEmployee.TransportInfo ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {selectedEmployee.TransportInfo ? 'مسجل بسيارة' : 'غير مسجل بسيارة'}
                      </span>
                    </div>
                    <div><span className="font-medium">كود السيارة:</span> <span className="font-bold text-blue-600">{selectedEmployee.TransportInfo?.CarCode || 'غير محدد'}</span></div>
                    {selectedEmployee.TransportInfo && (
                      <>
                        <div><span className="font-medium">خط السير:</span> <span className="text-green-600 font-medium">{selectedEmployee.TransportInfo.Route}</span></div>
                        <div><span className="font-medium">اسم المقاول:</span> <span className="text-orange-600 font-medium">{selectedEmployee.TransportInfo.ContractorName}</span></div>
                        <div><span className="font-medium">رقم السيارة:</span> <span className="text-blue-600 font-bold">{selectedEmployee.TransportInfo.CarNumber}</span></div>
                        <div><span className="font-medium">نوع السيارة:</span> <span className="text-purple-600">{selectedEmployee.TransportInfo.CarType}</span></div>
                      </>
                    )}
                  </div>

                  {/* بيانات الاتصال */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-purple-600 border-b border-purple-200 pb-2">بيانات الاتصال</h3>
                    <div><span className="font-medium">الجوال:</span> {selectedEmployee.Mobile || 'غير محدد'}</div>
                    <div><span className="font-medium">البريد الإلكتروني:</span> <span className="text-blue-600">{selectedEmployee.Email || 'غير محدد'}</span></div>
                    <div><span className="font-medium">رقم الطوارئ:</span> {selectedEmployee.EmergencyNumber || 'غير محدد'}</div>
                    <div><span className="font-medium">صلة القرابة:</span> {selectedEmployee.Kinship || 'غير محدد'}</div>
                  </div>
                </div>

                {/* الصف الثالث: التأمينات والتواريخ المهمة ومعلومات إضافية */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* التأمينات */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-teal-600 border-b border-teal-200 pb-2">التأمينات</h3>
                    <div>
                      <span className="font-medium">التأمين الاجتماعي:</span>
                      <span className={`ml-2 px-2 py-1 rounded text-sm font-medium ${
                        selectedEmployee.SocialInsurance === 'مؤمن' || selectedEmployee.SocialInsurance === 'نعم' || selectedEmployee.SocialInsurance === '1' || selectedEmployee.SocialInsurance === 1
                          ? 'bg-green-100 text-green-800'
                          : selectedEmployee.SocialInsurance === 'غير مؤمن' || selectedEmployee.SocialInsurance === 'لا' || selectedEmployee.SocialInsurance === '0' || selectedEmployee.SocialInsurance === 0
                          ? 'bg-red-100 text-red-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {selectedEmployee.SocialInsurance === 'مؤمن' || selectedEmployee.SocialInsurance === 'نعم' || selectedEmployee.SocialInsurance === '1' || selectedEmployee.SocialInsurance === 1
                          ? 'مؤمن'
                          : selectedEmployee.SocialInsurance === 'غير مؤمن' || selectedEmployee.SocialInsurance === 'لا' || selectedEmployee.SocialInsurance === '0' || selectedEmployee.SocialInsurance === 0
                          ? 'غير مؤمن'
                          : 'غير محدد'}
                      </span>
                    </div>
                    <div><span className="font-medium">الرقم التأميني:</span> {selectedEmployee.SocialInsuranceNumber || selectedEmployee.SocialInsureNum || 'غير محدد'}</div>
                    <div><span className="font-medium">تاريخ التأمين:</span> {selectedEmployee.SocialInsuranceDate || selectedEmployee.spcialInsDate ? new Date(selectedEmployee.SocialInsuranceDate || selectedEmployee.spcialInsDate).toLocaleDateString('ar-EG') : 'غير محدد'}</div>
                    <div>
                      <span className="font-medium">التأمين الطبي:</span>
                      <span className={`ml-2 px-2 py-1 rounded text-sm font-medium ${
                        selectedEmployee.MedicalInsurance === 'مؤمن' || selectedEmployee.MedicalInsurance === 'نعم' || selectedEmployee.MedicalInsurance === '1' || selectedEmployee.MedicalInsurance === 1
                          ? 'bg-green-100 text-green-800'
                          : selectedEmployee.MedicalInsurance === 'غير مؤمن' || selectedEmployee.MedicalInsurance === 'لا' || selectedEmployee.MedicalInsurance === '0' || selectedEmployee.MedicalInsurance === 0
                          ? 'bg-red-100 text-red-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {selectedEmployee.MedicalInsurance === 'مؤمن' || selectedEmployee.MedicalInsurance === 'نعم' || selectedEmployee.MedicalInsurance === '1' || selectedEmployee.MedicalInsurance === 1
                          ? 'مؤمن'
                          : selectedEmployee.MedicalInsurance === 'غير مؤمن' || selectedEmployee.MedicalInsurance === 'لا' || selectedEmployee.MedicalInsurance === '0' || selectedEmployee.MedicalInsurance === 0
                          ? 'غير مؤمن'
                          : 'غير محدد'}
                      </span>
                    </div>
                    <div><span className="font-medium">رقم الوثيقة الطبية:</span> {selectedEmployee.MedicalInsuranceNumber || selectedEmployee.MedicalInsuranceNum || 'غير محدد'}</div>
                  </div>

                  {/* التواريخ المهمة */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-indigo-600 border-b border-indigo-200 pb-2">التواريخ المهمة</h3>
                    <div><span className="font-medium">تاريخ التعيين:</span> {selectedEmployee.HireDate ? new Date(selectedEmployee.HireDate).toLocaleDateString('ar-EG') : 'غير محدد'}</div>
                    <div><span className="font-medium">تاريخ الالتحاق:</span> {selectedEmployee.JoinDate ? new Date(selectedEmployee.JoinDate).toLocaleDateString('ar-EG') : 'غير محدد'}</div>
                  </div>

                  {/* معلومات إضافية */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-600 border-b border-gray-200 pb-2">معلومات إضافية</h3>
                    <div><span className="font-medium">المنطقة:</span> {selectedEmployee.Area || 'غير محدد'}</div>
                    <div>
                      <span className="font-medium">الحالة الحالية:</span>
                      <span className={`ml-2 px-2 py-1 rounded text-sm font-medium ${
                        selectedEmployee.CurrentStatus === 'نشط' || selectedEmployee.CurrentStatus === 'سارى'
                          ? 'bg-green-100 text-green-800'
                          : selectedEmployee.CurrentStatus === 'منقول'
                          ? 'bg-blue-100 text-blue-800'
                          : selectedEmployee.CurrentStatus === 'مستقيل'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {selectedEmployee.CurrentStatus || 'غير محدد'}
                      </span>
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">ملاحظات:</span>
                      <div className="mt-1 text-gray-600">{selectedEmployee.Notes || 'لا توجد ملاحظات'}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* نتائج البحث المجمع */}
        {showGroupResults && groupResults.length > 0 && searchType === 'group' && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-2xl font-bold text-gray-800">
                نتائج البحث ({groupResults.length} موظف)
              </h2>
              <button
                onClick={exportToExcel}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
              >
                <FiDownload />
                تصدير Excel
              </button>
            </div>

            {/* رسالة التعديل السريع */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
              <div className="flex items-center gap-2 text-blue-800">
                <FiEdit className="text-lg" />
                <span className="font-medium">تعديل سريع:</span>
                <span className="text-sm">مرر الماوس على أي خلية واضغط على أيقونة التعديل لتعديلها مباشرة</span>
              </div>
              <div className="text-xs text-blue-600 mt-1">
                💡 للتعديل الشامل مع جميع البيانات، استخدم زر "تعديل" في عمود الإجراءات
              </div>
            </div>

            <div className="w-full">
              <table className="w-full table-fixed border-collapse">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="w-[8%] px-2 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الكود</th>
                    <th className="w-[18%] px-2 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الاسم</th>
                    <th className="w-[15%] px-2 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المسمى الوظيفي</th>
                    <th className="w-[12%] px-2 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">القسم</th>
                    <th className="w-[10%] px-2 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المحافظة</th>
                    <th className="w-[8%] px-2 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">كود الشقة</th>
                    <th className="w-[8%] px-2 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">كود السيارة</th>
                    <th className="w-[11%] px-2 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الجوال</th>
                    <th className="w-[5%] px-2 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">النوع</th>
                    <th className="w-[5%] px-2 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {groupResults.map((employee, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="w-[8%] px-2 py-3 text-sm font-medium text-gray-900">
                        {employee.EmployeeID}
                      </td>
                      <td className="w-[18%] px-2 py-3 text-sm text-gray-900">
                        <QuickEditCell
                          value={employee.FullName}
                          employeeId={employee.EmployeeID}
                          field="FullName"
                          onUpdate={updateEmployeeInTable}
                        />
                      </td>
                      <td className="w-[15%] px-2 py-3 text-sm text-gray-900">
                        <QuickEditCell
                          value={employee.JobTitle}
                          employeeId={employee.EmployeeID}
                          field="JobTitle"
                          onUpdate={updateEmployeeInTable}
                        />
                      </td>
                      <td className="w-[12%] px-2 py-3 text-sm text-gray-900">
                        <QuickEditCell
                          value={employee.Department}
                          employeeId={employee.EmployeeID}
                          field="Department"
                          onUpdate={updateEmployeeInTable}
                        />
                      </td>
                      <td className="w-[10%] px-2 py-3 text-sm text-gray-900">
                        <QuickEditCell
                          value={employee.Governorate}
                          employeeId={employee.EmployeeID}
                          field="Governorate"
                          onUpdate={updateEmployeeInTable}
                        />
                      </td>
                      <td className="w-[8%] px-2 py-3 text-sm text-gray-900">
                        {employee.HousingCode ? (
                          <span className="bg-blue-100 text-blue-800 px-1 py-1 rounded text-xs font-bold">
                            {employee.HousingCode}
                          </span>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </td>
                      <td className="w-[8%] px-2 py-3 text-sm text-gray-900">
                        {employee.TransportMethod ? (
                          <span className="bg-green-100 text-green-800 px-1 py-1 rounded text-xs font-bold">
                            {employee.TransportMethod.includes('باص الشركة') ?
                              employee.TransportMethod.replace('باص الشركة - ', '') :
                              employee.TransportMethod
                            }
                          </span>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </td>
                      <td className="w-[11%] px-2 py-3 text-sm text-gray-900">
                        <QuickEditCell
                          value={employee.Mobile}
                          employeeId={employee.EmployeeID}
                          field="Mobile"
                          onUpdate={updateEmployeeInTable}
                          type="tel"
                        />
                      </td>
                      <td className="w-[5%] px-2 py-3 text-sm text-gray-900">
                        {employee.Gender}
                      </td>
                      <td className="w-[5%] px-2 py-3 text-sm font-medium">
                        <div className="flex gap-2">
                          <button
                            onClick={async () => {
                              setSearchType('individual');
                              setSelectedEmployee(employee);
                              await fetchEmployeePhoto(employee.EmployeeID);
                              await fetchEmployeeAssets(employee.EmployeeCode || employee.EmployeeID);
                              setShowGroupResults(false);
                            }}
                            className="text-blue-600 hover:text-blue-900"
                            title="عرض التفاصيل"
                          >
                            <FiUser />
                          </button>
                          <button
                            onClick={() => {
                              if (confirm(`هل تريد تعديل بيانات الموظف ${employee.FullName}؟`)) {
                                window.location.href = `/employees/edit/${employee.EmployeeID}`;
                              }
                            }}
                            className="text-green-600 hover:text-green-900"
                            title="تعديل"
                          >
                            <FiEdit />
                          </button>
                          <button
                            onClick={() => {
                              if (confirm(`⚠️ تحذير!\n\nهل أنت متأكد من حذف الموظف ${employee.FullName}؟\n\nكود الموظف: ${employee.EmployeeID}\n\nهذا الإجراء لا يمكن التراجع عنه!`)) {
                                alert(`تم تأكيد حذف الموظف: ${employee.FullName}\n\nملاحظة: وظيفة الحذف الفعلية ستحتاج لتطوير API خاص.`);
                              }
                            }}
                            className="text-red-600 hover:text-red-900"
                            title="حذف"
                          >
                            <FiTrash2 />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* نافذة المستندات */}
        {showDocuments && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
              <div className="flex items-center justify-between p-6 border-b">
                <h3 className="text-xl font-bold text-gray-800">أرشيف مستندات {selectedEmployee?.FullName}</h3>
                <button
                  onClick={() => setShowDocuments(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <FiX className="text-2xl" />
                </button>
              </div>

              <div className="p-6 overflow-y-auto max-h-[70vh]">
                {employeeDocuments.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {employeeDocuments.map((doc, index) => (
                      <div key={index} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div className="flex items-center gap-3 mb-3">
                          <i className={`fas ${doc.Icon} text-2xl`} style={{ color: doc.Color }}></i>
                          <div>
                            <h4 className="font-semibold text-gray-800">{doc.DocumentType}</h4>
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-2 mt-4">
                          {/* زر العرض */}
                          <button
                            onClick={() => {
                              // تحويل مسار Windows إلى مسار ويب
                              let webPath = doc.Path.replace(/\\/g, '/');
                              const archivIndex = webPath.indexOf('archiv/');
                              if (archivIndex !== -1) {
                                webPath = webPath.substring(archivIndex);
                              }
                              // فتح المستند في نافذة جديدة
                              window.open(`/${webPath}`, '_blank');
                            }}
                            className="bg-blue-600 text-white py-2 px-3 rounded hover:bg-blue-700 transition-colors text-sm"
                            title="عرض المستند"
                          >
                            <i className="fas fa-eye mr-1"></i>
                            عرض
                          </button>

                          {/* زر الطباعة */}
                          <button
                            onClick={() => {
                              let webPath = doc.Path.replace(/\\/g, '/');
                              const archivIndex = webPath.indexOf('archiv/');
                              if (archivIndex !== -1) {
                                webPath = webPath.substring(archivIndex);
                              }

                              try {
                                // فتح المستند للطباعة
                                const printWindow = window.open(`/${webPath}`, '_blank');

                                if (!printWindow) {
                                  alert('تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة.');
                                  return;
                                }

                                // انتظار تحميل المستند ثم طباعته
                                printWindow.onload = () => {
                                  setTimeout(() => {
                                    try {
                                      printWindow.print();
                                      // إغلاق النافذة بعد الطباعة (اختياري)
                                      setTimeout(() => {
                                        printWindow.close();
                                      }, 2000);
                                    } catch (error) {

                                      alert('حدث خطأ أثناء الطباعة. يرجى المحاولة مرة أخرى.');
                                    }
                                  }, 1500); // زيادة وقت الانتظار للتأكد من التحميل
                                };

                                // في حالة فشل التحميل
                                printWindow.onerror = () => {
                                  alert('فشل في تحميل المستند للطباعة. تأكد من وجود الملف.');
                                  printWindow.close();
                                };

                                // في حالة عدم تحميل الصفحة خلال 10 ثوان
                                setTimeout(() => {
                                  if (printWindow && !printWindow.closed) {
                                    try {
                                      // محاولة الطباعة حتى لو لم يتم تحميل الصفحة بالكامل
                                      printWindow.print();
                                    } catch (error) {
                                      alert('انتهت مهلة تحميل المستند. يرجى المحاولة مرة أخرى.');
                                      printWindow.close();
                                    }
                                  }
                                }, 10000);

                              } catch (error) {

                                alert('حدث خطأ في فتح المستند للطباعة.');
                              }
                            }}
                            className="bg-green-600 text-white py-2 px-3 rounded hover:bg-green-700 transition-colors text-sm"
                            title="طباعة المستند"
                          >
                            <i className="fas fa-print mr-1"></i>
                            طباعة
                          </button>

                          {/* زر التعديل */}
                          <button
                            onClick={() => {
                              // إنشاء input مخفي لاختيار ملف جديد
                              const input = document.createElement('input');
                              input.type = 'file';
                              input.accept = doc.Item === 'pic' ? 'image/*' : '.pdf';
                              input.onchange = async (e) => {
                                const file = e.target.files[0];
                                if (file) {
                                  // التحقق من نوع الملف
                                  const isValidType = doc.Item === 'pic' ?
                                    file.type.startsWith('image/') :
                                    file.type === 'application/pdf';

                                  if (!isValidType) {
                                    alert(`نوع الملف غير صحيح!\nيجب أن يكون ${doc.Item === 'pic' ? 'صورة (JPG, PNG)' : 'ملف PDF'}`);
                                    return;
                                  }

                                  if (confirm(`هل تريد استبدال ${doc.DocumentType} بالملف الجديد: ${file.name}؟`)) {
                                    alert(`تم اختيار الملف: ${file.name}\n\nملاحظة: وظيفة الرفع الفعلية ستحتاج لتطوير API خاص لرفع الملفات.`);
                                  }
                                }
                              };
                              input.click();
                            }}
                            className="bg-orange-600 text-white py-2 px-3 rounded hover:bg-orange-700 transition-colors text-sm"
                            title="تعديل المستند"
                          >
                            <i className="fas fa-edit mr-1"></i>
                            تعديل
                          </button>

                          {/* زر الحذف */}
                          <button
                            onClick={() => {
                              if (confirm(`⚠️ تحذير!\n\nهل أنت متأكد من حذف ${doc.DocumentType}؟\n\nهذا الإجراء لا يمكن التراجع عنه!`)) {
                                alert(`تم تأكيد حذف ${doc.DocumentType}\n\nملاحظة: وظيفة الحذف الفعلية ستحتاج لتطوير API خاص لحذف الملفات من الخادم.`);
                              }
                            }}
                            className="bg-red-600 text-white py-2 px-3 rounded hover:bg-red-700 transition-colors text-sm"
                            title="حذف المستند"
                          >
                            <i className="fas fa-trash mr-1"></i>
                            حذف
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <FiArchive className="text-6xl text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-600">لا توجد مستندات مؤرشفة لهذا الموظف</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* نافذة نقل الموظف */}
        {showTransferModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
              <div className="flex items-center justify-between p-6 border-b">
                <div>
                  <h3 className="text-xl font-bold text-gray-800">نقل الموظف</h3>
                  <p className="text-sm text-gray-600 mt-1">سيتم تحديث حالة الموظف إلى "منقول" وإضافة سجل في جدول النقل</p>
                </div>
                <button
                  onClick={() => setShowTransferModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <FiX className="text-2xl" />
                </button>
              </div>

              <div className="p-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الموظف
                    </label>
                    <input
                      type="text"
                      value={`${transferData.employeeName} (${transferData.employeeId})`}
                      disabled
                      className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      القسم الحالي
                    </label>
                    <input
                      type="text"
                      value={transferData.currentDepartment}
                      disabled
                      className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      القسم الجديد *
                    </label>
                    <select
                      value={transferData.newDepartment}
                      onChange={(e) => setTransferData(prev => ({ ...prev, newDepartment: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    >
                      <option value="">اختر القسم الجديد</option>
                      {departments.map(dept => (
                        <option key={dept} value={dept}>{dept}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      المشروع الجديد
                    </label>
                    <input
                      type="text"
                      value={transferData.newProject}
                      onChange={(e) => setTransferData(prev => ({ ...prev, newProject: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="اسم المشروع (اختياري)"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      تاريخ النقل *
                    </label>
                    <input
                      type="date"
                      value={transferData.transferDate}
                      onChange={(e) => setTransferData(prev => ({ ...prev, transferDate: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      سبب النقل *
                    </label>
                    <select
                      value={transferData.reason}
                      onChange={(e) => setTransferData(prev => ({ ...prev, reason: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    >
                      <option value="">اختر سبب النقل</option>
                      <option value="ترقية">ترقية</option>
                      <option value="إعادة هيكلة">إعادة هيكلة</option>
                      <option value="طلب شخصي">طلب شخصي</option>
                      <option value="احتياجات العمل">احتياجات العمل</option>
                      <option value="أخرى">أخرى</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      ملاحظات
                    </label>
                    <textarea
                      value={transferData.notes}
                      onChange={(e) => setTransferData(prev => ({ ...prev, notes: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      rows="3"
                      placeholder="ملاحظات إضافية (اختياري)"
                    />
                  </div>
                </div>

                <div className="flex gap-3 mt-6">
                  <button
                    onClick={confirmTransfer}
                    disabled={loading || !transferData.newDepartment || !transferData.reason}
                    className="flex-1 bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? 'جاري النقل...' : 'تأكيد النقل'}
                  </button>
                  <button
                    onClick={() => setShowTransferModal(false)}
                    disabled={loading}
                    className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors"
                  >
                    إلغاء
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* نافذة استقالة الموظف */}
        {showResignModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
              <div className="flex items-center justify-between p-6 border-b">
                <div>
                  <h3 className="text-xl font-bold text-gray-800">استقالة الموظف</h3>
                  <p className="text-sm text-gray-600 mt-1">سيتم تحديث حالة الموظف إلى "مستقيل" وإضافة سجل في جدول الاستقالة</p>
                </div>
                <button
                  onClick={() => setShowResignModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <FiX className="text-2xl" />
                </button>
              </div>

              <div className="p-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الموظف
                    </label>
                    <input
                      type="text"
                      value={`${resignData.employeeName} (${resignData.employeeId})`}
                      disabled
                      className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      القسم
                    </label>
                    <input
                      type="text"
                      value={resignData.department}
                      disabled
                      className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      تاريخ الاستقالة *
                    </label>
                    <input
                      type="date"
                      value={resignData.resignationDate}
                      onChange={(e) => setResignData(prev => ({ ...prev, resignationDate: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      سبب الاستقالة *
                    </label>
                    <select
                      value={resignData.reason}
                      onChange={(e) => setResignData(prev => ({ ...prev, reason: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                      required
                    >
                      <option value="">اختر سبب الاستقالة</option>
                      <option value="ظروف شخصية">ظروف شخصية</option>
                      <option value="فرصة عمل أفضل">فرصة عمل أفضل</option>
                      <option value="ظروف صحية">ظروف صحية</option>
                      <option value="ظروف عائلية">ظروف عائلية</option>
                      <option value="عدم الرضا عن العمل">عدم الرضا عن العمل</option>
                      <option value="أخرى">أخرى</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      ملاحظات
                    </label>
                    <textarea
                      value={resignData.notes}
                      onChange={(e) => setResignData(prev => ({ ...prev, notes: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                      rows="3"
                      placeholder="ملاحظات إضافية (اختياري)"
                    />
                  </div>
                </div>

                <div className="flex gap-3 mt-6">
                  <button
                    onClick={confirmResignation}
                    disabled={loading || !resignData.reason}
                    className="flex-1 bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? 'جاري التسجيل...' : 'تأكيد الاستقالة'}
                  </button>
                  <button
                    onClick={() => setShowResignModal(false)}
                    disabled={loading}
                    className="flex-1 bg-gray-600 text-white py-2 px-4 rounded-md hover:bg-gray-700 transition-colors"
                  >
                    إلغاء
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
        </div>
      </div>
    </div>
  );
}
