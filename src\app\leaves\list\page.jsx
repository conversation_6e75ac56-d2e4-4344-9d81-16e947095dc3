'use client';

import React, { useState, useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import MainLayout from '@/components/MainLayout';
import { Search, Eye, Edit, Trash2, CheckCircle, XCircle, Clock, FileText, Download } from 'lucide-react';

export default function LeavesList() {
  const { isDarkMode } = useTheme();
  const [leaves, setLeaves] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [message, setMessage] = useState('');

  useEffect(() => {
    loadLeaves();
  }, []);

  const loadLeaves = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/leave-management', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'list',
          employeeCode: searchTerm,
          status: statusFilter
        })
      });

      const result = await response.json();
      if (result.success) {
        setLeaves(result.data);
      } else {
        setMessage('فشل في جلب البيانات');
      }
    } catch (error) {
      setMessage('خطأ في الاتصال بالخادم');
    }
    setLoading(false);
  };

  const updateLeaveStatus = async (id, status) => {
    try {
      const response = await fetch('/api/leave-management', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'updateStatus',
          id: id,
          status: status
        })
      });

      const result = await response.json();
      if (result.success) {
        setMessage('تم تحديث حالة الطلب بنجاح');
        loadLeaves(); // إعادة تحميل البيانات
      } else {
        setMessage('فشل في تحديث الحالة');
      }
    } catch (error) {
      setMessage('خطأ في تحديث الحالة');
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      'قيد المراجعة': { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      'معتمد': { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      'مرفوض': { color: 'bg-red-100 text-red-800', icon: XCircle }
    };

    const config = statusConfig[status] || statusConfig['قيد المراجعة'];
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3 ml-1" />
        {status}
      </span>
    );
  };

  const getLeaveTypeBadge = (type) => {
    const typeConfig = {
      'اعتيادية': 'bg-blue-100 text-blue-800',
      'عارضة': 'bg-green-100 text-green-800',
      'مرضية': 'bg-red-100 text-red-800',
      'أمومة': 'bg-pink-100 text-pink-800'
    };

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${typeConfig[type] || 'bg-gray-100 text-gray-800'}`}>
        {type}
      </span>
    );
  };

  const exportToExcel = () => {
    // تحويل البيانات إلى CSV
    const headers = ['الكود', 'الاسم', 'الوظيفة', 'نوع الإجازة', 'تاريخ البداية', 'تاريخ النهاية', 'عدد الأيام', 'الحالة'];
    const csvContent = [
      headers.join(','),
      ...leaves.map(leave => [
        leave.EmployeeCode,
        leave.EmployeeName,
        leave.JobTitle,
        leave.LeaveType,
        leave.StartDate,
        leave.EndDate,
        leave.DaysCount,
        leave.Status
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', 'leaves_report.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const filteredLeaves = leaves.filter(leave => 
    leave.EmployeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    leave.EmployeeCode.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto">
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="flex items-center justify-between">
            <div>
              <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
                إدارة طلبات الإجازات
              </h1>
              <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                عرض وإدارة جميع طلبات الإجازات المسجلة في النظام
              </p>
            </div>
            <div className="flex gap-3">
              <button
                onClick={exportToExcel}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <Download className="w-4 h-4" />
                تصدير Excel
              </button>
              <a
                href="/leaves/request"
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <FileText className="w-4 h-4" />
                طلب إجازة جديد
              </a>
            </div>
          </div>
        </div>

        {/* رسائل التنبيه */}
        {message && (
          <div className={`p-4 rounded-lg mb-6 ${
            message.includes('نجاح')
              ? 'bg-green-100 text-green-800 border border-green-200'
              : 'bg-red-100 text-red-800 border border-red-200'
          }`}>
            {message}
          </div>
        )}

        {/* أدوات البحث والتصفية */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-700'} mb-2`}>
                البحث بالكود أو الاسم
              </label>
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`w-full pr-10 p-3 border rounded-lg ${
                    isDarkMode 
                      ? 'bg-slate-800 border-slate-600 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                  placeholder="ابحث بالكود أو الاسم..."
                />
              </div>
            </div>
            
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-700'} mb-2`}>
                تصفية بالحالة
              </label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className={`w-full p-3 border rounded-lg ${
                  isDarkMode 
                    ? 'bg-slate-800 border-slate-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="">جميع الحالات</option>
                <option value="قيد المراجعة">قيد المراجعة</option>
                <option value="معتمد">معتمد</option>
                <option value="مرفوض">مرفوض</option>
              </select>
            </div>
            
            <div className="flex items-end">
              <button
                onClick={loadLeaves}
                disabled={loading}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg flex items-center justify-center gap-2 transition-colors disabled:opacity-50"
              >
                <Search className="w-4 h-4" />
                {loading ? 'جاري البحث...' : 'بحث'}
              </button>
            </div>
          </div>
        </div>

        {/* جدول الإجازات */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border overflow-hidden`}>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-slate-700">
              <thead className={`${isDarkMode ? 'bg-slate-800' : 'bg-gray-50'}`}>
                <tr>
                  <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    الكود
                  </th>
                  <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    الاسم
                  </th>
                  <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    الوظيفة
                  </th>
                  <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    نوع الإجازة
                  </th>
                  <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    تاريخ البداية
                  </th>
                  <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    تاريخ النهاية
                  </th>
                  <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    عدد الأيام
                  </th>
                  <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    الحالة
                  </th>
                  <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} divide-y divide-gray-200 dark:divide-slate-700`}>
                {loading ? (
                  <tr>
                    <td colSpan="9" className="px-6 py-4 text-center">
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <span className="mr-2">جاري التحميل...</span>
                      </div>
                    </td>
                  </tr>
                ) : filteredLeaves.length === 0 ? (
                  <tr>
                    <td colSpan="9" className={`px-6 py-4 text-center ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>
                      لا توجد طلبات إجازات
                    </td>
                  </tr>
                ) : (
                  filteredLeaves.map((leave) => (
                    <tr key={leave.ID} className={`${isDarkMode ? 'hover:bg-slate-800' : 'hover:bg-gray-50'} transition-colors`}>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {leave.EmployeeCode}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-700'}`}>
                        {leave.EmployeeName}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-700'}`}>
                        {leave.JobTitle}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        {getLeaveTypeBadge(leave.LeaveType)}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-700'}`}>
                        {new Date(leave.StartDate).toLocaleDateString('ar-EG')}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-700'}`}>
                        {new Date(leave.EndDate).toLocaleDateString('ar-EG')}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {leave.DaysCount} يوم
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        {getStatusBadge(leave.Status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center gap-2">
                          {leave.Status === 'قيد المراجعة' && (
                            <>
                              <button
                                onClick={() => updateLeaveStatus(leave.ID, 'معتمد')}
                                className="text-green-600 hover:text-green-900 transition-colors"
                                title="اعتماد"
                              >
                                <CheckCircle className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => updateLeaveStatus(leave.ID, 'مرفوض')}
                                className="text-red-600 hover:text-red-900 transition-colors"
                                title="رفض"
                              >
                                <XCircle className="w-4 h-4" />
                              </button>
                            </>
                          )}
                          <button
                            className="text-blue-600 hover:text-blue-900 transition-colors"
                            title="عرض التفاصيل"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-6">
          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className={`text-sm font-medium ${isDarkMode ? 'text-slate-400' : 'text-gray-500'} truncate`}>
                    قيد المراجعة
                  </dt>
                  <dd className={`text-lg font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {leaves.filter(l => l.Status === 'قيد المراجعة').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className={`text-sm font-medium ${isDarkMode ? 'text-slate-400' : 'text-gray-500'} truncate`}>
                    معتمد
                  </dt>
                  <dd className={`text-lg font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {leaves.filter(l => l.Status === 'معتمد').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <XCircle className="h-8 w-8 text-red-600" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className={`text-sm font-medium ${isDarkMode ? 'text-slate-400' : 'text-gray-500'} truncate`}>
                    مرفوض
                  </dt>
                  <dd className={`text-lg font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {leaves.filter(l => l.Status === 'مرفوض').length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className={`text-sm font-medium ${isDarkMode ? 'text-slate-400' : 'text-gray-500'} truncate`}>
                    إجمالي الطلبات
                  </dt>
                  <dd className={`text-lg font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {leaves.length}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
