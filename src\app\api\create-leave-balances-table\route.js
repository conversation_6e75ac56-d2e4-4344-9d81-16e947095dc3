import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function POST(request) {
  try {
    const pool = await getConnection();
    
    // إضافة الأعمدة المفقودة إلى جدول LeaveBalances الموجود
    await pool.request().query(`
      -- إضافة عمود UsedRegular إذا لم يكن موجوداً
      IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('LeaveBalances') AND name = 'UsedRegular')
      BEGIN
        ALTER TABLE LeaveBalances ADD UsedRegular INT DEFAULT 0
      END

      -- إضافة عمود UsedCasual إذا لم يكن موجوداً
      IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('LeaveBalances') AND name = 'UsedCasual')
      BEGIN
        ALTER TABLE LeaveBalances ADD UsedCasual INT DEFAULT 0
      END

      -- إضافة عمود LastLeaveDate إذا لم يكن موجوداً
      IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('LeaveBalances') AND name = 'LastLeaveDate')
      BEGIN
        ALTER TABLE LeaveBalances ADD LastLeaveDate DATE
      END

      -- إضافة عمود Year إذا لم يكن موجوداً
      IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('LeaveBalances') AND name = 'Year')
      BEGIN
        ALTER TABLE LeaveBalances ADD Year INT DEFAULT YEAR(GETDATE())
      END

      -- إضافة عمود CreatedAt إذا لم يكن موجوداً
      IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('LeaveBalances') AND name = 'CreatedAt')
      BEGIN
        ALTER TABLE LeaveBalances ADD CreatedAt DATETIME DEFAULT GETDATE()
      END

      -- إضافة عمود UpdatedAt إذا لم يكن موجوداً
      IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('LeaveBalances') AND name = 'UpdatedAt')
      BEGIN
        ALTER TABLE LeaveBalances ADD UpdatedAt DATETIME DEFAULT GETDATE()
      END
    `);

    // إدراج بيانات افتراضية للموظفين الموجودين
    await pool.request().query(`
      INSERT INTO LeaveBalances (EmployeeCode, EmployeeName, JobTitle, Department)
      SELECT DISTINCT
        EmployeeCode,
        EmployeeName,
        JobTitle,
        Department
      FROM Employees
      WHERE EmployeeCode IS NOT NULL 
        AND EmployeeCode != ''
        AND NOT EXISTS (
          SELECT 1 FROM LeaveBalances lb 
          WHERE lb.EmployeeCode = Employees.EmployeeCode
        )
    `);

    // فحص النتيجة
    const result = await pool.request().query(`
      SELECT COUNT(*) as TotalRecords FROM LeaveBalances
    `);

    return NextResponse.json({
      success: true,
      message: 'تم إنشاء جدول LeaveBalances بنجاح',
      totalRecords: result.recordset[0].TotalRecords
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  }
}
