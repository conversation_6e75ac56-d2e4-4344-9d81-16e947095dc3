'use client';
import React from 'react';

function MainComponent() {
  const [selectedLang, setSelectedLang] = useState('ar');
  const [selectedReport, setSelectedReport] = useState('employees');
  const [dateRange, setDateRange] = useState({ start: '', end: '' });
  const [department, setDepartment] = useState('');
  const [employee, setEmployee] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const dir = selectedLang === 'ar' ? 'rtl' : 'ltr';

  const reportTypes = [
    {
      id: 'employees',
      titleAr: 'تقارير الموظفين',
      titleEn: 'Employee Reports',
      icon: 'fa-users',
    },
    {
      id: 'attendance',
      titleAr: 'تقارير الحضور',
      titleEn: 'Attendance Reports',
      icon: 'fa-clock',
    },
    {
      id: 'leaves',
      titleAr: 'تقارير الإجازات',
      titleEn: 'Leave Reports',
      icon: 'fa-calendar',
    },
    {
      id: 'costs',
      titleAr: 'تقارير التكاليف',
      titleEn: 'Cost Reports',
      icon: 'fa-money-bill',
    },
  ];

  const handleGenerateReport = async (format) => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/data-service', {
        method: 'POST',
        body: JSON.stringify({
          action: 'generate_report',
          reportType: selectedReport,
          format,
          filters: {
            dateRange,
            department,
            employee,
          },
        }),
      });

      if (!response.ok) {
        throw new Error(
          selectedLang === 'ar'
            ? 'حدث خطأ أثناء إنشاء التقرير'
            : 'Error generating report'
        );
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `report-${selectedReport}-${format}.${format}`;
      a.click();
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      dir={dir}
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {selectedLang === 'ar' ? 'التقارير الموحدة' : 'Unified Reports'}
          </h1>
          <button
            onClick={() => setSelectedLang(selectedLang === 'ar' ? 'en' : 'ar')}
            className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
          >
            {selectedLang === 'ar' ? 'English' : 'العربية'}
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          {reportTypes.map((report) => (
            <button
              key={report.id}
              onClick={() => setSelectedReport(report.id)}
              className={`p-4 rounded-lg flex items-center space-x-3 ${
                selectedReport === report.id
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
            >
              <i className={`fas ${report.icon} text-xl`}></i>
              <span className={selectedLang === 'ar' ? 'mr-3' : 'ml-3'}>
                {selectedLang === 'ar' ? report.titleAr : report.titleEn}
              </span>
            </button>
          ))}
        </div>

        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
            {selectedLang === 'ar' ? 'تصفية التقارير' : 'Filter Reports'}
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                {selectedLang === 'ar' ? 'من تاريخ' : 'Start Date'}
              </label>
              <input
                type="date"
                value={dateRange.start}
                onChange={(e) =>
                  setDateRange({ ...dateRange, start: e.target.value })
                }
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                {selectedLang === 'ar' ? 'إلى تاريخ' : 'End Date'}
              </label>
              <input
                type="date"
                value={dateRange.end}
                onChange={(e) =>
                  setDateRange({ ...dateRange, end: e.target.value })
                }
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                {selectedLang === 'ar' ? 'القسم' : 'Department'}
              </label>
              <select
                value={department}
                onChange={(e) => setDepartment(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">
                  {selectedLang === 'ar' ? 'كل الأقسام' : 'All Departments'}
                </option>
              </select>
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                {selectedLang === 'ar' ? 'الموظف' : 'Employee'}
              </label>
              <select
                value={employee}
                onChange={(e) => setEmployee(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="">
                  {selectedLang === 'ar' ? 'كل الموظفين' : 'All Employees'}
                </option>
              </select>
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-4">
          <button
            onClick={() => handleGenerateReport('pdf')}
            disabled={loading}
            className="px-6 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors disabled:bg-red-300"
          >
            <i className="fas fa-file-pdf mr-2"></i>
            {selectedLang === 'ar' ? 'تصدير PDF' : 'Export PDF'}
          </button>

          <button
            onClick={() => handleGenerateReport('excel')}
            disabled={loading}
            className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:bg-green-300"
          >
            <i className="fas fa-file-excel mr-2"></i>
            {selectedLang === 'ar' ? 'تصدير Excel' : 'Export Excel'}
          </button>
        </div>

        {error && (
          <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-md">
            {error}
          </div>
        )}

        <div className="mt-8 bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
          <div className="text-center text-gray-600 dark:text-gray-400">
            {selectedLang === 'ar'
              ? 'سيتم عرض نتائج التقرير هنا'
              : 'Report results will be displayed here'}
          </div>
        </div>
      </div>
    </div>
  );
}

export default MainComponent;
