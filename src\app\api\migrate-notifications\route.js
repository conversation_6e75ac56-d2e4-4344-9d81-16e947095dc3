import { NextResponse } from 'next/server';
import { getConnection } from '@/utils/db';
import sql from 'mssql';

export async function POST(request) {
  let pool;

  try {
    pool = await getConnection();

    // حذف الإشعارات القديمة
    await pool.request().query(`
      DELETE FROM Notifications WHERE NotificationType LIKE '%LEAVE%'
    `);

    // حذف الإشعارات الذكية القديمة
    await pool.request().query(`
      DELETE FROM SmartNotifications WHERE NotificationType LIKE '%LEAVE%'
    `);

    // إنشاء إشعار تجريبي بالتنسيق الجديد
    await pool.request()
      .input('notificationType', sql.NVarChar, 'LEAVE_REQUEST_SUBMITTED')
      .input('title', sql.NVarChar, 'طلب إجازة جديد - أحمد محمد')
      .input('message', sql.NVarChar, 'تم تقديم إجازة إعتيادية لـ (أحمد محمد) (5 أيام) تبدأ من (15/01/2025) بواسطة (سامي منير)')
      .input('employeeId', sql.NVarChar, 'EMP001')
      .input('employeeName', sql.NVarChar, 'أحمد محمد')
      .input('systemUserId', sql.NVarChar, 'سامي منير')
      .input('leaveStartDate', sql.Date, new Date('2025-01-15'))
      .input('priority', sql.NVarChar, 'high')
      .input('relatedData', sql.NVarChar, JSON.stringify({
        requestId: 1,
        leaveType: 'إعتيادية',
        startDate: '15/01/2025',
        totalDays: 5,
        systemUser: 'سامي منير',
        submissionTime: new Date().toISOString()
      }))
      .query(`
        INSERT INTO SmartNotifications (
          NotificationType, Title, Message, EmployeeID, EmployeeName, 
          SystemUserID, LeaveStartDate, RelatedData, Priority
        )
        VALUES (
          @notificationType, @title, @message, @employeeId, @employeeName,
          @systemUserId, @leaveStartDate, @relatedData, @priority
        )
      `);

    // إنشاء إشعار اعتماد تجريبي
    await pool.request()
      .input('notificationType', sql.NVarChar, 'LEAVE_REQUEST_APPROVED')
      .input('title', sql.NVarChar, 'تم اعتماد طلب الإجازة - سارة أحمد')
      .input('message', sql.NVarChar, 'تم اعتماد إجازة مرضية لـ (سارة أحمد) (3 أيام) تبدأ من (17/01/2025) بواسطة (إسلام فايز)')
      .input('employeeId', sql.NVarChar, 'EMP002')
      .input('employeeName', sql.NVarChar, 'سارة أحمد')
      .input('systemUserId', sql.NVarChar, 'إسلام فايز')
      .input('leaveStartDate', sql.Date, new Date('2025-01-17'))
      .input('priority', sql.NVarChar, 'medium')
      .input('relatedData', sql.NVarChar, JSON.stringify({
        requestId: 2,
        leaveType: 'مرضية',
        startDate: '17/01/2025',
        totalDays: 3,
        systemUser: 'إسلام فايز',
        actionTime: new Date().toISOString()
      }))
      .query(`
        INSERT INTO SmartNotifications (
          NotificationType, Title, Message, EmployeeID, EmployeeName, 
          SystemUserID, LeaveStartDate, RelatedData, Priority
        )
        VALUES (
          @notificationType, @title, @message, @employeeId, @employeeName,
          @systemUserId, @leaveStartDate, @relatedData, @priority
        )
      `);

    return NextResponse.json({
      success: true,
      message: 'تم تنظيف الإشعارات القديمة وإنشاء إشعارات جديدة بالتنسيق المحدث'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في ترحيل الإشعارات: ' + error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      try {
        await pool.close();
      } catch (error) {

      }
    }
  }
}

export async function GET() {
  return POST({ json: () => Promise.resolve({}) });
}
