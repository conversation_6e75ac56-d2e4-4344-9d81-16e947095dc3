import { NextResponse } from 'next/server';
import sql from 'mssql';
import { config } from '@/lib/db';

export async function POST(request) {
  let pool = null;
  try {

    const body = await request.json();
    const { employeeId } = body;

    if (!employeeId || employeeId.trim() === '') {
      return NextResponse.json({
        success: false,
        error: 'يرجى إدخال رقم الموظف'
      });
    }

    pool = await sql.connect(config);

    const result = await pool.request()
      .input('employeeId', sql.NVarChar, employeeId)
      .query(`
        SELECT 
          EmployeeID,
          FullName,
          DepartmentID,
          JobTitleID,
          IsActive
        FROM Employees WITH(NOLOCK)
        WHERE EmployeeID = @employeeId
          AND IsActive = 1
      `);

    if (result.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'رقم الموظف غير صحيح'
      });
    }

    const employee = result.recordset[0];
    if (!employee.IsActive) {
      return NextResponse.json({
        success: false,
        error: 'هذا الحساب غير نشط. يرجى مراجعة الإدارة'
      });
    }

    return NextResponse.json({
      success: true,
      data: employee
    });

  } catch (error) {

    let errorMessage = 'حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى';
    if (error.code === 'ETIMEDOUT') {
      errorMessage = 'انتهت مهلة الاتصال بقاعدة البيانات. يرجى المحاولة مرة أخرى';
    } else if (error.code === 'ECONNREFUSED') {
      errorMessage = 'تعذر الاتصال بقاعدة البيانات. يرجى التحقق من اتصال الشبكة';
    }
    
    return NextResponse.json({
      success: false,
      error: errorMessage
    }, { status: 500 });
  } finally {
    if (pool) {
      try {

        await sql.close();

      } catch (err) {

      }
    }
  }
}