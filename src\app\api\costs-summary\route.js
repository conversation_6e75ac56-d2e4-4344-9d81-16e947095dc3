import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

// جلب ملخص التكاليف وطلبات الإصدار
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'cars';
    const year = parseInt(searchParams.get('year')) || new Date().getFullYear();

    const pool = await getConnection();
    let summary = {};

    switch (type) {
      case 'cars':
        summary = await getCarsCostsSummary(pool, year);
        break;
      case 'apartments':
        summary = await getApartmentsCostsSummary(pool, year);
        break;
      case 'tempWorkers':
        summary = await getTempWorkersCostsSummary(pool, year);
        break;
      default:
        summary = { totalAmount: 0, totalRecords: 0, reports: [] };
    }

    return NextResponse.json({
      success: true,
      summary: summary
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'حدث خطأ في جلب ملخص التكاليف',
      error: error.message
    }, { status: 500 });
  }
}

// ملخص تكاليف السيارات
async function getCarsCostsSummary(pool, year) {
  try {
    // حساب إجمالي التكاليف
    const totalQuery = `
      SELECT 
        COUNT(*) as TotalRecords,
        SUM(CAST([القيمة الإيجارية] AS DECIMAL(10,2))) as TotalAmount,
        SUM(CAST([العدد] AS INT)) as TotalCars,
        AVG(CAST([القيمة الإيجارية] AS DECIMAL(10,2))) as AvgAmount
      FROM carscost 
      WHERE [السنة] = @Year AND CAST([القيمة الإيجارية] AS INT) > 0
    `;

    const totalRequest = pool.request();
    totalRequest.input('Year', sql.NVarChar, year.toString());
    const totalResult = await totalRequest.query(totalQuery);
    const totals = totalResult.recordset[0];

    // جلب طلبات الإصدار من الأرشيف
    const reportsQuery = `
      SELECT 
        ID,
        Path,
        CreatedAt,
        CASE 
          WHEN Path LIKE '%1-${year}%' THEN 'يناير ${year}'
          WHEN Path LIKE '%2-${year}%' THEN 'فبراير ${year}'
          WHEN Path LIKE '%3-${year}%' THEN 'مارس ${year}'
          WHEN Path LIKE '%4-${year}%' THEN 'أبريل ${year}'
          WHEN Path LIKE '%5-${year}%' THEN 'مايو ${year}'
          WHEN Path LIKE '%6-${year}%' THEN 'يونيو ${year}'
          WHEN Path LIKE '%7-${year}%' THEN 'يوليو ${year}'
          WHEN Path LIKE '%8-${year}%' THEN 'أغسطس ${year}'
          WHEN Path LIKE '%9-${year}%' THEN 'سبتمبر ${year}'
          WHEN Path LIKE '%10-${year}%' THEN 'أكتوبر ${year}'
          WHEN Path LIKE '%11-${year}%' THEN 'نوفمبر ${year}'
          WHEN Path LIKE '%12-${year}%' THEN 'ديسمبر ${year}'
          ELSE 'غير محدد'
        END as PeriodName,
        CASE 
          WHEN Path LIKE '%1-${year}%' THEN 1
          WHEN Path LIKE '%2-${year}%' THEN 2
          WHEN Path LIKE '%3-${year}%' THEN 3
          WHEN Path LIKE '%4-${year}%' THEN 4
          WHEN Path LIKE '%5-${year}%' THEN 5
          WHEN Path LIKE '%6-${year}%' THEN 6
          WHEN Path LIKE '%7-${year}%' THEN 7
          WHEN Path LIKE '%8-${year}%' THEN 8
          WHEN Path LIKE '%9-${year}%' THEN 9
          WHEN Path LIKE '%10-${year}%' THEN 10
          WHEN Path LIKE '%11-${year}%' THEN 11
          WHEN Path LIKE '%12-${year}%' THEN 12
          ELSE 0
        END as MonthNumber
      FROM Archive 
      WHERE Item = 'carscost' AND Path LIKE '%${year}%'
      ORDER BY MonthNumber DESC
    `;

    const reportsResult = await pool.request().query(reportsQuery);

    // جلب التفاصيل الشهرية
    const monthlyQuery = `
      SELECT 
        [الشهر] as Month,
        CAST([القيمة الإيجارية] AS DECIMAL(10,2)) as Amount,
        CAST([العدد] AS INT) as CarCount
      FROM carscost 
      WHERE [السنة] = @Year
      ORDER BY 
        CASE [الشهر]
          WHEN 'يناير' THEN 1
          WHEN 'فبراير' THEN 2
          WHEN 'مارس' THEN 3
          WHEN 'أبريل' THEN 4
          WHEN 'مايو' THEN 5
          WHEN 'يونيو' THEN 6
          WHEN 'يوليو' THEN 7
          WHEN 'أغسطس' THEN 8
          WHEN 'سبتمبر' THEN 9
          WHEN 'أكتوبر' THEN 10
          WHEN 'نوفمبر' THEN 11
          WHEN 'ديسمبر' THEN 12
        END
    `;

    const monthlyRequest = pool.request();
    monthlyRequest.input('Year', sql.NVarChar, year.toString());
    const monthlyResult = await monthlyRequest.query(monthlyQuery);

    return {
      totalAmount: totals.TotalAmount || 0,
      totalRecords: totals.TotalRecords || 0,
      totalCars: totals.TotalCars || 0,
      avgAmount: totals.AvgAmount || 0,
      reports: reportsResult.recordset || [],
      monthlyData: monthlyResult.recordset || [],
      year: year
    };

  } catch (error) {

    return {
      totalAmount: 0,
      totalRecords: 0,
      totalCars: 0,
      avgAmount: 0,
      reports: [],
      monthlyData: [],
      year: year
    };
  }
}

// ملخص تكاليف الشقق (بيانات تجريبية)
async function getApartmentsCostsSummary(pool, year) {
  try {
    const query = `
      SELECT 
        COUNT(*) as TotalRecords,
        SUM(Amount) as TotalAmount,
        AVG(Amount) as AvgAmount
      FROM Costs 
      WHERE Type = 'apartment' AND YEAR(Date) = @Year
    `;

    const request = pool.request();
    request.input('Year', sql.Int, year);
    const result = await request.query(query);
    const totals = result.recordset[0];

    return {
      totalAmount: totals.TotalAmount || 0,
      totalRecords: totals.TotalRecords || 0,
      avgAmount: totals.AvgAmount || 0,
      reports: [], // يمكن إضافة طلبات إصدار للشقق لاحقاً
      monthlyData: [],
      year: year
    };

  } catch (error) {

    return {
      totalAmount: 125000,
      totalRecords: 15,
      avgAmount: 8333,
      reports: [],
      monthlyData: [],
      year: year
    };
  }
}

// ملخص تكاليف العمالة المؤقتة (بيانات تجريبية)
async function getTempWorkersCostsSummary(pool, year) {
  try {
    const query = `
      SELECT 
        COUNT(*) as TotalRecords,
        SUM(Amount) as TotalAmount,
        AVG(Amount) as AvgAmount
      FROM Costs 
      WHERE Type = 'temp_worker' AND YEAR(Date) = @Year
    `;

    const request = pool.request();
    request.input('Year', sql.Int, year);
    const result = await request.query(query);
    const totals = result.recordset[0];

    return {
      totalAmount: totals.TotalAmount || 0,
      totalRecords: totals.TotalRecords || 0,
      avgAmount: totals.AvgAmount || 0,
      reports: [], // يمكن إضافة طلبات إصدار للعمالة لاحقاً
      monthlyData: [],
      year: year
    };

  } catch (error) {

    return {
      totalAmount: 180000,
      totalRecords: 25,
      avgAmount: 7200,
      reports: [],
      monthlyData: [],
      year: year
    };
  }
}
