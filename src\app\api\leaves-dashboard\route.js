import { NextResponse } from 'next/server';
import { getConnection } from '@/utils/db';
import sql from 'mssql';

export async function POST(request) {
  try {
    const body = await request.json();
    const { period = 'month' } = body;

    const pool = await getConnection();

    // تحديد الفترة الزمنية
    let dateFilter = '';
    const now = new Date();
    
    switch (period) {
      case 'week':
        const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
        dateFilter = `AND RequestDate >= '${weekStart.toISOString().split('T')[0]}'`;
        break;
      case 'month':
        const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);
        dateFilter = `AND RequestDate >= '${monthStart.toISOString().split('T')[0]}'`;
        break;
      case 'quarter':
        const quarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 1);
        dateFilter = `AND RequestDate >= '${quarterStart.toISOString().split('T')[0]}'`;
        break;
      case 'year':
        const yearStart = new Date(now.getFullYear(), 0, 1);
        dateFilter = `AND RequestDate >= '${yearStart.toISOString().split('T')[0]}'`;
        break;
      default:
        dateFilter = '';
    }

    // 1. إحصائيات الطلبات العامة
    const generalStatsQuery = `
      SELECT 
        COUNT(*) as TotalRequests,
        COUNT(CASE WHEN Status = 'معتمدة' THEN 1 END) as ApprovedRequests,
        COUNT(CASE WHEN Status = 'قيد المراجعة' THEN 1 END) as PendingRequests,
        COUNT(CASE WHEN Status = 'مرفوضة' THEN 1 END) as RejectedRequests,
        SUM(CASE WHEN Status = 'معتمدة' THEN DaysCount ELSE 0 END) as TotalApprovedDays,
        AVG(CASE WHEN Status = 'معتمدة' THEN CAST(DaysCount as FLOAT) ELSE NULL END) as AverageDays
      FROM PaperRequests 
      WHERE RequestType = 'leave' ${dateFilter}
    `;

    const generalStats = await pool.request().query(generalStatsQuery);
    const stats = generalStats.recordset[0];

    // 2. إحصائيات أنواع الإجازات
    const leaveTypesQuery = `
      SELECT 
        LeaveType,
        COUNT(*) as Count,
        SUM(CASE WHEN Status = 'معتمدة' THEN DaysCount ELSE 0 END) as TotalDays
      FROM PaperRequests 
      WHERE RequestType = 'leave' 
        AND Status = 'معتمدة' 
        ${dateFilter}
      GROUP BY LeaveType
    `;

    const leaveTypesResult = await pool.request().query(leaveTypesQuery);
    const leaveTypes = {};
    
    leaveTypesResult.recordset.forEach(row => {
      const type = row.LeaveType;
      if (type === 'annual' || type === 'اعتيادية' || type === 'إعتيادية') {
        leaveTypes.annual = (leaveTypes.annual || 0) + row.Count;
      } else if (type === 'emergency' || type === 'عارضة') {
        leaveTypes.emergency = (leaveTypes.emergency || 0) + row.Count;
      } else if (type === 'sick' || type === 'مرضية') {
        leaveTypes.sick = (leaveTypes.sick || 0) + row.Count;
      } else if (type === 'unpaid' || type === 'بدون أجر') {
        leaveTypes.unpaid = (leaveTypes.unpaid || 0) + row.Count;
      }
    });

    // 3. أهم الموظفين (الأكثر استخداماً للإجازات)
    const topEmployeesQuery = `
      SELECT TOP 10
        EmployeeName,
        COUNT(*) as RequestCount,
        SUM(CASE WHEN Status = 'معتمدة' THEN DaysCount ELSE 0 END) as TotalDays
      FROM PaperRequests 
      WHERE RequestType = 'leave' 
        AND Status = 'معتمدة' 
        ${dateFilter}
      GROUP BY EmployeeName, EmployeeCode
      ORDER BY TotalDays DESC
    `;

    const topEmployeesResult = await pool.request().query(topEmployeesQuery);
    const topEmployees = topEmployeesResult.recordset.map(row => ({
      name: row.EmployeeName,
      requests: row.RequestCount,
      days: row.TotalDays
    }));

    // 4. الاتجاه الشهري (مقارنة مع الفترة السابقة)
    let previousPeriodFilter = '';
    switch (period) {
      case 'week':
        const prevWeekStart = new Date(now.setDate(now.getDate() - 14));
        const prevWeekEnd = new Date(now.setDate(now.getDate() - 7));
        previousPeriodFilter = `AND RequestDate >= '${prevWeekStart.toISOString().split('T')[0]}' AND RequestDate < '${prevWeekEnd.toISOString().split('T')[0]}'`;
        break;
      case 'month':
        const prevMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const prevMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
        previousPeriodFilter = `AND RequestDate >= '${prevMonthStart.toISOString().split('T')[0]}' AND RequestDate <= '${prevMonthEnd.toISOString().split('T')[0]}'`;
        break;
      case 'quarter':
        const prevQuarterStart = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3 - 3, 1);
        const prevQuarterEnd = new Date(now.getFullYear(), Math.floor(now.getMonth() / 3) * 3, 0);
        previousPeriodFilter = `AND RequestDate >= '${prevQuarterStart.toISOString().split('T')[0]}' AND RequestDate <= '${prevQuarterEnd.toISOString().split('T')[0]}'`;
        break;
      case 'year':
        const prevYearStart = new Date(now.getFullYear() - 1, 0, 1);
        const prevYearEnd = new Date(now.getFullYear() - 1, 11, 31);
        previousPeriodFilter = `AND RequestDate >= '${prevYearStart.toISOString().split('T')[0]}' AND RequestDate <= '${prevYearEnd.toISOString().split('T')[0]}'`;
        break;
    }

    const previousPeriodQuery = `
      SELECT COUNT(*) as PreviousRequests
      FROM PaperRequests 
      WHERE RequestType = 'leave' ${previousPeriodFilter}
    `;

    const previousPeriodResult = await pool.request().query(previousPeriodQuery);
    const previousRequests = previousPeriodResult.recordset[0].PreviousRequests || 0;
    
    const change = previousRequests > 0 ? 
      ((stats.TotalRequests - previousRequests) / previousRequests * 100).toFixed(1) : 0;

    // 5. إحصائيات الأقسام
    const departmentStatsQuery = `
      SELECT 
        e.Department,
        COUNT(pr.ID) as RequestCount,
        SUM(CASE WHEN pr.Status = 'معتمدة' THEN pr.DaysCount ELSE 0 END) as TotalDays
      FROM PaperRequests pr
      LEFT JOIN Employees e ON pr.EmployeeCode = e.EmployeeCode
      WHERE pr.RequestType = 'leave' 
        AND pr.Status = 'معتمدة' 
        ${dateFilter}
        AND e.Department IS NOT NULL
      GROUP BY e.Department
      ORDER BY TotalDays DESC
    `;

    const departmentStatsResult = await pool.request().query(departmentStatsQuery);
    const departmentStats = departmentStatsResult.recordset;

    // 6. إحصائيات يومية للأسبوع الحالي
    const dailyStatsQuery = `
      SELECT 
        CAST(RequestDate as DATE) as RequestDate,
        COUNT(*) as DailyRequests
      FROM PaperRequests 
      WHERE RequestType = 'leave' 
        AND RequestDate >= DATEADD(day, -7, GETDATE())
      GROUP BY CAST(RequestDate as DATE)
      ORDER BY RequestDate
    `;

    const dailyStatsResult = await pool.request().query(dailyStatsQuery);
    const dailyStats = dailyStatsResult.recordset;

    // تجميع البيانات
    const dashboardData = {
      // الإحصائيات العامة
      totalRequests: stats.TotalRequests || 0,
      approvedRequests: stats.ApprovedRequests || 0,
      pendingRequests: stats.PendingRequests || 0,
      rejectedRequests: stats.RejectedRequests || 0,
      totalDays: stats.TotalApprovedDays || 0,
      averageDays: parseFloat(stats.AverageDays || 0).toFixed(1),

      // أنواع الإجازات
      leaveTypes: {
        annual: leaveTypes.annual || 0,
        emergency: leaveTypes.emergency || 0,
        sick: leaveTypes.sick || 0,
        unpaid: leaveTypes.unpaid || 0
      },

      // أهم الموظفين
      topEmployees: topEmployees.slice(0, 5),

      // الاتجاه
      monthlyTrend: {
        current: stats.TotalRequests || 0,
        previous: previousRequests,
        change: parseFloat(change)
      },

      // إحصائيات الأقسام
      departmentStats: departmentStats,

      // الإحصائيات اليومية
      dailyStats: dailyStats,

      // معلومات إضافية
      period: period,
      generatedAt: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      data: dashboardData
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب بيانات الداش بورد'
    }, { status: 500 });
  }
}

export async function GET() {
  return POST({ json: () => Promise.resolve({ period: 'month' }) });
}
