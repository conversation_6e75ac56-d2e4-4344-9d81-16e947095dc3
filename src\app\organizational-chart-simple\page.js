'use client';

import { useState, useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import MainLayout from '@/components/MainLayout';
import { Tree, TreeNode } from 'react-organizational-chart';
import {
  FiUsers,
  FiUser,
  FiSearch,
  FiFilter,
  FiDownload,
  FiZoomIn,
  FiZoomOut,
  FiMaximize,
  FiMail,
  FiPhone,
  FiMapPin,
  FiCalendar,
  FiAward,
  FiRefreshCw
} from 'react-icons/fi';

export default function OrganizationalChartSimple() {
  const { isDarkMode } = useTheme();

  const [employees, setEmployees] = useState([]);
  const [organizationTree, setOrganizationTree] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState('');
  const [departments, setDepartments] = useState([]);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [selectedEmployee, setSelectedEmployee] = useState(null);

  // ألوان مختلفة للمستويات الإدارية
  const levelColors = {
    1: { bg: 'from-blue-500 to-purple-600', border: 'border-blue-500', text: 'text-blue-600' },
    2: { bg: 'from-pink-500 to-red-500', border: 'border-pink-500', text: 'text-pink-600' },
    3: { bg: 'from-blue-400 to-cyan-400', border: 'border-blue-400', text: 'text-blue-500' },
    4: { bg: 'from-green-400 to-teal-400', border: 'border-green-400', text: 'text-green-600' },
    5: { bg: 'from-yellow-400 to-orange-400', border: 'border-yellow-400', text: 'text-yellow-600' }
  };

  useEffect(() => {
    fetchEmployees();
  }, []);

  // جلب بيانات الموظفين
  const fetchEmployees = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/organizational-chart-enhanced');
      const data = await response.json();

      if (data.success) {
        setEmployees(data.employees);
        setDepartments(data.departments);
        buildOrganizationTree(data.employees);
      } else {
        setError(data.error || 'فشل في جلب البيانات');
      }
    } catch (error) {

      setError('حدث خطأ في الاتصال بالخادم');
    } finally {
      setLoading(false);
    }
  };

  // بناء شجرة التنظيم
  const buildOrganizationTree = (employeesData) => {
    // العثور على المدير العام (الذي ليس له مدير مباشر)
    const topManager = employeesData.find(emp =>
      !emp.DirectManager1 || emp.DirectManager1 === '' || emp.DirectManager1 === null
    );

    if (!topManager) {
      setError('لم يتم العثور على المدير العام');
      return;
    }

    const tree = buildEmployeeNode(topManager, employeesData, 1);
    setOrganizationTree(tree);
  };

  // بناء عقدة موظف مع أطفاله
  const buildEmployeeNode = (employee, allEmployees, level) => {
    // العثور على المرؤوسين المباشرين
    const directReports = allEmployees.filter(emp =>
      emp.DirectManager1 === employee.EmployeeCode ||
      emp.DirectManager2 === employee.EmployeeCode ||
      emp.DirectManager3 === employee.EmployeeCode ||
      emp.DirectManager4 === employee.EmployeeCode
    );

    const children = directReports.map(report =>
      buildEmployeeNode(report, allEmployees, level + 1)
    );

    return {
      ...employee,
      level,
      children
    };
  };

  // مكون عقدة الموظف
  const EmployeeNode = ({ employee, onClick }) => {
    const colors = levelColors[employee.level] || levelColors[5];
    const initials = employee.EmployeeName
      ? employee.EmployeeName.split(' ').map(name => name[0]).join('').substring(0, 2)
      : 'غ م';

    return (
      <div
        className={`
          p-4 rounded-xl border-2 ${colors.border}
          ${isDarkMode ? 'bg-gray-800' : 'bg-white'}
          shadow-lg hover:shadow-xl transition-all duration-300
          cursor-pointer hover:-translate-y-1
          min-w-[280px] max-w-[320px] relative
          hover:border-opacity-80
        `}
        onClick={() => onClick(employee)}
      >
        {/* خلفية متدرجة */}
        <div className={`absolute inset-0 bg-gradient-to-br ${colors.bg} opacity-5 rounded-xl`}></div>

        {/* الصورة الرمزية */}
        <div className="relative z-10">
          <div className={`
            w-16 h-16 rounded-full bg-gradient-to-br ${colors.bg}
            flex items-center justify-center text-white font-bold text-xl
            mx-auto mb-3 border-4 border-white shadow-lg
          `}>
            {initials}
          </div>

          {/* معلومات الموظف */}
          <div className="text-center">
            <h3 className={`text-lg font-bold mb-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              {employee.EmployeeName || 'غير محدد'}
            </h3>

            <p className={`text-sm mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'} font-medium`}>
              {employee.JobTitle || 'غير محدد'}
            </p>

            <span className={`
              inline-block px-3 py-1 rounded-full text-xs font-semibold
              ${isDarkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-700'}
            `}>
              كود: {employee.EmployeeCode}
            </span>

            {/* إحصائيات */}
            <div className={`
              flex justify-around mt-3 pt-3
              border-t ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}
            `}>
              <div className="text-center">
                <FiUsers className={`mx-auto mb-1 ${colors.text}`} />
                <div className={`text-xs font-semibold ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  {employee.children?.length || 0}
                </div>
                <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  مرؤوس
                </div>
              </div>

              <div className="text-center">
                <FiAward className={`mx-auto mb-1 ${colors.text}`} />
                <div className={`text-xs font-semibold ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  {employee.level}
                </div>
                <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  المستوى
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // رندر شجرة التنظيم
  const renderTree = (node) => {
    if (!node) return null;

    return (
      <TreeNode
        key={node.EmployeeCode}
        label={<EmployeeNode employee={node} onClick={setSelectedEmployee} />}
      >
        {node.children && node.children.map(child => renderTree(child))}
      </TreeNode>
    );
  };

  // فلترة الموظفين
  const filteredEmployees = employees.filter(emp => {
    const matchesSearch = !searchTerm ||
      emp.EmployeeName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      emp.EmployeeCode?.includes(searchTerm);

    const matchesDepartment = !selectedDepartment ||
      emp.Department === selectedDepartment;

    return matchesSearch && matchesDepartment;
  });

  return (
    <MainLayout>
      <div className="max-w-full mx-auto p-6">
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FiUsers className="text-3xl text-blue-600" />
              <div>
                <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  الهيكل التنظيمي الاحترافي
                </h1>
                <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                  عرض تفاعلي للهيكل الإداري للشركة مع تصميم احترافي
                </p>
              </div>
            </div>

            <div className="flex gap-3">
              <button
                onClick={fetchEmployees}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
              >
                <FiRefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                تحديث البيانات
              </button>

              <button
                onClick={() => setZoomLevel(prev => Math.min(prev + 0.1, 2))}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <FiZoomIn className="w-4 h-4" />
                تكبير
              </button>

              <button
                onClick={() => setZoomLevel(prev => Math.max(prev - 0.1, 0.5))}
                className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <FiZoomOut className="w-4 h-4" />
                تصغير
              </button>
            </div>
          </div>
        </div>

        {/* أدوات التحكم والفلاتر */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* البحث */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                البحث في الموظفين
              </label>
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="اسم الموظف أو الكود..."
                  className={`w-full pl-10 pr-4 py-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                />
              </div>
            </div>

            {/* فلتر القسم */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                القسم
              </label>
              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
              >
                <option value="">جميع الأقسام</option>
                {departments.map(dept => (
                  <option key={dept} value={dept}>{dept}</option>
                ))}
              </select>
            </div>

            {/* إحصائيات سريعة */}
            <div className="flex items-center justify-center">
              <div className="text-center">
                <div className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {filteredEmployees.length}
                </div>
                <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                  إجمالي الموظفين
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* الهيكل التنظيمي */}
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <FiRefreshCw className="w-8 h-8 animate-spin text-blue-500 mr-3" />
            <span className={`text-lg ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>جاري تحميل الهيكل التنظيمي...</span>
          </div>
        ) : error ? (
          <div className={`${isDarkMode ? 'bg-red-900 border-red-700' : 'bg-red-50 border-red-200'} border rounded-lg p-6 text-center`}>
            <div className={`text-lg font-semibold ${isDarkMode ? 'text-red-300' : 'text-red-800'} mb-2`}>
              خطأ في تحميل البيانات
            </div>
            <div className={`${isDarkMode ? 'text-red-400' : 'text-red-600'}`}>
              {error}
            </div>
            <button
              onClick={fetchEmployees}
              className="mt-4 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              إعادة المحاولة
            </button>
          </div>
        ) : organizationTree ? (
          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div
              className="overflow-auto"
              style={{
                transform: `scale(${zoomLevel})`,
                transformOrigin: 'top center',
                minHeight: '600px'
              }}
            >
              <Tree
                lineWidth="2px"
                lineColor={isDarkMode ? '#4b5563' : '#d1d5db'}
                lineBorderRadius="10px"
                label={<EmployeeNode employee={organizationTree} onClick={setSelectedEmployee} />}
              >
                {organizationTree.children && organizationTree.children.map(child => renderTree(child))}
              </Tree>
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <FiUsers className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <div className={`text-lg ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              لا توجد بيانات هيكل تنظيمي
            </div>
          </div>
        )}

        {/* مودال تفاصيل الموظف */}
        {selectedEmployee && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-xl border max-w-md w-full max-h-[90vh] overflow-y-auto`}>
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    تفاصيل الموظف
                  </h3>
                  <button
                    onClick={() => setSelectedEmployee(null)}
                    className={`text-gray-400 hover:text-gray-600 text-2xl`}
                  >
                    ×
                  </button>
                </div>

                <div className="text-center mb-6">
                  <div className={`
                    w-20 h-20 rounded-full bg-gradient-to-br ${levelColors[selectedEmployee.level]?.bg || levelColors[5].bg}
                    flex items-center justify-center text-white font-bold text-2xl
                    mx-auto mb-3 border-4 border-white shadow-lg
                  `}>
                    {selectedEmployee.EmployeeName
                      ? selectedEmployee.EmployeeName.split(' ').map(name => name[0]).join('').substring(0, 2)
                      : 'غ م'}
                  </div>

                  <h4 className={`text-lg font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-1`}>
                    {selectedEmployee.EmployeeName || 'غير محدد'}
                  </h4>

                  <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-600'} mb-2`}>
                    {selectedEmployee.JobTitle || 'غير محدد'}
                  </p>

                  <span className={`
                    inline-block px-3 py-1 rounded-full text-xs font-semibold
                    ${isDarkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-100 text-gray-700'}
                  `}>
                    كود: {selectedEmployee.EmployeeCode}
                  </span>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <FiMapPin className="text-blue-500" />
                    <div>
                      <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>القسم</div>
                      <div className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {selectedEmployee.Department || 'غير محدد'}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <FiAward className="text-green-500" />
                    <div>
                      <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>المستوى الإداري</div>
                      <div className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        المستوى {selectedEmployee.level}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <FiUsers className="text-purple-500" />
                    <div>
                      <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>عدد المرؤوسين</div>
                      <div className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {selectedEmployee.children?.length || 0} موظف
                      </div>
                    </div>
                  </div>

                  {selectedEmployee.DirectManager1 && (
                    <div className="flex items-center gap-3">
                      <FiUser className="text-orange-500" />
                      <div>
                        <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>المدير المباشر</div>
                        <div className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          {selectedEmployee.DirectManager1}
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                  <button
                    onClick={() => setSelectedEmployee(null)}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors"
                  >
                    إغلاق
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}