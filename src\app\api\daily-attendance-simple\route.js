import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

let pool;

export async function POST(request) {
  try {
    const body = await request.json();
    const { action } = body;

    pool = await getConnection();

    switch (action) {
      case 'list':
        return await getDailyAttendance(pool, body);
      case 'add':
        return await addAttendanceRecord(pool, body);
      case 'update':
        return await updateAttendanceRecord(pool, body);
      case 'delete':
        return await deleteAttendanceRecord(pool, body);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  }
}

// جلب سجلات التمام اليومي
async function getDailyAttendance(pool, data) {
  try {
    const { 
      date, 
      startDate, 
      endDate, 
      employeeCode, 
      department, 
      attendanceStatus,
      page = 1, 
      limit = 50 
    } = data;

    let whereClause = 'WHERE 1=1';
    const request = pool.request();

    if (date) {
      whereClause += ' AND AttendanceDate = @date';
      request.input('date', sql.Date, date);
    }

    if (startDate && endDate) {
      whereClause += ' AND AttendanceDate BETWEEN @startDate AND @endDate';
      request.input('startDate', sql.Date, startDate);
      request.input('endDate', sql.Date, endDate);
    }

    if (employeeCode) {
      whereClause += ' AND EmployeeCode LIKE @employeeCode';
      request.input('employeeCode', sql.NVarChar, `%${employeeCode}%`);
    }

    if (department) {
      whereClause += ' AND Department = @department';
      request.input('department', sql.NVarChar, department);
    }

    if (attendanceStatus) {
      whereClause += ' AND Attendance = @attendanceStatus';
      request.input('attendanceStatus', sql.NVarChar, attendanceStatus);
    }

    const offset = (page - 1) * limit;
    request.input('offset', sql.Int, offset);
    request.input('limit', sql.Int, limit);

    const query = `
      SELECT 
        ID,
        AttendanceDate,
        EmployeeCode,
        EmployeeName,
        ISNULL(JobTitle, '') as JobTitle,
        ISNULL(Department, '') as Department,
        ISNULL(CheckInTime, '') as CheckInTime,
        ISNULL(CheckOutTime, '') as CheckOutTime,
        ISNULL(Attendance, '') as AttendanceStatus,
        ISNULL(Notes, '') as Notes,
        ISNULL(IsFromRequest, 0) as IsFromRequest,
        ISNULL(RequestID, 0) as RequestID,
        FORMAT(CreatedAt, 'yyyy-MM-dd HH:mm:ss') as CreatedAt,
        FORMAT(UpdatedAt, 'yyyy-MM-dd HH:mm:ss') as UpdatedAt
      FROM DailyAttendance
      ${whereClause}
      ORDER BY AttendanceDate DESC, EmployeeCode
      OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY
    `;

    const result = await request.query(query);

    // جلب العدد الإجمالي
    const countQuery = `
      SELECT COUNT(*) as total
      FROM DailyAttendance
      ${whereClause}
    `;

    const countRequest = pool.request();
    if (date) countRequest.input('date', sql.Date, date);
    if (startDate && endDate) {
      countRequest.input('startDate', sql.Date, startDate);
      countRequest.input('endDate', sql.Date, endDate);
    }
    if (employeeCode) countRequest.input('employeeCode', sql.NVarChar, `%${employeeCode}%`);
    if (department) countRequest.input('department', sql.NVarChar, department);
    if (attendanceStatus) countRequest.input('attendanceStatus', sql.NVarChar, attendanceStatus);

    const countResult = await countRequest.query(countQuery);
    const total = countResult.recordset[0].total;

    return NextResponse.json({
      success: true,
      data: result.recordset,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب التمام اليومي: ' + error.message
    }, { status: 500 });
  }
}

// إضافة سجل تمام جديد
async function addAttendanceRecord(pool, data) {
  try {
    const {
      attendanceDate,
      employeeCode,
      employeeName,
      jobTitle,
      department,
      attendance,
      checkInTime,
      checkOutTime,
      notes
    } = data;

    const request = pool.request();
    request.input('attendanceDate', sql.Date, attendanceDate);
    request.input('employeeCode', sql.NVarChar, employeeCode);
    request.input('employeeName', sql.NVarChar, employeeName);
    request.input('jobTitle', sql.NVarChar, jobTitle);
    request.input('department', sql.NVarChar, department);
    request.input('attendance', sql.NVarChar, attendance);
    request.input('checkInTime', sql.NVarChar, checkInTime);
    request.input('checkOutTime', sql.NVarChar, checkOutTime);
    request.input('notes', sql.NVarChar, notes);

    const result = await request.query(`
      INSERT INTO DailyAttendance (
        AttendanceDate, EmployeeCode, EmployeeName, JobTitle, Department,
        Attendance, attendanceStatus, CheckInTime, CheckOutTime, Notes, IsFromRequest
      ) VALUES (
        @attendanceDate, @employeeCode, @employeeName, @jobTitle, @department,
        @attendance, @attendance, @checkInTime, @checkOutTime, @notes, 0
      )
    `);

    return NextResponse.json({
      success: true,
      message: 'تم إضافة سجل التمام بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في إضافة سجل التمام: ' + error.message
    }, { status: 500 });
  }
}

// تحديث سجل تمام
async function updateAttendanceRecord(pool, data) {
  try {
    const {
      id,
      attendance,
      checkInTime,
      checkOutTime,
      notes
    } = data;

    const request = pool.request();
    request.input('id', sql.Int, id);
    request.input('attendance', sql.NVarChar, attendance);
    request.input('checkInTime', sql.NVarChar, checkInTime);
    request.input('checkOutTime', sql.NVarChar, checkOutTime);
    request.input('notes', sql.NVarChar, notes);

    const result = await request.query(`
      UPDATE DailyAttendance 
      SET 
        Attendance = @attendance,
        CheckInTime = @checkInTime,
        CheckOutTime = @checkOutTime,
        Notes = @notes,
        UpdatedAt = GETDATE()
      WHERE ID = @id
    `);

    return NextResponse.json({
      success: true,
      message: 'تم تحديث سجل التمام بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث سجل التمام: ' + error.message
    }, { status: 500 });
  }
}

// حذف سجل تمام
async function deleteAttendanceRecord(pool, data) {
  try {
    const { id } = data;

    const request = pool.request();
    request.input('id', sql.Int, id);

    const result = await request.query(`
      DELETE FROM DailyAttendance WHERE ID = @id
    `);

    return NextResponse.json({
      success: true,
      message: 'تم حذف سجل التمام بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في حذف سجل التمام: ' + error.message
    }, { status: 500 });
  }
}
