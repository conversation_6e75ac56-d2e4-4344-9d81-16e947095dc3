import { NextResponse } from 'next/server';
import { getConnection } from '@/lib/db';
import sql from 'mssql';
import fs from 'fs';
import path from 'path';

export async function POST(request) {
  try {
    const { action, backupName, restoreFile } = await request.json();

    const pool = await getConnection();

    switch (action) {
      case 'createBackup':
        return await createDatabaseBackup(pool, backupName);
      case 'createDataBackup':
        return await createDataOnlyBackup(pool, backupName);
      case 'restoreBackup':
        return await restoreDatabaseBackup(pool, restoreFile);
      case 'listBackups':
        return await listAvailableBackups();
      case 'deleteBackup':
        return await deleteBackupFile(backupName);
      case 'exportData':
        return await exportDataToJson(pool);
      case 'importData':
        return await importDataFromJson(pool, request);
      case 'getBackupInfo':
        return await getBackupInformation(pool);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  }
}

// إنشاء نسخة احتياطية من قاعدة البيانات
async function createDatabaseBackup(pool, backupName) {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const finalBackupName = backupName || `HR_System_Backup_${timestamp}`;

    // مجلد النسخ الاحتياطية
    const backupDir = path.join(process.cwd(), 'backups');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    const backupPath = path.join(backupDir, `${finalBackupName}.bak`);

    // التحقق من وجود قاعدة البيانات والصلاحيات
    const dbCheck = await pool.request().query(`
      SELECT name FROM sys.databases WHERE name = '${pool.config.database}'
    `);

    if (dbCheck.recordset.length === 0) {
      throw new Error('قاعدة البيانات غير موجودة');
    }

    // تنفيذ أمر النسخ الاحتياطي مع معالجة أفضل للأخطاء
    const backupQuery = `
      BACKUP DATABASE [${pool.config.database}]
      TO DISK = N'${backupPath.replace(/\\/g, '\\\\')}'
      WITH FORMAT, INIT,
      NAME = N'${finalBackupName}',
      DESCRIPTION = N'نسخة احتياطية تلقائية لنظام الموارد البشرية',
      SKIP, NOREWIND, NOUNLOAD, STATS = 10
    `;

    await pool.request().query(backupQuery);

    // تسجيل النسخة الاحتياطية في السجل
    await logBackupOperation(pool, 'backup', finalBackupName, backupPath, 'success');

    return NextResponse.json({
      success: true,
      message: 'تم إنشاء النسخة الاحتياطية بنجاح',
      backupName: finalBackupName,
      backupPath: backupPath,
      size: fs.existsSync(backupPath) ? fs.statSync(backupPath).size : 0
    });

  } catch (error) {

    await logBackupOperation(pool, 'backup', backupName, '', 'failed', error.message);

    // في حالة فشل النسخ الاحتياطي التقليدي، جرب النسخ البديل

    try {
      return await createDataOnlyBackup(pool, finalBackupName);
    } catch (fallbackError) {

      return NextResponse.json({
        success: false,
        error: 'فشل في إنشاء النسخة الاحتياطية: ' + error.message + '. النسخ البديل فشل أيضاً: ' + fallbackError.message
      }, { status: 500 });
    }
  }
}

// نسخ احتياطي بديل يعتمد على تصدير البيانات
async function createDataOnlyBackup(pool, backupName) {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const finalBackupName = backupName || `Data_Backup_${timestamp}`;

    console.log('🔄 بدء النسخ الاحتياطي البديل (تصدير البيانات)...');

    // مجلد النسخ الاحتياطية
    const backupDir = path.join(process.cwd(), 'backups');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    const tables = [
      'Employees', 'PaperRequests', 'DailyAttendance', 'LeaveBalances',
      'ApartmentBeneficiaries', 'CarBeneficiaries', 'SmartNotifications',
      'SystemAlerts', 'BackupLog'
    ];

    const backupData = {
      metadata: {
        backupName: finalBackupName,
        backupDate: new Date().toISOString(),
        backupType: 'data_only',
        database: pool.config.database,
        version: '1.0'
      },
      tables: {}
    };

    let totalRecords = 0;

    for (const table of tables) {
      try {
        const result = await pool.request().query(`SELECT * FROM ${table}`);
        backupData.tables[table] = result.recordset;
        totalRecords += result.recordset.length;

      } catch (tableError) {

        backupData.tables[table] = [];
      }
    }

    const backupPath = path.join(backupDir, `${finalBackupName}.json`);
    fs.writeFileSync(backupPath, JSON.stringify(backupData, null, 2));

    // تسجيل النسخة الاحتياطية في السجل
    await logBackupOperation(pool, 'data_backup', finalBackupName, backupPath, 'success');

    return NextResponse.json({
      success: true,
      message: 'تم إنشاء النسخة الاحتياطية البديلة بنجاح (تصدير البيانات)',
      backupName: finalBackupName,
      backupPath: backupPath,
      backupType: 'data_only',
      totalRecords: totalRecords,
      tablesBackedUp: Object.keys(backupData.tables).length,
      size: fs.existsSync(backupPath) ? fs.statSync(backupPath).size : 0
    });

  } catch (error) {

    await logBackupOperation(pool, 'data_backup', backupName, '', 'failed', error.message);

    return NextResponse.json({
      success: false,
      error: 'فشل في النسخ الاحتياطي البديل: ' + error.message
    }, { status: 500 });
  }
}

// استرداد قاعدة البيانات من نسخة احتياطية
async function restoreDatabaseBackup(pool, restoreFile) {
  try {
    const backupDir = path.join(process.cwd(), 'backups');
    const backupPath = path.join(backupDir, restoreFile);

    if (!fs.existsSync(backupPath)) {
      return NextResponse.json({
        success: false,
        error: 'ملف النسخة الاحتياطية غير موجود'
      }, { status: 404 });
    }

    // إغلاق جميع الاتصالات النشطة
    await pool.request().query(`
      ALTER DATABASE [${pool.config.database}] SET SINGLE_USER WITH ROLLBACK IMMEDIATE
    `);

    // تنفيذ أمر الاسترداد
    await pool.request()
      .input('backupPath', sql.NVarChar, backupPath)
      .input('dbName', sql.NVarChar, pool.config.database)
      .query(`
        RESTORE DATABASE @dbName 
        FROM DISK = @backupPath
        WITH REPLACE, RECOVERY
      `);

    // إعادة تفعيل الاتصالات المتعددة
    await pool.request().query(`
      ALTER DATABASE [${pool.config.database}] SET MULTI_USER
    `);

    // تسجيل عملية الاسترداد
    await logBackupOperation(pool, 'restore', restoreFile, backupPath, 'success');

    return NextResponse.json({
      success: true,
      message: 'تم استرداد قاعدة البيانات بنجاح',
      restoredFrom: restoreFile
    });

  } catch (error) {

    // محاولة إعادة تفعيل الاتصالات في حالة الخطأ
    try {
      await pool.request().query(`
        ALTER DATABASE [${pool.config.database}] SET MULTI_USER
      `);
    } catch (resetError) {

    }

    await logBackupOperation(pool, 'restore', restoreFile, '', 'failed', error.message);
    
    return NextResponse.json({
      success: false,
      error: 'فشل في استرداد قاعدة البيانات: ' + error.message
    }, { status: 500 });
  }
}

// جلب قائمة النسخ الاحتياطية المتاحة
async function listAvailableBackups() {
  try {
    const backupDir = path.join(process.cwd(), 'backups');
    
    if (!fs.existsSync(backupDir)) {
      return NextResponse.json({
        success: true,
        backups: []
      });
    }

    const files = fs.readdirSync(backupDir)
      .filter(file => file.endsWith('.bak'))
      .map(file => {
        const filePath = path.join(backupDir, file);
        const stats = fs.statSync(filePath);
        
        return {
          name: file,
          size: stats.size,
          created: stats.birthtime,
          modified: stats.mtime,
          sizeFormatted: formatFileSize(stats.size)
        };
      })
      .sort((a, b) => new Date(b.created) - new Date(a.created));

    return NextResponse.json({
      success: true,
      backups: files
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب قائمة النسخ الاحتياطية: ' + error.message
    }, { status: 500 });
  }
}

// حذف ملف نسخة احتياطية
async function deleteBackupFile(backupName) {
  try {
    const backupDir = path.join(process.cwd(), 'backups');
    const backupPath = path.join(backupDir, backupName);

    if (!fs.existsSync(backupPath)) {
      return NextResponse.json({
        success: false,
        error: 'ملف النسخة الاحتياطية غير موجود'
      }, { status: 404 });
    }

    fs.unlinkSync(backupPath);

    return NextResponse.json({
      success: true,
      message: 'تم حذف النسخة الاحتياطية بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في حذف النسخة الاحتياطية: ' + error.message
    }, { status: 500 });
  }
}

// تصدير البيانات إلى JSON
async function exportDataToJson(pool) {
  try {

    const tables = [
      'Employees', 'PaperRequests', 'DailyAttendance', 'LeaveBalances',
      'ApartmentBeneficiaries', 'CarBeneficiaries', 'SmartNotifications',
      'SystemAlerts'
    ];

    const exportData = {};
    
    for (const table of tables) {
      try {
        const result = await pool.request().query(`SELECT * FROM ${table}`);
        exportData[table] = result.recordset;

      } catch (tableError) {

        exportData[table] = [];
      }
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const exportDir = path.join(process.cwd(), 'exports');
    if (!fs.existsSync(exportDir)) {
      fs.mkdirSync(exportDir, { recursive: true });
    }

    const exportPath = path.join(exportDir, `HR_Data_Export_${timestamp}.json`);
    fs.writeFileSync(exportPath, JSON.stringify(exportData, null, 2));

    return NextResponse.json({
      success: true,
      message: 'تم تصدير البيانات بنجاح',
      exportPath: exportPath,
      tablesExported: Object.keys(exportData).length
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في تصدير البيانات: ' + error.message
    }, { status: 500 });
  }
}

// تسجيل عمليات النسخ الاحتياطي
async function logBackupOperation(pool, operation, backupName, filePath, status, errorMessage = null) {
  try {
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='BackupLog' AND xtype='U')
      BEGIN
        CREATE TABLE BackupLog (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          Operation NVARCHAR(50) NOT NULL,
          BackupName NVARCHAR(255) NOT NULL,
          FilePath NVARCHAR(500),
          Status NVARCHAR(20) NOT NULL,
          ErrorMessage NVARCHAR(MAX),
          CreatedAt DATETIME DEFAULT GETDATE(),
          CreatedBy NVARCHAR(100) DEFAULT 'System'
        )
      END
    `);

    await pool.request()
      .input('operation', sql.NVarChar, operation)
      .input('backupName', sql.NVarChar, backupName)
      .input('filePath', sql.NVarChar, filePath)
      .input('status', sql.NVarChar, status)
      .input('errorMessage', sql.NVarChar, errorMessage)
      .query(`
        INSERT INTO BackupLog (Operation, BackupName, FilePath, Status, ErrorMessage)
        VALUES (@operation, @backupName, @filePath, @status, @errorMessage)
      `);

  } catch (error) {

  }
}

// معلومات النسخ الاحتياطي
async function getBackupInformation(pool) {
  try {
    // إحصائيات قاعدة البيانات
    const dbStats = await pool.request().query(`
      SELECT 
        DB_NAME() as DatabaseName,
        (SELECT COUNT(*) FROM Employees) as TotalEmployees,
        (SELECT COUNT(*) FROM PaperRequests) as TotalRequests,
        (SELECT COUNT(*) FROM DailyAttendance) as TotalAttendanceRecords,
        GETDATE() as CurrentTime
    `);

    // آخر نسخة احتياطية
    const lastBackup = await pool.request().query(`
      IF EXISTS (SELECT * FROM sysobjects WHERE name='BackupLog' AND xtype='U')
      BEGIN
        SELECT TOP 1 * FROM BackupLog 
        WHERE Operation = 'backup' AND Status = 'success'
        ORDER BY CreatedAt DESC
      END
    `);

    return NextResponse.json({
      success: true,
      databaseInfo: dbStats.recordset[0],
      lastBackup: lastBackup.recordset[0] || null
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب معلومات النسخ الاحتياطي: ' + error.message
    }, { status: 500 });
  }
}

// تنسيق حجم الملف
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
