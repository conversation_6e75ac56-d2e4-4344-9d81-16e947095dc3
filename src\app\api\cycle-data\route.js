import { NextResponse } from 'next/server';
import { getConnection } from '@/utils/db';
import { 
  CYCLE_TYPES, 
  getCycleSQLFilter, 
  getCurrentCycle,
  parseCycleFilter,
  calculateCycleDates
} from '@/utils/dateFilters';

export async function POST(request) {
  try {
    const body = await request.json();
    const { cycleType, cycleFilter, dataType } = body;

    // التحقق من صحة المعاملات
    if (!Object.values(CYCLE_TYPES).includes(cycleType)) {
      return NextResponse.json({
        success: false,
        error: 'نوع دورة غير صحيح'
      }, { status: 400 });
    }

    const pool = await getConnection();

    // تحديد الفترة الزمنية
    let month, year;
    if (cycleFilter && cycleFilter !== 'current') {
      const parsed = parseCycleFilter(cycleFilter);
      month = parsed.month;
      year = parsed.year;
    } else {
      // استخدام الدورة الحالية
      const currentCycle = getCurrentCycle(cycleType);
      const currentDate = new Date();
      month = currentDate.getMonth() + 1;
      year = currentDate.getFullYear();
    }

    // حساب تواريخ الدورة
    const cycleInfo = calculateCycleDates(cycleType, month, year);

    let result = {};

    switch (dataType) {
      case 'attendance':
        result = await getAttendanceData(pool, cycleType, month, year);
        break;
      case 'monthly_effects':
        result = await getMonthlyEffectsData(pool, cycleType, month, year);
        break;
      case 'temp_workers':
        result = await getTempWorkersData(pool, cycleType, month, year);
        break;
      case 'costs':
        result = await getCostsData(pool, cycleType, month, year);
        break;
      case 'leaves':
        result = await getLeavesData(pool, cycleType, month, year);
        break;
      default:
        return NextResponse.json({
          success: false,
          error: 'نوع بيانات غير مدعوم'
        }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      data: {
        ...result,
        cycleInfo: {
          type: cycleType,
          month,
          year,
          displayName: cycleInfo.displayName,
          description: cycleInfo.description,
          startDate: cycleInfo.startDate,
          endDate: cycleInfo.endDate
        }
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب البيانات: ' + error.message
    }, { status: 500 });
  }
}

// جلب بيانات الحضور
async function getAttendanceData(pool, cycleType, month, year) {
  const filter = getCycleSQLFilter(cycleType, month, year, 'AttendanceDate');
  
  try {
    // إحصائيات الحضور اليومي
    const dailyStats = await pool.request().query(`
      SELECT 
        COUNT(*) as totalRecords,
        COUNT(DISTINCT EmployeeCode) as uniqueEmployees,
        COUNT(CASE WHEN MovementType = N'دخول' THEN 1 END) as entriesCount,
        COUNT(CASE WHEN MovementType = N'خروج' THEN 1 END) as exitsCount
      FROM DailyAttendance
      WHERE ${filter.whereClause}
    `);

    // الحضور حسب اليوم
    const dailyBreakdown = await pool.request().query(`
      SELECT 
        CAST(AttendanceDate AS DATE) as date,
        COUNT(*) as recordsCount,
        COUNT(DISTINCT EmployeeCode) as employeesCount
      FROM DailyAttendance
      WHERE ${filter.whereClause}
      GROUP BY CAST(AttendanceDate AS DATE)
      ORDER BY date
    `);

    // الموظفين الأكثر حضوراً
    const topEmployees = await pool.request().query(`
      SELECT TOP 10
        da.EmployeeCode,
        da.EmployeeName,
        COUNT(*) as attendanceCount,
        COUNT(DISTINCT CAST(AttendanceDate AS DATE)) as daysPresent
      FROM DailyAttendance da
      WHERE ${filter.whereClause}
      GROUP BY da.EmployeeCode, da.EmployeeName
      ORDER BY attendanceCount DESC
    `);

    return {
      dailyStats: dailyStats.recordset[0],
      dailyBreakdown: dailyBreakdown.recordset,
      topEmployees: topEmployees.recordset
    };
  } catch (error) {

    return {
      dailyStats: { totalRecords: 0, uniqueEmployees: 0, entriesCount: 0, exitsCount: 0 },
      dailyBreakdown: [],
      topEmployees: []
    };
  }
}

// جلب بيانات المؤثرات الشهرية
async function getMonthlyEffectsData(pool, cycleType, month, year) {
  try {
    // إحصائيات المؤثرات
    const effectsStats = await pool.request().query(`
      SELECT 
        EffectType,
        COUNT(*) as recordsCount,
        SUM(EffectValue) as totalValue,
        SUM(EffectDays) as totalDays,
        COUNT(DISTINCT EmployeeCode) as affectedEmployees
      FROM MonthlyEffects
      WHERE EffectMonth = ${month} AND EffectYear = ${year}
      GROUP BY EffectType
      ORDER BY totalValue DESC
    `);

    // الموظفين الأكثر تأثراً
    const topAffectedEmployees = await pool.request().query(`
      SELECT TOP 10
        EmployeeCode,
        EmployeeName,
        COUNT(*) as effectsCount,
        SUM(EffectValue) as totalEffectValue,
        SUM(EffectDays) as totalEffectDays
      FROM MonthlyEffects
      WHERE EffectMonth = ${month} AND EffectYear = ${year}
      GROUP BY EmployeeCode, EmployeeName
      ORDER BY totalEffectValue DESC
    `);

    return {
      effectsStats: effectsStats.recordset,
      topAffectedEmployees: topAffectedEmployees.recordset
    };
  } catch (error) {

    return {
      effectsStats: [],
      topAffectedEmployees: []
    };
  }
}

// جلب بيانات العمالة المؤقتة
async function getTempWorkersData(pool, cycleType, month, year) {
  const filter = getCycleSQLFilter(cycleType, month, year, 'CreatedAt');
  
  try {
    // إحصائيات العمالة المؤقتة
    const tempStats = await pool.request().query(`
      SELECT 
        COUNT(*) as totalWorkers,
        COUNT(CASE WHEN WorkerType = N'خدمي' THEN 1 END) as serviceWorkers,
        COUNT(CASE WHEN WorkerType = N'إنتاجي' THEN 1 END) as productionWorkers,
        COUNT(CASE WHEN IsActive = 1 THEN 1 END) as activeWorkers,
        AVG(DailyRate) as averageDailyRate,
        SUM(DailyRate) as totalDailyCost
      FROM TemporaryWorkers
      WHERE ${filter.whereClause}
    `);

    // العمالة حسب النوع
    const workersByType = await pool.request().query(`
      SELECT 
        WorkerType,
        COUNT(*) as count,
        AVG(DailyRate) as averageRate,
        SUM(DailyRate) as totalCost
      FROM TemporaryWorkers
      WHERE ${filter.whereClause}
      GROUP BY WorkerType
    `);

    return {
      tempStats: tempStats.recordset[0],
      workersByType: workersByType.recordset
    };
  } catch (error) {

    return {
      tempStats: { totalWorkers: 0, serviceWorkers: 0, productionWorkers: 0, activeWorkers: 0, averageDailyRate: 0, totalDailyCost: 0 },
      workersByType: []
    };
  }
}

// جلب بيانات التكاليف
async function getCostsData(pool, cycleType, month, year) {
  try {
    // إحصائيات التكاليف
    const costsStats = await pool.request().query(`
      SELECT 
        CostType,
        SUM(TotalCost) as totalCost,
        SUM(ItemCount) as totalItems,
        COUNT(*) as recordsCount,
        AVG(TotalCost) as averageCost
      FROM Costs
      WHERE CostMonth = ${month} AND CostYear = ${year}
      GROUP BY CostType
      ORDER BY totalCost DESC
    `);

    // إجمالي التكاليف
    const totalCosts = await pool.request().query(`
      SELECT 
        SUM(TotalCost) as grandTotal,
        SUM(ItemCount) as grandTotalItems,
        COUNT(DISTINCT CostType) as costTypes
      FROM Costs
      WHERE CostMonth = ${month} AND CostYear = ${year}
    `);

    return {
      costsStats: costsStats.recordset,
      totalCosts: totalCosts.recordset[0]
    };
  } catch (error) {

    return {
      costsStats: [],
      totalCosts: { grandTotal: 0, grandTotalItems: 0, costTypes: 0 }
    };
  }
}

// جلب بيانات الإجازات
async function getLeavesData(pool, cycleType, month, year) {
  const filter = getCycleSQLFilter(cycleType, month, year, 'CreatedAt');
  
  try {
    // إحصائيات الإجازات
    const leavesStats = await pool.request().query(`
      SELECT 
        LeaveType,
        COUNT(*) as requestsCount,
        SUM(TotalDays) as totalDays,
        COUNT(CASE WHEN RequestStatus = N'موافق' THEN 1 END) as approvedCount,
        COUNT(CASE WHEN RequestStatus = N'مرفوض' THEN 1 END) as rejectedCount,
        COUNT(CASE WHEN RequestStatus = N'قيد المراجعة' THEN 1 END) as pendingCount
      FROM LeaveRequests
      WHERE ${filter.whereClause}
      GROUP BY LeaveType
      ORDER BY requestsCount DESC
    `);

    // الموظفين الأكثر طلباً للإجازات
    const topLeaveRequesters = await pool.request().query(`
      SELECT TOP 10
        EmployeeCode,
        EmployeeName,
        COUNT(*) as requestsCount,
        SUM(TotalDays) as totalDaysRequested,
        COUNT(CASE WHEN RequestStatus = N'موافق' THEN 1 END) as approvedRequests
      FROM LeaveRequests
      WHERE ${filter.whereClause}
      GROUP BY EmployeeCode, EmployeeName
      ORDER BY requestsCount DESC
    `);

    return {
      leavesStats: leavesStats.recordset,
      topLeaveRequesters: topLeaveRequesters.recordset
    };
  } catch (error) {

    return {
      leavesStats: [],
      topLeaveRequesters: []
    };
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Cycle Data API is working',
    supportedCycles: Object.values(CYCLE_TYPES),
    supportedDataTypes: ['attendance', 'monthly_effects', 'temp_workers', 'costs', 'leaves'],
    timestamp: new Date().toISOString()
  });
}
