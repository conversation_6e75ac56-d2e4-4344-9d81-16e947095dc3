'use client';

import React, { useState, useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { Calendar } from 'lucide-react';

const DateInput = ({
  id,
  name,
  value,
  onChange,
  required = false,
  className = '',
  label = '',
  placeholder = 'dd/mm/yyyy'
}) => {
  const { isDarkMode } = useTheme();
  const [displayValue, setDisplayValue] = useState('');

  // تحويل من YYYY-MM-DD إلى DD/MM/YYYY للعرض
  const formatForDisplay = (dateValue) => {
    if (!dateValue) return '';
    try {
      const date = new Date(dateValue);
      if (isNaN(date.getTime())) return '';

      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();

      return `${day}/${month}/${year}`;
    } catch (error) {
      return '';
    }
  };

  // تحويل من DD/MM/YYYY إلى YYYY-MM-DD للحفظ
  const formatForSave = (displayValue) => {
    if (!displayValue || displayValue.length < 8) return '';

    const parts = displayValue.split('/');
    if (parts.length !== 3) return '';

    const [day, month, year] = parts;
    if (!day || !month || !year) return '';

    // التحقق من صحة التاريخ
    const dayNum = parseInt(day, 10);
    const monthNum = parseInt(month, 10);
    const yearNum = parseInt(year, 10);

    if (isNaN(dayNum) || isNaN(monthNum) || isNaN(yearNum)) return '';
    if (dayNum < 1 || dayNum > 31 || monthNum < 1 || monthNum > 12 || yearNum < 1900 || yearNum > 2100) {
      return '';
    }

    // التحقق من صحة التاريخ باستخدام Date object
    const testDate = new Date(yearNum, monthNum - 1, dayNum);
    if (testDate.getFullYear() !== yearNum || testDate.getMonth() !== monthNum - 1 || testDate.getDate() !== dayNum) {
      return '';
    }

    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  };

  // معالجة إدخال النص
  const handleTextInput = (e) => {
    let inputValue = e.target.value;
    // إزالة أي أحرف غير رقمية أو /
    inputValue = inputValue.replace(/[^\d/]/g, '');

    // إضافة / تلقائياً
    if (inputValue.length === 2 && !inputValue.includes('/')) {
      inputValue += '/';
    } else if (inputValue.length === 5 && inputValue.split('/').length === 2) {
      inputValue += '/';
    }

    // منع الإدخال الزائد
    if (inputValue.length > 10) {
      inputValue = inputValue.substring(0, 10);
    }
    setDisplayValue(inputValue);

    // تحديث القيمة فقط إذا كان التاريخ فارغ
    if (inputValue === '') {
      onChange({
        target: {
          name: name,
          value: ''
        }
      });
    }
    // لا نحدث القيمة أثناء الكتابة، فقط عند فقدان التركيز
  };

  // معالجة فقدان التركيز - للتحقق من التاريخ المكتمل
  const handleBlur = () => {
    if (displayValue && displayValue.length >= 8) {
      const formattedValue = formatForSave(displayValue);
      if (formattedValue) {
        onChange({
          target: {
            name: name,
            value: formattedValue
          }
        });
      } else if (displayValue.length === 10) {
        // إذا كان التاريخ مكتمل لكن غير صحيح، اعرض رسالة
        alert('تاريخ غير صحيح. يرجى استخدام تنسيق DD/MM/YYYY');
        setDisplayValue('');
        onChange({
          target: {
            name: name,
            value: ''
          }
        });
      }
    }
  };

  // معالجة تغيير date picker
  const handleDatePickerChange = (e) => {
    const dateValue = e.target.value;
    onChange({
      target: {
        name: name,
        value: dateValue
      }
    });
  };

  // تحديث العرض عند تغيير القيمة - فقط عند التحميل الأولي
  useEffect(() => {
    // فقط إذا كان displayValue فارغ وvalue موجود، أو إذا كان value فارغ
    if (value && !displayValue) {
      setDisplayValue(formatForDisplay(value));
    } else if (!value && displayValue) {
      // إذا تم مسح value من الخارج، امسح displayValue أيضاً
      setDisplayValue('');
    }
  }, [value]);

  // فتح date picker بطريقة متوافقة
  const openDatePicker = () => {
    const hiddenInput = document.getElementById(`${id}_hidden`);
    if (hiddenInput) {
      try {
        // محاولة استخدام showPicker إذا كان متاحاً
        if (hiddenInput.showPicker) {
          hiddenInput.showPicker();
        } else {
          // البديل: تركيز وضغط على الحقل
          hiddenInput.focus();
          hiddenInput.click();
        }
      } catch (error) {
        // في حالة فشل showPicker، استخدم الطريقة التقليدية
        hiddenInput.focus();
        hiddenInput.click();
      }
    }
  };

  return (
    <div className="relative">
      {/* تسمية الحقل */}
      {label && (
        <label htmlFor={id} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {label}
        </label>
      )}

      {/* حقل النص للإدخال اليدوي */}
      <input
        id={id}
        name={`${name}_display`}
        type="text"
        value={displayValue}
        onChange={handleTextInput}
        onBlur={handleBlur}
        placeholder="يوم/شهر/سنة"
        className={`
          w-full px-4 py-3 pr-12 border-2 rounded-lg
          text-lg font-medium text-center
          transition-all duration-200
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50
          ${isDarkMode
            ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-blue-400'
            : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500'
          }
          ${className}
        `}
        dir="ltr"
      />

      {/* date picker مخفي */}
      <input
        id={`${id}_hidden`}
        type="date"
        value={value || ''}
        onChange={handleDatePickerChange}
        required={required}
        tabIndex={-1}
        className="absolute opacity-0 pointer-events-none"
        style={{ left: '-9999px', visibility: 'hidden', position: 'absolute' }}
        placeholder=""
      />

      {/* أيقونة التقويم للفتح */}
      <button
        type="button"
        onClick={openDatePicker}
        className={`absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors ${
          isDarkMode ? 'text-gray-400 hover:text-gray-300' : 'text-gray-500 hover:text-gray-700'
        }`}
      >
        <Calendar className="h-5 w-5" />
      </button>

      {/* عرض التنسيق المطلوب */}
      <div className={`absolute left-3 top-1/2 transform -translate-y-1/2 text-xs ${
        isDarkMode ? 'text-gray-500' : 'text-gray-400'
      }`}>
        يوم/شهر/سنة
      </div>
    </div>
  );
};

export default DateInput;
