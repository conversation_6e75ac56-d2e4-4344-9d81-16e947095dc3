#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);

// قائمة الواردات الشائعة التي قد تكون غير مستخدمة
const COMMON_UNUSED_IMPORTS = [
  'useState',
  'useEffect',
  'useCallback',
  'useMemo',
  'useRef',
  'useContext',
  'useReducer',
  'Fragment',
  'Component',
  'PureComponent',
  'memo',
  'forwardRef',
  'createContext',
  'lazy',
  'Suspense'
];

// مجلدات للتجاهل
const IGNORE_DIRS = [
  'node_modules',
  '.next',
  '.git',
  'build',
  'dist',
  'out',
  'coverage',
  '.vercel',
  'public',
  'archiv',
  'backups',
  'exports',
  'Augment-free',
  'oje',
  'ojesta',
  'templates'
];

// دالة لتحليل الواردات في ملف
function analyzeImports(content, filePath) {
  const issues = [];
  const lines = content.split('\n');
  
  // البحث عن الواردات
  const importLines = lines.filter((line, index) => {
    const trimmed = line.trim();
    return trimmed.startsWith('import ') && !trimmed.startsWith('import \'') && !trimmed.startsWith('import "');
  });
  
  importLines.forEach((importLine, index) => {
    // فحص الواردات المتعددة في سطر واحد
    if (importLine.includes('{') && importLine.includes('}')) {
      const match = importLine.match(/import\s*{\s*([^}]+)\s*}\s*from/);
      if (match) {
        const imports = match[1].split(',').map(imp => imp.trim());
        
        imports.forEach(imp => {
          // إزالة alias إذا وجد
          const cleanImport = imp.split(' as ')[0].trim();
          
          // فحص إذا كان الاستيراد مستخدم في الملف
          const isUsed = content.includes(cleanImport) && 
                        content.split(cleanImport).length > 2; // أكثر من مرة واحدة (الاستيراد نفسه)
          
          if (!isUsed) {
            issues.push({
              type: 'unused_import',
              import: cleanImport,
              line: importLine,
              file: filePath,
              suggestion: `Remove unused import: ${cleanImport}`
            });
          }
        });
      }
    }
    
    // فحص الواردات الافتراضية
    const defaultMatch = importLine.match(/import\s+(\w+)\s+from/);
    if (defaultMatch) {
      const defaultImport = defaultMatch[1];
      const isUsed = content.includes(defaultImport) && 
                    content.split(defaultImport).length > 2;
      
      if (!isUsed) {
        issues.push({
          type: 'unused_default_import',
          import: defaultImport,
          line: importLine,
          file: filePath,
          suggestion: `Remove unused default import: ${defaultImport}`
        });
      }
    }
  });
  
  // فحص console.log المتبقية
  lines.forEach((line, index) => {
    if (line.includes('console.log') || line.includes('console.warn') || line.includes('console.error')) {
      issues.push({
        type: 'console_statement',
        line: line.trim(),
        lineNumber: index + 1,
        file: filePath,
        suggestion: 'Remove console statement'
      });
    }
  });
  
  // فحص التعليقات المؤقتة
  lines.forEach((line, index) => {
    const trimmed = line.trim();
    if (trimmed.startsWith('// TODO:') || 
        trimmed.startsWith('// FIXME:') || 
        trimmed.startsWith('// DEBUG:') ||
        trimmed.startsWith('// TEMP:')) {
      issues.push({
        type: 'temp_comment',
        line: trimmed,
        lineNumber: index + 1,
        file: filePath,
        suggestion: 'Remove temporary comment'
      });
    }
  });
  
  return issues;
}

// دالة لتحليل ملفات JavaScript/TypeScript
async function analyzeFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return analyzeImports(content, filePath);
  } catch (error) {

    return [];
  }
}

// دالة للبحث في المجلدات
async function analyzeDirectory(dir = '.') {
  const allIssues = [];
  
  try {
    const items = await readdir(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stats = await stat(fullPath);
      
      if (stats.isDirectory()) {
        if (!IGNORE_DIRS.includes(item) && !item.startsWith('.')) {
          const subIssues = await analyzeDirectory(fullPath);
          allIssues.push(...subIssues);
        }
      } else if (stats.isFile()) {
        if (item.endsWith('.js') || item.endsWith('.jsx') || 
            item.endsWith('.ts') || item.endsWith('.tsx')) {
          const issues = await analyzeFile(fullPath);
          allIssues.push(...issues);
        }
      }
    }
  } catch (error) {

  }
  
  return allIssues;
}

// دالة لتجميع وعرض النتائج
function displayResults(issues) {
  const grouped = {
    unused_import: [],
    unused_default_import: [],
    console_statement: [],
    temp_comment: []
  };
  
  issues.forEach(issue => {
    if (grouped[issue.type]) {
      grouped[issue.type].push(issue);
    }
  });

  // الواردات غير المستخدمة
  if (grouped.unused_import.length > 0) {
    console.log(`🔍 الواردات غير المستخدمة (${grouped.unused_import.length}):`);
    grouped.unused_import.forEach(issue => {

    });
  }
  
  // الواردات الافتراضية غير المستخدمة
  if (grouped.unused_default_import.length > 0) {
    console.log(`🔍 الواردات الافتراضية غير المستخدمة (${grouped.unused_default_import.length}):`);
    grouped.unused_default_import.forEach(issue => {

    });
  }
  
  // عبارات console
  if (grouped.console_statement.length > 0) {
    console.log(`🔍 عبارات Console المتبقية (${grouped.console_statement.length}):`);
    grouped.console_statement.forEach(issue => {

    });
  }
  
  // التعليقات المؤقتة
  if (grouped.temp_comment.length > 0) {
    console.log(`🔍 التعليقات المؤقتة (${grouped.temp_comment.length}):`);
    grouped.temp_comment.forEach(issue => {

    });
  }
  
  // الملخص
  const totalIssues = Object.values(grouped).reduce((sum, arr) => sum + arr.length, 0);

  return grouped;
}

// الدالة الرئيسية
async function main() {

  const issues = await analyzeDirectory();
  const results = displayResults(issues);

  // حفظ النتائج في ملف
  const reportPath = 'code-analysis-report.json';
  fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));

}

// تشغيل السكريبت
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { analyzeDirectory, analyzeImports };
