'use client';
import React, { useState } from 'react';

function MainComponent() {
  const [selectedLang, setSelectedLang] = useState('ar');
  const [monthYear, setMonthYear] = useState('');
  const [costs, setCosts] = useState({
    apartments: [],
    vehicles: [],
    workers: [],
    loading: true,
    error: null,
  });

  const dir = selectedLang === 'ar' ? 'rtl' : 'ltr';

  useEffect(() => {
    const fetchMonthlyCosts = async () => {
      try {
        const response = await fetch('/api/data-service', {
          method: 'POST',
          body: JSON.stringify({
            action: 'get_monthly_costs',
            month_year: monthYear || new Date().toISOString().slice(0, 7),
          }),
        });

        if (!response.ok) {
          throw new Error(
            selectedLang === 'ar'
              ? 'حدث خطأ أثناء جلب البيانات'
              : 'Error fetching data'
          );
        }

        const data = await response.json();
        setCosts({
          apartments: data.apartments || [],
          vehicles: data.vehicles || [],
          workers: data.workers || [],
          loading: false,
          error: null,
        });
      } catch (error) {
        setCosts((prev) => ({
          ...prev,
          loading: false,
          error: error.message,
        }));
      }
    };

    fetchMonthlyCosts();
  }, [monthYear, selectedLang]);

  const calculateTotal = (items) => {
    return items.reduce((sum, item) => sum + (Number(item.cost) || 0), 0);
  };

  const grandTotal =
    calculateTotal(costs.apartments) +
    calculateTotal(costs.vehicles) +
    calculateTotal(costs.workers);

  return (
    <div
      dir={dir}
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {selectedLang === 'ar'
              ? 'التكاليف غير المباشرة الشهرية'
              : 'Monthly Indirect Costs'}
          </h1>
          <button
            onClick={() => setSelectedLang(selectedLang === 'ar' ? 'en' : 'ar')}
            className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
          >
            {selectedLang === 'ar' ? 'English' : 'العربية'}
          </button>
        </div>

        <div className="mb-6">
          <input
            type="month"
            value={monthYear}
            onChange={(e) => setMonthYear(e.target.value)}
            className="p-2 border border-gray-300 rounded-md"
          />
        </div>

        {costs.error && (
          <div className="mb-6 p-4 bg-red-100 text-red-700 rounded-md">
            {costs.error}
          </div>
        )}

        {costs.loading ? (
          <div className="text-center py-8">
            <i className="fas fa-spinner fa-spin text-4xl text-gray-400"></i>
          </div>
        ) : (
          <div className="space-y-8">
            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
                {selectedLang === 'ar'
                  ? 'تكاليف الشقق المؤجرة'
                  : 'Rented Apartments Costs'}
              </h2>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b dark:border-gray-700">
                      <th className="text-right py-3 px-4">
                        {selectedLang === 'ar' ? 'رمز الشقة' : 'Apartment Code'}
                      </th>
                      <th className="text-right py-3 px-4">
                        {selectedLang === 'ar' ? 'الموقع' : 'Location'}
                      </th>
                      <th className="text-right py-3 px-4">
                        {selectedLang === 'ar' ? 'التكلفة' : 'Cost'}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {costs.apartments.map((apt, index) => (
                      <tr key={index} className="border-b dark:border-gray-700">
                        <td className="py-3 px-4">{apt.code}</td>
                        <td className="py-3 px-4">{apt.location}</td>
                        <td className="py-3 px-4">{apt.cost}</td>
                      </tr>
                    ))}
                    <tr className="font-semibold">
                      <td colSpan="2" className="py-3 px-4">
                        {selectedLang === 'ar' ? 'المجموع' : 'Total'}
                      </td>
                      <td className="py-3 px-4">
                        {calculateTotal(costs.apartments)}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
                {selectedLang === 'ar'
                  ? 'تكاليف السيارات المؤجرة'
                  : 'Rented Vehicles Costs'}
              </h2>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b dark:border-gray-700">
                      <th className="text-right py-3 px-4">
                        {selectedLang === 'ar'
                          ? 'رقم السيارة'
                          : 'Vehicle Number'}
                      </th>
                      <th className="text-right py-3 px-4">
                        {selectedLang === 'ar' ? 'الموديل' : 'Model'}
                      </th>
                      <th className="text-right py-3 px-4">
                        {selectedLang === 'ar' ? 'التكلفة' : 'Cost'}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {costs.vehicles.map((vehicle, index) => (
                      <tr key={index} className="border-b dark:border-gray-700">
                        <td className="py-3 px-4">{vehicle.number}</td>
                        <td className="py-3 px-4">{vehicle.model}</td>
                        <td className="py-3 px-4">{vehicle.cost}</td>
                      </tr>
                    ))}
                    <tr className="font-semibold">
                      <td colSpan="2" className="py-3 px-4">
                        {selectedLang === 'ar' ? 'المجموع' : 'Total'}
                      </td>
                      <td className="py-3 px-4">
                        {calculateTotal(costs.vehicles)}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
                {selectedLang === 'ar'
                  ? 'تكاليف العمالة المؤقتة'
                  : 'Temporary Workers Costs'}
              </h2>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b dark:border-gray-700">
                      <th className="text-right py-3 px-4">
                        {selectedLang === 'ar' ? 'اسم العامل' : 'Worker Name'}
                      </th>
                      <th className="text-right py-3 px-4">
                        {selectedLang === 'ar' ? 'المهنة' : 'Job'}
                      </th>
                      <th className="text-right py-3 px-4">
                        {selectedLang === 'ar' ? 'التكلفة' : 'Cost'}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {costs.workers.map((worker, index) => (
                      <tr key={index} className="border-b dark:border-gray-700">
                        <td className="py-3 px-4">{worker.name}</td>
                        <td className="py-3 px-4">{worker.job}</td>
                        <td className="py-3 px-4">{worker.cost}</td>
                      </tr>
                    ))}
                    <tr className="font-semibold">
                      <td colSpan="2" className="py-3 px-4">
                        {selectedLang === 'ar' ? 'المجموع' : 'Total'}
                      </td>
                      <td className="py-3 px-4">
                        {calculateTotal(costs.workers)}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div className="bg-blue-50 dark:bg-blue-900 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-blue-900 dark:text-blue-100">
                {selectedLang === 'ar' ? 'إجمالي التكاليف' : 'Grand Total'}:{' '}
                {grandTotal}
              </h2>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default MainComponent;
