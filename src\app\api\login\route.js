import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';
import { SignJWT } from 'jose';
import bcrypt from 'bcryptjs';

// إنشاء JWT Token
async function createJWT(payload) {
  const secret = new TextEncoder().encode(process.env.JWT_SECRET || 'your-secret-key-here');

  return await new SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' })
    .setIssuedAt()
    .setExpirationTime('24h') // انتهاء الصلاحية بعد 24 ساعة
    .sign(secret);
}

export async function POST(request) {
  let pool = null;

  try {

    const body = await request.json();
    const { username, password } = body;

    // التحقق من وجود البيانات المطلوبة
    if (!username || !password) {

      return NextResponse.json({
        success: false,
        error: 'يجب إدخال اسم المستخدم وكلمة المرور'
      }, { status: 400 });
    }

    pool = await getConnection();

    // البحث عن المستخدم في جدول LOGIN

    const result = await pool.request()
      .input('username', username)
      .input('password', password)
      .query(`
        SELECT
          code,
          password
        FROM LOGIN
        WHERE code = @username
          AND password = @password
      `);

    const user = result.recordset[0];

    if (!user) {

      // تسجيل محاولة تسجيل دخول فاشلة
      try {
        await pool.request()
          .input('userCode', sql.NVarChar, username)
          .input('userName', sql.NVarChar, username)
          .input('actionType', sql.NVarChar, 'LOGIN_FAILED')
          .input('actionDescription', sql.NVarChar, `محاولة تسجيل دخول فاشلة للمستخدم: ${username}`)
          .input('ipAddress', sql.NVarChar, request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'Unknown')
          .input('userAgent', sql.NVarChar, request.headers.get('user-agent') || 'Unknown')
          .input('isSuccess', sql.Bit, 0)
          .query(`
            INSERT INTO UserActions (UserCode, UserName, ActionType, ActionDescription, IPAddress, UserAgent, IsSuccess, ActionDate)
            VALUES (@userCode, @userName, @actionType, @actionDescription, @ipAddress, @userAgent, @isSuccess, GETDATE())
          `);
      } catch (logError) {

      }

      return NextResponse.json({
        success: false,
        error: 'اسم المستخدم أو كلمة المرور غير صحيحة'
      }, { status: 401 });
    }

    // تسجيل محاولة تسجيل دخول ناجحة
    try {
      await pool.request()
        .input('userCode', sql.NVarChar, user.code)
        .input('userName', sql.NVarChar, user.fullName || user.code)
        .input('actionType', sql.NVarChar, 'LOGIN_SUCCESS')
        .input('actionDescription', sql.NVarChar, `تم تسجيل الدخول بنجاح للمستخدم: ${user.code}`)
        .input('ipAddress', sql.NVarChar, request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'Unknown')
        .input('userAgent', sql.NVarChar, request.headers.get('user-agent') || 'Unknown')
        .input('isSuccess', sql.Bit, 1)
        .query(`
          INSERT INTO UserActions (UserCode, UserName, ActionType, ActionDescription, IPAddress, UserAgent, IsSuccess, ActionDate)
          VALUES (@userCode, @userName, @actionType, @actionDescription, @ipAddress, @userAgent, @isSuccess, GETDATE())
        `);
    } catch (logError) {

    }

    // إنشاء بيانات المستخدم
    const userData = {
      userId: user.code,
      username: user.code,
      name: `مستخدم ${user.code}`,
      role: 'employee',
      loginTime: new Date().toISOString()
    };

    // إنشاء JWT Token
    const token = await createJWT(userData);

    // إنشاء الاستجابة مع تعيين الكوكيز
    const response = NextResponse.json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: userData,
      token: token
    });

    // تعيين التوكن في الكوكيز (آمن)
    response.cookies.set('auth-token', token, {
      httpOnly: true, // لا يمكن الوصول إليه من JavaScript
      secure: process.env.NODE_ENV === 'production', // HTTPS في الإنتاج فقط
      sameSite: 'strict', // حماية من CSRF
      maxAge: 24 * 60 * 60, // 24 ساعة
      path: '/' // متاح لجميع المسارات
    });

    return response;

  } catch (error) {

    // رسائل خطأ مخصصة حسب نوع الخطأ
    let errorMessage = 'حدث خطأ أثناء تسجيل الدخول';

    if (error.code === 'ECONNREFUSED') {
      errorMessage = 'خطأ في الاتصال بقاعدة البيانات';
    } else if (error.code === 'ELOGIN') {
      errorMessage = 'خطأ في تسجيل الدخول لقاعدة البيانات';
    } else if (error.code === 'ETIMEOUT') {
      errorMessage = 'انتهت مهلة الاتصال بقاعدة البيانات';
    } else if (error.number === 207) {
      errorMessage = 'خطأ في هيكل قاعدة البيانات';
    }

    return NextResponse.json({
      success: false,
      error: errorMessage,
      details: process.env.NODE_ENV === 'development' ? {
        message: error.message,
        code: error.code,
        state: error.state
      } : undefined
    }, { status: 500 });

  } finally {
    // لا نحتاج لإغلاق الاتصال لأن getConnection تستخدم connection pool

  }
}

// GET method للتحقق من حالة API
export async function GET() {
  return NextResponse.json({
    message: 'Login API is working',
    timestamp: new Date().toISOString()
  });
}
