'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  <PERSON><PERSON><PERSON>, <PERSON>L<PERSON>, <PERSON><PERSON>ye, FiEyeOff, FiLogIn,
  FiShield, FiUsers, FiHome, FiTruck, FiSun, FiMoon
} from 'react-icons/fi';
import { useTheme } from '@/contexts/ThemeContext';

// مكون بسيط لتبديل الثيم
const SimpleThemeToggle = () => {
  const { isDarkMode, toggleTheme } = useTheme();

  return (
    <button
      onClick={toggleTheme}
      className={`w-12 h-12 rounded-lg flex items-center justify-center transition-all duration-300 ${
        isDarkMode
          ? 'bg-gray-700 hover:bg-gray-600 text-yellow-400 shadow-lg'
          : 'bg-white hover:bg-gray-50 text-gray-700 shadow-md border border-gray-200'
      } hover:scale-105 active:scale-95`}
      title={isDarkMode ? 'تبديل للوضع النهاري' : 'تبديل للوضع الليلي'}
    >
      {isDarkMode ? (
        <FiSun className="text-lg" />
      ) : (
        <FiMoon className="text-lg" />
      )}
    </button>
  );
};

export default function LoginPage() {
  const router = useRouter();
  const { isDarkMode, themeClasses } = useTheme();

  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);

  // فحص حالة تسجيل الدخول عند تحميل الصفحة
  useEffect(() => {

    const isLoggedIn = localStorage.getItem('isLoggedIn');
    const userInfo = localStorage.getItem('userInfo');

    if (isLoggedIn === 'true' && userInfo) {

      // لا نقوم بتوجيه تلقائي، نترك المستخدم يقرر
    } else {
      // تنظيف أي بيانات قديمة
      localStorage.removeItem('isLoggedIn');
      localStorage.removeItem('userInfo');
      document.cookie = 'isLoggedIn=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
      document.cookie = 'userInfo=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';

    }
  }, [router]);
  const [error, setError] = useState('');

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setError('');
  };

  const handleLogin = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {

      // إرسال طلب تسجيل الدخول إلى API
      const response = await fetch('/api/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: formData.username,
          password: formData.password
        })
      });

      const data = await response.json();

      if (data.success) {

        // حفظ حالة تسجيل الدخول في localStorage و cookies
        localStorage.setItem('isLoggedIn', 'true');
        localStorage.setItem('userInfo', JSON.stringify(data.user));

        // حفظ في cookies أيضاً للـ middleware
        document.cookie = `isLoggedIn=true; path=/; max-age=${60 * 60 * 24 * 7}`; // أسبوع
        document.cookie = `userInfo=${JSON.stringify(data.user)}; path=/; max-age=${60 * 60 * 24 * 7}`;

        // التوجه إلى الداش بورد
        router.push('/dashboard');
      } else {

        setError(data.error || 'حدث خطأ أثناء تسجيل الدخول');
      }
    } catch (err) {

      setError('حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`min-h-screen flex ${themeClasses.bg.primary}`}>
      <div className="absolute top-4 left-4 z-50">
        <SimpleThemeToggle />
      </div>

      <div className={`hidden lg:flex lg:w-1/2 ${isDarkMode ? 'bg-gradient-to-br from-blue-900 to-purple-900' : 'bg-gradient-to-br from-blue-600 to-purple-600'} relative overflow-hidden`}>
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-10 left-10 w-20 h-20 bg-white rounded-full animate-pulse"></div>
          <div className="absolute top-32 right-16 w-16 h-16 bg-white rounded-full animate-pulse delay-1000"></div>
          <div className="absolute bottom-20 left-20 w-24 h-24 bg-white rounded-full animate-pulse delay-2000"></div>
          <div className="absolute bottom-40 right-10 w-12 h-12 bg-white rounded-full animate-pulse delay-500"></div>
        </div>

        <div className="relative z-10 flex flex-col justify-center items-center text-white p-12 text-center">
          <div className="mb-8">
            <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center mb-4 mx-auto backdrop-blur-sm">
              <FiShield className="text-4xl text-white" />
            </div>
            <h1 className="text-4xl font-bold mb-2">نظام إدارة الشركة</h1>
            <p className="text-xl opacity-90">منصة شاملة لإدارة الموارد</p>
          </div>

          <div className="space-y-6 max-w-md">
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                <FiUsers className="text-xl text-white" />
              </div>
              <div className="text-right">
                <h3 className="font-semibold text-lg">إدارة الموظفين</h3>
                <p className="text-sm opacity-80">نظام شامل لإدارة بيانات الموظفين والأرشيف</p>
              </div>
            </div>

            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                <FiHome className="text-xl text-white" />
              </div>
              <div className="text-right">
                <h3 className="font-semibold text-lg">إدارة الشقق</h3>
                <p className="text-sm opacity-80">متابعة الشقق والإيجارات والصيانة</p>
              </div>
            </div>

            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                <FiTruck className="text-xl text-white" />
              </div>
              <div className="text-right">
                <h3 className="font-semibold text-lg">إدارة السيارات</h3>
                <p className="text-sm opacity-80">تتبع السيارات والإيجارات والصيانة</p>
              </div>
            </div>
          </div>

          <div className="mt-12 grid grid-cols-3 gap-6 w-full max-w-md">
            <div className="text-center">
              <div className="text-2xl font-bold">500+</div>
              <div className="text-sm opacity-80">موظف</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">150+</div>
              <div className="text-sm opacity-80">شقة</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">75+</div>
              <div className="text-sm opacity-80">سيارة</div>
            </div>
          </div>
        </div>
      </div>

      <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <div className={`w-16 h-16 ${isDarkMode ? 'bg-blue-900' : 'bg-blue-100'} rounded-full flex items-center justify-center mx-auto mb-4`}>
              <FiLogIn className={`text-2xl ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`} />
            </div>
            <h2 className={`text-3xl font-bold ${themeClasses.text.primary} mb-2`}>تسجيل الدخول</h2>
            <p className={themeClasses.text.secondary}>أدخل بياناتك للوصول إلى النظام</p>
          </div>

          <form onSubmit={handleLogin} className="space-y-6">
            <div>
              <label className={`block text-sm font-medium ${themeClasses.text.primary} mb-2`}>
                اسم المستخدم
              </label>
              <div className="relative">
                <input
                  type="text"
                  name="username"
                  value={formData.username}
                  onChange={handleInputChange}
                  required
                  className={`w-full px-4 py-3 pr-12 ${themeClasses.input.primary} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all`}
                  placeholder="أدخل اسم المستخدم"
                />
                <FiUser className={`absolute right-4 top-1/2 transform -translate-y-1/2 ${themeClasses.text.secondary}`} />
              </div>
            </div>

            <div>
              <label className={`block text-sm font-medium ${themeClasses.text.primary} mb-2`}>
                كلمة المرور
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                  className={`w-full px-4 py-3 pr-12 pl-12 ${themeClasses.input.primary} rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all`}
                  placeholder="أدخل كلمة المرور"
                />
                <FiLock className={`absolute right-4 top-1/2 transform -translate-y-1/2 ${themeClasses.text.secondary}`} />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className={`absolute left-4 top-1/2 transform -translate-y-1/2 ${themeClasses.text.secondary} hover:${themeClasses.text.primary} transition-colors`}
                >
                  {showPassword ? <FiEyeOff /> : <FiEye />}
                </button>
              </div>
            </div>

            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg text-sm">
                {error}
              </div>
            )}

            <button
              type="submit"
              disabled={loading}
              className={`w-full ${themeClasses.button.primary} py-3 px-4 rounded-lg font-medium transition-all duration-200 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed hover:shadow-lg transform hover:-translate-y-0.5`}
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  جاري تسجيل الدخول...
                </>
              ) : (
                <>
                  <FiLogIn />
                  تسجيل الدخول
                </>
              )}
            </button>
          </form>

          <div className={`mt-8 p-4 ${isDarkMode ? 'bg-blue-900/20' : 'bg-blue-50'} rounded-lg`}>
            <h4 className={`font-medium ${themeClasses.text.primary} mb-2`}>بيانات من قاعدة البيانات:</h4>
            <div className={`text-sm ${themeClasses.text.secondary} space-y-1`}>
              <div>🔗 متصل بجدول LOGIN</div>
              <div>📊 قاعدة البيانات: EMP</div>
              <div>👥 المستخدمين المتاحين:</div>
              <div className="text-xs font-mono bg-gray-100 dark:bg-gray-700 p-2 rounded mt-2">
                <div>• 6019</div>
                <div>• 5567</div>
                <div>• 1450</div>
                <div className="text-xs text-gray-500 mt-2">
                  * يرجى التواصل مع المدير للحصول على كلمة المرور
                </div>
              </div>
            </div>
          </div>

          {/* روابط مفيدة */}
          <div className="text-center mt-6 space-y-2">
            <div className="text-xs text-blue-600">
              <a href="/check-users" className="hover:underline">
                🔍 فحص المستخدمين المتاحين
              </a>
            </div>
            <div className="text-xs text-red-600">
              <a href="/clear-session" className="hover:underline">
                🗑️ مسح بيانات الجلسة (حل مشكلة التوجيه التلقائي)
              </a>
            </div>
          </div>

          <div className={`text-center mt-8 text-sm ${themeClasses.text.secondary}`}>
            © 2024 نظام إدارة الشركة. جميع الحقوق محفوظة.
          </div>
        </div>
      </div>
    </div>
  );
}
