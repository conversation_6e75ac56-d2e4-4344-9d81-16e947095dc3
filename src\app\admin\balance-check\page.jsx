'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import { FiUser, FiCalendar, FiAlertTriangle, FiRefreshCw } from 'react-icons/fi';

export default function BalanceCheckPage() {
  const [employeeCode, setEmployeeCode] = useState('1450');
  const [balanceData, setBalanceData] = useState(null);
  const [approvedLeaves, setApprovedLeaves] = useState([]);
  const [loading, setLoading] = useState(false);

  const checkBalance = async () => {
    if (!employeeCode) {
      alert('يرجى إدخال كود الموظف');
      return;
    }

    try {
      setLoading(true);

      // جلب رصيد الإجازات
      const balanceResponse = await fetch(`/api/leave-balances?action=list&employeeCode=${employeeCode}`);
      const balanceResult = await balanceResponse.json();

      // جلب الإجازات المعتمدة
      const leavesResponse = await fetch(`/api/paper-requests?action=list&status=معتمدة&requestType=leave&employeeCode=${employeeCode}`);
      const leavesResult = await leavesResponse.json();

      if (!balanceResult.success) {

        alert('خطأ في جلب رصيد الإجازات: ' + balanceResult.error);
      }

      if (!leavesResult.success) {

        alert('خطأ في جلب الإجازات المعتمدة: ' + leavesResult.error);
      }

      setBalanceData(balanceResult.success && balanceResult.balances.length > 0 ? balanceResult.balances[0] : null);
      setApprovedLeaves(leavesResult.success ? (leavesResult.requests || []) : []);

      if (balanceResult.success && balanceResult.balances.length === 0) {
        alert('لم يتم العثور على رصيد إجازات للموظف ' + employeeCode);
      }

    } catch (error) {

      alert('خطأ في الاتصال بالخادم: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const fixBalance = async () => {
    if (!confirm('هل أنت متأكد من إصلاح الرصيد؟ سيتم إعادة حساب الرصيد بناءً على الإجازات المعتمدة.')) {
      return;
    }

    try {
      setLoading(true);

      const response = await fetch('/api/admin/fix-balance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          employeeCode: employeeCode
        }),
      });

      const result = await response.json();

      if (result.success) {
        alert('تم إصلاح الرصيد بنجاح\n\n' +
              `الإجازات المعتمدة: ${result.data.approvedLeavesCount}\n` +
              `المستخدم من الاعتيادية: ${result.data.usedRegular} يوم\n` +
              `المستخدم من العارضة: ${result.data.usedCasual} يوم\n` +
              `الرصيد الجديد: اعتيادي ${result.data.correctRegularBalance}، عارضة ${result.data.correctCasualBalance}`);
        checkBalance(); // إعادة تحميل البيانات
      } else {
        alert(result.error || 'خطأ في إصلاح الرصيد');
      }
    } catch (error) {

      alert('خطأ في الاتصال بالخادم: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkBalance();
  }, []);

  const calculateExpectedBalance = () => {
    if (!balanceData || !approvedLeaves) return null;

    const initialRegular = 15; // الرصيد الأولي للإجازة الاعتيادية
    const initialCasual = 6;   // الرصيد الأولي للإجازة العارضة

    let usedRegular = 0;
    let usedCasual = 0;

    approvedLeaves.forEach(leave => {
      if (leave.LeaveType === 'annual') {
        usedRegular += leave.DaysCount || 0;
      } else if (leave.LeaveType === 'casual') {
        usedCasual += leave.DaysCount || 0;
      }
    });

    return {
      expectedRegular: initialRegular - usedRegular,
      expectedCasual: initialCasual - usedCasual,
      usedRegular,
      usedCasual
    };
  };

  const expected = calculateExpectedBalance();

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-6xl mx-auto">
          {/* العنوان */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-200 mb-2">
                  فحص رصيد الإجازات
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  التحقق من صحة رصيد الإجازات مقابل الإجازات المعتمدة
                </p>
              </div>
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <FiUser className="text-2xl text-white" />
              </div>
            </div>
          </div>

          {/* البحث */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
            <div className="flex gap-4 items-end">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  كود الموظف
                </label>
                <input
                  type="text"
                  value={employeeCode}
                  onChange={(e) => setEmployeeCode(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
                  placeholder="أدخل كود الموظف"
                />
              </div>
              <button
                onClick={checkBalance}
                disabled={loading}
                className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center gap-2"
              >
                {loading ? (
                  <FiRefreshCw className="animate-spin" />
                ) : (
                  <FiUser />
                )}
                فحص
              </button>
            </div>
          </div>

          {/* رسالة عدم وجود رصيد */}
          {!loading && !balanceData && employeeCode && (
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
              <div className="flex items-center gap-3">
                <FiAlertTriangle className="text-yellow-600 dark:text-yellow-400 flex-shrink-0" />
                <div className="flex-1">
                  <h3 className="text-yellow-800 dark:text-yellow-200 font-medium mb-2">
                    لا يوجد رصيد إجازات للموظف {employeeCode}
                  </h3>
                  <p className="text-yellow-700 dark:text-yellow-300 text-sm mb-4">
                    لم يتم العثور على سجل رصيد إجازات لهذا الموظف. يمكنك إنشاء رصيد جديد أو إصلاح الرصيد.
                  </p>
                  <button
                    onClick={fixBalance}
                    disabled={loading}
                    className="bg-yellow-500 hover:bg-yellow-600 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                  >
                    إنشاء/إصلاح الرصيد
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* النتائج */}
          {balanceData && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* الرصيد الحالي */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
                  الرصيد الحالي في قاعدة البيانات
                </h2>
                <div className="space-y-3">
                  <div className="flex justify-between items-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <span>الإجازة الاعتيادية:</span>
                    <span className="font-bold text-blue-600">{balanceData.AnnualBalance || balanceData.RegularBalance || 0} يوم</span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <span>الإجازة العارضة:</span>
                    <span className="font-bold text-green-600">{balanceData.CasualBalance || 0} يوم</span>
                  </div>
                </div>
              </div>

              {/* الرصيد المتوقع */}
              {expected && (
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                  <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
                    الرصيد المتوقع (بعد خصم الإجازات المعتمدة)
                  </h2>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <span>الإجازة الاعتيادية:</span>
                      <span className={`font-bold ${
                        expected.expectedRegular === (balanceData.AnnualBalance || balanceData.RegularBalance || 0)
                          ? 'text-green-600'
                          : 'text-red-600'
                      }`}>
                        {expected.expectedRegular} يوم
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <span>الإجازة العارضة:</span>
                      <span className={`font-bold ${
                        expected.expectedCasual === (balanceData.CasualBalance || 0)
                          ? 'text-green-600'
                          : 'text-red-600'
                      }`}>
                        {expected.expectedCasual} يوم
                      </span>
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                      <p>مستخدم من الاعتيادية: {expected.usedRegular} يوم</p>
                      <p>مستخدم من العارضة: {expected.usedCasual} يوم</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* تحذير إذا كان هناك خطأ */}
          {expected && balanceData && (
            (expected.expectedRegular !== (balanceData.AnnualBalance || balanceData.RegularBalance || 0) ||
             expected.expectedCasual !== (balanceData.CasualBalance || 0)) && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mt-6">
                <div className="flex items-start gap-3">
                  <FiAlertTriangle className="text-red-600 dark:text-red-400 mt-1 flex-shrink-0" />
                  <div className="flex-1">
                    <h3 className="text-red-800 dark:text-red-200 font-medium mb-2">
                      خطأ في الرصيد!
                    </h3>
                    <p className="text-red-700 dark:text-red-300 text-sm mb-4">
                      الرصيد الحالي في قاعدة البيانات لا يطابق الرصيد المتوقع بعد خصم الإجازات المعتمدة.
                    </p>
                    <button
                      onClick={fixBalance}
                      disabled={loading}
                      className="bg-red-500 hover:bg-red-600 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                    >
                      إصلاح الرصيد
                    </button>
                  </div>
                </div>
              </div>
            )
          )}

          {/* الإجازات المعتمدة */}
          {approvedLeaves.length > 0 && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mt-6">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center gap-2">
                <FiCalendar />
                الإجازات المعتمدة ({approvedLeaves.length})
              </h2>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300">نوع الإجازة</th>
                      <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300">عدد الأيام</th>
                      <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300">من تاريخ</th>
                      <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300">إلى تاريخ</th>
                      <th className="px-4 py-2 text-right text-xs font-medium text-gray-500 dark:text-gray-300">تاريخ الاعتماد</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
                    {approvedLeaves.map((leave, index) => (
                      <tr key={index}>
                        <td className="px-4 py-2 text-sm">
                          {leave.LeaveType === 'annual' ? 'اعتيادية' : 'عارضة'}
                        </td>
                        <td className="px-4 py-2 text-sm font-medium">{leave.DaysCount}</td>
                        <td className="px-4 py-2 text-sm">{new Date(leave.StartDate).toLocaleDateString('ar-EG')}</td>
                        <td className="px-4 py-2 text-sm">{new Date(leave.EndDate).toLocaleDateString('ar-EG')}</td>
                        <td className="px-4 py-2 text-sm">{new Date(leave.RequestDate).toLocaleDateString('ar-EG')}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
