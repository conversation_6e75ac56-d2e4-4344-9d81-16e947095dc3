import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

// جلب التكاليف
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const tab = searchParams.get('tab') || 'apartments';
    const month = parseInt(searchParams.get('month')) || new Date().getMonth() + 1;
    const year = parseInt(searchParams.get('year')) || new Date().getFullYear();
    const status = searchParams.get('status') || 'all';

    const pool = await getConnection();
    let costs = [];

    // جلب التكاليف حسب النوع
    switch (tab) {
      case 'apartments':
        costs = await getApartmentsCosts(pool, month, year, status);
        break;
      case 'cars':
        costs = await getCarsCosts(pool, month, year, status);
        break;
      case 'tempWorkers':
        costs = await getTempWorkersCosts(pool, month, year, status);
        break;
      default:
        costs = [];
    }

    return NextResponse.json({
      success: true,
      costs: costs,
      count: costs.length
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'حدث خطأ في جلب التكاليف',
      error: error.message
    }, { status: 500 });
  }
}

// إضافة تكلفة جديدة
export async function POST(request) {
  try {
    const body = await request.json();
    const { type, category, description, amount, date, status, reference } = body;

    const pool = await getConnection();

    // إنشاء جدول التكاليف إذا لم يكن موجوداً
    await createCostsTableIfNotExists(pool);

    const insertQuery = `
      INSERT INTO Costs (Type, Category, Description, Amount, Date, Status, Reference, CreatedAt)
      VALUES (@Type, @Category, @Description, @Amount, @Date, @Status, @Reference, GETDATE())
    `;

    const request = pool.request();
    request.input('Type', sql.NVarChar, type);
    request.input('Category', sql.NVarChar, category);
    request.input('Description', sql.NVarChar, description);
    request.input('Amount', sql.Decimal(10, 2), amount);
    request.input('Date', sql.Date, new Date(date));
    request.input('Status', sql.NVarChar, status);
    request.input('Reference', sql.NVarChar, reference || null);

    await request.query(insertQuery);

    return NextResponse.json({
      success: true,
      message: 'تم إضافة التكلفة بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'حدث خطأ في إضافة التكلفة',
      error: error.message
    }, { status: 500 });
  }
}

// تحديث تكلفة
export async function PUT(request) {
  try {
    const body = await request.json();
    const { id, type, category, description, amount, date, status, reference } = body;

    const pool = await getConnection();

    const updateQuery = `
      UPDATE Costs 
      SET Type = @Type, Category = @Category, Description = @Description, 
          Amount = @Amount, Date = @Date, Status = @Status, Reference = @Reference,
          UpdatedAt = GETDATE()
      WHERE ID = @ID
    `;

    const request = pool.request();
    request.input('ID', sql.Int, id);
    request.input('Type', sql.NVarChar, type);
    request.input('Category', sql.NVarChar, category);
    request.input('Description', sql.NVarChar, description);
    request.input('Amount', sql.Decimal(10, 2), amount);
    request.input('Date', sql.Date, new Date(date));
    request.input('Status', sql.NVarChar, status);
    request.input('Reference', sql.NVarChar, reference || null);

    const result = await request.query(updateQuery);

    if (result.rowsAffected[0] > 0) {
      return NextResponse.json({
        success: true,
        message: 'تم تحديث التكلفة بنجاح'
      });
    } else {
      return NextResponse.json({
        success: false,
        message: 'لم يتم العثور على التكلفة'
      }, { status: 404 });
    }

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'حدث خطأ في تحديث التكلفة',
      error: error.message
    }, { status: 500 });
  }
}

// حذف تكلفة
export async function DELETE(request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    const pool = await getConnection();

    const deleteQuery = 'DELETE FROM Costs WHERE ID = @ID';
    const request = pool.request();
    request.input('ID', sql.Int, parseInt(id));

    const result = await request.query(deleteQuery);

    if (result.rowsAffected[0] > 0) {
      return NextResponse.json({
        success: true,
        message: 'تم حذف التكلفة بنجاح'
      });
    } else {
      return NextResponse.json({
        success: false,
        message: 'لم يتم العثور على التكلفة'
      }, { status: 404 });
    }

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'حدث خطأ في حذف التكلفة',
      error: error.message
    }, { status: 500 });
  }
}

// دوال مساعدة لجلب التكاليف

async function getApartmentsCosts(pool, month, year, status) {
  try {
    let whereClause = `WHERE Type = 'apartment' AND MONTH(Date) = @Month AND YEAR(Date) = @Year`;
    if (status !== 'all') {
      whereClause += ` AND Status = @Status`;
    }

    const query = `
      SELECT 
        ID as id,
        Type as type,
        Category,
        Description as description,
        Amount as amount,
        Date as date,
        Status as status,
        Reference,
        CreatedAt
      FROM Costs 
      ${whereClause}
      ORDER BY Date DESC
    `;

    const request = pool.request();
    request.input('Month', sql.Int, month);
    request.input('Year', sql.Int, year);
    if (status !== 'all') {
      request.input('Status', sql.NVarChar, status);
    }

    const result = await request.query(query);
    return result.recordset;
  } catch (error) {

    return generateMockApartmentsCosts(month, year);
  }
}

async function getCarsCosts(pool, month, year, status) {
  try {
    // استخدام جدول carscost الفعلي

    // تحويل رقم الشهر إلى اسم الشهر بالعربية
    const monthNames = {
      1: 'يناير', 2: 'فبراير', 3: 'مارس', 4: 'ابريل',
      5: 'مايو', 6: 'يونيو', 7: 'يوليو', 8: 'اغسطس',
      9: 'سبتمبر', 10: 'اكتوبر', 11: 'نوفمبر', 12: 'ديسمبر'
    };

    const monthName = monthNames[month];

    const query = `
      SELECT
        ID as id,
        'سيارات' as type,
        'إيجار شهري' as description,
        CAST([القيمة الإيجارية] AS DECIMAL(10,2)) as amount,
        CONCAT([السنة], '-',
          CASE [الشهر]
            WHEN 'يناير' THEN '01'
            WHEN 'فبراير' THEN '02'
            WHEN 'مارس' THEN '03'
            WHEN 'ابريل' THEN '04'
            WHEN 'مايو' THEN '05'
            WHEN 'يونيو' THEN '06'
            WHEN 'يوليو' THEN '07'
            WHEN 'اغسطس' THEN '08'
            WHEN 'سبتمبر' THEN '09'
            WHEN 'اكتوبر' THEN '10'
            WHEN 'نوفمبر' THEN '11'
            WHEN 'ديسمبر' THEN '12'
            ELSE '01'
          END, '-01') as date,
        CASE
          WHEN CAST([القيمة الإيجارية] AS INT) > 0 THEN 'paid'
          ELSE 'pending'
        END as status,
        [الشهر] as month_name,
        [السنة] as year_name,
        [العدد] as car_count
      FROM carscost
      WHERE [الشهر] = @MonthName AND [السنة] = @Year
      ORDER BY ID DESC
    `;

    const request = pool.request();
    request.input('MonthName', sql.NVarChar, monthName);
    request.input('Year', sql.NVarChar, year.toString());

    const result = await request.query(query);

    // إذا لم توجد بيانات للشهر المحدد، جلب جميع البيانات
    if (result.recordset.length === 0) {
      const allDataQuery = `
        SELECT
          ID as id,
          'سيارات - ' + [الشهر] + ' ' + [السنة] as type,
          'إيجار شهري - ' + [العدد] + ' سيارة' as description,
          CAST([القيمة الإيجارية] AS DECIMAL(10,2)) as amount,
          CONCAT([السنة], '-',
            CASE [الشهر]
              WHEN 'يناير' THEN '01'
              WHEN 'فبراير' THEN '02'
              WHEN 'مارس' THEN '03'
              WHEN 'ابريل' THEN '04'
              WHEN 'مايو' THEN '05'
              WHEN 'يونيو' THEN '06'
              WHEN 'يوليو' THEN '07'
              WHEN 'اغسطس' THEN '08'
              WHEN 'سبتمبر' THEN '09'
              WHEN 'اكتوبر' THEN '10'
              WHEN 'نوفمبر' THEN '11'
              WHEN 'ديسمبر' THEN '12'
              ELSE '01'
            END, '-01') as date,
          CASE
            WHEN CAST([القيمة الإيجارية] AS INT) > 0 THEN 'paid'
            ELSE 'pending'
          END as status,
          [الشهر] as month_name,
          [السنة] as year_name,
          [العدد] as car_count
        FROM carscost
        WHERE [السنة] = @Year
        ORDER BY
          CASE [الشهر]
            WHEN 'يناير' THEN 1
            WHEN 'فبراير' THEN 2
            WHEN 'مارس' THEN 3
            WHEN 'ابريل' THEN 4
            WHEN 'مايو' THEN 5
            WHEN 'يونيو' THEN 6
            WHEN 'يوليو' THEN 7
            WHEN 'اغسطس' THEN 8
            WHEN 'سبتمبر' THEN 9
            WHEN 'اكتوبر' THEN 10
            WHEN 'نوفمبر' THEN 11
            WHEN 'ديسمبر' THEN 12
          END DESC
      `;

      const allDataRequest = pool.request();
      allDataRequest.input('Year', sql.NVarChar, year.toString());
      const allDataResult = await allDataRequest.query(allDataQuery);

      return allDataResult.recordset;
    }

    return result.recordset;
  } catch (error) {

    return generateMockCarsCosts(month, year);
  }
}

async function getTempWorkersCosts(pool, month, year, status) {
  try {
    let whereClause = `WHERE Type = 'temp_worker' AND MONTH(Date) = @Month AND YEAR(Date) = @Year`;
    if (status !== 'all') {
      whereClause += ` AND Status = @Status`;
    }

    const query = `
      SELECT 
        ID as id,
        Type as type,
        Category,
        Description as description,
        Amount as amount,
        Date as date,
        Status as status,
        Reference,
        CreatedAt
      FROM Costs 
      ${whereClause}
      ORDER BY Date DESC
    `;

    const request = pool.request();
    request.input('Month', sql.Int, month);
    request.input('Year', sql.Int, year);
    if (status !== 'all') {
      request.input('Status', sql.NVarChar, status);
    }

    const result = await request.query(query);
    return result.recordset;
  } catch (error) {

    return generateMockTempWorkersCosts(month, year);
  }
}

// إنشاء جدول التكاليف
async function createCostsTableIfNotExists(pool) {
  const createTableQuery = `
    IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Costs]') AND type in (N'U'))
    BEGIN
      CREATE TABLE [dbo].[Costs](
        [ID] [int] IDENTITY(1,1) PRIMARY KEY,
        [Type] [nvarchar](50) NOT NULL,
        [Category] [nvarchar](100) NULL,
        [Description] [nvarchar](500) NOT NULL,
        [Amount] [decimal](10, 2) NOT NULL,
        [Date] [date] NOT NULL,
        [Status] [nvarchar](20) NOT NULL DEFAULT 'pending',
        [Reference] [nvarchar](100) NULL,
        [CreatedAt] [datetime] DEFAULT GETDATE(),
        [UpdatedAt] [datetime] NULL
      )
    END
  `;
  
  await pool.request().query(createTableQuery);
}

// بيانات تجريبية
function generateMockApartmentsCosts(month, year) {
  return [
    {
      id: 1,
      type: 'شقة A-101',
      description: 'صيانة دورية - تكييف',
      amount: 1500,
      date: `${year}-${month.toString().padStart(2, '0')}-05`,
      status: 'paid'
    },
    {
      id: 2,
      type: 'شقة B-205',
      description: 'إصلاح سباكة',
      amount: 800,
      date: `${year}-${month.toString().padStart(2, '0')}-12`,
      status: 'pending'
    },
    {
      id: 3,
      type: 'شقة C-301',
      description: 'تجديد دهان',
      amount: 2200,
      date: `${year}-${month.toString().padStart(2, '0')}-18`,
      status: 'overdue'
    }
  ];
}

function generateMockCarsCosts(month, year) {
  return [
    {
      id: 4,
      type: 'سيارة رقم 123',
      description: 'صيانة دورية',
      amount: 1200,
      date: `${year}-${month.toString().padStart(2, '0')}-08`,
      status: 'paid'
    },
    {
      id: 5,
      type: 'سيارة رقم 456',
      description: 'تغيير إطارات',
      amount: 2800,
      date: `${year}-${month.toString().padStart(2, '0')}-15`,
      status: 'pending'
    }
  ];
}

function generateMockTempWorkersCosts(month, year) {
  return [
    {
      id: 6,
      type: 'عامل نظافة',
      description: 'راتب شهري - أحمد محمد',
      amount: 3500,
      date: `${year}-${month.toString().padStart(2, '0')}-01`,
      status: 'paid'
    },
    {
      id: 7,
      type: 'عامل صيانة',
      description: 'راتب شهري - محمود علي',
      amount: 4200,
      date: `${year}-${month.toString().padStart(2, '0')}-01`,
      status: 'pending'
    }
  ];
}
