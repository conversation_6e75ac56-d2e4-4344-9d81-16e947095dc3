{"name": "create-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "clean": "node scripts/clean-project.js", "clean:temp": "rimraf temp-* test-* debug-* fix-* check-* verify-* *.html", "clean:logs": "rimraf *.log npm-debug.log* yarn-debug.log* yarn-error.log*", "create-sample-costs": "node scripts/create-sample-costs.js", "sync-real-costs": "node scripts/sync-real-costs.js", "clean-test-data": "node scripts/clean-test-data.js"}, "dependencies": {"@next/swc-win32-x64-msvc": "^13.4.19", "bcryptjs": "^3.0.2", "chart.js": "^4.5.0", "chartjs-plugin-datalabels": "^2.2.0", "date-fns": "^4.1.0", "exceljs": "^4.4.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "mssql": "^11.0.1", "next": "^13.4.19", "react": "^18.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.0.0", "react-icons": "^5.5.0", "react-organizational-chart": "^2.2.1", "styled-components": "^6.1.19", "xlsx": "^0.18.5"}, "devDependencies": {"@tailwindcss/forms": "^0.5.0", "@tailwindcss/typography": "^0.5.0", "@types/react": "19.1.8", "autoprefixer": "^10.0.0", "postcss": "^8.0.0", "prettier": "^3.5.3", "tailwindcss": "^3.0.0", "typescript": "5.8.3"}, "overrides": {"supports-color": "^10.0.0"}, "resolutions": {"supports-color": "^10.0.0"}}