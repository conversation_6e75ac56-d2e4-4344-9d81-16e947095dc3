// دالة مساعدة لإنشاء إشعارات عند كل إجراء

/**
 * إنشاء إشعار ذكي عند تنفيذ إجراء
 * @param {Object} actionData - بيانات الإجراء
 * @param {string} actionData.type - نوع الإجراء
 * @param {string} actionData.title - عنوان الإشعار
 * @param {string} actionData.message - رسالة الإشعار
 * @param {string} actionData.employeeId - كود الموظف
 * @param {string} actionData.employeeName - اسم الموظف
 * @param {string} actionData.systemUserId - كود مسؤول النظام
 * @param {string} actionData.priority - أولوية الإشعار (low, medium, high, urgent)
 * @param {Object} actionData.relatedData - بيانات إضافية
 */
export async function createActionNotification(actionData) {
  try {
    const response = await fetch('/api/smart-notifications', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'create',
        ...actionData
      })
    });

    const result = await response.json();
    if (result.success) {

      // إشعار جميع مكونات الإشعارات بالتحديث
      window.dispatchEvent(new CustomEvent('notificationUpdate', {
        detail: { type: 'new', notification: result.notification }
      }));
      
      return result;
    } else {

      return null;
    }
  } catch (error) {

    return null;
  }
}

/**
 * إنشاء إشعار عند تقديم طلب إجازة
 */
export async function createLeaveRequestNotification(requestData) {
  const currentManagerName = getCurrentManagerName();
  const startDate = formatDate(requestData.startDate);
  
  return await createActionNotification({
    type: 'LEAVE_REQUEST_SUBMITTED',
    title: `طلب إجازة جديد - ${requestData.employeeName}`,
    message: `تم تقديم إجازة ${getLeaveTypeArabic(requestData.leaveType)} لـ (${requestData.employeeName}) (${requestData.daysCount || 0} أيام) تبدأ من (${startDate}) بواسطة (${currentManagerName})`,
    employeeId: requestData.employeeCode,
    employeeName: requestData.employeeName,
    systemUserId: currentManagerName,
    leaveStartDate: requestData.startDate,
    priority: 'high',
    relatedData: {
      requestId: requestData.requestId,
      leaveType: requestData.leaveType,
      startDate: requestData.startDate,
      endDate: requestData.endDate,
      daysCount: requestData.daysCount,
      systemUser: currentManagerName,
      submissionTime: new Date().toISOString()
    }
  });
}

/**
 * إنشاء إشعار عند اعتماد/رفض طلب
 */
export async function createRequestStatusNotification(requestData, status, notes = '') {
  const currentManagerName = getCurrentManagerName();
  const actionText = status === 'معتمدة' ? 'اعتماد' : 
                    status === 'مرفوضة' ? 'رفض' : 'تحديث';
  
  const notificationMessage = `تم ${actionText} طلب إجازة ${requestData.LeaveType || 'إعتيادية'} لـ (${requestData.EmployeeName}) (${requestData.DaysCount || 0} أيام) من (${formatDate(requestData.StartDate)}) إلى (${formatDate(requestData.EndDate)}) بواسطة (${currentManagerName})`;

  return await createActionNotification({
    type: status === 'معتمدة' ? 'LEAVE_REQUEST_APPROVED' :
          status === 'مرفوضة' ? 'LEAVE_REQUEST_REJECTED' : 'LEAVE_REQUEST_UPDATED',
    title: `تم ${actionText} طلب الإجازة - ${requestData.EmployeeName}`,
    message: notificationMessage,
    employeeId: requestData.EmployeeCode,
    employeeName: requestData.EmployeeName,
    systemUserId: currentManagerName,
    leaveStartDate: requestData.StartDate,
    priority: status === 'معتمدة' ? 'medium' : 'high',
    relatedData: {
      requestId: requestData.ID,
      status: status,
      notes: notes,
      approvedBy: currentManagerName,
      leaveType: requestData.LeaveType,
      startDate: requestData.StartDate,
      endDate: requestData.EndDate,
      daysCount: requestData.DaysCount,
      actionTime: new Date().toISOString()
    }
  });
}

/**
 * إنشاء إشعار عند إنشاء موظف جديد
 */
export async function createEmployeeCreatedNotification(employeeData) {
  const currentManagerName = getCurrentManagerName();
  
  return await createActionNotification({
    type: 'EMPLOYEE_CREATED',
    title: `موظف جديد - ${employeeData.name}`,
    message: `تم إضافة موظف جديد: (${employeeData.name}) - كود: (${employeeData.code}) بواسطة (${currentManagerName})`,
    employeeId: employeeData.code,
    employeeName: employeeData.name,
    systemUserId: currentManagerName,
    priority: 'medium',
    relatedData: {
      employeeCode: employeeData.code,
      employeeName: employeeData.name,
      department: employeeData.department,
      jobTitle: employeeData.jobTitle,
      createdBy: currentManagerName,
      creationTime: new Date().toISOString()
    }
  });
}

/**
 * إنشاء إشعار عند تحديث بيانات موظف
 */
export async function createEmployeeUpdatedNotification(employeeData, changes) {
  const currentManagerName = getCurrentManagerName();
  
  return await createActionNotification({
    type: 'EMPLOYEE_UPDATED',
    title: `تحديث بيانات موظف - ${employeeData.name}`,
    message: `تم تحديث بيانات الموظف: (${employeeData.name}) - كود: (${employeeData.code}) بواسطة (${currentManagerName})`,
    employeeId: employeeData.code,
    employeeName: employeeData.name,
    systemUserId: currentManagerName,
    priority: 'low',
    relatedData: {
      employeeCode: employeeData.code,
      employeeName: employeeData.name,
      changes: changes,
      updatedBy: currentManagerName,
      updateTime: new Date().toISOString()
    }
  });
}

/**
 * الحصول على اسم مسؤول النظام الحالي
 */
function getCurrentManagerName() {
  try {
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    const userCode = userInfo.username || '1450';
    
    // تحديد اسم المسؤول بناءً على الكود
    const managers = {
      '1450': 'سامي منير',
      '5567': 'إسلام فايز'
    };
    
    return managers[userCode] || 'مدير النظام';
  } catch (error) {
    return 'مدير النظام';
  }
}

/**
 * تحويل أنواع الإجازات من الإنجليزية إلى العربية
 */
function getLeaveTypeArabic(leaveType) {
  if (!leaveType) return 'إعتيادية';

  const leaveTypeMap = {
    'annual': 'إعتيادية',
    'emergency': 'عارضة',
    'sick': 'مرضية',
    'unpaid': 'بدون أجر',
    'badal': 'بدل',
    'maternity': 'أمومة',
    'paternity': 'أبوة',
    'hajj': 'حج',
    'marriage': 'زواج',
    'death': 'وفاة',
    'exam': 'امتحانات',
    'other': 'أخرى',
    // المسميات العربية (في حالة كانت مرسلة بالعربية أصلاً)
    'إعتيادية': 'إعتيادية',
    'اعتيادية': 'إعتيادية',
    'عارضة': 'عارضة',
    'مرضية': 'مرضية',
    'بدون أجر': 'بدون أجر',
    'بدل': 'بدل',
    'أمومة': 'أمومة',
    'أبوة': 'أبوة',
    'حج': 'حج',
    'زواج': 'زواج',
    'وفاة': 'وفاة',
    'امتحانات': 'امتحانات',
    'أخرى': 'أخرى'
  };

  return leaveTypeMap[leaveType] || leaveType;
}

/**
 * تنسيق التاريخ بصيغة dd/mm/yyyy
 */
function formatDate(dateString) {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';

    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  } catch (error) {
    return '';
  }
}

/**
 * إشعار جميع مكونات الإشعارات بالتحديث
 */
export function notifyAllComponents() {
  window.dispatchEvent(new CustomEvent('notificationUpdate', {
    detail: { type: 'refresh' }
  }));
}
