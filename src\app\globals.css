@import '../styles/font-fallbacks.css';
@import '../styles/local-fonts.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

/* تأثيرات مخصصة للبطاقات */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 3s linear infinite;
}

/* تحريك ظهور الكروت */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* إطار ملون دائر للكروت */
@keyframes rotating-border {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* تأثير الكارت المحسن */
.cost-card {
  position: relative;
  overflow: hidden;
}

.cost-card::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: inherit;
  background: conic-gradient(from 0deg, transparent 70%, var(--border-color) 100%);
  animation: rotating-border 2s linear infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.cost-card:hover::before {
  opacity: 1;
}

:root {
  --foreground-color: #000000;
  --background-color: #ffffff;
  --border-color: #e5e7eb;
}

/* أنيميشن دوران الحدود */
@keyframes rotating-border {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* كلاس للبوكسات مع حدود متحركة */
.animated-box {
  position: relative;
  filter: drop-shadow(0 15px 50px rgba(0, 0, 0, 0.3));
  transition: all 0.3s ease;
  overflow: hidden;
}

.animated-box::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    transparent,
    transparent,
    transparent,
    #3b82f6,
    #8b5cf6,
    #06b6d4,
    #10b981,
    transparent,
    transparent,
    transparent
  );
  z-index: -2;
  animation: rotating-border 4s linear infinite;
  animation-delay: -1s;
}

.animated-box::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  background: inherit;
  border-radius: inherit;
  z-index: -1;
}

/* تأثير hover للبوكسات */
.animated-box:hover {
  filter: drop-shadow(0 15px 40px rgba(0, 0, 0, 0.3));
  transform: scale(1.02);
}

.animated-box:hover::before {
  animation-duration: 3s;
}

/* تأثير hover عام لجميع البطاقات */
.pulse-box:hover, .success-box:hover, .warning-box:hover, .danger-box:hover {
  transform: scale(1.05);
  transition: all 0.3s ease-in-out;
}

.pulse-box, .success-box, .warning-box, .danger-box {
  transition: all 0.3s ease-in-out;
}

/* أنيميشن أكثر هدوءاً للبوكسات الحساسة */
.gentle-animated-box {
  position: relative;
  filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.2));
  transition: all 0.3s ease;
  overflow: hidden;
}

.gentle-animated-box::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    from 0deg,
    transparent 85%,
    rgba(255, 255, 255, 0.3) 90%,
    rgba(255, 255, 255, 0.5) 95%,
    transparent 100%
  );
  z-index: -2;
  animation: rotating-border 8s linear infinite;
  animation-delay: -2s;
}

.gentle-animated-box::after {
  content: '';
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  bottom: 1px;
  background: inherit;
  border-radius: inherit;
  z-index: -1;
}

/* تأثير hover للبوكسات الهادئة */
.gentle-animated-box:hover {
  filter: drop-shadow(0 12px 35px rgba(0, 0, 0, 0.25));
  transform: scale(1.01);
}

.gentle-animated-box:hover::before {
  animation-duration: 6s;
}

/* CSS Custom Property for angle */
@property --angle {
  syntax: "<angle>";
  initial-value: 0deg;
  inherits: false;
}

/* تأثير خط نيون دوار للبطاقات */
.pulse-box {
  --border-width: 4px;
  --card-color: #3b82f6;
  position: relative;
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow:
    0 0 30px rgba(59, 130, 246, 0.5) !important,
    0 0 60px rgba(59, 130, 246, 0.3) !important,
    0 0 90px rgba(59, 130, 246, 0.2) !important;
  filter: drop-shadow(0 0 15px rgba(59, 130, 246, 0.4)) !important;
}

.pulse-box::before {
  content: '';
  position: absolute;
  inset: -4px;
  border-radius: inherit;
  background: conic-gradient(
    from var(--angle),
    transparent 88%,
    var(--card-color) 94%,
    transparent 100%
  );
  opacity: 1;
  z-index: -1;
  animation: neon-rotate 3s linear infinite;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  padding: 4px;
  filter: drop-shadow(0 0 12px var(--card-color));
}

/* ألوان مختلفة للبطاقات مع ظلال متوهجة قوية */
.pulse-box-red {
  --card-color: #ef4444;
  box-shadow:
    0 0 30px rgba(239, 68, 68, 0.5) !important,
    0 0 60px rgba(239, 68, 68, 0.3) !important,
    0 0 90px rgba(239, 68, 68, 0.2) !important;
  filter: drop-shadow(0 0 15px rgba(239, 68, 68, 0.4)) !important;
}

.pulse-box-yellow {
  --card-color: #f59e0b;
  box-shadow:
    0 0 30px rgba(245, 158, 11, 0.5) !important,
    0 0 60px rgba(245, 158, 11, 0.3) !important,
    0 0 90px rgba(245, 158, 11, 0.2) !important;
  filter: drop-shadow(0 0 15px rgba(245, 158, 11, 0.4)) !important;
}

.pulse-box-green {
  --card-color: #10b981;
  box-shadow:
    0 0 30px rgba(16, 185, 129, 0.5) !important,
    0 0 60px rgba(16, 185, 129, 0.3) !important,
    0 0 90px rgba(16, 185, 129, 0.2) !important;
  filter: drop-shadow(0 0 15px rgba(16, 185, 129, 0.4)) !important;
}

.pulse-box-purple {
  --card-color: #8b5cf6;
  box-shadow:
    0 0 30px rgba(139, 92, 246, 0.5) !important,
    0 0 60px rgba(139, 92, 246, 0.3) !important,
    0 0 90px rgba(139, 92, 246, 0.2) !important;
  filter: drop-shadow(0 0 15px rgba(139, 92, 246, 0.4)) !important;
}

.pulse-box-gray {
  --card-color: #6b7280;
  box-shadow:
    0 0 30px rgba(107, 114, 128, 0.5) !important,
    0 0 60px rgba(107, 114, 128, 0.3) !important,
    0 0 90px rgba(107, 114, 128, 0.2) !important;
  filter: drop-shadow(0 0 15px rgba(107, 114, 128, 0.4)) !important;
}

.pulse-box-orange {
  --card-color: #f97316;
  box-shadow:
    0 0 30px rgba(249, 115, 22, 0.5) !important,
    0 0 60px rgba(249, 115, 22, 0.3) !important,
    0 0 90px rgba(249, 115, 22, 0.2) !important;
  filter: drop-shadow(0 0 15px rgba(249, 115, 22, 0.4)) !important;
}

.pulse-box-pink {
  --card-color: #ec4899;
  box-shadow:
    0 0 30px rgba(236, 72, 153, 0.5) !important,
    0 0 60px rgba(236, 72, 153, 0.3) !important,
    0 0 90px rgba(236, 72, 153, 0.2) !important;
  filter: drop-shadow(0 0 15px rgba(236, 72, 153, 0.4)) !important;
}

.pulse-box-cyan {
  --card-color: #06b6d4;
  box-shadow:
    0 0 30px rgba(6, 182, 212, 0.5) !important,
    0 0 60px rgba(6, 182, 212, 0.3) !important,
    0 0 90px rgba(6, 182, 212, 0.2) !important;
  filter: drop-shadow(0 0 15px rgba(6, 182, 212, 0.4)) !important;
}

.pulse-box-indigo {
  --card-color: #6366f1;
  box-shadow:
    0 0 30px rgba(99, 102, 241, 0.5) !important,
    0 0 60px rgba(99, 102, 241, 0.3) !important,
    0 0 90px rgba(99, 102, 241, 0.2) !important;
  filter: drop-shadow(0 0 15px rgba(99, 102, 241, 0.4)) !important;
}

@keyframes neon-rotate {
  from {
    --angle: 0deg;
  }
  to {
    --angle: 360deg;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    filter: drop-shadow(0 15px 50px rgba(59, 130, 246, 0.3));
  }
  50% {
    filter: drop-shadow(0 20px 60px rgba(59, 130, 246, 0.6));
  }
}

@keyframes pulse-gradient {
  0%, 100% {
    background-position: 0% 50%;
    opacity: 0.8;
  }
  50% {
    background-position: 100% 50%;
    opacity: 1;
  }
}

/* أنيميشن تموج للبوكسات الإحصائية */
.wave-box {
  position: relative;
  animation: wave-effect 6s ease-in-out infinite;
  overflow: hidden;
}

.wave-box::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    transparent,
    #10b981,
    #059669,
    #34d399,
    #6ee7b7,
    transparent,
    transparent,
    transparent
  );
  z-index: -2;
  animation: rotating-border 6s linear infinite;
}

.wave-box::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  background: inherit;
  border-radius: inherit;
  z-index: -1;
}

@keyframes wave-effect {
  0%, 100% {
    transform: translateY(0px);
    filter: drop-shadow(0 15px 50px rgba(0, 0, 0, 0.3));
  }
  25% {
    transform: translateY(-3px);
    filter: drop-shadow(0 20px 55px rgba(0, 0, 0, 0.35));
  }
  75% {
    transform: translateY(3px);
    filter: drop-shadow(0 10px 45px rgba(0, 0, 0, 0.25));
  }
}

@keyframes wave-gradient {
  0%, 100% {
    background-position: 0% 50%;
  }
  33% {
    background-position: 100% 0%;
  }
  66% {
    background-position: 0% 100%;
  }
}

/* تأثير للبوكسات الناجحة */
.success-box {
  position: relative;
  filter: drop-shadow(0 15px 50px rgba(34, 197, 94, 0.3));
  overflow: hidden;
}

.success-box::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    transparent,
    transparent,
    #10b981,
    #059669,
    #34d399,
    #6ee7b7,
    transparent,
    transparent
  );
  z-index: -2;
  animation: rotating-border 5s linear infinite;
  animation-delay: -1.5s;
}

.success-box::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  background: inherit;
  border-radius: inherit;
  z-index: -1;
}

/* تأثير للبوكسات التحذيرية */
.warning-box {
  position: relative;
  filter: drop-shadow(0 15px 50px rgba(245, 158, 11, 0.3));
  overflow: hidden;
}

.warning-box::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    transparent,
    #f59e0b,
    #d97706,
    #fbbf24,
    #fcd34d,
    transparent,
    transparent
  );
  z-index: -2;
  animation: rotating-border 3s linear infinite;
  animation-delay: -0.5s;
}

.warning-box::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  background: inherit;
  border-radius: inherit;
  z-index: -1;
}

/* تأثير للبوكسات الخطر */
.danger-box {
  position: relative;
  filter: drop-shadow(0 15px 50px rgba(239, 68, 68, 0.3));
  overflow: hidden;
}

.danger-box::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: conic-gradient(
    transparent,
    #ef4444,
    #dc2626,
    #f87171,
    transparent,
    transparent
  );
  z-index: -2;
  animation: rotating-border 2s linear infinite;
  animation-delay: -0.8s;
}

.danger-box::after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  background: inherit;
  border-radius: inherit;
  z-index: -1;
}

/* Dark Mode Variables */
:root {
  --bg-primary: #f9fafb;
  --bg-secondary: #ffffff;
  --bg-tertiary: #f3f4f6;
  --text-primary: #111827;
  --text-secondary: #374151;
  --text-tertiary: #4b5563;
  --border-primary: #d1d5db;
  --shadow-color: rgba(0, 0, 0, 0.1);
}

.dark {
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --bg-tertiary: #374151;
  --bg-hover: #4b5563; /* لون التظليل المحسن */
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-tertiary: #9ca3af;
  --border-primary: #4b5563;
  --shadow-color: rgba(0, 0, 0, 0.3);
}

/* Dark Mode Styles */
.dark body {
  background-color: var(--bg-primary);
  color: var(--text-primary);
}

.dark .bg-white {
  background-color: var(--bg-secondary) !important;
}

.dark .bg-gray-50 {
  background-color: var(--bg-primary) !important;
}

.dark .bg-gray-100 {
  background-color: var(--bg-tertiary) !important;
}

.dark .text-gray-800 {
  color: var(--text-primary) !important;
}

.dark .text-gray-600 {
  color: var(--text-secondary) !important;
}

.dark .text-gray-500 {
  color: var(--text-tertiary) !important;
}

/* تحسين تباين الألوان في الوضع النهاري */
.text-gray-600 {
  color: var(--text-secondary) !important;
}

.text-gray-500 {
  color: var(--text-tertiary) !important;
}

.text-gray-400 {
  color: #6b7280 !important;
}

/* تحسين ألوان النصوص في البطاقات */
.card-text-primary {
  color: var(--text-primary) !important;
}

.card-text-secondary {
  color: var(--text-secondary) !important;
}

.card-text-tertiary {
  color: var(--text-tertiary) !important;
}

/* تحسين مظهر البطاقات في الوضع النهاري */
.pulse-box, .success-box, .warning-box, .danger-box {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
}

/* تعديل ألوان الحدود للبطاقات */
.pulse-box::before, .success-box::before, .warning-box::before, .danger-box::before {
  opacity: 0.5;
}

/* تحسين تباين ألوان النصوص في البطاقات للوضع النهاري */
.text-blue-700, .text-green-700, .text-red-700, .text-yellow-700,
.text-purple-700, .text-indigo-700, .text-pink-700, .text-orange-700 {
  font-weight: 700;
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.1);
}

/* تحسين تباين ألوان الأيقونات في البطاقات للوضع النهاري */
.bg-gradient-to-br.bg-opacity-40 {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);
}

/* تحسين مظهر البطاقات في الوضع النهاري */
.pulse-box, .success-box, .warning-box, .danger-box {
  background-color: var(--bg-secondary) !important;
  color: var(--text-primary) !important;
}

/* تعديل ألوان الحدود للبطاقات */
.pulse-box::before, .success-box::before, .warning-box::before, .danger-box::before {
  opacity: 0.5;
}

.dark .border-gray-300 {
  border-color: var(--border-primary) !important;
}

.dark .shadow-sm {
  box-shadow: 0 1px 2px 0 var(--shadow-color) !important;
}

.dark .shadow-lg {
  box-shadow: 0 10px 15px -3px var(--shadow-color), 0 4px 6px -2px var(--shadow-color) !important;
}

/* Dark Mode Input Styles */
.dark input[type="text"],
.dark input[type="email"],
.dark input[type="tel"],
.dark input[type="date"],
.dark select,
.dark textarea {
  background-color: var(--bg-tertiary) !important;
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

.dark input[type="text"]:focus,
.dark input[type="email"]:focus,
.dark input[type="tel"]:focus,
.dark input[type="date"]:focus,
.dark select:focus,
.dark textarea:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.dark input::placeholder {
  color: #9ca3af !important;
}

/* Dark Mode Button Hover Effects */
.dark .hover\\:bg-gray-100:hover {
  background-color: var(--bg-tertiary) !important;
}

.dark .hover\\:bg-gray-50:hover {
  background-color: var(--bg-tertiary) !important;
}

/* تحسين تأثيرات التظليل في الوضع الليلي للجداول */
.dark tr.hover\\:bg-gray-50:hover,
.dark .hover\\:bg-gray-50:hover {
  background-color: #4b5563 !important; /* لون أفتح من الخلفية الأساسية */
}

.dark tr:hover {
  background-color: #4b5563 !important; /* لون أفتح من الخلفية الأساسية */
}

/* تحسين تأثيرات التظليل للبطاقات والعناصر التفاعلية */
.dark .hover\\:bg-blue-50:hover {
  background-color: #1e3a8a !important; /* أزرق داكن مع شفافية */
}

.dark .hover\\:bg-green-50:hover {
  background-color: #166534 !important; /* أخضر داكن مع شفافية */
}

.dark .hover\\:bg-red-50:hover {
  background-color: #991b1b !important; /* أحمر داكن مع شفافية */
}

.dark .hover\\:bg-purple-50:hover {
  background-color: #6b21a8 !important; /* بنفسجي داكن مع شفافية */
}

.dark .hover\\:bg-yellow-50:hover {
  background-color: #a16207 !important; /* أصفر داكن مع شفافية */
}

/* Dark Mode Table Styles */
.dark table {
  background-color: var(--bg-secondary) !important;
}

.dark th {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
}

.dark td {
  color: var(--text-primary) !important;
}

/* تحسين تظليل صفوف الجداول في الوضع الليلي */
.dark tbody tr:hover {
  background-color: var(--bg-hover) !important;
}

.dark tbody tr:hover td {
  color: var(--text-primary) !important;
}

/* منع انقسام النصوص في الجداول */
.table-no-truncate td {
  white-space: nowrap;
  overflow: visible;
  text-overflow: clip;
}

/* منع انقسام كلمة الإجمالي */
.no-break-text {
  white-space: nowrap !important;
  word-break: keep-all !important;
  overflow-wrap: normal !important;
}

/* تحسين تظليل البطاقات والعناصر التفاعلية */
.dark .transition-colors:hover {
  background-color: var(--bg-hover) !important;
}

/* تحسين ألوان النصوص في التظليل */
.dark tr:hover .text-gray-900,
.dark tr:hover .text-gray-800,
.dark tr:hover .text-gray-700 {
  color: var(--text-primary) !important;
}

.dark tr:hover .text-gray-600,
.dark tr:hover .text-gray-500 {
  color: var(--text-secondary) !important;
}

/* تحسين تباين الألوان للعناصر التفاعلية في الوضع الليلي */
.dark .hover\\:bg-gray-100:hover {
  background-color: #4b5563 !important;
}

.dark .hover\\:bg-gray-200:hover {
  background-color: #6b7280 !important;
}

/* تحسين ألوان الحدود في الوضع الليلي */
.dark .border-gray-100 {
  border-color: #4b5563 !important;
}

.dark .border-gray-200 {
  border-color: #6b7280 !important;
}

/* تحسين خلفيات العناصر في الوضع الليلي */
.dark .bg-gray-50 {
  background-color: #374151 !important;
}

.dark .bg-gray-100 {
  background-color: #4b5563 !important;
}

.dark .bg-gray-200 {
  background-color: #6b7280 !important;
}

/* تحسين تأثيرات التظليل المحددة للجداول */
.dark table tbody tr:hover {
  background-color: #4b5563 !important;
  color: #f9fafb !important;
}

.dark table tbody tr:hover td {
  color: #f9fafb !important;
}

.dark table tbody tr:hover .text-gray-500,
.dark table tbody tr:hover .text-gray-600,
.dark table tbody tr:hover .text-gray-700,
.dark table tbody tr:hover .text-gray-800,
.dark table tbody tr:hover .text-gray-900 {
  color: #f9fafb !important;
}

/* تحسين تباين الأزرار في التظليل */
.dark tr:hover button {
  opacity: 1 !important;
}

.dark tr:hover .text-blue-600 {
  color: #60a5fa !important;
}

.dark tr:hover .text-green-600 {
  color: #4ade80 !important;
}

.dark tr:hover .text-red-600 {
  color: #f87171 !important;
}

.dark tr:hover .text-purple-600 {
  color: #c084fc !important;
}

/* تحسين ألوان البطاقات الملونة في الوضع الليلي */
.dark .bg-blue-100 {
  background-color: #1e3a8a !important;
  color: #dbeafe !important;
}

.dark .bg-green-100 {
  background-color: #166534 !important;
  color: #dcfce7 !important;
}

.dark .bg-red-100 {
  background-color: #991b1b !important;
  color: #fecaca !important;
}

.dark .bg-yellow-100 {
  background-color: #a16207 !important;
  color: #fef3c7 !important;
}

.dark .bg-purple-100 {
  background-color: #6b21a8 !important;
  color: #e9d5ff !important;
}

.dark .bg-gray-100 {
  background-color: #4b5563 !important;
  color: #f3f4f6 !important;
}

/* تحسين ألوان النصوص الملونة في الوضع الليلي */
.dark .text-blue-800 {
  color: #dbeafe !important;
}

.dark .text-green-800 {
  color: #dcfce7 !important;
}

.dark .text-red-800 {
  color: #fecaca !important;
}

.dark .text-yellow-800 {
  color: #fef3c7 !important;
}

.dark .text-purple-800 {
  color: #e9d5ff !important;
}

.dark .text-gray-800 {
  color: #f3f4f6 !important;
}

/* تحسين عرض الجداول لإظهار البيانات كاملة - قواعد عامة */
table {
  table-layout: auto !important;
  width: 100% !important;
  min-width: 100% !important;
}

/* منع كسر النصوص في جميع خلايا الجداول */
table td, table th {
  white-space: nowrap !important;
  overflow: visible !important;
  text-overflow: clip !important;
  word-break: keep-all !important;
  overflow-wrap: normal !important;
  hyphens: none !important;
  line-height: 1.5 !important;
  padding: 8px 12px !important;
  vertical-align: middle !important;
}

/* تطبيق على جميع عناصر النصوص داخل الجداول */
table td *, table th * {
  white-space: nowrap !important;
  word-break: keep-all !important;
  overflow-wrap: normal !important;
}

/* منع قطع النصوص في الجداول */
.table-no-truncate td, .table-no-truncate th {
  white-space: nowrap !important;
  overflow: visible !important;
  text-overflow: clip !important;
  word-break: keep-all !important;
  overflow-wrap: normal !important;
}

.table-no-truncate .truncate {
  white-space: nowrap !important;
  overflow: visible !important;
  text-overflow: clip !important;
}

/* إزالة أي تأثيرات truncate موجودة */
.truncate {
  white-space: nowrap !important;
  overflow: visible !important;
  text-overflow: clip !important;
}

/* تحسين عرض النصوص الطويلة - فقط خارج الجداول */
.break-words:not(table *) {
  word-wrap: break-word !important;
  word-break: break-word !important;
  overflow-wrap: break-word !important;
  hyphens: auto !important;
}

/* قواعد إضافية لمنع كسر النصوص في جميع أنحاء النظام */
.no-text-wrap, .no-text-wrap * {
  white-space: nowrap !important;
  word-break: keep-all !important;
  overflow-wrap: normal !important;
  hyphens: none !important;
}

/* تطبيق على جميع عناصر الجداول بشكل قوي */
tbody td, thead th, tfoot td {
  white-space: nowrap !important;
  word-break: keep-all !important;
  overflow-wrap: normal !important;
  text-overflow: clip !important;
}

/* منع كسر النصوص في العناصر التفاعلية */
button, .btn, .button {
  white-space: nowrap !important;
  word-break: keep-all !important;
}

/* منع كسر النصوص في البطاقات والعناصر المهمة */
.card-title, .card-header, .card-content {
  white-space: nowrap !important;
  word-break: keep-all !important;
}

/* تحسين عرض الجداول لتجنب الحاجة للـ scroll */
table.table-fixed {
  table-layout: fixed !important;
  width: 100% !important;
}

table.table-fixed td, table.table-fixed th {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  padding: 0.5rem !important;
}

/* تحسين عرض الجداول على الشاشات الصغيرة */
@media (max-width: 768px) {
  table {
    font-size: 0.75rem !important;
  }

  table td, table th {
    padding: 0.25rem !important;
    white-space: nowrap !important;
  }

  /* تقليل حجم النص في الجداول الصغيرة */
  .table-responsive {
    font-size: 0.7rem !important;
  }

  .table-responsive td, .table-responsive th {
    padding: 0.2rem !important;
  }
}

/* تحسينات إضافية للتظليل في الوضع الليلي */
.dark .hover\:bg-gray-50:hover {
  background-color: #6b7280 !important;
  color: #f9fafb !important;
}

.dark .hover\:bg-gray-100:hover {
  background-color: #6b7280 !important;
  color: #f9fafb !important;
}

.dark .hover\:bg-blue-50:hover {
  background-color: #2563eb !important;
  color: #eff6ff !important;
}

.dark .hover\:bg-green-50:hover {
  background-color: #16a34a !important;
  color: #f0fdf4 !important;
}

.dark .hover\:bg-red-50:hover {
  background-color: #dc2626 !important;
  color: #fef2f2 !important;
}

.dark .hover\:bg-purple-50:hover {
  background-color: #7c3aed !important;
  color: #faf5ff !important;
}

/* تحسين تباين الجداول في الوضع الليلي */
.dark table tbody tr:hover {
  background-color: #6b7280 !important;
  color: #f9fafb !important;
}

.dark table tbody tr:hover td {
  color: #f9fafb !important;
}

.dark table tbody tr:hover .text-blue-700 {
  color: #93c5fd !important;
}

.dark table tbody tr:hover .text-green-800 {
  color: #86efac !important;
}

.dark table tbody tr:hover .text-red-600 {
  color: #fca5a5 !important;
}

.dark table tbody tr:hover .text-purple-600 {
  color: #c4b5fd !important;
}

.dark table tbody tr:hover .text-gray-500 {
  color: #d1d5db !important;
}

.dark table tbody tr:hover .text-gray-600 {
  color: #e5e7eb !important;
}

.dark table tbody tr:hover .text-gray-700 {
  color: #f3f4f6 !important;
}

.dark table tbody tr:hover .text-gray-800 {
  color: #f9fafb !important;
}

.dark table tbody tr:hover .text-gray-900 {
  color: #ffffff !important;
}

/* تحسين تباين النصوص في البطاقات */
.dark .bg-gray-50 {
  background-color: #4b5563 !important;
  color: #f9fafb !important;
}

.dark .bg-blue-50 {
  background-color: #1d4ed8 !important;
  color: #eff6ff !important;
}

.dark .bg-green-50 {
  background-color: #15803d !important;
  color: #f0fdf4 !important;
}

.dark .bg-red-50 {
  background-color: #dc2626 !important;
  color: #fef2f2 !important;
}

.dark .bg-purple-50 {
  background-color: #7c3aed !important;
  color: #faf5ff !important;
}

/* تحسين تباين الأزرار في الوضع الليلي */
.dark button:hover {
  filter: brightness(1.2) !important;
}

.dark .text-blue-600:hover {
  color: #93c5fd !important;
}

.dark .text-green-600:hover {
  color: #86efac !important;
}

.dark .text-red-600:hover {
  color: #fca5a5 !important;
}

.dark .text-purple-600:hover {
  color: #c4b5fd !important;
}

.dark .text-gray-600:hover {
  color: #e5e7eb !important;
}

.dark td {
  border-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

/* Dark Mode Card Styles */
.dark .card {
  background-color: var(--bg-secondary) !important;
  border-color: var(--border-primary) !important;
}

/* Smooth Transitions */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Prevent Horizontal Overflow */
html, body {
  overflow-x: hidden;
  max-width: 100vw;
}

* {
  box-sizing: border-box;
}

/* Responsive Container */
.container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* Fix for form elements */
input, select, textarea, button {
  max-width: 100%;
  word-wrap: break-word;
}

/* Grid responsive fixes */
.grid {
  width: 100%;
  max-width: 100%;
}

/* Flex responsive fixes */
.flex {
  flex-wrap: wrap;
}

/* Button responsive fixes */
button {
  word-break: break-word;
  hyphens: auto;
}

/* تحسين وضوح النصوص في وضع النهار */
.text-black {
  font-weight: 900;
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.01em;
}

/* تحسين وضوح الأيقونات في وضع النهار */
.bg-opacity-70 {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2), 0 0 0 2px rgba(0, 0, 0, 0.08);
}

/* تقليل شفافية الحدود المتحركة */
.animated-box::before {
  opacity: 0.3;
}

[class*="text-blue-900"],
[class*="text-green-900"],
[class*="text-red-900"],
[class*="text-yellow-900"],
[class*="text-purple-900"],
[class*="text-indigo-900"],
[class*="text-pink-900"],
[class*="text-orange-900"] {
  font-weight: 800;
  text-shadow: 0 0 1px rgba(0, 0, 0, 0.1);
}

/* تحسين وضوح الأيقونات في وضع النهار */
.bg-opacity-60 {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(0, 0, 0, 0.05);
}

/* تقليل شفافية الحدود المتحركة */
.animated-box::before {
  opacity: 0.4;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* قواعد نهائية وقوية لمنع كسر النصوص في جميع الجداول */
table, table * {
  white-space: nowrap !important;
  word-break: keep-all !important;
  overflow-wrap: normal !important;
  hyphens: none !important;
  text-overflow: clip !important;
}

/* تطبيق على جميع عناصر الجداول بقوة أكبر */
table td, table th,
tbody td, tbody th,
thead td, thead th,
tfoot td, tfoot th {
  white-space: nowrap !important;
  word-break: keep-all !important;
  overflow-wrap: normal !important;
  text-overflow: clip !important;
  overflow: visible !important;
  max-width: none !important;
  min-width: auto !important;
}

/* إزالة أي تأثيرات truncate أو ellipsis */
.truncate, .text-ellipsis, .overflow-hidden {
  white-space: nowrap !important;
  overflow: visible !important;
  text-overflow: clip !important;
}

/* تطبيق على جميع النصوص داخل الجداول */
table span, table div, table p, table a, table button {
  white-space: nowrap !important;
  word-break: keep-all !important;
  overflow-wrap: normal !important;
}
