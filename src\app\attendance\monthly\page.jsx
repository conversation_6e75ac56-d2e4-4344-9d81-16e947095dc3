'use client';

import { useState, useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { useLanguage } from '@/contexts/LanguageContext';
import MainLayout from '@/components/MainLayout';
import DateInput from '@/components/DateInput';
import { 
  Calendar, 
  Clock, 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  FileText, 
  Download,
  Plus,
  Edit,
  Trash2,
  Search,
  Filter
} from 'lucide-react';

function MonthlyEffects() {
  const { isDarkMode } = useTheme();
  const { isRTL, isArabic } = useLanguage();
  
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [loading, setLoading] = useState(false);
  const [effects, setEffects] = useState([]);
  const [summary, setSummary] = useState({
    totalEmployees: 0,
    totalMissions: 0,
    totalUnpaidLeave: 0,
    totalSickLeave: 0,
    totalPenalties: 0,
    totalHolidayWork: 0,
    totalRestAllowance: 0,
    totalPositive: 0,
    totalNegative: 0,
    netEffect: 0
  });
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingEffect, setEditingEffect] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    effectType: '',
    department: '',
    employeeCode: ''
  });

  // أنواع المؤثرات الشهرية
  const effectTypes = [
    { value: 'missions', label: 'المأموريات', color: 'blue', icon: TrendingUp, type: 'positive' },
    { value: 'unpaid_leave', label: 'الإجازات بدون أجر', color: 'red', icon: TrendingDown, type: 'negative' },
    { value: 'sick_leave', label: 'إجازات مرضية', color: 'orange', icon: TrendingDown, type: 'negative' },
    { value: 'penalties', label: 'جزاءات', color: 'red', icon: TrendingDown, type: 'negative' },
    { value: 'holiday_work', label: 'العمل خلال يوم إجازة رسمي', color: 'green', icon: TrendingUp, type: 'positive' },
    { value: 'rest_allowance', label: 'رصيد بدل الراحات', color: 'purple', icon: DollarSign, type: 'positive' }
  ];

  // تحويل التاريخ من DD/MM/YYYY إلى YYYY-MM-DD للحسابات
  const convertDateForCalculation = (dateStr) => {
    if (!dateStr || dateStr === 'undefined' || dateStr === 'null') return '';
    if (typeof dateStr === 'string' && dateStr.includes('/')) {
      const parts = dateStr.split('/');
      if (parts.length === 3) {
        const [day, month, year] = parts;
        if (day && month && year) {
          return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        }
      }
    }
    return dateStr;
  };

  // تحويل التاريخ من YYYY-MM-DD إلى DD/MM/YYYY للعرض
  const convertDateForDisplay = (dateStr) => {
    if (!dateStr || dateStr === 'undefined' || dateStr === 'null') return '';
    if (typeof dateStr === 'string' && dateStr.includes('-') && dateStr.length === 10) {
      const parts = dateStr.split('-');
      if (parts.length === 3) {
        const [year, month, day] = parts;
        if (year && month && day) {
          return `${day}/${month}/${year}`;
        }
      }
    }
    return dateStr;
  };

  // حساب فترة تقفيل السجلات الشهرية (من 11 إلى 10)
  const getMonthlyPeriod = (month, year) => {
    // فترة الشهر من 11 الشهر السابق إلى 10 الشهر الحالي
    const startMonth = month === 1 ? 12 : month - 1;
    const startYear = month === 1 ? year - 1 : year;
    const endMonth = month;
    const endYear = year;

    const startDate = `${startYear}-${startMonth.toString().padStart(2, '0')}-11`;
    const endDate = `${endYear}-${endMonth.toString().padStart(2, '0')}-10`;

    return {
      startDate,
      endDate,
      periodText: `من 11/${startMonth}/${startYear} إلى 10/${endMonth}/${endYear}`
    };
  };

  // الحصول على فترة الشهر المحدد
  const currentPeriod = getMonthlyPeriod(selectedMonth, selectedYear);

  useEffect(() => {
    loadMonthlyEffects();
  }, [selectedMonth, selectedYear]);

  const loadMonthlyEffects = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/monthly-effects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'list',
          month: selectedMonth,
          year: selectedYear,
          periodStart: currentPeriod.startDate,
          periodEnd: currentPeriod.endDate,
          ...filters
        })
      });

      const result = await response.json();
      
      if (result.success) {
        setEffects(result.data || []);
        setSummary(result.summary || {});
      } else {

      }
    } catch (error) {

    }
    setLoading(false);
  };

  // تنسيق العملة
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(amount || 0);
  };

  // تنسيق التاريخ
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG');
  };

  // الحصول على لون نوع المؤثر
  const getEffectTypeConfig = (type) => {
    return effectTypes.find(et => et.value === type) || effectTypes[0];
  };

  // فلترة المؤثرات
  const filteredEffects = effects.filter(effect => {
    const matchesSearch = !searchTerm || 
      effect.EmployeeName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      effect.EmployeeCode?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = !filters.effectType || effect.EffectType === filters.effectType;
    const matchesDepartment = !filters.department || effect.Department === filters.department;
    const matchesEmployee = !filters.employeeCode || effect.EmployeeCode === filters.employeeCode;

    return matchesSearch && matchesType && matchesDepartment && matchesEmployee;
  });

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto">
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="flex items-center justify-between">
            <div>
              <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
                كشف المؤثرات الشهرية
              </h1>
              <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-600'} mb-2`}>
                إدارة ومتابعة المؤثرات الشهرية للموظفين (مأموريات، إجازات، جزاءات، بدل راحات)
              </p>
              <div className={`text-sm ${isDarkMode ? 'text-blue-400' : 'text-blue-600'} bg-blue-50 dark:bg-blue-900/20 px-3 py-2 rounded-lg inline-block`}>
                📅 فترة التقفيل: {currentPeriod.periodText}
              </div>
            </div>
            <div className="flex gap-3">
              <button
                onClick={() => setShowAddModal(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <Plus className="w-4 h-4" />
                إضافة مؤثر
              </button>
              <button
                onClick={loadMonthlyEffects}
                disabled={loading}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
              >
                <Download className="w-4 h-4" />
                {loading ? 'جاري التحميل...' : 'تصدير Excel'}
              </button>
            </div>
          </div>
        </div>

        {/* فلاتر الشهر والسنة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                الشهر
              </label>
              <select
                value={selectedMonth}
                onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                  isDarkMode 
                    ? 'bg-slate-800 border-slate-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                {[
                  { value: 1, label: 'يناير' },
                  { value: 2, label: 'فبراير' },
                  { value: 3, label: 'مارس' },
                  { value: 4, label: 'أبريل' },
                  { value: 5, label: 'مايو' },
                  { value: 6, label: 'يونيو' },
                  { value: 7, label: 'يوليو' },
                  { value: 8, label: 'أغسطس' },
                  { value: 9, label: 'سبتمبر' },
                  { value: 10, label: 'أكتوبر' },
                  { value: 11, label: 'نوفمبر' },
                  { value: 12, label: 'ديسمبر' }
                ].map(month => (
                  <option key={month.value} value={month.value}>
                    {month.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                السنة
              </label>
              <select
                value={selectedYear}
                onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                  isDarkMode 
                    ? 'bg-slate-800 border-slate-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - 2 + i).map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}

export default MonthlyEffects;
