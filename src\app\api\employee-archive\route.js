import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';
import fs from 'fs';
import path from 'path';

export async function POST(request) {
  try {
    const { action, employeeId } = await request.json();
    const pool = await getConnection();

    switch (action) {
      case 'getDocuments':
        if (!employeeId) {
          return NextResponse.json({
            success: false,
            error: 'معرف الموظف مطلوب'
          }, { status: 400 });
        }

        // استخدام EmployeeID مباشرة كما هو مطلوب
        const docsEmployeeID = employeeId;

        // جلب المسارات من جدول ARCHIV وبناء قائمة المستندات
        // تصفية البنود لإخفاء بنود التكاليف من أرشيف الموظف
        const archivQuery = `
          SELECT Item, Path FROM ARCHIV
          WHERE Item NOT IN ('carscost', 'housingcost', '3amala')
        `;

        const archivRequest = pool.request();
        const archivResult = await archivRequest.query(archivQuery);

        const documents = [];

        // بناء قائمة المستندات بناءً على المسارات الموجودة
        for (const archivItem of archivResult.recordset) {
          const item = archivItem.Item;
          const basePath = archivItem.Path;

          let documentType, icon, color, fileExtension;

          // تحديد نوع المستند والأيقونة واللون والامتداد بناءً على الأسماء الإنجليزية من جدول ARCHIV
          switch (item) {
            case 'NationalIDs':
              documentType = 'بطاقة الرقم القومى';
              icon = 'fa-id-card';
              color = '#2563eb';
              fileExtension = 'pdf';
              break;
            case 'WorkReceipts':
              documentType = 'إستلام العمل';
              icon = 'fa-handshake';
              color = '#ea580c';
              fileExtension = 'pdf';
              break;
            case 'StatusReports':
              documentType = 'بيان حالة إجتماعية';
              icon = 'fa-file-alt';
              color = '#7c3aed';
              fileExtension = 'pdf';
              break;
            case 'Bhousing':
              documentType = 'بدل إسكان';
              icon = 'fa-home';
              color = '#65a30d';
              fileExtension = 'pdf';
              break;
            case 'Btransport':
              documentType = 'بدل إنتقال';
              icon = 'fa-bus';
              color = '#be185d';
              fileExtension = 'pdf';
              break;
            case 'photo':
              documentType = 'صورة شخصية pdf';
              icon = 'fa-file-pdf';
              color = '#dc2626';
              fileExtension = 'pdf';
              break;
            case 'UnionCards':
              documentType = 'كارينة النقابة';
              icon = 'fa-id-badge';
              color = '#0891b2';
              fileExtension = 'pdf';
              break;
            case 'pic':
              documentType = 'صورة شخصية jpg';
              icon = 'fa-image';
              color = '#059669';
              fileExtension = 'jpg';
              break;
            // البنود الجديدة
            case 'Resignation':
              documentType = 'طلب الاستقالة';
              icon = 'fa-file-signature';
              color = '#dc2626';
              fileExtension = 'pdf';
              break;
            case 'Transfer':
              documentType = 'طلب النقل';
              icon = 'fa-exchange-alt';
              color = '#2563eb';
              fileExtension = 'pdf';
              break;
            case 'LastPeriod':
              documentType = 'بيان آخر فترة';
              icon = 'fa-chart-line';
              color = '#7c3aed';
              fileExtension = 'pdf';
              break;
            case 'Clearance':
              documentType = 'إخلاء الطرف';
              icon = 'fa-check-circle';
              color = '#059669';
              fileExtension = 'pdf';
              break;
            default:
              documentType = item;
              icon = 'fa-file';
              color = '#6b7280';
              fileExtension = 'pdf';
          }

          // بناء المسار الكامل للملف
          // إزالة الشرطة المائلة الأخيرة إذا كانت موجودة
          const cleanBasePath = basePath.endsWith('\\') ? basePath.slice(0, -1) : basePath;
          const filePath = `${cleanBasePath}\\${docsEmployeeID}.${fileExtension}`;

          documents.push({
            Item: item,
            DocumentType: documentType,
            Path: filePath,
            Icon: icon,
            Color: color,
            EmployeeID: docsEmployeeID
          });
        }

        return NextResponse.json({
          success: true,
          documents: documents
        });

      case 'getPhoto':
        if (!employeeId) {
          return NextResponse.json({
            success: false,
            error: 'معرف الموظف مطلوب'
          }, { status: 400 });
        }

        // البحث عن الصورة الشخصية بجميع الصيغ المدعومة
        const photoEmployeeID = employeeId;
        const imageExtensions = ['jpg', 'jpeg', 'png', 'gif'];
        const basePath = path.join(process.cwd(), 'public', 'archiv', 'pic');

        let foundPhotoPath = null;

        // البحث عن الصورة بأي صيغة مدعومة
        for (const ext of imageExtensions) {
          const fullPath = path.join(basePath, `${photoEmployeeID}.${ext}`);
          if (fs.existsSync(fullPath)) {
            foundPhotoPath = `archiv/pic/${photoEmployeeID}.${ext}`;
            break;
          }
        }

        if (foundPhotoPath) {
          return NextResponse.json({
            success: true,
            photoPath: foundPhotoPath
          });
        } else {
          return NextResponse.json({
            success: false,
            error: 'لم يتم العثور على صورة للموظف',
            searchedPaths: imageExtensions.map(ext => `archiv/pic/${photoEmployeeID}.${ext}`)
          });
        }

      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  }
}
