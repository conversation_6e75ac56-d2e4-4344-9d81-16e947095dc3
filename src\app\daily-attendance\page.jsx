'use client';
import React, { useState, useEffect } from 'react';
import { FiUpload, FiSave, FiPrinter, FiCalendar, FiDownload } from 'react-icons/fi';

const ATTENDANCE_OPTIONS = [
  { value: 'حضور', label: 'حضور' },
  { value: 'غياب', label: 'غياب' },
  { value: 'مأمورية', label: 'مأمورية' },
  { value: 'إجازة اعتيادية', label: 'إجازة اعتيادية' },
  { value: 'إجازة عارضة', label: 'إجازة عارضة' },
  { value: 'إجازة مرضية', label: 'إجازة مرضية' },
  { value: 'إجازة بدون أجر', label: 'إجازة بدون أجر' },
  { value: 'إجازة بدل', label: 'إجازة بدل' },
  { value: 'إجازة زواج', label: 'إجازة زواج' },
  { value: 'إجازة وفاة', label: 'إجازة وفاة' },
  { value: 'استقالة', label: 'استقالة' },
  { value: 'وردية ليلية', label: 'وردية ليلية' },
  { value: 'منقول', label: 'منقول' },
  { value: 'مستقيل', label: 'مستقيل' },
  { value: 'أخرى', label: 'أخرى' },
];

function DailyAttendancePage() {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [employees, setEmployees] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [attendanceData, setAttendanceData] = useState([]); // [{employeeCode, ... , attendance, checkIn, notes}]
  const [uploading, setUploading] = useState(false);
  const [printing, setPrinting] = useState(false);

  useEffect(() => {
    fetchEmployees();
    fetchAttendanceForDate(selectedDate);
  }, [selectedDate]);

  const fetchEmployees = async () => {
    setLoading(true);
    try {
      const res = await fetch('/api/employees');
      const data = await res.json();
      setEmployees(data);
      setAttendanceData(
        data.map((emp, idx) => ({
          ...emp,
          seq: idx + 1,
          attendance: '',
          checkIn: '',
          notes: '',
        }))
      );
    } catch (err) {
      setError('فشل في تحميل بيانات الموظفين');
    } finally {
      setLoading(false);
    }
  };

  const fetchAttendanceForDate = async (date) => {
    try {
      const res = await fetch(`/api/daily-attendance?date=${date}`);
      if (res.ok) {
        const data = await res.json();
        setAttendanceData(data);
      }
    } catch {}
  };

  const handleDownloadTemplate = () => {
    const csvContent = 'employeeCode,employeeName,checkIn\nEMP001,أحمد محمد,08:30\nEMP002,فاطمة علي,08:45\n';
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'daily_attendance_template.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    if (!file) return;
    setUploading(true);
    if (file.name.endsWith('.csv')) {
      const reader = new FileReader();
      reader.onload = (evt) => {
        const text = evt.target.result;
        const lines = text.split('\n').filter(Boolean);
        const header = lines[0].split(',');
        const rows = lines.slice(1).map((line) => {
          const [employeeCode, employeeName, checkIn] = line.split(',');
          return { employeeCode: employeeCode?.trim(), employeeName: employeeName?.trim(), checkIn: checkIn?.trim() };
        });
        setAttendanceData((prev) =>
          prev.map((emp) => {
            const found = rows.find((row) => row.employeeCode === emp.employeeCode);
            if (found) {
              return { ...emp, attendance: 'حضور', checkIn: found.checkIn || '' };
            }
            return emp;
          })
        );
      };
      reader.readAsText(file);
    } else {
      alert('يرجى رفع ملف CSV فقط حالياً');
    }
    setUploading(false);
  };

  const handleAttendanceChange = (idx, value) => {
    setAttendanceData((prev) =>
      prev.map((row, i) => (i === idx ? { ...row, attendance: value } : row))
    );
  };

  const handleCheckInChange = (idx, value) => {
    setAttendanceData((prev) =>
      prev.map((row, i) => (i === idx ? { ...row, checkIn: value } : row))
    );
  };

  const handleNotesChange = (idx, value) => {
    setAttendanceData((prev) =>
      prev.map((row, i) => (i === idx ? { ...row, notes: value } : row))
    );
  };

  const handleSave = async () => {
    const missing = attendanceData.filter((row) => !row.attendance || row.attendance === '');
    if (missing.length > 0) {
      alert(
        missing
          .map((row) => `⚠️ لم يتم تسجيل التمام للموظف "${row.employeeName}" عن يوم ${selectedDate}`)
          .join('\n')
      );
    }
    setLoading(true);
    try {
      await fetch('/api/daily-attendance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ date: selectedDate, attendance: attendanceData }),
      });
      alert('تم حفظ التمام بنجاح!');
      fetchAttendanceForDate(selectedDate);
    } catch {
      alert('فشل في حفظ التمام!');
    } finally {
      setLoading(false);
    }
  };

  const handlePrint = () => {
    setPrinting(true);
    setTimeout(() => {
      window.print();
      setPrinting(false);
    }, 500);
  };

  const filteredData = attendanceData.filter((emp) =>
    emp.employeeName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    emp.employeeCode?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <div dir="rtl" className="p-4 bg-gray-900 min-h-screen text-white">
      <div className="flex items-center gap-4 mb-4 flex-wrap">
        <label htmlFor="date" className="font-bold">اليوم:</label>
        <input
          id="date"
          type="date"
          value={selectedDate}
          onChange={(e) => setSelectedDate(e.target.value)}
          className="rounded px-2 py-1 text-black"
        />
        <label htmlFor="upload" className="flex items-center gap-2 cursor-pointer bg-blue-700 px-3 py-2 rounded text-white">
          <FiUpload /> رفع شيت البصمة
          <input id="upload" type="file" accept=".xlsx,.xls,.csv" onChange={handleFileUpload} className="hidden" />
        </label>
        <button onClick={handleDownloadTemplate} className="bg-gray-700 px-3 py-2 rounded flex items-center gap-2">
          <FiDownload /> تحميل نموذج شيت البصمة
        </button>
        <button onClick={handleSave} className="bg-green-600 px-4 py-2 rounded flex items-center gap-2">
          <FiSave /> تسجيل التمام
        </button>
        <button onClick={handlePrint} className="bg-blue-600 px-4 py-2 rounded flex items-center gap-2">
          <FiPrinter /> طباعة التمام اليومي
        </button>
        <input
          type="text"
          placeholder="بحث بالاسم أو الكود..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="rounded px-2 py-1 text-black ml-4"
        />
      </div>
      <div className="overflow-x-auto bg-gray-800 rounded-lg shadow p-2">
        <table className="min-w-full text-center">
          <thead>
            <tr>
              <th>م</th>
              <th>كود الموظف</th>
              <th>الاسم</th>
              <th>الوظيفة</th>
              <th>القسم</th>
              <th>التمام</th>
              <th>التوقيت</th>
              <th>ملاحظات</th>
            </tr>
          </thead>
          <tbody>
            {filteredData.map((emp, idx) => (
              <tr key={emp.employeeCode} className="bg-gray-700 border-b border-gray-600">
                <td>{emp.seq || idx + 1}</td>
                <td>{emp.employeeCode}</td>
                <td>{emp.employeeName}</td>
                <td>{emp.jobTitle}</td>
                <td>{emp.department}</td>
                <td>
                  <select
                    value={emp.attendance}
                    onChange={(e) => handleAttendanceChange(idx, e.target.value)}
                    className="rounded px-2 py-1 text-black"
                  >
                    <option value="">اختر</option>
                    {ATTENDANCE_OPTIONS.map((opt) => (
                      <option key={opt.value} value={opt.value}>{opt.label}</option>
                    ))}
                  </select>
                </td>
                <td>
                  <input
                    type="text"
                    value={emp.checkIn}
                    onChange={(e) => handleCheckInChange(idx, e.target.value)}
                    className="rounded px-2 py-1 text-black"
                    placeholder={emp.attendance === 'حضور' || emp.attendance === 'وردية ليلية' ? 'وقت الحضور (اختياري)' : 'يمكن كتابة نص (مأمورية/إجازة...)'}
                  />
                </td>
                <td>
                  <input
                    type="text"
                    value={emp.notes}
                    onChange={(e) => handleNotesChange(idx, e.target.value)}
                    className="rounded px-2 py-1 text-black"
                    placeholder="ملاحظات (مكان المأمورية/موعد انتهاء الإجازة...)"
                  />
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {loading && <div className="mt-4 text-center text-yellow-400">جاري التحميل...</div>}
      {error && <div className="mt-4 text-center text-red-400">{error}</div>}
    </div>
  );
}

export default DailyAttendancePage;
