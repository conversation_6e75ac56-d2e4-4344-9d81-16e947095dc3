import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function POST(request) {
  try {
    const body = await request.json();
    const { action } = body;

    const pool = await getConnection();

    switch (action) {
      case 'analyze':
        return await analyzeInconsistencies(pool);
      case 'fix-tables':
        return await fixTableStructures(pool);
      case 'fix-apis':
        return await fixApiConsistency(pool);
      case 'migrate-data':
        return await migrateExistingData(pool);
      case 'full-fix':
        return await performFullFix(pool);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في إصلاح قاعدة البيانات: ' + error.message
    }, { status: 500 });
  }
}

// تحليل التناقضات الحالية
async function analyzeInconsistencies(pool) {
  try {
    const issues = [];

    // فحص جداول الموظفين
    const employeeTableCheck = await pool.request().query(`
      SELECT 
        TABLE_NAME,
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME IN ('Employees', 'employees', 'employee_data')
      AND COLUMN_NAME LIKE '%Employee%'
      ORDER BY TABLE_NAME, COLUMN_NAME
    `);

    // فحص جداول الإجازات
    const leaveTableCheck = await pool.request().query(`
      SELECT 
        TABLE_NAME,
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME IN ('LeaveRequests', 'leave_requests', 'SimpleLeaves', 'LeaveBalances', 'leave_balances')
      ORDER BY TABLE_NAME, COLUMN_NAME
    `);

    // فحص جداول الشقق
    const apartmentTableCheck = await pool.request().query(`
      SELECT 
        TABLE_NAME,
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME IN ('Apartments', 'ApartmentBeneficiaries')
      AND COLUMN_NAME LIKE '%Apartment%'
      ORDER BY TABLE_NAME, COLUMN_NAME
    `);

    return NextResponse.json({
      success: true,
      analysis: {
        employeeTables: employeeTableCheck.recordset,
        leaveTables: leaveTableCheck.recordset,
        apartmentTables: apartmentTableCheck.recordset,
        issues: issues
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في تحليل التناقضات: ' + error.message
    }, { status: 500 });
  }
}

// إصلاح هياكل الجداول
async function fixTableStructures(pool) {
  try {
    const results = [];

    // 1. إنشاء جدول الموظفين الموحد (إذا لم يكن موجوداً)
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Employees' AND xtype='U')
      BEGIN
        CREATE TABLE Employees (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          EmployeeCode NVARCHAR(20) NOT NULL UNIQUE,
          EmployeeName NVARCHAR(100) NOT NULL,
          JobTitle NVARCHAR(100) NOT NULL,
          Department NVARCHAR(100),
          DirectManager NVARCHAR(100),
          HireDate DATE,
          BirthDate DATE,
          JoinDate DATE,
          NationalID NVARCHAR(20),
          Governorate NVARCHAR(50),
          MaritalStatus NVARCHAR(20),
          Gender NVARCHAR(10),
          CurrentStatus NVARCHAR(20) DEFAULT N'ساري',
          MilitaryService NVARCHAR(20),
          IsResidentEmployee BIT DEFAULT 0,
          CompanyHousing NVARCHAR(100),
          HousingCode NVARCHAR(20),
          TransportMethod NVARCHAR(100),
          Area NVARCHAR(100),
          Mobile NVARCHAR(20),
          Email NVARCHAR(100),
          Education NVARCHAR(100),
          University NVARCHAR(100),
          Major NVARCHAR(100),
          Grade NVARCHAR(50),
          Batch NVARCHAR(50),
          EmergencyNumber NVARCHAR(20),
          Kinship NVARCHAR(50),
          SocialInsurance NVARCHAR(50),
          SocialInsuranceDate DATE,
          SocialInsuranceNumber NVARCHAR(50),
          MedicalInsurance NVARCHAR(50),
          MedicalInsuranceNumber NVARCHAR(50),
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE()
        )
        
        CREATE INDEX IX_Employees_Code ON Employees(EmployeeCode)
        CREATE INDEX IX_Employees_Name ON Employees(EmployeeName)
        CREATE INDEX IX_Employees_Status ON Employees(CurrentStatus)
      END
    `);
    results.push({ table: 'Employees', status: 'تم التحقق/الإنشاء' });

    // 2. إنشاء جدول أرصدة الإجازات الموحد
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LeaveBalances' AND xtype='U')
      BEGIN
        CREATE TABLE LeaveBalances (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          EmployeeCode NVARCHAR(20) NOT NULL UNIQUE,
          EmployeeName NVARCHAR(100) NOT NULL,
          JobTitle NVARCHAR(100) NOT NULL,
          Department NVARCHAR(100),
          AnnualBalance INT DEFAULT 15,  -- رصيد إعتيادي
          CasualBalance INT DEFAULT 6,   -- رصيد عارضة
          UsedAnnual INT DEFAULT 0,      -- المستخدم من الإعتيادي
          UsedCasual INT DEFAULT 0,      -- المستخدم من العارضة
          RemainingAnnual AS (AnnualBalance - UsedAnnual),  -- المتبقي إعتيادي
          RemainingCasual AS (CasualBalance - UsedCasual),   -- المتبقي عارضة
          Year INT DEFAULT YEAR(GETDATE()),
          LastLeaveDate DATE,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),

          FOREIGN KEY (EmployeeCode) REFERENCES Employees(EmployeeCode) ON UPDATE CASCADE
        )

        CREATE INDEX IX_LeaveBalances_Employee ON LeaveBalances(EmployeeCode)
        CREATE INDEX IX_LeaveBalances_Year ON LeaveBalances(Year)
      END
    `);
    results.push({ table: 'LeaveBalances', status: 'تم التحقق/الإنشاء' });

    // 3. إنشاء جدول طلبات الإجازات الموحد
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LeaveRequests' AND xtype='U')
      BEGIN
        CREATE TABLE LeaveRequests (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          EmployeeCode NVARCHAR(20) NOT NULL,
          EmployeeName NVARCHAR(100) NOT NULL,
          JobTitle NVARCHAR(100) NOT NULL,
          Department NVARCHAR(100),
          Project NVARCHAR(100),
          LeaveType NVARCHAR(50) NOT NULL, -- إعتيادية، عارضة، مرضية، بدون أجر
          StartDate DATE NOT NULL,
          EndDate DATE NOT NULL,
          DaysCount INT NOT NULL,
          Reason NVARCHAR(MAX),
          Status NVARCHAR(20) DEFAULT N'قيد المراجعة', -- قيد المراجعة، معتمد، مرفوض
          RequestDate DATE DEFAULT GETDATE(),
          ApprovalDate DATE,
          RejectionDate DATE,
          ApprovedBy NVARCHAR(100),
          RejectionReason NVARCHAR(MAX),
          Notes NVARCHAR(MAX),
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),

          FOREIGN KEY (EmployeeCode) REFERENCES Employees(EmployeeCode) ON UPDATE CASCADE
        )

        CREATE INDEX IX_LeaveRequests_Employee ON LeaveRequests(EmployeeCode)
        CREATE INDEX IX_LeaveRequests_Status ON LeaveRequests(Status)
        CREATE INDEX IX_LeaveRequests_Type ON LeaveRequests(LeaveType)
        CREATE INDEX IX_LeaveRequests_Dates ON LeaveRequests(StartDate, EndDate)
      END
    `);
    results.push({ table: 'LeaveRequests', status: 'تم التحقق/الإنشاء' });

    // 4. إنشاء جدول الإجازات بدون أجر
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='UnpaidLeaves' AND xtype='U')
      BEGIN
        CREATE TABLE UnpaidLeaves (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          EmployeeCode NVARCHAR(20) NOT NULL,
          EmployeeName NVARCHAR(100) NOT NULL,
          JobTitle NVARCHAR(100) NOT NULL,
          Department NVARCHAR(100),
          StartDate DATE NOT NULL,
          EndDate DATE NOT NULL,
          DaysCount INT NOT NULL,
          Reason NVARCHAR(MAX),
          Status NVARCHAR(20) DEFAULT N'قيد المراجعة',
          RequestDate DATE DEFAULT GETDATE(),
          ApprovalDate DATE,
          ApprovedBy NVARCHAR(100),
          Notes NVARCHAR(MAX),
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),

          FOREIGN KEY (EmployeeCode) REFERENCES Employees(EmployeeCode) ON UPDATE CASCADE
        )

        CREATE INDEX IX_UnpaidLeaves_Employee ON UnpaidLeaves(EmployeeCode)
        CREATE INDEX IX_UnpaidLeaves_Status ON UnpaidLeaves(Status)
        CREATE INDEX IX_UnpaidLeaves_Dates ON UnpaidLeaves(StartDate, EndDate)
      END
    `);
    results.push({ table: 'UnpaidLeaves', status: 'تم التحقق/الإنشاء' });

    return NextResponse.json({
      success: true,
      results: results
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في إصلاح هياكل الجداول: ' + error.message
    }, { status: 500 });
  }
}

// إصلاح تناسق APIs
async function fixApiConsistency(pool) {
  try {
    const results = [];

    // التحقق من وجود الجداول المطلوبة
    const tablesCheck = await pool.request().query(`
      SELECT TABLE_NAME
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_NAME IN ('Employees', 'LeaveBalances', 'LeaveRequests', 'UnpaidLeaves')
    `);

    const existingTables = tablesCheck.recordset.map(row => row.TABLE_NAME);

    if (!existingTables.includes('Employees')) {
      throw new Error('جدول الموظفين غير موجود - يجب إنشاء الجداول أولاً');
    }

    results.push({
      step: 'فحص الجداول',
      status: 'تم بنجاح',
      tables: existingTables
    });

    return NextResponse.json({
      success: true,
      results: results,
      message: 'تم فحص تناسق APIs بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في إصلاح تناسق APIs: ' + error.message
    }, { status: 500 });
  }
}

// ترحيل البيانات الموجودة
async function migrateExistingData(pool) {
  try {
    const results = [];

    // 1. ترحيل بيانات أرصدة الإجازات من الموظفين
    const migrateBalances = await pool.request().query(`
      INSERT INTO LeaveBalances (EmployeeCode, EmployeeName, JobTitle, Department, AnnualBalance, CasualBalance, Year)
      SELECT
        e.EmployeeCode,
        e.EmployeeName,
        e.JobTitle,
        e.Department,
        15 as AnnualBalance,
        6 as CasualBalance,
        YEAR(GETDATE()) as Year
      FROM Employees e
      WHERE e.EmployeeCode NOT IN (SELECT EmployeeCode FROM LeaveBalances WHERE Year = YEAR(GETDATE()))
      AND e.CurrentStatus IN ('ساري', 'نشط', 'سارى')
    `);

    results.push({
      step: 'ترحيل أرصدة الإجازات',
      status: 'تم بنجاح',
      rowsAffected: migrateBalances.rowsAffected[0]
    });

    // 2. تحديث الأرصدة المستخدمة بناءً على الطلبات المعتمدة
    const updateUsedBalances = await pool.request().query(`
      UPDATE lb
      SET
        UsedAnnual = ISNULL(annual_used.used_days, 0),
        UsedCasual = ISNULL(casual_used.used_days, 0)
      FROM LeaveBalances lb
      LEFT JOIN (
        SELECT
          EmployeeCode,
          SUM(DaysCount) as used_days
        FROM LeaveRequests
        WHERE Status = N'معتمد'
        AND LeaveType IN (N'إعتيادية', N'اعتيادية', N'annual')
        AND YEAR(RequestDate) = YEAR(GETDATE())
        GROUP BY EmployeeCode
      ) annual_used ON lb.EmployeeCode = annual_used.EmployeeCode
      LEFT JOIN (
        SELECT
          EmployeeCode,
          SUM(DaysCount) as used_days
        FROM LeaveRequests
        WHERE Status = N'معتمد'
        AND LeaveType IN (N'عارضة', N'casual')
        AND YEAR(RequestDate) = YEAR(GETDATE())
        GROUP BY EmployeeCode
      ) casual_used ON lb.EmployeeCode = casual_used.EmployeeCode
      WHERE lb.Year = YEAR(GETDATE())
    `);

    results.push({
      step: 'تحديث الأرصدة المستخدمة',
      status: 'تم بنجاح',
      rowsAffected: updateUsedBalances.rowsAffected[0]
    });

    return NextResponse.json({
      success: true,
      results: results,
      message: 'تم ترحيل البيانات بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في ترحيل البيانات: ' + error.message
    }, { status: 500 });
  }
}

// تنفيذ الإصلاح الكامل
async function performFullFix(pool) {
  try {
    const results = [];

    // 1. إصلاح هياكل الجداول
    const tableFixResult = await fixTableStructures(pool);
    if (tableFixResult.success) {
      results.push({ step: 'إصلاح هياكل الجداول', status: 'تم بنجاح' });
    } else {
      throw new Error('فشل في إصلاح هياكل الجداول');
    }

    // 2. ترحيل البيانات
    const migrationResult = await migrateExistingData(pool);
    if (migrationResult.success) {
      results.push({ step: 'ترحيل البيانات', status: 'تم بنجاح' });
    } else {
      throw new Error('فشل في ترحيل البيانات');
    }

    // 3. إصلاح تناسق APIs
    const apiFixResult = await fixApiConsistency(pool);
    if (apiFixResult.success) {
      results.push({ step: 'إصلاح تناسق APIs', status: 'تم بنجاح' });
    } else {
      throw new Error('فشل في إصلاح تناسق APIs');
    }

    return NextResponse.json({
      success: true,
      results: results,
      message: 'تم تنفيذ الإصلاح الكامل بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في تنفيذ الإصلاح الكامل: ' + error.message
    }, { status: 500 });
  }
}
