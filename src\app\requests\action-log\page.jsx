'use client';
import React, { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTheme } from '@/contexts/ThemeContext';
import { Search, RefreshCw, FileText, Clock, User, Activity, Filter } from 'lucide-react';

export default function ActionLogPage() {
  const { isArabic } = useLanguage();
  const { isDarkMode } = useTheme();
  
  const [requests, setRequests] = useState([]);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [actionLog, setActionLog] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');

  // جلب جميع الطلبات
  const fetchRequests = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/paper-requests?action=list');
      const result = await response.json();
      
      if (result.success) {
        setRequests(result.requests || []);
      } else {

      }
    } catch (error) {

    }
    setLoading(false);
  };

  // جلب سجل الإجراءات لطلب معين
  const fetchActionLog = async (requestId) => {
    try {
      const response = await fetch('/api/paper-requests', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getActionLog',
          requestId: requestId
        })
      });
      
      const result = await response.json();
      
      if (result.success) {
        setActionLog(result.actionLog || []);
      } else {

      }
    } catch (error) {

    }
  };

  useEffect(() => {
    fetchRequests();
  }, []);

  // فلترة الطلبات
  const filteredRequests = requests.filter(request => {
    const matchesSearch = 
      request.EmployeeName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.EmployeeCode?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.LeaveType?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || request.Status === statusFilter;
    const matchesType = typeFilter === 'all' || request.RequestType === typeFilter;
    
    return matchesSearch && matchesStatus && matchesType;
  });

  // اختيار طلب وجلب سجل إجراءاته
  const selectRequest = (request) => {
    setSelectedRequest(request);
    fetchActionLog(request.ID);
  };

  // ترجمة نوع الإجراء
  const getActionTypeLabel = (actionType) => {
    const types = {
      'submitted': 'تم التقديم',
      'approved': 'تم الاعتماد',
      'rejected': 'تم الرفض',
      'deleted': 'تم الحذف',
      'reverted': 'تم الرجوع عن الاعتماد'
    };
    return types[actionType] || actionType;
  };

  // لون حسب نوع الإجراء
  const getActionColor = (actionType) => {
    const colors = {
      'submitted': 'bg-blue-100 text-blue-800',
      'approved': 'bg-green-100 text-green-800',
      'rejected': 'bg-red-100 text-red-800',
      'deleted': 'bg-gray-100 text-gray-800',
      'reverted': 'bg-orange-100 text-orange-800'
    };
    return colors[actionType] || 'bg-gray-100 text-gray-800';
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto">
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="flex items-center justify-between">
            <div>
              <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
                سجل إجراءات الطلبات الورقية
              </h1>
              <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                تتبع جميع الإجراءات المتخذة على الطلبات الورقية
              </p>
            </div>
            <div className="flex gap-3">
              <button
                onClick={fetchRequests}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
              >
                <RefreshCw className="w-4 h-4" />
                تحديث
              </button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* قائمة الطلبات */}
          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border`}>
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <h2 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-4`}>
                الطلبات المقدمة
              </h2>
              
              {/* فلاتر البحث */}
              <div className="space-y-3">
                <div className="relative">
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <input
                    type="text"
                    placeholder="البحث بالاسم أو الكود..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className={`w-full pr-10 pl-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      isDarkMode 
                        ? 'bg-slate-800 border-slate-600 text-white' 
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-3">
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className={`px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      isDarkMode 
                        ? 'bg-slate-800 border-slate-600 text-white' 
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  >
                    <option value="all">جميع الحالات</option>
                    <option value="قيد المراجعة">قيد المراجعة</option>
                    <option value="معتمدة">معتمدة</option>
                    <option value="مرفوضة">مرفوضة</option>
                  </select>
                  
                  <select
                    value={typeFilter}
                    onChange={(e) => setTypeFilter(e.target.value)}
                    className={`px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      isDarkMode 
                        ? 'bg-slate-800 border-slate-600 text-white' 
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  >
                    <option value="all">جميع الأنواع</option>
                    <option value="leave">إجازة</option>
                    <option value="mission">مأمورية</option>
                    <option value="permission">إذن</option>
                    <option value="night_shift">وردية ليلية</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="max-h-96 overflow-y-auto">
              {loading ? (
                <div className="flex justify-center items-center py-8">
                  <RefreshCw className="w-6 h-6 animate-spin text-blue-500" />
                  <span className="mr-2">جاري التحميل...</span>
                </div>
              ) : filteredRequests.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className={`text-lg ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                    لا توجد طلبات
                  </p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredRequests.map((request) => (
                    <div
                      key={request.ID}
                      onClick={() => selectRequest(request)}
                      className={`p-4 cursor-pointer transition-colors ${
                        selectedRequest?.ID === request.ID
                          ? isDarkMode ? 'bg-blue-900/30' : 'bg-blue-50'
                          : isDarkMode ? 'hover:bg-slate-800' : 'hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                            {request.EmployeeName}
                          </div>
                          <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>
                            {request.EmployeeCode} - {request.RequestType === 'leave' ? 'إجازة' : request.RequestType}
                          </div>
                        </div>
                        <div className="text-right">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            request.Status === 'قيد المراجعة' 
                              ? 'bg-yellow-100 text-yellow-800' 
                              : request.Status === 'معتمدة'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {request.Status}
                          </span>
                          <div className={`text-xs mt-1 ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>
                            {request.RequestDate && new Date(request.RequestDate).toLocaleDateString('ar-EG')}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* سجل الإجراءات */}
          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border`}>
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <h2 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                سجل الإجراءات
              </h2>
              {selectedRequest && (
                <p className={`text-sm mt-1 ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                  {selectedRequest.EmployeeName} - {selectedRequest.RequestType === 'leave' ? 'إجازة' : selectedRequest.RequestType}
                </p>
              )}
            </div>

            <div className="max-h-96 overflow-y-auto">
              {!selectedRequest ? (
                <div className="text-center py-8">
                  <Activity className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className={`text-lg ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                    اختر طلب لعرض سجل إجراءاته
                  </p>
                </div>
              ) : actionLog.length === 0 ? (
                <div className="text-center py-8">
                  <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className={`text-lg ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                    لا توجد إجراءات مسجلة
                  </p>
                </div>
              ) : (
                <div className="p-4 space-y-4">
                  {actionLog.map((action, index) => (
                    <div key={action.ID} className="relative">
                      {index < actionLog.length - 1 && (
                        <div className="absolute right-4 top-8 w-0.5 h-full bg-gray-200 dark:bg-gray-600"></div>
                      )}
                      
                      <div className="flex items-start gap-4">
                        <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                          <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getActionColor(action.ActionType)}`}>
                              {getActionTypeLabel(action.ActionType)}
                            </span>
                            <span className={`text-xs ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>
                              {new Date(action.ActionDate).toLocaleString('ar-EG')}
                            </span>
                          </div>
                          
                          <div className={`mt-1 text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-700'}`}>
                            <div className="flex items-center gap-2">
                              <User className="w-4 h-4" />
                              <span>{action.ActionBy}</span>
                            </div>
                          </div>
                          
                          {action.Notes && (
                            <div className={`mt-2 text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                              <strong>ملاحظات:</strong> {action.Notes}
                            </div>
                          )}
                          
                          {action.PreviousStatus && action.NewStatus && (
                            <div className={`mt-2 text-xs ${isDarkMode ? 'text-slate-500' : 'text-gray-500'}`}>
                              تغيير الحالة: {action.PreviousStatus} ← {action.NewStatus}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
