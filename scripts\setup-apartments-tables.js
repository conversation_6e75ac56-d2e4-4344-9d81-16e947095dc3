const { getConnection, sql } = require('../src/utils/db');

async function setupApartmentsTables() {
  try {

    const pool = await getConnection();

    // 1. إنشاء جدول الشقق

    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Apartments' AND xtype='U')
      BEGIN
        CREATE TABLE Apartments (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          ApartmentCode NVARCHAR(20) NOT NULL UNIQUE,
          LandlordName NVARCHAR(100) NOT NULL,
          Address NVARCHAR(500) NOT NULL,
          StartDate DATE NOT NULL,
          EndDate DATE,
          RentAmount DECIMAL(10,2) NOT NULL,
          InsuranceAmount DECIMAL(10,2) DEFAULT 0,
          CommissionAmount DECIMAL(10,2) DEFAULT 0,
          BacklogAmount DECIMAL(10,2) DEFAULT 0,
          Notes NVARCHAR(MAX),
          IsActive BIT DEFAULT 1,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE()
        )
        
        CREATE INDEX IX_Apartments_Code ON Apartments(ApartmentCode)
        CREATE INDEX IX_Apartments_Active ON Apartments(IsActive)
        CREATE INDEX IX_Apartments_Dates ON Apartments(StartDate, EndDate)
        
        PRINT 'تم إنشاء جدول Apartments بنجاح'
      END
      ELSE
      BEGIN
        PRINT 'جدول Apartments موجود بالفعل'
      END
    `);

    // 2. إنشاء جدول المستفيدين

    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ApartmentBeneficiaries' AND xtype='U')
      BEGIN
        CREATE TABLE ApartmentBeneficiaries (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          ApartmentID INT NOT NULL,
          EmployeeCode NVARCHAR(20) NOT NULL,
          EmployeeName NVARCHAR(100) NOT NULL,
          JobTitle NVARCHAR(100) NOT NULL,
          Department NVARCHAR(100),
          StartDate DATE NOT NULL,
          EndDate DATE,
          IsActive BIT DEFAULT 1,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE()
        )
        
        CREATE INDEX IX_ApartmentBeneficiaries_ApartmentID ON ApartmentBeneficiaries(ApartmentID)
        CREATE INDEX IX_ApartmentBeneficiaries_EmployeeCode ON ApartmentBeneficiaries(EmployeeCode)
        CREATE INDEX IX_ApartmentBeneficiaries_Active ON ApartmentBeneficiaries(IsActive)
        
        PRINT 'تم إنشاء جدول ApartmentBeneficiaries بنجاح'
      END
      ELSE
      BEGIN
        PRINT 'جدول ApartmentBeneficiaries موجود بالفعل'
      END
    `);

    // 3. إدراج بيانات تجريبية

    // التحقق من وجود بيانات
    const existingData = await pool.request().query('SELECT COUNT(*) as Count FROM Apartments');
    
    if (existingData.recordset[0].Count === 0) {
      // إدراج شقق تجريبية
      await pool.request().query(`
        INSERT INTO Apartments (ApartmentCode, LandlordName, Address, StartDate, RentAmount, InsuranceAmount, CommissionAmount, Notes)
        VALUES
          (N'APT-001', N'أحمد محمد علي', N'شارع النيل، المعادي، القاهرة', '2024-01-01', 5000.00, 500.00, 250.00, N'شقة مفروشة 3 غرف'),
          (N'APT-002', N'فاطمة حسن', N'شارع الهرم، الجيزة', '2024-02-01', 4500.00, 450.00, 225.00, N'شقة مفروشة 2 غرف'),
          (N'APT-003', N'محمود السيد', N'مدينة نصر، القاهرة', '2024-03-01', 6000.00, 600.00, 300.00, N'شقة مفروشة 4 غرف'),
          (N'APT-004', N'سارة أحمد', N'المهندسين، الجيزة', '2024-04-01', 5500.00, 550.00, 275.00, N'شقة مفروشة 3 غرف + حديقة'),
          (N'APT-005', N'محمد عبدالله', N'الزمالك، القاهرة', '2024-05-01', 7000.00, 700.00, 350.00, N'شقة مفروشة 4 غرف - إطلالة على النيل')
      `);

      // إدراج مستفيدين تجريبيين (إذا كان جدول الموظفين موجود)
      try {
        const employeesCheck = await pool.request().query(`
          SELECT TOP 5 EmployeeID, FullName, JobTitle, Department 
          FROM Employees 
          WHERE EmployeeID IS NOT NULL AND EmployeeID != ''
        `);

        if (employeesCheck.recordset.length > 0) {
          // إدراج مستفيدين للشقق
          for (let i = 0; i < Math.min(5, employeesCheck.recordset.length); i++) {
            const employee = employeesCheck.recordset[i];
            const apartmentId = i + 1; // معرف الشقة

            await pool.request()
              .input('apartmentId', sql.Int, apartmentId)
              .input('employeeCode', sql.NVarChar, employee.EmployeeID)
              .input('employeeName', sql.NVarChar, employee.FullName)
              .input('jobTitle', sql.NVarChar, employee.JobTitle)
              .input('department', sql.NVarChar, employee.Department)
              .query(`
                INSERT INTO ApartmentBeneficiaries (
                  ApartmentID, EmployeeCode, EmployeeName, JobTitle, Department, StartDate
                )
                VALUES (
                  @apartmentId, @employeeCode, @employeeName, @jobTitle, @department, GETDATE()
                )
              `);
          }

        }
      } catch (error) {

      }
    } else {

    }

    // 4. عرض الإحصائيات

    const stats = await pool.request().query(`
      SELECT 
        (SELECT COUNT(*) FROM Apartments WHERE IsActive = 1) as TotalActiveApartments,
        (SELECT COUNT(*) FROM Apartments WHERE IsActive = 0) as TotalInactiveApartments,
        (SELECT COUNT(*) FROM ApartmentBeneficiaries WHERE IsActive = 1) as TotalActiveBeneficiaries,
        (SELECT SUM(RentAmount) FROM Apartments WHERE IsActive = 1) as TotalMonthlyRent,
        (SELECT SUM(InsuranceAmount) FROM Apartments WHERE IsActive = 1) as TotalInsurance,
        (SELECT SUM(CommissionAmount) FROM Apartments WHERE IsActive = 1) as TotalCommission,
        (SELECT SUM(BacklogAmount) FROM Apartments WHERE IsActive = 1) as TotalBacklog
    `);

    const data = stats.recordset[0];

  } catch (error) {

    throw error;
  }
}

// تشغيل السكريبت إذا تم استدعاؤه مباشرة
if (require.main === module) {
  setupApartmentsTables()
    .then(() => {

      process.exit(0);
    })
    .catch((error) => {

      process.exit(1);
    });
}

module.exports = { setupApartmentsTables };
