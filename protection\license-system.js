// نظام حماية الترخيص
const crypto = require('crypto');
const os = require('os');
const fs = require('fs');
const path = require('path');

class LicenseSystem {
  constructor() {
    this.secretKey = 'YOUR_SECRET_KEY_HERE'; // غيّر هذا المفتاح
    this.licenseFile = path.join(__dirname, '../.license');
    this.hardwareId = this.generateHardwareId();
  }

  // توليد معرف فريد للجهاز
  generateHardwareId() {
    const networkInterfaces = os.networkInterfaces();
    const cpus = os.cpus();
    
    let hardwareInfo = '';
    hardwareInfo += os.hostname();
    hardwareInfo += os.platform();
    hardwareInfo += os.arch();
    
    // إضافة معلومات المعالج
    if (cpus.length > 0) {
      hardwareInfo += cpus[0].model;
    }
    
    // إضافة MAC address
    for (const interfaceName in networkInterfaces) {
      const interfaces = networkInterfaces[interfaceName];
      for (const iface of interfaces) {
        if (!iface.internal && iface.mac !== '00:00:00:00:00:00') {
          hardwareInfo += iface.mac;
          break;
        }
      }
    }
    
    return crypto.createHash('sha256').update(hardwareInfo).digest('hex');
  }

  // إنشاء ترخيص
  generateLicense(customerInfo) {
    const licenseData = {
      customerId: customerInfo.id,
      customerName: customerInfo.name,
      email: customerInfo.email,
      hardwareId: this.hardwareId,
      issueDate: new Date().toISOString(),
      expiryDate: customerInfo.expiryDate || null,
      features: customerInfo.features || ['all'],
      version: customerInfo.version || '1.0.0'
    };

    const licenseString = JSON.stringify(licenseData);
    const signature = this.signLicense(licenseString);
    
    return {
      license: Buffer.from(licenseString).toString('base64'),
      signature: signature
    };
  }

  // توقيع الترخيص
  signLicense(licenseString) {
    const hmac = crypto.createHmac('sha256', this.secretKey);
    hmac.update(licenseString);
    return hmac.digest('hex');
  }

  // التحقق من صحة الترخيص
  validateLicense() {
    try {
      if (!fs.existsSync(this.licenseFile)) {
        throw new Error('ملف الترخيص غير موجود');
      }

      const licenseContent = fs.readFileSync(this.licenseFile, 'utf8');
      const { license, signature } = JSON.parse(licenseContent);
      
      // فك تشفير الترخيص
      const licenseString = Buffer.from(license, 'base64').toString('utf8');
      const licenseData = JSON.parse(licenseString);
      
      // التحقق من التوقيع
      const expectedSignature = this.signLicense(licenseString);
      if (signature !== expectedSignature) {
        throw new Error('الترخيص غير صالح - توقيع خاطئ');
      }
      
      // التحقق من معرف الجهاز
      if (licenseData.hardwareId !== this.hardwareId) {
        throw new Error('الترخيص غير صالح لهذا الجهاز');
      }
      
      // التحقق من تاريخ الانتهاء
      if (licenseData.expiryDate) {
        const expiryDate = new Date(licenseData.expiryDate);
        if (new Date() > expiryDate) {
          throw new Error('انتهت صلاحية الترخيص');
        }
      }
      
      return {
        valid: true,
        data: licenseData
      };
      
    } catch (error) {
      return {
        valid: false,
        error: error.message
      };
    }
  }

  // حفظ الترخيص
  saveLicense(licenseInfo) {
    fs.writeFileSync(this.licenseFile, JSON.stringify(licenseInfo, null, 2));
  }

  // تشفير ملف
  encryptFile(filePath, outputPath) {
    const algorithm = 'aes-256-cbc';
    const key = crypto.scryptSync(this.secretKey, 'salt', 32);
    const iv = crypto.randomBytes(16);
    
    const cipher = crypto.createCipher(algorithm, key);
    const input = fs.createReadStream(filePath);
    const output = fs.createWriteStream(outputPath);
    
    output.write(iv);
    input.pipe(cipher).pipe(output);
  }

  // فك تشفير ملف
  decryptFile(filePath, outputPath) {
    const algorithm = 'aes-256-cbc';
    const key = crypto.scryptSync(this.secretKey, 'salt', 32);
    
    const input = fs.createReadStream(filePath);
    const output = fs.createWriteStream(outputPath);
    
    input.once('readable', () => {
      const iv = input.read(16);
      const decipher = crypto.createDecipher(algorithm, key);
      input.pipe(decipher).pipe(output);
    });
  }
}

module.exports = LicenseSystem;
