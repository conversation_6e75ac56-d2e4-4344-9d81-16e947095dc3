const sql = require('mssql');

// إعدادات قاعدة البيانات
const dbConfig = {
  user: 'SA',
  password: 'admin@123',
  server: 'localhost\\DBOJESTA',
  database: 'EMP',
  options: {
    encrypt: false,
    trustServerCertificate: true,
    enableArithAbort: true,
    instanceName: 'DBOJESTA'
  }
};

async function addSampleAnnexes() {
  try {
    console.log('🔗 الاتصال بقاعدة البيانات...');
    const pool = await sql.connect(dbConfig);

    // التحقق من وجود جدول APARTMENTCOST
    const tableCheck = await pool.request().query(`
      SELECT COUNT(*) as count
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_NAME = 'APARTMENTCOST'
    `);

    if (tableCheck.recordset[0].count === 0) {
      console.log('❌ جدول APARTMENTCOST غير موجود');
      return;
    }

    // إضافة بعض الملاحق التجريبية
    const sampleAnnexes = [
      {
        month: 4,
        year: 2025,
        count: 3,
        cost: 15000,
        isAnnex: 1,
        annexFile: '4-2025.pdf'
      },
      {
        month: 3,
        year: 2025,
        count: 2,
        cost: 8500,
        isAnnex: 1,
        annexFile: '3-2025.pdf'
      }
    ];

    console.log('📝 إضافة الملاحق التجريبية...');

    for (const annex of sampleAnnexes) {
      // التحقق من وجود السجل
      const existingCheck = await pool.request()
        .input('month', annex.month)
        .input('year', annex.year)
        .input('isAnnex', annex.isAnnex)
        .query(`
          SELECT COUNT(*) as count
          FROM APARTMENTCOST
          WHERE [الشهر] = @month AND [السنة] = @year AND [ملحق] = @isAnnex
        `);

      if (existingCheck.recordset[0].count > 0) {
        console.log(`⚠️ الملحق ${annex.month}/${annex.year} موجود بالفعل`);
        continue;
      }

      // إضافة الملحق
      await pool.request()
        .input('month', annex.month)
        .input('year', annex.year)
        .input('count', annex.count)
        .input('cost', annex.cost)
        .input('isAnnex', annex.isAnnex)
        .input('annexFile', annex.annexFile)
        .query(`
          INSERT INTO APARTMENTCOST (
            [الشهر], [السنة], [العدد], [القيمة الإيجارية], [ملحق], [ملف_الملحق]
          )
          VALUES (
            @month, @year, @count, @cost, @isAnnex, @annexFile
          )
        `);

      console.log(`✅ تم إضافة ملحق ${annex.month}/${annex.year} - ${annex.annexFile}`);
    }

    // عرض النتائج
    const result = await pool.request().query(`
      SELECT
        [الشهر] as Month,
        [السنة] as Year,
        [العدد] as Count,
        [القيمة الإيجارية] as Cost,
        [ملحق] as IsAnnex,
        [ملف_الملحق] as AnnexFile
      FROM APARTMENTCOST
      WHERE [ملحق] = 1
      ORDER BY [السنة] DESC, [الشهر] DESC
    `);

    console.log('\n📋 الملاحق الموجودة:');
    result.recordset.forEach((record, index) => {
      console.log(`  ${index + 1}. ${record.Month}/${record.Year} - ${record.AnnexFile} (${record.Count} شقق - ${record.Cost} ج.م)`);
    });

    await pool.close();
    console.log('\n✅ تم الانتهاء بنجاح!');

  } catch (error) {
    console.error('❌ خطأ:', error.message);
  }
}

addSampleAnnexes();
