'use client';
import React, { useState } from 'react';

import { useUpload } from '@/app/utilities/runtime-helpers';

function MainComponent() {
  const [formData, setFormData] = useState({
    employee_code: '',
    first_name: '',
    last_name: '',
    job_title: '',
    department: '',
    birth_date: '',
    nationality: '',
    id_number: '',
    passport_number: '',
    phone: '',
    email: '',
    address: '',
    start_date: '',
    salary: '',
    status: 'active',
  });

  const [documents, setDocuments] = useState({
    id_copy: null,
    passport_copy: null,
    qualifications: null,
    contract: null,
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [upload, { loading: uploading }] = useUpload();

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleFileChange = async (e, documentType) => {
    const file = e.target.files[0];
    if (!file) return;

    try {
      const { url, error } = await upload({ file });
      if (error) throw new Error(error);

      setDocuments((prev) => ({
        ...prev,
        [documentType]: url,
      }));
    } catch (err) {
      setError('فشل في رفع المستند');

    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess(false);

    try {
      const employeeData = {
        ...formData,
        documents,
      };

      const response = await fetch('/api/data-service', {
        method: 'POST',
        body: JSON.stringify({
          table: 'employees',
          action: 'create',
          data: employeeData,
        }),
      });

      if (!response.ok) {
        throw new Error('فشل في إضافة الموظف');
      }

      setSuccess(true);
      setFormData({
        employee_code: '',
        first_name: '',
        last_name: '',
        job_title: '',
        department: '',
        birth_date: '',
        nationality: '',
        id_number: '',
        passport_number: '',
        phone: '',
        email: '',
        address: '',
        start_date: '',
        salary: '',
        status: 'active',
      });
      setDocuments({
        id_copy: null,
        passport_copy: null,
        qualifications: null,
        contract: null,
      });
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      dir="rtl"
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <a
            href="/employees"
            className="flex items-center text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
          >
            <i className="fas fa-arrow-right ml-2"></i>
            عودة
          </a>
        </div>

        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          إضافة موظف جديد
        </h1>

        <form
          onSubmit={handleSubmit}
          className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                كود الموظف
              </label>
              <input
                type="text"
                name="employee_code"
                value={formData.employee_code}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                الاسم الأول
              </label>
              <input
                type="text"
                name="first_name"
                value={formData.first_name}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                اسم العائلة
              </label>
              <input
                type="text"
                name="last_name"
                value={formData.last_name}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                المسمى الوظيفي
              </label>
              <input
                type="text"
                name="job_title"
                value={formData.job_title}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                القسم
              </label>
              <input
                type="text"
                name="department"
                value={formData.department}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                تاريخ الميلاد
              </label>
              <input
                type="date"
                name="birth_date"
                value={formData.birth_date}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                الجنسية
              </label>
              <input
                type="text"
                name="nationality"
                value={formData.nationality}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                رقم الهوية
              </label>
              <input
                type="text"
                name="id_number"
                value={formData.id_number}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                رقم الجواز
              </label>
              <input
                type="text"
                name="passport_number"
                value={formData.passport_number}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                رقم الهاتف
              </label>
              <input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                البريد الإلكتروني
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                العنوان
              </label>
              <textarea
                name="address"
                value={formData.address}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                rows="3"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                تاريخ بداية العمل
              </label>
              <input
                type="date"
                name="start_date"
                value={formData.start_date}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                الراتب
              </label>
              <input
                type="number"
                name="salary"
                value={formData.salary}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              />
            </div>
          </div>

          <div className="mt-8">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              المستندات المطلوبة
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  صورة الهوية
                </label>
                <input
                  type="file"
                  onChange={(e) => handleFileChange(e, 'id_copy')}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  accept="image/*,.pdf"
                />
              </div>

              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  صورة جواز السفر
                </label>
                <input
                  type="file"
                  onChange={(e) => handleFileChange(e, 'passport_copy')}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  accept="image/*,.pdf"
                />
              </div>

              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  المؤهلات العلمية
                </label>
                <input
                  type="file"
                  onChange={(e) => handleFileChange(e, 'qualifications')}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  accept="image/*,.pdf"
                />
              </div>

              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  عقد العمل
                </label>
                <input
                  type="file"
                  onChange={(e) => handleFileChange(e, 'contract')}
                  className="w-full p-2 border border-gray-300 rounded-md"
                  accept="image/*,.pdf"
                />
              </div>
            </div>
          </div>

          {error && (
            <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-md">
              {error}
            </div>
          )}

          {success && (
            <div className="mt-4 p-3 bg-green-100 text-green-700 rounded-md">
              تمت إضافة الموظف بنجاح
            </div>
          )}

          <div className="mt-6">
            <button
              type="submit"
              disabled={loading || uploading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors disabled:bg-blue-300"
            >
              {loading || uploading ? 'جاري الإضافة...' : 'إضافة الموظف'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default MainComponent;
