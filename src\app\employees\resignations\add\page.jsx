'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  FiLogOut, 
  FiUser, 
  FiCalendar, 
  FiFileText, 
  FiSave,
  FiX,
  FiSearch,
  FiRefreshCw,
  FiDollarSign,
  FiMessageSquare
} from 'react-icons/fi';

export default function AddResignationPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [employees, setEmployees] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedEmployee, setSelectedEmployee] = useState(null);

  const [formData, setFormData] = useState({
    employeeId: '',
    employeeName: '',
    department: '',
    jobTitle: '',
    resignationDate: '',
    lastWorkingDay: '',
    resignationReason: '',
    finalSettlementAmount: '',
    createdBy: 'المدير'
  });

  // جلب البيانات المطلوبة
  useEffect(() => {
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    if (!isLoggedIn) {
      router.push('/login');
      return;
    }

    fetchEmployees();
  }, [router]);

  // جلب قائمة الموظفين
  const fetchEmployees = async () => {
    try {
      const response = await fetch('/api/employee-search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          searchType: 'group',
          department: '',
          jobTitle: '',
          governorate: ''
        })
      });

      const data = await response.json();
      if (data.success) {
        // فلترة الموظفين النشطين فقط
        const activeEmployees = (data.employees || []).filter(emp => 
          !emp.CurrentStatus || emp.CurrentStatus === 'نشط' || emp.CurrentStatus === 'سارى'
        );
        setEmployees(activeEmployees);
      }
    } catch (error) {

    }
  };

  // تصفية الموظفين حسب البحث
  const filteredEmployees = employees.filter(emp =>
    emp.FullName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    emp.EmployeeID?.toString().includes(searchTerm) ||
    emp.Department?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // اختيار موظف
  const selectEmployee = (employee) => {
    setSelectedEmployee(employee);
    setFormData(prev => ({
      ...prev,
      employeeId: employee.EmployeeID,
      employeeName: employee.FullName,
      department: employee.Department || '',
      jobTitle: employee.JobTitle || ''
    }));
    setSearchTerm('');
  };

  // معالجة تغيير البيانات
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // إرسال النموذج
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.employeeId || !formData.resignationDate) {
      setError('يرجى ملء جميع الحقول المطلوبة');
      return;
    }

    setLoading(true);
    setError('');
    setSuccess('');

    try {
      const response = await fetch('/api/resignations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          finalSettlementAmount: parseFloat(formData.finalSettlementAmount) || 0
        })
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('تم إضافة الاستقالة بنجاح!');
        setTimeout(() => {
          router.push('/employees/resignations');
        }, 2000);
      } else {
        setError(data.error || 'حدث خطأ في إضافة الاستقالة');
      }
    } catch (error) {

      setError('حدث خطأ في إضافة الاستقالة');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* رأس الصفحة */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <FiLogOut className="text-2xl text-red-600" />
              <h1 className="text-3xl font-bold text-gray-800">إضافة استقالة جديدة</h1>
            </div>
            <button
              onClick={() => router.push('/employees/resignations')}
              className="text-gray-600 hover:text-gray-800 flex items-center gap-2"
            >
              <FiX />
              إلغاء
            </button>
          </div>
        </div>

        {/* رسائل النجاح والخطأ */}
        {success && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <p className="text-green-800">✅ {success}</p>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <p className="text-red-800">❌ {error}</p>
          </div>
        )}

        {/* نموذج إضافة الاستقالة */}
        <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-sm p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* اختيار الموظف */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                اختيار الموظف *
              </label>
              
              {selectedEmployee ? (
                <div className="flex items-center justify-between p-4 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                      <FiUser className="text-red-600" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{selectedEmployee.FullName}</div>
                      <div className="text-sm text-gray-600">
                        #{selectedEmployee.EmployeeID} - {selectedEmployee.Department} - {selectedEmployee.JobTitle}
                      </div>
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={() => {
                      setSelectedEmployee(null);
                      setFormData(prev => ({
                        ...prev,
                        employeeId: '',
                        employeeName: '',
                        department: '',
                        jobTitle: ''
                      }));
                    }}
                    className="text-red-600 hover:text-red-800"
                  >
                    <FiX />
                  </button>
                </div>
              ) : (
                <div>
                  <div className="relative mb-4">
                    <FiSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      placeholder="ابحث عن موظف بالاسم أو الكود..."
                      className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    />
                  </div>

                  {searchTerm && (
                    <div className="max-h-60 overflow-y-auto border border-gray-200 rounded-lg">
                      {filteredEmployees.length > 0 ? (
                        filteredEmployees.slice(0, 10).map((employee, index) => (
                          <button
                            key={index}
                            type="button"
                            onClick={() => selectEmployee(employee)}
                            className="w-full p-3 text-right hover:bg-gray-50 border-b border-gray-100 last:border-b-0 transition-colors"
                          >
                            <div className="flex items-center justify-between">
                              <div>
                                <div className="font-medium text-gray-900">{employee.FullName}</div>
                                <div className="text-sm text-gray-600">
                                  {employee.Department} - {employee.JobTitle}
                                </div>
                              </div>
                              <div className="text-red-600 font-bold">#{employee.EmployeeID}</div>
                            </div>
                          </button>
                        ))
                      ) : (
                        <div className="p-4 text-center text-gray-500">
                          لا توجد نتائج للبحث
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* القسم */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                القسم
              </label>
              <input
                type="text"
                value={formData.department}
                readOnly
                className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600"
              />
            </div>

            {/* المسمى الوظيفي */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                المسمى الوظيفي
              </label>
              <input
                type="text"
                value={formData.jobTitle}
                readOnly
                className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600"
              />
            </div>

            {/* تاريخ الاستقالة */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                تاريخ الاستقالة *
              </label>
              <input
                type="date"
                name="resignationDate"
                value={formData.resignationDate}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
              />
            </div>

            {/* آخر يوم عمل */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                آخر يوم عمل
              </label>
              <input
                type="date"
                name="lastWorkingDay"
                value={formData.lastWorkingDay}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
              />
            </div>

            {/* التسوية النهائية */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                التسوية النهائية (ج.م)
              </label>
              <div className="relative">
                <FiDollarSign className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="number"
                  name="finalSettlementAmount"
                  value={formData.finalSettlementAmount}
                  onChange={handleInputChange}
                  placeholder="0.00"
                  step="0.01"
                  min="0"
                  className="w-full pr-10 pl-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* سبب الاستقالة */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                سبب الاستقالة *
              </label>
              <div className="relative">
                <FiMessageSquare className="absolute right-3 top-3 text-gray-400" />
                <textarea
                  name="resignationReason"
                  value={formData.resignationReason}
                  onChange={handleInputChange}
                  rows="4"
                  placeholder="اكتب سبب الاستقالة..."
                  required
                  className="w-full pr-10 pl-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* أزرار الإجراءات */}
          <div className="flex gap-4 mt-8">
            <button
              type="submit"
              disabled={loading}
              className="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2 disabled:opacity-50"
            >
              {loading ? <FiRefreshCw className="animate-spin" /> : <FiSave />}
              {loading ? 'جاري الحفظ...' : 'حفظ الاستقالة'}
            </button>
            
            <button
              type="button"
              onClick={() => router.push('/employees/resignations')}
              className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-2"
            >
              <FiX />
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
