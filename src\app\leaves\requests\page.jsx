'use client';

import React, { useState, useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { useLanguage } from '@/contexts/LanguageContext';
import MainLayout from '@/components/MainLayout';
import DateInput from '@/components/DateInput';
import { Calendar, User, FileText, Clock, CheckCircle, XCircle, Search, Filter, Download } from 'lucide-react';

export default function LeaveRequestsList() {
  const { isDarkMode } = useTheme();
  const { isRTL, isArabic } = useLanguage();
  
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [filters, setFilters] = useState({
    employeeCode: '',
    status: '',
    leaveType: '',
    startDate: '',
    endDate: ''
  });

  // حالات الطلبات
  const statusOptions = [
    { value: '', label: 'جميع الحالات' },
    { value: 'قيد المراجعة', label: 'قيد المراجعة' },
    { value: 'معتمد', label: 'معتمد' },
    { value: 'مرفوض', label: 'مرفوض' }
  ];

  // أنواع الإجازات
  const leaveTypeOptions = [
    { value: '', label: 'جميع الأنواع' },
    { value: 'اعتيادية', label: 'إجازة اعتيادية' },
    { value: 'عارضة', label: 'إجازة عارضة' },
    { value: 'مرضية', label: 'إجازة مرضية' },
    { value: 'أمومة', label: 'إجازة أمومة' },
    { value: 'بدل', label: 'إجازة بدل' },
    { value: 'بدون راتب', label: 'بدون راتب' },
    { value: 'أخرى', label: 'أخرى' }
  ];

  // تحويل التاريخ من DD/MM/YYYY إلى YYYY-MM-DD للحسابات
  const convertDateForCalculation = (dateStr) => {
    if (!dateStr || dateStr === 'undefined' || dateStr === 'null') return '';
    if (typeof dateStr === 'string' && dateStr.includes('/')) {
      const parts = dateStr.split('/');
      if (parts.length === 3) {
        const [day, month, year] = parts;
        if (day && month && year) {
          return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        }
      }
    }
    return dateStr;
  };

  // تحويل التاريخ من YYYY-MM-DD إلى DD/MM/YYYY للعرض
  const convertDateForDisplay = (dateStr) => {
    if (!dateStr || dateStr === 'undefined' || dateStr === 'null') return '';
    if (typeof dateStr === 'string' && dateStr.includes('-') && dateStr.length === 10) {
      const parts = dateStr.split('-');
      if (parts.length === 3) {
        const [year, month, day] = parts;
        if (year && month && day) {
          return `${day}/${month}/${year}`;
        }
      }
    }
    return dateStr;
  };

  // جلب طلبات الإجازات
  const fetchRequests = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/leave-management', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'list',
          ...filters
        })
      });

      const result = await response.json();
      
      if (result.success) {
        setRequests(result.data || []);
        setMessage('');
      } else {
        setMessage(result.error || 'فشل في جلب البيانات');
        setRequests([]);
      }
    } catch (error) {

      setMessage('خطأ في الاتصال بالخادم');
      setRequests([]);
    }
    setLoading(false);
  };

  // تحديث حالة الطلب
  const updateRequestStatus = async (id, status, notes = '') => {
    try {
      const response = await fetch('/api/leave-management', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'updateStatus',
          id,
          status,
          notes
        })
      });

      const result = await response.json();
      
      if (result.success) {
        setMessage('تم تحديث حالة الطلب بنجاح');
        fetchRequests(); // إعادة جلب البيانات
      } else {
        setMessage(result.error || 'فشل في تحديث الحالة');
      }
    } catch (error) {

      setMessage('خطأ في تحديث الحالة');
    }
  };

  // تحميل البيانات عند بداية الصفحة
  useEffect(() => {
    fetchRequests();
  }, []);

  // تنسيق التاريخ
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-EG');
  };

  // تنسيق حالة الطلب
  const getStatusBadge = (status) => {
    const statusConfig = {
      'قيد المراجعة': { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      'معتمد': { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      'مرفوض': { color: 'bg-red-100 text-red-800', icon: XCircle }
    };

    const config = statusConfig[status] || statusConfig['قيد المراجعة'];
    const Icon = config.icon;

    return (
      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3" />
        {status}
      </span>
    );
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto">
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="flex items-center justify-between">
            <div>
              <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
                طلبات الإجازات
              </h1>
              <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                عرض وإدارة جميع طلبات الإجازات
              </p>
            </div>
            <div className="flex gap-3">
              <button
                onClick={fetchRequests}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
              >
                <Search className="w-4 h-4" />
                {loading ? 'جاري التحميل...' : 'تحديث'}
              </button>
              <a
                href="/leaves/request"
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <FileText className="w-4 h-4" />
                طلب إجازة جديد
              </a>
            </div>
          </div>
        </div>

        {/* رسائل التنبيه */}
        {message && (
          <div className={`p-4 rounded-lg mb-6 ${
            message.includes('نجاح') || message.includes('تم')
              ? 'bg-green-100 text-green-800 border border-green-200'
              : 'bg-red-100 text-red-800 border border-red-200'
          }`}>
            <div className="flex items-center gap-2">
              {message.includes('نجاح') || message.includes('تم') ? (
                <CheckCircle className="w-4 h-4" />
              ) : (
                <XCircle className="w-4 h-4" />
              )}
              {message}
            </div>
          </div>
        )}

        {/* فلاتر البحث */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-800'} mb-4`}>
            <Filter className="w-5 h-5 inline-block ml-2" />
            فلاتر البحث
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                كود الموظف
              </label>
              <input
                type="text"
                value={filters.employeeCode}
                onChange={(e) => setFilters(prev => ({ ...prev, employeeCode: e.target.value }))}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                  isDarkMode 
                    ? 'bg-slate-800 border-slate-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
                placeholder="1450"
              />
            </div>

            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                حالة الطلب
              </label>
              <select
                value={filters.status}
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                  isDarkMode 
                    ? 'bg-slate-800 border-slate-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                {statusOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                نوع الإجازة
              </label>
              <select
                value={filters.leaveType}
                onChange={(e) => setFilters(prev => ({ ...prev, leaveType: e.target.value }))}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                  isDarkMode 
                    ? 'bg-slate-800 border-slate-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                {leaveTypeOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <DateInput
                name="startDate"
                value={convertDateForDisplay(filters.startDate)}
                onChange={(e) => {
                  const value = e.target.value;
                  // تحويل من DD/MM/YYYY إلى YYYY-MM-DD للفلترة
                  const convertedDate = convertDateForCalculation(value);
                  setFilters(prev => ({ ...prev, startDate: convertedDate }));
                }}
                label="من تاريخ"
                placeholder="DD/MM/YYYY"
                isArabic={true}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                  isDarkMode
                    ? 'bg-slate-800 border-slate-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>

            <div>
              <DateInput
                name="endDate"
                value={convertDateForDisplay(filters.endDate)}
                onChange={(e) => {
                  const value = e.target.value;
                  // تحويل من DD/MM/YYYY إلى YYYY-MM-DD للفلترة
                  const convertedDate = convertDateForCalculation(value);
                  setFilters(prev => ({ ...prev, endDate: convertedDate }));
                }}
                label="إلى تاريخ"
                placeholder="DD/MM/YYYY"
                isArabic={true}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                  isDarkMode
                    ? 'bg-slate-800 border-slate-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>
          </div>

          <div className="flex gap-3 mt-4">
            <button
              onClick={fetchRequests}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
            >
              <Search className="w-4 h-4" />
              بحث
            </button>
            <button
              onClick={() => {
                setFilters({
                  employeeCode: '',
                  status: '',
                  leaveType: '',
                  startDate: '',
                  endDate: ''
                });
              }}
              className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              مسح الفلاتر
            </button>
          </div>
        </div>

        {/* جدول طلبات الإجازات */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border overflow-hidden`}>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className={`${isDarkMode ? 'bg-slate-800' : 'bg-gray-50'}`}>
                <tr>
                  <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    رقم الطلب
                  </th>
                  <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    كود الموظف
                  </th>
                  <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    اسم الموظف
                  </th>
                  <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    نوع الإجازة
                  </th>
                  <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    من - إلى
                  </th>
                  <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    عدد الأيام
                  </th>
                  <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    الحالة
                  </th>
                  <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    تاريخ الطلب
                  </th>
                  <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} divide-y ${isDarkMode ? 'divide-slate-700' : 'divide-gray-200'}`}>
                {loading ? (
                  <tr>
                    <td colSpan="9" className="px-6 py-12 text-center">
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <span className={`mr-3 ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                          جاري تحميل البيانات...
                        </span>
                      </div>
                    </td>
                  </tr>
                ) : requests.length === 0 ? (
                  <tr>
                    <td colSpan="9" className="px-6 py-12 text-center">
                      <div className="flex flex-col items-center">
                        <FileText className={`w-12 h-12 ${isDarkMode ? 'text-slate-400' : 'text-gray-400'} mb-4`} />
                        <p className={`text-lg font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                          لا توجد طلبات إجازات
                        </p>
                        <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-500'} mt-2`}>
                          لم يتم العثور على أي طلبات إجازات بالمعايير المحددة
                        </p>
                      </div>
                    </td>
                  </tr>
                ) : (
                  requests.map((request) => (
                    <tr key={request.ID} className={`hover:${isDarkMode ? 'bg-slate-800' : 'bg-gray-50'} transition-colors`}>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        #{request.ID}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                        {request.EmployeeCode}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                        {request.EmployeeName}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {request.LeaveType}
                        </span>
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                        <div className="flex flex-col">
                          <span>{formatDate(request.StartDate)}</span>
                          <span className="text-xs text-gray-400">إلى</span>
                          <span>{formatDate(request.EndDate)}</span>
                        </div>
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {request.DaysCount} يوم
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(request.Status)}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                        {formatDate(request.RequestDate)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex gap-2">
                          {request.Status === 'قيد المراجعة' && (
                            <>
                              <button
                                onClick={() => updateRequestStatus(request.ID, 'معتمد')}
                                className="text-green-600 hover:text-green-900 transition-colors"
                                title="اعتماد"
                              >
                                <CheckCircle className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => updateRequestStatus(request.ID, 'مرفوض')}
                                className="text-red-600 hover:text-red-900 transition-colors"
                                title="رفض"
                              >
                                <XCircle className="w-4 h-4" />
                              </button>
                            </>
                          )}
                          <button
                            onClick={() => {
                              // يمكن إضافة وظيفة عرض التفاصيل هنا
                              alert(`تفاصيل الطلب رقم ${request.ID}\n\nالسبب: ${request.Reason || 'غير محدد'}`);
                            }}
                            className="text-blue-600 hover:text-blue-900 transition-colors"
                            title="عرض التفاصيل"
                          >
                            <FileText className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        {requests.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-6">
            <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <FileText className="h-8 w-8 text-blue-600" />
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className={`text-sm font-medium ${isDarkMode ? 'text-slate-400' : 'text-gray-500'} truncate`}>
                      إجمالي الطلبات
                    </dt>
                    <dd className={`text-lg font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {requests.length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Clock className="h-8 w-8 text-yellow-600" />
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className={`text-sm font-medium ${isDarkMode ? 'text-slate-400' : 'text-gray-500'} truncate`}>
                      قيد المراجعة
                    </dt>
                    <dd className={`text-lg font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {requests.filter(r => r.Status === 'قيد المراجعة').length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className={`text-sm font-medium ${isDarkMode ? 'text-slate-400' : 'text-gray-500'} truncate`}>
                      معتمد
                    </dt>
                    <dd className={`text-lg font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {requests.filter(r => r.Status === 'معتمد').length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>

            <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <XCircle className="h-8 w-8 text-red-600" />
                </div>
                <div className="mr-5 w-0 flex-1">
                  <dl>
                    <dt className={`text-sm font-medium ${isDarkMode ? 'text-slate-400' : 'text-gray-500'} truncate`}>
                      مرفوض
                    </dt>
                    <dd className={`text-lg font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                      {requests.filter(r => r.Status === 'مرفوض').length}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
