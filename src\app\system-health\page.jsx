'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import {
  FiActivity,
  FiCheck,
  FiX,
  FiRefreshCw,
  FiDatabase,
  FiClock,
  FiHardDrive,
  FiTrendingUp,
  FiAlertTriangle,
  FiCheckCircle,
  FiXCircle
} from 'react-icons/fi';

export default function SystemHealthPage() {
  const [healthData, setHealthData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(null);

  useEffect(() => {
    checkSystemHealth();
    // تحديث تلقائي كل 30 ثانية
    const interval = setInterval(checkSystemHealth, 30000);
    return () => clearInterval(interval);
  }, []);

  const checkSystemHealth = async () => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/system-health', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
      });

      const result = await response.json();
      if (result.success) {
        setHealthData(result.data);
        setLastUpdate(new Date());
      }

    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  const getHealthColor = (status) => {
    switch (status) {
      case 'healthy': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'critical': return 'text-red-600 bg-red-100';
      case 'error': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getHealthIcon = (status) => {
    switch (status) {
      case 'healthy': return <FiCheckCircle className="text-green-500" />;
      case 'warning': return <FiAlertTriangle className="text-yellow-500" />;
      case 'critical': return <FiXCircle className="text-red-500" />;
      case 'error': return <FiXCircle className="text-red-500" />;
      default: return <FiActivity className="text-gray-500" />;
    }
  };

  const getGradeColor = (grade) => {
    switch (grade) {
      case 'A': return 'text-green-600 bg-green-100';
      case 'B': return 'text-blue-600 bg-blue-100';
      case 'C': return 'text-yellow-600 bg-yellow-100';
      case 'D': return 'text-orange-600 bg-orange-100';
      case 'F': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto p-6">
        {/* رأس الصفحة */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FiActivity className="text-3xl text-blue-600" />
              <div>
                <h1 className="text-3xl font-bold text-gray-800 dark:text-white">
                  مراقبة صحة النظام
                </h1>
                <p className="text-gray-600 dark:text-gray-300">
                  مراقبة مستمرة لحالة النظام والأداء
                </p>
              </div>
            </div>
            <div className="flex items-center gap-4">
              {lastUpdate && (
                <div className="text-sm text-gray-500">
                  آخر تحديث: {lastUpdate.toLocaleTimeString('ar-EG')}
                </div>
              )}
              <button
                onClick={checkSystemHealth}
                disabled={loading}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2 disabled:opacity-50"
              >
                <FiRefreshCw className={loading ? 'animate-spin' : ''} />
                تحديث
              </button>
            </div>
          </div>
        </div>

        {loading && !healthData ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600 dark:text-gray-300 mt-4">جاري فحص صحة النظام...</p>
          </div>
        ) : healthData ? (
          <div className="space-y-6">
            {/* الإحصائيات العامة */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
                      الحالة العامة
                    </p>
                    <div className="flex items-center gap-2 mt-1">
                      {getHealthIcon(healthData.overall_status)}
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getHealthColor(healthData.overall_status)}`}>
                        {healthData.overall_status === 'healthy' ? 'سليم' : 
                         healthData.overall_status === 'warning' ? 'تحذير' : 'حرج'}
                      </span>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className={`text-2xl font-bold px-3 py-1 rounded-lg ${getGradeColor(healthData.health_grade)}`}>
                      {healthData.health_grade}
                    </div>
                    <p className="text-xs text-gray-500 mt-1">{healthData.health_score}%</p>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
                      الجداول
                    </p>
                    <p className="text-2xl font-bold text-blue-600">
                      {healthData.tables.existing}/{healthData.tables.total}
                    </p>
                  </div>
                  <FiDatabase className="text-2xl text-blue-600" />
                </div>
                <div className="mt-2">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${healthData.tables.percentage}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">{healthData.tables.percentage}% مكتمل</p>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
                      إجمالي السجلات
                    </p>
                    <p className="text-2xl font-bold text-green-600">
                      {healthData.data.total_records.toLocaleString('ar-EG')}
                    </p>
                  </div>
                  <FiHardDrive className="text-2xl text-green-600" />
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  {healthData.data.tables_with_data} جدول يحتوي على بيانات
                </p>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
                      الأداء
                    </p>
                    <p className="text-2xl font-bold text-purple-600">
                      {healthData.performance.filter(p => p.result === 'success').length}/
                      {healthData.performance.length}
                    </p>
                  </div>
                  <FiTrendingUp className="text-2xl text-purple-600" />
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  اختبارات الأداء
                </p>
              </div>
            </div>

            {/* تفاصيل الجداول */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                حالة الجداول
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Object.entries(healthData.table_details).map(([tableName, details]) => (
                  <div key={tableName} className={`p-4 rounded-lg border ${
                    details.status === 'healthy' ? 'bg-green-50 border-green-200' :
                    details.status === 'empty' ? 'bg-yellow-50 border-yellow-200' :
                    details.status === 'missing' ? 'bg-red-50 border-red-200' :
                    'bg-gray-50 border-gray-200'
                  }`}>
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-gray-800">{tableName}</span>
                      {details.status === 'healthy' ? (
                        <FiCheck className="text-green-500" />
                      ) : details.status === 'empty' ? (
                        <FiAlertTriangle className="text-yellow-500" />
                      ) : (
                        <FiX className="text-red-500" />
                      )}
                    </div>
                    
                    <div className="text-sm text-gray-600">
                      <p>السجلات: {details.recordCount.toLocaleString('ar-EG')}</p>
                      <p>الأعمدة: {details.structure.length}</p>
                      <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium mt-1 ${
                        details.status === 'healthy' ? 'bg-green-100 text-green-800' :
                        details.status === 'empty' ? 'bg-yellow-100 text-yellow-800' :
                        details.status === 'missing' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {details.status === 'healthy' ? 'سليم' :
                         details.status === 'empty' ? 'فارغ' :
                         details.status === 'missing' ? 'مفقود' : 'خطأ'}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* اختبارات الأداء */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                اختبارات الأداء
              </h2>
              
              <div className="space-y-4">
                {healthData.performance.map((test, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex items-center gap-3">
                      {test.result === 'success' ? (
                        <FiCheck className="text-green-500" />
                      ) : (
                        <FiX className="text-red-500" />
                      )}
                      <div>
                        <span className="font-medium text-gray-800 dark:text-white">
                          {test.test === 'query_speed' ? 'سرعة الاستعلام' :
                           test.test === 'database_size' ? 'حجم قاعدة البيانات' : test.test}
                        </span>
                        {test.error && (
                          <p className="text-sm text-red-600">{test.error}</p>
                        )}
                      </div>
                    </div>
                    
                    <div className="text-right">
                      {test.time && (
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          test.status === 'excellent' ? 'bg-green-100 text-green-800' :
                          test.status === 'good' ? 'bg-blue-100 text-blue-800' :
                          test.status === 'slow' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {test.time}ms
                        </span>
                      )}
                      {test.size_mb && (
                        <span className="text-sm text-gray-600">
                          {Math.round(test.size_mb)} MB
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* معلومات إضافية */}
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
              <h3 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
                ملاحظات:
              </h3>
              <ul className="text-blue-600 dark:text-blue-300 space-y-1 text-sm">
                <li>• يتم تحديث البيانات تلقائياً كل 30 ثانية</li>
                <li>• الدرجة A تعني أن النظام يعمل بكفاءة عالية</li>
                <li>• الجداول الفارغة طبيعية في بداية استخدام النظام</li>
                <li>• يمكن إنشاء الجداول المفقودة من صفحة إعداد النظام</li>
              </ul>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <FiActivity className="text-4xl text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">اضغط على "تحديث" لبدء فحص النظام</p>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
