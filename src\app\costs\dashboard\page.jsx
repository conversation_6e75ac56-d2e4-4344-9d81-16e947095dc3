'use client';

import EditCostForm from '@/components/EditCostForm';
import MainLayout from '@/components/MainLayout';
import MonthlyCostForm from '@/components/MonthlyCostForm';
import { useEffect, useState } from 'react';

export default function CostsDashboard() {
  const [stats, setStats] = useState({
    cars: { total: 0, monthly: 0, count: 0 },
    apartments: { total: 0, monthly: 0, count: 0 },
    tempWorkers: { total: 0, monthly: 0, count: 0 },
    loading: true
  });
  const [selectedMonth, setSelectedMonth] = useState(6); // يونيو - آخر شهر متاح في البيانات
  const [selectedYear, setSelectedYear] = useState(2025);
  const [versionRequests, setVersionRequests] = useState({
    carscost: [],
    housingcost: [],
    '3amala': []
  });
  const [showAddCostForm, setShowAddCostForm] = useState(false);
  const [selectedCostType, setSelectedCostType] = useState('');
  const [showAttachmentOptions, setShowAttachmentOptions] = useState({});
  const [showEditForm, setShowEditForm] = useState(false);
  const [editingCost, setEditingCost] = useState(null);
  const [registeredCosts, setRegisteredCosts] = useState([]);
  const [showCostsTable, setShowCostsTable] = useState(false);
  const [syncingData, setSyncingData] = useState(false);
  const [loadingCosts, setLoadingCosts] = useState(false);

  const months = [
    { value: 1, label: 'يناير' },
    { value: 2, label: 'فبراير' },
    { value: 3, label: 'مارس' },
    { value: 4, label: 'أبريل' },
    { value: 5, label: 'مايو' },
    { value: 6, label: 'يونيو' },
    { value: 7, label: 'يوليو' },
    { value: 8, label: 'أغسطس' },
    { value: 9, label: 'سبتمبر' },
    { value: 10, label: 'أكتوبر' },
    { value: 11, label: 'نوفمبر' },
    { value: 12, label: 'ديسمبر' }
  ];

  const costTypes = [
    {
      id: 'carscost',
      title: 'تكاليف السيارات',
      archivePath: 'E:\\web\\project\\archiv\\carscost\\',
      link: '/costs/cars'
    },
    {
      id: 'housingcost',
      title: 'تكاليف الشقق',
      archivePath: 'E:\\web\\project\\archiv\\housingcost\\',
      link: '/costs/apartments'
    },
    {
      id: '3amala',
      title: 'تكاليف العمالة المؤقتة',
      archivePath: 'E:\\web\\project\\archiv\\3amala\\',
      link: '/costs/temp-workers'
    }
  ];

  useEffect(() => {
    setupSystem();
  }, []); // تشغيل مرة واحدة فقط

  useEffect(() => {
    // تأخير قصير لضمان تحديث الفلاتر أولاً
    const timer = setTimeout(() => {
      loadCostStats();
      loadVersionRequests();
      if (showCostsTable) {
        loadRegisteredCosts();
      }
    }, 50);

    return () => clearTimeout(timer);
  }, [selectedMonth, selectedYear]); // تحديث عند تغيير الشهر أو السنة

  // دالة لإعادة تحميل البيانات يدوياً
  const refreshData = () => {
    loadCostStats();
    loadVersionRequests();
    if (showCostsTable) {
      loadRegisteredCosts();
    }
  };

  useEffect(() => {
    if (showCostsTable) {
      loadRegisteredCosts();
    }
  }, [showCostsTable]);

  // إعداد النظام
  const setupSystem = async () => {
    try {
      const response = await fetch('/api/monthly-costs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'setup' })
      });

      const result = await response.json();
      if (!result.success) {

      }
    } catch (error) {

    }
  };

  const loadCostStats = async () => {
    try {
      // إعادة تعيين البيانات أولاً لتجنب عرض البيانات القديمة
      setStats({
        cars: { total: 0, monthly: 0, count: 0 },
        apartments: { total: 0, monthly: 0, count: 0 },
        tempWorkers: { total: 0, monthly: 0, count: 0 },
        loading: true
      });

      // جلب إحصائيات التكاليف الحقيقية مع منع التخزين المؤقت
      const response = await fetch('/api/monthly-costs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        },
        body: JSON.stringify({
          action: 'getCostStats',
          month: selectedMonth,
          year: selectedYear,
          timestamp: Date.now() // لضمان عدم التخزين المؤقت
        })
      });

      const result = await response.json();
      console.log('📊 بيانات التكاليف المستلمة:', result);

      if (result.success) {
        console.log('✅ تحديث البيانات للشهر:', selectedMonth, 'السنة:', selectedYear);
        console.log('📈 البيانات الجديدة:', result.data);

        // تحديث فوري للبيانات
        setStats({
          cars: result.data.cars || { total: 0, monthly: 0, count: 0 },
          apartments: result.data.apartments || { total: 0, monthly: 0, count: 0 },
          tempWorkers: result.data.tempWorkers || { total: 0, monthly: 0, count: 0 },
          loading: false
        });
      } else {
        console.error('خطأ في جلب البيانات:', result.error);
        // استخدام بيانات فارغة في حالة الفشل
        setStats({
          cars: { total: 0, monthly: 0, count: 0 },
          apartments: { total: 0, monthly: 0, count: 0 },
          tempWorkers: { total: 0, monthly: 0, count: 0 },
          loading: false
        });
      }
    } catch (error) {

      setStats({
        cars: { total: 0, monthly: 0, count: 0 },
        apartments: { total: 0, monthly: 0, count: 0 },
        tempWorkers: { total: 0, monthly: 0, count: 0 },
        loading: false
      });
    }
  };

  // جلب طلبات الإصدار
  const loadVersionRequests = async () => {
    try {
      const requests = {};

      for (const costType of costTypes) {
        const response = await fetch('/api/monthly-costs', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'getVersionRequests',
            costType: costType.id
          })
        });

        const result = await response.json();
        if (result.success) {
          requests[costType.id] = result.data;
        } else {
          requests[costType.id] = [];
        }
      }

      setVersionRequests(requests);
    } catch (error) {

    }
  };

  // رفع ملف طلب الإصدار
  const uploadVersionDocument = async (costType, file, isAttachment = false) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('costType', costType);
      formData.append('month', selectedMonth);
      formData.append('year', selectedYear);
      formData.append('isAttachment', isAttachment.toString());

      const response = await fetch('/api/upload-version', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();
      if (result.success) {
        const attachmentText = isAttachment ? '-ملحق' : '';
        alert(`تم رفع ملف ${selectedMonth}-${selectedYear}${attachmentText}.pdf بنجاح`);
        loadVersionRequests();
        setShowAttachmentOptions(prev => ({ ...prev, [costType]: false }));
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {
      alert('خطأ في رفع الملف: ' + error.message);
    }
  };

  // فتح نافذة اختيار الملف
  const openFileUpload = (costType, isAttachment = false) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.pdf';
    input.onchange = (e) => {
      const file = e.target.files[0];
      if (file) {
        if (file.type !== 'application/pdf') {
          alert('يجب اختيار ملف PDF فقط');
          return;
        }
        uploadVersionDocument(costType, file, isAttachment);
      }
    };
    input.click();
  };

  // إظهار خيارات الملحق للشقق
  const showAttachmentOptionsForHousing = (costType) => {
    if (costType === 'housingcost') {
      setShowAttachmentOptions(prev => ({ ...prev, [costType]: true }));
    } else {
      openFileUpload(costType, false);
    }
  };

  // حفظ التكاليف الشهرية
  const saveMonthlyCosts = async (costData) => {
    try {
      const response = await fetch('/api/monthly-costs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'createMonthlyCosts',
          ...costData
        })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم حفظ التكاليف الشهرية بنجاح');
        setShowAddCostForm(false);
        setSelectedCostType('');
        loadCostStats();
        loadVersionRequests();
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {
      alert('خطأ في حفظ التكاليف: ' + error.message);
    }
  };

  // جلب التكاليف المسجلة
  const loadRegisteredCosts = async () => {
    setLoadingCosts(true);
    try {
      // جلب جميع التكاليف للشهر والسنة المحددة
      const response = await fetch('/api/monthly-costs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getMonthlyCosts',
          month: selectedMonth,
          year: selectedYear,
          includeDetails: true
        })
      });

      const result = await response.json();
      if (result.success && result.data.length > 0) {
        // إضافة اسم نوع التكلفة لكل سجل
        const costsWithNames = result.data.map(cost => {
          const costType = costTypes.find(ct => ct.id === cost.CostType);
          return {
            ...cost,
            costTypeName: costType ? costType.title : cost.CostType
          };
        });
        setRegisteredCosts(costsWithNames);
      } else {
        setRegisteredCosts([]);
      }
    } catch (error) {
      console.error('خطأ في جلب التكاليف المسجلة:', error);
      setRegisteredCosts([]);
    } finally {
      setLoadingCosts(false);
    }
  };

  // فتح نموذج تعديل التكاليف
  const openEditForm = (cost) => {
    setEditingCost(cost);
    setShowEditForm(true);
  };

  // تحديث التكاليف
  const updateCost = async (updatedData) => {
    try {
      const response = await fetch('/api/monthly-costs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'updateMonthlyCosts',
          id: editingCost.ID,
          ...updatedData
        })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم تحديث التكاليف بنجاح');
        setShowEditForm(false);
        setEditingCost(null);
        loadCostStats();
        loadRegisteredCosts();
        loadVersionRequests();
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {
      alert('خطأ في تحديث التكاليف: ' + error.message);
    }
  };

  // حذف التكاليف
  const deleteCost = async (costId) => {
    if (!confirm('هل أنت متأكد من حذف هذه التكاليف؟')) {
      return;
    }

    try {
      const response = await fetch('/api/monthly-costs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'deleteMonthlyCosts',
          id: costId
        })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم حذف التكاليف بنجاح');
        loadCostStats();
        loadRegisteredCosts();
        loadVersionRequests();
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {
      alert('خطأ في حذف التكاليف: ' + error.message);
    }
  };

  // مزامنة البيانات الفعلية
  const syncRealCosts = async () => {
    if (!confirm('هل تريد مزامنة البيانات الفعلية من الجداول الأصلية؟\nسيتم استبدال البيانات المزامنة السابقة.')) {
      return;
    }

    setSyncingData(true);
    try {
      const response = await fetch('/api/sync-costs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'syncRealCosts' })
      });

      const result = await response.json();
      if (result.success) {
        alert(`تم مزامنة ${result.syncedCount} سجل من البيانات الفعلية بنجاح`);
        loadCostStats();
        loadRegisteredCosts();
        loadVersionRequests();
      } else {
        alert('خطأ في المزامنة: ' + result.error);
      }
    } catch (error) {
      alert('خطأ في مزامنة البيانات: ' + error.message);
    } finally {
      setSyncingData(false);
    }
  };



  // فتح نموذج إضافة التكاليف
  const openAddCostForm = (costType) => {
    setSelectedCostType(costType);
    setShowAddCostForm(true);
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <span className="text-3xl">💸</span>
              <div>
                <h1 className="text-3xl font-bold text-gray-800">لوحة تحكم التكاليف الشهرية</h1>
                <p className="text-gray-600">نظام شامل للتكاليف الدورية مع الأرشفة التلقائية</p>
              </div>
            </div>
            <div className="flex gap-3">
              <select
                value={selectedMonth}
                onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              >
                {months.map(month => (
                  <option key={month.value} value={month.value}>
                    {month.label}
                  </option>
                ))}
              </select>
              <select
                value={selectedYear}
                onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              >
                {[2023, 2024, 2025, 2026].map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>

              {/* زر التحديث */}
              <button
                onClick={refreshData}
                disabled={stats.loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-colors"
                title="تحديث البيانات"
              >
                <span className={`text-lg ${stats.loading ? 'animate-spin' : ''}`}>
                  {stats.loading ? '⏳' : '🔄'}
                </span>
                {stats.loading ? 'جاري التحديث...' : 'تحديث'}
              </button>
            </div>
          </div>
        </div>

        {/* Cost Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          {/* Car Costs */}
          <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-orange-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">تكاليف السيارات الشهرية</p>
                <p className="text-3xl font-bold text-orange-600">
                  {stats.loading ? '🔄 جاري التحميل...' : formatCurrency(stats.cars.monthly)}
                </p>
                <div className="flex justify-between items-center mt-1">
                  <p className="text-xs text-gray-500">
                    الإجمالي السنوي: {stats.loading ? '...' : formatCurrency(stats.cars.total)}
                  </p>
                  <p className="text-xs text-orange-600 font-medium">
                    {stats.loading ? '...' : `${stats.cars.count} سيارة`}
                  </p>
                </div>
              </div>
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                <span className="text-2xl">🚗</span>
              </div>
            </div>
          </div>

          {/* Apartment Costs */}
          <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-purple-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">تكاليف الشقق الشهرية</p>
                <p className="text-3xl font-bold text-purple-600">
                  {stats.loading ? '🔄 جاري التحميل...' : formatCurrency(stats.apartments.monthly)}
                </p>
                <div className="flex justify-between items-center mt-1">
                  <p className="text-xs text-gray-500">
                    الإجمالي السنوي: {stats.loading ? '...' : formatCurrency(stats.apartments.total)}
                  </p>
                  <p className="text-xs text-purple-600 font-medium">
                    {stats.loading ? '...' : `${stats.apartments.count} شقة`}
                  </p>
                </div>
              </div>
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <span className="text-2xl">🏢</span>
              </div>
            </div>
          </div>

          {/* Temp Workers Costs */}
          <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-red-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">تكاليف العمالة المؤقتة الشهرية</p>
                <p className="text-3xl font-bold text-red-600">
                  {stats.loading ? '🔄 جاري التحميل...' : formatCurrency(stats.tempWorkers.monthly)}
                </p>
                <div className="flex justify-between items-center mt-1">
                  <p className="text-xs text-gray-500">
                    الإجمالي السنوي: {stats.loading ? '...' : formatCurrency(stats.tempWorkers.total)}
                  </p>
                  <p className="text-xs text-red-600 font-medium">
                    {stats.loading ? '...' : `${stats.tempWorkers.count} عامل`}
                  </p>
                </div>
              </div>
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                <span className="text-2xl">👷</span>
              </div>
            </div>
          </div>
        </div>

        {/* Total Summary */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">إجمالي التكاليف</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="text-center p-6 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600 mb-2">إجمالي التكاليف السنوية</p>
              <p className="text-4xl font-bold text-gray-800">
                {formatCurrency(stats.cars.total + stats.apartments.total + stats.tempWorkers.total)}
              </p>
            </div>
            <div className="text-center p-6 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-600 mb-2">إجمالي التكاليف الشهرية</p>
              <p className="text-4xl font-bold text-blue-600">
                {formatCurrency(stats.cars.monthly + stats.apartments.monthly + stats.tempWorkers.monthly)}
              </p>
            </div>
          </div>
        </div>

        {/* Version Requests Section */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">
            طلبات الإصدار - {months.find(m => m.value === selectedMonth)?.label} {selectedYear}
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {costTypes.map((costType) => (
              <div key={costType.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-gray-800">{costType.title}</h4>
                  <div className="relative">
                    <button
                      onClick={() => showAttachmentOptionsForHousing(costType.id)}
                      className="text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 flex items-center gap-1"
                    >
                      📎 رفع {selectedMonth}-{selectedYear}.pdf
                    </button>

                    {/* خيارات الملحق للشقق */}
                    {costType.id === 'housingcost' && showAttachmentOptions[costType.id] && (
                      <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-48">
                        <div className="p-3">
                          <p className="text-xs text-gray-600 mb-2 font-medium">نوع طلب الإصدار:</p>
                          <div className="space-y-2">
                            <button
                              onClick={() => {
                                openFileUpload(costType.id, false);
                                setShowAttachmentOptions(prev => ({ ...prev, [costType.id]: false }));
                              }}
                              className="w-full text-left text-xs bg-blue-50 hover:bg-blue-100 p-2 rounded border"
                            >
                              <div className="font-medium text-blue-800">طلب إصدار أساسي</div>
                              <div className="text-blue-600">{selectedMonth}-{selectedYear}.pdf</div>
                            </button>
                            <button
                              onClick={() => {
                                openFileUpload(costType.id, true);
                                setShowAttachmentOptions(prev => ({ ...prev, [costType.id]: false }));
                              }}
                              className="w-full text-left text-xs bg-orange-50 hover:bg-orange-100 p-2 rounded border"
                            >
                              <div className="font-medium text-orange-800">طلب إصدار ملحق</div>
                              <div className="text-orange-600">{selectedMonth}-{selectedYear}-ملحق.pdf</div>
                            </button>
                          </div>
                          <button
                            onClick={() => setShowAttachmentOptions(prev => ({ ...prev, [costType.id]: false }))}
                            className="w-full mt-2 text-xs text-gray-500 hover:text-gray-700"
                          >
                            إلغاء
                          </button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                <div className="text-xs text-gray-600 mb-2">
                  <strong>مسار الأرشيف:</strong>
                </div>
                <div className="text-xs font-mono bg-gray-100 p-2 rounded mb-3">
                  {costType.archivePath}
                </div>

                <div className="text-xs text-gray-600">
                  <strong>الملفات المحفوظة:</strong>
                </div>
                {versionRequests[costType.id] && versionRequests[costType.id].length > 0 ? (
                  <div className="space-y-1 mt-2">
                    {versionRequests[costType.id].slice(0, 3).map((request) => (
                      <div key={request.id} className="flex items-center justify-between text-xs bg-green-50 p-2 rounded">
                        <span className="font-medium">{request.versionName}.pdf</span>
                        <span className="text-green-600">{formatCurrency(request.totalAmount)}</span>
                      </div>
                    ))}
                    {versionRequests[costType.id].length > 3 && (
                      <div className="text-xs text-gray-500 text-center">
                        +{versionRequests[costType.id].length - 3} ملف آخر
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-xs text-gray-500 mt-2 text-center py-2 bg-gray-50 rounded">
                    لا توجد ملفات PDF محفوظة
                  </div>
                )}

                {/* أزرار الإجراءات */}
                <div className="flex gap-2 mt-4">
                  <button
                    onClick={() => openAddCostForm(costType.id)}
                    className="flex-1 bg-orange-600 text-white text-center py-2 px-3 rounded text-xs hover:bg-orange-700"
                  >
                    💰 إضافة تكاليف شهرية
                  </button>
                  <a
                    href={costType.link}
                    className="bg-blue-600 text-white py-2 px-3 rounded text-xs hover:bg-blue-700"
                  >
                    📋 تفاصيل
                  </a>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800">إجراءات سريعة</h3>
            <div className="flex gap-2">
              <button
                onClick={() => setShowCostsTable(!showCostsTable)}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
              >
                <span className="text-lg">📋</span>
                {showCostsTable ? 'إخفاء التكاليف المسجلة' : 'عرض التكاليف المسجلة'}
              </button>
              <button
                onClick={syncRealCosts}
                disabled={syncingData}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <span className="text-lg">🔄</span>
                {syncingData ? 'جاري المزامنة...' : 'مزامنة البيانات الفعلية'}
              </button>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <a
              href="/costs/cars"
              className="bg-orange-600 text-white p-4 rounded-lg hover:bg-orange-700 transition-colors text-center group"
            >
              <span className="text-2xl block mb-2">🚗</span>
              <div className="font-medium">تكاليف السيارات</div>
              <div className="text-xs opacity-80 mt-1">إدارة تكاليف السيارات</div>
            </a>

            <a
              href="/costs/apartments"
              className="bg-purple-600 text-white p-4 rounded-lg hover:bg-purple-700 transition-colors text-center group"
            >
              <span className="text-2xl block mb-2">🏢</span>
              <div className="font-medium">تكاليف الشقق</div>
              <div className="text-xs opacity-80 mt-1">إدارة تكاليف الشقق</div>
            </a>

            <a
              href="/costs/temp-workers"
              className="bg-red-600 text-white p-4 rounded-lg hover:bg-red-700 transition-colors text-center group"
            >
              <span className="text-2xl block mb-2">👷</span>
              <div className="font-medium">تكاليف العمالة المؤقتة</div>
              <div className="text-xs opacity-80 mt-1">إدارة تكاليف العمالة</div>
            </a>

            <a
              href="/costs/charts"
              className="bg-indigo-600 text-white p-4 rounded-lg hover:bg-indigo-700 transition-colors text-center group"
            >
              <span className="text-2xl block mb-2">📊</span>
              <div className="font-medium">الرسوم البيانية</div>
              <div className="text-xs opacity-80 mt-1">عرض تحليل التكاليف</div>
            </a>

            <a
              href="/costs/version-requests"
              className="bg-teal-600 text-white p-4 rounded-lg hover:bg-teal-700 transition-colors text-center group"
            >
              <span className="text-2xl block mb-2">📋</span>
              <div className="font-medium">طلبات الإصدار</div>
              <div className="text-xs opacity-80 mt-1">عرض طلبات الإصدار المسجلة</div>
            </a>
          </div>
        </div>

        {/* جدول التكاليف المسجلة */}
        {showCostsTable && (
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              التكاليف المسجلة - {months.find(m => m.value === selectedMonth)?.label} {selectedYear}
            </h3>

            {loadingCosts ? (
              <div className="text-center py-8">
                <div className="inline-flex items-center gap-2 text-blue-600">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                  <span>جاري تحميل التكاليف...</span>
                </div>
              </div>
            ) : registeredCosts.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        نوع التكلفة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الشهر/السنة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        إجمالي التكلفة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        عدد العناصر
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        متوسط التكلفة
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        تاريخ الإنشاء
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        المصدر
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        الإجراءات
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {registeredCosts.map((cost) => (
                      <tr key={cost.ID} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {cost.costTypeName}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {months.find(m => m.value === cost.Month)?.label} {cost.Year}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-semibold">
                          {formatCurrency(cost.TotalAmount)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {cost.ItemsCount}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatCurrency(cost.AverageCostPerItem || 0)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(cost.CreatedAt).toLocaleDateString('ar-EG')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            cost.CreatedBy === 'مزامنة البيانات الفعلية'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-blue-100 text-blue-800'
                          }`}>
                            {cost.CreatedBy === 'مزامنة البيانات الفعلية' ? '📊 بيانات فعلية' : '✏️ إدخال يدوي'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex gap-2">
                            {cost.CreatedBy !== 'مزامنة البيانات الفعلية' ? (
                              <>
                                <button
                                  onClick={() => openEditForm(cost)}
                                  className="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700"
                                >
                                  ✏️ تعديل
                                </button>
                                <button
                                  onClick={() => deleteCost(cost.ID)}
                                  className="bg-red-600 text-white px-3 py-1 rounded text-xs hover:bg-red-700"
                                >
                                  🗑️ حذف
                                </button>
                              </>
                            ) : (
                              <span className="text-xs text-gray-500 px-3 py-1">
                                📊 بيانات فعلية (غير قابلة للتعديل)
                              </span>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <span className="text-4xl block mb-2">📋</span>
                <p>لا توجد تكاليف مسجلة لهذا الشهر</p>
              </div>
            )}
          </div>
        )}

        {/* نموذج إضافة التكاليف الشهرية */}
        {showAddCostForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <MonthlyCostForm
                costType={selectedCostType}
                onSave={saveMonthlyCosts}
                onCancel={() => {
                  setShowAddCostForm(false);
                  setSelectedCostType('');
                }}
              />
            </div>
          </div>
        )}

        {/* نموذج تعديل التكاليف */}
        {showEditForm && editingCost && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <EditCostForm
                cost={editingCost}
                onSave={updateCost}
                onCancel={() => {
                  setShowEditForm(false);
                  setEditingCost(null);
                }}
              />
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
