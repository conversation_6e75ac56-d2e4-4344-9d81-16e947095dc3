'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import { useTheme } from '@/contexts/ThemeContext';
import {
  FiDollarSign,
  FiPlus,
  FiEdit3,
  FiTrash2,
  FiUpload,
  FiFileText,
  FiSearch,
  FiRefreshCw,
  FiSave,
  FiX,
  FiCheck
} from 'react-icons/fi';

export default function ProjectCustodyPage() {
  const { isDarkMode } = useTheme();
  const [loading, setLoading] = useState(false);
  const [custodyItems, setCustodyItems] = useState([]);
  const [filteredItems, setFilteredItems] = useState([]);
  const [mainCategories, setMainCategories] = useState([]);
  const [subCategories, setSubCategories] = useState([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showSettlementModal, setShowSettlementModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterMainCategory, setFilterMainCategory] = useState('all');
  const [filterSubCategory, setFilterSubCategory] = useState('all');
  const [filterCustodyType, setFilterCustodyType] = useState('all');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');

  // بيانات النموذج
  const [formData, setFormData] = useState({
    itemName: '',
    itemType: '',
    description: '',
    quantity: '',
    unitPrice: '',
    totalValue: '',
    custodian: '',
    custodianId: '',
    receiveDate: '',
    expectedReturnDate: '',
    actualReturnDate: '',
    status: 'مستلم',
    location: '',
    condition: 'جيد',
    notes: '',
    invoiceNumber: '',
    supplier: ''
  });

  // أنواع العهد
  const custodyTypes = [
    'أجهزة كمبيوتر',
    'أجهزة محمولة',
    'معدات مكتبية',
    'أدوات وآلات',
    'مركبات',
    'أثاث',
    'معدات أمان',
    'أجهزة اتصالات',
    'مواد استهلاكية',
    'أخرى'
  ];

  // حالات العهد
  const custodyStatuses = [
    'مستلم',
    'مُسلم',
    'تالف',
    'مفقود',
    'قيد الصيانة',
    'مُستبدل'
  ];

  // حالة العهد
  const itemConditions = [
    'ممتاز',
    'جيد جداً',
    'جيد',
    'مقبول',
    'يحتاج صيانة',
    'تالف'
  ];

  useEffect(() => {
    loadCustodyData();
  }, []);

  useEffect(() => {
    filterCustodyItems();
  }, [custodyItems, searchTerm, filterStatus, filterMainCategory, filterSubCategory, filterCustodyType]);

  // تحميل بيانات العهد
  const loadCustodyData = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/project-custody', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'getCustodyItems' })
      });

      const result = await response.json();
      if (result.success) {
        setCustodyItems(result.data || []);
      } else {

        // بيانات تجريبية في حالة عدم وجود API
        setCustodyItems([
          {
            id: 1,
            itemName: 'لابتوب Dell Latitude',
            itemType: 'أجهزة كمبيوتر',
            description: 'لابتوب للعمل المكتبي',
            quantity: 1,
            unitPrice: 15000,
            totalValue: 15000,
            custodian: 'أحمد محمد علي',
            custodianId: '1001',
            receiveDate: '2025-01-15',
            expectedReturnDate: '2025-12-31',
            actualReturnDate: '',
            status: 'مستلم',
            location: 'مكتب الإدارة',
            condition: 'جيد',
            notes: 'تم التسليم بحالة ممتازة',
            invoiceNumber: 'INV-2025-001',
            supplier: 'شركة التقنية المتقدمة'
          },
          {
            id: 2,
            itemName: 'طابعة HP LaserJet',
            itemType: 'معدات مكتبية',
            description: 'طابعة ليزر للمكتب',
            quantity: 1,
            unitPrice: 3500,
            totalValue: 3500,
            custodian: 'سارة أحمد محمد',
            custodianId: '1002',
            receiveDate: '2025-02-01',
            expectedReturnDate: '2025-12-31',
            actualReturnDate: '',
            status: 'مستلم',
            location: 'قسم المحاسبة',
            condition: 'ممتاز',
            notes: '',
            invoiceNumber: 'INV-2025-002',
            supplier: 'مكتبة الأعمال'
          }
        ]);
      }
    } catch (error) {

      setCustodyItems([]);
    }
    setLoading(false);
  };

  // فلترة عناصر العهد
  const filterCustodyItems = () => {
    let filtered = custodyItems;

    // البحث النصي
    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.itemName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.custodian.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.custodianId.includes(searchTerm) ||
        item.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // فلترة حسب الحالة
    if (filterStatus !== 'all') {
      filtered = filtered.filter(item => item.status === filterStatus);
    }

    // فلترة حسب البند الرئيسي
    if (filterMainCategory !== 'all') {
      filtered = filtered.filter(item => item.mainCategoryId == filterMainCategory);
    }

    // فلترة حسب البند الفرعي
    if (filterSubCategory !== 'all') {
      filtered = filtered.filter(item => item.subCategoryId == filterSubCategory);
    }

    // فلترة حسب نوع العهدة
    if (filterCustodyType !== 'all') {
      filtered = filtered.filter(item => item.custodyType === filterCustodyType);
    }

    setFilteredItems(filtered);
  };

  // حساب القيمة الإجمالية
  const calculateTotalValue = () => {
    const quantity = parseFloat(formData.quantity) || 0;
    const unitPrice = parseFloat(formData.unitPrice) || 0;
    const total = quantity * unitPrice;
    setFormData(prev => ({ ...prev, totalValue: total.toString() }));
  };

  useEffect(() => {
    calculateTotalValue();
  }, [formData.quantity, formData.unitPrice]);

  // إضافة عنصر جديد
  const handleAddItem = async () => {
    if (!formData.itemName || !formData.custodian || !formData.quantity) {
      alert('يرجى ملء الحقول المطلوبة');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/project-custody', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'addCustodyItem',
          data: formData
        })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم إضافة العنصر بنجاح');
        setShowAddModal(false);
        resetForm();
        loadCustodyData();
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {

      alert('خطأ في إضافة العنصر');
    }
    setLoading(false);
  };

  // تحديث عنصر
  const handleUpdateItem = async () => {
    if (!formData.itemName || !formData.custodian || !formData.quantity) {
      alert('يرجى ملء الحقول المطلوبة');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/project-custody', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'updateCustodyItem',
          id: selectedItem.id,
          data: formData
        })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم تحديث العنصر بنجاح');
        setShowEditModal(false);
        resetForm();
        loadCustodyData();
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {

      alert('خطأ في تحديث العنصر');
    }
    setLoading(false);
  };

  // حذف عنصر
  const handleDeleteItem = async (id) => {
    if (!confirm('هل أنت متأكد من حذف هذا العنصر؟')) return;

    setLoading(true);
    try {
      const response = await fetch('/api/project-custody', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'deleteCustodyItem',
          id: id
        })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم حذف العنصر بنجاح');
        loadCustodyData();
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {

      alert('خطأ في حذف العنصر');
    }
    setLoading(false);
  };

  // إعادة تعيين النموذج
  const resetForm = () => {
    setFormData({
      itemName: '',
      itemType: '',
      description: '',
      quantity: '',
      unitPrice: '',
      totalValue: '',
      custodian: '',
      custodianId: '',
      receiveDate: '',
      expectedReturnDate: '',
      actualReturnDate: '',
      status: 'مستلم',
      location: '',
      condition: 'جيد',
      notes: '',
      invoiceNumber: '',
      supplier: ''
    });
    setSelectedItem(null);
  };

  // فتح نموذج التعديل
  const openEditModal = (item) => {
    setSelectedItem(item);
    setFormData({
      itemName: item.itemName || '',
      itemType: item.itemType || '',
      description: item.description || '',
      quantity: item.quantity?.toString() || '',
      unitPrice: item.unitPrice?.toString() || '',
      totalValue: item.totalValue?.toString() || '',
      custodian: item.custodian || '',
      custodianId: item.custodianId || '',
      receiveDate: item.receiveDate || '',
      expectedReturnDate: item.expectedReturnDate || '',
      actualReturnDate: item.actualReturnDate || '',
      status: item.status || 'مستلم',
      location: item.location || '',
      condition: item.condition || 'جيد',
      notes: item.notes || '',
      invoiceNumber: item.invoiceNumber || '',
      supplier: item.supplier || ''
    });
    setShowEditModal(true);
  };

  // تنسيق التاريخ
  const formatDate = (dateString) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('ar-EG');
  };

  // تنسيق المبلغ
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG').format(amount) + ' جنيه';
  };

  // حساب الإحصائيات
  const getStatistics = () => {
    const totalItems = custodyItems.length;
    const totalValue = custodyItems.reduce((sum, item) => sum + (item.totalValue || 0), 0);
    const activeItems = custodyItems.filter(item => item.status === 'مستلم').length;
    const returnedItems = custodyItems.filter(item => item.status === 'مُسلم').length;

    return { totalItems, totalValue, activeItems, returnedItems };
  };

  // إحصائيات البنود الرئيسية
  const getMainCategoryStats = () => {
    const categoryStats = {};
    filteredItems.forEach(item => {
      const categoryName = item.mainCategoryName || 'غير محدد';
      if (!categoryStats[categoryName]) {
        categoryStats[categoryName] = { count: 0, amount: 0 };
      }
      categoryStats[categoryName].count += 1;
      categoryStats[categoryName].amount += item.amount || 0;
    });

    return Object.entries(categoryStats).map(([name, data]) => ({
      name,
      count: data.count,
      amount: data.amount
    }));
  };

  // إحصائيات الحالات
  const getStatusStats = () => {
    const statusStats = {};
    filteredItems.forEach(item => {
      const statusName = item.status || 'غير محدد';
      if (!statusStats[statusName]) {
        statusStats[statusName] = { count: 0, amount: 0 };
      }
      statusStats[statusName].count += 1;
      statusStats[statusName].amount += item.amount || 0;
    });

    return Object.entries(statusStats).map(([name, data]) => ({
      name,
      count: data.count,
      amount: data.amount
    }));
  };

  // ألوان الرسوم البيانية
  const getChartColors = () => [
    '#3B82F6', '#10B981', '#F59E0B', '#EF4444',
    '#8B5CF6', '#EC4899', '#06B6D4', '#22C55E'
  ];

  // ألوان الحالات
  const getStatusColors = () => ({
    'مُصرفة': '#F59E0B',
    'مُسوّاة': '#10B981',
    'مُلغاة': '#EF4444',
    'غير محدد': '#6B7280'
  });

  const stats = getStatistics();

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto p-6">
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FiDollarSign className="text-3xl text-green-600" />
              <div>
                <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  عهد المشروع
                </h1>
                <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                  إدارة وتتبع عهد المشروع والأصول
                </p>
              </div>
            </div>
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
            >
              <FiPlus className="w-4 h-4" />
              إضافة عهدة جديدة
            </button>
          </div>
        </div>

        {/* الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <FiFileText className="w-8 h-8 text-blue-500 mr-3" />
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>إجمالي العهد</p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {stats.totalItems}
                </p>
              </div>
            </div>
          </div>

          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <FiDollarSign className="w-8 h-8 text-green-500 mr-3" />
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>القيمة الإجمالية</p>
                <p className={`text-lg font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {formatCurrency(stats.totalValue)}
                </p>
              </div>
            </div>
          </div>

          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <FiCheck className="w-8 h-8 text-green-500 mr-3" />
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>عهد نشطة</p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {stats.activeItems}
                </p>
              </div>
            </div>
          </div>

          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <FiUpload className="w-8 h-8 text-orange-500 mr-3" />
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>عهد مُسلمة</p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {stats.returnedItems}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* أدوات البحث والفلترة المحسنة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {/* نوع الرسم */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                نوع الرسم
              </label>
              <select
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="table">جدول</option>
                <option value="chart">رسم بياني</option>
              </select>
            </div>

            {/* من تاريخ */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                من تاريخ
              </label>
              <input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>

            {/* إلى تاريخ */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                إلى تاريخ
              </label>
              <input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>

            {/* البند الفرعي */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                البند الفرعي
              </label>
              <select
                value={filterSubCategory}
                onChange={(e) => setFilterSubCategory(e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="all">جميع البنود الفرعية</option>
                {subCategories.map((subCategory) => (
                  <option key={subCategory.id} value={subCategory.id}>
                    {subCategory.subCategoryName}
                  </option>
                ))}
              </select>
            </div>

            {/* البند الرئيسي */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                البند الرئيسي
              </label>
              <select
                value={filterMainCategory}
                onChange={(e) => setFilterMainCategory(e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="all">جميع البنود الرئيسية</option>
                {mainCategories.map((category) => (
                  <option key={category.id} value={category.id}>{category.categoryName}</option>
                ))}
              </select>
            </div>
          </div>

          {/* صف ثاني للفلاتر */}
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mt-4">
            {/* انتقالات */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                انتقالات
              </label>
              <select
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="all">جميع الانتقالات</option>
                <option value="consultant">انتقالات استشاري</option>
                <option value="missions">انتقالات مأموريات</option>
                <option value="allowance">انتقالات بدل</option>
                <option value="deduction">انتقالات بالخصم</option>
              </select>
            </div>

            {/* البحث في الحقول */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                البحث في الحقول
              </label>
              <div className="relative">
                <FiSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="البحث..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`w-full pr-10 pl-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                    isDarkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>
            </div>

            {/* أزرار العمليات */}
            <div className="flex gap-2">
              <button
                onClick={() => setShowAddModal(true)}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <FiPlus className="w-4 h-4" />
                إضافة
              </button>
              <button
                onClick={() => setShowSettlementModal(true)}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <FiCheck className="w-4 h-4" />
                تسوية
              </button>
            </div>

            {/* زر التحديث */}
            <div>
              <button
                onClick={loadCustodyData}
                disabled={loading}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors disabled:opacity-50"
              >
                <FiRefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                تحديث
              </button>
            </div>
          </div>
        </div>

        {/* جدول العهد */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border`}>
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              قائمة العهد ({filteredItems.length})
            </h2>
          </div>

          <div className="overflow-x-auto">
            {loading ? (
              <div className="flex justify-center items-center py-12">
                <FiRefreshCw className="w-6 h-6 animate-spin text-green-500" />
                <span className="mr-2">جاري التحميل...</span>
              </div>
            ) : filteredItems.length === 0 ? (
              <div className="text-center py-12">
                <FiFileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className={`text-lg ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                  لا توجد عهد مسجلة
                </p>
              </div>
            ) : (
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className={isDarkMode ? 'bg-gray-800' : 'bg-gray-50'}>
                  <tr>
                    <th className={`px-6 py-3 text-right text-xs font-medium uppercase tracking-wider ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-500'
                    }`}>
                      العنصر
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium uppercase tracking-wider ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-500'
                    }`}>
                      المستلم
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium uppercase tracking-wider ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-500'
                    }`}>
                      القيمة
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium uppercase tracking-wider ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-500'
                    }`}>
                      تاريخ الاستلام
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium uppercase tracking-wider ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-500'
                    }`}>
                      الحالة
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium uppercase tracking-wider ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-500'
                    }`}>
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className={`divide-y ${isDarkMode ? 'divide-gray-700 bg-gray-800' : 'divide-gray-200 bg-white'}`}>
                  {filteredItems.map((item) => (
                    <tr key={item.id} className={isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'}>
                      <td className={`px-6 py-4 whitespace-nowrap ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        <div>
                          <div className="text-sm font-medium">{item.itemName}</div>
                          <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>
                            {item.itemType} - الكمية: {item.quantity}
                          </div>
                        </div>
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        <div>
                          <div className="text-sm font-medium">{item.custodian}</div>
                          <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>
                            كود: {item.custodianId}
                          </div>
                        </div>
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {formatCurrency(item.totalValue)}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                        {formatDate(item.receiveDate)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          item.status === 'مستلم' ? 'bg-green-100 text-green-800' :
                          item.status === 'مُسلم' ? 'bg-blue-100 text-blue-800' :
                          item.status === 'تالف' ? 'bg-red-100 text-red-800' :
                          item.status === 'مفقود' ? 'bg-red-100 text-red-800' :
                          item.status === 'قيد الصيانة' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {item.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => openEditModal(item)}
                            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                            title="تعديل"
                          >
                            <FiEdit3 className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => {
                              setSelectedItem(item);
                              setShowSettlementModal(true);
                            }}
                            className="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300"
                            title="تسوية"
                          >
                            <FiCheck className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteItem(item.id)}
                            className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                            title="حذف"
                          >
                            <FiTrash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        </div>

        {/* قسم الرسوم البيانية */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <h2 className={`text-xl font-bold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            الرسوم البيانية والإحصائيات
          </h2>

          {/* إحصائيات سريعة */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-blue-50'} rounded-lg p-4 text-center`}>
              <div className="flex items-center justify-center mb-2">
                <FiFileText className="w-8 h-8 text-blue-500" />
              </div>
              <div className={`text-2xl font-bold ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`}>
                {filteredItems.length}
              </div>
              <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                إجمالي العهد
              </div>
            </div>

            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-green-50'} rounded-lg p-4 text-center`}>
              <div className="flex items-center justify-center mb-2">
                <FiDollarSign className="w-8 h-8 text-green-500" />
              </div>
              <div className={`text-lg font-bold ${isDarkMode ? 'text-green-400' : 'text-green-600'}`}>
                {formatCurrency(filteredItems.reduce((sum, item) => sum + (item.amount || 0), 0))}
              </div>
              <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                إجمالي المبالغ
              </div>
            </div>

            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-orange-50'} rounded-lg p-4 text-center`}>
              <div className="flex items-center justify-center mb-2">
                <FiCheck className="w-8 h-8 text-orange-500" />
              </div>
              <div className={`text-2xl font-bold ${isDarkMode ? 'text-orange-400' : 'text-orange-600'}`}>
                {filteredItems.filter(item => item.status === 'مُصرفة').length}
              </div>
              <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                عهد مُصرفة
              </div>
            </div>

            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-purple-50'} rounded-lg p-4 text-center`}>
              <div className="flex items-center justify-center mb-2">
                <FiUpload className="w-8 h-8 text-purple-500" />
              </div>
              <div className={`text-2xl font-bold ${isDarkMode ? 'text-purple-400' : 'text-purple-600'}`}>
                {filteredItems.filter(item => item.status === 'مُسوّاة').length}
              </div>
              <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                عهد مُسوّاة
              </div>
            </div>
          </div>

          {/* الرسوم البيانية */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* رسم بياني للبنود الرئيسية */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-gray-50'} rounded-lg p-6`}>
              <h3 className={`text-lg font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                التوزيع حسب البنود الرئيسية
              </h3>
              <div className="space-y-3">
                {getMainCategoryStats().map((category, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div
                        className="w-4 h-4 rounded"
                        style={{ backgroundColor: getChartColors()[index % getChartColors().length] }}
                      ></div>
                      <span className={`text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                        {category.name}
                      </span>
                    </div>
                    <div className="text-right">
                      <div className={`text-sm font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {formatCurrency(category.amount)}
                      </div>
                      <div className={`text-xs ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>
                        {category.count} عهدة
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* رسم بياني للحالات */}
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-gray-50'} rounded-lg p-6`}>
              <h3 className={`text-lg font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                التوزيع حسب الحالة
              </h3>
              <div className="space-y-3">
                {getStatusStats().map((status, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div
                        className="w-4 h-4 rounded"
                        style={{ backgroundColor: getStatusColors()[status.name] || '#6B7280' }}
                      ></div>
                      <span className={`text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                        {status.name}
                      </span>
                    </div>
                    <div className="text-right">
                      <div className={`text-sm font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {formatCurrency(status.amount)}
                      </div>
                      <div className={`text-xs ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>
                        {status.count} عهدة
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* نموذج إضافة عهدة جديدة */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto`}>
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h3 className={`text-lg font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    إضافة عهدة جديدة
                  </h3>
                  <button
                    onClick={() => {
                      setShowAddModal(false);
                      resetForm();
                    }}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <FiX className="w-6 h-6" />
                  </button>
                </div>
              </div>

              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* اسم العنصر */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      اسم العنصر *
                    </label>
                    <input
                      type="text"
                      value={formData.itemName}
                      onChange={(e) => setFormData(prev => ({ ...prev, itemName: e.target.value }))}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      placeholder="أدخل اسم العنصر"
                    />
                  </div>

                  {/* نوع العنصر */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      نوع العنصر
                    </label>
                    <select
                      value={formData.itemType}
                      onChange={(e) => setFormData(prev => ({ ...prev, itemType: e.target.value }))}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    >
                      <option value="">اختر النوع</option>
                      {custodyTypes.map((type) => (
                        <option key={type} value={type}>{type}</option>
                      ))}
                    </select>
                  </div>

                  {/* الوصف */}
                  <div className="md:col-span-2">
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      الوصف
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      rows={3}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      placeholder="وصف تفصيلي للعنصر"
                    />
                  </div>

                  {/* الكمية */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      الكمية *
                    </label>
                    <input
                      type="number"
                      value={formData.quantity}
                      onChange={(e) => setFormData(prev => ({ ...prev, quantity: e.target.value }))}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      placeholder="1"
                      min="1"
                    />
                  </div>

                  {/* سعر الوحدة */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      سعر الوحدة (جنيه)
                    </label>
                    <input
                      type="number"
                      value={formData.unitPrice}
                      onChange={(e) => setFormData(prev => ({ ...prev, unitPrice: e.target.value }))}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      placeholder="0.00"
                      min="0"
                      step="0.01"
                    />
                  </div>

                  {/* القيمة الإجمالية */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      القيمة الإجمالية (جنيه)
                    </label>
                    <input
                      type="number"
                      value={formData.totalValue}
                      readOnly
                      className={`w-full px-3 py-2 border rounded-lg bg-gray-100 ${
                        isDarkMode
                          ? 'bg-gray-600 border-gray-600 text-white'
                          : 'bg-gray-100 border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>

                  {/* المستلم */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      اسم المستلم *
                    </label>
                    <input
                      type="text"
                      value={formData.custodian}
                      onChange={(e) => setFormData(prev => ({ ...prev, custodian: e.target.value }))}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      placeholder="اسم الموظف المستلم"
                    />
                  </div>

                  {/* كود المستلم */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      كود المستلم
                    </label>
                    <input
                      type="text"
                      value={formData.custodianId}
                      onChange={(e) => setFormData(prev => ({ ...prev, custodianId: e.target.value }))}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      placeholder="كود الموظف"
                    />
                  </div>

                  {/* تاريخ الاستلام */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      تاريخ الاستلام
                    </label>
                    <input
                      type="date"
                      value={formData.receiveDate}
                      onChange={(e) => setFormData(prev => ({ ...prev, receiveDate: e.target.value }))}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>

                  {/* تاريخ الإرجاع المتوقع */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      تاريخ الإرجاع المتوقع
                    </label>
                    <input
                      type="date"
                      value={formData.expectedReturnDate}
                      onChange={(e) => setFormData(prev => ({ ...prev, expectedReturnDate: e.target.value }))}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>

                  {/* الحالة */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      الحالة
                    </label>
                    <select
                      value={formData.status}
                      onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value }))}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    >
                      {custodyStatuses.map((status) => (
                        <option key={status} value={status}>{status}</option>
                      ))}
                    </select>
                  </div>

                  {/* الموقع */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      الموقع
                    </label>
                    <input
                      type="text"
                      value={formData.location}
                      onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      placeholder="موقع العنصر"
                    />
                  </div>

                  {/* حالة العنصر */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      حالة العنصر
                    </label>
                    <select
                      value={formData.condition}
                      onChange={(e) => setFormData(prev => ({ ...prev, condition: e.target.value }))}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    >
                      {itemConditions.map((condition) => (
                        <option key={condition} value={condition}>{condition}</option>
                      ))}
                    </select>
                  </div>

                  {/* رقم الفاتورة */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      رقم الفاتورة
                    </label>
                    <input
                      type="text"
                      value={formData.invoiceNumber}
                      onChange={(e) => setFormData(prev => ({ ...prev, invoiceNumber: e.target.value }))}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      placeholder="رقم الفاتورة"
                    />
                  </div>

                  {/* المورد */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      المورد
                    </label>
                    <input
                      type="text"
                      value={formData.supplier}
                      onChange={(e) => setFormData(prev => ({ ...prev, supplier: e.target.value }))}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      placeholder="اسم المورد"
                    />
                  </div>

                  {/* الملاحظات */}
                  <div className="md:col-span-2">
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      الملاحظات
                    </label>
                    <textarea
                      value={formData.notes}
                      onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                      rows={3}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                      placeholder="ملاحظات إضافية"
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-3 mt-6">
                  <button
                    onClick={() => {
                      setShowAddModal(false);
                      resetForm();
                    }}
                    className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    إلغاء
                  </button>
                  <button
                    onClick={handleAddItem}
                    disabled={loading}
                    className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
                  >
                    <FiSave className="w-4 h-4" />
                    حفظ العهدة
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* نموذج تعديل العهدة */}
        {showEditModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto`}>
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h3 className={`text-lg font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    تعديل العهدة
                  </h3>
                  <button
                    onClick={() => {
                      setShowEditModal(false);
                      resetForm();
                    }}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <FiX className="w-6 h-6" />
                  </button>
                </div>
              </div>

              <div className="p-6">
                {/* نفس النموذج مع بيانات التعديل */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* نفس الحقول كما في نموذج الإضافة */}
                  {/* ... يمكن نسخ نفس الحقول هنا ... */}
                </div>

                <div className="flex justify-end gap-3 mt-6">
                  <button
                    onClick={() => {
                      setShowEditModal(false);
                      resetForm();
                    }}
                    className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    إلغاء
                  </button>
                  <button
                    onClick={handleUpdateItem}
                    disabled={loading}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
                  >
                    <FiSave className="w-4 h-4" />
                    تحديث العهدة
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* نموذج التسوية */}
        {showSettlementModal && selectedItem && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-xl max-w-2xl w-full`}>
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h3 className={`text-lg font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    تسوية العهدة - {selectedItem.itemName}
                  </h3>
                  <button
                    onClick={() => {
                      setShowSettlementModal(false);
                      setSelectedItem(null);
                    }}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <FiX className="w-6 h-6" />
                  </button>
                </div>
              </div>

              <div className="p-6">
                {/* معلومات العهدة */}
                <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-gray-50'} rounded-lg p-4 mb-6`}>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                        المبلغ الأصلي:
                      </span>
                      <span className={`block font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {formatCurrency(selectedItem.amount || 0)}
                      </span>
                    </div>
                    <div>
                      <span className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                        المبلغ المتبقي:
                      </span>
                      <span className={`block font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {formatCurrency(selectedItem.remainingAmount || selectedItem.amount || 0)}
                      </span>
                    </div>
                  </div>
                </div>

                {/* نموذج التسوية */}
                <div className="grid grid-cols-1 gap-4">
                  {/* رقم التسوية */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      رقم التسوية *
                    </label>
                    <input
                      type="text"
                      placeholder="مثال: SET-2025-001"
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>

                  {/* قيمة التسوية */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      قيمة التسوية (جنيه) *
                    </label>
                    <input
                      type="number"
                      placeholder="0.00"
                      min="0"
                      max={selectedItem.remainingAmount || selectedItem.amount || 0}
                      step="0.01"
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>

                  {/* تاريخ التسوية */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      تاريخ التسوية *
                    </label>
                    <input
                      type="date"
                      defaultValue={new Date().toISOString().split('T')[0]}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>

                  {/* ملاحظات التسوية */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      ملاحظات التسوية
                    </label>
                    <textarea
                      rows={3}
                      placeholder="ملاحظات حول التسوية..."
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-3 mt-6">
                  <button
                    onClick={() => {
                      setShowSettlementModal(false);
                      setSelectedItem(null);
                    }}
                    className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    إلغاء
                  </button>
                  <button
                    className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
                  >
                    <FiCheck className="w-4 h-4" />
                    تسوية العهدة
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
