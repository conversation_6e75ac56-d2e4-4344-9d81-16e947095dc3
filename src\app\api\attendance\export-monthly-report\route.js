import { NextResponse } from 'next/server';
import ExcelJS from 'exceljs';

export async function POST(request) {
  try {
    const { month, year, reportData } = await request.json();
    
    if (!reportData || !reportData.employees) {
      return NextResponse.json(
        { error: 'بيانات التقرير مطلوبة' },
        { status: 400 }
      );
    }

    // إنشاء ملف Excel جديد
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('كشف التمام الشهري');

    // تحديد فترة التقرير
    const startDate = new Date(year, month - 1, 11);
    const endDate = new Date(year, month, 10);
    
    // إنشاء قائمة الأيام
    const days = [];
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      days.push({
        date: new Date(currentDate),
        dayNumber: currentDate.getDate(),
        dayName: currentDate.toLocaleDateString('ar-EG', { weekday: 'short' })
      });
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // رموز الحضور
    const attendanceCodes = ['W', 'Ab', 'S', 'R', 'NH', 'CR', 'M', 'AL', 'CL', 'UL', 'ML'];

    // إعداد رأس التقرير
    worksheet.mergeCells('A1:' + String.fromCharCode(67 + days.length + attendanceCodes.length) + '1');
    worksheet.getCell('A1').value = 'حضور الموظفين المتحدة بمشروع أوجيستا';
    worksheet.getCell('A1').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A1').font = { size: 16, bold: true };

    // فترة التقرير
    worksheet.mergeCells('A2:' + String.fromCharCode(67 + days.length + attendanceCodes.length) + '2');
    worksheet.getCell('A2').value = `من ${startDate.toLocaleDateString('ar-EG')} إلى ${endDate.toLocaleDateString('ar-EG')}`;
    worksheet.getCell('A2').alignment = { horizontal: 'center', vertical: 'middle' };
    worksheet.getCell('A2').font = { size: 14 };

    // إعداد رؤوس الأعمدة
    let colIndex = 1;
    
    // الأعمدة الأساسية
    worksheet.getCell(4, colIndex++).value = 'م';
    worksheet.getCell(4, colIndex++).value = 'الاسم';
    worksheet.getCell(4, colIndex++).value = 'الكود';

    // أعمدة الأيام
    days.forEach(day => {
      const cell = worksheet.getCell(4, colIndex);
      cell.value = day.dayNumber;
      worksheet.getCell(3, colIndex).value = day.dayName;
      colIndex++;
    });

    // أعمدة الملخص
    attendanceCodes.forEach(code => {
      worksheet.getCell(4, colIndex++).value = code;
    });

    // تنسيق رؤوس الأعمدة
    for (let i = 1; i < colIndex; i++) {
      const headerCell3 = worksheet.getCell(3, i);
      const headerCell4 = worksheet.getCell(4, i);
      
      headerCell3.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE6E6FA' } };
      headerCell3.font = { bold: true };
      headerCell3.alignment = { horizontal: 'center', vertical: 'middle' };
      headerCell3.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };

      headerCell4.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFE6E6FA' } };
      headerCell4.font = { bold: true };
      headerCell4.alignment = { horizontal: 'center', vertical: 'middle' };
      headerCell4.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      };
    }

    // إضافة بيانات الموظفين
    let rowIndex = 5;
    reportData.employees.forEach((employee, employeeIndex) => {
      colIndex = 1;
      
      // البيانات الأساسية
      worksheet.getCell(rowIndex, colIndex++).value = employeeIndex + 1;
      worksheet.getCell(rowIndex, colIndex++).value = employee.EmployeeName;
      worksheet.getCell(rowIndex, colIndex++).value = employee.EmployeeCode;

      // بيانات الحضور اليومي
      days.forEach(day => {
        const dateStr = day.date.toISOString().split('T')[0];
        const attendance = employee.attendance?.[dateStr] || 'W';
        const cell = worksheet.getCell(rowIndex, colIndex);
        cell.value = attendance;
        
        // تلوين الخلايا حسب نوع الحضور
        switch (attendance) {
          case 'W':
            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF90EE90' } }; // أخضر فاتح
            break;
          case 'Ab':
            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFA0A0' } }; // أحمر فاتح
            break;
          case 'S':
            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFFF00' } }; // أصفر
            break;
          case 'R':
            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFADD8E6' } }; // أزرق فاتح
            break;
          case 'AL':
            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FF00FFFF' } }; // سماوي
            break;
          case 'M':
            cell.fill = { type: 'pattern', pattern: 'solid', fgColor: { argb: 'FFFFA500' } }; // برتقالي
            break;
        }
        
        cell.alignment = { horizontal: 'center', vertical: 'middle' };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
        
        colIndex++;
      });

      // بيانات الملخص
      attendanceCodes.forEach(code => {
        const count = employee.summary?.[code] || 0;
        const cell = worksheet.getCell(rowIndex, colIndex);
        cell.value = count;
        cell.alignment = { horizontal: 'center', vertical: 'middle' };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
        colIndex++;
      });

      // تنسيق الصف
      for (let i = 1; i < colIndex; i++) {
        const cell = worksheet.getCell(rowIndex, i);
        if (!cell.border) {
          cell.border = {
            top: { style: 'thin' },
            left: { style: 'thin' },
            bottom: { style: 'thin' },
            right: { style: 'thin' }
          };
        }
      }

      rowIndex++;
    });

    // إضافة التوقيعات
    const signatureRow = rowIndex + 3;
    const totalCols = 3 + days.length + attendanceCodes.length;
    const colsPerSignature = Math.floor(totalCols / 3);

    worksheet.getCell(signatureRow, 1).value = 'رئيس القطاع';
    worksheet.getCell(signatureRow, colsPerSignature + 1).value = 'مدير المنطقة';
    worksheet.getCell(signatureRow, (colsPerSignature * 2) + 1).value = 'الشئون الإدارية';

    // تنسيق التوقيعات
    [1, colsPerSignature + 1, (colsPerSignature * 2) + 1].forEach(col => {
      const cell = worksheet.getCell(signatureRow, col);
      cell.font = { bold: true };
      cell.alignment = { horizontal: 'center' };
      
      // خط للتوقيع
      const signatureLine = worksheet.getCell(signatureRow + 2, col);
      signatureLine.value = '................................';
      signatureLine.alignment = { horizontal: 'center' };
    });

    // تعديل عرض الأعمدة
    worksheet.getColumn(1).width = 5;  // م
    worksheet.getColumn(2).width = 25; // الاسم
    worksheet.getColumn(3).width = 10; // الكود

    // أعمدة الأيام
    for (let i = 4; i <= 3 + days.length; i++) {
      worksheet.getColumn(i).width = 4;
    }

    // أعمدة الملخص
    for (let i = 4 + days.length; i < 4 + days.length + attendanceCodes.length; i++) {
      worksheet.getColumn(i).width = 4;
    }

    // إنشاء Buffer للملف
    const buffer = await workbook.xlsx.writeBuffer();

    // إرجاع الملف
    return new NextResponse(buffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="كشف_التمام_الشهري_${year}_${month}.xlsx"`,
      },
    });

  } catch (error) {

    return NextResponse.json(
      { error: 'خطأ في تصدير كشف التمام', details: error.message },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { message: 'استخدم POST لتصدير كشف التمام الشهري' },
    { status: 405 }
  );
}
