{"CodeGPT.apiKey": "CodeGPT Plus Beta", "zencoder.enableRepoIndexing": true, "dataworkspace.excludedProjects": ["oje/oje.sqlproj"], "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.quickSuggestions": {"strings": true}, "files.exclude": {"node_modules": true, ".next": true, "archiv": true, "backups": true, "exports": true, "Augment-free": true, "oje": true, "ojesta": true, "**/*.log": true, "**/*.tmp": true, "**/*.temp": true, "**/*.bak": true}, "search.exclude": {"node_modules": true, ".next": true, "archiv": true, "backups": true, "exports": true, "**/*.log": true}, "css.validate": false, "less.validate": false, "scss.validate": false, "tailwindCSS.includeLanguages": {"javascript": "javascript", "html": "HTML", "typescript": "typescript", "typescriptreact": "typescriptreact"}, "files.associations": {"*.css": "tailwindcss", "*.js": "javascriptreact", "*.jsx": "javascriptreact"}, "tailwindCSS.emmetCompletions": true, "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cn\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["clsx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"], ["classnames\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "tailwindCSS.validate": true, "editor.tabSize": 2, "editor.insertSpaces": true, "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "typescript.preferences.importModuleSpecifier": "relative", "javascript.preferences.importModuleSpecifier": "relative"}