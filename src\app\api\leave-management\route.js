import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function POST(request) {
  try {
    const body = await request.json();
    const { action } = body;

    const pool = await getConnection();

    switch (action) {
      case 'create':
        return await createLeaveRequest(pool, body);
      case 'list':
        return await getLeaveRequests(pool, body);
      case 'getEmployee':
        return await getEmployeeData(pool, body);
      case 'getBalance':
        return await getLeaveBalance(pool, body);
      case 'updateStatus':
        return await updateLeaveStatus(pool, body);
      case 'getLastLeaveDate':
        return await getLastLeaveDate(pool, body);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  }
}

// إنشاء الجداول المطلوبة
async function createTables(pool) {
  try {
    // جدول رصيد الإجازات المحسن
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LeaveBalances' AND xtype='U')
      BEGIN
        CREATE TABLE LeaveBalances (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          EmployeeCode NVARCHAR(20) NOT NULL UNIQUE,
          EmployeeName NVARCHAR(100) NOT NULL,
          JobTitle NVARCHAR(100) NOT NULL,
          Department NVARCHAR(100),
          RegularBalance INT DEFAULT 15,
          CasualBalance INT DEFAULT 6,
          UsedRegular INT DEFAULT 0,
          UsedCasual INT DEFAULT 0,
          RemainingRegular AS (RegularBalance - UsedRegular),
          RemainingCasual AS (CasualBalance - UsedCasual),
          Year INT DEFAULT YEAR(GETDATE()),
          LastLeaveDate DATE,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE()
        )

        CREATE INDEX IX_LeaveBalances_EmployeeCode ON LeaveBalances(EmployeeCode)
        CREATE INDEX IX_LeaveBalances_Year ON LeaveBalances(Year)
      END
    `);

    // استخدام جدول PaperRequests الموحد بدلاً من إنشاء جدول منفصل

          ProjectManagerApprovalDate DATE,
          HRManagerApprovalDate DATE,
          Notes NVARCHAR(MAX),
          HRNotes NVARCHAR(MAX),
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE()
        )

        CREATE INDEX IX_LeaveRequests_EmployeeCode ON LeaveRequests(EmployeeCode)
        CREATE INDEX IX_LeaveRequests_Status ON LeaveRequests(Status)
        CREATE INDEX IX_LeaveRequests_LeaveType ON LeaveRequests(LeaveType)
        CREATE INDEX IX_LeaveRequests_Dates ON LeaveRequests(StartDate, EndDate)
      END
    `);

    // إدراج بيانات رصيد الإجازات للموظفين الموجودين
    await pool.request().query(`
      INSERT INTO LeaveBalances (EmployeeCode, EmployeeName, JobTitle, Department)
      SELECT DISTINCT
        e.EmployeeCode,
        CONCAT(e.FirstName, ' ', e.LastName),
        e.JobTitle,
        e.Department
      FROM Employees e
      WHERE e.EmployeeCode IS NOT NULL
        AND e.EmployeeCode != ''
        AND NOT EXISTS (
          SELECT 1 FROM LeaveBalances lb
          WHERE lb.EmployeeCode = e.EmployeeCode
        )
    `);

  } catch (error) {

  }
}

// جلب بيانات الموظف
async function getEmployeeData(pool, data) {
  try {
    await createTables(pool);
    
    const { employeeCode } = data;

    const result = await pool.request()
      .input('employeeCode', sql.VarChar, employeeCode)
      .query(`
        SELECT
          EmployeeCode,
          CONCAT(FirstName, ' ', LastName) as EmployeeName,
          JobTitle,
          Department,
          Project
        FROM Employees
        WHERE EmployeeCode = @employeeCode
      `);

    if (result.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'كود الموظف غير موجود'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: result.recordset[0]
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب بيانات الموظف'
    }, { status: 500 });
  }
}

// إنشاء طلب إجازة جديد
async function createLeaveRequest(pool, data) {
  try {
    await createTables(pool);
    
    const {
      employeeCode,
      employeeName,
      jobTitle,
      department,
      project,
      leaveType,
      startDate,
      endDate,
      daysCount,
      reason
    } = data;

    // التحقق من وجود رصيد الإجازات
    let balanceResult = await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .query(`
        SELECT
          lb.*,
          COALESCE(used_regular.UsedDays, 0) as UsedRegular,
          COALESCE(used_casual.UsedDays, 0) as UsedCasual,
          (lb.RegularBalance - COALESCE(used_regular.UsedDays, 0)) as RemainingRegular,
          (lb.CasualBalance - COALESCE(used_casual.UsedDays, 0)) as RemainingCasual
        FROM LeaveBalances lb
        LEFT JOIN (
          SELECT EmployeeCode, SUM(DaysCount) as UsedDays
          FROM LeaveRequests
          WHERE LeaveType = N'اعتيادية' AND Status = N'معتمد'
          AND YEAR(StartDate) = YEAR(GETDATE())
          GROUP BY EmployeeCode
        ) used_regular ON lb.EmployeeCode = used_regular.EmployeeCode
        LEFT JOIN (
          SELECT EmployeeCode, SUM(DaysCount) as UsedDays
          FROM LeaveRequests
          WHERE LeaveType = N'عارضة' AND Status = N'معتمد'
          AND YEAR(StartDate) = YEAR(GETDATE())
          GROUP BY EmployeeCode
        ) used_casual ON lb.EmployeeCode = used_casual.EmployeeCode
        WHERE lb.EmployeeCode = @employeeCode AND lb.Year = YEAR(GETDATE())
      `);

    if (balanceResult.recordset.length === 0) {
      // إنشاء رصيد جديد
      await pool.request()
        .input('employeeCode', sql.NVarChar, employeeCode)
        .input('employeeName', sql.NVarChar, employeeName)
        .input('jobTitle', sql.NVarChar, jobTitle)
        .input('department', sql.NVarChar, department)
        .query(`
          INSERT INTO LeaveBalances (EmployeeCode, EmployeeName, JobTitle, Department)
          VALUES (@employeeCode, @employeeName, @jobTitle, @department)
        `);

      // إعادة جلب الرصيد الجديد
      balanceResult = await pool.request()
        .input('employeeCode', sql.NVarChar, employeeCode)
        .query(`
          SELECT *, 15 as RemainingRegular, 6 as RemainingCasual
          FROM LeaveBalances
          WHERE EmployeeCode = @employeeCode AND Year = YEAR(GETDATE())
        `);
    }

    // التحقق من كفاية الرصيد للإجازات الاعتيادية والعارضة فقط
    // إجازة البدل والمرضية والأمومة وبدون راتب لا تحتاج رصيد
    const balance = balanceResult.recordset[0];
    if (leaveType === 'اعتيادية' && balance.RemainingRegular < daysCount) {
      return NextResponse.json({
        success: false,
        error: `الرصيد المتاح للإجازة الاعتيادية غير كافي. المتاح: ${balance.RemainingRegular} يوم، المطلوب: ${daysCount} يوم`
      }, { status: 400 });
    }

    if (leaveType === 'عارضة' && balance.RemainingCasual < daysCount) {
      return NextResponse.json({
        success: false,
        error: `الرصيد المتاح للإجازة العارضة غير كافي. المتاح: ${balance.RemainingCasual} يوم، المطلوب: ${daysCount} يوم`
      }, { status: 400 });
    }

    // إدراج طلب الإجازة
    const result = await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .input('employeeName', sql.NVarChar, employeeName)
      .input('jobTitle', sql.NVarChar, jobTitle)
      .input('department', sql.NVarChar, department)
      .input('project', sql.NVarChar, project)
      .input('leaveType', sql.NVarChar, leaveType)
      .input('startDate', sql.Date, startDate)
      .input('endDate', sql.Date, endDate)
      .input('daysCount', sql.Int, daysCount)
      .input('reason', sql.NVarChar, reason)
      .query(`
        INSERT INTO LeaveRequests 
        (EmployeeCode, EmployeeName, JobTitle, Department, Project, LeaveType, StartDate, EndDate, DaysCount, Reason)
        VALUES (@employeeCode, @employeeName, @jobTitle, @department, @project, @leaveType, @startDate, @endDate, @daysCount, @reason);
        SELECT SCOPE_IDENTITY() as ID;
      `);

    return NextResponse.json({
      success: true,
      message: 'تم إرسال طلب الإجازة بنجاح',
      data: { id: result.recordset[0].ID }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في إنشاء طلب الإجازة'
    }, { status: 500 });
  }
}

// جلب طلبات الإجازات
async function getLeaveRequests(pool, data) {
  try {
    await createTables(pool);
    
    const { employeeCode, status } = data;
    
    let query = `
      SELECT 
        lr.*,
        lb.RegularBalance,
        lb.CasualBalance,
        (lb.RegularBalance + lb.CasualBalance) as TotalBalance
      FROM LeaveRequests lr
      LEFT JOIN LeaveBalances lb ON lr.EmployeeCode = lb.EmployeeCode
      WHERE 1=1
    `;

    const request = pool.request();

    if (employeeCode) {
      query += ' AND lr.EmployeeCode = @employeeCode';
      request.input('employeeCode', sql.NVarChar, employeeCode);
    }

    if (status) {
      query += ' AND lr.Status = @status';
      request.input('status', sql.NVarChar, status);
    }

    query += ' ORDER BY lr.CreatedAt DESC';

    const result = await request.query(query);

    return NextResponse.json({
      success: true,
      data: result.recordset
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب طلبات الإجازات'
    }, { status: 500 });
  }
}

// جلب رصيد الإجازات
async function getLeaveBalance(pool, data) {
  try {
    await createTables(pool);
    
    const { employeeCode } = data;
    
    const result = await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .query(`
        SELECT 
          lb.*,
          COALESCE(used_regular.UsedDays, 0) as UsedRegular,
          COALESCE(used_casual.UsedDays, 0) as UsedCasual,
          (lb.RegularBalance - COALESCE(used_regular.UsedDays, 0)) as RemainingRegular,
          (lb.CasualBalance - COALESCE(used_casual.UsedDays, 0)) as RemainingCasual
        FROM LeaveBalances lb
        LEFT JOIN (
          SELECT EmployeeCode, SUM(DaysCount) as UsedDays
          FROM LeaveRequests 
          WHERE LeaveType = N'اعتيادية' AND Status = N'معتمد' 
          AND YEAR(StartDate) = YEAR(GETDATE())
          GROUP BY EmployeeCode
        ) used_regular ON lb.EmployeeCode = used_regular.EmployeeCode
        LEFT JOIN (
          SELECT EmployeeCode, SUM(DaysCount) as UsedDays
          FROM LeaveRequests 
          WHERE LeaveType = N'عارضة' AND Status = N'معتمد' 
          AND YEAR(StartDate) = YEAR(GETDATE())
          GROUP BY EmployeeCode
        ) used_casual ON lb.EmployeeCode = used_casual.EmployeeCode
        WHERE lb.EmployeeCode = @employeeCode AND lb.Year = YEAR(GETDATE())
      `);

    return NextResponse.json({
      success: true,
      data: result.recordset[0] || null
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب رصيد الإجازات'
    }, { status: 500 });
  }
}

// تحديث حالة طلب الإجازة
async function updateLeaveStatus(pool, data) {
  try {
    const { id, status, notes } = data;
    
    await pool.request()
      .input('id', sql.Int, id)
      .input('status', sql.NVarChar, status)
      .input('notes', sql.NVarChar, notes || '')
      .query(`
        UPDATE LeaveRequests 
        SET Status = @status, Notes = @notes, UpdatedAt = GETDATE()
        WHERE ID = @id
      `);

    return NextResponse.json({
      success: true,
      message: 'تم تحديث حالة الطلب بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث حالة الطلب'
    }, { status: 500 });
  }
}

// جلب تاريخ آخر إجازة للموظف
async function getLastLeaveDate(pool, data) {
  try {
    await createTables(pool);

    const { employeeCode } = data;

    const result = await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .query(`
        SELECT TOP 1
          EndDate as LastLeaveDate,
          LeaveType,
          DaysCount,
          FORMAT(EndDate, 'dd/MM/yyyy') as FormattedDate
        FROM LeaveRequests
        WHERE EmployeeCode = @employeeCode
          AND Status = N'معتمد'
        ORDER BY EndDate DESC
      `);

    return NextResponse.json({
      success: true,
      data: result.recordset[0] || {
        LastLeaveDate: null,
        FormattedDate: 'لا توجد إجازات سابقة',
        LeaveType: null,
        DaysCount: 0
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب تاريخ آخر إجازة'
    }, { status: 500 });
  }
}
