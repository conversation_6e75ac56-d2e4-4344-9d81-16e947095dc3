import { NextResponse } from 'next/server';
import { getConnection } from '@/utils/db';

export async function GET() {
  let pool = null;

  try {

    pool = await getConnection();

    // جلب جميع البيانات من جدول ARCHIV
    const result = await pool.request().query(`
      SELECT Item, Path FROM ARCHIV ORDER BY Item
    `);

    // طباعة المحتويات في الكونسول
    result.recordset.forEach((row, index) => {

    });

    return NextResponse.json({
      success: true,
      message: 'تم جلب محتويات جدول ARCHIV بنجاح',
      data: result.recordset,
      count: result.recordset.length
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'حدث خطأ في جلب محتويات جدول ARCHIV',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}
