import React from 'react';

const MonthlyAttendance = () => {
  // Placeholder data
  const attendanceData = [
    {
      employeeCode: '123',
      name: '<PERSON>',
      jobTitle: 'Software Engineer',
      days: Array(30).fill('P'), // Present
      leaves: 2,
      missions: 1,
    },
    {
      employeeCode: '456',
      name: '<PERSON>',
      jobTitle: 'Data Scientist',
      days: Array(30).fill('A'), // Absent
      leaves: 1,
      missions: 0,
    },
  ];

  return (
    <div>
      <h2>Monthly Attendance Sheet</h2>
      <table>
        <thead>
          <tr>
            <th>Employee Code</th>
            <th>Name</th>
            <th>Job Title</th>
            {Array.from({ length: 30 }, (_, i) => (
              <th key={i}>{i + 1}</th>
            ))}
            <th>Leaves</th>
            <th>Missions</th>
          </tr>
        </thead>
        <tbody>
          {attendanceData.map((employee) => (
            <tr key={employee.employeeCode}>
              <td>{employee.employeeCode}</td>
              <td>{employee.name}</td>
              <td>{employee.jobTitle}</td>
              {employee.days.map((day, i) => (
                <td key={i}>{day}</td>
              ))}
              <td>{employee.leaves}</td>
              <td>{employee.missions}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default MonthlyAttendance;