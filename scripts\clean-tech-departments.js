const sql = require('mssql');

const dbConfig = {
  user: 'SA',
  password: 'admin@123',
  server: 'localhost\\DBOJESTA',
  database: 'EMP',
  options: {
    encrypt: false,
    trustServerCertificate: true,
    enableArithAbort: true,
    instanceName: 'DBOJESTA'
  }
};

async function cleanTechDepartments() {
  try {
    const pool = await sql.connect(dbConfig);
    
    // حذف جميع الأقسام الفرعية للمكتب الفني
    await pool.request().query(`
      DELETE FROM EmployeeUnits 
      WHERE UnitID IN (
        SELECT ID FROM OrganizationalUnits 
        WHERE ParentUnitID = (
          SELECT ID FROM OrganizationalUnits 
          WHERE UnitName = N'المكتب الفني'
        )
      )
    `);
    
    await pool.request().query(`
      DELETE FROM OrganizationalUnits 
      WHERE ParentUnitID = (
        SELECT ID FROM OrganizationalUnits 
        WHERE UnitName = N'المكتب الفني'
      )
    `);
    
    console.log('✅ تم حذف جميع الأقسام الفرعية للمكتب الفني');
    
    await pool.close();
  } catch (error) {
    console.error('❌ خطأ:', error.message);
  }
}

cleanTechDepartments();
