const { getConnection, sql } = require('../src/utils/db');

async function addCommissionField() {
  try {

    const pool = await getConnection();

    // 1. التحقق من وجود جدول الشقق
    const tableCheck = await pool.request().query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_NAME = 'Apartments'
    `);

    if (tableCheck.recordset.length === 0) {

      return;
    }

    // 2. التحقق من وجود حقل العمولة
    const columnCheck = await pool.request().query(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'Apartments' AND COLUMN_NAME = 'CommissionAmount'
    `);

    if (columnCheck.recordset.length > 0) {

    } else {
      // 3. إضافة حقل العمولة

      await pool.request().query(`
        ALTER TABLE Apartments 
        ADD CommissionAmount DECIMAL(10,2) DEFAULT 0
      `);

      // 4. تحديث البيانات الموجودة بقيم تجريبية للعمولة

      await pool.request().query(`
        UPDATE Apartments 
        SET CommissionAmount = RentAmount * 0.05
        WHERE CommissionAmount = 0 OR CommissionAmount IS NULL
      `);
      console.log('✅ تم تحديث قيم العمولة (5% من قيمة الإيجار)');
    }

    // 5. عرض الإحصائيات المحدثة

    const stats = await pool.request().query(`
      SELECT 
        COUNT(*) as TotalApartments,
        SUM(RentAmount) as TotalRent,
        SUM(InsuranceAmount) as TotalInsurance,
        SUM(CommissionAmount) as TotalCommission,
        SUM(BacklogAmount) as TotalBacklog,
        AVG(CommissionAmount) as AvgCommission
      FROM Apartments 
      WHERE IsActive = 1
    `);

    const data = stats.recordset[0];

    console.log(`- متوسط العمولة: ${(data.AvgCommission || 0).toFixed(2)} ج.م`);

    // 6. عرض عينة من البيانات

    const sampleData = await pool.request().query(`
      SELECT TOP 3
        ApartmentCode,
        LandlordName,
        RentAmount,
        InsuranceAmount,
        CommissionAmount,
        BacklogAmount
      FROM Apartments
      WHERE IsActive = 1
      ORDER BY ID
    `);

    sampleData.recordset.forEach((apt, index) => {

    });

  } catch (error) {

    throw error;
  }
}

// تشغيل السكريبت إذا تم استدعاؤه مباشرة
if (require.main === module) {
  addCommissionField()
    .then(() => {

      process.exit(0);
    })
    .catch((error) => {

      process.exit(1);
    });
}

module.exports = { addCommissionField };
