'use client';
import React, { useState } from 'react';
import MainLayout from '@/components/MainLayout';
import { FiDatabase, FiCheck, FiX, FiRefreshCw, FiAlertTriangle } from 'react-icons/fi';

export default function DatabaseFixPage() {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState(null);
  const [analysis, setAnalysis] = useState(null);
  const [message, setMessage] = useState('');

  // تحليل التناقضات
  const analyzeDatabase = async () => {
    setLoading(true);
    setMessage('');
    
    try {
      const response = await fetch('/api/database-consistency-fix', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'analyze' })
      });

      const result = await response.json();
      
      if (result.success) {
        setAnalysis(result.analysis);
        setMessage('تم تحليل قاعدة البيانات بنجاح');
      } else {
        setMessage('خطأ في التحليل: ' + result.error);
      }
    } catch (error) {
      setMessage('خطأ في الاتصال: ' + error.message);
    }
    
    setLoading(false);
  };

  // إصلاح هياكل الجداول
  const fixTableStructures = async () => {
    setLoading(true);
    setMessage('');
    
    try {
      const response = await fetch('/api/database-consistency-fix', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'fix-tables' })
      });

      const result = await response.json();
      
      if (result.success) {
        setResults(result.results);
        setMessage('تم إصلاح هياكل الجداول بنجاح');
      } else {
        setMessage('خطأ في الإصلاح: ' + result.error);
      }
    } catch (error) {
      setMessage('خطأ في الاتصال: ' + error.message);
    }
    
    setLoading(false);
  };

  // ترحيل البيانات
  const migrateData = async () => {
    setLoading(true);
    setMessage('');
    
    try {
      const response = await fetch('/api/database-consistency-fix', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'migrate-data' })
      });

      const result = await response.json();
      
      if (result.success) {
        setResults(result.results);
        setMessage('تم ترحيل البيانات بنجاح');
      } else {
        setMessage('خطأ في الترحيل: ' + result.error);
      }
    } catch (error) {
      setMessage('خطأ في الاتصال: ' + error.message);
    }
    
    setLoading(false);
  };

  // الإصلاح الكامل
  const performFullFix = async () => {
    if (!confirm('هل أنت متأكد من تنفيذ الإصلاح الكامل؟ هذا سيؤثر على قاعدة البيانات.')) {
      return;
    }

    setLoading(true);
    setMessage('');
    
    try {
      const response = await fetch('/api/database-consistency-fix', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'full-fix' })
      });

      const result = await response.json();
      
      if (result.success) {
        setResults(result.results);
        setMessage('تم تنفيذ الإصلاح الكامل بنجاح');
      } else {
        setMessage('خطأ في الإصلاح الكامل: ' + result.error);
      }
    } catch (error) {
      setMessage('خطأ في الاتصال: ' + error.message);
    }
    
    setLoading(false);
  };

  return (
    <MainLayout>
      <div className="p-6 max-w-7xl mx-auto">
        {/* العنوان */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <FiDatabase className="text-3xl text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                إصلاح تناقضات قاعدة البيانات
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                تحليل وإصلاح التناقضات في أسماء الأعمدة والجداول
              </p>
            </div>
          </div>
        </div>

        {/* الرسائل */}
        {message && (
          <div className={`mb-6 p-4 rounded-lg ${
            message.includes('خطأ') 
              ? 'bg-red-100 text-red-800 border border-red-200' 
              : 'bg-green-100 text-green-800 border border-green-200'
          }`}>
            <div className="flex items-center gap-2">
              {message.includes('خطأ') ? <FiX /> : <FiCheck />}
              {message}
            </div>
          </div>
        )}

        {/* أزرار الإجراءات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          <button
            onClick={analyzeDatabase}
            disabled={loading}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg flex items-center justify-center gap-2 transition-colors disabled:opacity-50"
          >
            {loading ? <FiRefreshCw className="animate-spin" /> : <FiAlertTriangle />}
            تحليل التناقضات
          </button>

          <button
            onClick={fixTableStructures}
            disabled={loading}
            className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg flex items-center justify-center gap-2 transition-colors disabled:opacity-50"
          >
            {loading ? <FiRefreshCw className="animate-spin" /> : <FiDatabase />}
            إصلاح الجداول
          </button>

          <button
            onClick={migrateData}
            disabled={loading}
            className="bg-yellow-600 hover:bg-yellow-700 text-white px-6 py-3 rounded-lg flex items-center justify-center gap-2 transition-colors disabled:opacity-50"
          >
            {loading ? <FiRefreshCw className="animate-spin" /> : <FiRefreshCw />}
            ترحيل البيانات
          </button>

          <button
            onClick={performFullFix}
            disabled={loading}
            className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg flex items-center justify-center gap-2 transition-colors disabled:opacity-50"
          >
            {loading ? <FiRefreshCw className="animate-spin" /> : <FiCheck />}
            الإصلاح الكامل
          </button>
        </div>

        {/* نتائج التحليل */}
        {analysis && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border p-6 mb-6">
            <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200 mb-4">
              نتائج التحليل
            </h2>
            
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* جداول الموظفين */}
              <div>
                <h3 className="font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  جداول الموظفين
                </h3>
                <div className="bg-gray-50 dark:bg-gray-700 rounded p-3">
                  <pre className="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap">
                    {JSON.stringify(analysis.employeeTables, null, 2)}
                  </pre>
                </div>
              </div>

              {/* جداول الإجازات */}
              <div>
                <h3 className="font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  جداول الإجازات
                </h3>
                <div className="bg-gray-50 dark:bg-gray-700 rounded p-3">
                  <pre className="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap">
                    {JSON.stringify(analysis.leaveTables, null, 2)}
                  </pre>
                </div>
              </div>

              {/* جداول الشقق */}
              <div>
                <h3 className="font-semibold text-gray-700 dark:text-gray-300 mb-2">
                  جداول الشقق
                </h3>
                <div className="bg-gray-50 dark:bg-gray-700 rounded p-3">
                  <pre className="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap">
                    {JSON.stringify(analysis.apartmentTables, null, 2)}
                  </pre>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* نتائج الإصلاح */}
        {results && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border p-6">
            <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200 mb-4">
              نتائج الإصلاح
            </h2>
            
            <div className="space-y-3">
              {results.map((result, index) => (
                <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700 rounded">
                  <FiCheck className="text-green-600" />
                  <div>
                    <span className="font-medium text-gray-800 dark:text-gray-200">
                      {result.step || result.table}
                    </span>
                    <span className="text-gray-600 dark:text-gray-400 ml-2">
                      - {result.status}
                    </span>
                    {result.rowsAffected !== undefined && (
                      <span className="text-blue-600 ml-2">
                        ({result.rowsAffected} صف متأثر)
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
