import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');

    if (!query || query.length < 2) {
      return NextResponse.json([]);
    }

    const pool = await getConnection();

    // البحث في قاعدة البيانات الحقيقية
    const result = await pool.request()
      .input('searchTerm', sql.NVarChar, `%${query}%`)
      .query(`
        SELECT TOP 10
          EmployeeCode as employeeId,
          EmployeeName as fullName,
          Department as department,
          JobTitle as jobTitle,
          NationalID as nationalId,
          Mobile as phone,
          email,
          CurrentStatus as status
        FROM Employees
        WHERE EmployeeName LIKE @searchTerm
           OR CAST(EmployeeCode AS NVARCHAR) LIKE @searchTerm
           OR Department LIKE @searchTerm
           OR JobTitle LIKE @searchTerm
        ORDER BY EmployeeName
      `);

    return NextResponse.json(result.recordset);
  } catch (error) {

    return NextResponse.json(
      { error: 'خطأ في البحث عن الموظفين' },
      { status: 500 }
    );
  }
}
