'use client';

import React from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { Calendar } from 'lucide-react';

const SimpleDateInput = ({
  id,
  name,
  value,
  onChange,
  required = false,
  className = ''
}) => {
  const { isDarkMode } = useTheme();

  // معالجة تغيير التاريخ
  const handleDateChange = (e) => {
    const inputValue = e.target.value;

    onChange({
      target: {
        name: name,
        value: inputValue
      }
    });
  };

  return (
    <div className="relative">
      <input
        id={id}
        name={name}
        type="text"
        value={value || ''}
        onChange={handleDateChange}
        required={required}
        placeholder="dd/mm/yyyy"
        pattern="\d{2}/\d{2}/\d{4}"
        className={`
          w-full px-4 py-3 pr-12 border-2 rounded-lg
          text-lg font-medium text-center
          transition-all duration-200
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50
          ${isDarkMode
            ? 'bg-gray-700 border-gray-600 text-white focus:border-blue-400'
            : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
          }
          ${className}
        `}
        style={{
          direction: 'ltr'
        }}
      />

      <Calendar
        className={`absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 pointer-events-none ${
          isDarkMode ? 'text-gray-400' : 'text-gray-500'
        }`}
      />
    </div>
  );
};

export default SimpleDateInput;
