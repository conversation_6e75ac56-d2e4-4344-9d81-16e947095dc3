'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { FiSave, FiArrowLeft, FiUser, FiMail, FiPhone, FiMapPin } from 'react-icons/fi';

export default function EditEmployee() {
  const params = useParams();
  const router = useRouter();
  const employeeId = params.id;

  const [employee, setEmployee] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // جلب بيانات الموظف
  useEffect(() => {
    if (employeeId) {
      fetchEmployee();
    }
  }, [employeeId]);

  const fetchEmployee = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/employee-search?employeeId=${employeeId}&searchType=individual`);
      const data = await response.json();

      if (data.success && data.employee) {
        setEmployee(data.employee);
      } else {
        setError('لم يتم العثور على الموظف');
      }
    } catch (error) {

      setError('حدث خطأ في جلب البيانات');
    } finally {
      setLoading(false);
    }
  };

  // تحديث قيمة حقل
  const handleInputChange = (field, value) => {
    setEmployee(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // حفظ التعديلات
  const handleSave = async () => {
    try {
      setSaving(true);
      setError('');
      setSuccess('');

      const response = await fetch('/api/employee-update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          employeeId: employee.EmployeeID,
          employeeData: employee
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('تم حفظ التعديلات بنجاح!');
        setTimeout(() => {
          router.push(`/employees/search?employeeId=${employee.EmployeeID}`);
        }, 2000);
      } else {
        setError(data.message || 'حدث خطأ في حفظ البيانات');
      }
    } catch (error) {

      setError('حدث خطأ في حفظ البيانات');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل بيانات الموظف...</p>
        </div>
      </div>
    );
  }

  if (error && !employee) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold text-gray-800 mb-2">خطأ</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => router.push('/employees/search')}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
          >
            العودة للبحث
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        {/* رأس الصفحة */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => router.push('/employees/search')}
                className="text-gray-600 hover:text-gray-800"
              >
                <FiArrowLeft className="text-2xl" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-800">تعديل بيانات الموظف</h1>
                <p className="text-gray-600">
                  {employee?.EmployeeName} - كود: {employee?.EmployeeCode}
                </p>
              </div>
            </div>

            <div className="flex gap-3">
              <button
                onClick={handleSave}
                disabled={saving}
                className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center gap-2"
              >
                <FiSave />
                {saving ? 'جاري الحفظ...' : 'حفظ التعديلات'}
              </button>
            </div>
          </div>

          {/* رسائل النجاح والخطأ */}
          {success && (
            <div className="mt-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
              {success}
            </div>
          )}

          {error && (
            <div className="mt-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              {error}
            </div>
          )}
        </div>

        {/* نموذج التعديل */}
        {employee && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* العمود الأول */}
            <div className="space-y-6">
              {/* البيانات الأساسية */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-blue-600 border-b border-blue-200 pb-3 mb-4 flex items-center gap-2">
                  <FiUser />
                  البيانات الأساسية
                </h3>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">كود الموظف</label>
                    <input
                      type="text"
                      value={employee.EmployeeCode || ''}
                      disabled
                      className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">الاسم الكامل</label>
                    <input
                      type="text"
                      value={employee.EmployeeName || ''}
                      onChange={(e) => handleInputChange('EmployeeName', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">المسمى الوظيفي</label>
                    <input
                      type="text"
                      value={employee.JobTitle || ''}
                      onChange={(e) => handleInputChange('JobTitle', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">القسم</label>
                    <input
                      type="text"
                      value={employee.Department || ''}
                      onChange={(e) => handleInputChange('Department', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">المدير المباشر</label>
                    <input
                      type="text"
                      value={employee.DirectManager || ''}
                      onChange={(e) => handleInputChange('DirectManager', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
              </div>

              {/* البيانات الشخصية */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-green-600 border-b border-green-200 pb-3 mb-4 flex items-center gap-2">
                  <FiUser />
                  البيانات الشخصية
                </h3>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">الرقم القومي</label>
                    <input
                      type="text"
                      value={employee.NationalID || ''}
                      onChange={(e) => handleInputChange('NationalID', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">تاريخ الميلاد</label>
                    <input
                      type="date"
                      value={employee.BirthDate ? new Date(employee.BirthDate).toISOString().split('T')[0] : ''}
                      onChange={(e) => handleInputChange('BirthDate', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">النوع</label>
                    <select
                      value={employee.Gender || ''}
                      onChange={(e) => handleInputChange('Gender', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">اختر النوع</option>
                      <option value="M">ذكر</option>
                      <option value="F">أنثى</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">المحافظة</label>
                    <input
                      type="text"
                      value={employee.Governorate || ''}
                      onChange={(e) => handleInputChange('Governorate', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">المنطقة</label>
                    <input
                      type="text"
                      value={employee.Area || ''}
                      onChange={(e) => handleInputChange('Area', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">الحالة الاجتماعية</label>
                    <select
                      value={employee.MaritalStatus || ''}
                      onChange={(e) => handleInputChange('MaritalStatus', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">اختر الحالة</option>
                      <option value="أعزب">أعزب</option>
                      <option value="متزوج">متزوج</option>
                      <option value="مطلق">مطلق</option>
                      <option value="أرمل">أرمل</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {/* العمود الثاني */}
            <div className="space-y-6">
              {/* بيانات الاتصال */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-purple-600 border-b border-purple-200 pb-3 mb-4 flex items-center gap-2">
                  <FiPhone />
                  بيانات الاتصال
                </h3>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">رقم الجوال</label>
                    <input
                      type="tel"
                      value={employee.Mobile || ''}
                      onChange={(e) => handleInputChange('Mobile', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
                    <input
                      type="email"
                      value={employee.Email || ''}
                      onChange={(e) => handleInputChange('Email', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">رقم الطوارئ</label>
                    <input
                      type="tel"
                      value={employee.EmergencyNumber || ''}
                      onChange={(e) => handleInputChange('EmergencyNumber', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">صلة القرابة</label>
                    <input
                      type="text"
                      value={employee.Kinship || ''}
                      onChange={(e) => handleInputChange('Kinship', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
              </div>

              {/* البيانات الدراسية */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-orange-600 border-b border-orange-200 pb-3 mb-4">
                  البيانات الدراسية
                </h3>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">المؤهل التعليمي</label>
                    <input
                      type="text"
                      value={employee.Education || ''}
                      onChange={(e) => handleInputChange('Education', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">الجامعة</label>
                    <input
                      type="text"
                      value={employee.University || ''}
                      onChange={(e) => handleInputChange('University', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">التخصص</label>
                    <input
                      type="text"
                      value={employee.Major || ''}
                      onChange={(e) => handleInputChange('Major', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">التقدير</label>
                    <select
                      value={employee.Grade || ''}
                      onChange={(e) => handleInputChange('Grade', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">اختر التقدير</option>
                      <option value="ممتاز">ممتاز</option>
                      <option value="جيد جداً">جيد جداً</option>
                      <option value="جيد">جيد</option>
                      <option value="مقبول">مقبول</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">سنة التخرج</label>
                    <input
                      type="number"
                      value={employee.Batch || ''}
                      onChange={(e) => handleInputChange('Batch', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
              </div>

              {/* السكن والمواصلات */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-red-600 border-b border-red-200 pb-3 mb-4 flex items-center gap-2">
                  <FiMapPin />
                  السكن والمواصلات
                </h3>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">موظف مقيم</label>
                    <select
                      value={employee.IsResidentEmployee ? 'true' : 'false'}
                      onChange={(e) => handleInputChange('IsResidentEmployee', e.target.value === 'true')}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="false">لا</option>
                      <option value="true">نعم</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">سكن الشركة</label>
                    <input
                      type="text"
                      value={employee.CompanyHousing || ''}
                      onChange={(e) => handleInputChange('CompanyHousing', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">كود السكن</label>
                    <input
                      type="text"
                      value={employee.HousingCode || ''}
                      onChange={(e) => handleInputChange('HousingCode', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">وسيلة النقل</label>
                    <input
                      type="text"
                      value={employee.TransportMethod || ''}
                      onChange={(e) => handleInputChange('TransportMethod', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
              </div>

              {/* التأمينات */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-teal-600 border-b border-teal-200 pb-3 mb-4">
                  التأمينات
                </h3>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">التأمين الاجتماعي</label>
                    <select
                      value={employee.SocialInsurance || ''}
                      onChange={(e) => handleInputChange('SocialInsurance', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">اختر الحالة</option>
                      <option value="مؤمن">مؤمن</option>
                      <option value="غير مؤمن">غير مؤمن</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">الرقم التأميني</label>
                    <input
                      type="text"
                      value={employee.SocialInsuranceNumber || ''}
                      onChange={(e) => handleInputChange('SocialInsuranceNumber', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">تاريخ التأمين</label>
                    <input
                      type="date"
                      value={employee.SocialInsuranceDate ? new Date(employee.SocialInsuranceDate).toISOString().split('T')[0] : ''}
                      onChange={(e) => handleInputChange('SocialInsuranceDate', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">مؤمن عليه طبي</label>
                    <select
                      value={employee.MedicalInsurance || ''}
                      onChange={(e) => handleInputChange('MedicalInsurance', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">اختر الحالة</option>
                      <option value="مؤمن">مؤمن</option>
                      <option value="غير مؤمن">غير مؤمن</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">رقم الوثيقة الطبية</label>
                    <input
                      type="text"
                      value={employee.MedicalInsuranceNumber || ''}
                      onChange={(e) => handleInputChange('MedicalInsuranceNumber', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
              </div>

              {/* التواريخ المهمة */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-indigo-600 border-b border-indigo-200 pb-3 mb-4">
                  التواريخ المهمة
                </h3>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">تاريخ التعيين</label>
                    <input
                      type="date"
                      value={employee.HireDate ? new Date(employee.HireDate).toISOString().split('T')[0] : ''}
                      onChange={(e) => handleInputChange('HireDate', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">تاريخ الالتحاق</label>
                    <input
                      type="date"
                      value={employee.JoinDate ? new Date(employee.JoinDate).toISOString().split('T')[0] : ''}
                      onChange={(e) => handleInputChange('JoinDate', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
              </div>

              {/* ملاحظات */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-600 border-b border-gray-200 pb-3 mb-4">
                  ملاحظات
                </h3>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">ملاحظات إضافية</label>
                  <textarea
                    value={employee.Notes || ''}
                    onChange={(e) => handleInputChange('Notes', e.target.value)}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="أدخل أي ملاحظات إضافية..."
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
