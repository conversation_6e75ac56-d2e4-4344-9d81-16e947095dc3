import { NextResponse } from 'next/server';
import { getConnection } from '@/lib/db';

export async function GET() {
  try {

    const pool = await getConnection();

    // جلب قائمة الأقسام
    const departmentsQuery = `
      SELECT DISTINCT Department 
      FROM Employees 
      WHERE Department IS NOT NULL AND Department != ''
      ORDER BY Department
    `;

    // جلب قائمة المحافظات
    const governoratesQuery = `
      SELECT DISTINCT Governorate 
      FROM Employees 
      WHERE Governorate IS NOT NULL AND Governorate != ''
      ORDER BY Governorate
    `;

    // جلب قائمة المسميات الوظيفية
    const jobTitlesQuery = `
      SELECT DISTINCT JobTitle 
      FROM Employees 
      WHERE JobTitle IS NOT NULL AND JobTitle != ''
      ORDER BY JobTitle
    `;

    // جلب قائمة المؤهلات
    const qualificationsQuery = `
      SELECT DISTINCT Education as Qualification
      FROM Employees
      WHERE Education IS NOT NULL AND Education != ''
      ORDER BY Education
    `;

    // تنفيذ الاستعلامات
    const [departmentsResult, governoratesResult, jobTitlesResult, qualificationsResult] = await Promise.all([
      pool.request().query(departmentsQuery),
      pool.request().query(governoratesQuery),
      pool.request().query(jobTitlesQuery),
      pool.request().query(qualificationsQuery)
    ]);

    // استخراج البيانات
    const departments = departmentsResult.recordset.map(row => row.Department);
    const governorates = governoratesResult.recordset.map(row => row.Governorate);
    const jobTitles = jobTitlesResult.recordset.map(row => row.JobTitle);
    const qualifications = qualificationsResult.recordset.map(row => row.Qualification);

    return NextResponse.json({
      success: true,
      data: {
        departments,
        governorates,
        jobTitles,
        qualifications
      }
    });

  } catch (error) {

    // إرجاع قوائم افتراضية في حالة الخطأ
    return NextResponse.json({
      success: true,
      data: {
        departments: [
          'الإدارة العامة',
          'الموارد البشرية', 
          'المالية',
          'التقنية',
          'التسويق',
          'المشتريات',
          'الأمن والسلامة',
          'الصيانة'
        ],
        governorates: [
          'القاهرة',
          'الجيزة',
          'الإسكندرية',
          'الدقهلية',
          'الشرقية',
          'القليوبية',
          'كفر الشيخ',
          'الغربية',
          'المنوفية',
          'البحيرة'
        ],
        jobTitles: [
          'مدير عام',
          'مدير إدارة',
          'رئيس قسم',
          'موظف',
          'محاسب',
          'مهندس',
          'فني',
          'سائق',
          'عامل'
        ],
        qualifications: [
          'بكالوريوس',
          'ليسانس',
          'ماجستير',
          'دكتوراه',
          'دبلوم',
          'ثانوية عامة',
          'إعدادية',
          'ابتدائية'
        ]
      }
    });
  }
}

export async function POST() {
  return NextResponse.json(
    { message: 'استخدم GET لجلب القوائم' },
    { status: 405 }
  );
}
