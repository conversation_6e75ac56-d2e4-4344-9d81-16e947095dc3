'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import DateInput from '@/components/DateInput';
import {
  FiCalendar,
  FiUser,
  FiFileText,
  FiSave,
  FiRefreshCw,
  FiCheck,
  FiAlertCircle,
  FiClock
} from 'react-icons/fi';

export default function NewLeaveRequest() {
  const [formData, setFormData] = useState({
    employeeId: '',
    employeeName: '',
    department: '',
    leaveType: '',
    startDate: '',
    endDate: '',
    totalDays: 0,
    reason: '',
    emergencyContact: '',
    emergencyPhone: '',
    replacementEmployee: '',
    notes: ''
  });

  const [employees, setEmployees] = useState([]);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });

  const leaveTypes = [
    { value: 'annual', label: 'إجازة سنوية', maxDays: 30, color: 'blue' },
    { value: 'sick', label: 'إجازة مرضية', maxDays: 15, color: 'red' },
    { value: 'emergency', label: 'إجازة طارئة', maxDays: 7, color: 'orange' },
    { value: 'maternity', label: 'إجازة أمومة', maxDays: 90, color: 'pink' },
    { value: 'paternity', label: 'إجازة أبوة', maxDays: 3, color: 'green' },
    { value: 'unpaid', label: 'إجازة بدون راتب', maxDays: 365, color: 'gray' },
    { value: 'study', label: 'إجازة دراسية', maxDays: 180, color: 'purple' },
    { value: 'hajj', label: 'إجازة حج', maxDays: 21, color: 'emerald' }
  ];

  // Load employees for selection
  useEffect(() => {
    loadEmployees();
  }, []);

  const loadEmployees = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/employee-live-search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ searchTerm: '' })
      });

      const data = await response.json();
      if (data.success) {
        setEmployees(data.employees || []);
      }
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  // تحويل التاريخ من DD/MM/YYYY إلى YYYY-MM-DD للحسابات
  const convertDateForCalculation = (dateStr) => {
    if (!dateStr || dateStr === 'undefined' || dateStr === 'null') return '';
    if (typeof dateStr === 'string' && dateStr.includes('/')) {
      const parts = dateStr.split('/');
      if (parts.length === 3) {
        const [day, month, year] = parts;
        if (day && month && year) {
          return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        }
      }
    }
    return dateStr;
  };

  // معالجة تغيير التاريخ
  const handleDateChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Calculate total days when dates change
  useEffect(() => {
    if (formData.startDate && formData.endDate) {
      const start = new Date(convertDateForCalculation(formData.startDate));
      const end = new Date(convertDateForCalculation(formData.endDate));
      const diffTime = Math.abs(end - start);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;

      setFormData(prev => ({
        ...prev,
        totalDays: diffDays
      }));
    }
  }, [formData.startDate, formData.endDate]);

  const handleEmployeeSelect = (employee) => {
    setFormData(prev => ({
      ...prev,
      employeeId: employee.EmployeeID,
      employeeName: employee.Name,
      department: employee.Department || ''
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validation
    if (!formData.employeeId || !formData.leaveType || !formData.startDate || !formData.endDate) {
      setMessage({
        type: 'error',
        text: 'يرجى ملء جميع الحقول المطلوبة'
      });
      return;
    }

    if (new Date(formData.endDate) < new Date(formData.startDate)) {
      setMessage({
        type: 'error',
        text: 'تاريخ انتهاء الإجازة يجب أن يكون بعد تاريخ البداية'
      });
      return;
    }

    const selectedLeaveType = leaveTypes.find(type => type.value === formData.leaveType);
    if (formData.totalDays > selectedLeaveType.maxDays) {
      setMessage({
        type: 'error',
        text: `عدد أيام الإجازة يتجاوز الحد المسموح (${selectedLeaveType.maxDays} يوم)`
      });
      return;
    }

    setSubmitting(true);
    try {
      const response = await fetch('/api/leave-requests', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'create',
          ...formData,
          status: 'pending',
          submittedAt: new Date().toISOString()
        })
      });

      const result = await response.json();
      if (result.success) {
        setMessage({
          type: 'success',
          text: 'تم تقديم طلب الإجازة بنجاح'
        });
        
        // Reset form
        setFormData({
          employeeId: '',
          employeeName: '',
          department: '',
          leaveType: '',
          startDate: '',
          endDate: '',
          totalDays: 0,
          reason: '',
          emergencyContact: '',
          emergencyPhone: '',
          replacementEmployee: '',
          notes: ''
        });
      } else {
        setMessage({
          type: 'error',
          text: result.error || 'حدث خطأ في تقديم الطلب'
        });
      }
    } catch (error) {
      setMessage({
        type: 'error',
        text: 'حدث خطأ في الاتصال بالخادم'
      });
    } finally {
      setSubmitting(false);
    }
  };

  const selectedLeaveType = leaveTypes.find(type => type.value === formData.leaveType);

  return (
    <MainLayout>
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center gap-3">
            <FiCalendar className="text-3xl text-green-600" />
            <div>
              <h1 className="text-3xl font-bold text-gray-800">طلب إجازة جديد</h1>
              <p className="text-gray-600">تقديم طلب إجازة للموظفين</p>
            </div>
          </div>
        </div>

        {/* Message */}
        {message.text && (
          <div className={`mb-6 p-4 rounded-lg flex items-center gap-3 ${
            message.type === 'success' 
              ? 'bg-green-50 text-green-800 border border-green-200' 
              : 'bg-red-50 text-red-800 border border-red-200'
          }`}>
            {message.type === 'success' ? <FiCheck /> : <FiAlertCircle />}
            <span>{message.text}</span>
          </div>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-sm p-6 space-y-6">
          {/* Employee Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FiUser className="inline ml-1" />
                الموظف *
              </label>
              <select
                value={formData.employeeId}
                onChange={(e) => {
                  const employee = employees.find(emp => emp.EmployeeID === e.target.value);
                  if (employee) handleEmployeeSelect(employee);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                required
              >
                <option value="">اختر الموظف</option>
                {employees.map((employee) => (
                  <option key={employee.EmployeeID} value={employee.EmployeeID}>
                    {employee.Name} - {employee.EmployeeID}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">القسم</label>
              <input
                type="text"
                value={formData.department}
                readOnly
                className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
                placeholder="سيتم ملؤه تلقائياً"
              />
            </div>
          </div>

          {/* Leave Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              نوع الإجازة *
            </label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {leaveTypes.map((type) => (
                <label
                  key={type.value}
                  className={`relative flex items-center p-3 rounded-lg border-2 cursor-pointer transition-all ${
                    formData.leaveType === type.value
                      ? `border-${type.color}-500 bg-${type.color}-50`
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <input
                    type="radio"
                    name="leaveType"
                    value={type.value}
                    checked={formData.leaveType === type.value}
                    onChange={(e) => setFormData(prev => ({ ...prev, leaveType: e.target.value }))}
                    className="sr-only"
                  />
                  <div className="text-center w-full">
                    <div className="font-medium text-sm">{type.label}</div>
                    <div className="text-xs text-gray-500 mt-1">حتى {type.maxDays} يوم</div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Dates */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <DateInput
                name="startDate"
                value={formData.startDate}
                onChange={handleDateChange}
                label="تاريخ البداية *"
                placeholder="DD/MM/YYYY"
                isArabic={true}
                required={true}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
              />
            </div>

            <div>
              <DateInput
                name="endDate"
                value={formData.endDate}
                onChange={handleDateChange}
                label="تاريخ النهاية *"
                placeholder="DD/MM/YYYY"
                isArabic={true}
                required={true}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FiClock className="inline ml-1" />
                إجمالي الأيام
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="number"
                  value={formData.totalDays}
                  readOnly
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
                />
                {selectedLeaveType && formData.totalDays > selectedLeaveType.maxDays && (
                  <FiAlertCircle className="text-red-500" title="يتجاوز الحد المسموح" />
                )}
              </div>
            </div>
          </div>

          {/* Reason */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <FiFileText className="inline ml-1" />
              سبب الإجازة *
            </label>
            <textarea
              value={formData.reason}
              onChange={(e) => setFormData(prev => ({ ...prev, reason: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
              placeholder="اذكر سبب طلب الإجازة..."
              required
            />
          </div>

          {/* Emergency Contact */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                جهة الاتصال في الطوارئ
              </label>
              <input
                type="text"
                value={formData.emergencyContact}
                onChange={(e) => setFormData(prev => ({ ...prev, emergencyContact: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                placeholder="اسم الشخص المسؤول"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                رقم الهاتف للطوارئ
              </label>
              <input
                type="tel"
                value={formData.emergencyPhone}
                onChange={(e) => setFormData(prev => ({ ...prev, emergencyPhone: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
                placeholder="رقم الهاتف"
              />
            </div>
          </div>

          {/* Replacement Employee */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الموظف البديل (اختياري)
            </label>
            <select
              value={formData.replacementEmployee}
              onChange={(e) => setFormData(prev => ({ ...prev, replacementEmployee: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
            >
              <option value="">اختر الموظف البديل</option>
              {employees.filter(emp => emp.EmployeeID !== formData.employeeId).map((employee) => (
                <option key={employee.EmployeeID} value={employee.EmployeeID}>
                  {employee.Name} - {employee.EmployeeID}
                </option>
              ))}
            </select>
          </div>

          {/* Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              ملاحظات إضافية
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
              rows={2}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500"
              placeholder="أي ملاحظات إضافية..."
            />
          </div>

          {/* Submit Button */}
          <div className="flex justify-end gap-4 pt-6 border-t">
            <button
              type="button"
              onClick={() => window.history.back()}
              className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              إلغاء
            </button>
            <button
              type="submit"
              disabled={submitting}
              className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2 disabled:opacity-50"
            >
              {submitting ? (
                <>
                  <FiRefreshCw className="animate-spin" />
                  جاري التقديم...
                </>
              ) : (
                <>
                  <FiSave />
                  تقديم الطلب
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </MainLayout>
  );
}
