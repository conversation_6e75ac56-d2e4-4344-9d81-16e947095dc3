import { NextResponse } from 'next/server';
import { getConnection } from '@/lib/db';
import { readFile } from 'fs/promises';
import path from 'path';

export async function POST() {
  try {

    const pool = await getConnection();

    // قراءة ملف SQL
    const sqlFilePath = path.join(process.cwd(), 'database', 'direct_managers_system.sql');
    const sqlContent = await readFile(sqlFilePath, 'utf8');

    // تقسيم الملف إلى استعلامات منفصلة
    const sqlStatements = sqlContent
      .split('GO')
      .map(statement => statement.trim())
      .filter(statement => statement.length > 0 && !statement.startsWith('--') && !statement.startsWith('PRINT'));

    let successCount = 0;
    let errorCount = 0;
    const errors = [];

    // تنفيذ كل استعلام
    for (let i = 0; i < sqlStatements.length; i++) {
      try {
        const statement = sqlStatements[i];
        
        // تجاهل التعليقات والأسطر الفارغة
        if (statement.startsWith('--') || statement.trim() === '') {
          continue;
        }

        await pool.request().query(statement);
        successCount++;
        
      } catch (error) {

        errors.push(`الاستعلام ${i + 1}: ${error.message}`);
        errorCount++;
      }
    }

    // التحقق من إنشاء الجداول
    const tablesCheckQuery = `
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_NAME IN ('DirectManagers', 'DirectManagersHistory', 'DirectManagersFileUploads')
    `;

    const tablesResult = await pool.request().query(tablesCheckQuery);
    const createdTables = tablesResult.recordset.map(row => row.TABLE_NAME);

    // التحقق من إنشاء الإجراءات المخزنة
    const proceduresCheckQuery = `
      SELECT ROUTINE_NAME 
      FROM INFORMATION_SCHEMA.ROUTINES 
      WHERE ROUTINE_TYPE = 'PROCEDURE' 
      AND ROUTINE_NAME IN ('sp_UpdateDirectManager', 'sp_GetOrganizationalHierarchy')
    `;

    const proceduresResult = await pool.request().query(proceduresCheckQuery);
    const createdProcedures = proceduresResult.recordset.map(row => row.ROUTINE_NAME);

    // إضافة أعمدة المديرين المباشرين إلى جدول الموظفين إذا لم تكن موجودة
    try {
      await pool.request().query(`
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Employees' AND COLUMN_NAME = 'DirectManager1')
        BEGIN
          ALTER TABLE Employees ADD DirectManager1 NVARCHAR(20)
        END
        
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Employees' AND COLUMN_NAME = 'DirectManager2')
        BEGIN
          ALTER TABLE Employees ADD DirectManager2 NVARCHAR(20)
        END
        
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Employees' AND COLUMN_NAME = 'DirectManager3')
        BEGIN
          ALTER TABLE Employees ADD DirectManager3 NVARCHAR(20)
        END
        
        IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Employees' AND COLUMN_NAME = 'DirectManager4')
        BEGIN
          ALTER TABLE Employees ADD DirectManager4 NVARCHAR(20)
        END
      `);

    } catch (error) {

      errors.push(`إضافة الأعمدة: ${error.message}`);
    }

    // إنشاء مجلد الرفع
    try {
      const { mkdir } = await import('fs/promises');
      const uploadDir = path.join(process.cwd(), 'uploads', 'direct-managers');
      await mkdir(uploadDir, { recursive: true });

    } catch (error) {

      errors.push(`إنشاء المجلد: ${error.message}`);
    }

    // إحصائيات النتائج
    const setupStats = {
      totalStatements: sqlStatements.length,
      successfulStatements: successCount,
      failedStatements: errorCount,
      createdTables: createdTables.length,
      createdProcedures: createdProcedures.length,
      tablesCreated: createdTables,
      proceduresCreated: createdProcedures
    };

    return NextResponse.json({
      success: true,
      message: 'تم إعداد نظام إدارة المديرين المباشرين بنجاح',
      stats: setupStats,
      errors: errors.slice(0, 10), // عرض أول 10 أخطاء فقط
      details: {
        tablesCreated: createdTables,
        proceduresCreated: createdProcedures,
        uploadDirectoryCreated: true
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message,
      details: 'فشل في إعداد النظام'
    }, { status: 500 });
  }
}

// GET - التحقق من حالة النظام
export async function GET() {
  try {
    const pool = await getConnection();

    // التحقق من وجود الجداول
    const tablesQuery = `
      SELECT TABLE_NAME, 
             (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = t.TABLE_NAME) as COLUMN_COUNT
      FROM INFORMATION_SCHEMA.TABLES t
      WHERE TABLE_NAME IN ('DirectManagers', 'DirectManagersHistory', 'DirectManagersFileUploads')
    `;

    const tablesResult = await pool.request().query(tablesQuery);

    // التحقق من وجود الإجراءات المخزنة
    const proceduresQuery = `
      SELECT ROUTINE_NAME, CREATED, LAST_ALTERED
      FROM INFORMATION_SCHEMA.ROUTINES 
      WHERE ROUTINE_TYPE = 'PROCEDURE' 
      AND ROUTINE_NAME IN ('sp_UpdateDirectManager', 'sp_GetOrganizationalHierarchy')
    `;

    const proceduresResult = await pool.request().query(proceduresQuery);

    // التحقق من وجود أعمدة المديرين في جدول الموظفين
    const employeeColumnsQuery = `
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'Employees' 
      AND COLUMN_NAME IN ('DirectManager1', 'DirectManager2', 'DirectManager3', 'DirectManager4')
    `;

    const employeeColumnsResult = await pool.request().query(employeeColumnsQuery);

    // إحصائيات البيانات
    let dataStats = {};
    try {
      const statsQuery = `
        SELECT 
          (SELECT COUNT(*) FROM DirectManagers WHERE IsActive = 1) as TotalActiveRecords,
          (SELECT COUNT(*) FROM DirectManagersHistory) as TotalHistoryRecords,
          (SELECT COUNT(*) FROM DirectManagersFileUploads) as TotalFileUploads,
          (SELECT MAX(HierarchyLevel) FROM DirectManagers WHERE IsActive = 1) as MaxHierarchyLevel
      `;
      const statsResult = await pool.request().query(statsQuery);
      dataStats = statsResult.recordset[0];
    } catch (error) {
      console.log('⚠️ لا يمكن جلب إحصائيات البيانات (الجداول غير موجودة)');
    }

    const systemStatus = {
      isSetup: tablesResult.recordset.length === 3 && proceduresResult.recordset.length === 2,
      tables: tablesResult.recordset,
      procedures: proceduresResult.recordset,
      employeeColumns: employeeColumnsResult.recordset,
      dataStats,
      setupComplete: tablesResult.recordset.length === 3 && 
                     proceduresResult.recordset.length === 2 && 
                     employeeColumnsResult.recordset.length === 4
    };

    return NextResponse.json({
      success: true,
      systemStatus,
      message: systemStatus.setupComplete ? 
        'النظام مُعد بالكامل وجاهز للاستخدام' : 
        'النظام يحتاج إلى إعداد'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
