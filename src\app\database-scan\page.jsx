'use client';

import { useState } from 'react';
import MainLayout from '@/components/MainLayout';
import { FiDatabase, FiRefreshCw, FiCheck, FiX, FiInfo, FiTable, FiUsers, FiHome, FiTruck } from 'react-icons/fi';

export default function DatabaseScanPage() {
  const [scanData, setScanData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(null);

  const runDatabaseScan = async () => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/database-tables-scan', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
      });

      const result = await response.json();
      if (result.success) {
        setScanData(result.data);
        setLastUpdate(new Date());
      }

    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  const getTableIcon = (tableName) => {
    if (tableName.toLowerCase().includes('employee')) return FiUsers;
    if (tableName.toLowerCase().includes('apartment')) return FiHome;
    if (tableName.toLowerCase().includes('car')) return FiTruck;
    return FiTable;
  };

  const getTableCategory = (tableName) => {
    const name = tableName.toLowerCase();
    if (name.includes('employee') || name.includes('resignation') || name.includes('transfer')) return 'الموظفين';
    if (name.includes('apartment') || name.includes('beneficiar')) return 'الشقق';
    if (name.includes('car')) return 'السيارات';
    if (name.includes('leave') || name.includes('attendance')) return 'الحضور والإجازات';
    if (name.includes('cost') || name.includes('monthly')) return 'التكاليف';
    if (name.includes('alert') || name.includes('notification') || name.includes('user')) return 'النظام';
    return 'أخرى';
  };

  const groupTablesByCategory = (tables) => {
    const grouped = {};
    tables.forEach(table => {
      const category = getTableCategory(table);
      if (!grouped[category]) grouped[category] = [];
      grouped[category].push(table);
    });
    return grouped;
  };

  return (
    <MainLayout>
      <div className="p-6 max-w-7xl mx-auto">
        {/* العنوان */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FiDatabase className="text-3xl text-blue-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                  فحص جداول قاعدة البيانات
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  فحص شامل لجميع الجداول الموجودة في قاعدة البيانات
                </p>
              </div>
            </div>
            
            <button
              onClick={runDatabaseScan}
              disabled={loading}
              className="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              <FiRefreshCw className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
              {loading ? 'جاري الفحص...' : 'بدء الفحص'}
            </button>
          </div>
          
          {lastUpdate && (
            <div className="mt-4 text-sm text-gray-500">
              آخر تحديث: {lastUpdate.toLocaleString('ar-EG')}
            </div>
          )}
        </div>

        {/* نتائج الفحص */}
        {scanData && (
          <div className="space-y-6">
            {/* ملخص عام */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-blue-100 dark:bg-blue-900 p-4 rounded-lg">
                <div className="flex items-center gap-2">
                  <FiDatabase className="text-blue-600 text-xl" />
                  <div>
                    <div className="text-2xl font-bold text-blue-800 dark:text-blue-200">
                      {scanData.summary?.totalTables || 0}
                    </div>
                    <div className="text-sm text-blue-600 dark:text-blue-300">إجمالي الجداول</div>
                  </div>
                </div>
              </div>

              <div className="bg-green-100 dark:bg-green-900 p-4 rounded-lg">
                <div className="flex items-center gap-2">
                  <FiCheck className="text-green-600 text-xl" />
                  <div>
                    <div className="text-2xl font-bold text-green-800 dark:text-green-200">
                      {scanData.summary?.successfulTables || 0}
                    </div>
                    <div className="text-sm text-green-600 dark:text-green-300">جداول ناجحة</div>
                  </div>
                </div>
              </div>

              <div className="bg-red-100 dark:bg-red-900 p-4 rounded-lg">
                <div className="flex items-center gap-2">
                  <FiX className="text-red-600 text-xl" />
                  <div>
                    <div className="text-2xl font-bold text-red-800 dark:text-red-200">
                      {scanData.summary?.failedTables || 0}
                    </div>
                    <div className="text-sm text-red-600 dark:text-red-300">جداول فاشلة</div>
                  </div>
                </div>
              </div>

              <div className="bg-purple-100 dark:bg-purple-900 p-4 rounded-lg">
                <div className="flex items-center gap-2">
                  <FiInfo className="text-purple-600 text-xl" />
                  <div>
                    <div className="text-2xl font-bold text-purple-800 dark:text-purple-200">
                      {scanData.summary?.totalRecords?.toLocaleString('ar-EG') || 0}
                    </div>
                    <div className="text-sm text-purple-600 dark:text-purple-300">إجمالي السجلات</div>
                  </div>
                </div>
              </div>
            </div>

            {/* معلومات قاعدة البيانات */}
            {scanData.databaseInfo && (
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
                <h2 className="text-xl font-bold mb-4 text-gray-800 dark:text-gray-200">معلومات قاعدة البيانات</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-600 dark:text-gray-400">اسم قاعدة البيانات:</span>
                    <div className="text-gray-800 dark:text-gray-200">{scanData.databaseInfo.database}</div>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600 dark:text-gray-400">الخادم:</span>
                    <div className="text-gray-800 dark:text-gray-200">{scanData.databaseInfo.server}</div>
                  </div>
                  <div>
                    <span className="font-medium text-gray-600 dark:text-gray-400">وقت الفحص:</span>
                    <div className="text-gray-800 dark:text-gray-200">
                      {new Date(scanData.databaseInfo.timestamp).toLocaleString('ar-EG')}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* الجداول مجمعة حسب الفئة */}
            {scanData.allTables && scanData.allTables.length > 0 && (
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
                <h2 className="text-xl font-bold mb-6 text-gray-800 dark:text-gray-200">الجداول حسب الفئة</h2>
                
                {Object.entries(groupTablesByCategory(scanData.allTables)).map(([category, tables]) => (
                  <div key={category} className="mb-6">
                    <h3 className="text-lg font-semibold mb-3 text-blue-600 dark:text-blue-400">{category}</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                      {tables.map(table => {
                        const details = scanData.tableDetails[table];
                        const Icon = getTableIcon(table);
                        
                        return (
                          <div key={table} className="border border-gray-200 dark:border-gray-600 rounded-lg p-3">
                            <div className="flex items-center gap-2 mb-2">
                              {Icon && <Icon className="text-blue-600 text-lg" />}
                              <span className="font-medium text-gray-800 dark:text-gray-200">{table}</span>
                              {details?.status === 'success' ? (
                                <FiCheck className="text-green-600 text-sm" />
                              ) : (
                                <FiX className="text-red-600 text-sm" />
                              )}
                            </div>
                            
                            {details && (
                              <div className="text-sm text-gray-600 dark:text-gray-400">
                                <div>السجلات: {details.recordCount?.toLocaleString('ar-EG') || 0}</div>
                                <div>الأعمدة: {details.columns?.length || 0}</div>
                                {details.error && (
                                  <div className="text-red-600 text-xs mt-1">{details.error}</div>
                                )}
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* رسالة عدم وجود بيانات */}
        {!scanData && !loading && (
          <div className="text-center py-12">
            <FiDatabase className="mx-auto text-6xl text-gray-400 mb-4" />
            <h3 className="text-xl font-medium text-gray-600 dark:text-gray-400 mb-2">
              لم يتم فحص قاعدة البيانات بعد
            </h3>
            <p className="text-gray-500 dark:text-gray-500 mb-6">
              اضغط على "بدء الفحص" لفحص جميع الجداول في قاعدة البيانات
            </p>
            <button
              onClick={runDatabaseScan}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              بدء الفحص الآن
            </button>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
