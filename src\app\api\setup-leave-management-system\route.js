import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function POST(request) {
  try {
    const body = await request.json();
    const { action } = body;

    const pool = await getConnection();

    switch (action) {
      case 'create-schema':
        return await createDatabaseSchema(pool);
      case 'setup-relationships':
        return await setupTableRelationships(pool);
      case 'populate-reference-data':
        return await populateReferenceData(pool);
      case 'create-triggers':
        return await createDatabaseTriggers(pool);
      case 'full-setup':
        return await performFullSetup(pool);
      case 'verify-system':
        return await verifySystemIntegrity(pool);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في إعداد نظام إدارة الإجازات: ' + error.message
    }, { status: 500 });
  }
}

// إنشاء مخطط قاعدة البيانات الأساسي
async function createDatabaseSchema(pool) {
  try {
    const results = [];

    // 1. جدول أنواع الطلبات
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='RequestTypes' AND xtype='U')
      BEGIN
        CREATE TABLE RequestTypes (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          TypeCode NVARCHAR(20) NOT NULL UNIQUE,
          TypeNameArabic NVARCHAR(50) NOT NULL,
          TypeNameEnglish NVARCHAR(50) NOT NULL,
          RequiresApproval BIT DEFAULT 1,
          AffectsBalance BIT DEFAULT 1,
          MaxDaysAllowed INT DEFAULT NULL,
          IsActive BIT DEFAULT 1,
          DisplayOrder INT DEFAULT 0,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE()
        )
      END
    `);
    results.push({ table: 'RequestTypes', status: 'تم الإنشاء/التحقق' });

    // 2. جدول الطلبات الورقية (محدث)
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PaperRequests' AND xtype='U')
      BEGIN
        CREATE TABLE PaperRequests (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          EmployeeCode NVARCHAR(20) NOT NULL,
          EmployeeName NVARCHAR(100) NOT NULL,
          Department NVARCHAR(100),
          JobTitle NVARCHAR(100),
          RequestType NVARCHAR(50) NOT NULL,
          LeaveType NVARCHAR(50),
          StartDate DATE,
          EndDate DATE,
          DaysCount INT,
          LeaveReason NVARCHAR(MAX),
          Status NVARCHAR(20) DEFAULT N'قيد المراجعة',
          RequestDate DATETIME DEFAULT GETDATE(),
          ApprovalDate DATETIME,
          RejectionDate DATETIME,
          ApprovedBy NVARCHAR(100),
          RejectedBy NVARCHAR(100),
          ApprovalNotes NVARCHAR(MAX),
          RejectionReason NVARCHAR(MAX),
          IsAutoApproved BIT DEFAULT 0,
          OriginalStatus NVARCHAR(20), -- للرجوع عن الاعتماد
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),
          
          INDEX IX_PaperRequests_Employee (EmployeeCode),
          INDEX IX_PaperRequests_Status (Status),
          INDEX IX_PaperRequests_Type (RequestType),
          INDEX IX_PaperRequests_Dates (StartDate, EndDate),
          INDEX IX_PaperRequests_RequestDate (RequestDate)
        )
      END
    `);
    results.push({ table: 'PaperRequests', status: 'تم الإنشاء/التحقق' });

    // 3. جدول أرصدة الإجازات (محدث)
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LeaveBalances' AND xtype='U')
      BEGIN
        CREATE TABLE LeaveBalances (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          EmployeeCode NVARCHAR(20) NOT NULL,
          EmployeeName NVARCHAR(100) NOT NULL,
          JobTitle NVARCHAR(100),
          Department NVARCHAR(100),
          AnnualBalance INT DEFAULT 15,
          CasualBalance INT DEFAULT 6,
          UsedAnnual INT DEFAULT 0,
          UsedCasual INT DEFAULT 0,
          RemainingAnnual AS (AnnualBalance - UsedAnnual),
          RemainingCasual AS (CasualBalance - UsedCasual),
          Year INT DEFAULT YEAR(GETDATE()),
          LastLeaveDate DATE,
          LastUpdateDate DATETIME DEFAULT GETDATE(),
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),
          
          UNIQUE (EmployeeCode, Year),
          INDEX IX_LeaveBalances_Employee (EmployeeCode),
          INDEX IX_LeaveBalances_Year (Year)
        )
      END
    `);
    results.push({ table: 'LeaveBalances', status: 'تم الإنشاء/التحقق' });

    // 4. جدول التمام اليومي (محدث)
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DailyAttendance' AND xtype='U')
      BEGIN
        CREATE TABLE DailyAttendance (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          AttendanceDate DATE NOT NULL,
          EmployeeCode NVARCHAR(20) NOT NULL,
          EmployeeName NVARCHAR(100) NOT NULL,
          Department NVARCHAR(100),
          JobTitle NVARCHAR(100),
          Attendance NVARCHAR(50) NOT NULL,
          CheckInTime NVARCHAR(20),
          CheckOutTime NVARCHAR(20),
          Notes NVARCHAR(MAX),
          IsFromRequest BIT DEFAULT 0, -- هل من طلب معتمد
          RequestID INT NULL, -- ربط بالطلب
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),
          
          UNIQUE (AttendanceDate, EmployeeCode),
          INDEX IX_DailyAttendance_Date (AttendanceDate),
          INDEX IX_DailyAttendance_Employee (EmployeeCode),
          INDEX IX_DailyAttendance_Request (RequestID)
        )
      END
    `);
    results.push({ table: 'DailyAttendance', status: 'تم الإنشاء/التحقق' });

    // 5. جدول التمام الشهري (محدث)
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MonthlyAttendance' AND xtype='U')
      BEGIN
        CREATE TABLE MonthlyAttendance (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          EmployeeCode NVARCHAR(20) NOT NULL,
          EmployeeName NVARCHAR(100) NOT NULL,
          Department NVARCHAR(100),
          JobTitle NVARCHAR(100),
          Month INT NOT NULL,
          Year INT NOT NULL,
          TotalWorkingDays INT DEFAULT 0,
          TotalPresent INT DEFAULT 0,
          TotalAbsent INT DEFAULT 0,
          TotalLeaves INT DEFAULT 0,
          TotalMissions INT DEFAULT 0,
          AttendanceData NVARCHAR(MAX), -- JSON للتفاصيل اليومية
          LastUpdated DATETIME DEFAULT GETDATE(),
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),
          
          UNIQUE (EmployeeCode, Month, Year),
          INDEX IX_MonthlyAttendance_Employee (EmployeeCode),
          INDEX IX_MonthlyAttendance_Period (Month, Year)
        )
      END
    `);
    results.push({ table: 'MonthlyAttendance', status: 'تم الإنشاء/التحقق' });

    // 6. جدول سجل العمليات
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SystemOperationLog' AND xtype='U')
      BEGIN
        CREATE TABLE SystemOperationLog (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          OperationType NVARCHAR(50) NOT NULL, -- REQUEST_CREATED, REQUEST_APPROVED, BALANCE_UPDATED, etc.
          EntityType NVARCHAR(50) NOT NULL, -- PaperRequest, LeaveBalance, DailyAttendance
          EntityID INT NOT NULL,
          EmployeeCode NVARCHAR(20),
          OperationDetails NVARCHAR(MAX), -- JSON
          PerformedBy NVARCHAR(100),
          OperationDate DATETIME DEFAULT GETDATE(),
          
          INDEX IX_SystemOperationLog_Type (OperationType),
          INDEX IX_SystemOperationLog_Entity (EntityType, EntityID),
          INDEX IX_SystemOperationLog_Employee (EmployeeCode),
          INDEX IX_SystemOperationLog_Date (OperationDate)
        )
      END
    `);
    results.push({ table: 'SystemOperationLog', status: 'تم الإنشاء/التحقق' });

    return NextResponse.json({
      success: true,
      message: 'تم إنشاء مخطط قاعدة البيانات بنجاح',
      results: results
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في إنشاء مخطط قاعدة البيانات: ' + error.message
    }, { status: 500 });
  }
}

// إعداد العلاقات بين الجداول
async function setupTableRelationships(pool) {
  try {
    const results = [];

    // إضافة Foreign Keys والقيود
    await pool.request().query(`
      -- ربط PaperRequests بـ Employees (إذا كان جدول Employees موجود)
      IF EXISTS (SELECT * FROM sysobjects WHERE name='Employees' AND xtype='U')
      AND NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_PaperRequests_Employees')
      BEGIN
        ALTER TABLE PaperRequests
        ADD CONSTRAINT FK_PaperRequests_Employees
        FOREIGN KEY (EmployeeCode) REFERENCES Employees(EmployeeCode)
        ON UPDATE CASCADE
      END
      
      -- ربط LeaveBalances بـ Employees
      IF EXISTS (SELECT * FROM sysobjects WHERE name='Employees' AND xtype='U')
      AND NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_LeaveBalances_Employees')
      BEGIN
        ALTER TABLE LeaveBalances
        ADD CONSTRAINT FK_LeaveBalances_Employees
        FOREIGN KEY (EmployeeCode) REFERENCES Employees(EmployeeCode)
        ON UPDATE CASCADE
      END
      
      -- ربط DailyAttendance بـ PaperRequests
      IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_DailyAttendance_PaperRequests')
      BEGIN
        ALTER TABLE DailyAttendance
        ADD CONSTRAINT FK_DailyAttendance_PaperRequests
        FOREIGN KEY (RequestID) REFERENCES PaperRequests(ID)
        ON DELETE SET NULL
      END
    `);
    results.push({ operation: 'Foreign Keys', status: 'تم الإنشاء' });

    // إضافة Check Constraints
    await pool.request().query(`
      -- قيود التحقق من صحة البيانات
      IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_PaperRequests_Status')
      BEGIN
        ALTER TABLE PaperRequests
        ADD CONSTRAINT CK_PaperRequests_Status
        CHECK (Status IN (N'قيد المراجعة', N'معتمد', N'مرفوض', N'ملغي'))
      END
      
      IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_PaperRequests_DaysCount')
      BEGIN
        ALTER TABLE PaperRequests
        ADD CONSTRAINT CK_PaperRequests_DaysCount
        CHECK (DaysCount >= 0 AND DaysCount <= 365)
      END
      
      IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE name = 'CK_LeaveBalances_Positive')
      BEGIN
        ALTER TABLE LeaveBalances
        ADD CONSTRAINT CK_LeaveBalances_Positive
        CHECK (AnnualBalance >= 0 AND CasualBalance >= 0 AND UsedAnnual >= 0 AND UsedCasual >= 0)
      END
    `);
    results.push({ operation: 'Check Constraints', status: 'تم الإنشاء' });

    return NextResponse.json({
      success: true,
      message: 'تم إعداد العلاقات بين الجداول بنجاح',
      results: results
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في إعداد العلاقات: ' + error.message
    }, { status: 500 });
  }
}

// تعبئة البيانات المرجعية
async function populateReferenceData(pool) {
  try {
    const results = [];

    // تعبئة أنواع الطلبات
    await pool.request().query(`
      -- حذف البيانات القديمة وإعادة إدراج البيانات المحدثة
      DELETE FROM RequestTypes;

      INSERT INTO RequestTypes (TypeCode, TypeNameArabic, TypeNameEnglish, RequiresApproval, AffectsBalance, MaxDaysAllowed, DisplayOrder)
      VALUES
        ('annual', N'إجازة إعتيادية', 'Annual Leave', 1, 1, 15, 1),
        ('casual', N'إجازة عارضة', 'Casual Leave', 1, 1, 6, 2),
        ('unpaid', N'إجازة بدون أجر', 'Unpaid Leave', 1, 0, NULL, 3),
        ('sick', N'إجازة مرضية', 'Sick Leave', 0, 0, NULL, 4),
        ('mission', N'مأمورية', 'Mission', 0, 0, NULL, 5),
        ('permission', N'إذن', 'Permission', 0, 0, 1, 6),
        ('maternity', N'إجازة أمومة', 'Maternity Leave', 1, 0, 90, 7),
        ('paternity', N'إجازة أبوة', 'Paternity Leave', 1, 0, 3, 8),
        ('official', N'إجازة رسمية', 'Official Holiday', 0, 0, NULL, 9),
        ('night_shift', N'وردية ليلية', 'Night Shift', 0, 0, NULL, 10);
    `);
    results.push({ operation: 'Request Types Data', status: 'تم التعبئة' });

    // إنشاء أرصدة إجازات للموظفين الموجودين
    await pool.request().query(`
      IF EXISTS (SELECT * FROM sysobjects WHERE name='Employees' AND xtype='U')
      BEGIN
        INSERT INTO LeaveBalances (EmployeeCode, EmployeeName, JobTitle, Department, Year)
        SELECT
          e.EmployeeCode,
          e.EmployeeName,
          e.JobTitle,
          e.Department,
          YEAR(GETDATE())
        FROM Employees e
        WHERE e.EmployeeCode NOT IN (
          SELECT lb.EmployeeCode
          FROM LeaveBalances lb
          WHERE lb.Year = YEAR(GETDATE())
        )
        AND e.CurrentStatus IN (N'ساري', N'نشط', N'سارى')
      END
    `);
    results.push({ operation: 'Leave Balances Initialization', status: 'تم التعبئة' });

    return NextResponse.json({
      success: true,
      message: 'تم تعبئة البيانات المرجعية بنجاح',
      results: results
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في تعبئة البيانات المرجعية: ' + error.message
    }, { status: 500 });
  }
}

// إنشاء المشغلات (Triggers)
async function createDatabaseTriggers(pool) {
  try {
    const results = [];

    // مشغل تحديث التمام الشهري عند تغيير التمام اليومي
    await pool.request().query(`
      -- حذف المشغل إذا كان موجوداً
      IF EXISTS (SELECT * FROM sys.triggers WHERE name = 'TR_DailyAttendance_UpdateMonthly')
      BEGIN
        DROP TRIGGER TR_DailyAttendance_UpdateMonthly
      END

      -- إنشاء مشغل جديد
      CREATE TRIGGER TR_DailyAttendance_UpdateMonthly
      ON DailyAttendance
      AFTER INSERT, UPDATE, DELETE
      AS
      BEGIN
        SET NOCOUNT ON;

        -- تحديث التمام الشهري للموظفين المتأثرين
        DECLARE @EmployeeCodes TABLE (EmployeeCode NVARCHAR(20), Month INT, Year INT);

        -- جمع الموظفين المتأثرين من العمليات المدرجة والمحدثة
        INSERT INTO @EmployeeCodes (EmployeeCode, Month, Year)
        SELECT DISTINCT
          i.EmployeeCode,
          MONTH(i.AttendanceDate),
          YEAR(i.AttendanceDate)
        FROM inserted i

        UNION

        SELECT DISTINCT
          d.EmployeeCode,
          MONTH(d.AttendanceDate),
          YEAR(d.AttendanceDate)
        FROM deleted d;

        -- تحديث أو إدراج سجلات التمام الشهري
        MERGE MonthlyAttendance AS target
        USING (
          SELECT
            ec.EmployeeCode,
            ec.Month,
            ec.Year,
            MAX(da.EmployeeName) as EmployeeName,
            MAX(da.Department) as Department,
            MAX(da.JobTitle) as JobTitle,
            COUNT(*) as TotalWorkingDays,
            SUM(CASE WHEN da.Attendance = N'حضور' THEN 1 ELSE 0 END) as TotalPresent,
            SUM(CASE WHEN da.Attendance = N'غياب' THEN 1 ELSE 0 END) as TotalAbsent,
            SUM(CASE WHEN da.Attendance LIKE N'%إجازة%' THEN 1 ELSE 0 END) as TotalLeaves,
            SUM(CASE WHEN da.Attendance = N'مأمورية' THEN 1 ELSE 0 END) as TotalMissions
          FROM @EmployeeCodes ec
          LEFT JOIN DailyAttendance da ON ec.EmployeeCode = da.EmployeeCode
            AND MONTH(da.AttendanceDate) = ec.Month
            AND YEAR(da.AttendanceDate) = ec.Year
          GROUP BY ec.EmployeeCode, ec.Month, ec.Year
        ) AS source ON target.EmployeeCode = source.EmployeeCode
          AND target.Month = source.Month
          AND target.Year = source.Year
        WHEN MATCHED THEN
          UPDATE SET
            TotalWorkingDays = source.TotalWorkingDays,
            TotalPresent = source.TotalPresent,
            TotalAbsent = source.TotalAbsent,
            TotalLeaves = source.TotalLeaves,
            TotalMissions = source.TotalMissions,
            LastUpdated = GETDATE(),
            UpdatedAt = GETDATE()
        WHEN NOT MATCHED THEN
          INSERT (EmployeeCode, EmployeeName, Department, JobTitle, Month, Year,
                  TotalWorkingDays, TotalPresent, TotalAbsent, TotalLeaves, TotalMissions)
          VALUES (source.EmployeeCode, source.EmployeeName, source.Department, source.JobTitle,
                  source.Month, source.Year, source.TotalWorkingDays, source.TotalPresent,
                  source.TotalAbsent, source.TotalLeaves, source.TotalMissions);
      END
    `);
    results.push({ trigger: 'TR_DailyAttendance_UpdateMonthly', status: 'تم الإنشاء' });

    // مشغل تسجيل العمليات
    await pool.request().query(`
      -- حذف المشغل إذا كان موجوداً
      IF EXISTS (SELECT * FROM sys.triggers WHERE name = 'TR_PaperRequests_LogOperations')
      BEGIN
        DROP TRIGGER TR_PaperRequests_LogOperations
      END

      -- إنشاء مشغل تسجيل العمليات
      CREATE TRIGGER TR_PaperRequests_LogOperations
      ON PaperRequests
      AFTER INSERT, UPDATE
      AS
      BEGIN
        SET NOCOUNT ON;

        -- تسجيل العمليات الجديدة
        INSERT INTO SystemOperationLog (OperationType, EntityType, EntityID, EmployeeCode, OperationDetails, PerformedBy)
        SELECT
          CASE
            WHEN d.ID IS NULL THEN 'REQUEST_CREATED'
            WHEN i.Status != d.Status THEN 'REQUEST_STATUS_CHANGED'
            ELSE 'REQUEST_UPDATED'
          END,
          'PaperRequest',
          i.ID,
          i.EmployeeCode,
          JSON_OBJECT(
            'RequestType', i.RequestType,
            'LeaveType', i.LeaveType,
            'Status', i.Status,
            'PreviousStatus', ISNULL(d.Status, ''),
            'StartDate', FORMAT(i.StartDate, 'yyyy-MM-dd'),
            'EndDate', FORMAT(i.EndDate, 'yyyy-MM-dd'),
            'DaysCount', i.DaysCount
          ),
          ISNULL(i.ApprovedBy, SYSTEM_USER)
        FROM inserted i
        LEFT JOIN deleted d ON i.ID = d.ID;
      END
    `);
    results.push({ trigger: 'TR_PaperRequests_LogOperations', status: 'تم الإنشاء' });

    return NextResponse.json({
      success: true,
      message: 'تم إنشاء المشغلات بنجاح',
      results: results
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في إنشاء المشغلات: ' + error.message
    }, { status: 500 });
  }
}

// تنفيذ الإعداد الكامل
async function performFullSetup(pool) {
  try {
    const results = [];

    // 1. إنشاء مخطط قاعدة البيانات
    const schemaResult = await createDatabaseSchema(pool);
    if (schemaResult.success) {
      results.push({ step: 'Database Schema', status: 'تم بنجاح' });
    }

    // 2. إعداد العلاقات
    const relationshipsResult = await setupTableRelationships(pool);
    if (relationshipsResult.success) {
      results.push({ step: 'Table Relationships', status: 'تم بنجاح' });
    }

    // 3. تعبئة البيانات المرجعية
    const referenceDataResult = await populateReferenceData(pool);
    if (referenceDataResult.success) {
      results.push({ step: 'Reference Data', status: 'تم بنجاح' });
    }

    // 4. إنشاء المشغلات
    const triggersResult = await createDatabaseTriggers(pool);
    if (triggersResult.success) {
      results.push({ step: 'Database Triggers', status: 'تم بنجاح' });
    }

    return NextResponse.json({
      success: true,
      message: 'تم إعداد نظام إدارة الإجازات بالكامل بنجاح',
      results: results
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الإعداد الكامل: ' + error.message
    }, { status: 500 });
  }
}

// التحقق من سلامة النظام
async function verifySystemIntegrity(pool) {
  try {
    const verification = {
      tables: {},
      relationships: {},
      triggers: {},
      data: {},
      issues: []
    };

    // فحص الجداول
    const tablesCheck = await pool.request().query(`
      SELECT TABLE_NAME,
             (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = t.TABLE_NAME) as ColumnCount
      FROM INFORMATION_SCHEMA.TABLES t
      WHERE TABLE_NAME IN ('RequestTypes', 'PaperRequests', 'LeaveBalances', 'DailyAttendance', 'MonthlyAttendance', 'SystemOperationLog')
    `);

    tablesCheck.recordset.forEach(table => {
      verification.tables[table.TABLE_NAME] = {
        exists: true,
        columnCount: table.ColumnCount
      };
    });

    // فحص العلاقات
    const relationshipsCheck = await pool.request().query(`
      SELECT
        fk.name as ForeignKeyName,
        OBJECT_NAME(fk.parent_object_id) as TableName,
        OBJECT_NAME(fk.referenced_object_id) as ReferencedTable
      FROM sys.foreign_keys fk
      WHERE OBJECT_NAME(fk.parent_object_id) IN ('PaperRequests', 'LeaveBalances', 'DailyAttendance')
    `);

    verification.relationships = relationshipsCheck.recordset;

    // فحص المشغلات
    const triggersCheck = await pool.request().query(`
      SELECT
        t.name as TriggerName,
        OBJECT_NAME(t.parent_id) as TableName,
        t.is_disabled
      FROM sys.triggers t
      WHERE t.name IN ('TR_DailyAttendance_UpdateMonthly', 'TR_PaperRequests_LogOperations')
    `);

    verification.triggers = triggersCheck.recordset;

    // فحص البيانات المرجعية
    const requestTypesCount = await pool.request().query(`
      SELECT COUNT(*) as Count FROM RequestTypes
    `);

    verification.data.requestTypes = requestTypesCount.recordset[0].Count;

    // فحص أرصدة الإجازات
    const leaveBalancesCount = await pool.request().query(`
      SELECT COUNT(*) as Count FROM LeaveBalances WHERE Year = YEAR(GETDATE())
    `);

    verification.data.leaveBalances = leaveBalancesCount.recordset[0].Count;

    // التحقق من وجود مشاكل
    const requiredTables = ['RequestTypes', 'PaperRequests', 'LeaveBalances', 'DailyAttendance', 'MonthlyAttendance'];
    requiredTables.forEach(tableName => {
      if (!verification.tables[tableName]) {
        verification.issues.push(`الجدول ${tableName} غير موجود`);
      }
    });

    if (verification.data.requestTypes === 0) {
      verification.issues.push('لا توجد أنواع طلبات في الجدول المرجعي');
    }

    return NextResponse.json({
      success: verification.issues.length === 0,
      message: verification.issues.length === 0 ? 'النظام سليم ويعمل بشكل صحيح' : 'تم العثور على مشاكل في النظام',
      verification: verification
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في التحقق من سلامة النظام: ' + error.message
    }, { status: 500 });
  }
}
