import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function POST(request) {
  let pool = null;

  try {
    const body = await request.json();
    const { action } = body;

    // التحقق من وجود الإجراء
    if (!action) {
      return NextResponse.json({
        success: false,
        error: 'الإجراء مطلوب'
      }, { status: 400 });
    }

    // الحصول على اتصال قاعدة البيانات مع معالجة الأخطاء
    try {
      pool = await getConnection();

      // التحقق من صحة الاتصال
      if (!pool || !pool.connected) {
        throw new Error('فشل في الاتصال بقاعدة البيانات');
      }
    } catch (connectionError) {

      return NextResponse.json({
        success: false,
        error: 'فشل في الاتصال بقاعدة البيانات'
      }, { status: 500 });
    }

    switch (action) {
      case 'setup':
        return await setupNotificationTables(pool);
      case 'logAction':
        return await logUserAction(pool, body);
      case 'getActions':
        return await getUserActions(pool, body);
      case 'getNotifications':
        return await getNotifications(pool, body);
      case 'markAsRead':
        return await markNotificationAsRead(pool, body);
      case 'getStatistics':
        return await getActionStatistics(pool, body);
      case 'getRecentActions':
        return await getRecentActions(pool, body);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير مدعوم: ' + action
        }, { status: 400 });
    }
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  } finally {
    // تنظيف الاتصال إذا لزم الأمر
    try {
      if (pool && pool.connected) {
        // لا نغلق الاتصال هنا لأنه مُدار بواسطة connection pool

      }
    } catch (cleanupError) {

    }
  }
}

// إعداد جداول النظام
async function setupNotificationTables(pool) {
  try {

    // 1. جدول إجراءات المستخدمين
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='UserActions' AND xtype='U')
      BEGIN
        CREATE TABLE UserActions (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          UserCode NVARCHAR(50) NOT NULL,
          UserName NVARCHAR(100),
          ActionType NVARCHAR(50) NOT NULL,
          ActionDescription NVARCHAR(500) NOT NULL,
          TargetTable NVARCHAR(100),
          TargetID NVARCHAR(50),
          OldValues NVARCHAR(MAX),
          NewValues NVARCHAR(MAX),
          IPAddress NVARCHAR(45),
          UserAgent NVARCHAR(500),
          SessionID NVARCHAR(100),
          ActionDate DATETIME DEFAULT GETDATE(),
          IsSuccess BIT DEFAULT 1,
          ErrorMessage NVARCHAR(MAX),
          AdditionalData NVARCHAR(MAX)
        )
        
        CREATE INDEX IX_UserActions_UserCode ON UserActions(UserCode)
        CREATE INDEX IX_UserActions_ActionType ON UserActions(ActionType)
        CREATE INDEX IX_UserActions_ActionDate ON UserActions(ActionDate)
        CREATE INDEX IX_UserActions_TargetTable ON UserActions(TargetTable)
      END
    `);

    // 2. جدول الإشعارات
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Notifications' AND xtype='U')
      BEGIN
        CREATE TABLE Notifications (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          UserCode NVARCHAR(50) NOT NULL,
          Title NVARCHAR(200) NOT NULL,
          Message NVARCHAR(1000) NOT NULL,
          NotificationType NVARCHAR(50) NOT NULL,
          Priority NVARCHAR(20) DEFAULT 'normal',
          IsRead BIT DEFAULT 0,
          ActionID INT,
          RelatedTable NVARCHAR(100),
          RelatedID NVARCHAR(50),
          CreatedAt DATETIME DEFAULT GETDATE(),
          ReadAt DATETIME,
          ExpiresAt DATETIME
        )
        
        CREATE INDEX IX_Notifications_UserCode ON Notifications(UserCode)
        CREATE INDEX IX_Notifications_IsRead ON Notifications(IsRead)
        CREATE INDEX IX_Notifications_CreatedAt ON Notifications(CreatedAt)
        CREATE INDEX IX_Notifications_NotificationType ON Notifications(NotificationType)
      END
    `);

    // 3. جدول جلسات المستخدمين
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='UserSessions' AND xtype='U')
      BEGIN
        CREATE TABLE UserSessions (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          SessionID NVARCHAR(100) NOT NULL UNIQUE,
          UserCode NVARCHAR(50) NOT NULL,
          UserName NVARCHAR(100),
          LoginTime DATETIME DEFAULT GETDATE(),
          LastActivity DATETIME DEFAULT GETDATE(),
          LogoutTime DATETIME,
          IPAddress NVARCHAR(45),
          UserAgent NVARCHAR(500),
          IsActive BIT DEFAULT 1,
          DeviceInfo NVARCHAR(500)
        )
        
        CREATE INDEX IX_UserSessions_SessionID ON UserSessions(SessionID)
        CREATE INDEX IX_UserSessions_UserCode ON UserSessions(UserCode)
        CREATE INDEX IX_UserSessions_IsActive ON UserSessions(IsActive)
      END
    `);

    // 4. جدول أنواع الإجراءات
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ActionTypes' AND xtype='U')
      BEGIN
        CREATE TABLE ActionTypes (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          ActionCode NVARCHAR(50) NOT NULL UNIQUE,
          ActionName NVARCHAR(100) NOT NULL,
          ActionNameAr NVARCHAR(100) NOT NULL,
          Category NVARCHAR(50) NOT NULL,
          Description NVARCHAR(500),
          IsActive BIT DEFAULT 1,
          RequiresNotification BIT DEFAULT 1,
          NotificationTemplate NVARCHAR(1000),
          CreatedAt DATETIME DEFAULT GETDATE()
        )
        
        CREATE INDEX IX_ActionTypes_ActionCode ON ActionTypes(ActionCode)
        CREATE INDEX IX_ActionTypes_Category ON ActionTypes(Category)
      END
    `);

    // إدراج أنواع الإجراءات الأساسية
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM ActionTypes WHERE ActionCode = 'LOGIN')
      BEGIN
        INSERT INTO ActionTypes (ActionCode, ActionName, ActionNameAr, Category, Description, NotificationTemplate)
        VALUES 
        ('LOGIN', 'User Login', 'تسجيل دخول', 'Authentication', 'User logged into the system', 'تم تسجيل دخول المستخدم {UserName}'),
        ('LOGOUT', 'User Logout', 'تسجيل خروج', 'Authentication', 'User logged out of the system', 'تم تسجيل خروج المستخدم {UserName}'),
        ('CREATE_EMPLOYEE', 'Create Employee', 'إضافة موظف', 'Employee Management', 'New employee created', 'تم إضافة موظف جديد: {EmployeeName}'),
        ('UPDATE_EMPLOYEE', 'Update Employee', 'تعديل موظف', 'Employee Management', 'Employee information updated', 'تم تعديل بيانات الموظف: {EmployeeName}'),
        ('DELETE_EMPLOYEE', 'Delete Employee', 'حذف موظف', 'Employee Management', 'Employee deleted', 'تم حذف الموظف: {EmployeeName}'),
        ('CREATE_APARTMENT', 'Create Apartment', 'إضافة شقة', 'Apartment Management', 'New apartment created', 'تم إضافة شقة جديدة: {ApartmentCode}'),
        ('UPDATE_APARTMENT', 'Update Apartment', 'تعديل شقة', 'Apartment Management', 'Apartment information updated', 'تم تعديل بيانات الشقة: {ApartmentCode}'),
        ('DELETE_APARTMENT', 'Delete Apartment', 'حذف شقة', 'Apartment Management', 'Apartment deleted', 'تم حذف الشقة: {ApartmentCode}'),
        ('ADD_BENEFICIARY', 'Add Beneficiary', 'إضافة مستفيد', 'Apartment Management', 'Beneficiary added to apartment', 'تم إضافة مستفيد للشقة: {ApartmentCode}'),
        ('REMOVE_BENEFICIARY', 'Remove Beneficiary', 'إزالة مستفيد', 'Apartment Management', 'Beneficiary removed from apartment', 'تم إزالة مستفيد من الشقة: {ApartmentCode}'),
        ('CREATE_CAR', 'Create Car', 'إضافة سيارة', 'Car Management', 'New car created', 'تم إضافة سيارة جديدة: {CarCode}'),
        ('UPDATE_CAR', 'Update Car', 'تعديل سيارة', 'Car Management', 'Car information updated', 'تم تعديل بيانات السيارة: {CarCode}'),
        ('DELETE_CAR', 'Delete Car', 'حذف سيارة', 'Car Management', 'Car deleted', 'تم حذف السيارة: {CarCode}'),
        ('UPLOAD_DOCUMENT', 'Upload Document', 'رفع مستند', 'Document Management', 'Document uploaded', 'تم رفع مستند: {DocumentType}'),
        ('DELETE_DOCUMENT', 'Delete Document', 'حذف مستند', 'Document Management', 'Document deleted', 'تم حذف مستند: {DocumentType}'),
        ('ATTENDANCE_UPLOAD', 'Upload Attendance', 'رفع حضور', 'Attendance Management', 'Attendance data uploaded', 'تم رفع بيانات الحضور'),
        ('LEAVE_REQUEST', 'Leave Request', 'طلب إجازة', 'Leave Management', 'Leave request submitted', 'تم تقديم طلب إجازة'),
        ('APPROVE_LEAVE', 'Approve Leave', 'موافقة إجازة', 'Leave Management', 'Leave request approved', 'تم الموافقة على طلب الإجازة'),
        ('REJECT_LEAVE', 'Reject Leave', 'رفض إجازة', 'Leave Management', 'Leave request rejected', 'تم رفض طلب الإجازة'),
        ('COST_ENTRY', 'Cost Entry', 'إدخال تكلفة', 'Cost Management', 'Cost entry created', 'تم إدخال تكلفة جديدة'),
        ('SYSTEM_BACKUP', 'System Backup', 'نسخ احتياطي', 'System Management', 'System backup created', 'تم إنشاء نسخة احتياطية للنظام'),
        ('LEAVE_REQUEST_SUBMITTED', 'Leave Request Submitted', 'تقديم طلب إجازة', 'Leave Management', 'Leave request submitted', 'تم تقديم طلب إجازة جديد من {EmployeeName}'),
        ('LEAVE_REQUEST_APPROVED', 'Leave Request Approved', 'اعتماد طلب إجازة', 'Leave Management', 'Leave request approved', 'تم اعتماد طلب الإجازة للموظف {EmployeeName}'),
        ('LEAVE_REQUEST_REJECTED', 'Leave Request Rejected', 'رفض طلب إجازة', 'Leave Management', 'Leave request rejected', 'تم رفض طلب الإجازة للموظف {EmployeeName}')
      END
    `);

    return NextResponse.json({
      success: true,
      message: 'تم إعداد نظام الإشعارات بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في إعداد نظام الإشعارات: ' + error.message
    }, { status: 500 });
  }
}

// تسجيل إجراء مستخدم
async function logUserAction(pool, data) {
  try {
    const {
      userCode,
      userName,
      actionType,
      actionDescription,
      targetTable,
      targetID,
      oldValues,
      newValues,
      ipAddress,
      userAgent,
      sessionID,
      isSuccess = true,
      errorMessage,
      additionalData
    } = data;

    // التحقق من صحة الاتصال
    if (!pool || !pool.connected) {

      return NextResponse.json({
        success: false,
        error: 'اتصال قاعدة البيانات مغلق'
      }, { status: 500 });
    }

    // التحقق من البيانات المطلوبة
    if (!userCode || !actionType) {
      return NextResponse.json({
        success: false,
        error: 'بيانات غير كافية: userCode و actionType مطلوبان'
      }, { status: 400 });
    }

    // تسجيل الإجراء مع معالجة الأخطاء
    let actionResult;
    try {
      actionResult = await pool.request()
        .input('userCode', sql.NVarChar, userCode)
        .input('userName', sql.NVarChar, userName || userCode)
        .input('actionType', sql.NVarChar, actionType)
        .input('actionDescription', sql.NVarChar, actionDescription || 'إجراء غير محدد')
        .input('targetTable', sql.NVarChar, targetTable || '')
        .input('targetID', sql.NVarChar, targetID || '')
        .input('oldValues', sql.NVarChar, oldValues ? JSON.stringify(oldValues) : null)
        .input('newValues', sql.NVarChar, newValues ? JSON.stringify(newValues) : null)
        .input('ipAddress', sql.NVarChar, ipAddress || '')
        .input('userAgent', sql.NVarChar, userAgent || '')
        .input('sessionID', sql.NVarChar, sessionID || '')
        .input('isSuccess', sql.Bit, isSuccess)
        .input('errorMessage', sql.NVarChar, errorMessage || null)
        .input('additionalData', sql.NVarChar, additionalData ? JSON.stringify(additionalData) : null)
        .query(`
          INSERT INTO UserActions (
            UserCode, UserName, ActionType, ActionDescription, TargetTable, TargetID,
            OldValues, NewValues, IPAddress, UserAgent, SessionID, IsSuccess, ErrorMessage, AdditionalData
          )
          OUTPUT INSERTED.ID
          VALUES (
            @userCode, @userName, @actionType, @actionDescription, @targetTable, @targetID,
            @oldValues, @newValues, @ipAddress, @userAgent, @sessionID, @isSuccess, @errorMessage, @additionalData
          )
        `);
    } catch (insertError) {

      return NextResponse.json({
        success: false,
        error: 'فشل في تسجيل الإجراء: ' + insertError.message
      }, { status: 500 });
    }

    if (!actionResult || !actionResult.recordset || actionResult.recordset.length === 0) {

      return NextResponse.json({
        success: false,
        error: 'فشل في تسجيل الإجراء'
      }, { status: 500 });
    }

    const actionId = actionResult.recordset[0].ID;

    // إنشاء إشعار إذا كان مطلوباً (بشكل منفصل لتجنب فشل العملية الأساسية)
    try {
      await createNotificationForAction(pool, actionId, data);
    } catch (notificationError) {
      console.error('خطأ في إنشاء الإشعار (لكن الإجراء تم تسجيله):', notificationError);
      // لا نفشل العملية الأساسية بسبب فشل الإشعار
    }

    return NextResponse.json({
      success: true,
      message: 'تم تسجيل الإجراء بنجاح',
      data: { actionId }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في تسجيل الإجراء: ' + error.message
    }, { status: 500 });
  }
}

// إنشاء إشعار للإجراء
async function createNotificationForAction(pool, actionId, actionData) {
  try {
    // التحقق من صحة الاتصال
    if (!pool || !pool.connected) {

      return;
    }

    const { userCode, userName, actionType, actionDescription, targetTable, targetID } = actionData;

    // التحقق من البيانات المطلوبة
    if (!userCode || !actionType) {

      return;
    }

    // جلب قالب الإشعار مع معالجة الأخطاء
    let templateResult;
    try {
      templateResult = await pool.request()
        .input('actionType', sql.NVarChar, actionType)
        .query(`
          SELECT NotificationTemplate, ActionNameAr, RequiresNotification
          FROM ActionTypes
          WHERE ActionCode = @actionType AND RequiresNotification = 1
        `);
    } catch (templateError) {

      return;
    }

    if (!templateResult || templateResult.recordset.length === 0) {

      return; // لا يتطلب إشعار
    }

    const template = templateResult.recordset[0];

    // تخصيص رسالة الإشعار
    let message = template.NotificationTemplate || actionDescription || 'إجراء جديد';
    message = message.replace('{UserName}', userName || userCode);
    message = message.replace('{ActionType}', template.ActionNameAr || actionType);

    // إضافة الإشعار مع معالجة الأخطاء
    try {
      await pool.request()
        .input('userCode', sql.NVarChar, userCode)
        .input('title', sql.NVarChar, `إجراء جديد: ${template.ActionNameAr || actionType}`)
        .input('message', sql.NVarChar, message)
        .input('notificationType', sql.NVarChar, actionType)
        .input('actionId', sql.Int, actionId)
        .input('relatedTable', sql.NVarChar, targetTable || '')
        .input('relatedId', sql.NVarChar, targetID || '')
        .query(`
          INSERT INTO Notifications (
            UserCode, Title, Message, NotificationType, ActionID, RelatedTable, RelatedID
          )
          VALUES (
            @userCode, @title, @message, @notificationType, @actionId, @relatedTable, @relatedId
          )
        `);

    } catch (insertError) {

    }

  } catch (error) {

  }
}

// جلب إجراءات المستخدم
async function getUserActions(pool, data) {
  try {
    const {
      userCode,
      actionType,
      targetTable,
      startDate,
      endDate,
      page = 1,
      limit = 50,
      isSuccess
    } = data;

    let whereClause = 'WHERE 1=1';
    const request = pool.request();

    if (userCode) {
      whereClause += ' AND UserCode = @userCode';
      request.input('userCode', sql.NVarChar, userCode);
    }

    if (actionType) {
      whereClause += ' AND ActionType = @actionType';
      request.input('actionType', sql.NVarChar, actionType);
    }

    if (targetTable) {
      whereClause += ' AND TargetTable = @targetTable';
      request.input('targetTable', sql.NVarChar, targetTable);
    }

    if (startDate) {
      whereClause += ' AND ActionDate >= @startDate';
      request.input('startDate', sql.DateTime, new Date(startDate));
    }

    if (endDate) {
      whereClause += ' AND ActionDate <= @endDate';
      request.input('endDate', sql.DateTime, new Date(endDate));
    }

    if (isSuccess !== undefined) {
      whereClause += ' AND IsSuccess = @isSuccess';
      request.input('isSuccess', sql.Bit, isSuccess);
    }

    const offset = (page - 1) * limit;
    request.input('offset', sql.Int, offset);
    request.input('limit', sql.Int, limit);

    const query = `
      SELECT
        ua.ID,
        ua.UserCode,
        ua.UserName,
        ua.ActionType,
        ua.ActionDescription,
        ua.TargetTable,
        ua.TargetID,
        ua.OldValues,
        ua.NewValues,
        ua.IPAddress,
        ua.UserAgent,
        ua.SessionID,
        ua.ActionDate,
        ua.IsSuccess,
        ua.ErrorMessage,
        ua.AdditionalData,
        at.ActionNameAr,
        at.Category
      FROM UserActions ua
      LEFT JOIN ActionTypes at ON ua.ActionType = at.ActionCode
      ${whereClause}
      ORDER BY ua.ActionDate DESC
      OFFSET @offset ROWS
      FETCH NEXT @limit ROWS ONLY
    `;

    const result = await request.query(query);

    // جلب العدد الإجمالي مع نفس المعاملات
    const countQuery = `
      SELECT COUNT(*) as Total
      FROM UserActions ua
      ${whereClause}
    `;

    const countRequest = pool.request();

    // إضافة نفس المعاملات للاستعلام
    if (userCode) {
      countRequest.input('userCode', sql.NVarChar, userCode);
    }
    if (actionType) {
      countRequest.input('actionType', sql.NVarChar, actionType);
    }
    if (targetTable) {
      countRequest.input('targetTable', sql.NVarChar, targetTable);
    }
    if (startDate) {
      countRequest.input('startDate', sql.DateTime, new Date(startDate));
    }
    if (endDate) {
      countRequest.input('endDate', sql.DateTime, new Date(endDate));
    }
    if (isSuccess !== undefined) {
      countRequest.input('isSuccess', sql.Bit, isSuccess);
    }

    const countResult = await countRequest.query(countQuery);
    const total = countResult.recordset[0].Total;

    return NextResponse.json({
      success: true,
      data: {
        actions: result.recordset,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب إجراءات المستخدم: ' + error.message
    }, { status: 500 });
  }
}

// جلب الإشعارات
async function getNotifications(pool, data) {
  try {
    const {
      userCode,
      isRead,
      notificationType,
      page = 1,
      limit = 20
    } = data;

    let whereClause = 'WHERE 1=1';
    const request = pool.request();

    if (userCode) {
      whereClause += ' AND UserCode = @userCode';
      request.input('userCode', sql.NVarChar, userCode);
    }

    if (isRead !== undefined) {
      whereClause += ' AND IsRead = @isRead';
      request.input('isRead', sql.Bit, isRead);
    }

    if (notificationType) {
      whereClause += ' AND NotificationType = @notificationType';
      request.input('notificationType', sql.NVarChar, notificationType);
    }

    const offset = (page - 1) * limit;
    request.input('offset', sql.Int, offset);
    request.input('limit', sql.Int, limit);

    const query = `
      SELECT
        n.ID,
        n.UserCode,
        n.Title,
        n.Message,
        n.NotificationType,
        n.Priority,
        n.IsRead,
        n.ActionID,
        n.RelatedTable,
        n.RelatedID,
        n.CreatedAt,
        n.ReadAt,
        n.ExpiresAt,
        at.ActionNameAr,
        at.Category
      FROM Notifications n
      LEFT JOIN ActionTypes at ON n.NotificationType = at.ActionCode
      ${whereClause}
      AND (n.ExpiresAt IS NULL OR n.ExpiresAt > GETDATE())
      ORDER BY n.CreatedAt DESC
      OFFSET @offset ROWS
      FETCH NEXT @limit ROWS ONLY
    `;

    const result = await request.query(query);

    // جلب عدد الإشعارات غير المقروءة
    const unreadCountQuery = `
      SELECT COUNT(*) as UnreadCount
      FROM Notifications
      WHERE UserCode = @userCode AND IsRead = 0
      AND (ExpiresAt IS NULL OR ExpiresAt > GETDATE())
    `;

    const unreadResult = await pool.request()
      .input('userCode', sql.NVarChar, userCode)
      .query(unreadCountQuery);

    return NextResponse.json({
      success: true,
      data: {
        notifications: result.recordset,
        unreadCount: unreadResult.recordset[0].UnreadCount
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب الإشعارات: ' + error.message
    }, { status: 500 });
  }
}

// تحديد الإشعار كمقروء
async function markNotificationAsRead(pool, data) {
  try {
    const { notificationId, userCode } = data;

    await pool.request()
      .input('notificationId', sql.Int, notificationId)
      .input('userCode', sql.NVarChar, userCode)
      .query(`
        UPDATE Notifications
        SET IsRead = 1, ReadAt = GETDATE()
        WHERE ID = @notificationId AND UserCode = @userCode
      `);

    return NextResponse.json({
      success: true,
      message: 'تم تحديد الإشعار كمقروء'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث الإشعار: ' + error.message
    }, { status: 500 });
  }
}

// جلب إحصائيات الإجراءات
async function getActionStatistics(pool, data) {
  try {
    const { userCode, startDate, endDate } = data;

    let whereClause = 'WHERE 1=1';
    const request = pool.request();

    if (userCode) {
      whereClause += ' AND UserCode = @userCode';
      request.input('userCode', sql.NVarChar, userCode);
    }

    if (startDate) {
      whereClause += ' AND ActionDate >= @startDate';
      request.input('startDate', sql.DateTime, new Date(startDate));
    }

    if (endDate) {
      whereClause += ' AND ActionDate <= @endDate';
      request.input('endDate', sql.DateTime, new Date(endDate));
    }

    // إحصائيات عامة
    const generalStatsQuery = `
      SELECT
        COUNT(*) as TotalActions,
        COUNT(CASE WHEN IsSuccess = 1 THEN 1 END) as SuccessfulActions,
        COUNT(CASE WHEN IsSuccess = 0 THEN 1 END) as FailedActions,
        COUNT(DISTINCT UserCode) as UniqueUsers,
        COUNT(DISTINCT CAST(ActionDate AS DATE)) as ActiveDays
      FROM UserActions
      ${whereClause}
    `;

    const generalStatsResult = await request.query(generalStatsQuery);

    // إحصائيات حسب نوع الإجراء (استعلامات منفصلة)
    const typeStatsRequest = pool.request();
    if (userCode) {
      typeStatsRequest.input('userCode', sql.NVarChar, userCode);
    }
    if (startDate) {
      typeStatsRequest.input('startDate', sql.DateTime, new Date(startDate));
    }
    if (endDate) {
      typeStatsRequest.input('endDate', sql.DateTime, new Date(endDate));
    }

    const typeStatsQuery = `
      SELECT
        SUM(CASE WHEN ActionType LIKE '%LOGIN%' THEN 1 ELSE 0 END) as LoginActions,
        SUM(CASE WHEN ActionType LIKE '%EMPLOYEE%' THEN 1 ELSE 0 END) as EmployeeActions,
        SUM(CASE WHEN ActionType LIKE '%APARTMENT%' THEN 1 ELSE 0 END) as ApartmentActions,
        SUM(CASE WHEN ActionType LIKE '%CAR%' THEN 1 ELSE 0 END) as CarActions,
        SUM(CASE WHEN ActionType LIKE '%DOCUMENT%' THEN 1 ELSE 0 END) as DocumentActions
      FROM UserActions
      ${whereClause}
    `;

    const typeStatsResult = await typeStatsRequest.query(typeStatsQuery);

    // إحصائيات زمنية (استعلامات منفصلة)
    const timeStatsRequest = pool.request();
    if (userCode) {
      timeStatsRequest.input('userCode', sql.NVarChar, userCode);
    }
    if (startDate) {
      timeStatsRequest.input('startDate', sql.DateTime, new Date(startDate));
    }
    if (endDate) {
      timeStatsRequest.input('endDate', sql.DateTime, new Date(endDate));
    }

    const timeStatsQuery = `
      SELECT
        SUM(CASE WHEN ActionDate >= DATEADD(DAY, -1, GETDATE()) THEN 1 ELSE 0 END) as ActionsLast24Hours,
        SUM(CASE WHEN ActionDate >= DATEADD(DAY, -7, GETDATE()) THEN 1 ELSE 0 END) as ActionsLastWeek,
        SUM(CASE WHEN ActionDate >= DATEADD(MONTH, -1, GETDATE()) THEN 1 ELSE 0 END) as ActionsLastMonth
      FROM UserActions
      ${whereClause}
    `;

    const timeStatsResult = await timeStatsRequest.query(timeStatsQuery);

    // دمج النتائج
    const combinedStats = {
      ...generalStatsResult.recordset[0],
      ...typeStatsResult.recordset[0],
      ...timeStatsResult.recordset[0]
    };

    // إحصائيات حسب المستخدم
    const userStatsQuery = `
      SELECT TOP 10
        UserCode,
        UserName,
        COUNT(*) as ActionCount,
        COUNT(CASE WHEN IsSuccess = 1 THEN 1 END) as SuccessCount,
        COUNT(CASE WHEN IsSuccess = 0 THEN 1 END) as FailureCount,
        MAX(ActionDate) as LastAction
      FROM UserActions
      ${whereClause}
      GROUP BY UserCode, UserName
      ORDER BY ActionCount DESC
    `;

    // إنشاء request منفصل للإحصائيات حسب المستخدم
    const userStatsRequest = pool.request();
    if (userCode) {
      userStatsRequest.input('userCode', sql.NVarChar, userCode);
    }
    if (startDate) {
      userStatsRequest.input('startDate', sql.DateTime, new Date(startDate));
    }
    if (endDate) {
      userStatsRequest.input('endDate', sql.DateTime, new Date(endDate));
    }

    const userStatsResult = await userStatsRequest.query(userStatsQuery);

    // إحصائيات حسب نوع الإجراء
    const actionTypeStatsQuery = `
      SELECT
        ua.ActionType,
        at.ActionNameAr,
        at.Category,
        COUNT(*) as ActionCount,
        COUNT(CASE WHEN ua.IsSuccess = 1 THEN 1 END) as SuccessCount,
        COUNT(CASE WHEN ua.IsSuccess = 0 THEN 1 END) as FailureCount
      FROM UserActions ua
      LEFT JOIN ActionTypes at ON ua.ActionType = at.ActionCode
      ${whereClause}
      GROUP BY ua.ActionType, at.ActionNameAr, at.Category
      ORDER BY ActionCount DESC
    `;

    // إنشاء request منفصل للإحصائيات حسب نوع الإجراء
    const actionTypeStatsRequest = pool.request();
    if (userCode) {
      actionTypeStatsRequest.input('userCode', sql.NVarChar, userCode);
    }
    if (startDate) {
      actionTypeStatsRequest.input('startDate', sql.DateTime, new Date(startDate));
    }
    if (endDate) {
      actionTypeStatsRequest.input('endDate', sql.DateTime, new Date(endDate));
    }

    const actionTypeStatsResult = await actionTypeStatsRequest.query(actionTypeStatsQuery);

    return NextResponse.json({
      success: true,
      data: {
        general: combinedStats,
        topUsers: userStatsResult.recordset,
        actionTypes: actionTypeStatsResult.recordset
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب إحصائيات الإجراءات: ' + error.message
    }, { status: 500 });
  }
}

// جلب الإجراءات الحديثة
async function getRecentActions(pool, data) {
  try {
    const { limit = 10, userCode } = data;

    let whereClause = 'WHERE 1=1';
    const request = pool.request();

    if (userCode) {
      whereClause += ' AND UserCode = @userCode';
      request.input('userCode', sql.NVarChar, userCode);
    }

    request.input('limit', sql.Int, limit);

    const query = `
      SELECT TOP (@limit)
        ua.ID,
        ua.UserCode,
        ua.UserName,
        ua.ActionType,
        ua.ActionDescription,
        ua.TargetTable,
        ua.TargetID,
        ua.ActionDate,
        ua.IsSuccess,
        at.ActionNameAr,
        at.Category
      FROM UserActions ua
      LEFT JOIN ActionTypes at ON ua.ActionType = at.ActionCode
      ${whereClause}
      ORDER BY ua.ActionDate DESC
    `;

    const result = await request.query(query);

    return NextResponse.json({
      success: true,
      data: result.recordset
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب الإجراءات الحديثة: ' + error.message
    }, { status: 500 });
  }
}
