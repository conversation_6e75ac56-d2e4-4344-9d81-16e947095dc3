-- ===================================
-- جداول إدارة الإجازات المحسنة
-- ===================================

-- 1. جدول رصيد الإجازات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LeaveBalances' AND xtype='U')
BEGIN
    CREATE TABLE LeaveBalances (
        ID INT IDENTITY(1,1) PRIMARY KEY,
        EmployeeCode NVARCHAR(20) NOT NULL UNIQUE,
        EmployeeName NVARCHAR(100) NOT NULL,
        JobTitle NVARCHAR(100) NOT NULL,
        Department NVARCHAR(100),
        RegularBalance INT DEFAULT 15,  -- رصيد إعتيادي (15 يوم)
        CasualBalance INT DEFAULT 6,   -- رصيد عارضة (6 أيام)
        UsedRegular INT DEFAULT 0,     -- المستخدم من الإعتيادي
        UsedCasual INT DEFAULT 0,      -- المستخدم من العارضة
        RemainingRegular AS (RegularBalance - UsedRegular),  -- المتبقي إعتيادي
        RemainingCasual AS (CasualBalance - UsedCasual),     -- المتبقي عارضة
        Year INT DEFAULT YEAR(GETDATE()),
        LastLeaveDate DATE,            -- تاريخ آخر إجازة
        CreatedAt DATETIME DEFAULT GETDATE(),
        UpdatedAt DATETIME DEFAULT GETDATE()
    )
    
    -- إنشاء الفهارس
    CREATE INDEX IX_LeaveBalances_EmployeeCode ON LeaveBalances(EmployeeCode)
    CREATE INDEX IX_LeaveBalances_Year ON LeaveBalances(Year)
END

-- 2. جدول طلبات الإجازات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LeaveRequests' AND xtype='U')
BEGIN
    CREATE TABLE LeaveRequests (
        ID INT IDENTITY(1,1) PRIMARY KEY,
        EmployeeCode NVARCHAR(20) NOT NULL,
        EmployeeName NVARCHAR(100) NOT NULL,
        JobTitle NVARCHAR(100) NOT NULL,
        Department NVARCHAR(100),
        Project NVARCHAR(100),
        LeaveType NVARCHAR(50) NOT NULL, -- اعتيادية، عارضة، مرضية، أمومة، أبوة، بدل، بدون راتب
        StartDate DATE NOT NULL,
        EndDate DATE NOT NULL,
        DaysCount INT NOT NULL,
        Reason NVARCHAR(MAX),
        Status NVARCHAR(20) DEFAULT N'قيد المراجعة', -- قيد المراجعة، معتمد، مرفوض
        RequestDate DATE DEFAULT GETDATE(),
        
        -- التوقيعات
        EmployeeSignature NVARCHAR(100),
        DirectManagerSignature NVARCHAR(100),
        ProjectManagerSignature NVARCHAR(100),
        HRManagerSignature NVARCHAR(100),
        
        -- تواريخ الاعتماد
        DirectManagerApprovalDate DATE,
        ProjectManagerApprovalDate DATE,
        HRManagerApprovalDate DATE,
        
        -- ملاحظات
        Notes NVARCHAR(MAX),
        HRNotes NVARCHAR(MAX),
        
        CreatedAt DATETIME DEFAULT GETDATE(),
        UpdatedAt DATETIME DEFAULT GETDATE()
    )
    
    -- إنشاء الفهارس
    CREATE INDEX IX_LeaveRequests_EmployeeCode ON LeaveRequests(EmployeeCode)
    CREATE INDEX IX_LeaveRequests_Status ON LeaveRequests(Status)
    CREATE INDEX IX_LeaveRequests_LeaveType ON LeaveRequests(LeaveType)
    CREATE INDEX IX_LeaveRequests_Dates ON LeaveRequests(StartDate, EndDate)
    CREATE INDEX IX_LeaveRequests_RequestDate ON LeaveRequests(RequestDate)
END

-- 3. إدراج بيانات رصيد الإجازات للموظفين الموجودين
INSERT INTO LeaveBalances (EmployeeCode, EmployeeName, JobTitle, Department)
SELECT DISTINCT
    e.EmployeeCode,
    e.EmployeeName,
    e.JobTitle,
    e.Department
FROM Employees e
WHERE e.EmployeeCode IS NOT NULL
AND e.EmployeeCode != ''
AND NOT EXISTS (
    SELECT 1 FROM LeaveBalances lb
    WHERE lb.EmployeeCode = e.EmployeeCode
)

-- 4. تحديث تاريخ آخر إجازة لكل موظف
UPDATE lb
SET LastLeaveDate = (
    SELECT MAX(lr.EndDate)
    FROM LeaveRequests lr
    WHERE lr.EmployeeCode = lb.EmployeeCode
        AND lr.Status = N'معتمد'
)
FROM LeaveBalances lb

-- 5. تحديث الرصيد المستخدم
UPDATE lb
SET 
    UsedRegular = ISNULL((
        SELECT SUM(lr.DaysCount)
        FROM LeaveRequests lr
        WHERE lr.EmployeeCode = lb.EmployeeCode
            AND lr.LeaveType = N'اعتيادية'
            AND lr.Status = N'معتمد'
            AND YEAR(lr.StartDate) = lb.Year
    ), 0),
    UsedCasual = ISNULL((
        SELECT SUM(lr.DaysCount)
        FROM LeaveRequests lr
        WHERE lr.EmployeeCode = lb.EmployeeCode
            AND lr.LeaveType = N'عارضة'
            AND lr.Status = N'معتمد'
            AND YEAR(lr.StartDate) = lb.Year
    ), 0)
FROM LeaveBalances lb

-- 6. إنشاء trigger لتحديث الرصيد تلقائياً عند اعتماد الإجازة
IF NOT EXISTS (SELECT * FROM sys.triggers WHERE name = 'TR_UpdateLeaveBalance')
BEGIN
    EXEC('
    CREATE TRIGGER TR_UpdateLeaveBalance
    ON LeaveRequests
    AFTER UPDATE
    AS
    BEGIN
        SET NOCOUNT ON
        
        -- تحديث الرصيد عند تغيير حالة الطلب إلى معتمد
        IF UPDATE(Status)
        BEGIN
            UPDATE lb
            SET 
                UsedRegular = ISNULL((
                    SELECT SUM(lr.DaysCount)
                    FROM LeaveRequests lr
                    WHERE lr.EmployeeCode = lb.EmployeeCode
                        AND lr.LeaveType = N''اعتيادية''
                        AND lr.Status = N''معتمد''
                        AND YEAR(lr.StartDate) = lb.Year
                ), 0),
                UsedCasual = ISNULL((
                    SELECT SUM(lr.DaysCount)
                    FROM LeaveRequests lr
                    WHERE lr.EmployeeCode = lb.EmployeeCode
                        AND lr.LeaveType = N''عارضة''
                        AND lr.Status = N''معتمد''
                        AND YEAR(lr.StartDate) = lb.Year
                ), 0),
                LastLeaveDate = (
                    SELECT MAX(lr.EndDate)
                    FROM LeaveRequests lr
                    WHERE lr.EmployeeCode = lb.EmployeeCode
                        AND lr.Status = N''معتمد''
                ),
                UpdatedAt = GETDATE()
            FROM LeaveBalances lb
            INNER JOIN inserted i ON lb.EmployeeCode = i.EmployeeCode
            WHERE i.Status = N''معتمد''
        END
    END
    ')
END

-- 7. إنشاء view لعرض بيانات الإجازات مع الرصيد
IF NOT EXISTS (SELECT * FROM sys.views WHERE name = 'VW_LeaveRequestsWithBalance')
BEGIN
    EXEC('
    CREATE VIEW VW_LeaveRequestsWithBalance
    AS
    SELECT 
        lr.*,
        lb.RegularBalance,
        lb.CasualBalance,
        lb.UsedRegular,
        lb.UsedCasual,
        lb.RemainingRegular,
        lb.RemainingCasual,
        lb.LastLeaveDate,
        (lb.RemainingRegular + lb.RemainingCasual) as TotalRemaining
    FROM LeaveRequests lr
    LEFT JOIN LeaveBalances lb ON lr.EmployeeCode = lb.EmployeeCode
    ')
END

-- 8. إنشاء stored procedure لإضافة طلب إجازة جديد
IF NOT EXISTS (SELECT * FROM sys.procedures WHERE name = 'SP_CreateLeaveRequest')
BEGIN
    EXEC('
    CREATE PROCEDURE SP_CreateLeaveRequest
        @EmployeeCode NVARCHAR(20),
        @LeaveType NVARCHAR(50),
        @StartDate DATE,
        @EndDate DATE,
        @DaysCount INT,
        @Reason NVARCHAR(MAX),
        @Project NVARCHAR(100) = NULL
    AS
    BEGIN
        SET NOCOUNT ON
        
        DECLARE @EmployeeName NVARCHAR(100)
        DECLARE @JobTitle NVARCHAR(100)
        DECLARE @Department NVARCHAR(100)
        
        -- جلب بيانات الموظف
        SELECT
            @EmployeeName = EmployeeName,
            @JobTitle = JobTitle,
            @Department = Department
        FROM Employees
        WHERE EmployeeCode = @EmployeeCode
        
        IF @EmployeeName IS NULL
        BEGIN
            RAISERROR(N''كود الموظف غير موجود'', 16, 1)
            RETURN
        END
        
        -- التحقق من الرصيد المتاح
        DECLARE @RemainingBalance INT
        
        IF @LeaveType = N''اعتيادية''
        BEGIN
            SELECT @RemainingBalance = RemainingRegular
            FROM LeaveBalances
            WHERE EmployeeCode = @EmployeeCode
        END
        ELSE IF @LeaveType = N''عارضة''
        BEGIN
            SELECT @RemainingBalance = RemainingCasual
            FROM LeaveBalances
            WHERE EmployeeCode = @EmployeeCode
        END
        
        IF @RemainingBalance < @DaysCount AND @LeaveType IN (N''اعتيادية'', N''عارضة'')
        BEGIN
            RAISERROR(N''الرصيد المتاح غير كافي'', 16, 1)
            RETURN
        END
        
        -- إدراج طلب الإجازة
        INSERT INTO LeaveRequests (
            EmployeeCode, EmployeeName, JobTitle, Department, Project,
            LeaveType, StartDate, EndDate, DaysCount, Reason
        )
        VALUES (
            @EmployeeCode, @EmployeeName, @JobTitle, @Department, @Project,
            @LeaveType, @StartDate, @EndDate, @DaysCount, @Reason
        )
        
        SELECT SCOPE_IDENTITY() as NewRequestID
    END
    ')
END

PRINT 'تم إنشاء/تحديث جداول إدارة الإجازات بنجاح'
