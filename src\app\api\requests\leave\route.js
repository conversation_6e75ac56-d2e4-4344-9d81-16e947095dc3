import { NextResponse } from 'next/server';
import sql from 'mssql';

// إعدادات قاعدة البيانات
const config = {
  server: process.env.DB_SERVER || 'localhost',
  database: process.env.DB_NAME || 'EmployeeDB',
  user: process.env.DB_USER || 'sa',
  password: process.env.DB_PASSWORD || 'YourPassword123',
  options: {
    encrypt: false,
    trustServerCertificate: true,
    enableArithAbort: true,
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
};

export async function POST(request) {
  let pool;
  
  try {

    // الحصول على البيانات من الطلب
    const requestData = await request.json();

    // التحقق من الحقول المطلوبة
    const requiredFields = [
      'employeeName', 'leaveType', 'startDate', 'endDate', 'reason'
    ];

    const missingFields = requiredFields.filter(field => 
      !requestData[field] || requestData[field].trim() === ''
    );

    if (missingFields.length > 0) {
      return NextResponse.json({
        success: false,
        message: `الحقول التالية مطلوبة: ${missingFields.join(', ')}`
      }, { status: 400 });
    }

    // التحقق من صحة التواريخ
    const startDate = new Date(requestData.startDate);
    const endDate = new Date(requestData.endDate);
    
    if (endDate < startDate) {
      return NextResponse.json({
        success: false,
        message: 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية'
      }, { status: 400 });
    }

    // الاتصال بقاعدة البيانات
    pool = await sql.connect(config);

    // استخدام جدول PaperRequests الموحد بدلاً من إنشاء جدول منفصل

        CreatedAt datetime DEFAULT GETDATE(),
        UpdatedAt datetime DEFAULT GETDATE()
      )
    `);

    // إنشاء طلب الإدراج
    const insertRequest = pool.request();
    
    // إضافة المعاملات
    insertRequest.input('EmployeeName', sql.NVarChar, requestData.employeeName);
    insertRequest.input('EmployeeID', sql.NVarChar, requestData.employeeId || null);
    insertRequest.input('Department', sql.NVarChar, requestData.department || null);
    insertRequest.input('JobTitle', sql.NVarChar, requestData.jobTitle || null);
    insertRequest.input('LeaveType', sql.NVarChar, requestData.leaveType);
    insertRequest.input('StartDate', sql.Date, startDate);
    insertRequest.input('EndDate', sql.Date, endDate);
    insertRequest.input('TotalDays', sql.Int, parseInt(requestData.totalDays) || null);
    insertRequest.input('Reason', sql.NVarChar, requestData.reason);
    insertRequest.input('EmergencyContact', sql.NVarChar, requestData.emergencyContact || null);
    insertRequest.input('EmergencyPhone', sql.NVarChar, requestData.emergencyPhone || null);
    insertRequest.input('ReplacementEmployee', sql.NVarChar, requestData.replacementEmployee || null);
    insertRequest.input('Notes', sql.NVarChar, requestData.notes || null);

    // استعلام الإدراج
    const insertQuery = `
      INSERT INTO PaperRequests (
        RequestType, EmployeeName, EmployeeCode, Department, JobTitle, LeaveType,
        StartDate, EndDate, DaysCount, LeaveReason, EmergencyContact,
        EmergencyPhone, ReplacementEmployee, Notes, Status
      ) VALUES (
        'leave', @EmployeeName, @EmployeeID, @Department, @JobTitle, @LeaveType,
        @StartDate, @EndDate, @TotalDays, @Reason, @EmergencyContact,
        @EmergencyPhone, @ReplacementEmployee, @Notes, N'قيد المراجعة'
      );
      SELECT SCOPE_IDENTITY() as RequestID;
    `;

    const result = await insertRequest.query(insertQuery);
    const newRequestId = result.recordset[0].RequestID;

    return NextResponse.json({
      success: true,
      message: 'تم تقديم طلب الإجازة بنجاح',
      requestId: newRequestId,
      data: {
        requestId: newRequestId,
        employeeName: requestData.employeeName,
        leaveType: requestData.leaveType,
        startDate: requestData.startDate,
        endDate: requestData.endDate,
        totalDays: requestData.totalDays,
        status: 'pending'
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'خطأ في معالجة طلب الإجازة',
      error: error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      await pool.close();

    }
  }
}

export async function GET(request) {
  let pool;
  
  try {

    // الاتصال بقاعدة البيانات
    pool = await sql.connect(config);

    // جلب جميع طلبات الإجازة من جدول PaperRequests الموحد
    const result = await pool.request().query(`
      SELECT
        ID as RequestID,
        EmployeeName,
        EmployeeCode as EmployeeID,
        Department,
        JobTitle,
        LeaveType,
        StartDate,
        EndDate,
        DaysCount as TotalDays,
        LeaveReason as Reason,
        EmergencyContact,
        EmergencyPhone,
        ReplacementEmployee,
        Notes,
        Status,
        RequestDate as SubmittedAt,
        ApprovalDate as ReviewedAt,
        ApprovedBy as ReviewedBy,
        ApprovalNotes as ReviewComments
      FROM PaperRequests
      WHERE RequestType = 'leave'
      ORDER BY RequestDate DESC
    `);

    return NextResponse.json({
      success: true,
      requests: result.recordset
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'خطأ في جلب طلبات الإجازة',
      error: error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      await pool.close();

    }
  }
}
