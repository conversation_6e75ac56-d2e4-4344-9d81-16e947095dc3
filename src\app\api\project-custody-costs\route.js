import { NextResponse } from 'next/server';
import { getConnection } from '@/lib/db';
import sql from 'mssql';

// إنشاء جداول النظام المدمج للتكاليف والعُهد
async function ensureIntegratedCustodySystem(pool) {
  try {

    // 1. جدول العُهد المستديمة
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PermanentCustody' AND xtype='U')
      BEGIN
        CREATE TABLE PermanentCustody (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          CustodyNumber NVARCHAR(50) NOT NULL UNIQUE,
          CustodianName NVARCHAR(200) NOT NULL,
          CustodianID NVARCHAR(50),
          Department NVARCHAR(100),
          InitialAmount DECIMAL(18,2) NOT NULL,
          CurrentBalance DECIMAL(18,2) NOT NULL,
          TotalSpent DECIMAL(18,2) DEFAULT 0,
          TotalSettled DECIMAL(18,2) DEFAULT 0,
          Status NVARCHAR(50) DEFAULT N'نشطة', -- نشطة، مُعلقة، مُغلقة
          IssueDate DATE NOT NULL,
          LastTransactionDate DATE,
          Notes NVARCHAR(MAX),
          IsActive BIT DEFAULT 1,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),
          CreatedBy NVARCHAR(100) DEFAULT 'System'
        )
      END
    `);

    // 2. جدول العُهد المؤقتة
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='TemporaryCustody' AND xtype='U')
      BEGIN
        CREATE TABLE TemporaryCustody (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          PermanentCustodyID INT,
          RequestNumber NVARCHAR(50) NOT NULL UNIQUE,
          Purpose NVARCHAR(500) NOT NULL,
          RequestedAmount DECIMAL(18,2) NOT NULL,
          ApprovedAmount DECIMAL(18,2),
          SpentAmount DECIMAL(18,2) DEFAULT 0,
          RemainingAmount DECIMAL(18,2) DEFAULT 0,
          Status NVARCHAR(50) DEFAULT N'قيد المراجعة', -- قيد المراجعة، معتمدة، مرفوضة، مُسوّاة
          RequestDate DATE NOT NULL,
          ApprovalDate DATE,
          SettlementDate DATE,
          SettlementNumber NVARCHAR(50),
          Notes NVARCHAR(MAX),
          IsActive BIT DEFAULT 1,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),
          CreatedBy NVARCHAR(100) DEFAULT 'System',
          ApprovedBy NVARCHAR(100),
          SettledBy NVARCHAR(100),
          FOREIGN KEY (PermanentCustodyID) REFERENCES PermanentCustody(ID)
        )
      END
    `);

    // 3. جدول التكاليف المدمج
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='IntegratedCosts' AND xtype='U')
      BEGIN
        CREATE TABLE IntegratedCosts (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          CustodyType NVARCHAR(20) NOT NULL, -- مستديمة، مؤقتة
          PermanentCustodyID INT,
          TemporaryCustodyID INT,
          MainCategoryID INT NOT NULL,
          SubCategoryID INT,
          CostDescription NVARCHAR(500) NOT NULL,
          Amount DECIMAL(18,2) NOT NULL,
          CostDate DATE NOT NULL,
          ReceiptNumber NVARCHAR(100),
          Status NVARCHAR(50) DEFAULT N'قيد المراجعة', -- قيد المراجعة، معلّقة، مرفوضة، تم التسوية
          SettlementNumber NVARCHAR(50),
          SettlementDate DATE,
          DaysOverdue INT DEFAULT 0,
          Notes NVARCHAR(MAX),
          AttachmentPath NVARCHAR(500),
          IsActive BIT DEFAULT 1,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),
          CreatedBy NVARCHAR(100) DEFAULT 'System',
          ApprovedBy NVARCHAR(100),
          ApprovedAt DATETIME,
          FOREIGN KEY (PermanentCustodyID) REFERENCES PermanentCustody(ID),
          FOREIGN KEY (TemporaryCustodyID) REFERENCES TemporaryCustody(ID)
        )
      END
    `);

    // 4. جدول تصنيفات التكاليف
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CostCategories' AND xtype='U')
      BEGIN
        CREATE TABLE CostCategories (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          ParentID INT NULL,
          CategoryName NVARCHAR(200) NOT NULL,
          CategoryCode NVARCHAR(50) UNIQUE,
          Description NVARCHAR(500),
          CustodyType NVARCHAR(20) DEFAULT N'مستديمة', -- مستديمة، مؤقتة، مشتركة
          IsActive BIT DEFAULT 1,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),
          FOREIGN KEY (ParentID) REFERENCES CostCategories(ID)
        )
      END
    `);

    // إضافة عمود CustodyType إذا لم يكن موجوداً
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('CostCategories') AND name = 'CustodyType')
      BEGIN
        ALTER TABLE CostCategories ADD CustodyType NVARCHAR(20) DEFAULT N'مستديمة'
      END
    `);

    // 5. جدول سجل التسويات
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SettlementHistory' AND xtype='U')
      BEGIN
        CREATE TABLE SettlementHistory (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          CostID INT NOT NULL,
          SettlementNumber NVARCHAR(50) NOT NULL,
          SettlementType NVARCHAR(50) NOT NULL, -- تسوية_مؤقتة، إرجاع_مستديمة، إقفال_نهائي
          PreviousStatus NVARCHAR(50),
          NewStatus NVARCHAR(50),
          Amount DECIMAL(18,2) NOT NULL,
          SettlementDate DATE NOT NULL,
          ProcessedBy NVARCHAR(100),
          Notes NVARCHAR(MAX),
          CreatedAt DATETIME DEFAULT GETDATE(),
          FOREIGN KEY (CostID) REFERENCES IntegratedCosts(ID)
        )
      END
    `);

    // 6. جدول الأرشيف للملفات
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CustodyArchive' AND xtype='U')
      BEGIN
        CREATE TABLE CustodyArchive (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          CostID INT NOT NULL,
          CustodyType NVARCHAR(20) NOT NULL, -- مستديمة، مؤقتة
          SettlementNumber NVARCHAR(50),
          SequentialNumber INT, -- للعُهد المؤقتة
          FileName NVARCHAR(255) NOT NULL,
          FilePath NVARCHAR(500) NOT NULL,
          FileSize BIGINT,
          UploadedBy NVARCHAR(100),
          UploadedAt DATETIME DEFAULT GETDATE(),
          IsActive BIT DEFAULT 1,
          FOREIGN KEY (CostID) REFERENCES IntegratedCosts(ID)
        )
      END
    `);

    // 7. جدول العداد للعُهد المؤقتة
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='TemporaryCustodyCounter' AND xtype='U')
      BEGIN
        CREATE TABLE TemporaryCustodyCounter (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          CurrentCounter INT DEFAULT 0,
          LastUpdated DATETIME DEFAULT GETDATE()
        )

        -- إدراج العداد الأولي
        INSERT INTO TemporaryCustodyCounter (CurrentCounter) VALUES (0)
      END
    `);

    // إدراج البيانات الأساسية
    await insertBasicData(pool);

    return true;

  } catch (error) {

    throw error;
  }
}

// إدراج البيانات الأساسية
async function insertBasicData(pool) {
  try {

    // لا نحذف البنود الموجودة لتجنب كسر المراجع

    // إدراج التصنيفات الرئيسية للعُهدة المستديمة (مع فحص عدم التكرار)
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM CostCategories WHERE CategoryCode = 'MEALS')
      BEGIN
        INSERT INTO CostCategories (CategoryName, CategoryCode, Description, CustodyType)
        VALUES
          (N'الوجبات', 'MEALS', N'جميع أنواع الوجبات', N'مستديمة'),
          (N'مشروبات وأدوات نظافة', 'BEVERAGES_CLEANING', N'مشروبات وأدوات نظافة', N'مستديمة'),
          (N'الانتقالات', 'TRANSPORTATION', N'جميع أنواع الانتقالات', N'مستديمة'),
          (N'مشتريات', 'PURCHASES', N'المشتريات المختلفة', N'مستديمة'),
          (N'الشقق', 'APARTMENTS', N'خدمات وصيانة الشقق', N'مستديمة'),
          (N'السيارات', 'CARS', N'خدمات وصيانة السيارات', N'مستديمة'),
          (N'خدمات', 'SERVICES', N'الخدمات المختلفة', N'مستديمة')
      END
    `);

    // إدراج التصنيفات الرئيسية للعُهدة المؤقتة (بدون بنود فرعية)
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM CostCategories WHERE CategoryCode = 'TEMP_NEW_APARTMENT_RENT')
      BEGIN
        INSERT INTO CostCategories (CategoryName, CategoryCode, Description, CustodyType)
        VALUES
          (N'إيجار شقة جديدة', 'TEMP_NEW_APARTMENT_RENT', N'إيجار شقة جديدة للموظفين', N'مؤقتة'),
          (N'عمل اختبارات', 'TEMP_TESTS', N'إجراء اختبارات مختلفة', N'مؤقتة'),
          (N'أخرى', 'TEMP_OTHER', N'مصروفات أخرى للعُهدة المؤقتة (يرجى تحديد التفاصيل في الملاحظات)', N'مؤقتة')
      END
    `);

    // إدراج التصنيفات الفرعية الجديدة (مع فحص عدم التكرار)
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM CostCategories WHERE CategoryCode = 'MEALS_CONCORD')
      BEGIN
        INSERT INTO CostCategories (ParentID, CategoryName, CategoryCode, Description)
        SELECT
          p.ID,
          sub.CategoryName,
          sub.CategoryCode,
          sub.Description
        FROM (VALUES
        -- 1- الوجبات (5 فرعية)
        ('MEALS', N'وجبات كونكورد', 'MEALS_CONCORD', N'وجبات كونكورد'),
        ('MEALS', N'وجبات استشاري', 'MEALS_CONSULTANT', N'وجبات استشاري'),
        ('MEALS', N'وجبات مالك', 'MEALS_OWNER', N'وجبات مالك'),
        ('MEALS', N'وجبات شرطة عسكرية', 'MEALS_MILITARY_POLICE', N'وجبات شرطة عسكرية'),
        ('MEALS', N'وجبات قاعة الاجتماعات', 'MEALS_MEETING_ROOM', N'وجبات قاعة الاجتماعات'),

        -- 2- مشروبات وأدوات نظافة (4 فرعية)
        ('BEVERAGES_CLEANING', N'كونكورد', 'BEVERAGES_CONCORD', N'مشروبات وأدوات نظافة كونكورد'),
        ('BEVERAGES_CLEANING', N'استشاري', 'BEVERAGES_CONSULTANT', N'مشروبات وأدوات نظافة استشاري'),
        ('BEVERAGES_CLEANING', N'مالك', 'BEVERAGES_OWNER', N'مشروبات وأدوات نظافة مالك'),
        ('BEVERAGES_CLEANING', N'قاعة الاجتماعات', 'BEVERAGES_MEETING_ROOM', N'مشروبات وأدوات نظافة قاعة الاجتماعات'),

        -- 3- الانتقالات (4 فرعية)
        ('TRANSPORTATION', N'انتقالات مأمورية', 'TRANSPORT_MISSION', N'انتقالات مأمورية'),
        ('TRANSPORTATION', N'انتقالات بالخصم', 'TRANSPORT_DEDUCTION', N'انتقالات بالخصم'),
        ('TRANSPORTATION', N'انتقالات استشاري', 'TRANSPORT_CONSULTANT', N'انتقالات استشاري'),
        ('TRANSPORTATION', N'انتقالات بدل', 'TRANSPORT_ALLOWANCE', N'انتقالات بدل'),

        -- 4- مشتريات (3 فرعية)
        ('PURCHASES', N'مشتريات كونكورد', 'PURCHASES_CONCORD', N'مشتريات كونكورد'),
        ('PURCHASES', N'مشتريات استشاري', 'PURCHASES_CONSULTANT', N'مشتريات استشاري'),
        ('PURCHASES', N'مشتريات مالك', 'PURCHASES_OWNER', N'مشتريات مالك'),

        -- 5- الشقق (3 فرعية)
        ('APARTMENTS', N'صيانة', 'APARTMENTS_MAINTENANCE', N'صيانة الشقق'),
        ('APARTMENTS', N'خدمات', 'APARTMENTS_SERVICES', N'خدمات الشقق'),
        ('APARTMENTS', N'أصول', 'APARTMENTS_ASSETS', N'أصول الشقق'),

        -- 6- السيارات (7 فرعية)
        ('CARS', N'جاز بالخصم', 'CARS_FUEL_DEDUCTION', N'جاز بالخصم'),
        ('CARS', N'كارتات', 'CARS_CARDS', N'كارتات'),
        ('CARS', N'جراج', 'CARS_GARAGE', N'جراج'),
        ('CARS', N'ميزان', 'CARS_SCALE', N'ميزان'),
        ('CARS', N'مخالفة', 'CARS_VIOLATION', N'مخالفة'),
        ('CARS', N'إكرامية', 'CARS_TIP', N'إكرامية'),
        ('CARS', N'صيانة', 'CARS_MAINTENANCE', N'صيانة السيارات'),

        -- 7- خدمات (7 فرعية)
        ('SERVICES', N'إنترنت كونكورد', 'SERVICES_INTERNET_CONCORD', N'إنترنت كونكورد'),
        ('SERVICES', N'إنترنت مالك', 'SERVICES_INTERNET_OWNER', N'إنترنت مالك'),
        ('SERVICES', N'إنترنت استشاري', 'SERVICES_INTERNET_CONSULTANT', N'إنترنت استشاري'),
        ('SERVICES', N'مياه', 'SERVICES_WATER', N'مياه'),
        ('SERVICES', N'كهرباء', 'SERVICES_ELECTRICITY', N'كهرباء'),
        ('SERVICES', N'نقل', 'SERVICES_TRANSPORT', N'نقل'),
        ('SERVICES', N'إكرامية', 'SERVICES_TIP', N'إكرامية')
      ) AS sub(ParentCode, CategoryName, CategoryCode, Description)
      INNER JOIN CostCategories p ON p.CategoryCode = sub.ParentCode
      END
    `);

    // إنشاء/تحديث العُهدة المستديمة الرئيسية
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM PermanentCustody WHERE CustodyNumber = '1409')
      BEGIN
        INSERT INTO PermanentCustody (
          CustodyNumber, CustodianName, CustodianID, Department,
          InitialAmount, CurrentBalance, IssueDate, Notes
        )
        VALUES (
          '1409', N'ايمن يونس عبد الحميد', 'EMP001', N'الإدارة العامة',
          240000.00, 240000.00, GETDATE(), N'العُهدة المستديمة الرئيسية - دائرة مغلقة'
        )
      END
      ELSE
      BEGIN
        UPDATE PermanentCustody
        SET CustodianName = N'ايمن يونس عبد الحميد',
            InitialAmount = 240000.00,
            CurrentBalance = 240000.00,
            Notes = N'العُهدة المستديمة الرئيسية - دائرة مغلقة - تم التحديث',
            UpdatedAt = GETDATE()
        WHERE CustodyNumber = '1409' OR CustodyNumber = '1450'
      END
    `);

  } catch (error) {

  }
}

// إنشاء تكلفة جديدة مع الذكاء الاصطناعي
async function createSmartCost(pool, data) {
  try {

    const {
      custodyType, // مستديمة أو مؤقتة
      permanentCustodyNumber, // رقم العهدة المستديمة (إذا كانت مستديمة)
      settlementNumber, // رقم التسوية للعُهدة المستديمة
      purpose, // الغرض/الوصف
      mainCategoryId,
      subCategoryId,
      amount,
      receiptNumber,
      costDate, // تاريخ التكلفة
      notes
    } = data;

    let permanentCustodyId = null;
    let temporaryCustodyId = null;

    // التعامل مع العُهدة المستديمة
    if (custodyType === 'مستديمة' && permanentCustodyNumber) {
      const permanentCustody = await pool.request()
        .input('custodyNumber', sql.NVarChar, permanentCustodyNumber)
        .query(`
          SELECT ID, CurrentBalance, Status
          FROM PermanentCustody
          WHERE CustodyNumber = @custodyNumber AND IsActive = 1
        `);

      if (permanentCustody.recordset.length === 0) {
        throw new Error('العُهدة المستديمة غير موجودة أو غير نشطة');
      }

      const custody = permanentCustody.recordset[0];
      permanentCustodyId = custody.ID;

      // التحقق من الرصيد
      if (custody.CurrentBalance < amount) {
        throw new Error(`الرصيد المتاح (${custody.CurrentBalance}) غير كافي للمبلغ المطلوب (${amount})`);
      }

      // خصم المبلغ من العُهدة المستديمة مؤقتاً
      await pool.request()
        .input('custodyId', sql.Int, permanentCustodyId)
        .input('amount', sql.Decimal(18,2), amount)
        .query(`
          UPDATE PermanentCustody
          SET CurrentBalance = CurrentBalance - @amount,
              TotalSpent = TotalSpent + @amount,
              LastTransactionDate = GETDATE(),
              UpdatedAt = GETDATE()
          WHERE ID = @custodyId
        `);

    } else if (custodyType === 'مؤقتة') {
      // الحصول على الرقم التسلسلي التالي للعُهدة المؤقتة
      const counterResult = await pool.request().query(`
        UPDATE TemporaryCustodyCounter
        SET CurrentCounter = CurrentCounter + 1,
            LastUpdated = GETDATE()
        OUTPUT INSERTED.CurrentCounter
      `);

      const sequentialNumber = counterResult.recordset[0].CurrentCounter;
      const requestNumber = `TEMP-${sequentialNumber.toString().padStart(4, '0')}`;

      const tempCustodyResult = await pool.request()
        .input('requestNumber', sql.NVarChar, requestNumber)
        .input('purpose', sql.NVarChar, purpose)
        .input('requestedAmount', sql.Decimal(18,2), amount)
        .input('approvedAmount', sql.Decimal(18,2), 0) // لم يتم الاعتماد بعد
        .input('spentAmount', sql.Decimal(18,2), 0)
        .input('remainingAmount', sql.Decimal(18,2), 0)
        .query(`
          INSERT INTO TemporaryCustody (
            RequestNumber, Purpose, RequestedAmount, ApprovedAmount,
            SpentAmount, RemainingAmount, Status, RequestDate
          )
          OUTPUT INSERTED.ID
          VALUES (
            @requestNumber, @purpose, @requestedAmount, @approvedAmount,
            @spentAmount, @remainingAmount, N'طلب', GETDATE()
          )
        `);

      temporaryCustodyId = tempCustodyResult.recordset[0].ID;
    }

    // إنشاء سجل التكلفة
    const costResult = await pool.request()
      .input('custodyType', sql.NVarChar, custodyType)
      .input('permanentCustodyId', sql.Int, permanentCustodyId)
      .input('temporaryCustodyId', sql.Int, temporaryCustodyId)
      .input('mainCategoryId', sql.Int, mainCategoryId)
      .input('subCategoryId', sql.Int, subCategoryId)
      .input('costDescription', sql.NVarChar, purpose)
      .input('amount', sql.Decimal(18,2), amount)
      .input('costDate', sql.Date, costDate ? new Date(costDate) : new Date()) // استخدام التاريخ المحدد أو اليوم
      .input('receiptNumber', sql.NVarChar, receiptNumber)
      .input('settlementNumber', sql.NVarChar, settlementNumber) // رقم التسوية
      .input('notes', sql.NVarChar, notes)
      .query(`
        INSERT INTO IntegratedCosts (
          CustodyType, PermanentCustodyID, TemporaryCustodyID,
          MainCategoryID, SubCategoryID, CostDescription, Amount,
          CostDate, ReceiptNumber, SettlementNumber, Status, Notes
        )
        OUTPUT INSERTED.ID
        VALUES (
          @custodyType, @permanentCustodyId, @temporaryCustodyId,
          @mainCategoryId, @subCategoryId, @costDescription, @amount,
          @costDate, @receiptNumber, @settlementNumber, N'قيد المراجعة', @notes
        )
      `);

    const costId = costResult.recordset[0].ID;

    return {
      success: true,
      costId: costId,
      custodyType: custodyType,
      permanentCustodyId: permanentCustodyId,
      temporaryCustodyId: temporaryCustodyId,
      message: 'تم إنشاء التكلفة بنجاح'
    };

  } catch (error) {

    throw error;
  }
}

// تسوية التكلفة الذكية
async function settleCostSmart(pool, costId, settlementData) {
  try {

    const { settlementNumber, settlementType, notes } = settlementData;

    // جلب بيانات التكلفة
    const costResult = await pool.request()
      .input('costId', sql.Int, costId)
      .query(`
        SELECT
          ic.*,
          pc.CustodyNumber,
          pc.CurrentBalance as PermanentBalance,
          tc.RequestNumber,
          tc.RemainingAmount as TempRemainingAmount
        FROM IntegratedCosts ic
        LEFT JOIN PermanentCustody pc ON ic.PermanentCustodyID = pc.ID
        LEFT JOIN TemporaryCustody tc ON ic.TemporaryCustodyID = tc.ID
        WHERE ic.ID = @costId AND ic.IsActive = 1
      `);

    if (costResult.recordset.length === 0) {
      throw new Error('التكلفة غير موجودة');
    }

    const cost = costResult.recordset[0];
    const previousStatus = cost.Status;

    // تطبيق منطق التسوية الذكي
    if (cost.CustodyType === 'مؤقتة' && settlementType === 'تم التسوية') {
      // تسوية العُهدة المؤقتة - لا حاجة لإرجاع مبلغ
      await pool.request()
        .input('costId', sql.Int, costId)
        .input('settlementNumber', sql.NVarChar, settlementNumber)
        .query(`
          UPDATE IntegratedCosts
          SET Status = N'تم التسوية',
              SettlementNumber = @settlementNumber,
              SettlementDate = GETDATE(),
              UpdatedAt = GETDATE()
          WHERE ID = @costId
        `);

      // تحديث حالة العُهدة المؤقتة
      if (cost.TemporaryCustodyID) {
        await pool.request()
          .input('tempId', sql.Int, cost.TemporaryCustodyID)
          .input('settlementNumber', sql.NVarChar, settlementNumber)
          .query(`
            UPDATE TemporaryCustody
            SET Status = N'مُسوّاة',
                SettlementDate = GETDATE(),
                SettlementNumber = @settlementNumber,
                UpdatedAt = GETDATE()
            WHERE ID = @tempId
          `);
      }

    } else if (cost.CustodyType === 'مستديمة' && settlementType === 'تم التسوية') {
      // تسوية العُهدة المستديمة - إرجاع المبلغ للرصيد
      await pool.request()
        .input('costId', sql.Int, costId)
        .input('settlementNumber', sql.NVarChar, settlementNumber)
        .query(`
          UPDATE IntegratedCosts
          SET Status = N'تم التسوية',
              SettlementNumber = @settlementNumber,
              SettlementDate = GETDATE(),
              UpdatedAt = GETDATE()
          WHERE ID = @costId
        `);

      // إرجاع المبلغ للعُهدة المستديمة
      if (cost.PermanentCustodyID) {
        await pool.request()
          .input('custodyId', sql.Int, cost.PermanentCustodyID)
          .input('amount', sql.Decimal(18,2), cost.Amount)
          .query(`
            UPDATE PermanentCustody
            SET CurrentBalance = CurrentBalance + @amount,
                TotalSettled = TotalSettled + @amount,
                LastTransactionDate = GETDATE(),
                UpdatedAt = GETDATE()
            WHERE ID = @custodyId
          `);
      }
    }

    // إضافة سجل في تاريخ التسويات
    await pool.request()
      .input('costId', sql.Int, costId)
      .input('settlementNumber', sql.NVarChar, settlementNumber)
      .input('settlementType', sql.NVarChar, settlementType)
      .input('previousStatus', sql.NVarChar, previousStatus)
      .input('newStatus', sql.NVarChar, 'تم التسوية')
      .input('amount', sql.Decimal(18,2), cost.Amount)
      .input('notes', sql.NVarChar, notes)
      .query(`
        INSERT INTO SettlementHistory (
          CostID, SettlementNumber, SettlementType, PreviousStatus,
          NewStatus, Amount, SettlementDate, Notes
        )
        VALUES (
          @costId, @settlementNumber, @settlementType, @previousStatus,
          @newStatus, @amount, GETDATE(), @notes
        )
      `);

    return {
      success: true,
      message: 'تم تسوية التكلفة بنجاح',
      costId: costId,
      settlementNumber: settlementNumber
    };

  } catch (error) {

    throw error;
  }
}

// جلب التكاليف مع التفاصيل
async function getCostsWithDetails(pool, filters = {}) {
  try {

    const {
      custodyType,
      status,
      dateFrom,
      dateTo,
      month,
      year,
      searchText,
      settlementNumber,
      limit = 50
    } = filters;

    let whereClause = 'WHERE ic.IsActive = 1';
    const params = [];

    if (custodyType) {
      whereClause += ' AND ic.CustodyType = @custodyType';
      params.push({ name: 'custodyType', type: sql.NVarChar, value: custodyType });
    }

    if (status) {
      whereClause += ' AND ic.Status = @status';
      params.push({ name: 'status', type: sql.NVarChar, value: status });
    }

    if (dateFrom) {
      whereClause += ' AND ic.CostDate >= @dateFrom';
      params.push({ name: 'dateFrom', type: sql.Date, value: dateFrom });
    }

    if (dateTo) {
      whereClause += ' AND ic.CostDate <= @dateTo';
      params.push({ name: 'dateTo', type: sql.Date, value: dateTo });
    }

    // فلتر الشهر والسنة
    if (month && year) {
      whereClause += ' AND MONTH(ic.CostDate) = @month AND YEAR(ic.CostDate) = @year';
      params.push({ name: 'month', type: sql.Int, value: parseInt(month) });
      params.push({ name: 'year', type: sql.Int, value: parseInt(year) });
    } else if (year) {
      whereClause += ' AND YEAR(ic.CostDate) = @year';
      params.push({ name: 'year', type: sql.Int, value: parseInt(year) });
    }

    // فلتر البحث النصي
    if (searchText) {
      whereClause += ` AND (
        ic.CostDescription LIKE @searchText OR
        ic.Notes LIKE @searchText OR
        ic.SettlementNumber LIKE @searchText OR
        mc.CategoryName LIKE @searchText OR
        sc.CategoryName LIKE @searchText
      )`;
      params.push({ name: 'searchText', type: sql.NVarChar, value: `%${searchText}%` });
    }

    // فلتر رقم التسوية
    if (settlementNumber) {
      whereClause += ' AND ic.SettlementNumber = @settlementNumber';
      params.push({ name: 'settlementNumber', type: sql.NVarChar, value: settlementNumber });
    }

    const request = pool.request();
    params.forEach(param => {
      request.input(param.name, param.type, param.value);
    });

    const result = await request.query(`
      SELECT
        ic.*,
        mc.CategoryName as MainCategoryName,
        sc.CategoryName as SubCategoryName,
        pc.CustodyNumber,
        pc.CustodianName,
        pc.CurrentBalance as PermanentBalance,
        tc.RequestNumber,
        tc.Purpose as TempPurpose,
        tc.Status as TempStatus,
        DATEDIFF(day, ic.CostDate, GETDATE()) as DaysOld,
        CASE
          WHEN ic.Status = N'قيد المراجعة' AND DATEDIFF(day, ic.CostDate, GETDATE()) > 7 THEN N'متأخر'
          WHEN ic.Status = N'معلّقة' AND DATEDIFF(day, ic.CostDate, GETDATE()) > 14 THEN N'متأخر جداً'
          ELSE N'في الوقت المحدد'
        END as UrgencyStatus
      FROM IntegratedCosts ic
      LEFT JOIN CostCategories mc ON ic.MainCategoryID = mc.ID AND mc.IsActive = 1
      LEFT JOIN CostCategories sc ON ic.SubCategoryID = sc.ID AND sc.IsActive = 1
      LEFT JOIN PermanentCustody pc ON ic.PermanentCustodyID = pc.ID
      LEFT JOIN TemporaryCustody tc ON ic.TemporaryCustodyID = tc.ID
      ${whereClause}
      ORDER BY ic.CreatedAt DESC
      OFFSET 0 ROWS FETCH NEXT ${limit} ROWS ONLY
    `);

    return {
      success: true,
      costs: result.recordset,
      count: result.recordset.length
    };

  } catch (error) {

    throw error;
  }
}

// جلب ملخص العُهد والأرصدة
async function getCustodyBalances(pool) {
  try {

    // العُهد المستديمة مع حساب التكاليف قيد المراجعة
    const permanentResult = await pool.request().query(`
      SELECT
        COUNT(*) as TotalCount,
        SUM(pc.InitialAmount) as TotalInitialAmount,
        SUM(pc.CurrentBalance) as TotalCurrentBalance,
        SUM(pc.TotalSpent) as TotalSpent,
        SUM(pc.TotalSettled) as TotalSettled,
        AVG(pc.CurrentBalance) as AvgBalance,
        -- حساب التكاليف قيد المراجعة
        ISNULL(SUM(pending.PendingAmount), 0) as PendingAmount,
        -- الرصيد المتاح الفعلي (الرصيد الحالي - التكاليف قيد المراجعة)
        SUM(pc.CurrentBalance) - ISNULL(SUM(pending.PendingAmount), 0) as AvailableBalance
      FROM PermanentCustody pc
      LEFT JOIN (
        SELECT
          ic.PermanentCustodyID,
          SUM(ic.Amount) as PendingAmount
        FROM IntegratedCosts ic
        WHERE ic.CustodyType = N'مستديمة'
          AND ic.Status = N'قيد المراجعة'
          AND ic.IsActive = 1
        GROUP BY ic.PermanentCustodyID
      ) pending ON pc.ID = pending.PermanentCustodyID
      WHERE pc.IsActive = 1 AND pc.Status = N'نشطة'
    `);

    // العُهد المؤقتة
    const temporaryResult = await pool.request().query(`
      SELECT
        COUNT(*) as TotalCount,
        SUM(RequestedAmount) as TotalRequested,
        SUM(ApprovedAmount) as TotalApproved,
        SUM(SpentAmount) as TotalSpent,
        SUM(RemainingAmount) as TotalRemaining,
        COUNT(CASE WHEN Status = N'قيد المراجعة' THEN 1 END) as PendingCount,
        COUNT(CASE WHEN Status = N'معتمدة' THEN 1 END) as ApprovedCount,
        COUNT(CASE WHEN Status = N'مُسوّاة' THEN 1 END) as SettledCount
      FROM TemporaryCustody
      WHERE IsActive = 1
    `);

    // التكاليف حسب الحالة
    const costsStatusResult = await pool.request().query(`
      SELECT
        Status,
        COUNT(*) as Count,
        SUM(Amount) as TotalAmount
      FROM IntegratedCosts
      WHERE IsActive = 1
      GROUP BY Status
    `);

    // التكاليف حسب النوع
    const costsTypeResult = await pool.request().query(`
      SELECT
        CustodyType,
        COUNT(*) as Count,
        SUM(Amount) as TotalAmount,
        AVG(Amount) as AvgAmount
      FROM IntegratedCosts
      WHERE IsActive = 1
      GROUP BY CustodyType
    `);

    // آخر رقم تسوية مسجل
    const lastSettlementResult = await pool.request().query(`
      SELECT TOP 1 SettlementNumber, SettlementDate
      FROM IntegratedCosts
      WHERE SettlementNumber IS NOT NULL
        AND SettlementNumber != ''
        AND Status = N'تم التسوية'
        AND IsActive = 1
      ORDER BY SettlementDate DESC, CreatedAt DESC
    `);

    const permanentData = permanentResult.recordset[0] || {};

    return {
      success: true,
      permanent: {
        ...permanentData,
        // إضافة الرصيد المتاح الفعلي
        AvailableBalance: permanentData.AvailableBalance || permanentData.TotalCurrentBalance || 0,
        PendingAmount: permanentData.PendingAmount || 0
      },
      temporary: temporaryResult.recordset[0] || {},
      lastSettlementNumber: lastSettlementResult.recordset[0]?.SettlementNumber || 'لا توجد تسويات',
      lastSettlementDate: lastSettlementResult.recordset[0]?.SettlementDate || null,
      costsByStatus: costsStatusResult.recordset || [],
      costsByType: costsTypeResult.recordset || []
    };

  } catch (error) {

    throw error;
  }
}

// تغيير حالة التكلفة مع منطق العُهد
async function changeCostStatus(pool, costId, newStatus, notes = '', processedBy = 'System') {
  try {

    // جلب بيانات التكلفة الحالية
    const costResult = await pool.request()
      .input('costId', sql.Int, costId)
      .query(`
        SELECT
          ic.*,
          pc.CustodyNumber,
          pc.CurrentBalance as PermanentBalance,
          tc.RequestNumber,
          tc.Status as TempStatus
        FROM IntegratedCosts ic
        LEFT JOIN PermanentCustody pc ON ic.PermanentCustodyID = pc.ID
        LEFT JOIN TemporaryCustody tc ON ic.TemporaryCustodyID = tc.ID
        WHERE ic.ID = @costId AND ic.IsActive = 1
      `);

    if (costResult.recordset.length === 0) {
      throw new Error('التكلفة غير موجودة');
    }

    const cost = costResult.recordset[0];
    const previousStatus = cost.Status;

    // منطق تغيير الحالة حسب نوع العُهدة
    if (cost.CustodyType === 'مستديمة') {
      // العُهدة المستديمة - دائرة مغلقة
      if (previousStatus === 'قيد المراجعة' && newStatus === 'تم التسوية') {
        // إرجاع المبلغ للعُهدة المستديمة
        await pool.request()
          .input('custodyId', sql.Int, cost.PermanentCustodyID)
          .input('amount', sql.Decimal(18,2), cost.Amount)
          .query(`
            UPDATE PermanentCustody
            SET CurrentBalance = CurrentBalance + @amount,
                TotalSettled = TotalSettled + @amount,
                LastTransactionDate = GETDATE(),
                UpdatedAt = GETDATE()
            WHERE ID = @custodyId
          `);
      } else if (previousStatus === 'تم التسوية' && newStatus === 'قيد المراجعة') {
        // خصم المبلغ مرة أخرى عند التراجع
        await pool.request()
          .input('custodyId', sql.Int, cost.PermanentCustodyID)
          .input('amount', sql.Decimal(18,2), cost.Amount)
          .query(`
            UPDATE PermanentCustody
            SET CurrentBalance = CurrentBalance - @amount,
                TotalSettled = TotalSettled - @amount,
                LastTransactionDate = GETDATE(),
                UpdatedAt = GETDATE()
            WHERE ID = @custodyId
          `);
      }
    } else if (cost.CustodyType === 'مؤقتة') {
      // العُهدة المؤقتة - منطق الطلب والاعتماد والتسوية
      if (cost.TempStatus === 'طلب' && newStatus === 'قيد المراجعة') {
        // اعتماد الطلب - إضافة المبلغ للعُهدة المؤقتة
        await pool.request()
          .input('tempId', sql.Int, cost.TemporaryCustodyID)
          .input('amount', sql.Decimal(18,2), cost.Amount)
          .query(`
            UPDATE TemporaryCustody
            SET Status = N'معتمدة',
                ApprovedAmount = RequestedAmount,
                SpentAmount = RequestedAmount,
                ApprovalDate = GETDATE(),
                UpdatedAt = GETDATE()
            WHERE ID = @tempId
          `);
      } else if (previousStatus === 'قيد المراجعة' && newStatus === 'تم التسوية') {
        // تسوية العُهدة المؤقتة - خصم المبلغ
        await pool.request()
          .input('tempId', sql.Int, cost.TemporaryCustodyID)
          .input('amount', sql.Decimal(18,2), cost.Amount)
          .query(`
            UPDATE TemporaryCustody
            SET Status = N'مُسوّاة',
                RemainingAmount = 0,
                SettlementDate = GETDATE(),
                UpdatedAt = GETDATE()
            WHERE ID = @tempId
          `);
      }
    }

    // تحديث حالة التكلفة
    await pool.request()
      .input('costId', sql.Int, costId)
      .input('status', sql.NVarChar, newStatus)
      .input('notes', sql.NVarChar, notes)
      .query(`
        UPDATE IntegratedCosts
        SET Status = @status,
            Notes = CASE
              WHEN @notes != '' THEN CONCAT(ISNULL(Notes, ''), CHAR(13) + CHAR(10) + 'تغيير الحالة: ' + @notes)
              ELSE Notes
            END,
            UpdatedAt = GETDATE()
        WHERE ID = @costId
      `);

    // إضافة سجل في تاريخ التسويات
    await pool.request()
      .input('costId', sql.Int, costId)
      .input('settlementNumber', sql.NVarChar, cost.SettlementNumber || 'تغيير حالة')
      .input('settlementType', sql.NVarChar, 'تغيير_حالة')
      .input('previousStatus', sql.NVarChar, previousStatus)
      .input('newStatus', sql.NVarChar, newStatus)
      .input('amount', sql.Decimal(18,2), cost.Amount)
      .input('notes', sql.NVarChar, notes)
      .input('processedBy', sql.NVarChar, processedBy)
      .query(`
        INSERT INTO SettlementHistory (
          CostID, SettlementNumber, SettlementType, PreviousStatus,
          NewStatus, Amount, SettlementDate, Notes, ProcessedBy
        )
        VALUES (
          @costId, @settlementNumber, @settlementType, @previousStatus,
          @newStatus, @amount, GETDATE(), @notes, @processedBy
        )
      `);

    return {
      success: true,
      message: 'تم تغيير حالة التكلفة بنجاح',
      previousStatus: previousStatus,
      newStatus: newStatus
    };

  } catch (error) {

    throw error;
  }
}

// رفع ملف PDF للتسوية
async function uploadSettlementFile(pool, costId, fileData, uploadedBy = 'System') {
  try {

    // جلب بيانات التكلفة
    const costResult = await pool.request()
      .input('costId', sql.Int, costId)
      .query(`
        SELECT
          ic.*,
          tc.RequestNumber
        FROM IntegratedCosts ic
        LEFT JOIN TemporaryCustody tc ON ic.TemporaryCustodyID = tc.ID
        WHERE ic.ID = @costId AND ic.IsActive = 1
      `);

    if (costResult.recordset.length === 0) {
      throw new Error('التكلفة غير موجودة');
    }

    const cost = costResult.recordset[0];
    let fileName, filePath, sequentialNumber = null;

    if (cost.CustodyType === 'مستديمة') {
      // العُهدة المستديمة - حفظ برقم التسوية
      if (!cost.SettlementNumber) {
        throw new Error('رقم التسوية مطلوب للعُهدة المستديمة');
      }
      fileName = `${cost.SettlementNumber}.pdf`;
      filePath = `E:\\web\\project\\archiv\\custody\\Imprest_Custody\\${fileName}`;
    } else {
      // العُهدة المؤقتة - حفظ بالرقم التسلسلي
      const counterResult = await pool.request()
        .input('tempId', sql.Int, cost.TemporaryCustodyID)
        .query(`
          SELECT RequestNumber FROM TemporaryCustody WHERE ID = @tempId
        `);

      const requestNumber = counterResult.recordset[0]?.RequestNumber;
      if (requestNumber) {
        sequentialNumber = parseInt(requestNumber.replace('TEMP-', ''));
        fileName = `${sequentialNumber}.pdf`;
        filePath = `E:\\web\\project\\archiv\\custody\\Temporary_Custody\\${fileName}`;
      } else {
        throw new Error('لا يمكن تحديد الرقم التسلسلي للعُهدة المؤقتة');
      }
    }

    // حفظ معلومات الملف في قاعدة البيانات
    await pool.request()
      .input('costId', sql.Int, costId)
      .input('custodyType', sql.NVarChar, cost.CustodyType)
      .input('settlementNumber', sql.NVarChar, cost.SettlementNumber)
      .input('sequentialNumber', sql.Int, sequentialNumber)
      .input('fileName', sql.NVarChar, fileName)
      .input('filePath', sql.NVarChar, filePath)
      .input('fileSize', sql.BigInt, fileData.size || 0)
      .input('uploadedBy', sql.NVarChar, uploadedBy)
      .query(`
        INSERT INTO CustodyArchive (
          CostID, CustodyType, SettlementNumber, SequentialNumber,
          FileName, FilePath, FileSize, UploadedBy
        )
        VALUES (
          @costId, @custodyType, @settlementNumber, @sequentialNumber,
          @fileName, @filePath, @fileSize, @uploadedBy
        )
      `);

    return {
      success: true,
      message: 'تم رفع ملف التسوية بنجاح',
      fileName: fileName,
      filePath: filePath
    };

  } catch (error) {

    throw error;
  }
}

// إضافة عُهدة مستديمة جديدة
async function addPermanentCustody(pool, custodyData) {
  try {

    // التحقق من عدم وجود رقم العُهدة مسبقاً
    const existingResult = await pool.request()
      .input('custodyNumber', sql.NVarChar(50), custodyData.custodyNumber)
      .query(`
        SELECT COUNT(*) as count
        FROM PermanentCustody
        WHERE CustodyNumber = @custodyNumber AND IsActive = 1
      `);

    if (existingResult.recordset[0].count > 0) {
      throw new Error('رقم العُهدة موجود مسبقاً');
    }

    // إضافة العُهدة الجديدة
    const insertResult = await pool.request()
      .input('custodyNumber', sql.NVarChar(50), custodyData.custodyNumber)
      .input('custodianName', sql.NVarChar(255), custodyData.custodianName)
      .input('custodianId', sql.NVarChar(50), custodyData.custodianId || null)
      .input('department', sql.NVarChar(255), custodyData.department)
      .input('initialAmount', sql.Decimal(18, 2), custodyData.initialAmount)
      .input('notes', sql.NVarChar(sql.MAX), custodyData.notes || null)
      .query(`
        INSERT INTO PermanentCustody (
          CustodyNumber, CustodianName, CustodianID, Department,
          InitialAmount, CurrentBalance, IssueDate, Notes, Status, IsActive
        )
        OUTPUT INSERTED.ID
        VALUES (
          @custodyNumber, @custodianName, @custodianId, @department,
          @initialAmount, @initialAmount, GETDATE(), @notes, N'نشطة', 1
        )
      `);

    const newCustodyId = insertResult.recordset[0].ID;

    return {
      success: true,
      message: 'تم إضافة العُهدة المستديمة بنجاح',
      custodyId: newCustodyId
    };

  } catch (error) {

    return {
      success: false,
      error: error.message
    };
  }
}

// حذف عُهدة مستديمة
async function deletePermanentCustody(pool, custodyId) {
  try {

    // التحقق من وجود تكاليف مرتبطة
    const costsResult = await pool.request()
      .input('custodyId', sql.Int, custodyId)
      .query(`
        SELECT COUNT(*) as count
        FROM IntegratedCosts
        WHERE PermanentCustodyID = @custodyId AND IsActive = 1
      `);

    if (costsResult.recordset[0].count > 0) {
      throw new Error('لا يمكن حذف العُهدة لوجود تكاليف مرتبطة بها');
    }

    // حذف العُهدة (soft delete)
    await pool.request()
      .input('custodyId', sql.Int, custodyId)
      .query(`
        UPDATE PermanentCustody
        SET IsActive = 0, UpdatedAt = GETDATE()
        WHERE ID = @custodyId
      `);

    return {
      success: true,
      message: 'تم حذف العُهدة المستديمة بنجاح'
    };

  } catch (error) {

    return {
      success: false,
      error: error.message
    };
  }
}

// إضافة بند رئيسي جديد
async function addMainCategory(pool, data) {
  try {

    // التحقق من عدم وجود البند مسبقاً
    const existingResult = await pool.request()
      .input('categoryName', sql.NVarChar(255), data.categoryName)
      .query(`
        SELECT COUNT(*) as count
        FROM CostCategories
        WHERE CategoryName = @categoryName AND ParentID IS NULL AND IsActive = 1
      `);

    if (existingResult.recordset[0].count > 0) {
      throw new Error('البند الرئيسي موجود مسبقاً');
    }

    // إضافة البند الرئيسي
    await pool.request()
      .input('categoryName', sql.NVarChar(255), data.categoryName)
      .input('categoryCode', sql.NVarChar(50), data.categoryCode || '')
      .input('description', sql.NVarChar(sql.MAX), data.description || '')
      .input('custodyType', sql.NVarChar(50), data.custodyType || 'مستديمة')
      .query(`
        INSERT INTO CostCategories (
          CategoryName, CategoryCode, Description, CustodyType, IsActive
        )
        VALUES (
          @categoryName, @categoryCode, @description, @custodyType, 1
        )
      `);

    return {
      success: true,
      message: 'تم إضافة البند الرئيسي بنجاح'
    };

  } catch (error) {

    return {
      success: false,
      error: error.message
    };
  }
}

// إضافة بند فرعي جديد
async function addSubCategory(pool, data) {
  try {

    // التحقق من وجود البند الرئيسي
    const parentResult = await pool.request()
      .input('parentId', sql.Int, data.parentCategoryId)
      .query(`
        SELECT ID, CategoryName
        FROM CostCategories
        WHERE ID = @parentId AND ParentID IS NULL AND IsActive = 1
      `);

    if (parentResult.recordset.length === 0) {
      throw new Error('البند الرئيسي غير موجود');
    }

    // التحقق من عدم وجود البند الفرعي مسبقاً
    const existingResult = await pool.request()
      .input('categoryName', sql.NVarChar(255), data.categoryName)
      .input('parentId', sql.Int, data.parentCategoryId)
      .query(`
        SELECT COUNT(*) as count
        FROM CostCategories
        WHERE CategoryName = @categoryName AND ParentID = @parentId AND IsActive = 1
      `);

    if (existingResult.recordset[0].count > 0) {
      throw new Error('البند الفرعي موجود مسبقاً تحت هذا البند الرئيسي');
    }

    // إضافة البند الفرعي
    await pool.request()
      .input('categoryName', sql.NVarChar(255), data.categoryName)
      .input('categoryCode', sql.NVarChar(50), data.categoryCode || '')
      .input('description', sql.NVarChar(sql.MAX), data.description || '')
      .input('parentId', sql.Int, data.parentCategoryId)
      .input('custodyType', sql.NVarChar(50), 'مستديمة')
      .query(`
        INSERT INTO CostCategories (
          CategoryName, CategoryCode, Description, ParentID, CustodyType, IsActive
        )
        VALUES (
          @categoryName, @categoryCode, @description, @parentId, @custodyType, 1
        )
      `);

    return {
      success: true,
      message: 'تم إضافة البند الفرعي بنجاح'
    };

  } catch (error) {

    return {
      success: false,
      error: error.message
    };
  }
}

// تحديث قيمة العُهدة المستديمة
async function updatePermanentCustodyAmount(pool, custodyId, newAmount, updatedBy = 'System') {
  try {

    // جلب البيانات الحالية
    const currentResult = await pool.request()
      .input('custodyId', sql.Int, custodyId)
      .query(`
        SELECT InitialAmount, CurrentBalance, TotalSpent
        FROM PermanentCustody
        WHERE ID = @custodyId AND IsActive = 1
      `);

    if (currentResult.recordset.length === 0) {
      throw new Error('العُهدة المستديمة غير موجودة');
    }

    const current = currentResult.recordset[0];
    const difference = newAmount - current.InitialAmount;
    const newCurrentBalance = current.CurrentBalance + difference;

    // تحديث العُهدة
    await pool.request()
      .input('custodyId', sql.Int, custodyId)
      .input('newAmount', sql.Decimal(18,2), newAmount)
      .input('newCurrentBalance', sql.Decimal(18,2), newCurrentBalance)
      .query(`
        UPDATE PermanentCustody
        SET InitialAmount = @newAmount,
            CurrentBalance = @newCurrentBalance,
            Notes = CONCAT(ISNULL(Notes, ''), CHAR(13) + CHAR(10) + 'تم تحديث المبلغ من ' + CAST(InitialAmount AS NVARCHAR) + ' إلى ' + CAST(@newAmount AS NVARCHAR)),
            UpdatedAt = GETDATE()
        WHERE ID = @custodyId
      `);

    return {
      success: true,
      message: 'تم تحديث قيمة العُهدة المستديمة بنجاح',
      oldAmount: current.InitialAmount,
      newAmount: newAmount,
      difference: difference
    };

  } catch (error) {

    throw error;
  }
}

// جلب تصنيفات التكاليف
async function getCostCategories(pool, custodyType = null) {
  try {

    let whereClause = 'WHERE IsActive = 1';
    const request = pool.request();

    if (custodyType) {
      whereClause += ' AND (CustodyType = @custodyType OR CustodyType = N\'مشتركة\')';
      request.input('custodyType', sql.NVarChar, custodyType);
    }

    const result = await request.query(`
      SELECT
        ID,
        ParentID,
        CategoryName,
        CategoryCode,
        Description,
        CustodyType,
        IsActive
      FROM CostCategories
      ${whereClause}
      ORDER BY
        CASE WHEN ParentID IS NULL THEN 0 ELSE 1 END,
        ParentID,
        CategoryName
    `);

    // تنظيم التصنيفات (رئيسية وفرعية)
    const mainCategories = result.recordset.filter(cat => !cat.ParentID);
    const subCategories = result.recordset.filter(cat => cat.ParentID);

    // إضافة التصنيفات الفرعية للرئيسية
    mainCategories.forEach(main => {
      main.SubCategories = subCategories.filter(sub => sub.ParentID === main.ID);
    });

    return {
      success: true,
      data: {
        all: result.recordset,
        main: mainCategories,
        sub: subCategories
      }
    };

  } catch (error) {

    throw error;
  }
}

// حذف التسوية الذكي
async function deleteCostSmart(pool, costId, deletedBy) {
  try {

    // جلب بيانات التكلفة قبل الحذف
    const costResult = await pool.request()
      .input('costId', sql.Int, costId)
      .query(`
        SELECT
          ic.*,
          pc.CustodyNumber,
          pc.CurrentBalance as PermanentBalance,
          tc.RequestNumber
        FROM IntegratedCosts ic
        LEFT JOIN PermanentCustody pc ON ic.PermanentCustodyID = pc.ID
        LEFT JOIN TemporaryCustody tc ON ic.TemporaryCustodyID = tc.ID
        WHERE ic.ID = @costId AND ic.IsActive = 1
      `);

    if (costResult.recordset.length === 0) {
      throw new Error('التكلفة غير موجودة أو تم حذفها مسبقاً');
    }

    const cost = costResult.recordset[0];

    // التحقق من إمكانية الحذف
    if (cost.Status === 'تم التسوية') {
      throw new Error('لا يمكن حذف تسوية مُسوّاة. يجب إلغاء التسوية أولاً.');
    }

    // إرجاع المبلغ للعُهدة المستديمة إذا كانت مخصومة
    if (cost.CustodyType === 'مستديمة' && cost.PermanentCustodyID && cost.Status === 'قيد المراجعة') {
      await pool.request()
        .input('custodyId', sql.Int, cost.PermanentCustodyID)
        .input('amount', sql.Decimal(18,2), cost.Amount)
        .query(`
          UPDATE PermanentCustody
          SET CurrentBalance = CurrentBalance + @amount,
              TotalSpent = TotalSpent - @amount,
              LastTransactionDate = GETDATE(),
              UpdatedAt = GETDATE()
          WHERE ID = @custodyId
        `);

    }

    // حذف التكلفة (soft delete)
    await pool.request()
      .input('costId', sql.Int, costId)
      .input('deletedBy', sql.NVarChar, deletedBy || 'المستخدم')
      .query(`
        UPDATE IntegratedCosts
        SET IsActive = 0,
            Status = N'محذوفة',
            Notes = CONCAT(ISNULL(Notes, ''), CHAR(13) + CHAR(10) + 'تم الحذف بواسطة: ' + @deletedBy + ' في ' + CONVERT(NVARCHAR, GETDATE(), 120)),
            UpdatedAt = GETDATE()
        WHERE ID = @costId
      `);

    // حذف العُهدة المؤقتة المرتبطة إذا وُجدت
    if (cost.TemporaryCustodyID) {
      await pool.request()
        .input('tempId', sql.Int, cost.TemporaryCustodyID)
        .query(`
          UPDATE TemporaryCustody
          SET IsActive = 0,
              Status = N'محذوفة',
              UpdatedAt = GETDATE()
          WHERE ID = @tempId
        `);

    }

    // إضافة سجل في تاريخ التسويات
    await pool.request()
      .input('costId', sql.Int, costId)
      .input('settlementNumber', sql.NVarChar, 'DELETE-' + Date.now())
      .input('settlementType', sql.NVarChar, 'حذف_التسوية')
      .input('previousStatus', sql.NVarChar, cost.Status)
      .input('newStatus', sql.NVarChar, 'محذوفة')
      .input('amount', sql.Decimal(18,2), cost.Amount)
      .input('processedBy', sql.NVarChar, deletedBy || 'المستخدم')
      .input('notes', sql.NVarChar, 'تم حذف التسوية وإرجاع المبلغ للعُهدة')
      .query(`
        INSERT INTO SettlementHistory (
          CostID, SettlementNumber, SettlementType, PreviousStatus,
          NewStatus, Amount, SettlementDate, ProcessedBy, Notes
        )
        VALUES (
          @costId, @settlementNumber, @settlementType, @previousStatus,
          @newStatus, @amount, GETDATE(), @processedBy, @notes
        )
      `);

    return {
      success: true,
      message: 'تم حذف التسوية بنجاح وإرجاع المبلغ للعُهدة',
      deletedCost: {
        id: costId,
        amount: cost.Amount,
        custodyType: cost.CustodyType,
        custodyNumber: cost.CustodyNumber || cost.RequestNumber
      }
    };

  } catch (error) {

    throw error;
  }
}

// معالج طلبات POST
export async function POST(request) {
  try {
    const body = await request.json();
    const { action } = body;

    const pool = await getConnection();

    switch (action) {
      case 'setup':
        await ensureIntegratedCustodySystem(pool);
        return NextResponse.json({
          success: true,
          message: 'تم إعداد النظام المدمج بنجاح'
        });

      case 'createCost':
        const costResult = await createSmartCost(pool, body.data);
        return NextResponse.json(costResult);

      case 'settleCost':
        const settlementResult = await settleCostSmart(pool, body.costId, body.settlementData);
        return NextResponse.json(settlementResult);

      case 'rejectCost':
        await pool.request()
          .input('costId', sql.Int, body.costId)
          .input('notes', sql.NVarChar, body.notes || '')
          .query(`
            UPDATE IntegratedCosts
            SET Status = N'مرفوضة',
                Notes = CONCAT(ISNULL(Notes, ''), CHAR(13) + CHAR(10) + 'مرفوضة: ' + @notes),
                UpdatedAt = GETDATE()
            WHERE ID = @costId
          `);

        return NextResponse.json({
          success: true,
          message: 'تم رفض التكلفة بنجاح'
        });

      case 'changeCostStatus':
        const statusResult = await changeCostStatus(pool, body.costId, body.newStatus, body.notes, body.processedBy);
        return NextResponse.json(statusResult);

      case 'uploadSettlementFile':
        const uploadResult = await uploadSettlementFile(pool, body.costId, body.fileData, body.uploadedBy);
        return NextResponse.json(uploadResult);

      case 'updatePermanentCustodyAmount':
        const updateResult = await updatePermanentCustodyAmount(pool, body.custodyId, body.newAmount, body.updatedBy);
        return NextResponse.json(updateResult);

      case 'deleteCost':
        const deleteResult = await deleteCostSmart(pool, body.costId, body.deletedBy);
        return NextResponse.json(deleteResult);

      case 'addPermanentCustody':
        const addCustodyResult = await addPermanentCustody(pool, body.data);
        return NextResponse.json(addCustodyResult);

      case 'deletePermanentCustody':
        const deleteCustodyResult = await deletePermanentCustody(pool, body.custodyId);
        return NextResponse.json(deleteCustodyResult);

      case 'addMainCategory':
        const addMainCategoryResult = await addMainCategory(pool, body.data);
        return NextResponse.json(addMainCategoryResult);

      case 'addSubCategory':
        const addSubCategoryResult = await addSubCategory(pool, body.data);
        return NextResponse.json(addSubCategoryResult);

      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير مدعوم'
        }, { status: 400 });
    }

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// معالج طلبات GET
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'getCosts';

    const pool = await getConnection();

    switch (action) {
      case 'getCosts':
        const filters = {
          custodyType: searchParams.get('custodyType'),
          status: searchParams.get('status'),
          dateFrom: searchParams.get('dateFrom'),
          dateTo: searchParams.get('dateTo'),
          limit: parseInt(searchParams.get('limit')) || 50
        };

        const costsResult = await getCostsWithDetails(pool, filters);
        return NextResponse.json(costsResult);

      case 'getBalances':
        const balancesResult = await getCustodyBalances(pool);
        return NextResponse.json(balancesResult);

      case 'getCategories':
        const custodyType = searchParams.get('custodyType');
        const categoriesResult = await getCostCategories(pool, custodyType);
        return NextResponse.json(categoriesResult);

      case 'getPermanentCustodies':

        // التحقق من وجود العمود IsActive
        let whereClause = '';
        try {
          const checkColumnResult = await pool.request().query(`
            SELECT COLUMN_NAME
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'PermanentCustody' AND COLUMN_NAME = 'IsActive'
          `);

          if (checkColumnResult.recordset.length > 0) {
            whereClause = 'WHERE IsActive = 1';

          } else {
            whereClause = 'WHERE 1=1';

          }
        } catch (error) {

          whereClause = 'WHERE 1=1';
        }

        const permanentResult = await pool.request().query(`
          SELECT
            ID,
            CustodyNumber,
            CustodianName,
            Department,
            InitialAmount,
            CurrentBalance,
            TotalSpent,
            TotalSettled,
            Status,
            IssueDate,
            LastTransactionDate
          FROM PermanentCustody
          ${whereClause}
          ORDER BY CustodyNumber
        `);

        if (permanentResult.recordset.length > 0) {
          permanentResult.recordset.forEach((custody, index) => {

          });
        }

        return NextResponse.json({
          success: true,
          custodies: permanentResult.recordset
        }, {
          headers: {
            'Content-Type': 'application/json; charset=utf-8'
          }
        });

      case 'getTemporaryCustodies':
        const temporaryResult = await pool.request().query(`
          SELECT
            tc.*,
            pc.CustodyNumber as PermanentCustodyNumber,
            pc.CustodianName
          FROM TemporaryCustody tc
          LEFT JOIN PermanentCustody pc ON tc.PermanentCustodyID = pc.ID
          WHERE tc.IsActive = 1
          ORDER BY tc.RequestDate DESC
        `);

        return NextResponse.json({
          success: true,
          custodies: temporaryResult.recordset
        });

      case 'getCostDetails':
        const costId = searchParams.get('costId');
        if (!costId) {
          return NextResponse.json({
            success: false,
            error: 'معرف التكلفة مطلوب'
          }, { status: 400 });
        }

        const detailsResult = await pool.request()
          .input('costId', sql.Int, costId)
          .query(`
            SELECT
              ic.*,
              mc.CategoryName as MainCategoryName,
              sc.CategoryName as SubCategoryName,
              pc.CustodyNumber,
              pc.CustodianName,
              pc.CurrentBalance as PermanentBalance,
              tc.RequestNumber,
              tc.Purpose as TempPurpose,
              tc.Status as TempStatus
            FROM IntegratedCosts ic
            LEFT JOIN CostCategories mc ON ic.MainCategoryID = mc.ID AND mc.IsActive = 1
            LEFT JOIN CostCategories sc ON ic.SubCategoryID = sc.ID AND sc.IsActive = 1
            LEFT JOIN PermanentCustody pc ON ic.PermanentCustodyID = pc.ID
            LEFT JOIN TemporaryCustody tc ON ic.TemporaryCustodyID = tc.ID
            WHERE ic.ID = @costId AND ic.IsActive = 1
          `);

        if (detailsResult.recordset.length === 0) {
          return NextResponse.json({
            success: false,
            error: 'التكلفة غير موجودة'
          }, { status: 404 });
        }

        // جلب تاريخ التسويات
        const historyResult = await pool.request()
          .input('costId', sql.Int, costId)
          .query(`
            SELECT * FROM SettlementHistory
            WHERE CostID = @costId
            ORDER BY CreatedAt DESC
          `);

        return NextResponse.json({
          success: true,
          cost: detailsResult.recordset[0],
          history: historyResult.recordset
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير مدعوم'
        }, { status: 400 });
    }

  } catch (error) {

    // معالجة خاصة لخطأ العُهد المستديمة
    if (error.message.includes('PermanentCustody')) {

      try {
        await ensurePermanentCustodyTable(pool);
        return NextResponse.json({
          success: true,
          custodies: [],
          message: 'تم إنشاء جدول العُهد المستديمة، يرجى المحاولة مرة أخرى'
        });
      } catch (createError) {

      }
    }

    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
