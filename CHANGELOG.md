# سجل التغييرات

جميع التغييرات المهمة في هذا المشروع سيتم توثيقها في هذا الملف.

التنسيق مبني على [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)،
وهذا المشروع يتبع [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [غير منشور]

### تم إضافته
- نظام تنظيف المشروع الشامل
- سكريبتات تنظيف الملفات المؤقتة والاختبارية
- إعدادات محسنة لـ ESLint و Prettier
- تحسين إعدادات VS Code
- ملف .gitignore محسن لتجنب الملفات غير الضرورية

### تم تغييره
- تنظيف التبعيات في package.json
- تحسين إعدادات TypeScript
- تحديث README مع معلومات شاملة عن المشروع

### تم إزالته
- 170+ ملف مؤقت واختباري
- ملفات التوثيق المؤقتة
- الملفات المكررة في مجلد public
- التبعيات غير المستخدمة
- عبارات console.log من الكود
- التعليقات المؤقتة

### تم إصلاحه
- خطأ قاعدة البيانات CustodyCosts
- خطأ عمود UpdatedAt في جدول LeaveBalances
- مشاكل الواردات غير المستخدمة

## [1.0.0] - 2025-01-06

### تم إضافته
- نظام إدارة الموظفين الشامل
- لوحة تحكم تفاعلية
- نظام إدارة الحضور والغياب
- إدارة الطلبات والموافقات
- نظام العهد والتكاليف
- إدارة الأصول (سيارات وشقق)
- التقارير والتحليلات
- نظام المصادقة والأمان
- دعم اللغة العربية
- تصميم متجاوب

### الميزات الرئيسية
- إدارة بيانات الموظفين مع البحث المتقدم
- نظام الحضور اليومي والشهري
- إدارة طلبات الإجازات والمهام
- تتبع التكاليف والعهد المستديمة
- رسوم بيانية تفاعلية
- تصدير التقارير
- رفع وإدارة الملفات
- نظام الإشعارات

### التقنيات المستخدمة
- Next.js 13 مع App Router
- React 18
- SQL Server
- Tailwind CSS
- Chart.js
- JWT Authentication
