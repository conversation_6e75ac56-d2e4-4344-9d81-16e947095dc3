"use client";
import React from "react";

function MainComponent() {
  const [selectedLang, setSelectedLang] = useState("ar");
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);
  const { signInWithCredentials } = useAuth();
  const [formData, setFormData] = useState({
    employee_code: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState(false);

  const dir = selectedLang === "ar" ? "rtl" : "ltr";

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    try {
      const response = await fetch("/api/authenticate-employee-login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error(
          selectedLang === "ar" ? "فشل تسجيل الدخول" : "Login failed"
        );
      }

      const data = await response.json();

      if (!data.success) {
        setError(
          selectedLang === "ar"
            ? data.error || "كود الموظف أو كلمة المرور غير صحيحة"
            : data.error || "Invalid employee code or password"
        );
        return;
      }

      await signInWithCredentials({
        email: data.user.email,
        password: formData.password,
        callbackUrl: "/dashboard",
        redirect: true,
      });
    } catch (err) {
      console.error(err);
      setError(
        selectedLang === "ar"
          ? "حدث خطأ أثناء تسجيل الدخول"
          : "An error occurred during sign in"
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      dir={dir}
      className="min-h-screen bg-gradient-to-b from-blue-100 to-white dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4"
    >
      <div className="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-xl p-8 transform transition-all duration-300 hover:scale-[1.02]">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-3">
            {selectedLang === "ar" ? "تسجيل الدخول" : "Sign In"}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {selectedLang === "ar"
              ? "قم بتسجيل الدخول باستخدام كود الموظف"
              : "Sign in using your employee code"}
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {selectedLang === "ar" ? "كود الموظف" : "Employee Code"}
            </label>
            <input
              type="text"
              value={formData.employee_code}
              onChange={(e) =>
                setFormData({ ...formData, employee_code: e.target.value })
              }
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
              required
              placeholder={
                selectedLang === "ar"
                  ? "أدخل كود الموظف"
                  : "Enter employee code"
              }
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {selectedLang === "ar" ? "كلمة المرور" : "Password"}
            </label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                value={formData.password}
                onChange={(e) =>
                  setFormData({ ...formData, password: e.target.value })
                }
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                required
                placeholder={
                  selectedLang === "ar" ? "أدخل كلمة المرور" : "Enter password"
                }
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-0 px-3 flex items-center text-gray-500 hover:text-gray-700"
              >
                {showPassword ? (
                  <i className="fas fa-eye-slash"></i>
                ) : (
                  <i className="fas fa-eye"></i>
                )}
              </button>
            </div>
          </div>

          {error && (
            <div className="p-4 bg-red-50 border-l-4 border-red-500 text-red-700 rounded">
              <div className="flex items-center">
                <i className="fas fa-exclamation-circle mx-2"></i>
                {error}
              </div>
            </div>
          )}

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-blue-300 transition-colors duration-200"
          >
            {loading ? (
              <span className="flex items-center justify-center">
                <i className="fas fa-spinner fa-spin mx-2"></i>
                {selectedLang === "ar" ? "جاري التحميل..." : "Loading..."}
              </span>
            ) : selectedLang === "ar" ? (
              "تسجيل الدخول"
            ) : (
              "Sign In"
            )}
          </button>
        </form>

        <div className="mt-6 text-center">
          <button
            onClick={() => setSelectedLang(selectedLang === "ar" ? "en" : "ar")}
            className="text-sm text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white transition-colors duration-200"
          >
            {selectedLang === "ar" ? "English" : "العربية"}
          </button>
        </div>
      </div>
    </div>
  );
}

export default MainComponent;