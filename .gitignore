# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Project specific ignores
# Temporary files
temp-*
test-*
debug-*
fix-*
check-*
verify-*
quick-*
simple-*
final-*
emergency-*
critical-*
auto-*
manual-*
run-*
sync-*
update-*
restore-*
reset-*
complete-*
unified-*
standalone-*
server-*
layout-*

# Documentation files (temporary)
*_GUIDE.md
*_FIX.md
*_SUMMARY.md
*_DOCUMENTATION.md
*_REPORT.md
*_ANALYSIS.md
*_README.md
*_UPDATE.md
*_FIXES.md

# SQL temporary files
add_*.sql
update_*.sql
fix_*.sql
create_*.sql
remove_*.sql

# Backup and archive files
*.bak
*.tmp
*.temp
*.old
*.backup

# Database analysis files
database-analysis.json

# Build tools
webpack.config.js

# Logs
*.log

# Archive folders (keep structure but ignore content)
/archiv/*
!/archiv/.gitkeep
/backups/*
!/backups/.gitkeep
/exports/*
!/exports/.gitkeep

# External tools
/Augment-free/
/oje/
/ojesta/

# PowerShell scripts
*.ps1

# Duplicate files
*\ (1).*
*\ (2).*
*\ (3).*
*\ (4).*