import { NextResponse } from 'next/server';
import { getConnection } from '@/lib/db';
import sql from 'mssql';

export async function POST() {
  try {

    const pool = await getConnection();

    // التحقق من وجود فئة السيارات
    const carsCategory = await pool.request().query(`
      SELECT ID FROM CostCategories 
      WHERE CategoryCode = 'CARS' AND ParentID IS NULL
    `);

    if (carsCategory.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'فئة السيارات غير موجودة'
      }, { status: 404 });
    }

    const carsCategoryId = carsCategory.recordset[0].ID;

    // التحقق من عدم وجود بند الصيانة مسبقاً
    const existingMaintenance = await pool.request().query(`
      SELECT ID FROM CostCategories 
      WHERE CategoryCode = 'CARS_MAINTENANCE' AND ParentID = ${carsCategoryId}
    `);

    if (existingMaintenance.recordset.length > 0) {
      return NextResponse.json({
        success: true,
        message: 'بند صيانة السيارات موجود بالفعل'
      });
    }

    // إضافة بند صيانة السيارات
    await pool.request()
      .input('parentId', sql.Int, carsCategoryId)
      .input('categoryName', sql.NVarChar, 'صيانة')
      .input('categoryCode', sql.NVarChar, 'CARS_MAINTENANCE')
      .input('description', sql.NVarChar, 'صيانة السيارات والإصلاحات')
      .query(`
        INSERT INTO CostCategories (ParentID, CategoryName, CategoryCode, Description, CustodyType, IsActive)
        VALUES (@parentId, @categoryName, @categoryCode, @description, N'مستديمة', 1)
      `);

    return NextResponse.json({
      success: true,
      message: 'تم إضافة بند صيانة السيارات بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

export async function GET() {
  try {

    const pool = await getConnection();

    // جلب فئة السيارات والبنود الفرعية
    const result = await pool.request().query(`
      SELECT 
        parent.ID as parentId,
        parent.CategoryName as parentName,
        parent.CategoryCode as parentCode,
        sub.ID as subId,
        sub.CategoryName as subName,
        sub.CategoryCode as subCode,
        sub.Description as subDescription
      FROM CostCategories parent
      LEFT JOIN CostCategories sub ON parent.ID = sub.ParentID
      WHERE parent.CategoryCode = 'CARS' AND parent.ParentID IS NULL
      ORDER BY sub.CategoryName
    `);

    const carsCategory = {
      id: null,
      name: '',
      code: '',
      subCategories: []
    };

    result.recordset.forEach(row => {
      if (!carsCategory.id) {
        carsCategory.id = row.parentId;
        carsCategory.name = row.parentName;
        carsCategory.code = row.parentCode;
      }

      if (row.subId) {
        carsCategory.subCategories.push({
          id: row.subId,
          name: row.subName,
          code: row.subCode,
          description: row.subDescription
        });
      }
    });

    return NextResponse.json({
      success: true,
      data: carsCategory
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
