async function handler({ fileContent, lang = 'ar' }) {
  const errorMessages = {
    required: { ar: 'البيانات مطلوبة', en: 'Data is required' },
    emptyFile: { ar: 'الملف فارغ', en: 'File is empty' },
    invalidData: { ar: 'خطأ في البيانات', en: 'Invalid data' },
  };

  const columnMapping = {
    code: 'EmployeeID',
    name: 'FullName',
    birthDay: 'BirthDate',
    joinDate: 'JoinDate',
    title: 'JobTitle',
    department: 'Department',
    education: 'Education',
    university: 'University',
    major: 'Major',
    grade: 'Grade',
    batch: 'Batch',
    mobile: 'Mobile',
    nationalId: 'NationalID',
    hireDate: 'HireDate',
    transportMethod: 'Transport_Method',
    companyHousing: 'CompanyHousing',
    codeHousing: 'codeHousing',
    mserv: 'M_serv',
    gender: 'Gender',
    currentStatus: 'CurrentStatus',
    direct: 'direct',
    area: 'area',
    email: 'email',
    emrnum: 'emrnum',
    kinship: 'Kinship',
  };

  try {
    if (!fileContent) {
      throw new Error(errorMessages.required[lang]);
    }

    const buffer = Buffer.from(fileContent.split(',')[1], 'base64');
    const csvString = buffer.toString('utf-8');
    const rows = csvString
      .split('\n')
      .map((row) => row.split(',').map((cell) => cell.trim()))
      .filter((row) => row.length > 1 && row.some((cell) => cell));

    if (rows.length <= 1) {
      throw new Error(errorMessages.emptyFile[lang]);
    }

    const headers = rows[0];
    const records = rows
      .slice(1)
      .map((row) => {
        const record = {};
        headers.forEach((header, index) => {
          const dbColumn = columnMapping[header.trim()] || header.trim();
          let value = row[index]?.trim() || '';

          if (
            !value ||
            value === '-' ||
            value === '################' ||
            value === '#'
          ) {
            record[dbColumn] = null;
          } else if (dbColumn.includes('date')) {
            try {
              const date = new Date(value);
              record[dbColumn] = date.toISOString().split('T')[0];
            } catch {
              record[dbColumn] = null;
            }
          } else if (dbColumn === 'graduation_year') {
            record[dbColumn] = parseInt(value) || null;
          } else if (dbColumn === 'transportation_allowance') {
            record[dbColumn] = parseFloat(value) || 0;
          } else {
            record[dbColumn] = value;
          }
        });
        return record;
      })
      .filter((record) => record.employee_id);

    const queries = records.map((record) => {
      const columns = Object.keys(record).filter((key) => record[key] !== null);
      const values = columns.map((col) => record[col]);
      const placeholders = columns.map((_, i) => `$${i + 1}`);
      const updateSet = columns
        .filter((col) => col !== 'employee_id')
        .map((col, i) => `${col} = $${i + 2}`)
        .join(', ');

      return {
        query: `INSERT INTO Employees (${columns.join(', ')})
                VALUES (${placeholders.join(', ')})
                ON CONFLICT (employee_id) 
                DO UPDATE SET
                  ${updateSet},
                  updated_at = CURRENT_TIMESTAMP
                RETURNING *`,
        values,
      };
    });

    const results = await sql.transaction(
      queries.map(({ query, values }) => sql(query, values))
    );

    return {
      success: true,
      message:
        lang === 'ar'
          ? `تم استيراد ${results.length} سجل بنجاح`
          : `Successfully imported ${results.length} records`,
      records: results.flat(),
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
}
