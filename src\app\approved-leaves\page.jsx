'use client';

import { useState, useEffect } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import MainLayout from '@/components/MainLayout';
import { formatDateToDDMMYYYY } from '@/utils/dateFormat';
import { FiCalendar, FiSearch, FiPrinter, FiFilter } from 'react-icons/fi';

export default function ApprovedLeavesPage() {
  const { isArabic } = useLanguage();
  const [approvedLeaves, setApprovedLeaves] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    month: 7, // يوليو (الشهر الذي يحتوي على الإجازات المعتمدة من 11 يونيو إلى 10 يوليو)
    year: 2025, // السنة التي تحتوي على الإجازات
    employeeCode: '',
    employeeName: ''
  });

  // أشهر السنة
  const months = [
    { value: 1, label: isArabic ? 'يناير' : 'January' },
    { value: 2, label: isArabic ? 'فبراير' : 'February' },
    { value: 3, label: isArabic ? 'مارس' : 'March' },
    { value: 4, label: isArabic ? 'أبريل' : 'April' },
    { value: 5, label: isArabic ? 'مايو' : 'May' },
    { value: 6, label: isArabic ? 'يونيو' : 'June' },
    { value: 7, label: isArabic ? 'يوليو' : 'July' },
    { value: 8, label: isArabic ? 'أغسطس' : 'August' },
    { value: 9, label: isArabic ? 'سبتمبر' : 'September' },
    { value: 10, label: isArabic ? 'أكتوبر' : 'October' },
    { value: 11, label: isArabic ? 'نوفمبر' : 'November' },
    { value: 12, label: isArabic ? 'ديسمبر' : 'December' }
  ];

  // حساب فترة الشهر (من 11 الشهر السابق إلى 10 الشهر الحالي)
  const getMonthPeriod = (month, year) => {
    const startMonth = month === 1 ? 12 : month - 1;
    const startYear = month === 1 ? year - 1 : year;
    const startDate = `${startYear}-${startMonth.toString().padStart(2, '0')}-11`;
    const endDate = `${year}-${month.toString().padStart(2, '0')}-10`;
    return { startDate, endDate };
  };

  // جلب الإجازات المعتمدة
  const fetchApprovedLeaves = async () => {
    try {
      setLoading(true);
      
      const { startDate, endDate } = getMonthPeriod(filters.month, filters.year);
      
      const queryParams = new URLSearchParams({
        action: 'list',
        requestType: 'leave',
        status: 'معتمدة',
        startDate,
        endDate,
        employeeCode: filters.employeeCode,
        employeeName: filters.employeeName
      });

      const response = await fetch(`/api/paper-requests?${queryParams}`);
      const result = await response.json();

      if (result.success) {
        const approvedLeaveRequests = (result.requests || []).filter(req => 
          req.RequestType === 'leave' && req.Status === 'معتمدة'
        );
        setApprovedLeaves(approvedLeaveRequests);
      } else {

      }
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  // تحديث الفلاتر
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // استخدام دالة تنسيق التاريخ الموحدة بصيغة dd/mm/yyyy
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';

    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  };

  // دالة لتحويل نوع الإجازة للعربية
  const getLeaveTypeLabel = (type) => {
    const types = {
      // الأنواع العربية
      'اعتيادية': 'إجازة اعتيادية',
      'إعتيادية': 'إجازة اعتيادية',
      'مرضية': 'إجازة مرضية',
      'عارضة': 'إجازة عارضة',
      'بدل': 'إجازة بدل',
      'بدون أجر': 'إجازة بدون أجر',

      // الأنواع الإنجليزية (تحويل للعربية)
      'annual': 'إجازة اعتيادية',
      'regular': 'إجازة اعتيادية',
      'sick': 'إجازة مرضية',
      'emergency': 'إجازة عارضة',
      'casual': 'إجازة عارضة',
      'compensatory': 'إجازة بدل',
      'unpaid': 'إجازة بدون أجر'
    };
    return types[type] || type;
  };

  // دالة تحميل اللوجو
  const getLogoBase64 = async () => {
    try {
      const response = await fetch('/logo.png');
      if (!response.ok) return null;

      const blob = await response.blob();
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onload = () => {
          const base64 = reader.result.split(',')[1];
          resolve(base64);
        };
        reader.onerror = () => resolve(null);
        reader.readAsDataURL(blob);
      });
    } catch (error) {

      return null;
    }
  };

  // طباعة طلب الإجازة الصحيح (مطابق لصفحة طلب الإجازة)
  const printLeaveRequestCorrect = async (requestData) => {
    // جلب البيانات الإضافية المطلوبة
    let lastLeaveDate = '';
    let remainingBalance = '';

    if (requestData.EmployeeCode && requestData.LeaveType) {
      try {
        // جلب تاريخ آخر إجازة معتمدة
        const lastLeaveResponse = await fetch(`/api/paper-requests?action=getLastApprovedLeave&employeeId=${requestData.EmployeeCode}`);
        if (lastLeaveResponse.ok) {
          const lastLeaveResult = await lastLeaveResponse.json();
          if (lastLeaveResult.success && lastLeaveResult.lastLeave) {
            lastLeaveDate = formatDate(lastLeaveResult.lastLeave.EndDate);
          }
        }

        // جلب الرصيد المتبقي
        const balanceResponse = await fetch(`/api/leave-balance?employeeId=${requestData.EmployeeCode}&leaveType=${requestData.LeaveType}`);
        if (balanceResponse.ok) {
          const balanceResult = await balanceResponse.json();
          if (balanceResult.success) {
            remainingBalance = balanceResult.balance || '';
          }
        }
      } catch (error) {

      }
    }

    // تحميل اللوجو أولاً قبل فتح نافذة الطباعة
    const logoBase64 = await getLogoBase64();

    // انتظار إضافي للتأكد من تحميل اللوجو
    await new Promise(resolve => setTimeout(resolve, 1000));

    const printWindow = window.open('', '_blank');

    // إضافة timestamp لمنع cache
    const timestamp = new Date().getTime();

    const printContent = `
      <!DOCTYPE html>
      <html dir="rtl">
      <!-- Updated: ${timestamp} - Fixed name/code order, spacing, notes alignment -->
      <head>
        <meta charset="UTF-8">
        <title>نموذج طلب إجازة - ${requestData.EmployeeName}</title>
        <style>
          @page {
            size: A4 portrait;
            margin: 10mm;
          }

          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          body {
            font-family: 'Arial', sans-serif;
            font-size: 11px;
            line-height: 1.3;
            color: #000 !important;
            background: white !important;
            direction: rtl;
            text-align: right;
            width: 190mm;
            max-width: 190mm;
            margin: 0 auto;
            padding: 5mm;
          }

          /* Header Table - مطابق للنموذج الأصلي تماماً */
          .header-table {
            width: 100%;
            border-collapse: collapse;
            border: 2px solid #000;
            margin-bottom: 8px;
            table-layout: fixed;
          }

          .header-table td {
            border: 1px solid #000;
            padding: 6px;
            text-align: center;
            vertical-align: middle;
            height: 50px;
            font-size: 10px;
          }

          .logo-cell {
            width: 33.33%;
          }

          .logo-img {
            max-width: 100px;
            max-height: 70px;
            object-fit: contain;
          }

          .form-title-cell {
            width: 33.33%;
            font-weight: bold;
            text-align: center;
          }

          .company-cell {
            width: 33.33%;
            font-weight: bold;
          }

          .form-title {
            font-size: 14px;
            margin-bottom: 3px;
            font-weight: bold;
          }

          .form-code {
            font-size: 10px;
            color: #666;
          }

          .company-name-ar {
            font-size: 10px;
            margin-bottom: 2px;
            font-weight: bold;
          }

          .company-name-en {
            font-size: 8px;
            color: #666;
            font-style: italic;
          }

          /* Section Headers - مطابق للنموذج الأصلي */
          .section-header {
            background-color: #f0f0f0;
            text-align: center;
            padding: 6px;
            font-size: 11px;
            font-weight: bold;
            border: 1px solid #000;
            margin: 8px 0 5px 0;
          }

          /* Form Fields */
          .form-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            min-height: 25px;
            font-size: 10px;
          }

          .field-label {
            font-weight: bold;
            margin-left: 8px;
            min-width: 100px;
            font-size: 10px;
            text-align: right;
            flex-shrink: 0;
          }

          .field-value {
            border-bottom: 1px solid #000;
            padding: 2px 6px;
            min-width: 120px;
            text-align: center;
            font-size: 10px;
          }

          .full-width-field {
            border-bottom: 1px solid #000;
            padding: 2px 6px;
            flex: 1;
            text-align: right;
            font-size: 10px;
            margin-right: 0;
          }

          /* محاذاة خاصة للصفوف الكاملة العرض */
          .full-width-row {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            min-height: 20px;
            font-size: 10px;
          }

          .full-width-row .field-label {
            width: 100px;
            min-width: 100px;
            text-align: right;
            margin-left: 8px;
            flex-shrink: 0;
          }

          /* Leave Type Section */
          .leave-types-container {
            display: flex;
            margin: 5px 0;
            font-size: 10px;
            border: 1px solid #000 !important;
          }

          .leave-types-left {
            width: 50%;
            padding: 8px;
            border-right: 1px solid #000 !important;
          }

          .leave-types-right {
            width: 50%;
            padding: 8px;
          }

          .leave-option {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            font-size: 10px;
          }

          .checkbox {
            width: 10px;
            height: 10px;
            border: 1px solid #000;
            margin-left: 6px;
            display: inline-block;
            text-align: center;
            line-height: 8px;
            font-size: 7px;
          }

          .checkbox.checked {
            background-color: #000;
            color: white;
          }

          .leave-duration {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 5px 0;
            padding: 5px;
            border: 1px solid #000;
          }

          /* Signatures Section */
          .signatures-container {
            margin-top: 20px;
          }

          .signature-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
          }

          .signature-box {
            text-align: center;
            width: 200px;
          }

          .signature-title {
            font-weight: bold;
            font-size: 10px;
            margin-bottom: 5px;
          }

          .signature-line {
            border-bottom: 1px solid #000;
            height: 20px;
            margin-bottom: 5px;
          }

          /* HR Section */
          .hr-section {
            margin-top: 20px;
            border: 1px solid #000;
          }

          .hr-header {
            background-color: #a8c5f0;
            text-align: center;
            padding: 8px;
            font-weight: bold;
            font-size: 12px;
            border-bottom: 1px solid #000;
          }

          .hr-content {
            padding: 15px;
          }

          .notes-lines {
            line-height: 2;
            margin-bottom: 15px;
          }

          .hr-specialist {
            text-align: center;
            margin: 15px 0;
          }

          .hr-notes {
            font-size: 10px;
            border-top: 1px solid #000;
            padding-top: 10px;
            margin-top: 15px;
          }

          @media print {
            @page {
              size: A4 portrait;
              margin: 10mm;
            }

            body {
              margin: 0 !important;
              padding: 0 !important;
              width: 190mm !important;
              max-width: 190mm !important;
              font-size: 10px !important;
            }

            .no-print {
              display: none !important;
            }

            * {
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
            }
          }
        </style>
      </head>
      <body>
        <!-- Header Table - مطابق للنموذج الأصلي تماماً -->
        <table class="header-table">
          <tr>
            <td class="company-cell">
              <div class="company-name-ar">شركة كونكورد للهندسة والمقاولات</div>
              <div class="company-name-en">Concord for Engineering & Contracting</div>
            </td>
            <td class="form-title-cell">
              <div class="form-title">طلب إجازة</div>
              <div class="form-code">HR-OP-01-F01</div>
            </td>
            <td class="logo-cell">
              ${logoBase64 ?
                `<img src="data:image/png;base64,${logoBase64}" alt="Concord Logo" class="logo-img" />` :
                `<div style="background:#1e40af;color:white;padding:8px;text-align:center;font-weight:bold;font-size:10px;border-radius:4px;">CONCORD<br>COMPANY</div>`
              }
            </td>
          </tr>
        </table>

        <!-- Section Header -->
        <div class="section-header">بيانات الطلب</div>

        <!-- Employee Information - مطابق للنموذج الأصلي -->
        <div class="form-row" style="justify-content: space-between; align-items: center; margin-bottom: 10px;">
          <div style="display: flex; align-items: center;">
            <span class="field-label" style="margin-left: 8px;">الاسم:</span>
            <div class="field-value" style="width: 300px; text-align: center;">${requestData.EmployeeName || ''}</div>
          </div>
          <div style="display: flex; align-items: center;">
            <span class="field-label" style="margin-left: 8px;">الكود الوظيفي:</span>
            <div class="field-value" style="width: 100px; text-align: center;">${requestData.EmployeeCode || ''}</div>
          </div>
        </div>

        <div class="full-width-row">
          <span class="field-label">الوظيفة:</span>
          <div class="full-width-field">${requestData.JobTitle || ''}</div>
        </div>

        <div class="full-width-row">
          <span class="field-label">الإدارة / المشروع:</span>
          <div class="full-width-field">مشروع مجمع مبانى أوجيستا</div>
        </div>

        <!-- البيانات الإضافية المطلوبة -->
        <div class="form-row" style="justify-content: space-between; align-items: center; margin-bottom: 10px;">
          <div style="display: flex; align-items: center;">
            <span class="field-label" style="margin-left: 8px;">تاريخ الطباعة:</span>
            <div class="field-value" style="width: 120px; text-align: center;">${formatDate(new Date())}</div>
          </div>
          <div style="display: flex; align-items: center;">
            <span class="field-label" style="margin-left: 8px;">تاريخ آخر إجازة:</span>
            <div class="field-value" style="width: 120px; text-align: center;">${lastLeaveDate}</div>
          </div>
          <div style="display: flex; align-items: center;">
            <span class="field-label" style="margin-left: 8px;">الرصيد المتبقي:</span>
            <div class="field-value" style="width: 100px; text-align: center;">${remainingBalance}</div>
          </div>
        </div>

        <!-- Leave Types Section - مطابق للنموذج الأصلي مع إضافة الضلع الناقص -->
        <div class="leave-types-container" style="border: 1px solid #000;">
          <div class="leave-types-left" style="border-right: 1px solid #000;">
            <div class="leave-option">
              <span class="checkbox ${requestData.LeaveType === 'اعتيادية' || requestData.LeaveType === 'annual' ? 'checked' : ''}">
                ${requestData.LeaveType === 'اعتيادية' || requestData.LeaveType === 'annual' ? '■' : ''}
              </span>
              <span>إجازة إعتيادية</span>
            </div>
            <div class="leave-option">
              <span class="checkbox ${requestData.LeaveType === 'مرضية' || requestData.LeaveType === 'sick' ? 'checked' : ''}">
                ${requestData.LeaveType === 'مرضية' || requestData.LeaveType === 'sick' ? '■' : ''}
              </span>
              <span>إجازة مرضية / إصابة</span>
            </div>
          </div>
          <div class="leave-types-right">
            <div class="leave-option">
              <span class="checkbox ${requestData.LeaveType === 'عارضة' || requestData.LeaveType === 'emergency' ? 'checked' : ''}">
                ${requestData.LeaveType === 'عارضة' || requestData.LeaveType === 'emergency' ? '■' : ''}
              </span>
              <span>إجازة عارضة</span>
            </div>
            <div class="leave-option">
              <span class="checkbox ${requestData.LeaveType === 'بدون أجر' || requestData.LeaveType === 'unpaid' ? 'checked' : ''}">
                ${requestData.LeaveType === 'بدون أجر' || requestData.LeaveType === 'unpaid' ? '■' : ''}
              </span>
              <span>إجازة بدون أجر</span>
            </div>
          </div>
        </div>

        <div style="text-align: center; font-size: 10px; margin: 5px 0;">
          ( بدل - حج - عمرة - وضع - ولادة - وفاة - زواج )
        </div>
        <div style="text-align: center; font-size: 10px; margin-bottom: 10px;">
          ( إجازات أخرى )
        </div>

        <div class="leave-option" style="text-align: center; margin: 10px 0;">
          <span class="checkbox ${requestData.LeaveType === 'بدل' || requestData.LeaveType === 'badal' ? 'checked' : ''}">
            ${requestData.LeaveType === 'بدل' || requestData.LeaveType === 'badal' ? '■' : ''}
          </span>
          <span>إجازة بدل</span>
        </div>

        <!-- Leave Duration - مطابق للنموذج الأصلي -->
        <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
          <tr>
            <td style="border: 1px solid #000; padding: 8px; text-align: center; width: 33.33%;">
              <span style="font-weight: bold;">عدد الأيام:</span>
              <div style="margin-top: 5px;">
                <span style="border-bottom: 1px solid #000; padding: 2px 8px; margin: 0 5px;">${requestData.DaysCount || ''}</span>
              </div>
            </td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center; width: 33.33%;">
              <span style="font-weight: bold;">إلى:</span>
              <div style="margin-top: 5px;">
                <span style="border-bottom: 1px solid #000; padding: 2px 8px; margin: 0 5px;">${formatDate(requestData.EndDate) || ''}</span>
              </div>
            </td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center; width: 33.33%;">
              <span style="font-weight: bold;">مدة الإجازة من:</span>
              <div style="margin-top: 5px;">
                <span style="border-bottom: 1px solid #000; padding: 2px 8px; margin: 0 5px;">${formatDate(requestData.StartDate) || ''}</span>
              </div>
            </td>
          </tr>
        </table>

        <!-- Signatures Section - مطابق للنموذج الأصلي -->
        <div style="margin: 15px 0;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
            <div style="text-align: center; width: 45%;">
              <div style="font-weight: bold; margin-bottom: 30px;">توقيع الموظف</div>
              <div style="border-bottom: 1px solid #000; width: 150px; margin: 0 auto;"></div>
            </div>
            <div style="text-align: center; width: 45%;">
              <div style="font-weight: bold; margin-bottom: 30px;">اعتماد الرئيس المباشر</div>
              <div style="border-bottom: 1px solid #000; width: 150px; margin: 0 auto;"></div>
            </div>
          </div>
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
            <div style="text-align: center; width: 45%;">
              <div style="font-weight: bold; margin-bottom: 30px;">المدير الإداري</div>
              <div style="border-bottom: 1px solid #000; width: 150px; margin: 0 auto;"></div>
            </div>
            <div style="text-align: center; width: 45%;">
              <div style="font-weight: bold; margin-bottom: 30px;">اعتماد مدير المشروع</div>
              <div style="border-bottom: 1px solid #000; width: 150px; margin: 0 auto;"></div>
            </div>
          </div>
        </div>

        <!-- HR Section - مطابق للنموذج الأصلي -->
        <div class="hr-section">
          <div class="hr-header">إدارة الموارد البشرية</div>
          <div class="hr-content">
            <!-- الملاحظات في الأعلى -->
            <div style="text-align: right; margin-bottom: 20px;">
              <div style="font-weight: bold; margin-bottom: 8px;">ملاحظات:</div>
              <div style="line-height: 1.8;">
                ................................................................................................................................................................................................<br>
                ................................................................................................................................................................................................
              </div>
            </div>

            <!-- توقيع أخصائي موارد بشرية تحت الملاحظات -->
            <div style="text-align: left; margin-bottom: 15px;">
              <div style="font-weight: bold; margin-bottom: 30px;">أخصائي موارد بشرية</div>
              <div style="border-bottom: 1px solid #000; width: 150px; height: 15px;"></div>
            </div>

            <!-- الملاحظات السفلية -->
            <div style="font-size: 10px; text-align: right; border-top: 1px solid #000; padding-top: 10px;">
              <div style="margin-bottom: 3px;">
                في حالة الإجازة المرضية يتم إرفاق التقرير الطبي.
              </div>
              <div>
                في حالة عدم وجود رصيد إجازات سنوية يتم احتساب الطلب إجازة بدون أجر.
              </div>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();

    // انتظار تحميل المحتوى والصور قبل الطباعة
    printWindow.onload = () => {
      setTimeout(() => {
        printWindow.print();
      }, 500);
    };

    // احتياطي في حالة عدم تشغيل onload
    setTimeout(() => {
      printWindow.print();
    }, 1500);
  };

  // طباعة الإجازة (نفس النموذج من صفحة "طلباتي")
  const printLeave = async (leaveData) => {
    try {
      // استخدام نفس دالة الطباعة من صفحة "طلباتي"
      await printLeaveRequestCorrect(leaveData);
    } catch (error) {

      alert('خطأ في الطباعة');
    }
  };

  useEffect(() => {
    fetchApprovedLeaves();
  }, [filters]);

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto">
          {/* العنوان */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-200 mb-2">
                  {isArabic ? 'الإجازات المعتمدة' : 'Approved Leaves'}
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  {isArabic ? 'عرض وإدارة الإجازات المعتمدة' : 'View and manage approved leaves'}
                </p>
              </div>
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                <FiCalendar className="text-2xl text-white" />
              </div>
            </div>
          </div>

          {/* الفلاتر */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center gap-2">
              <FiFilter className="text-blue-600" />
              {isArabic ? 'فلاتر البحث' : 'Search Filters'}
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {isArabic ? 'الشهر' : 'Month'}
                </label>
                <select
                  value={filters.month}
                  onChange={(e) => handleFilterChange('month', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
                >
                  {months.map((month) => (
                    <option key={month.value} value={month.value}>
                      {month.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {isArabic ? 'السنة' : 'Year'}
                </label>
                <select
                  value={filters.year}
                  onChange={(e) => handleFilterChange('year', parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
                >
                  {[2023, 2024, 2025, 2026].map((year) => (
                    <option key={year} value={year}>{year}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {isArabic ? 'كود الموظف' : 'Employee Code'}
                </label>
                <input
                  type="text"
                  value={filters.employeeCode}
                  onChange={(e) => handleFilterChange('employeeCode', e.target.value)}
                  placeholder={isArabic ? 'أدخل كود الموظف...' : 'Enter employee code...'}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {isArabic ? 'اسم الموظف' : 'Employee Name'}
                </label>
                <input
                  type="text"
                  value={filters.employeeName}
                  onChange={(e) => handleFilterChange('employeeName', e.target.value)}
                  placeholder={isArabic ? 'أدخل اسم الموظف...' : 'Enter employee name...'}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
                />
              </div>
            </div>

            <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
              <p>
                {isArabic ? 'فترة الشهر المحددة:' : 'Selected month period:'} 
                {' '}
                {(() => {
                  const { startDate, endDate } = getMonthPeriod(filters.month, filters.year);
                  return `${formatDate(startDate)} - ${formatDate(endDate)}`;
                })()}
              </p>
            </div>
          </div>

          {/* جدول الإجازات المعتمدة */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
                {isArabic ? 'الإجازات المعتمدة' : 'Approved Leaves'}
                <span className="text-sm text-gray-500 ml-2">({approvedLeaves.length})</span>
              </h2>
            </div>

            {loading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600 dark:text-gray-400">
                  {isArabic ? 'جاري التحميل...' : 'Loading...'}
                </p>
              </div>
            ) : approvedLeaves.length === 0 ? (
              <div className="p-8 text-center">
                <FiCalendar className="mx-auto h-12 w-12 text-gray-400" />
                <p className="mt-2 text-gray-600 dark:text-gray-400">
                  {isArabic ? 'لا توجد إجازات معتمدة في هذه الفترة' : 'No approved leaves in this period'}
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'الموظف' : 'Employee'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'نوع الإجازة' : 'Leave Type'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'من - إلى' : 'From - To'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'عدد الأيام' : 'Days'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'تاريخ الاعتماد' : 'Approval Date'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'الإجراءات' : 'Actions'}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {approvedLeaves.map((leave) => (
                      <tr key={leave.ID} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {leave.EmployeeName}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {leave.EmployeeCode}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                            {getLeaveTypeLabel(leave.LeaveType)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          <div>{formatDate(leave.StartDate)}</div>
                          <div className="text-gray-500">إلى {formatDate(leave.EndDate)}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          <span className="font-medium">{leave.DaysCount}</span> يوم
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {formatDate(leave.ApprovalDate)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() => printLeave(leave)}
                            className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 flex items-center gap-1"
                          >
                            <FiPrinter />
                            {isArabic ? 'طباعة' : 'Print'}
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
