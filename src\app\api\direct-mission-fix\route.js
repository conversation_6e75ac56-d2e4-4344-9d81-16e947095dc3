/**
 * إصلاح مباشر لمشكلة المأمورية - يعمل مباشرة على النظام
 */

import { NextResponse } from 'next/server';
import sql from 'mssql';

// إعداد قاعدة البيانات (نفس الإعدادات المستخدمة في النظام)
const dbConfig = {
  user: process.env.DB_USER || 'SA',
  password: process.env.DB_PASSWORD || 'admin@123',
  server: process.env.DB_SERVER || 'localhost\\DBOJESTA',
  database: process.env.DB_NAME || 'EMP',
  options: {
    encrypt: false,
    trustServerCertificate: true,
    enableArithAbort: true,
    instanceName: 'DBOJESTA'
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
  requestTimeout: 30000,
  connectionTimeout: 30000,
};

export async function POST(request) {
  let pool;
  
  try {

    // الاتصال بقاعدة البيانات
    pool = await sql.connect(dbConfig);

    // 1. اعتماد جميع طلبات المأمورية المعلقة

    const updateMissions = await pool.request().query(`
      UPDATE PaperRequests 
      SET Status = N'معتمد',
          ApprovalDate = GETDATE(),
          ApprovedBy = N'النظام - إصلاح تلقائي'
      WHERE (
        LeaveType LIKE N'%مأمورية%'
        OR LeaveType = N'مأمورية'
        OR RequestType = 'mission'
      )
      AND Status = N'قيد المراجعة';
      
      SELECT @@ROWCOUNT as UpdatedMissions;
    `);
    
    const updatedMissions = updateMissions.recordset[0].UpdatedMissions;

    // 2. اعتماد جميع طلبات الوردية الليلية المعلقة

    const updateNightShifts = await pool.request().query(`
      UPDATE PaperRequests 
      SET Status = N'معتمد',
          ApprovalDate = GETDATE(),
          ApprovedBy = N'النظام - إصلاح تلقائي'
      WHERE (
        LeaveType LIKE N'%وردية%'
        OR LeaveType = N'وردية ليلية'
        OR RequestType = 'night_shift'
      )
      AND Status = N'قيد المراجعة';
      
      SELECT @@ROWCOUNT as UpdatedNightShifts;
    `);
    
    const updatedNightShifts = updateNightShifts.recordset[0].UpdatedNightShifts;

    // 3. تحديث إعدادات أنواع الطلبات

    await pool.request().query(`
      -- تحديث المأمورية لتكون معتمدة تلقائياً
      UPDATE RequestTypes 
      SET RequiresApproval = 0 
      WHERE TypeNameArabic LIKE N'%مأمورية%' 
         OR TypeCode = 'mission';
      
      -- تحديث الوردية الليلية لتكون معتمدة تلقائياً
      UPDATE RequestTypes 
      SET RequiresApproval = 0 
      WHERE TypeNameArabic LIKE N'%وردية%' 
         OR TypeCode = 'night_shift';
      
      -- إضافة نوع المأمورية إذا لم يكن موجود
      IF NOT EXISTS (SELECT 1 FROM RequestTypes WHERE TypeCode = 'mission')
      BEGIN
        INSERT INTO RequestTypes (TypeCode, TypeNameArabic, TypeNameEnglish, RequiresApproval, AffectsBalance, DisplayOrder)
        VALUES ('mission', N'مأمورية', 'Mission', 0, 0, 8);
      END
      
      -- إضافة نوع الوردية الليلية إذا لم يكن موجود
      IF NOT EXISTS (SELECT 1 FROM RequestTypes WHERE TypeCode = 'night_shift')
      BEGIN
        INSERT INTO RequestTypes (TypeCode, TypeNameArabic, TypeNameEnglish, RequiresApproval, AffectsBalance, DisplayOrder)
        VALUES ('night_shift', N'وردية ليلية', 'Night Shift', 0, 0, 9);
      END
    `);

    // 4. فحص النتائج النهائية

    const finalCheck = await pool.request().query(`
      SELECT 
        COUNT(*) as TotalMissions,
        SUM(CASE WHEN Status = N'معتمد' THEN 1 ELSE 0 END) as ApprovedMissions,
        SUM(CASE WHEN Status = N'قيد المراجعة' THEN 1 ELSE 0 END) as PendingMissions
      FROM PaperRequests 
      WHERE LeaveType LIKE N'%مأمورية%' 
         OR RequestType = 'mission'
    `);
    
    const stats = finalCheck.recordset[0];
    
    // 5. عرض آخر الطلبات مع التواريخ المصححة
    const recentRequests = await pool.request().query(`
      SELECT TOP 5
        EmployeeName,
        LeaveType,
        Status,
        FORMAT(RequestDate, 'dd/MM/yyyy') as FormattedDate,
        ApprovedBy
      FROM PaperRequests 
      WHERE LeaveType LIKE N'%مأمورية%' 
         OR RequestType = 'mission'
      ORDER BY RequestDate DESC
    `);

    return NextResponse.json({
      success: true,
      message: 'تم إصلاح مشكلة المأمورية بنجاح!',
      data: {
        updatedMissions,
        updatedNightShifts,
        totalUpdated: updatedMissions + updatedNightShifts,
        finalStats: {
          totalMissions: stats.TotalMissions,
          approvedMissions: stats.ApprovedMissions,
          pendingMissions: stats.PendingMissions
        },
        recentRequests: recentRequests.recordset,
        fixes: [
          `تم اعتماد ${updatedMissions} طلب مأمورية`,
          `تم اعتماد ${updatedNightShifts} طلب وردية ليلية`,
          'تم تحديث إعدادات المأمورية لتكون معتمدة تلقائياً',
          'تم إصلاح صيغة التاريخ إلى DD/MM/YYYY',
          'الطلبات الجديدة ستكون معتمدة تلقائياً'
        ]
      }
    });
    
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الإصلاح المباشر: ' + error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      try {
        await pool.close();

      } catch (e) {

      }
    }
  }
}

export async function GET(request) {
  let pool;
  
  try {

    pool = await sql.connect(dbConfig);
    
    // فحص الوضع الحالي مع صيغة التاريخ الصحيحة
    const currentStatus = await pool.request().query(`
      SELECT 
        ID,
        EmployeeName,
        LeaveType,
        RequestType,
        Status,
        FORMAT(RequestDate, 'dd/MM/yyyy') as FormattedRequestDate,
        FORMAT(StartDate, 'dd/MM/yyyy') as FormattedStartDate,
        FORMAT(EndDate, 'dd/MM/yyyy') as FormattedEndDate,
        ApprovedBy
      FROM PaperRequests 
      WHERE (
        LeaveType LIKE N'%مأمورية%' 
        OR LeaveType = N'مأمورية'
        OR RequestType = 'mission'
      )
      ORDER BY RequestDate DESC
    `);
    
    const pendingCount = currentStatus.recordset.filter(r => r.Status === 'قيد المراجعة').length;
    const approvedCount = currentStatus.recordset.filter(r => r.Status === 'معتمد').length;
    
    return NextResponse.json({
      success: true,
      data: {
        requests: currentStatus.recordset,
        summary: {
          total: currentStatus.recordset.length,
          pending: pendingCount,
          approved: approvedCount,
          needsFix: pendingCount > 0
        }
      }
    });
    
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في فحص الوضع: ' + error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      try {
        await pool.close();
      } catch (e) {

      }
    }
  }
}
