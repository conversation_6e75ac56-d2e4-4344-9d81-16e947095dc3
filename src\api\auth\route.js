async function handler({
  email,
  password,
  action = 'login',
  token,
  newPassword,
}) {
  const session = getSession();

  if (action === 'login') {
    if (!email || !password) {
      return { error: 'يجب توفير البريد الإلكتروني وكلمة المرور' };
    }

    const user = await sql`
      SELECT u.*, r.name as role_name 
      FROM users u
      LEFT JOIN roles r ON u.role_id = r.id
      WHERE u.email = ${email}
    `;

    if (!user.length) {
      return { error: 'البريد الإلكتروني غير صحيح' };
    }

    const userData = user[0];

    if (!userData.is_active) {
      return { error: 'الحساب غير نشط' };
    }

    if (userData.account_locked && userData.account_locked_until > new Date()) {
      return { error: 'الحساب مقفل مؤقتاً. يرجى المحاولة لاحقاً' };
    }

    if (userData.password_hash !== password) {
      const failedAttempts = (userData.failed_login_attempts || 0) + 1;

      if (failedAttempts >= 5) {
        const lockUntil = new Date(Date.now() + 30 * 60000); // 30 minutes
        await sql`
          UPDATE users 
          SET failed_login_attempts = ${failedAttempts},
              account_locked = true,
              account_locked_until = ${lockUntil}
          WHERE id = ${userData.id}
        `;
        return {
          error:
            'تم قفل الحساب لمدة 30 دقيقة بسبب محاولات تسجيل دخول فاشلة متكررة',
        };
      }

      await sql`
        UPDATE users 
        SET failed_login_attempts = ${failedAttempts}
        WHERE id = ${userData.id}
      `;

      return { error: 'كلمة المرور غير صحيحة' };
    }

    await sql`
      UPDATE users 
      SET failed_login_attempts = 0,
          account_locked = false,
          account_locked_until = null,
          last_login = CURRENT_TIMESTAMP
      WHERE id = ${userData.id}
    `;

    return {
      success: true,
      user: {
        id: userData.id,
        email: userData.email,
        role: userData.role_name,
        employee_id: userData.employee_id,
      },
    };
  }

  if (action === 'reset-password') {
    if (!token || !newPassword) {
      return { error: 'يجب توفير رمز إعادة التعيين وكلمة المرور الجديدة' };
    }

    const user = await sql`
      SELECT * FROM users 
      WHERE password_reset_token = ${token}
      AND password_reset_expires > CURRENT_TIMESTAMP
    `;

    if (!user.length) {
      return { error: 'رمز إعادة التعيين غير صالح أو منتهي الصلاحية' };
    }

    await sql`
      UPDATE users 
      SET password_hash = ${newPassword},
          password_reset_token = null,
          password_reset_expires = null,
          failed_login_attempts = 0,
          account_locked = false,
          account_locked_until = null
      WHERE id = ${user[0].id}
    `;

    return { success: true, message: 'تم تغيير كلمة المرور بنجاح' };
  }

  return { error: 'إجراء غير صالح' };
}
