import { NextResponse } from 'next/server';
import ExcelJS from 'exceljs';
import sql from 'mssql';

// إعدادات قاعدة البيانات
const config = {
  server: process.env.DB_SERVER || 'localhost',
  database: process.env.DB_NAME || 'EmployeeDB',
  user: process.env.DB_USER || 'sa',
  password: process.env.DB_PASSWORD || 'YourPassword123',
  options: {
    encrypt: false,
    trustServerCertificate: true,
    enableArithAbort: true,
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
};

export async function POST(request) {
  let pool;

  try {

    // الحصول على الملف من الطلب
    const formData = await request.formData();
    const file = formData.get('file');

    if (!file) {
      return NextResponse.json({
        success: false,
        message: 'لم يتم العثور على ملف'
      }, { status: 400 });
    }

    // التحقق من نوع الملف
    if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      return NextResponse.json({
        success: false,
        message: 'نوع الملف غير مدعوم. يرجى استخدام ملف Excel (.xlsx أو .xls)'
      }, { status: 400 });
    }

    // قراءة محتوى الملف
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // قراءة ملف Excel
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(buffer);

    const worksheet = workbook.getWorksheet('الموظفين') || workbook.getWorksheet(1);

    if (!worksheet) {
      return NextResponse.json({
        success: false,
        message: 'لم يتم العثور على ورقة عمل صحيحة في الملف'
      }, { status: 400 });
    }

    // الاتصال بقاعدة البيانات
    pool = await sql.connect(config);

    // قراءة البيانات من Excel
    const employees = [];
    const errors = [];
    let rowIndex = 2; // البدء من الصف الثاني (تجاهل العناوين)

    // تعريف مطابقة الأعمدة (محدث للتصميم الجديد)
    const columnMapping = {
      A: 'employeeCode',
      B: 'fullName',
      C: 'jobTitle',
      D: 'department',
      E: 'directManager',
      F: 'nationalId',
      G: 'birthDate',
      H: 'gender',
      I: 'governorate',
      J: 'area',
      K: 'maritalStatus',
      L: 'mobile',
      M: 'email',
      N: 'emergencyNumber',
      O: 'kinship',
      P: 'hireDate',
      Q: 'joinDate',
      R: 'employeeStatus',
      S: 'militaryService',
      T: 'isResident',
      // حقول الشقق والسيارات تم إزالتها - سيتم إدارتها من جداول المستفيدين
      U: 'education',
      V: 'university',
      W: 'specialization',
      X: 'grade',
      Y: 'graduationYear',
      Z: 'socialInsurance',
      AA: 'socialInsuranceNumber',
      AB: 'socialInsuranceDate',
      AC: 'medicalInsurance',
      AD: 'medicalInsuranceNumber'
    };

    // قراءة كل صف
    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber === 1) return; // تجاهل صف العناوين

      try {
        const employee = {};
        let hasData = false;

        // قراءة كل عمود
        let hasEssentialData = false; // للتحقق من وجود البيانات الأساسية

        Object.keys(columnMapping).forEach(col => {
          const cell = row.getCell(col);
          let value = cell.value;

          // معالجة خاصة للقيم المختلفة
          if (value !== null && value !== undefined) {
            // إذا كانت القيمة كائن (مثل hyperlink)، استخدم النص
            if (typeof value === 'object' && value.text) {
              value = value.text;
            }

            // معالجة خاصة للرقم القومي (إصلاح التنسيق العلمي)
            if (columnMapping[col] === 'nationalId') {
              if (typeof value === 'number') {
                // تحويل من التنسيق العلمي إلى رقم كامل
                value = value.toFixed(0);
              }
              // التأكد من أن الرقم القومي 14 رقم
              const nationalIdStr = String(value).trim();
              if (nationalIdStr.length === 14 && /^\d{14}$/.test(nationalIdStr)) {
                employee[columnMapping[col]] = nationalIdStr;
                hasData = true;
                hasEssentialData = true; // الرقم القومي من البيانات الأساسية
              } else if (nationalIdStr !== '') {
                console.log(`⚠️ رقم قومي غير صحيح في الصف ${rowNumber}: ${nationalIdStr} (الطول: ${nationalIdStr.length})`);
              }
            }
            // معالجة خاصة لكود الموظف (قد يكون رقم)
            else if (columnMapping[col] === 'employeeCode') {
              if (typeof value === 'number') {
                value = value.toString();
              }
              const codeStr = String(value).trim();
              if (codeStr !== '') {
                employee[columnMapping[col]] = codeStr;
                hasData = true;
                hasEssentialData = true; // كود الموظف من البيانات الأساسية
              }
            }
            // معالجة خاصة لاسم الموظف
            else if (columnMapping[col] === 'fullName') {
              const nameStr = String(value).trim();
              if (nameStr !== '') {
                employee[columnMapping[col]] = nameStr;
                hasData = true;
                hasEssentialData = true; // اسم الموظف من البيانات الأساسية
              }
            }
            // معالجة خاصة للتواريخ
            else if (['birthDate', 'hireDate', 'joinDate'].includes(columnMapping[col])) {
              if (value instanceof Date) {
                employee[columnMapping[col]] = value.toISOString().split('T')[0];
                hasData = true;
              } else if (typeof value === 'string') {
                const dateStr = String(value).trim();
                if (dateStr !== '') {
                  employee[columnMapping[col]] = dateStr;
                  hasData = true;
                }
              }
            }
            // معالجة عامة للحقول الأخرى
            else {
              const stringValue = String(value).trim();
              if (stringValue !== '') {
                employee[columnMapping[col]] = stringValue;
                hasData = true;
              }
            }
          }
        });

        // تحديث hasData ليعتمد على وجود البيانات الأساسية الكاملة
        // يجب أن تكون الثلاث حقول الأساسية موجودة: كود الموظف، الاسم، والرقم القومي
        const hasAllEssentials = employee.employeeCode && employee.fullName && employee.nationalId &&
                                employee.employeeCode.trim() !== '' &&
                                employee.fullName.trim() !== '' &&
                                employee.nationalId.trim() !== '';

        hasData = hasAllEssentials;

        // تسجيل البيانات المقروءة للتشخيص

        // إذا كان الصف يحتوي على بيانات، أضفه للقائمة
        if (hasData) {
          // التحقق من أن هذا ليس صف عناوين
          if (employee.fullName && (
            employee.fullName.includes('اسم الموظف') ||
            employee.fullName.includes('الاسم') ||
            employee.fullName.includes('Name') ||
            employee.employeeCode === 'كود الموظف' ||
            employee.employeeCode === 'Code'
          )) {

            return;
          }

          // التحقق من الحقول الأساسية المطلوبة دائماً
          const alwaysRequiredFields = ['employeeCode', 'fullName', 'nationalId'];

          const missingAlwaysRequired = alwaysRequiredFields.filter(field =>
            !employee[field] || employee[field].trim() === ''
          );

          if (missingAlwaysRequired.length > 0) {
            console.log(`❌ الصف ${rowNumber}: حقول مفقودة: ${missingAlwaysRequired.join(', ')}`);

            errors.push(`الصف ${rowNumber}: حقول أساسية مطلوبة: ${missingAlwaysRequired.join(', ')}`);
            return;
          }

          // التحقق من صحة الرقم القومي
          if (employee.nationalId && employee.nationalId.length !== 14) {
            errors.push(`الصف ${rowNumber}: الرقم القومي يجب أن يكون 14 رقم`);
            return;
          }

          employees.push({ ...employee, rowNumber });
        } else {
          // إذا كان الصف فارغ أو يحتوي على بيانات جزئية، تجاهله بصمت
          if (hasEssentialData) {
            // الصف يحتوي على بعض البيانات الأساسية ولكن ليس كلها

          } else {
            // الصف فارغ تماماً

          }
          return;
        }
      } catch (error) {
        errors.push(`الصف ${rowNumber}: خطأ في قراءة البيانات - ${error.message}`);
      }
    });

    if (employees.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'لم يتم العثور على بيانات صحيحة في الملف',
        errors: errors
      }, { status: 400 });
    }

    // إدراج البيانات في قاعدة البيانات
    let successCount = 0;
    let failedCount = 0;
    const addedEmployees = [];
    const updatedEmployees = [];
    const failedEmployees = [];

    for (const employee of employees) {
      try {
        // التحقق من وجود الموظف بالرقم القومي أو كود الموظف
        const checkRequest = pool.request();
        checkRequest.input('NationalID', sql.NVarChar, employee.nationalId);
        checkRequest.input('EmployeeCode', sql.NVarChar, employee.employeeCode);

        const existingEmployee = await checkRequest.query(`
          SELECT EmployeeCode, EmployeeName, NationalID FROM Employees
          WHERE NationalID = @NationalID OR EmployeeCode = @EmployeeCode
        `);

        console.log(`🔍 فحص الموظف: ${employee.fullName} (كود: ${employee.employeeCode}, رقم قومي: ${employee.nationalId})`);

        if (existingEmployee.recordset.length > 0) {
          const existing = existingEmployee.recordset[0];
          console.log(`📋 موظف موجود: ${existing.EmployeeName} (كود: ${existing.EmployeeCode}, رقم قومي: ${existing.NationalID})`);
        }

        const request = pool.request();

        // إضافة المعاملات
        request.input('EmployeeCode', sql.NVarChar, employee.employeeCode);
        request.input('EmployeeName', sql.NVarChar, employee.fullName); // استخدام EmployeeName بدلاً من FullName
        request.input('JobTitle', sql.NVarChar, employee.jobTitle);
        request.input('Department', sql.NVarChar, employee.department);
        request.input('DirectManager', sql.NVarChar, employee.directManager);
        request.input('NationalID', sql.NVarChar, employee.nationalId);
        request.input('BirthDate', sql.Date, new Date(employee.birthDate));
        request.input('Gender', sql.NVarChar, employee.gender);
        request.input('Governorate', sql.NVarChar, employee.governorate);
        request.input('Area', sql.NVarChar, employee.area || null);
        request.input('MaritalStatus', sql.NVarChar, employee.maritalStatus);
        request.input('Mobile', sql.NVarChar, employee.mobile || null);
        request.input('Email', sql.NVarChar, employee.email || null);
        request.input('EmergencyNumber', sql.NVarChar, employee.emergencyNumber || null);
        request.input('Kinship', sql.NVarChar, employee.kinship || null);
        request.input('HireDate', sql.Date, new Date(employee.hireDate));
        request.input('JoinDate', sql.Date, new Date(employee.joinDate));
        request.input('CurrentStatus', sql.NVarChar, employee.employeeStatus);
        request.input('MilitaryService', sql.NVarChar, employee.militaryService);
        request.input('IsResidentEmployee', sql.NVarChar, employee.isResident === 'نعم' || employee.isResident === 'true' || employee.isResident === true ? 'نعم' : 'لا');
        // حقول الشقق والسيارات تم إزالتها
        request.input('Education', sql.NVarChar, employee.education || null);
        request.input('University', sql.NVarChar, employee.university || null);
        request.input('Major', sql.NVarChar, employee.specialization || null);
        request.input('Grade', sql.NVarChar, employee.grade || null);
        request.input('Batch', sql.NVarChar, employee.graduationYear || null);
        // حقول التأمين الجديدة مع معالجة أفضل للتواريخ
        request.input('SocialInsurance', sql.NVarChar, employee.socialInsurance || null);
        request.input('SocialInsuranceNumber', sql.NVarChar, employee.socialInsuranceNumber || null);

        // معالجة تاريخ التأمين الاجتماعي
        let socialInsuranceDate = null;
        if (employee.socialInsuranceDate) {
          try {
            const dateStr = employee.socialInsuranceDate.toString().trim();
            if (dateStr && dateStr !== '' && dateStr !== 'null' && dateStr !== 'undefined') {
              // محاولة تحويل التاريخ
              const parsedDate = new Date(dateStr);
              if (!isNaN(parsedDate.getTime()) && parsedDate.getFullYear() > 1900 && parsedDate.getFullYear() < 2100) {
                socialInsuranceDate = parsedDate;
              }
            }
          } catch (dateError) {

          }
        }
        request.input('SocialInsuranceDate', sql.Date, socialInsuranceDate);

        request.input('MedicalInsurance', sql.NVarChar, employee.medicalInsurance || null);
        request.input('MedicalInsuranceNumber', sql.NVarChar, employee.medicalInsuranceNumber || null);

        let operationType = '';
        let result;

        if (existingEmployee.recordset.length > 0) {
          // تحديث الموظف الموجود - تحديث ذكي (فقط الحقول المملوءة)

          // بناء استعلام التحديث الديناميكي - فقط للحقول المملوءة
          const updateFields = [];
          const updateParams = {};

          // الحقول الأساسية (دائماً تحديث) - لا نحدث EmployeeCode لأنه PRIMARY KEY
          updateFields.push('EmployeeName = @EmployeeName');

          // الحقول الاختيارية (تحديث فقط إذا كانت مملوءة) - بأسماء الأعمدة الصحيحة
          if (employee.jobTitle && employee.jobTitle.trim()) {
            updateFields.push('JobTitle = @JobTitle');
          }
          if (employee.department && employee.department.trim()) {
            updateFields.push('Department = @Department');
          }
          if (employee.directManager && employee.directManager.trim()) {
            updateFields.push('direct = @DirectManager');
          }
          if (employee.birthDate) {
            updateFields.push('BirthDate = @BirthDate');
          }
          if (employee.gender && employee.gender.trim()) {
            updateFields.push('Gender = @Gender');
          }
          if (employee.governorate && employee.governorate.trim()) {
            updateFields.push('Governorate = @Governorate');
          }
          if (employee.area && employee.area.trim()) {
            updateFields.push('area = @Area');
          }
          if (employee.maritalStatus && employee.maritalStatus.trim()) {
            updateFields.push('MaritalStatus = @MaritalStatus');
          }
          if (employee.mobile && employee.mobile.trim()) {
            updateFields.push('Mobile = @Mobile');
          }
          if (employee.email && employee.email.trim()) {
            updateFields.push('email = @Email');
          }
          if (employee.emergencyNumber && employee.emergencyNumber.trim()) {
            updateFields.push('emrnum = @EmergencyNumber');
          }
          if (employee.kinship && employee.kinship.trim()) {
            updateFields.push('Kinship = @Kinship');
          }
          if (employee.hireDate) {
            updateFields.push('HireDate = @HireDate');
          }
          if (employee.joinDate) {
            updateFields.push('JoinDate = @JoinDate');
          }
          if (employee.employeeStatus && employee.employeeStatus.trim()) {
            updateFields.push('CurrentStatus = @CurrentStatus');
          }
          if (employee.militaryService && employee.militaryService.trim()) {
            updateFields.push('Mserv = @MilitaryService');
          }
          if (employee.isResident && employee.isResident.trim()) {
            updateFields.push('IsResidentEmployee = @IsResidentEmployee');
          }
          if (employee.education && employee.education.trim()) {
            updateFields.push('Education = @Education');
          }
          if (employee.university && employee.university.trim()) {
            updateFields.push('University = @University');
          }
          if (employee.specialization && employee.specialization.trim()) {
            updateFields.push('Major = @Major');
          }
          if (employee.grade && employee.grade.trim()) {
            updateFields.push('Grade = @Grade');
          }
          if (employee.graduationYear && employee.graduationYear.trim()) {
            updateFields.push('Batch = @Batch');
          }
          if (employee.socialInsurance && employee.socialInsurance.trim()) {
            updateFields.push('SocialInsurance = @SocialInsurance');
          }
          if (employee.socialInsuranceNumber && employee.socialInsuranceNumber.trim()) {
            updateFields.push('SocialInsureNum = @SocialInsuranceNumber');
          }
          if (employee.socialInsuranceDate) {
            updateFields.push('spcialInsDate = @SocialInsuranceDate');
          }
          if (employee.medicalInsurance && employee.medicalInsurance.trim()) {
            updateFields.push('MedicalInsurance = @MedicalInsurance');
          }
          if (employee.medicalInsuranceNumber && employee.medicalInsuranceNumber.trim()) {
            updateFields.push('MedicalInsuranceNum = @MedicalInsuranceNumber');
          }

          // تحديد الموظف للتحديث (بالرقم القومي أو كود الموظف)
          const existingEmp = existingEmployee.recordset[0];
          let whereClause = '';

          if (existingEmp.NationalID === employee.nationalId) {
            whereClause = 'WHERE NationalID = @NationalID';

          } else if (existingEmp.EmployeeCode === employee.employeeCode) {
            whereClause = 'WHERE EmployeeCode = @EmployeeCode';

          } else {
            throw new Error('لا يمكن تحديد الموظف للتحديث');
          }

          const updateQuery = `
            UPDATE Employees SET
              ${updateFields.join(',\n              ')}
            ${whereClause}
          `;

          result = await request.query(updateQuery);
          operationType = 'updated';

          // إنشاء قائمة بالحقول المحدثة للعرض
          const updatedFieldsList = [];
          if (employee.jobTitle && employee.jobTitle.trim()) updatedFieldsList.push('المسمى الوظيفي');
          if (employee.department && employee.department.trim()) updatedFieldsList.push('القسم');
          if (employee.directManager && employee.directManager.trim()) updatedFieldsList.push('المدير المباشر');
          if (employee.mobile && employee.mobile.trim()) updatedFieldsList.push('رقم الموبايل');
          if (employee.email && employee.email.trim()) updatedFieldsList.push('البريد الإلكتروني');
          if (employee.emergencyNumber && employee.emergencyNumber.trim()) updatedFieldsList.push('رقم الطوارئ');
          if (employee.governorate && employee.governorate.trim()) updatedFieldsList.push('المحافظة');
          if (employee.area && employee.area.trim()) updatedFieldsList.push('المنطقة');
          if (employee.maritalStatus && employee.maritalStatus.trim()) updatedFieldsList.push('الحالة الاجتماعية');
          if (employee.education && employee.education.trim()) updatedFieldsList.push('المؤهل');
          if (employee.university && employee.university.trim()) updatedFieldsList.push('الجامعة');
          if (employee.specialization && employee.specialization.trim()) updatedFieldsList.push('التخصص');

          updatedEmployees.push({
            employeeId: existingEmployee.recordset[0].EmployeeCode,
            employeeCode: employee.employeeCode,
            fullName: employee.fullName,
            nationalId: employee.nationalId,
            department: employee.department || 'غير محدد',
            jobTitle: employee.jobTitle || 'غير محدد',
            rowNumber: employee.rowNumber,
            updatedFields: updateFields.length - 1, // طرح الحقل الأساسي (اسم فقط)
            updatedFieldsList: updatedFieldsList,
            updateType: 'smart_update', // تحديث ذكي
            status: 'تم التحديث',
            statusColor: 'success'
          });

          console.log(`🔄 تم تحديث الموظف: ${employee.fullName} (${updateFields.length} حقل)`);
        } else {
          // التحقق من الحقول المطلوبة للموظفين الجدد (محدث - حقول أقل)
          const requiredForNewEmployee = [
            'jobTitle', 'department', 'birthDate', 'gender',
            'governorate', 'maritalStatus', 'joinDate',
            'employeeStatus', 'isResident'
          ];

          const missingForNew = requiredForNewEmployee.filter(field => {
            const value = employee[field];
            if (field === 'isResident') {
              return value === undefined || value === null || value === '';
            }
            return !value || (typeof value === 'string' && value.trim() === '');
          });

          if (missingForNew.length > 0) {
            throw new Error(`موظف جديد - حقول أساسية مفقودة: ${missingForNew.join(', ')}`);
          }

          // التعامل مع "تعيين جديد" في كود الموظف أو التحقق من عدم تضارب الكود
          if (employee.employeeCode === 'تعيين جديد' || employee.employeeCode === 'NEW') {
            // إنشاء كود موظف تلقائي
            const autoCodeQuery = `
              SELECT MAX(CAST(EmployeeCode AS INT)) as MaxCode
              FROM Employees
              WHERE ISNUMERIC(EmployeeCode) = 1
            `;
            const autoCodeResult = await request.query(autoCodeQuery);
            const maxCode = autoCodeResult.recordset[0]?.MaxCode || 1000;
            employee.employeeCode = (maxCode + 1).toString();

          } else {
            // التحقق من عدم وجود كود الموظف مع موظف آخر
            const codeCheckQuery = `
              SELECT EmployeeCode, EmployeeName FROM Employees
              WHERE EmployeeCode = @EmployeeCode AND NationalID != @NationalID
            `;
            const codeCheckResult = await request.query(codeCheckQuery);

            if (codeCheckResult.recordset.length > 0) {
              const conflictEmployee = codeCheckResult.recordset[0];
              throw new Error(`كود الموظف ${employee.employeeCode} مستخدم بالفعل مع موظف آخر: ${conflictEmployee.EmployeeName}`);
            }
          }

          // إضافة موظف جديد (محدث بأسماء الأعمدة الصحيحة)
          const insertQuery = `
            INSERT INTO Employees (
              EmployeeCode, EmployeeName, JobTitle, Department, direct, NationalID,
              BirthDate, Gender, Governorate, area, MaritalStatus, Mobile, email,
              emrnum, Kinship, HireDate, JoinDate, CurrentStatus, Mserv, IsResidentEmployee,
              Education, University, Major, Grade, Batch,
              SocialInsurance, SocialInsureNum, spcialInsDate,
              MedicalInsurance, MedicalInsuranceNum
            ) VALUES (
              @EmployeeCode, @EmployeeName, @JobTitle, @Department, @DirectManager, @NationalID,
              @BirthDate, @Gender, @Governorate, @Area, @MaritalStatus, @Mobile, @Email,
              @EmergencyNumber, @Kinship, @HireDate, @JoinDate, @CurrentStatus, @MilitaryService, @IsResidentEmployee,
              @Education, @University, @Major, @Grade, @Batch,
              @SocialInsurance, @SocialInsuranceNumber, @SocialInsuranceDate,
              @MedicalInsurance, @MedicalInsuranceNumber
            );
            SELECT SCOPE_IDENTITY() as NewEmployeeID;
          `;

          result = await request.query(insertQuery);
          operationType = 'added';

          addedEmployees.push({
            employeeId: result.recordset[0].NewEmployeeID,
            employeeCode: employee.employeeCode,
            fullName: employee.fullName,
            nationalId: employee.nationalId,
            department: employee.department || 'غير محدد',
            jobTitle: employee.jobTitle || 'غير محدد',
            rowNumber: employee.rowNumber,
            updateType: 'new_employee', // موظف جديد
            status: 'تم الإضافة',
            statusColor: 'primary'
          });

        }

        successCount++;

      } catch (error) {
        failedCount++;

        failedEmployees.push({
          employeeCode: employee.employeeCode || 'غير محدد',
          fullName: employee.fullName || 'غير محدد',
          nationalId: employee.nationalId || 'غير محدد',
          department: employee.department || 'غير محدد',
          jobTitle: employee.jobTitle || 'غير محدد',
          rowNumber: employee.rowNumber,
          error: error.message,
          status: 'فشل',
          statusColor: 'danger'
        });

        errors.push(`الصف ${employee.rowNumber} (${employee.fullName}): ${error.message}`);
      }
    }

    return NextResponse.json({
      success: true,
      message: `تم معالجة الملف بنجاح - التحديث الذكي`,
      info: {
        smartUpdate: true,
        rules: [
          "✅ الحقول المملوءة في الملف: سيتم تحديثها",
          "🛡️ الحقول الفارغة في الملف: سيتم تجاهلها (الاحتفاظ بالقيم الموجودة)",
          "📋 الحقول المطلوبة للموظفين الجدد: المسمى الوظيفي، القسم، تاريخ الميلاد، النوع، المحافظة، الحالة الاجتماعية، تاريخ الانضمام، حالة الموظف، مغترب",
          "🆕 الحقول الاختيارية للموظفين الجدد: المدير المباشر، تاريخ التعيين، الخدمة العسكرية",
          "🔑 الحقول الأساسية المطلوبة دائماً: كود الموظف (أو 'تعيين جديد')، الاسم، الرقم القومي"
        ]
      },
      summary: {
        total: employees.length,
        success: successCount,
        failed: failedCount,
        added: addedEmployees.length,
        updated: updatedEmployees.length
      },
      details: {
        addedEmployees: addedEmployees,
        updatedEmployees: updatedEmployees,
        failedEmployees: failedEmployees
      },
      errors: errors
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'خطأ في معالجة الملف',
      error: error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      await pool.close();

    }
  }
}
