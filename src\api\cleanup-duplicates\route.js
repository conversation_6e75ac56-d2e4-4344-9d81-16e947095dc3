async function handler() {
  const session = getSession();
  if (!session?.user?.id) {
    return { error: "غير مصرح به" };
  }

  try {
    // تحديد الصفحات الرئيسية للحفاظ عليها
    const primaryPages = {
      login: "/account/signin",
      dashboard: "/dashboard",
      employeeManagement: "/employee-management",
    };

    // حذف الصفحات المكررة مع الحفاظ على الصفحات الرئيسية
    const deletedPages = await sql`
      WITH duplicate_pages AS (
        SELECT id, name, path,
          ROW_NUMBER() OVER (
            PARTITION BY LOWER(REGEXP_REPLACE(name, '[^a-zA-Z0-9]', '', 'g'))
            ORDER BY 
              CASE 
                WHEN path = ${primaryPages.login} THEN 1
                WHEN path = ${primaryPages.dashboard} THEN 1
                WHEN path = ${primaryPages.employeeManagement} THEN 1
                ELSE 2
              END,
              created_at DESC
          ) as rn
        FROM modules
        WHERE 
          LOWER(name) LIKE '%login%'
          OR LOWER(name) LIKE '%dashboard%'
          OR LOWER(name) LIKE '%home%'
          OR LOWER(name) LIKE '%unified%'
          OR LOWER(name) LIKE '%auth%'
          OR LOWER(name) LIKE '%employee%'
      )
      DELETE FROM modules 
      WHERE id IN (
        SELECT id FROM duplicate_pages 
        WHERE rn > 1
      )
      RETURNING id, name, path`;

    // حذف الوظائف المكررة
    const deletedFunctions = await sql`
      WITH duplicate_functions AS (
        SELECT id, name,
          ROW_NUMBER() OVER (
            PARTITION BY LOWER(REGEXP_REPLACE(name, '[^a-zA-Z0-9]', '', 'g'))
            ORDER BY created_at DESC
          ) as rn
        FROM functions
        WHERE 
          LOWER(name) LIKE '%auth%'
          OR LOWER(name) LIKE '%login%'
          OR LOWER(name) LIKE '%authenticate%'
          OR LOWER(name) LIKE '%employee%'
          OR LOWER(name) LIKE '%data%'
      )
      DELETE FROM functions 
      WHERE id IN (
        SELECT id FROM duplicate_functions 
        WHERE rn > 1
      )
      RETURNING id, name`;

    // تنظيف سجلات النشاط المرتبطة
    await sql`
      DELETE FROM activity_log 
      WHERE entity_type = 'page' 
      AND action_type = 'access'
      AND (
        description LIKE '%login%'
        OR description LIKE '%duplicate%'
        OR description LIKE '%temp%'
      )
      AND description NOT LIKE '%/account/signin%'`;

    // تسجيل عملية التنظيف
    await sql`
      INSERT INTO activity_log (
        user_id,
        action_type,
        entity_type,
        description
      ) VALUES (
        ${session.user.id},
        'cleanup',
        'system',
        'تم تنظيف وحذف الصفحات والوظائف المكررة في النظام'
      )`;

    // تنظيم النتائج حسب النوع
    const categorizeResults = (items) => {
      const categories = {
        auth: [],
        dashboard: [],
        employee: [],
        other: [],
      };

      items.forEach((item) => {
        if (
          item.name.toLowerCase().includes("auth") ||
          item.name.toLowerCase().includes("login")
        ) {
          categories.auth.push(item);
        } else if (
          item.name.toLowerCase().includes("dashboard") ||
          item.name.toLowerCase().includes("home")
        ) {
          categories.dashboard.push(item);
        } else if (item.name.toLowerCase().includes("employee")) {
          categories.employee.push(item);
        } else {
          categories.other.push(item);
        }
      });

      return categories;
    };

    const categorizedPages = categorizeResults(deletedPages);
    const categorizedFunctions = categorizeResults(deletedFunctions);

    return {
      success: true,
      message: "تم تنظيف وحذف العناصر المكررة بنجاح",
      details: {
        pages: {
          total: deletedPages.length,
          byCategory: {
            auth: categorizedPages.auth.length,
            dashboard: categorizedPages.dashboard.length,
            employee: categorizedPages.employee.length,
            other: categorizedPages.other.length,
          },
          items: categorizedPages,
        },
        functions: {
          total: deletedFunctions.length,
          byCategory: {
            auth: categorizedFunctions.auth.length,
            dashboard: categorizedFunctions.dashboard.length,
            employee: categorizedFunctions.employee.length,
            other: categorizedFunctions.other.length,
          },
          items: categorizedFunctions,
        },
      },
    };
  } catch (error) {

    return {
      error: "فشل في عملية تنظيف وحذف العناصر المكررة",
      details: error.message,
    };
  }
}
export async function POST(request) {
  return handler(await request.json());
}