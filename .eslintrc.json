{"extends": ["next/core-web-vitals"], "rules": {"no-console": "warn", "no-unused-vars": "warn", "no-debugger": "error", "prefer-const": "error", "no-var": "error", "eqeqeq": "error", "curly": "error", "no-duplicate-imports": "error", "no-unreachable": "error", "no-undef": "error", "react/no-unused-state": "warn", "react/jsx-no-duplicate-props": "error", "react/jsx-uses-react": "off", "react/react-in-jsx-scope": "off", "react-hooks/exhaustive-deps": "warn"}, "env": {"browser": true, "node": true, "es6": true}, "parserOptions": {"ecmaVersion": 2021, "sourceType": "module", "ecmaFeatures": {"jsx": true}}}