import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const limit = parseInt(searchParams.get('limit')) || 10;

    if (!query || query.trim().length === 0) {
      return NextResponse.json({
        success: true,
        suggestions: []
      });
    }

    const pool = await getConnection();
    const searchTerm = query.trim();
    
    // تحديد نوع البحث: رقم أم نص
    const isNumeric = /^\d+$/.test(searchTerm);
    
    let searchQuery;

    if (isNumeric) {
      // البحث بالكود
      searchQuery = `
        SELECT TOP (@Limit)
          EmployeeCode,
          EmployeeName,
          JobTitle,
          Department
        FROM Employees
        WHERE CAST(EmployeeCode AS NVARCHAR) LIKE @SearchTerm + '%'
        ORDER BY EmployeeCode
      `;
    } else {
      // البحث بالاسم
      searchQuery = `
        SELECT TOP (@Limit)
          EmployeeCode,
          EmployeeName,
          JobTitle,
          Department
        FROM Employees
        WHERE EmployeeName LIKE '%' + @SearchTerm + '%'
        ORDER BY EmployeeName
      `;
    }

    const result = await pool.request()
      .input('SearchTerm', sql.NVarChar, searchTerm)
      .input('Limit', sql.Int, limit)
      .query(searchQuery);

    const suggestions = result.recordset.map(emp => ({
      id: emp.EmployeeID,
      employeeId: emp.EmployeeID,
      employeeCode: emp.EmployeeID, // للتوافق مع الواجهات
      name: emp.FullName,
      employeeName: emp.FullName, // للتوافق مع الواجهات
      jobTitle: emp.JobTitle,
      department: emp.Department,
      displayText: `${emp.FullName} (${emp.EmployeeID})`,
      searchType: isNumeric ? 'code' : 'name'
    }));

    return NextResponse.json({
      success: true,
      suggestions: suggestions,
      searchType: isNumeric ? 'code' : 'name',
      query: searchTerm
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في البحث: ' + error.message
    }, { status: 500 });
  }
}

export async function POST(request) {
  try {
    const { searchTerm, limit = 10 } = await request.json();

    if (!searchTerm || searchTerm.trim().length === 0) {
      return NextResponse.json({
        success: true,
        data: []
      });
    }

    const pool = await getConnection();
    const cleanSearchTerm = searchTerm.trim();
    
    // تحديد نوع البحث: رقم أم نص
    const isNumeric = /^\d+$/.test(cleanSearchTerm);
    
    let searchQuery;

    if (isNumeric) {
      // البحث بالكود
      searchQuery = `
        SELECT TOP (@Limit)
          EmployeeCode,
          EmployeeName,
          JobTitle,
          Department
        FROM Employees
        WHERE CAST(EmployeeCode AS NVARCHAR) LIKE @SearchTerm + '%'
        ORDER BY EmployeeCode
      `;
    } else {
      // البحث بالاسم
      searchQuery = `
        SELECT TOP (@Limit)
          EmployeeCode,
          EmployeeName,
          JobTitle,
          Department
        FROM Employees
        WHERE EmployeeName LIKE '%' + @SearchTerm + '%'
        ORDER BY EmployeeName
      `;
    }

    const result = await pool.request()
      .input('SearchTerm', sql.NVarChar, cleanSearchTerm)
      .input('Limit', sql.Int, limit)
      .query(searchQuery);

    const employees = result.recordset.map(emp => ({
      id: emp.EmployeeID,
      employeeId: emp.EmployeeID,
      employeeCode: emp.EmployeeID, // للتوافق مع الواجهات
      name: emp.FullName,
      employeeName: emp.FullName, // للتوافق مع الواجهات
      FullName: emp.FullName, // للتوافق مع الواجهات القديمة
      EmployeeID: emp.EmployeeID, // للتوافق مع الواجهات القديمة
      jobTitle: emp.JobTitle,
      JobTitle: emp.JobTitle, // للتوافق مع الواجهات القديمة
      department: emp.Department,
      Department: emp.Department, // للتوافق مع الواجهات القديمة
      displayText: `${emp.FullName} (${emp.EmployeeID})`,
      searchType: isNumeric ? 'code' : 'name'
    }));

    return NextResponse.json({
      success: true,
      data: employees,
      searchType: isNumeric ? 'code' : 'name',
      query: cleanSearchTerm,
      totalFound: employees.length
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في البحث: ' + error.message
    }, { status: 500 });
  }
}
