'use client';
import React from 'react';

function MainComponent() {
  const [selectedLang, setSelectedLang] = useState('ar');
  const [formData, setFormData] = useState({
    apartment_code: '',
    location: '',
    monthly_rent: '',
    rooms: '',
    floor: '',
    status: 'available',
    description: '',
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);

  const dir = selectedLang === 'ar' ? 'rtl' : 'ltr';

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess(false);

    try {
      const response = await fetch('/api/data-service', {
        method: 'POST',
        body: JSON.stringify({
          table: 'apartments',
          action: 'create',
          data: formData,
        }),
      });

      if (!response.ok) {
        throw new Error(
          selectedLang === 'ar'
            ? 'حدث خطأ أثناء إضافة الشقة'
            : 'Error adding apartment'
        );
      }

      setSuccess(true);
      setFormData({
        apartment_code: '',
        location: '',
        monthly_rent: '',
        rooms: '',
        floor: '',
        status: 'available',
        description: '',
      });
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      dir={dir}
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="max-w-3xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <a
            href="/assets"
            className="flex items-center text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
          >
            <i
              className={`fas fa-arrow-${
                selectedLang === 'ar' ? 'left' : 'right'
              } ml-2`}
            ></i>
            {selectedLang === 'ar' ? 'عودة' : 'Back'}
          </a>
          <button
            onClick={() => setSelectedLang(selectedLang === 'ar' ? 'en' : 'ar')}
            className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
          >
            {selectedLang === 'ar' ? 'English' : 'العربية'}
          </button>
        </div>

        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          {selectedLang === 'ar' ? 'إضافة شقة جديدة' : 'Add New Apartment'}
        </h1>

        <form
          onSubmit={handleSubmit}
          className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                {selectedLang === 'ar' ? 'رمز الشقة' : 'Apartment Code'}
              </label>
              <input
                type="text"
                name="apartment_code"
                value={formData.apartment_code}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                {selectedLang === 'ar' ? 'الموقع' : 'Location'}
              </label>
              <input
                type="text"
                name="location"
                value={formData.location}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                {selectedLang === 'ar' ? 'الإيجار الشهري' : 'Monthly Rent'}
              </label>
              <input
                type="number"
                name="monthly_rent"
                value={formData.monthly_rent}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                {selectedLang === 'ar' ? 'عدد الغرف' : 'Number of Rooms'}
              </label>
              <input
                type="number"
                name="rooms"
                value={formData.rooms}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                {selectedLang === 'ar' ? 'الطابق' : 'Floor'}
              </label>
              <input
                type="number"
                name="floor"
                value={formData.floor}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                {selectedLang === 'ar' ? 'الحالة' : 'Status'}
              </label>
              <select
                name="status"
                value={formData.status}
                onChange={handleInputChange}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="available">
                  {selectedLang === 'ar' ? 'متاح' : 'Available'}
                </option>
                <option value="rented">
                  {selectedLang === 'ar' ? 'مؤجر' : 'Rented'}
                </option>
                <option value="maintenance">
                  {selectedLang === 'ar' ? 'صيانة' : 'Maintenance'}
                </option>
              </select>
            </div>
          </div>

          <div className="mt-6">
            <label className="block text-gray-700 dark:text-gray-300 mb-2">
              {selectedLang === 'ar' ? 'الوصف' : 'Description'}
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              className="w-full p-2 border border-gray-300 rounded-md"
              rows="4"
            />
          </div>

          {error && (
            <div className="mt-4 p-3 bg-red-100 text-red-700 rounded-md">
              {error}
            </div>
          )}

          {success && (
            <div className="mt-4 p-3 bg-green-100 text-green-700 rounded-md">
              {selectedLang === 'ar'
                ? 'تمت إضافة الشقة بنجاح'
                : 'Apartment added successfully'}
            </div>
          )}

          <div className="mt-6">
            <button
              type="submit"
              disabled={loading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors disabled:bg-blue-300"
            >
              {loading
                ? selectedLang === 'ar'
                  ? 'جاري الإضافة...'
                  : 'Adding...'
                : selectedLang === 'ar'
                  ? 'إضافة الشقة'
                  : 'Add Apartment'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default MainComponent;
