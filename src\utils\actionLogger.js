// مكتبة تسجيل الإجراءات التلقائي

class ActionLogger {
  constructor() {
    this.userCode = null;
    this.userName = null;
    this.sessionID = null;
    this.isEnabled = true;
  }

  // تهيئة المسجل
  initialize(userCode, userName, sessionID) {
    this.userCode = userCode;
    this.userName = userName;
    this.sessionID = sessionID || this.generateSessionID();
    
    // تسجيل دخول المستخدم
    this.logAction('LOGIN', `تسجيل دخول المستخدم ${userName}`, {
      loginTime: new Date().toISOString()
    });
  }

  // توليد معرف جلسة
  generateSessionID() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  // تسجيل إجراء
  async logAction(actionType, description, additionalData = {}, targetTable = null, targetID = null, oldValues = null, newValues = null) {
    if (!this.isEnabled || !this.userCode) {
      return;
    }

    try {
      const actionData = {
        action: 'logAction',
        userCode: this.userCode,
        userName: this.userName,
        actionType: actionType,
        actionDescription: description,
        targetTable: targetTable,
        targetID: targetID,
        oldValues: oldValues,
        newValues: newValues,
        ipAddress: await this.getClientIP(),
        userAgent: navigator.userAgent,
        sessionID: this.sessionID,
        isSuccess: true,
        additionalData: additionalData
      };

      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(actionData)
      });

      if (!response.ok) {
        console.error('فشل في تسجيل الإجراء:', await response.text());
      }
    } catch (error) {

      // محاولة تسجيل الخطأ
      try {
        await this.logError(actionType, description, error.message);
      } catch (logError) {

      }
    }
  }

  // تسجيل خطأ
  async logError(actionType, description, errorMessage) {
    const errorData = {
      action: 'logAction',
      userCode: this.userCode,
      userName: this.userName,
      actionType: actionType,
      actionDescription: description,
      ipAddress: await this.getClientIP(),
      userAgent: navigator.userAgent,
      sessionID: this.sessionID,
      isSuccess: false,
      errorMessage: errorMessage
    };

    await fetch('/api/notifications', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(errorData)
    });
  }

  // الحصول على IP العميل
  async getClientIP() {
    try {
      const response = await fetch('https://api.ipify.org?format=json');
      const data = await response.json();
      return data.ip;
    } catch (error) {
      return 'unknown';
    }
  }

  // تسجيل إجراءات الموظفين
  logEmployeeAction(action, employeeData, oldData = null) {
    const actionTypes = {
      'create': 'CREATE_EMPLOYEE',
      'update': 'UPDATE_EMPLOYEE',
      'delete': 'DELETE_EMPLOYEE'
    };

    const descriptions = {
      'create': `إضافة موظف جديد: ${employeeData.fullName || employeeData.FullName}`,
      'update': `تعديل بيانات الموظف: ${employeeData.fullName || employeeData.FullName}`,
      'delete': `حذف الموظف: ${employeeData.fullName || employeeData.FullName}`
    };

    this.logAction(
      actionTypes[action],
      descriptions[action],
      { employeeName: employeeData.fullName || employeeData.FullName },
      'Employees',
      employeeData.employeeCode || employeeData.EmployeeID,
      oldData,
      employeeData
    );
  }

  // تسجيل إجراءات الشقق
  logApartmentAction(action, apartmentData, oldData = null) {
    const actionTypes = {
      'create': 'CREATE_APARTMENT',
      'update': 'UPDATE_APARTMENT',
      'delete': 'DELETE_APARTMENT'
    };

    const descriptions = {
      'create': `إضافة شقة جديدة: ${apartmentData.apartmentCode || apartmentData.ApartmentCode}`,
      'update': `تعديل بيانات الشقة: ${apartmentData.apartmentCode || apartmentData.ApartmentCode}`,
      'delete': `حذف الشقة: ${apartmentData.apartmentCode || apartmentData.ApartmentCode}`
    };

    this.logAction(
      actionTypes[action],
      descriptions[action],
      { apartmentCode: apartmentData.apartmentCode || apartmentData.ApartmentCode },
      'Apartments',
      apartmentData.apartmentCode || apartmentData.ApartmentCode,
      oldData,
      apartmentData
    );
  }

  // تسجيل إجراءات المستفيدين
  logBeneficiaryAction(action, beneficiaryData, apartmentCode) {
    const actionTypes = {
      'add': 'ADD_BENEFICIARY',
      'remove': 'REMOVE_BENEFICIARY'
    };

    const descriptions = {
      'add': `إضافة مستفيد للشقة ${apartmentCode}: ${beneficiaryData.employeeCode}`,
      'remove': `إزالة مستفيد من الشقة ${apartmentCode}: ${beneficiaryData.employeeCode}`
    };

    this.logAction(
      actionTypes[action],
      descriptions[action],
      { 
        apartmentCode: apartmentCode,
        employeeCode: beneficiaryData.employeeCode 
      },
      'ApartmentBeneficiaries',
      beneficiaryData.ID || beneficiaryData.employeeCode,
      null,
      beneficiaryData
    );
  }

  // تسجيل إجراءات السيارات
  logCarAction(action, carData, oldData = null) {
    const actionTypes = {
      'create': 'CREATE_CAR',
      'update': 'UPDATE_CAR',
      'delete': 'DELETE_CAR'
    };

    const descriptions = {
      'create': `إضافة سيارة جديدة: ${carData.carCode || carData.CarCode}`,
      'update': `تعديل بيانات السيارة: ${carData.carCode || carData.CarCode}`,
      'delete': `حذف السيارة: ${carData.carCode || carData.CarCode}`
    };

    this.logAction(
      actionTypes[action],
      descriptions[action],
      { carCode: carData.carCode || carData.CarCode },
      'Cars',
      carData.carCode || carData.CarCode,
      oldData,
      carData
    );
  }

  // تسجيل إجراءات المستندات
  logDocumentAction(action, documentData) {
    const actionTypes = {
      'upload': 'UPLOAD_DOCUMENT',
      'delete': 'DELETE_DOCUMENT'
    };

    const descriptions = {
      'upload': `رفع مستند: ${documentData.documentType} للموظف ${documentData.employeeCode}`,
      'delete': `حذف مستند: ${documentData.documentType} للموظف ${documentData.employeeCode}`
    };

    this.logAction(
      actionTypes[action],
      descriptions[action],
      { 
        documentType: documentData.documentType,
        employeeCode: documentData.employeeCode 
      },
      'Documents',
      documentData.fileName || documentData.documentType,
      null,
      documentData
    );
  }

  // تسجيل إجراءات الحضور
  logAttendanceAction(action, attendanceData) {
    this.logAction(
      'ATTENDANCE_UPLOAD',
      `رفع بيانات الحضور: ${attendanceData.date || 'تاريخ غير محدد'}`,
      attendanceData,
      'Attendance',
      attendanceData.date
    );
  }

  // تسجيل إجراءات الإجازات (محدث ليتوافق مع ActionTypes)
  logLeaveAction(action, leaveData) {
    const actionTypes = {
      'submit': 'SUBMIT_LEAVE_REQUEST',
      'approve': 'APPROVE_LEAVE_REQUEST',
      'reject': 'REJECT_LEAVE_REQUEST',
      'update': 'UPDATE_LEAVE_REQUEST',
      'delete': 'DELETE_LEAVE_REQUEST'
    };

    const descriptions = {
      'submit': `تقديم طلب إجازة ${leaveData.leaveType || 'إعتيادية'} للموظف: ${leaveData.employeeName || leaveData.employeeCode}`,
      'approve': `اعتماد طلب إجازة ${leaveData.leaveType || 'إعتيادية'} للموظف: ${leaveData.employeeName || leaveData.employeeCode}`,
      'reject': `رفض طلب إجازة ${leaveData.leaveType || 'إعتيادية'} للموظف: ${leaveData.employeeName || leaveData.employeeCode}`,
      'update': `تحديث طلب إجازة للموظف: ${leaveData.employeeName || leaveData.employeeCode}`,
      'delete': `حذف طلب إجازة للموظف: ${leaveData.employeeName || leaveData.employeeCode}`
    };

    this.logAction(
      actionTypes[action],
      descriptions[action],
      {
        ...leaveData,
        leaveType: leaveData.leaveType,
        startDate: leaveData.startDate,
        endDate: leaveData.endDate,
        daysCount: leaveData.daysCount
      },
      'PaperRequests',
      leaveData.requestId || leaveData.ID
    );
  }

  // تسجيل إجراءات الطلبات الورقية الأخرى
  logPaperRequestAction(action, requestType, requestData) {
    const actionTypeMap = {
      'mission': {
        'submit': 'SUBMIT_MISSION_REQUEST',
        'approve': 'APPROVE_MISSION_REQUEST',
        'reject': 'REJECT_MISSION_REQUEST'
      },
      'permission': {
        'submit': 'SUBMIT_PERMISSION_REQUEST',
        'approve': 'APPROVE_PERMISSION_REQUEST',
        'reject': 'REJECT_PERMISSION_REQUEST'
      },
      'night_shift': {
        'submit': 'SUBMIT_NIGHT_SHIFT_REQUEST',
        'approve': 'APPROVE_NIGHT_SHIFT_REQUEST',
        'reject': 'REJECT_NIGHT_SHIFT_REQUEST'
      }
    };

    const requestTypeArabic = {
      'mission': 'مأمورية',
      'permission': 'إذن',
      'night_shift': 'وردية ليلية'
    };

    const actionText = {
      'submit': 'تقديم',
      'approve': 'اعتماد',
      'reject': 'رفض'
    };

    const actionType = actionTypeMap[requestType]?.[action];
    if (!actionType) return;

    const description = `${actionText[action]} طلب ${requestTypeArabic[requestType]} للموظف: ${requestData.employeeName || requestData.employeeCode}`;

    this.logAction(
      actionType,
      description,
      requestData,
      'PaperRequests',
      requestData.requestId || requestData.ID
    );
  }

  // تسجيل إجراءات التكاليف
  logCostAction(action, costData) {
    this.logAction(
      'COST_ENTRY',
      `إدخال تكلفة جديدة: ${costData.costType} - ${costData.amount}`,
      costData,
      'Costs',
      costData.costId
    );
  }

  // تسجيل خروج المستخدم
  logLogout() {
    this.logAction('LOGOUT', `تسجيل خروج المستخدم ${this.userName}`, {
      logoutTime: new Date().toISOString()
    });
  }

  // تعطيل/تفعيل التسجيل
  setEnabled(enabled) {
    this.isEnabled = enabled;
  }

  // تنظيف البيانات
  cleanup() {
    this.userCode = null;
    this.userName = null;
    this.sessionID = null;
  }
}

// إنشاء مثيل واحد للاستخدام العام
const actionLogger = new ActionLogger();

export default actionLogger;
