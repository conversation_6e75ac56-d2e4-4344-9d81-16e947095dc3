'use client';

import { useState, useEffect } from 'react';
import {
  FiUsers, FiPlus, FiUpload, FiSearch, FiEdit, FiTrash2,
  FiDownload, FiFileText, FiCheck, FiX, FiEye
} from 'react-icons/fi';
import MainLayout from '@/components/MainLayout';

export default function DirectManagersPage() {
  const [managers, setManagers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedManager, setSelectedManager] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [stats, setStats] = useState({});
  const [uploadHistory, setUploadHistory] = useState([]);

  // بيانات النموذج
  const [formData, setFormData] = useState({
    employeeCode: '',
    employeeName: '',
    jobTitle: '',
    department: '',
    directManager1Code: '',
    directManager1Name: '',
    directManager2Code: '',
    directManager2Name: '',
    directManager3Code: '',
    directManager3Name: '',
    directManager4Code: '',
    directManager4Name: '',
    notes: ''
  });

  // جلب بيانات المديرين المباشرين
  const fetchManagers = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/direct-managers');
      const data = await response.json();

      if (data.success) {
        setManagers(data.data);
        setStats(data.stats);
      } else {
        setError(data.error || 'فشل في جلب البيانات');
      }
    } catch (error) {
      setError('حدث خطأ في الاتصال بالخادم');
    } finally {
      setLoading(false);
    }
  };

  // جلب سجل رفع الملفات
  const fetchUploadHistory = async () => {
    try {
      const response = await fetch('/api/direct-managers/upload-excel');
      const data = await response.json();

      if (data.success) {
        setUploadHistory(data.data);
      }
    } catch (error) {
    }
  };

  // حفظ بيانات المدير المباشر
  const saveManager = async () => {
    try {
      setLoading(true);

      const response = await fetch('/api/direct-managers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          updatedBy: 'Admin'
        })
      });

      const data = await response.json();

      if (data.success) {
        alert('تم حفظ البيانات بنجاح');
        setShowAddForm(false);
        resetForm();
        await fetchManagers();
      } else {
        alert(`خطأ: ${data.error}`);
      }
    } catch (error) {
      alert('حدث خطأ في حفظ البيانات');
    } finally {
      setLoading(false);
    }
  };

  // حذف مدير مباشر
  const deleteManager = async (employeeCode) => {
    if (!confirm('هل أنت متأكد من حذف هذا السجل؟')) return;

    try {
      const response = await fetch(`/api/direct-managers?employeeCode=${employeeCode}&deletedBy=Admin`, {
        method: 'DELETE'
      });

      const data = await response.json();

      if (data.success) {
        alert('تم حذف السجل بنجاح');
        await fetchManagers();
      } else {
        alert(`خطأ: ${data.error}`);
      }
    } catch (error) {
      alert('حدث خطأ في حذف السجل');
    }
  };

  // رفع ملف Excel
  const uploadExcelFile = async (file) => {
    try {
      setLoading(true);

      const formData = new FormData();
      formData.append('file', file);
      formData.append('uploadedBy', 'Admin');

      const response = await fetch('/api/direct-managers/upload-excel', {
        method: 'POST',
        body: formData
      });

      const data = await response.json();

      if (data.success) {
        alert(`تم رفع الملف بنجاح!\nالسجلات المعالجة: ${data.data.totalRecords}\nالناجحة: ${data.data.successfulRecords}\nالفاشلة: ${data.data.failedRecords}`);
        setShowUploadModal(false);
        await fetchManagers();
        await fetchUploadHistory();
      } else {
        alert(`خطأ في رفع الملف: ${data.error}`);
      }
    } catch (error) {
      alert('حدث خطأ في رفع الملف');
    } finally {
      setLoading(false);
    }
  };

  // إعادة تعيين النموذج
  const resetForm = () => {
    setFormData({
      employeeCode: '',
      employeeName: '',
      jobTitle: '',
      department: '',
      directManager1Code: '',
      directManager1Name: '',
      directManager2Code: '',
      directManager2Name: '',
      directManager3Code: '',
      directManager3Name: '',
      directManager4Code: '',
      directManager4Name: '',
      notes: ''
    });
  };

  // تحديث بيانات النموذج
  const updateFormData = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // تحرير مدير مباشر
  const editManager = (manager) => {
    setFormData({
      employeeCode: manager.EmployeeCode,
      employeeName: manager.EmployeeName,
      jobTitle: manager.JobTitle || '',
      department: manager.Department || '',
      directManager1Code: manager.DirectManager1Code || '',
      directManager1Name: manager.DirectManager1Name || '',
      directManager2Code: manager.DirectManager2Code || '',
      directManager2Name: manager.DirectManager2Name || '',
      directManager3Code: manager.DirectManager3Code || '',
      directManager3Name: manager.DirectManager3Name || '',
      directManager4Code: manager.DirectManager4Code || '',
      directManager4Name: manager.DirectManager4Name || '',
      notes: manager.Notes || ''
    });
    setShowAddForm(true);
  };

  // تصفية البيانات
  const filteredManagers = managers.filter(manager =>
    manager.EmployeeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    manager.EmployeeCode.includes(searchTerm) ||
    (manager.JobTitle && manager.JobTitle.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  useEffect(() => {
    fetchManagers();
    fetchUploadHistory();
  }, []);

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto p-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          🏢 إدارة المديرين المباشرين
        </h1>

        <div className="bg-white rounded-xl p-6 shadow-lg">
          <p className="text-gray-600 text-center">
            صفحة إدارة المديرين المباشرين قيد التطوير...
          </p>
        </div>
      </div>
    </MainLayout>
  );
}