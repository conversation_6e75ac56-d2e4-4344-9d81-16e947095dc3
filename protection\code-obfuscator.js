// نظام تشويش الكود
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class CodeObfuscator {
  constructor() {
    this.variableMap = new Map();
    this.functionMap = new Map();
    this.counter = 0;
  }

  // توليد اسم متغير عشوائي
  generateRandomName() {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    let result = '';
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return '_' + result + '_' + this.counter++;
  }

  // تشفير النصوص
  encryptString(str) {
    const key = 'protection_key_2025';
    const cipher = crypto.createCipher('aes192', key);
    let encrypted = cipher.update(str, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
  }

  // تشويش أسماء المتغيرات
  obfuscateVariables(code) {
    // البحث عن تعريفات المتغيرات
    const variableRegex = /(?:var|let|const)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g;
    let match;

    while ((match = variableRegex.exec(code)) !== null) {
      const originalName = match[1];
      if (!this.variableMap.has(originalName)) {
        this.variableMap.set(originalName, this.generateRandomName());
      }
    }

    // استبدال أسماء المتغيرات
    for (const [original, obfuscated] of this.variableMap) {
      const regex = new RegExp(`\\b${original}\\b`, 'g');
      code = code.replace(regex, obfuscated);
    }

    return code;
  }

  // تشويش أسماء الدوال
  obfuscateFunctions(code) {
    // البحث عن تعريفات الدوال
    const functionRegex = /function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g;
    let match;

    while ((match = functionRegex.exec(code)) !== null) {
      const originalName = match[1];
      if (!this.functionMap.has(originalName)) {
        this.functionMap.set(originalName, this.generateRandomName());
      }
    }

    // استبدال أسماء الدوال
    for (const [original, obfuscated] of this.functionMap) {
      const regex = new RegExp(`\\b${original}\\b`, 'g');
      code = code.replace(regex, obfuscated);
    }

    return code;
  }

  // تشفير النصوص الثابتة
  obfuscateStrings(code) {
    const stringRegex = /(['"`])((?:(?!\1)[^\\]|\\.)*)(\1)/g;

    return code.replace(stringRegex, (match, quote, content, endQuote) => {
      if (content.length > 2) { // تشفير النصوص الطويلة فقط
        const encrypted = this.encryptString(content);
        return `decrypt('${encrypted}')`;
      }
      return match;
    });
  }

  // إضافة كود فك التشفير
  addDecryptFunction(code) {
    const decryptFunction = `
function decrypt(encryptedStr) {
  const crypto = require('crypto');
  const key = 'encryption_key_here';
  const decipher = crypto.createDecipher('aes192', key);
  let decrypted = decipher.update(encryptedStr, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
}
`;
    return decryptFunction + '\n' + code;
  }

  // إضافة كود مكافحة التصحيح
  addAntiDebug(code) {
    const antiDebugCode = `
// مكافحة التصحيح
(function() {
  let devtools = {open: false, orientation: null};
  const threshold = 160;

  setInterval(function() {
    if (window.outerHeight - window.innerHeight > threshold ||
        window.outerWidth - window.innerWidth > threshold) {
      if (!devtools.open) {
        devtools.open = true;
        console.clear();
        console.log('%cتحذير: محاولة فتح أدوات المطور!', 'color: red; font-size: 20px;');
        // يمكن إضافة المزيد من الإجراءات هنا
      }
    } else {
      devtools.open = false;
    }
  }, 500);

  // منع النقر بالزر الأيمن
  document.addEventListener('contextmenu', function(e) {
    e.preventDefault();
    return false;
  });

  // منع اختصارات لوحة المفاتيح
  document.addEventListener('keydown', function(e) {
    if (e.key === 'F12' ||
        (e.ctrlKey && e.shiftKey && e.key === 'I') ||
        (e.ctrlKey && e.shiftKey && e.key === 'C') ||
        (e.ctrlKey && e.key === 'U')) {
      e.preventDefault();
      return false;
    }
  });
})();
`;
    return antiDebugCode + '\n' + code;
  }

  // تشويش الكود الكامل
  obfuscateCode(code) {
    // إزالة التعليقات
    code = code.replace(/\/\*[\s\S]*?\*\//g, '');
    code = code.replace(/\/\/.*$/gm, '');

    // تشويش المتغيرات والدوال
    code = this.obfuscateVariables(code);
    code = this.obfuscateFunctions(code);

    // تشفير النصوص
    code = this.obfuscateStrings(code);

    // إضافة دالة فك التشفير
    code = this.addDecryptFunction(code);

    // إضافة مكافحة التصحيح (للملفات الأمامية)
    if (code.includes('document') || code.includes('window')) {
      code = this.addAntiDebug(code);
    }

    // ضغط الكود
    code = code.replace(/\s+/g, ' ').trim();

    return code;
  }

  // تشويش ملف
  obfuscateFile(inputPath, outputPath) {
    try {
      const code = fs.readFileSync(inputPath, 'utf8');
      const obfuscatedCode = this.obfuscateCode(code);
      fs.writeFileSync(outputPath, obfuscatedCode);
      console.log(`تم تشويش الملف: ${inputPath} -> ${outputPath}`);
    } catch (error) {
      console.error(`خطأ في تشويش الملف ${inputPath}:`, error.message);
    }
  }

  // تشويش مجلد كامل
  obfuscateDirectory(inputDir, outputDir) {
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    const files = fs.readdirSync(inputDir);

    for (const file of files) {
      const inputPath = path.join(inputDir, file);
      const outputPath = path.join(outputDir, file);

      const stat = fs.statSync(inputPath);

      if (stat.isDirectory()) {
        this.obfuscateDirectory(inputPath, outputPath);
      } else if (file.endsWith('.js') || file.endsWith('.jsx')) {
        this.obfuscateFile(inputPath, outputPath);
      } else {
        // نسخ الملفات الأخرى كما هي
        fs.copyFileSync(inputPath, outputPath);
      }
    }
  }
}

module.exports = CodeObfuscator;
