'use client';

import { useState, useEffect } from 'react';
import {
  FiAlertTriangle,
  FiX,
  FiClock,
  FiFileText,
  FiUsers,
  FiCalendar,
  FiHome,
  FiTruck,
  FiDollarSign,
  FiDatabase,
  FiRefreshCw,
  FiEye,
  FiEyeOff
} from 'react-icons/fi';

export default function AlertCenter({ userCode, className = '' }) {
  const [alerts, setAlerts] = useState([]);
  const [alertCounts, setAlertCounts] = useState({});
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [activeFilter, setActiveFilter] = useState('all');

  useEffect(() => {
    if (userCode) {
      loadAlerts();
      loadAlertCounts();
      // تحديث التنبيهات كل دقيقة
      const interval = setInterval(() => {
        loadAlerts();
        loadAlertCounts();
      }, 60000);
      return () => clearInterval(interval);
    }
  }, [userCode, activeFilter]);

  // جلب التنبيهات
  const loadAlerts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/alerts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getAlerts',
          category: activeFilter === 'all' ? null : activeFilter,
          limit: 50
        })
      });

      const result = await response.json();
      if (result.success) {
        setAlerts(result.data || []);
      }
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  // جلب عدد التنبيهات
  const loadAlertCounts = async () => {
    try {
      const response = await fetch('/api/alerts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getAlertCounts'
        })
      });

      const result = await response.json();
      if (result.success) {
        setAlertCounts(result.data || {});
      }
    } catch (error) {

    }
  };

  // إخفاء تنبيه
  const dismissAlert = async (alertId) => {
    try {
      const response = await fetch('/api/alerts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'dismissAlert',
          alertId: alertId,
          userCode: userCode
        })
      });

      if (response.ok) {
        setAlerts(prev => prev.filter(alert => alert.ID !== alertId));
        loadAlertCounts();
      }
    } catch (error) {

    }
  };

  // تشغيل فحص التنبيهات
  const runAlertChecks = async () => {
    try {
      setLoading(true);
      
      // فحص التمام المفقود
      await fetch('/api/alerts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'checkMissingAttendance'
        })
      });

      // فحص التعاقدات المنتهية
      await fetch('/api/alerts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'checkExpiringContracts',
          daysAhead: 30
        })
      });

      // فحص المستندات المفقودة
      await fetch('/api/alerts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'checkMissingDocuments'
        })
      });

      // إعادة تحميل التنبيهات
      await loadAlerts();
      await loadAlertCounts();
      
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  // تنسيق التاريخ
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'الآن';
    if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;
    if (diffInMinutes < 1440) return `منذ ${Math.floor(diffInMinutes / 60)} ساعة`;
    return `منذ ${Math.floor(diffInMinutes / 1440)} يوم`;
  };

  // أيقونة نوع التنبيه
  const getAlertIcon = (alertType, severity) => {
    const iconClass = `w-4 h-4 ${
      severity === 'critical' ? 'text-red-600' :
      severity === 'high' ? 'text-orange-500' :
      severity === 'medium' ? 'text-yellow-500' :
      'text-blue-500'
    }`;

    switch (alertType) {
      case 'MISSING_ATTENDANCE':
        return <FiClock className={iconClass} />;
      case 'EXPIRING_CONTRACT':
        return <FiCalendar className={iconClass} />;
      case 'MISSING_DOCUMENTS':
        return <FiFileText className={iconClass} />;
      case 'APARTMENT_VACANCY':
        return <FiHome className={iconClass} />;
      case 'CAR_UNASSIGNED':
        return <FiTruck className={iconClass} />;
      case 'COST_OVERDUE':
        return <FiDollarSign className={iconClass} />;
      default:
        return <FiAlertTriangle className={iconClass} />;
    }
  };

  // لون شدة التنبيه
  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 border-red-500 text-red-800 dark:bg-red-900/20 dark:border-red-400 dark:text-red-300';
      case 'high':
        return 'bg-orange-100 border-orange-500 text-orange-800 dark:bg-orange-900/20 dark:border-orange-400 dark:text-orange-300';
      case 'medium':
        return 'bg-yellow-100 border-yellow-500 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-400 dark:text-yellow-300';
      case 'low':
        return 'bg-blue-100 border-blue-500 text-blue-800 dark:bg-blue-900/20 dark:border-blue-400 dark:text-blue-300';
      default:
        return 'bg-gray-100 border-gray-500 text-gray-800 dark:bg-gray-900/20 dark:border-gray-400 dark:text-gray-300';
    }
  };

  const totalAlerts = alertCounts.TotalAlerts || 0;
  const criticalAlerts = alertCounts.CriticalAlerts || 0;

  return (
    <div className={`relative ${className}`}>
      {/* زر التنبيهات */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white transition-colors duration-200"
      >
        <FiAlertTriangle className="w-6 h-6" />
        {totalAlerts > 0 && (
          <span className={`absolute -top-1 -right-1 ${
            criticalAlerts > 0 ? 'bg-red-500' : 'bg-orange-500'
          } text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium animate-pulse`}>
            {totalAlerts > 99 ? '99+' : totalAlerts}
          </span>
        )}
      </button>

      {/* قائمة التنبيهات */}
      {isOpen && (
        <>
          {/* خلفية شفافة للإغلاق */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* نافذة التنبيهات */}
          <div className="absolute left-0 mt-2 w-96 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 z-50 max-h-96 overflow-hidden">
            {/* رأس النافذة */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                التنبيهات الذكية
              </h3>
              <div className="flex items-center gap-2">
                <button
                  onClick={runAlertChecks}
                  disabled={loading}
                  className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 disabled:opacity-50"
                  title="تحديث التنبيهات"
                >
                  <FiRefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                </button>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <FiX className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* فلاتر التنبيهات */}
            <div className="p-3 border-b border-gray-200 dark:border-gray-700">
              <div className="flex flex-wrap gap-2">
                {[
                  { key: 'all', label: 'الكل', count: totalAlerts },
                  { key: 'Attendance', label: 'الحضور', count: alertCounts.AttendanceAlerts },
                  { key: 'Contracts', label: 'التعاقدات', count: alertCounts.ContractAlerts },
                  { key: 'Documents', label: 'المستندات', count: alertCounts.DocumentAlerts }
                ].map(filter => (
                  <button
                    key={filter.key}
                    onClick={() => setActiveFilter(filter.key)}
                    className={`px-3 py-1 text-xs rounded-full transition-colors ${
                      activeFilter === filter.key
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                    }`}
                  >
                    {filter.label} {filter.count > 0 && `(${filter.count})`}
                  </button>
                ))}
              </div>
            </div>

            {/* محتوى التنبيهات */}
            <div className="max-h-80 overflow-y-auto">
              {loading ? (
                <div className="flex items-center justify-center p-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              ) : alerts.length === 0 ? (
                <div className="text-center p-8">
                  <FiAlertTriangle className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400">لا توجد تنبيهات</p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200 dark:divide-gray-700">
                  {alerts.map((alert) => (
                    <div
                      key={alert.ID}
                      className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 border-l-4 ${getSeverityColor(alert.Severity)}`}
                    >
                      <div className="flex items-start gap-3">
                        {/* أيقونة التنبيه */}
                        <div className="flex-shrink-0 mt-1">
                          {getAlertIcon(alert.AlertType, alert.Severity)}
                        </div>

                        {/* محتوى التنبيه */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                              {alert.Title}
                            </h4>
                            <button
                              onClick={() => dismissAlert(alert.ID)}
                              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 ml-2"
                              title="إخفاء التنبيه"
                            >
                              <FiEyeOff className="w-4 h-4" />
                            </button>
                          </div>
                          
                          <p className="text-sm text-gray-600 dark:text-gray-300 mt-1 line-clamp-2">
                            {alert.Message}
                          </p>
                          
                          <div className="flex items-center justify-between mt-2">
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {formatDate(alert.CreatedAt)}
                            </span>
                            
                            <div className="flex items-center gap-2">
                              {alert.EmployeeCode && (
                                <span className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-1 rounded-full">
                                  {alert.EmployeeCode}
                                </span>
                              )}
                              
                              <span className={`text-xs px-2 py-1 rounded-full ${getSeverityColor(alert.Severity)}`}>
                                {alert.Severity === 'critical' ? 'حرج' :
                                 alert.Severity === 'high' ? 'عالي' :
                                 alert.Severity === 'medium' ? 'متوسط' : 'منخفض'}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* تذييل النافذة */}
            {alerts.length > 0 && (
              <div className="p-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                <div className="text-center text-xs text-gray-500 dark:text-gray-400">
                  إجمالي {totalAlerts} تنبيه - آخر تحديث: {formatDate(new Date())}
                </div>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}
