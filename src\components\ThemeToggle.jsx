'use client';

import React from 'react';
import { FiSun, FiMoon } from 'react-icons/fi';
import { useTheme } from '@/contexts/ThemeContext';

const ThemeToggle = ({ size = 'md', showLabel = false }) => {
  const { isDarkMode, toggleTheme } = useTheme();

  const sizeClasses = {
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-base',
    lg: 'w-12 h-12 text-lg'
  };

  return (
    <button
      onClick={toggleTheme}
      className={`${sizeClasses[size]} rounded-lg flex items-center justify-center transition-all duration-300 ${
        isDarkMode 
          ? 'bg-gray-700 hover:bg-gray-600 text-yellow-400 shadow-lg' 
          : 'bg-white hover:bg-gray-50 text-gray-700 shadow-md border border-gray-200'
      } hover:scale-105 active:scale-95`}
      title={isDarkMode ? 'تبديل للوضع النهاري' : 'تبديل للوضع الليلي'}
    >
      <div className="relative overflow-hidden">
        <div className={`transition-all duration-500 ${isDarkMode ? 'rotate-0 opacity-100' : 'rotate-180 opacity-0 absolute inset-0'}`}>
          <FiSun className="w-full h-full" />
        </div>
        <div className={`transition-all duration-500 ${!isDarkMode ? 'rotate-0 opacity-100' : 'rotate-180 opacity-0 absolute inset-0'}`}>
          <FiMoon className="w-full h-full" />
        </div>
      </div>
      {showLabel && (
        <span className="mr-2 text-sm font-medium">
          {isDarkMode ? 'نهاري' : 'ليلي'}
        </span>
      )}
    </button>
  );
};

// مكون خاص للشريط الجانبي
export const SidebarThemeToggle = ({ expanded = false }) => {
  const { isDarkMode, toggleTheme } = useTheme();

  return (
    <button
      onClick={toggleTheme}
      className={`w-full flex items-center p-3 rounded-lg transition-all duration-200 ${
        isDarkMode 
          ? 'bg-gray-700 hover:bg-gray-600 text-yellow-400' 
          : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
      }`}
      title={isDarkMode ? 'تبديل للوضع النهاري' : 'تبديل للوضع الليلي'}
    >
      <div className="relative">
        {isDarkMode ? (
          <FiSun className="text-lg transition-transform duration-300 rotate-0" />
        ) : (
          <FiMoon className="text-lg transition-transform duration-300 rotate-0" />
        )}
      </div>
      {expanded && (
        <span className="mr-3 transition-opacity duration-200">
          {isDarkMode ? 'الوضع النهاري' : 'الوضع الليلي'}
        </span>
      )}
    </button>
  );
};

export default ThemeToggle;
