async function handler({ operation, table, data, id, filters }) {
  try {
    switch (operation) {
      case 'إنشاء':
        if (!table || !data) {
          return { error: 'البيانات أو اسم الجدول مفقود' };
        }
        const columns = Object.keys(data);
        const values = Object.values(data);
        const placeholders = values.map((_, i) => `$${i + 1}`);

        const result = await sql(
          `INSERT INTO ${table} (${columns.join(
            ', '
          )}) VALUES (${placeholders.join(', ')}) RETURNING *`,
          values
        );
        return { data: result[0] };

      case 'تحديث':
        if (!table || !data || !id) {
          return { error: 'البيانات أو المعرف أو اسم الجدول مفقود' };
        }
        const setClause = Object.keys(data)
          .map((key, i) => `${key} = $${i + 1}`)
          .join(', ');
        const updateValues = [...Object.values(data), id];

        const updated = await sql(
          `UPDATE ${table} SET ${setClause} WHERE id = $${updateValues.length} RETURNING *`,
          updateValues
        );
        return { data: updated[0] };

      case 'حذف':
        if (!table || !id) {
          return { error: 'المعرف أو اسم الجدول مفقود' };
        }
        await sql(`DELETE FROM ${table} WHERE id = $1`, [id]);
        return { message: 'تم الحذف بنجاح' };

      case 'استعلام':
        if (!table) {
          return { error: 'اسم الجدول مفقود' };
        }
        let query = `SELECT * FROM ${table}`;
        const queryValues = [];

        if (filters) {
          const filterClauses = Object.entries(filters).map(
            ([key, value], i) => {
              queryValues.push(value);
              return `${key} = $${i + 1}`;
            }
          );
          if (filterClauses.length > 0) {
            query += ` WHERE ${filterClauses.join(' AND ')}`;
          }
        }

        const records = await sql(query, queryValues);
        return { data: records };

      default:
        return { error: 'عملية غير صالحة' };
    }
  } catch (error) {
    return { error: `خطأ في قاعدة البيانات: ${error.message}` };
  }
}
