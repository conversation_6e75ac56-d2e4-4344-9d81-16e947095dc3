'use client';

import { useTheme } from '@/contexts/ThemeContext';

const CostCardStatic = ({
  title,
  value,
  icon: Icon,
  color = 'blue',
  href,
  description,
  trend,
  trendValue,
  isLoading = false,
  lastSettlement,
  utilizationRate,
  additionalInfo
}) => {
  const { isDarkMode } = useTheme();

  const colorClasses = {
    blue: {
      bg: isDarkMode ? 'from-blue-600/20 to-blue-700/20' : 'from-blue-50 to-blue-100',
      border: 'border-blue-500/30',
      icon: 'text-blue-600',
      iconBg: isDarkMode ? 'bg-blue-600/20' : 'bg-blue-100',
      text: isDarkMode ? 'text-blue-400' : 'text-blue-600',
      shadow: 'shadow-blue-500/20',
      borderColor: '#3B82F6'
    },
    purple: {
      bg: isDarkMode ? 'from-purple-600/20 to-purple-700/20' : 'from-purple-50 to-purple-100',
      border: 'border-purple-500/30',
      icon: 'text-purple-600',
      iconBg: isDarkMode ? 'bg-purple-600/20' : 'bg-purple-100',
      text: isDarkMode ? 'text-purple-400' : 'text-purple-600',
      shadow: 'shadow-purple-500/20',
      borderColor: '#8B5CF6'
    },
    green: {
      bg: isDarkMode ? 'from-green-600/20 to-green-700/20' : 'from-green-50 to-green-100',
      border: 'border-green-500/30',
      icon: 'text-green-600',
      iconBg: isDarkMode ? 'bg-green-600/20' : 'bg-green-100',
      text: isDarkMode ? 'text-green-400' : 'text-green-600',
      shadow: 'shadow-green-500/20',
      borderColor: '#10B981'
    },
    orange: {
      bg: isDarkMode ? 'from-orange-600/20 to-orange-700/20' : 'from-orange-50 to-orange-100',
      border: 'border-orange-500/30',
      icon: 'text-orange-600',
      iconBg: isDarkMode ? 'bg-orange-600/20' : 'bg-orange-100',
      text: isDarkMode ? 'text-orange-400' : 'text-orange-600',
      shadow: 'shadow-orange-500/20',
      borderColor: '#F97316'
    },
    red: {
      bg: isDarkMode ? 'from-red-600/20 to-red-700/20' : 'from-red-50 to-red-100',
      border: 'border-red-500/30',
      icon: 'text-red-600',
      iconBg: isDarkMode ? 'bg-red-600/20' : 'bg-red-100',
      text: isDarkMode ? 'text-red-400' : 'text-red-600',
      shadow: 'shadow-red-500/20',
      borderColor: '#EF4444'
    }
  };

  const currentColor = colorClasses[color] || colorClasses.blue;

  const CardContent = () => (
    <div
      className={`
        relative rounded-xl p-6
        bg-gradient-to-br ${currentColor.bg}
        border ${currentColor.border}
        shadow-lg ${currentColor.shadow} hover:shadow-xl
        ${isDarkMode ? 'backdrop-blur-sm' : ''}
        group cursor-pointer
      `}
    >
      {/* إطار ملون دائر رفيع - يظهر فقط عند hover */}
      <div
        className="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
        style={{
          background: `conic-gradient(from 0deg, transparent 70%, ${currentColor.borderColor} 80%, transparent 90%)`,
          animation: 'rotating-border 3s linear infinite',
          mask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
          maskComposite: 'xor',
          WebkitMask: 'linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0)',
          WebkitMaskComposite: 'xor',
          padding: '1px'
        }}
      ></div>

      {/* محتوى الكارت */}
      <div className="relative z-10">
        {/* الرأس */}
        <div className="flex items-center justify-between mb-4">
          <div className={`p-3 rounded-lg ${currentColor.iconBg}`}>
            <Icon className={`text-xl ${currentColor.icon}`} />
          </div>

          {trend && (
            <div className={`text-xs px-2 py-1 rounded-full ${
              trend === 'up' ? 'bg-green-100 text-green-700' :
              trend === 'down' ? 'bg-red-100 text-red-700' :
              'bg-gray-100 text-gray-700'
            }`}>
              {trendValue}
            </div>
          )}
        </div>

        {/* العنوان والقيمة */}
        <div className="mb-4">
          <h3 className={`text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            {title}
          </h3>

          {isLoading ? (
            <div className="animate-pulse">
              <div className={`h-8 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-200'} rounded mb-2`}></div>
              <div className={`h-4 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-200'} rounded w-3/4`}></div>
            </div>
          ) : (
            <>
              <div className={`text-2xl font-bold mb-1 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {value}
              </div>

              {/* معلومات إضافية للعُهد المستديمة */}
              {lastSettlement && (
                <div className="mb-2">
                  <div className={`text-xs font-medium ${currentColor.text} bg-${color}-500/10 px-2 py-1 rounded-full inline-block`}>
                    آخر تسوية: رقم {lastSettlement}
                  </div>
                </div>
              )}

              {/* معدل الاستخدام */}
              {utilizationRate !== undefined && (
                <div className="mb-2">
                  <div className="flex items-center justify-between text-xs mb-1">
                    <span className={isDarkMode ? 'text-gray-400' : 'text-gray-600'}>معدل الاستخدام</span>
                    <span className={`font-medium ${currentColor.text}`}>{utilizationRate}%</span>
                  </div>
                  <div className={`w-full ${isDarkMode ? 'bg-gray-700' : 'bg-gray-200'} rounded-full h-2`}>
                    <div
                      className={`h-2 rounded-full`}
                      style={{
                        width: `${Math.min(utilizationRate, 100)}%`,
                        background: `linear-gradient(to right, ${currentColor.borderColor}, ${currentColor.borderColor}90)`
                      }}
                    ></div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>

        {/* الوصف */}
        {description && !isLoading && (
          <div className={`text-xs space-y-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            {typeof description === 'string' ? (
              <div>{description}</div>
            ) : (
              description
            )}
          </div>
        )}

        {/* معلومات إضافية */}
        {additionalInfo && !isLoading && (
          <div className="mt-3 pt-3 border-t border-gray-200/20">
            <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
              {additionalInfo}
            </div>
          </div>
        )}
      </div>
    </div>
  );

  if (href) {
    return (
      <a href={href} className="block">
        <CardContent />
      </a>
    );
  }

  return <CardContent />;
};

export default CostCardStatic;
