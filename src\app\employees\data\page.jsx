'use client';
import React from 'react';

function MainComponent() {
  const [employees, setEmployees] = useState([]);
  const [selectedLang, setSelectedLang] = useState('ar');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [filters, setFilters] = useState({
    department: '',
    area: '',
    status: '',
  });
  const dir = selectedLang === 'ar' ? 'rtl' : 'ltr';

  const loadEmployees = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/employees/list', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ filters }),
      });
      const data = await response.json();
      if (data.success) {
        setEmployees(data.employees);
      }
    } catch (error) {

    }
    setLoading(false);
  };

  useEffect(() => {
    loadEmployees();
  }, [filters]);

  const handleFileUpload = async (file) => {
    try {
      const reader = new FileReader();
      reader.onload = async (e) => {
        const fileContent = e.target.result.split(',')[1];
        const response = await fetch('/api/excel-import-handler', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            lang: selectedLang,
            fileContent,
          }),
        });

        const result = await response.json();
        if (!result.success) {
          throw new Error(result.error);
        }
        loadEmployees();
      };
      reader.readAsDataURL(file);
    } catch (error) {

      setError(
        selectedLang === 'ar' ? 'فشل في تحميل الملف' : 'Failed to upload file'
      );
    }
  };

  const handleDrop = (event) => {
    event.preventDefault();
    event.stopPropagation();
    const files = event.dataTransfer.files;
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  const handleDragOver = (event) => {
    event.preventDefault();
    event.stopPropagation();
  };

  const handleFileChange = (event) => {
    const files = event.target.files;
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  const handleExport = async () => {
    try {
      const response = await fetch('/api/excel-handler', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'export',
          filters,
        }),
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'employees.xlsx';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
      }
    } catch (error) {

    }
  };

  const handlePrint = () => {
    window.print();
  };

  const handleBack = () => {
    window.history.back();
  };

  return (
    <div
      dir={dir}
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          {selectedLang === 'ar' ? 'بيانات الموظفين' : 'Employee Data'}
        </h1>
        <div className="flex gap-2">
          <button
            onClick={() => setSelectedLang(selectedLang === 'ar' ? 'en' : 'ar')}
            className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
          >
            {selectedLang === 'ar' ? 'English' : 'العربية'}
          </button>
          <button
            onClick={handleBack}
            className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
          >
            <i className="fas fa-arrow-left mr-2"></i>
            {selectedLang === 'ar' ? 'رجوع' : 'Back'}
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <select
          value={filters.department}
          onChange={(e) =>
            setFilters({ ...filters, department: e.target.value })
          }
          className="p-2 border rounded-md"
        >
          <option value="">
            {selectedLang === 'ar' ? 'كل الأقسام' : 'All Departments'}
          </option>
        </select>

        <select
          value={filters.area}
          onChange={(e) => setFilters({ ...filters, area: e.target.value })}
          className="p-2 border rounded-md"
        >
          <option value="">
            {selectedLang === 'ar' ? 'كل المناطق' : 'All Areas'}
          </option>
        </select>

        <select
          value={filters.status}
          onChange={(e) => setFilters({ ...filters, status: e.target.value })}
          className="p-2 border rounded-md"
        >
          <option value="">
            {selectedLang === 'ar' ? 'كل الحالات' : 'All Statuses'}
          </option>
        </select>
      </div>

      <div className="flex flex-col gap-4 mb-6">
        <div className="flex gap-2">
          <button
            onClick={handleExport}
            className="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600 flex items-center"
          >
            <i className="fas fa-file-excel mr-2"></i>
            {selectedLang === 'ar' ? 'تصدير إلى Excel' : 'Export to Excel'}
          </button>

          <button
            onClick={handlePrint}
            className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 flex items-center"
          >
            <i className="fas fa-print mr-2"></i>
            {selectedLang === 'ar' ? 'طباعة' : 'Print'}
          </button>
        </div>

        <div
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          className="border-2 border-dashed border-gray-300 p-6 rounded-lg text-center"
        >
          <p className="text-gray-600 dark:text-gray-300">
            {selectedLang === 'ar'
              ? 'اسحب الملف هنا أو انقر للتحميل'
              : 'Drag file here or click to upload'}
          </p>
          <input
            type="file"
            onChange={handleFileChange}
            className="hidden"
            id="fileUpload"
          />
          <label
            htmlFor="fileUpload"
            className="cursor-pointer text-blue-600 hover:underline"
          >
            {selectedLang === 'ar' ? 'اختر ملف' : 'Choose a file'}
          </label>
        </div>
      </div>

      {error && (
        <div className="mt-4 p-4 bg-red-100 text-red-700 rounded-md">
          {error}
        </div>
      )}

      <div className="overflow-x-auto">
        <table className="min-w-full bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th className="px-6 py-3 text-right">
                {selectedLang === 'ar' ? 'كود الموظف' : 'Employee ID'}
              </th>
              <th className="px-6 py-3 text-right">
                {selectedLang === 'ar' ? 'الاسم' : 'Name'}
              </th>
              <th className="px-6 py-3 text-right">
                {selectedLang === 'ar' ? 'القسم' : 'Department'}
              </th>
              <th className="px-6 py-3 text-right">
                {selectedLang === 'ar' ? 'المسمى الوظيفي' : 'Job Title'}
              </th>
              <th className="px-6 py-3 text-right">
                {selectedLang === 'ar' ? 'المنطقة' : 'Area'}
              </th>
              <th className="px-6 py-3 text-right">
                {selectedLang === 'ar' ? 'رقم الهاتف' : 'Phone'}
              </th>
              <th className="px-6 py-3 text-right">
                {selectedLang === 'ar' ? 'الحالة' : 'Status'}
              </th>
              <th className="px-6 py-3 text-right">
                {selectedLang === 'ar' ? 'تاريخ التعيين' : 'Hire Date'}
              </th>
              <th className="px-6 py-3 text-right">
                {selectedLang === 'ar'
                  ? 'تاريخ الالتحاق بالمشروع'
                  : 'Project Join Date'}
              </th>
              <th className="px-6 py-3 text-right">
                {selectedLang === 'ar' ? 'الإجراءات' : 'Actions'}
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
            {loading ? (
              <tr>
                <td colSpan="10" className="text-center py-4">
                  <div className="spinner"></div>
                </td>
              </tr>
            ) : (
              employees.map((employee) => (
                <tr
                  key={employee.employee_id}
                  className="hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  <td className="px-6 py-4">{employee.employee_id}</td>
                  <td className="px-6 py-4">{employee.employee_name}</td>
                  <td className="px-6 py-4">{employee.department}</td>
                  <td className="px-6 py-4">{employee.job_title}</td>
                  <td className="px-6 py-4">{employee.area}</td>
                  <td className="px-6 py-4">{employee.phone_number}</td>
                  <td className="px-6 py-4">{employee.status}</td>
                  <td className="px-6 py-4">
                    {new Date(employee.hire_date).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4">
                    {new Date(employee.project_join_date).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex gap-2">
                      <button
                        onClick={() =>
                          (window.location.href = `/employee/edit/${employee.employee_id}`)
                        }
                        className="text-blue-500 hover:text-blue-700"
                      >
                        <i className="fas fa-edit"></i>
                      </button>
                      <button
                        onClick={() =>
                          (window.location.href = `/employee/view/${employee.employee_id}`)
                        }
                        className="text-green-500 hover:text-green-700"
                      >
                        <i className="fas fa-eye"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      <style jsx global>{`
        @media print {
          .no-print {
            display: none !important;
          }
          body * {
            visibility: hidden;
          }
          .print-section,
          .print-section * {
            visibility: visible;
          }
          .print-section {
            position: absolute;
            left: 0;
            top: 0;
          }
        }

        @keyframes spin {
          to {
            transform: rotate(360deg);
          }
        }
        .spinner {
          border: 4px solid rgba(0, 0, 0, 0.1);
          border-left-color: #3498db;
          border-radius: 50%;
          width: 24px;
          height: 24px;
          animation: spin 1s linear infinite;
          margin: 0 auto;
        }
      `}</style>
    </div>
  );
}

export default MainComponent;
