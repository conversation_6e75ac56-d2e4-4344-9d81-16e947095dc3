'use client';

import {
    BarElement,
    CategoryScale,
    Chart as ChartJ<PERSON>,
    Filler,
    Legend,
    LinearScale,
    LineElement,
    PointElement,
    Title,
    Tooltip
} from 'chart.js';
import { useEffect, useState } from 'react';
import { Chart } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

// مكون رسم بياني مبسط وفعال
export default function ProfessionalChart({ title, data, type = 'cars', selectedYear }) {
  const [isClient, setIsClient] = useState(false);
  const [fromMonth, setFromMonth] = useState('');
  const [toMonth, setToMonth] = useState('');

  useEffect(() => {
    setIsClient(true);
  }, []);

  // قائمة الشهور
  const months = [
    { value: '01', label: 'يناير' },
    { value: '02', label: 'فبراير' },
    { value: '03', label: 'مارس' },
    { value: '04', label: 'أبريل' },
    { value: '05', label: 'مايو' },
    { value: '06', label: 'يونيو' },
    { value: '07', label: 'يوليو' },
    { value: '08', label: 'أغسطس' },
    { value: '09', label: 'سبتمبر' },
    { value: '10', label: 'أكتوبر' },
    { value: '11', label: 'نوفمبر' },
    { value: '12', label: 'ديسمبر' }
  ];

  // التأكد من وجود البيانات وتحويلها للتنسيق المطلوب
  if (!data || data.length === 0) {
    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <h3 className="text-xl font-bold text-gray-800 dark:text-gray-100 mb-4">{title}</h3>
        <div className="text-center py-12">
          <p className="text-gray-500 dark:text-gray-400">لا توجد بيانات متاحة</p>
        </div>
      </div>
    );
  }

  // تصفية البيانات حسب السنة المختارة (تأتي من الصفحة الرئيسية)
  const yearFilteredData = selectedYear ? data.filter(item => item.year === parseInt(selectedYear)) : data;

  // دالة لتحويل اسم الشهر إلى رقم
  const getMonthNumber = (monthName) => {
    const monthMap = {
      'يناير': 1, 'فبراير': 2, 'مارس': 3, 'أبريل': 4, 'ابريل': 4,
      'مايو': 5, 'يونيو': 6, 'يوليو': 7, 'أغسطس': 8, 'اغسطس': 8,
      'سبتمبر': 9, 'أكتوبر': 10, 'اكتوبر': 10, 'نوفمبر': 11, 'ديسمبر': 12
    };
    return monthMap[monthName] || 1;
  };

  // تصفية البيانات حسب الشهور المختارة
  const getFilteredData = () => {
    let filtered = yearFilteredData;

    if (fromMonth && toMonth) {
      filtered = filtered.filter(item => {
        const itemMonth = getMonthNumber(item.month);
        const fromMonthNum = parseInt(fromMonth);
        const toMonthNum = parseInt(toMonth);

        if (fromMonthNum <= toMonthNum) {
          // نفس السنة
          return itemMonth >= fromMonthNum && itemMonth <= toMonthNum;
        } else {
          // عبر سنتين (مثل من ديسمبر إلى فبراير)
          return itemMonth >= fromMonthNum || itemMonth <= toMonthNum;
        }
      });
    } else if (fromMonth) {
      // من شهر معين إلى نهاية السنة
      const fromMonthNum = parseInt(fromMonth);
      filtered = filtered.filter(item => {
        const itemMonth = getMonthNumber(item.month);
        return itemMonth >= fromMonthNum;
      });
    } else if (toMonth) {
      // من بداية السنة إلى شهر معين
      const toMonthNum = parseInt(toMonth);
      filtered = filtered.filter(item => {
        const itemMonth = getMonthNumber(item.month);
        return itemMonth <= toMonthNum;
      });
    }

    // ترتيب البيانات حسب الشهر
    return filtered.sort((a, b) => {
      const monthA = getMonthNumber(a.month);
      const monthB = getMonthNumber(b.month);
      return monthA - monthB;
    });
  };

  const filteredData = getFilteredData();

  // فحص وجود بيانات بعد التصفية
  if (filteredData.length === 0) {
    return (
      <div className="bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 rounded-2xl shadow-2xl p-8 text-white">
        <div className="text-center">
          <h3 className="text-xl font-bold text-white mb-4">{title}</h3>
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
            <p className="text-gray-200 mb-2">لا توجد بيانات في الفترة المختارة</p>
            {(fromMonth || toMonth) && (
              <p className="text-sm text-blue-200">
                جرب تغيير فترة الشهور أو اختر "جميع الشهور"
              </p>
            )}
          </div>
        </div>
      </div>
    );
  }

  // تحويل البيانات للتنسيق المطلوب للرسم البياني
  const labels = filteredData.map(item => `${item.month} ${item.year}`);
  const costs = filteredData.map(item => item.cost);
  const counts = filteredData.map(item => item.count);

  // دالة تنسيق الأرقام
  const formatNumber = (num) => {
    if (!isClient) return num.toString();
    return num.toLocaleString('ar-EG');
  };

  // ألوان وتدرجات عصرية حسب النوع
  const getColors = () => {
    switch(type) {
      case 'cars':
        return {
          primary: '#3B82F6',
          gradient: ['rgba(59, 130, 246, 0.8)', 'rgba(59, 130, 246, 0.1)'],
          border: '#2563EB',
          glow: 'rgba(59, 130, 246, 0.4)'
        };
      case 'apartments':
        return {
          primary: '#10B981',
          gradient: ['rgba(16, 185, 129, 0.8)', 'rgba(16, 185, 129, 0.1)'],
          border: '#059669',
          glow: 'rgba(16, 185, 129, 0.4)'
        };
      default:
        return {
          primary: '#8B5CF6',
          gradient: ['rgba(139, 92, 246, 0.8)', 'rgba(139, 92, 246, 0.1)'],
          border: '#7C3AED',
          glow: 'rgba(139, 92, 246, 0.4)'
        };
    }
  };

  const colors = getColors();

  // إنشاء التدرج للخلفية
  const createGradient = (ctx) => {
    const gradient = ctx.createLinearGradient(0, 0, 0, 400);
    gradient.addColorStop(0, colors.gradient[0]);
    gradient.addColorStop(1, colors.gradient[1]);
    return gradient;
  };

  const chartData = {
    labels: labels,
    datasets: [
      {
        type: 'bar',
        label: 'التكلفة الشهرية',
        data: costs,
        backgroundColor: (context) => {
          const chart = context.chart;
          const {ctx, chartArea} = chart;
          if (!chartArea) return null;
          return createGradient(ctx);
        },
        borderColor: colors.border,
        borderWidth: 2,
        borderRadius: 8,
        borderSkipped: false,
        yAxisID: 'y'
      },
      {
        type: 'line',
        label: 'خط الاتجاه',
        data: costs,
        borderColor: '#F59E0B',
        backgroundColor: 'rgba(245, 158, 11, 0.1)',
        borderWidth: 4,
        fill: false,
        tension: 0.4, // منحنيات ناعمة
        pointBackgroundColor: '#F59E0B',
        pointBorderColor: '#ffffff',
        pointBorderWidth: 3,
        pointRadius: 6,
        pointHoverRadius: 10,
        pointHoverBackgroundColor: '#F59E0B',
        pointHoverBorderColor: '#ffffff',
        pointHoverBorderWidth: 4,
        yAxisID: 'y'
      }
    ]
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    elements: {
      point: {
        hoverRadius: 12,
      }
    },
    scales: {
      x: {
        grid: {
          display: true,
          color: 'rgba(255, 255, 255, 0.1)',
          lineWidth: 1,
        },
        ticks: {
          color: '#6B7280',
          font: {
            size: 12,
            weight: '500'
          }
        }
      },
      y: {
        beginAtZero: true,
        grid: {
          display: true,
          color: 'rgba(255, 255, 255, 0.1)',
          lineWidth: 1,
        },
        ticks: {
          color: '#6B7280',
          font: {
            size: 12,
            weight: '500'
          },
          callback: (value) => formatNumber(value) + ' ج.م'
        }
      }
    },
    plugins: {
      legend: {
        display: false // إخفاء المفتاح التقليدي
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.9)',
        titleColor: '#ffffff',
        bodyColor: '#ffffff',
        borderColor: colors.primary,
        borderWidth: 2,
        cornerRadius: 12,
        displayColors: false,
        titleFont: {
          size: 14,
          weight: 'bold'
        },
        bodyFont: {
          size: 13
        },
        padding: 16,
        callbacks: {
          title: function(context) {
            return `📅 ${context[0].label}`;
          },
          label: function(context) {
            const dataIndex = context.dataIndex;
            const cost = formatNumber(context.parsed.y);
            const count = counts[dataIndex];

            if (context.datasetIndex === 0) {
              // للأعمدة
              return [
                `💰 التكلفة: ${cost} ج.م`,
                `📊 العدد: ${count}`,
                `📈 متوسط التكلفة للوحدة: ${formatNumber(context.parsed.y / count)} ج.م`
              ];
            } else {
              // للخط
              return `📈 اتجاه التكلفة: ${cost} ج.م`;
            }
          }
        }
      }
    },
    animation: {
      duration: 2000,
      easing: 'easeInOutQuart'
    },
    // خيارات خاصة للطباعة
    onResize: (chart, size) => {
      // تحديث الألوان للطباعة
      if (window.matchMedia && window.matchMedia('print').matches) {
        // تحديث ألوان الشبكة للطباعة
        chart.options.scales.x.grid.color = '#E5E7EB';
        chart.options.scales.y.grid.color = '#E5E7EB';
        chart.options.scales.x.ticks.color = '#374151';
        chart.options.scales.y.ticks.color = '#374151';
        chart.update('none');
      }
    }
  };

  return (
    <div className="relative overflow-hidden rounded-2xl shadow-2xl mb-8 print:bg-white print:shadow-none print:border-2 print:border-gray-300"
         style={{
           background: 'linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%)',
           backdropFilter: 'blur(10px)',
           border: '1px solid rgba(255,255,255,0.1)'
         }}>

      {/* خلفية متدرجة - مخفية في الطباعة */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 dark:from-gray-800 dark:via-gray-900 dark:to-black print:hidden"></div>

      {/* تأثيرات الإضاءة - مخفية في الطباعة */}
      <div className="absolute top-0 left-0 w-full h-1 print:hidden"
           style={{ background: `linear-gradient(90deg, transparent, ${colors.primary}, transparent)` }}></div>

      <div className="relative z-10 p-8 print:p-4">
        {/* عنوان للشاشة */}
        <div className="flex items-center justify-between mb-8 print:hidden">
          <div className="flex items-center gap-4">
            <div className="w-3 h-3 rounded-full animate-pulse"
                 style={{ backgroundColor: colors.primary, boxShadow: `0 0 20px ${colors.glow}` }}></div>
            <h3 className="text-2xl font-bold text-white">{title}</h3>
          </div>

          {/* معلومات السنة المختارة */}
          <div className="flex items-center gap-3">
            <div className="bg-white/10 backdrop-blur-sm rounded-lg px-3 py-1 border border-white/20">
              <span className="text-sm text-gray-300">السنة المختارة: </span>
              <span className="text-white font-medium">{selectedYear || 'جميع السنوات'}</span>
            </div>
          </div>
        </div>

        {/* عنوان للطباعة */}
        <div className="hidden print:block mb-6 text-center border-b-2 border-gray-300 pb-4">
          <h3 className="text-xl font-bold text-gray-900">{title}</h3>
          <p className="text-sm text-gray-600 mt-1">
            {selectedYear ? `سنة ${selectedYear}` : 'جميع السنوات'} •
            {filteredData.length} شهر
            {(fromMonth || toMonth) && (
              <span className="text-blue-600 font-medium">
                {' • '}
                {fromMonth && toMonth ? (
                  `من ${months.find(m => m.value === fromMonth)?.label} إلى ${months.find(m => m.value === toMonth)?.label}`
                ) : fromMonth ? (
                  `من ${months.find(m => m.value === fromMonth)?.label}`
                ) : (
                  `حتى ${months.find(m => m.value === toMonth)?.label}`
                )}
              </span>
            )}
          </p>
        </div>

        {/* فلاتر الشهور */}
        <div className="mb-6 print:hidden">
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
            <div className="flex items-center gap-4 flex-wrap">
              <span className="text-sm text-gray-300 font-medium">تصفية الشهور:</span>

              {/* من شهر */}
              <div className="flex items-center gap-2">
                <label className="text-sm text-gray-300">من:</label>
                <select
                  value={fromMonth}
                  onChange={(e) => setFromMonth(e.target.value)}
                  className="px-3 py-1 bg-white/20 border border-white/30 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="" className="bg-gray-800 text-white">جميع الشهور</option>
                  {months.map(month => (
                    <option key={month.value} value={month.value} className="bg-gray-800 text-white">
                      {month.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* إلى شهر */}
              <div className="flex items-center gap-2">
                <label className="text-sm text-gray-300">إلى:</label>
                <select
                  value={toMonth}
                  onChange={(e) => setToMonth(e.target.value)}
                  className="px-3 py-1 bg-white/20 border border-white/30 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="" className="bg-gray-800 text-white">نهاية السنة</option>
                  {months.map(month => (
                    <option key={month.value} value={month.value} className="bg-gray-800 text-white">
                      {month.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* زر إعادة تعيين */}
              {(fromMonth || toMonth) && (
                <button
                  onClick={() => {
                    setFromMonth('');
                    setToMonth('');
                  }}
                  className="px-3 py-1 bg-red-500/20 border border-red-500/30 rounded-lg text-red-300 text-sm hover:bg-red-500/30 transition-colors"
                >
                  إعادة تعيين
                </button>
              )}

              {/* عرض الفترة المختارة */}
              {(fromMonth || toMonth) && (
                <div className="text-sm text-blue-300 bg-blue-500/20 px-3 py-1 rounded-lg border border-blue-500/30">
                  {fromMonth && toMonth ? (
                    `من ${months.find(m => m.value === fromMonth)?.label} إلى ${months.find(m => m.value === toMonth)?.label}`
                  ) : fromMonth ? (
                    `من ${months.find(m => m.value === fromMonth)?.label} إلى نهاية السنة`
                  ) : (
                    `من بداية السنة إلى ${months.find(m => m.value === toMonth)?.label}`
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* خط فاصل للشاشة */}
        <div className="h-px bg-gradient-to-r from-transparent via-white/20 to-transparent mb-8 print:hidden"></div>

        {/* إحصائيات للشاشة */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8 print:hidden">
          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
            <p className="text-sm text-gray-300 mb-1">
              {selectedYear === 'all' ? 'إجمالي التكلفة' : `تكلفة ${selectedYear}`}
            </p>
            <p className="text-xl font-bold text-white">
              {formatNumber(costs.reduce((sum, cost) => sum + cost, 0))} ج.م
            </p>
            <div className="w-full h-1 bg-white/20 rounded-full mt-2">
              <div className="h-full rounded-full"
                   style={{ backgroundColor: colors.primary, width: '100%' }}></div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
            <p className="text-sm text-gray-300 mb-1">متوسط العدد</p>
            <p className="text-xl font-bold text-white">
              {counts.length > 0 ? Math.round(counts.reduce((sum, count) => sum + count, 0) / counts.length) : 0}
            </p>
            <div className="w-full h-1 bg-white/20 rounded-full mt-2">
              <div className="h-full bg-emerald-500 rounded-full" style={{ width: '85%' }}></div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
            <p className="text-sm text-gray-300 mb-1">أعلى تكلفة</p>
            <p className="text-xl font-bold text-white">
              {costs.length > 0 ? formatNumber(Math.max(...costs)) : 0} ج.م
            </p>
            <div className="w-full h-1 bg-white/20 rounded-full mt-2">
              <div className="h-full bg-red-500 rounded-full" style={{ width: '70%' }}></div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
            <p className="text-sm text-gray-300 mb-1">متوسط التكلفة الشهرية</p>
            <p className="text-xl font-bold text-white">
              {costs.length > 0 ? formatNumber(costs.reduce((sum, cost) => sum + cost, 0) / costs.length) : 0} ج.م
            </p>
            <div className="w-full h-1 bg-white/20 rounded-full mt-2">
              <div className="h-full bg-yellow-500 rounded-full" style={{ width: '60%' }}></div>
            </div>
          </div>
        </div>

        {/* مؤشر الفلترة للشاشة */}
        {selectedYear !== 'all' && (
          <div className="bg-blue-500/20 backdrop-blur-sm rounded-lg p-3 mb-6 border border-blue-400/30 print:hidden">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
              <span className="text-blue-200 text-sm font-medium">
                عرض بيانات سنة {selectedYear} فقط • {filteredData.length} شهر
              </span>
            </div>
          </div>
        )}

        {/* إحصائيات مبسطة للطباعة */}
        <div className="hidden print:block mb-6">
          <div className="grid grid-cols-4 gap-4 text-center">
            <div className="border border-gray-300 rounded p-3">
              <p className="text-xs text-gray-600 mb-1">إجمالي التكلفة</p>
              <p className="text-sm font-bold text-gray-900">
                {formatNumber(costs.reduce((sum, cost) => sum + cost, 0))} ج.م
              </p>
            </div>
            <div className="border border-gray-300 rounded p-3">
              <p className="text-xs text-gray-600 mb-1">متوسط العدد</p>
              <p className="text-sm font-bold text-gray-900">
                {costs.length > 0 ? Math.round(counts.reduce((sum, count) => sum + count, 0) / counts.length) : 0}
              </p>
            </div>
            <div className="border border-gray-300 rounded p-3">
              <p className="text-xs text-gray-600 mb-1">أعلى تكلفة</p>
              <p className="text-sm font-bold text-gray-900">
                {costs.length > 0 ? formatNumber(Math.max(...costs)) : 0} ج.م
              </p>
            </div>
            <div className="border border-gray-300 rounded p-3">
              <p className="text-xs text-gray-600 mb-1">متوسط التكلفة</p>
              <p className="text-sm font-bold text-gray-900">
                {costs.length > 0 ? formatNumber(costs.reduce((sum, cost) => sum + cost, 0) / costs.length) : 0} ج.م
              </p>
            </div>
          </div>
        </div>

        {/* منطقة الرسم البياني */}
        <div className="bg-white/5 backdrop-blur-sm rounded-xl p-6 border border-white/10 print:bg-white print:border-gray-300 print:p-4"
             style={{ height: '450px' }}>
          {isClient && (
            <Chart type='bar' data={chartData} options={options} />
          )}
        </div>

        {/* مؤشر الألوان العصري - مخفي في الطباعة */}
        <div className="flex justify-center mt-6 print:hidden">
          <div className="bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 border border-white/20">
            <div className="flex items-center gap-6">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 rounded"
                     style={{ backgroundColor: colors.primary, boxShadow: `0 0 10px ${colors.glow}` }}></div>
                <span className="text-white font-medium text-sm">التكلفة الشهرية</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-1 rounded-full bg-yellow-500"
                     style={{ boxShadow: '0 0 10px rgba(245, 158, 11, 0.4)' }}></div>
                <span className="text-white font-medium text-sm">خط الاتجاه</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
