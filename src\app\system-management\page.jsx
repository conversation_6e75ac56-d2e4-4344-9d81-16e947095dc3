'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import MonthlyAttendance from './monthly-attendance';
import {
  FiDatabase,
  FiSettings,
  FiCheck,
  FiX,
  FiRefreshCw,
  FiPlay,
  FiCheckCircle,
  FiAlertCircle,
  FiLoader,
  FiUsers,
  FiHome,
  FiTruck,
  FiBell,
  FiCalendar,
  FiBarChart,
  FiFileText,
  FiShield
} from 'react-icons/fi';

export default function SystemManagementPage() {
  const [systemStatus, setSystemStatus] = useState(null);
  const [loading, setLoading] = useState(false);
  const [activeSection, setActiveSection] = useState('overview');

  useEffect(() => {
    checkSystemStatus();
  }, []);

  const checkSystemStatus = async () => {
    try {
      setLoading(true);
      
      // فحص حالة النظام
      const response = await fetch('/api/complete-setup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'testAllConnections' })
      });

      const result = await response.json();
      setSystemStatus(result);

    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  const runSystemAction = async (action, actionName) => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/complete-setup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action })
      });

      const result = await response.json();
      setSystemStatus(result);

      // إعادة فحص حالة النظام بعد التنفيذ
      setTimeout(() => {
        checkSystemStatus();
      }, 1000);
      
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  const systemModules = [
    {
      id: 'employees',
      title: 'إدارة الموظفين',
      icon: FiUsers,
      description: 'نظام إدارة بيانات الموظفين والأقسام',
      status: 'active',
      link: '/employees'
    },
    {
      id: 'apartments',
      title: 'إدارة الشقق',
      icon: FiHome,
      description: 'نظام إدارة الشقق والمستفيدين',
      status: 'pending',
      link: '/apartments'
    },
    {
      id: 'cars',
      title: 'إدارة السيارات',
      icon: FiTruck,
      description: 'نظام إدارة السيارات والنقل',
      status: 'pending',
      link: '/cars'
    },
    {
      id: 'attendance',
      title: 'الحضور والانصراف',
      icon: FiCalendar,
      description: 'نظام تتبع الحضور والإجازات',
      status: 'pending',
      link: '/attendance'
    },
    {
      id: 'costs',
      title: 'إدارة التكاليف',
      icon: FiBarChart,
      description: 'نظام إدارة التكاليف والمصروفات',
      status: 'pending',
      link: '/costs'
    },
    {
      id: 'reports',
      title: 'التقارير',
      icon: FiFileText,
      description: 'نظام التقارير والإحصائيات',
      status: 'pending',
      link: '/reports'
    },
    {
      id: 'alerts',
      title: 'التنبيهات',
      icon: FiBell,
      description: 'نظام التنبيهات والإشعارات',
      status: 'active',
      link: '/alerts'
    },
    {
      id: 'security',
      title: 'الأمان والصلاحيات',
      icon: FiShield,
      description: 'نظام إدارة المستخدمين والصلاحيات',
      status: 'pending',
      link: '/security'
  },
  {
    id: 'monthlyAttendance',
    title: 'الحضور الشهري',
    icon: FiCalendar,
    description: 'نظام عرض بيانات الحضور الشهري للموظفين',
    status: 'active',
    link: '/monthly-attendance'
  }
];

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'error': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'active': return 'نشط';
      case 'pending': return 'قيد التطوير';
      case 'error': return 'خطأ';
      default: return 'غير محدد';
    }
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto p-6">
        {/* رأس الصفحة */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FiSettings className="text-3xl text-blue-600" />
              <div>
                <h1 className="text-3xl font-bold text-gray-800 dark:text-white">
                  إدارة النظام الشاملة
                </h1>
                <p className="text-gray-600 dark:text-gray-300">
                  مراقبة وإدارة جميع أجزاء النظام
                </p>
              </div>
            </div>
            <div className="flex gap-2">
              <button
                onClick={checkSystemStatus}
                disabled={loading}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2 disabled:opacity-50"
              >
                <FiRefreshCw className={loading ? 'animate-spin' : ''} />
                فحص النظام
              </button>
              <button
                onClick={() => window.location.href = '/dashboard'}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2"
              >
                <FiCheckCircle />
                الداش بورد
              </button>
            </div>
          </div>
        </div>

        {/* التبويبات */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm mb-6">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveSection('overview')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeSection === 'overview'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <FiDatabase />
                  نظرة عامة
                </div>
              </button>
              
              <button
                onClick={() => setActiveSection('modules')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeSection === 'modules'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <FiSettings />
                  وحدات النظام
                </div>
              </button>

              <button
                onClick={() => setActiveSection('actions')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeSection === 'actions'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <FiPlay />
                  إجراءات النظام
                </div>
              </button>
            </nav>
          </div>

          {/* محتوى التبويبات */}
          <div className="p-6">
            {/* نظرة عامة */}
            {activeSection === 'overview' && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
                  حالة النظام العامة
                </h2>

                {loading ? (
                  <div className="text-center py-12">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="text-gray-600 dark:text-gray-300 mt-4">جاري فحص النظام...</p>
                  </div>
                ) : systemStatus ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {systemStatus.tests && systemStatus.tests.map((test, index) => (
                      <div key={index} className={`p-4 rounded-lg border ${
                        test.success 
                          ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                          : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
                      }`}>
                        <div className="flex items-center gap-2 mb-2">
                          {test.success ? (
                            <FiCheck className="text-green-500" />
                          ) : (
                            <FiX className="text-red-500" />
                          )}
                          <span className="font-medium text-gray-800 dark:text-white">
                            {test.table}
                          </span>
                        </div>
                        {test.success ? (
                          <p className="text-sm text-green-600 dark:text-green-300">
                            عدد السجلات: {test.count}
                          </p>
                        ) : (
                          <p className="text-sm text-red-600 dark:text-red-300">
                            {test.error}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <FiDatabase className="text-4xl text-gray-300 mx-auto mb-4" />
                    <p className="text-gray-500">اضغط على "فحص النظام" لبدء الفحص</p>
                  </div>
                )}
              </div>
            )}

            {/* وحدات النظام */}
            {activeSection === 'modules' && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
                  وحدات النظام المتاحة
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {systemModules.map((module) => {
                    const IconComponent = module.icon;
                    return (
                      <div key={module.id} className="bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 p-6 hover:shadow-md transition-shadow">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center gap-3">
                            <IconComponent className="text-2xl text-blue-600" />
                            <div>
                              <h3 className="font-medium text-gray-800 dark:text-white">
                                {module.title}
                              </h3>
                              <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(module.status)}`}>
                                {getStatusText(module.status)}
                              </span>
                            </div>
                          </div>
                        </div>
                        
                        <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                          {module.description}
                        </p>
                        
                        <button
                          onClick={() => window.location.href = module.link}
                          disabled={module.status !== 'active'}
                          className={`w-full py-2 px-4 rounded-lg text-sm font-medium transition-colors ${
                            module.status === 'active'
                              ? 'bg-blue-600 text-white hover:bg-blue-700'
                              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                          }`}
                        >
                          {module.status === 'active' ? 'فتح الوحدة' : 'قيد التطوير'}
                        </button>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* إجراءات النظام */}
            {activeSection === 'actions' && (
              <div className="space-y-6">
                <h2 className="text-xl font-semibold text-gray-800 dark:text-white">
                  إجراءات إدارة النظام
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <button
                    onClick={() => runSystemAction('setupComplete', 'الإعداد الكامل')}
                    disabled={loading}
                    className="bg-blue-600 text-white p-6 rounded-lg hover:bg-blue-700 disabled:opacity-50 text-left"
                  >
                    <div className="flex items-center gap-3 mb-2">
                      <FiPlay className="text-2xl" />
                      <span className="text-lg font-medium">إعداد النظام الكامل</span>
                    </div>
                    <p className="text-blue-100 text-sm">
                      إنشاء جميع الجداول وربط البيانات واختبار الاتصالات
                    </p>
                  </button>

                  <button
                    onClick={() => runSystemAction('createAllTables', 'إنشاء الجداول')}
                    disabled={loading}
                    className="bg-purple-600 text-white p-6 rounded-lg hover:bg-purple-700 disabled:opacity-50 text-left"
                  >
                    <div className="flex items-center gap-3 mb-2">
                      <FiDatabase className="text-2xl" />
                      <span className="text-lg font-medium">إنشاء الجداول</span>
                    </div>
                    <p className="text-purple-100 text-sm">
                      إنشاء جميع الجداول المطلوبة في قاعدة البيانات
                    </p>
                  </button>

                  <button
                    onClick={() => runSystemAction('linkAllData', 'ربط البيانات')}
                    disabled={loading}
                    className="bg-green-600 text-white p-6 rounded-lg hover:bg-green-700 disabled:opacity-50 text-left"
                  >
                    <div className="flex items-center gap-3 mb-2">
                      <FiRefreshCw className="text-2xl" />
                      <span className="text-lg font-medium">ربط مصادر البيانات</span>
                    </div>
                    <p className="text-green-100 text-sm">
                      ربط جميع مصادر البيانات والعلاقات بين الجداول
                    </p>
                  </button>

                  <button
                    onClick={() => runSystemAction('testAllConnections', 'اختبار الاتصالات')}
                    disabled={loading}
                    className="bg-orange-600 text-white p-6 rounded-lg hover:bg-orange-700 disabled:opacity-50 text-left"
                  >
                    <div className="flex items-center gap-3 mb-2">
                      <FiCheckCircle className="text-2xl" />
                      <span className="text-lg font-medium">اختبار الاتصالات</span>
                    </div>
                    <p className="text-orange-100 text-sm">
                      فحص جميع الجداول والتأكد من صحة الاتصالات
                    </p>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
