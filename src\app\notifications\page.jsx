'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import {
  FiBell,
  FiActivity,
  FiUser,
  FiCalendar,
  FiFilter,
  FiSearch,
  FiRefreshCw,
  FiDownload,
  FiEye,
  FiClock,
  FiCheckCircle,
  FiXCircle,
  FiBarChart
} from 'react-icons/fi';

export default function NotificationsPage() {
  const [actions, setActions] = useState([]);
  const [statistics, setStatistics] = useState(null);
  const [recentActions, setRecentActions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('actions');
  
  // فلاتر البحث
  const [filters, setFilters] = useState({
    userCode: '',
    actionType: '',
    targetTable: '',
    startDate: '',
    endDate: '',
    isSuccess: '',
    page: 1,
    limit: 50
  });

  // أنواع الإجراءات
  const actionTypes = [
    { value: '', label: 'جميع الإجراءات' },
    { value: 'LOGIN', label: 'تسجيل دخول' },
    { value: 'LOGOUT', label: 'تسجيل خروج' },
    { value: 'CREATE_EMPLOYEE', label: 'إضافة موظف' },
    { value: 'UPDATE_EMPLOYEE', label: 'تعديل موظف' },
    { value: 'DELETE_EMPLOYEE', label: 'حذف موظف' },
    { value: 'CREATE_APARTMENT', label: 'إضافة شقة' },
    { value: 'UPDATE_APARTMENT', label: 'تعديل شقة' },
    { value: 'DELETE_APARTMENT', label: 'حذف شقة' },
    { value: 'ADD_BENEFICIARY', label: 'إضافة مستفيد' },
    { value: 'REMOVE_BENEFICIARY', label: 'إزالة مستفيد' },
    { value: 'UPLOAD_DOCUMENT', label: 'رفع مستند' },
    { value: 'DELETE_DOCUMENT', label: 'حذف مستند' }
  ];

  const targetTables = [
    { value: '', label: 'جميع الجداول' },
    { value: 'Employees', label: 'الموظفين' },
    { value: 'Apartments', label: 'الشقق' },
    { value: 'ApartmentBeneficiaries', label: 'مستفيدي الشقق' },
    { value: 'Cars', label: 'السيارات' },
    { value: 'Documents', label: 'المستندات' },
    { value: 'Attendance', label: 'الحضور' },
    { value: 'LeaveRequests', label: 'طلبات الإجازات' }
  ];

  useEffect(() => {
    setupNotificationSystem();
    loadData();
  }, []);

  useEffect(() => {
    if (activeTab === 'actions') {
      loadActions();
    }
  }, [filters, activeTab]);

  // إعداد نظام الإشعارات
  const setupNotificationSystem = async () => {
    try {
      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'setup' })
      });

      const result = await response.json();
      if (!result.success) {

      }
    } catch (error) {

    }
  };

  // جلب البيانات
  const loadData = async () => {
    await Promise.all([
      loadStatistics(),
      loadRecentActions()
    ]);
  };

  // جلب الإجراءات
  const loadActions = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getActions',
          ...filters
        })
      });

      const result = await response.json();
      if (result.success) {
        setActions(result.data.actions || []);
      } else {

      }
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  // جلب الإحصائيات
  const loadStatistics = async () => {
    try {
      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getStatistics',
          startDate: filters.startDate,
          endDate: filters.endDate
        })
      });

      const result = await response.json();
      if (result.success) {
        setStatistics(result.data);
      }
    } catch (error) {

    }
  };

  // جلب الإجراءات الحديثة
  const loadRecentActions = async () => {
    try {
      const response = await fetch('/api/notifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getRecentActions',
          limit: 10
        })
      });

      const result = await response.json();
      if (result.success) {
        setRecentActions(result.data || []);
      }
    } catch (error) {

    }
  };

  // تحديث الفلاتر
  const updateFilter = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // إعادة تعيين الصفحة عند تغيير الفلتر
    }));
  };

  // إعادة تعيين الفلاتر
  const resetFilters = () => {
    setFilters({
      userCode: '',
      actionType: '',
      targetTable: '',
      startDate: '',
      endDate: '',
      isSuccess: '',
      page: 1,
      limit: 50
    });
  };

  // تنسيق التاريخ
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('ar-EG', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // أيقونة حالة الإجراء
  const getStatusIcon = (isSuccess) => {
    return isSuccess ? (
      <FiCheckCircle className="w-4 h-4 text-green-500" />
    ) : (
      <FiXCircle className="w-4 h-4 text-red-500" />
    );
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto p-6">
        {/* رأس الصفحة */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FiBell className="text-3xl text-blue-600" />
              <div>
                <h1 className="text-3xl font-bold text-gray-800 dark:text-white">
                  نظام الإشعارات وسجل الإجراءات
                </h1>
                <p className="text-gray-600 dark:text-gray-300">
                  تتبع جميع الإجراءات والأنشطة في النظام
                </p>
              </div>
            </div>
            <button
              onClick={loadData}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
            >
              <FiRefreshCw />
              تحديث
            </button>
          </div>
        </div>

        {/* الإحصائيات */}
        {statistics && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border-l-4 border-blue-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
                    إجمالي الإجراءات
                  </p>
                  <p className="text-3xl font-bold text-blue-600">
                    {statistics.general?.TotalActions || 0}
                  </p>
                </div>
                <FiActivity className="text-2xl text-blue-600" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border-l-4 border-green-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
                    إجراءات ناجحة
                  </p>
                  <p className="text-3xl font-bold text-green-600">
                    {statistics.general?.SuccessfulActions || 0}
                  </p>
                </div>
                <FiCheckCircle className="text-2xl text-green-600" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border-l-4 border-red-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
                    إجراءات فاشلة
                  </p>
                  <p className="text-3xl font-bold text-red-600">
                    {statistics.general?.FailedActions || 0}
                  </p>
                </div>
                <FiXCircle className="text-2xl text-red-600" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border-l-4 border-purple-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
                    مستخدمين نشطين
                  </p>
                  <p className="text-3xl font-bold text-purple-600">
                    {statistics.general?.UniqueUsers || 0}
                  </p>
                </div>
                <FiUser className="text-2xl text-purple-600" />
              </div>
            </div>
          </div>
        )}

        {/* التبويبات */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm mb-6">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('actions')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'actions'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <FiActivity />
                  سجل الإجراءات
                </div>
              </button>
              
              <button
                onClick={() => setActiveTab('recent')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'recent'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <FiClock />
                  الإجراءات الحديثة
                </div>
              </button>

              <button
                onClick={() => setActiveTab('statistics')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'statistics'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <FiBarChart />
                  الإحصائيات التفصيلية
                </div>
              </button>
            </nav>
          </div>

          {/* محتوى التبويبات */}
          <div className="p-6">
            {/* تبويب سجل الإجراءات */}
            {activeTab === 'actions' && (
              <div>
                {/* فلاتر البحث */}
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        كود المستخدم
                      </label>
                      <input
                        type="text"
                        value={filters.userCode}
                        onChange={(e) => updateFilter('userCode', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                        placeholder="البحث بكود المستخدم"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        نوع الإجراء
                      </label>
                      <select
                        value={filters.actionType}
                        onChange={(e) => updateFilter('actionType', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                      >
                        {actionTypes.map(type => (
                          <option key={type.value} value={type.value}>
                            {type.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        الجدول المستهدف
                      </label>
                      <select
                        value={filters.targetTable}
                        onChange={(e) => updateFilter('targetTable', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                      >
                        {targetTables.map(table => (
                          <option key={table.value} value={table.value}>
                            {table.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        من تاريخ
                      </label>
                      <input
                        type="date"
                        value={filters.startDate}
                        onChange={(e) => updateFilter('startDate', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        إلى تاريخ
                      </label>
                      <input
                        type="date"
                        value={filters.endDate}
                        onChange={(e) => updateFilter('endDate', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        الحالة
                      </label>
                      <select
                        value={filters.isSuccess}
                        onChange={(e) => updateFilter('isSuccess', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                      >
                        <option value="">جميع الحالات</option>
                        <option value="true">ناجح</option>
                        <option value="false">فاشل</option>
                      </select>
                    </div>
                  </div>

                  <div className="flex gap-3 mt-4">
                    <button
                      onClick={loadActions}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
                    >
                      <FiSearch />
                      بحث
                    </button>
                    <button
                      onClick={resetFilters}
                      className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center gap-2"
                    >
                      <FiRefreshCw />
                      إعادة تعيين
                    </button>
                  </div>
                </div>

                {/* جدول الإجراءات */}
                <div className="overflow-x-auto">
                  {loading ? (
                    <div className="text-center py-12">
                      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                      <p className="text-gray-600 dark:text-gray-300 mt-4">جاري تحميل الإجراءات...</p>
                    </div>
                  ) : actions.length === 0 ? (
                    <div className="text-center py-12">
                      <FiActivity className="text-6xl text-gray-300 mx-auto mb-4" />
                      <h3 className="text-xl font-medium text-gray-600 mb-2">لا توجد إجراءات</h3>
                      <p className="text-gray-500">لم يتم العثور على إجراءات مطابقة للفلاتر المحددة</p>
                    </div>
                  ) : (
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-700">
                        <tr>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الحالة
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            المستخدم
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            نوع الإجراء
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الوصف
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الجدول المستهدف
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            التاريخ
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الإجراءات
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        {actions.map((action) => (
                          <tr key={action.ID} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td className="px-6 py-4 whitespace-nowrap">
                              {getStatusIcon(action.IsSuccess)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div>
                                <div className="text-sm font-medium text-gray-900 dark:text-white">
                                  {action.UserName || action.UserCode}
                                </div>
                                <div className="text-sm text-gray-500 dark:text-gray-400">
                                  {action.UserCode}
                                </div>
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                {action.ActionNameAr || action.ActionType}
                              </span>
                            </td>
                            <td className="px-6 py-4">
                              <div className="text-sm text-gray-900 dark:text-white max-w-xs truncate" title={action.ActionDescription}>
                                {action.ActionDescription}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                              {action.TargetTable || '-'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                              {formatDate(action.ActionDate)}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                              <button
                                onClick={() => {
                                  // يمكن إضافة نافذة لعرض تفاصيل الإجراء

                                }}
                                className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                                title="عرض التفاصيل"
                              >
                                <FiEye />
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  )}
                </div>
              </div>
            )}

            {/* تبويب الإجراءات الحديثة */}
            {activeTab === 'recent' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                  آخر 10 إجراءات في النظام
                </h3>

                {recentActions.length === 0 ? (
                  <div className="text-center py-12">
                    <FiClock className="text-6xl text-gray-300 mx-auto mb-4" />
                    <h3 className="text-xl font-medium text-gray-600 mb-2">لا توجد إجراءات حديثة</h3>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {recentActions.map((action) => (
                      <div key={action.ID} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            {getStatusIcon(action.IsSuccess)}
                            <div>
                              <div className="font-medium text-gray-900 dark:text-white">
                                {action.ActionNameAr || action.ActionType}
                              </div>
                              <div className="text-sm text-gray-600 dark:text-gray-300">
                                {action.ActionDescription}
                              </div>
                              <div className="text-xs text-gray-500 dark:text-gray-400">
                                بواسطة: {action.UserName || action.UserCode}
                              </div>
                            </div>
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {formatDate(action.ActionDate)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* تبويب الإحصائيات التفصيلية */}
            {activeTab === 'statistics' && statistics && (
              <div className="space-y-6">
                {/* أكثر المستخدمين نشاطاً */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                    أكثر المستخدمين نشاطاً
                  </h3>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-700">
                        <tr>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            المستخدم
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            إجمالي الإجراءات
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            ناجحة
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            فاشلة
                          </th>
                          <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            آخر نشاط
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        {statistics.topUsers?.map((user, index) => (
                          <tr key={index}>
                            <td className="px-6 py-4 whitespace-nowrap">
                              <div className="text-sm font-medium text-gray-900 dark:text-white">
                                {user.UserName || user.UserCode}
                              </div>
                              <div className="text-sm text-gray-500 dark:text-gray-400">
                                {user.UserCode}
                              </div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                              {user.ActionCount}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                              {user.SuccessCount}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                              {user.FailureCount}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                              {formatDate(user.LastAction)}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* إحصائيات أنواع الإجراءات */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                    إحصائيات أنواع الإجراءات
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {statistics.actionTypes?.map((actionType, index) => (
                      <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-gray-900 dark:text-white">
                            {actionType.ActionNameAr || actionType.ActionType}
                          </h4>
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            {actionType.Category}
                          </span>
                        </div>
                        <div className="space-y-1">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600 dark:text-gray-300">إجمالي:</span>
                            <span className="font-medium">{actionType.ActionCount}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-green-600">ناجحة:</span>
                            <span className="font-medium">{actionType.SuccessCount}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-red-600">فاشلة:</span>
                            <span className="font-medium">{actionType.FailureCount}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
