const { getConnection } = require('./src/utils/db.js');

async function addSampleCosts() {
  let pool = null;
  
  try {

    pool = await getConnection();
    
    // جلب البنود الموجودة
    const categoriesResult = await pool.request().query(`
      SELECT ID, CategoryName, CategoryCode, ParentID
      FROM CostCategories 
      WHERE IsActive = 1
      ORDER BY ParentID, ID
    `);
    
    const mainCategories = categoriesResult.recordset.filter(c => !c.ParentID);
    const subCategories = categoriesResult.recordset.filter(c => c.ParentID);
    
    // جلب عُهدة مستديمة موجودة
    const custodyResult = await pool.request().query(`
      SELECT TOP 1 ID FROM PermanentCustody WHERE IsActive = 1
    `);
    
    if (custodyResult.recordset.length === 0) {

      return;
    }
    
    const custodyId = custodyResult.recordset[0].ID;
    
    // التكاليف التجريبية
    const sampleCosts = [
      {
        settlementNumber: '194',
        description: 'وجبات غداء لفريق كونكورد - شهر يوليو',
        amount: 1800,
        mainCategoryCode: 'MEALS',
        subCategoryCode: 'MEALS_CONCORD',
        receiptNumber: 'REC-194'
      },
      {
        settlementNumber: '195',
        description: 'فاتورة كهرباء المكتب الرئيسي',
        amount: 650,
        mainCategoryCode: 'SERVICES',
        subCategoryCode: 'SERVICES_ELECTRICITY',
        receiptNumber: 'REC-195'
      },
      {
        settlementNumber: '196',
        description: 'مشتريات أدوات مكتبية للاستشاري',
        amount: 320,
        mainCategoryCode: 'PURCHASES',
        subCategoryCode: 'PURCHASES_CONSULTANT',
        receiptNumber: 'REC-196'
      },
      {
        settlementNumber: '197',
        description: 'انتقالات مأمورية خارجية',
        amount: 450,
        mainCategoryCode: 'TRANSPORTATION',
        subCategoryCode: 'TRANSPORT_MISSION',
        receiptNumber: 'REC-197'
      }
    ];

    for (const cost of sampleCosts) {
      // البحث عن البند الرئيسي
      const mainCat = mainCategories.find(c => c.CategoryCode === cost.mainCategoryCode);
      if (!mainCat) {

        continue;
      }
      
      // البحث عن البند الفرعي
      const subCat = subCategories.find(c => c.CategoryCode === cost.subCategoryCode && c.ParentID === mainCat.ID);
      if (!subCat) {

        continue;
      }
      
      // التحقق من عدم وجود التسوية مسبقاً
      const existingResult = await pool.request()
        .input('settlementNumber', cost.settlementNumber)
        .query(`
          SELECT COUNT(*) as count FROM IntegratedCosts 
          WHERE SettlementNumber = @settlementNumber AND IsActive = 1
        `);
      
      if (existingResult.recordset[0].count > 0) {

        continue;
      }
      
      // إضافة التكلفة
      const insertResult = await pool.request()
        .input('custodyType', 'مستديمة')
        .input('permanentCustodyId', custodyId)
        .input('mainCategoryId', mainCat.ID)
        .input('subCategoryId', subCat.ID)
        .input('costDescription', cost.description)
        .input('amount', cost.amount)
        .input('costDate', new Date().toISOString().split('T')[0])
        .input('receiptNumber', cost.receiptNumber)
        .input('status', 'تم التسوية')
        .input('settlementNumber', cost.settlementNumber)
        .input('settlementDate', new Date().toISOString().split('T')[0])
        .query(`
          INSERT INTO IntegratedCosts (
            CustodyType, PermanentCustodyID, MainCategoryID, SubCategoryID,
            CostDescription, Amount, CostDate, ReceiptNumber, Status,
            SettlementNumber, SettlementDate, IsActive, CreatedAt
          )
          VALUES (
            @custodyType, @permanentCustodyId, @mainCategoryId, @subCategoryId,
            @costDescription, @amount, @costDate, @receiptNumber, @status,
            @settlementNumber, @settlementDate, 1, GETDATE()
          )
        `);

    }
    
    // اختبار نهائي

    const finalResult = await pool.request().query(`
      SELECT 
        ic.SettlementNumber,
        mc.CategoryName as MainCategoryName,
        sc.CategoryName as SubCategoryName,
        ic.CostDescription,
        ic.Amount
      FROM IntegratedCosts ic
      LEFT JOIN CostCategories mc ON ic.MainCategoryID = mc.ID AND mc.IsActive = 1
      LEFT JOIN CostCategories sc ON ic.SubCategoryID = sc.ID AND sc.IsActive = 1
      WHERE ic.IsActive = 1
      ORDER BY ic.SettlementNumber
    `);

    finalResult.recordset.forEach(result => {
      const displayFormat = result.SubCategoryName 
        ? `${result.MainCategoryName} → ${result.SubCategoryName}`
        : result.MainCategoryName || 'غير محدد';

      console.log(`     الوصف: ${result.CostDescription.substring(0, 50)}...`);

    });

    // اختبار API

    try {
      const apiResponse = await fetch('http://localhost:3001/api/project-custody-costs?action=getCosts');
      const apiData = await apiResponse.json();
      
      if (apiData.success && apiData.costs) {

        apiData.costs.forEach(cost => {
          const displayFormat = cost.SubCategoryName 
            ? `${cost.MainCategoryName} → ${cost.SubCategoryName}`
            : cost.MainCategoryName || 'غير محدد';

        });

      }
    } catch (apiError) {

    }
    
  } catch (error) {

  } finally {
    if (pool) {
      await pool.close();
    }
  }
}

addSampleCosts();
