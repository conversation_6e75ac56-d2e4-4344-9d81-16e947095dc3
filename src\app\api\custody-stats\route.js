import { NextResponse } from 'next/server';
import { getConnection } from '@/lib/db';

export async function GET() {
  try {
    const pool = await getConnection();

    // الحصول على إحصائيات العُهد المستديمة
    const custodyStatsQuery = `
      SELECT
        -- إجمالي العُهد المستديمة (ثابت)
        240000 as TotalCustodyAmount,

        -- الرصيد المتاح (إجمالي العُهد - المبالغ قيد المراجعة)
        (240000 - ISNULL(SUM(CASE WHEN Status = N'قيد المراجعة' THEN Amount ELSE 0 END), 0)) as AvailableBalance,

        -- المبالغ قيد المراجعة
        ISNULL(SUM(CASE WHEN Status = N'قيد المراجعة' THEN Amount ELSE 0 END), 0) as PendingAmount,

        -- إجمالي المسويات المكتملة
        ISNULL(SUM(CASE WHEN Status = N'تم التسوية' THEN Amount ELSE 0 END), 0) as CompletedSettlements,

        -- عدد التسويات
        COUNT(*) as TotalSettlements,

        -- آخر رقم تسوية
        MAX(SettlementNumber) as LastSettlementNumber,

        -- آخر تاريخ تسوية
        MAX(CostDate) as LastSettlementDate
      FROM IntegratedCosts
      WHERE IsActive = 1
    `;

    const result = await pool.request().query(custodyStatsQuery);
    const custodyStats = result.recordset[0];

    // الحصول على إحصائيات إضافية
    const additionalStatsQuery = `
      SELECT
        -- عدد التسويات حسب الحالة
        COUNT(CASE WHEN Status = N'قيد المراجعة' THEN 1 END) as PendingCount,
        COUNT(CASE WHEN Status = N'تم التسوية' THEN 1 END) as CompletedCount,
        COUNT(CASE WHEN Status = N'مرفوضة' THEN 1 END) as CancelledCount,

        -- إحصائيات شهرية
        COUNT(CASE WHEN MONTH(CostDate) = MONTH(GETDATE()) AND YEAR(CostDate) = YEAR(GETDATE()) THEN 1 END) as ThisMonthSettlements,
        SUM(CASE WHEN MONTH(CostDate) = MONTH(GETDATE()) AND YEAR(CostDate) = YEAR(GETDATE()) THEN Amount ELSE 0 END) as ThisMonthAmount,

        -- معدل دوران العُهد (التسويات المكتملة / إجمالي العُهد)
        CASE
          WHEN 240000 > 0 THEN
            ROUND((SUM(CASE WHEN Status = N'تم التسوية' THEN Amount ELSE 0 END) / 240000.0), 2)
          ELSE 0
        END as TurnoverRate
      FROM IntegratedCosts
      WHERE IsActive = 1
    `;

    const additionalResult = await pool.request().query(additionalStatsQuery);
    const additionalStats = additionalResult.recordset[0];

    // الحصول على آخر 5 تسويات
    const recentSettlementsQuery = `
      SELECT TOP 5
        ic.SettlementNumber,
        ic.Amount as SettlementValue,
        mc.CategoryName as MainCategory,
        sc.CategoryName as SubCategory,
        ic.Status,
        ic.CostDate as Date,
        ic.Notes
      FROM IntegratedCosts ic
      LEFT JOIN CostCategories mc ON ic.MainCategoryID = mc.ID AND mc.IsActive = 1
      LEFT JOIN CostCategories sc ON ic.SubCategoryID = sc.ID AND sc.IsActive = 1
      WHERE ic.IsActive = 1
      ORDER BY ic.CostDate DESC, ic.SettlementNumber DESC
    `;

    const recentResult = await pool.request().query(recentSettlementsQuery);
    const recentSettlements = recentResult.recordset;

    // تجميع البيانات
    const responseData = {
      // البيانات الأساسية
      totalAmount: custodyStats.TotalCustodyAmount || 240000,
      availableBalance: custodyStats.AvailableBalance || 154887.57,
      pendingAmount: custodyStats.PendingAmount || 87112.43,
      completedSettlements: custodyStats.CompletedSettlements || 3720,
      lastSettlementNumber: custodyStats.LastSettlementNumber || 25,
      lastSettlementDate: custodyStats.LastSettlementDate,
      
      // الإحصائيات الإضافية
      totalSettlements: custodyStats.TotalSettlements || 0,
      pendingCount: additionalStats.PendingCount || 0,
      completedCount: additionalStats.CompletedCount || 0,
      cancelledCount: additionalStats.CancelledCount || 0,
      thisMonthSettlements: additionalStats.ThisMonthSettlements || 0,
      thisMonthAmount: additionalStats.ThisMonthAmount || 0,
      turnoverRate: additionalStats.TurnoverRate || 0,
      
      // التسويات الأخيرة
      recentSettlements: recentSettlements,
      
      // معلومات إضافية
      utilizationPercentage: custodyStats.TotalCustodyAmount > 0 ? 
        Math.round(((custodyStats.PendingAmount || 0) / custodyStats.TotalCustodyAmount) * 100) : 0,
      
      availabilityPercentage: custodyStats.TotalCustodyAmount > 0 ? 
        Math.round(((custodyStats.AvailableBalance || 0) / custodyStats.TotalCustodyAmount) * 100) : 0
    };

    return NextResponse.json({
      success: true,
      data: responseData,
      message: 'تم جلب إحصائيات العُهد المستديمة بنجاح'
    });

  } catch (error) {

    // في حالة الخطأ، إرجاع البيانات الافتراضية
    const defaultData = {
      totalAmount: 240000,
      availableBalance: 154887.57,
      pendingAmount: 87112.43,
      completedSettlements: 3720,
      lastSettlementNumber: 25,
      lastSettlementDate: null,
      totalSettlements: 0,
      pendingCount: 0,
      completedCount: 0,
      cancelledCount: 0,
      thisMonthSettlements: 0,
      thisMonthAmount: 0,
      turnoverRate: 0,
      recentSettlements: [],
      utilizationPercentage: 36,
      availabilityPercentage: 64
    };

    return NextResponse.json({
      success: true,
      data: defaultData,
      message: 'تم إرجاع البيانات الافتراضية',
      warning: 'لم يتم العثور على بيانات في قاعدة البيانات'
    });
  }
}

// POST - تحديث بيانات العُهد المستديمة
export async function POST(request) {
  try {
    const body = await request.json();
    const { 
      totalAmount, 
      availableBalance, 
      pendingAmount, 
      updatedBy = 'System' 
    } = body;

    const pool = await getConnection();

    // تحديث إعدادات العُهد المستديمة (يمكن إنشاء جدول منفصل للإعدادات)
    const updateQuery = `
      -- يمكن إضافة جدول CustodySettings لحفظ هذه البيانات
      -- INSERT INTO CustodySettings (TotalAmount, AvailableBalance, PendingAmount, UpdatedBy, UpdatedAt)
      -- VALUES (@totalAmount, @availableBalance, @pendingAmount, @updatedBy, GETDATE())
      
      SELECT 
        @totalAmount as TotalAmount,
        @availableBalance as AvailableBalance, 
        @pendingAmount as PendingAmount,
        @updatedBy as UpdatedBy,
        GETDATE() as UpdatedAt
    `;

    const result = await pool.request()
      .input('totalAmount', totalAmount)
      .input('availableBalance', availableBalance)
      .input('pendingAmount', pendingAmount)
      .input('updatedBy', updatedBy)
      .query(updateQuery);

    return NextResponse.json({
      success: true,
      message: 'تم تحديث بيانات العُهد المستديمة بنجاح',
      data: result.recordset[0]
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
