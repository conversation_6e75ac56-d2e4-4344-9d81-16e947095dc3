import { NextResponse } from 'next/server';
import sql from 'mssql';

// إعداد قاعدة البيانات
const dbConfig = {
  user: process.env.DB_USER || 'SA',
  password: process.env.DB_PASSWORD || 'admin@123',
  server: process.env.DB_SERVER || 'localhost\\DBOJESTA',
  database: process.env.DB_NAME || 'EMP',
  options: {
    encrypt: false,
    trustServerCertificate: true,
    enableArithAbort: true,
    instanceName: 'DBOJESTA'
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
  requestTimeout: 30000,
  connectionTimeout: 30000,
};

export async function POST(request) {
  let pool;
  
  try {
    const body = await request.json();
    const { employeeId, status, fromDate, toDate, leaveType } = body;

    // الاتصال بقاعدة البيانات
    pool = await sql.connect(dbConfig);

    // بناء الاستعلام - استخدام الأعمدة الموجودة فعلاً
    let query = `
      SELECT
        lr.ID as id,
        lr.EmployeeCode as employeeId,
        lr.EmployeeName as employeeName,
        lr.Department,
        lr.LeaveType as leaveType,
        lr.StartDate as startDate,
        lr.EndDate as endDate,
        lr.DaysCount as totalDays,
        lr.Reason,
        lr.Status as status,
        lr.RequestDate as createdAt,
        lr.RequestDate as updatedAt
      FROM PaperRequests lr
      WHERE lr.RequestType = 'leave'
    `;

    const request = pool.request();

    // إضافة شروط البحث
    if (employeeId && employeeId.trim() !== '') {
      query += ` AND lr.EmployeeCode LIKE @employeeId`;
      request.input('employeeId', sql.NVarChar, `%${employeeId}%`);
    }

    if (status && status.trim() !== '') {
      // تحويل الحالة إلى العربية
      let arabicStatus = status;
      if (status === 'pending') arabicStatus = 'قيد المراجعة';
      else if (status === 'approved') arabicStatus = 'معتمدة';
      else if (status === 'rejected') arabicStatus = 'مرفوضة';

      query += ` AND lr.Status = @status`;
      request.input('status', sql.NVarChar, arabicStatus);
    }

    if (leaveType && leaveType.trim() !== '') {
      query += ` AND lr.LeaveType = @leaveType`;
      request.input('leaveType', sql.NVarChar, leaveType);
    }

    if (fromDate && fromDate.trim() !== '') {
      query += ` AND lr.StartDate >= @fromDate`;
      request.input('fromDate', sql.Date, fromDate);
    }

    if (toDate && toDate.trim() !== '') {
      query += ` AND lr.EndDate <= @toDate`;
      request.input('toDate', sql.Date, toDate);
    }

    // ترتيب النتائج
    query += ` ORDER BY lr.CreatedAt DESC`;

    const result = await request.query(query);

    // تنسيق النتائج
    const formattedResults = result.recordset.map(record => ({
      id: record.id,
      employeeId: record.employeeId,
      employeeName: record.employeeName,
      department: record.Department,
      jobTitle: record.JobTitle,
      leaveType: getLeaveTypeLabel(record.leaveType),
      startDate: record.startDate,
      endDate: record.endDate,
      totalDays: record.totalDays,
      reason: record.Reason,
      status: convertStatusToEnglish(record.status),
      createdAt: record.CreatedAt,
      updatedAt: record.UpdatedAt
    }));

    return NextResponse.json({
      success: true,
      data: formattedResults,
      count: formattedResults.length
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'خطأ في البحث عن الإجازات',
      error: error.message
    }, { status: 500 });

  } finally {
    if (pool) {
      try {
        await pool.close();
      } catch (closeError) {

      }
    }
  }
}

// دالة لتحويل نوع الإجازة إلى نص
function getLeaveTypeLabel(leaveType) {
  const types = {
    'annual': 'إجازة سنوية',
    'emergency': 'إجازة عارضة',
    'sick': 'إجازة مرضية',
    'unpaid': 'إجازة بدون أجر',
    'badal': 'إجازة بدل',
    'other': 'إجازة أخرى'
  };

  return types[leaveType] || leaveType;
}

// دالة لتحويل الحالة من العربية إلى الإنجليزية
function convertStatusToEnglish(arabicStatus) {
  const statusMap = {
    'قيد المراجعة': 'pending',
    'معتمدة': 'approved',
    'مرفوضة': 'rejected'
  };

  return statusMap[arabicStatus] || arabicStatus;
}
