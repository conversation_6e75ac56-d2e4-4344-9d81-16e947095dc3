"use client";

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { FiSave, FiArrowLeft, FiUser, FiMail, FiPhone, FiHome, FiCalendar, FiUpload, FiDownload, FiUsers, FiFileText, FiCheck, FiX, FiAlertCircle } from 'react-icons/fi';
import DateInput from '@/components/DateInput';
import MainLayout from '@/components/MainLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import actionLogger from '@/utils/actionLogger';

// CSS للطباعة
const printStyles = `
  @media print {
    body * {
      visibility: hidden;
    }
    .print-area, .print-area * {
      visibility: visible;
    }
    .print-area {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
    }
    .print-hide {
      display: none !important;
    }
    table {
      page-break-inside: auto;
    }
    tr {
      page-break-inside: avoid;
      page-break-after: auto;
    }
    thead {
      display: table-header-group;
    }
    .print-title {
      text-align: center;
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 20px;
      color: #000 !important;
    }
    .print-summary {
      margin-bottom: 30px;
      border: 1px solid #000;
      padding: 15px;
    }
    .print-table {
      border-collapse: collapse;
      width: 100%;
      margin-bottom: 30px;
    }
    .print-table th,
    .print-table td {
      border: 1px solid #000 !important;
      padding: 8px !important;
      text-align: right !important;
      color: #000 !important;
      background: white !important;
    }
    .print-table th {
      background: #f0f0f0 !important;
      font-weight: bold !important;
    }
  }
`;

// إضافة الـ CSS للصفحة
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.type = 'text/css';
  styleSheet.innerText = printStyles;
  document.head.appendChild(styleSheet);
}

const departmentOptions = [
  { value: 'الكهرباء', label: 'الكهرباء' },
  { value: 'المساحة', label: 'المساحة' },
  { value: 'التنفيذ', label: 'التنفيذ' },
  { value: 'الإدارة', label: 'الإدارة' },
  { value: 'المكتب الفنى', label: 'المكتب الفنى' },
  { value: 'المستندات', label: 'المستندات' },
  { value: 'السلامة والصحة المهنية', label: 'السلامة والصحة المهنية' },
  { value: 'الشئون الإدارية و المالية', label: 'الشئون الإدارية و المالية' },
  { value: 'الجودة', label: 'الجودة' },
  { value: 'IT', label: 'IT' },
  { value: 'المخازن', label: 'المخازن' },
  { value: 'الميكانيكا', label: 'الميكانيكا' },
  { value: 'الأمن', label: 'الأمن' }
];

const MainComponent = () => {
  const router = useRouter();
  const { isArabic } = useLanguage();
  const fileInputRef = useRef(null);

  // حالة التبويبات
  const [activeTab, setActiveTab] = useState('single'); // 'single' or 'bulk'

  // حالة رفع الملفات
  const [uploadedFile, setUploadedFile] = useState(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadStatus, setUploadStatus] = useState(''); // 'uploading', 'success', 'error'
  const [uploadResults, setUploadResults] = useState(null);

  const [formData, setFormData] = useState({
    employeeCode: '',          // كود الموظف
    name: '',                  // الاسم
    jobTitle: '',             // المسمى الوظيفي
    department: '',           // القسم
    directManager: '',        // المدير المباشر
    nationalId: '',           // الرقم القومي
    birthDate: '',            // تاريخ الميلاد
    hireDate: '',             // تاريخ التعيين
    joinDate: '',             // تاريخ التواجد
    governorate: '',          // المحافظة
    gender: 'male',           // النوع
    employeeStatus: 'active', // حالة الموظف
    militaryService: '',      // الخدمة العسكرية
    isResident: 'no',         // مغترب (نعم/لا)
    area: '',                // المنطقة
    mobile: '',              // رقم الجوال
    email: '',               // البريد الإلكتروني
    education: '',           // المؤهل
    university: '',          // الجامعة
    specialization: '',      // التخصص
    grade: '',              // التقدير
    graduationYear: '',     // سنة التخرج
    emergencyNumber: '',    // رقم الطوارئ
    kinshipDegree: '',     // صلة القرابة
    maritalStatus: '',     // الحالة الاجتماعية
    // حقول التأمين الجديدة
    socialInsurance: '',           // التأمين الاجتماعي
    socialInsuranceNumber: '',     // رقم التأمين الاجتماعي
    socialInsuranceDate: '',       // تاريخ التأمين الاجتماعي
    medicalInsurance: '',          // التأمين الطبي
    medicalInsuranceNumber: ''     // رقم التأمين الطبي
  });

  const [governorates, setGovernorates] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedUploadType, setSelectedUploadType] = useState(null);
  const [isDownloading, setIsDownloading] = useState(false);
  const [isDownloadingUpdate, setIsDownloadingUpdate] = useState(false);

  // تحميل ملف الإضافة (موظفين جدد)
  const downloadTemplate = async () => {
    try {
      setIsDownloading(true);
      const response = await fetch('/api/employees/export-add-template');
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'ملف_إضافة_موظفين_جدد.xlsx';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        alert('خطأ في تحميل ملف الإضافة');
      }
    } catch (error) {

      alert('خطأ في تحميل ملف الإضافة');
    } finally {
      setIsDownloading(false);
    }
  };

  // تحميل ملف التحديث (البيانات المسجلة)
  const downloadUpdateTemplate = async () => {
    try {
      setIsDownloadingUpdate(true);
      const response = await fetch('/api/employees/export-update-template');
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'ملف_التحديث_البيانات_المسجلة.xlsx';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        alert('خطأ في تحميل ملف التحديث');
      }
    } catch (error) {

      alert('خطأ في تحميل ملف التحديث');
    } finally {
      setIsDownloadingUpdate(false);
    }
  };

  // معالجة رفع الملف
  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      if (file.type !== 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' &&
          file.type !== 'application/vnd.ms-excel') {
        alert('يرجى اختيار ملف Excel صحيح (.xlsx أو .xls)');
        return;
      }

      if (file.size > 5 * 1024 * 1024) { // 5MB
        alert('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت');
        return;
      }

      setUploadedFile(file);
      setUploadStatus('');
      setUploadResults(null);
    }
  };

  // رفع ومعالجة الملف
  const processExcelFile = async () => {
    if (!uploadedFile) {
      alert('يرجى اختيار ملف أولاً');
      return;
    }

    setUploadStatus('uploading');
    setUploadProgress(0);

    const formData = new FormData();
    formData.append('file', uploadedFile);

    try {
      const response = await fetch('/api/employees/bulk-add', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (response.ok) {
        setUploadStatus('success');
        setUploadResults(result);
        setUploadProgress(100);
      } else {
        setUploadStatus('error');
        setUploadResults(result);
      }
    } catch (error) {

      setUploadStatus('error');
      setUploadResults({ message: 'خطأ في الاتصال بالخادم' });
    }
  };

  // Add handleButtonClick function
  const handleButtonClick = async (documentType) => {
    try {
      setSelectedUploadType(documentType);
      setLoading(true);
      
      // Create file input element
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = documentType.includes('PDF') ? '.pdf' : 
                    documentType.includes('JPG') ? '.jpg,.jpeg,.png' : 
                    '.pdf,.jpg,.jpeg,.png';

      // Handle file selection
      input.onchange = async (e) => {
        const file = e.target.files[0];
        if (!file) {
          setLoading(false);
          return;
        }

        try {
          const uploadData = new FormData();
          uploadData.append('file', file);
          uploadData.append('type', documentType);
          // Get the employee code from the form
          uploadData.append('employeeId', formData.employeeCode || 'temp');

          const response = await fetch('/api/upload', {
            method: 'POST',
            body: uploadData
          });

          const result = await response.json();
          
          if (!response.ok) {
            throw new Error(result.error || 'فشل في رفع الملف');
          }

          alert('تم رفع المستند بنجاح');
          
        } catch (error) {

          alert('حدث خطأ أثناء رفع الملف');
        } finally {
          setLoading(false);
          setSelectedUploadType(null);
        }
      };

      input.click();
    } catch (error) {

      alert('حدث خطأ في معالجة رفع المستند');
      setLoading(false);
      setSelectedUploadType(null);
    }
  };

  // Form fields definition with updated IDs
  const formFields = [
    // البيانات الوظيفية
    [
      {
        id: 'employeeCode',
        label: 'كود الموظف *',
        type: 'text',
        required: true,
        col: 1,
        icon: <FiUser className="w-5 h-5" />,
        className: 'bg-blue-50'
      },
      {
        id: 'name',
        label: 'الاسم *',
        type: 'text',
        required: true,
        col: 2,
        icon: <FiUser className="w-5 h-5" />,
        className: 'bg-blue-50'
      }
    ],
    [
      {
        id: 'jobTitle',
        label: 'المسمى الوظيفي *',
        type: 'text',
        required: true,
        col: 1,
        className: 'bg-blue-50'
      },
      {
        id: 'department',
        label: 'القسم *',
        type: 'select',
        options: departmentOptions,
        required: true,
        col: 1,
        className: 'bg-blue-50'
      },
      {
        id: 'directManager',
        label: 'المدير المباشر *',
        type: 'text',
        required: true,
        col: 1,
        className: 'bg-blue-50'
      }
    ],
    [
      {
        id: 'hireDate',
        label: 'تاريخ التعيين *',
        type: 'date',
        required: true,
        col: 1,
        icon: <FiCalendar className="w-5 h-5" />,
        className: 'bg-blue-50'
      },
      {
        id: 'joinDate',
        label: 'تاريخ التواجد *',
        type: 'date',
        required: true,
        col: 1,
        icon: <FiCalendar className="w-5 h-5" />,
        className: 'bg-blue-50'
      },
      {
        id: 'employeeStatus',
        label: 'حالة الموظف *',
        type: 'select',
        options: [
          { value: 'active', label: 'نشط' },
          { value: 'inactive', label: 'غير نشط' }
        ],
        required: true,
        col: 1,
        className: 'bg-blue-50'
      }
    ],
    // البيانات الشخصية
    [
      {
        id: 'nationalId',
        label: 'الرقم القومي *',
        type: 'text',
        required: true,
        col: 1,
        className: 'bg-green-50'
      },
      {
        id: 'birthDate',
        label: 'تاريخ الميلاد *',
        type: 'date',
        required: true,
        col: 1,
        icon: <FiCalendar className="w-5 h-5" />,
        className: 'bg-green-50'
      },
      {
        id: 'gender',
        label: 'النوع *',
        type: 'select',
        options: [
          { value: 'male', label: 'ذكر' },
          { value: 'female', label: 'أنثى' }
        ],
        required: true,
        col: 1,
        className: 'bg-green-50'
      }
    ],
    [
      {
        id: 'governorate',
        label: 'المحافظة *',
        type: 'select',
        options: governorates,
        required: true,
        col: 1,
        className: 'bg-green-50'
      },
      {
        id: 'address',
        label: 'العنوان',
        type: 'text',
        required: false,
        col: 2,
        className: 'bg-green-50'
      }
    ],
    [
      {
        id: 'maritalStatus',
        label: 'الحالة الاجتماعية *',
        type: 'select',
        options: [
          { value: 'single', label: 'أعزب' },
          { value: 'married', label: 'متزوج' },
          { value: 'divorced', label: 'مطلق' },
          { value: 'widowed', label: 'أرمل' }
        ],
        required: true,
        col: 1,
        className: 'bg-green-50'
      },
      {
        id: 'militaryService',
        label: 'الخدمة العسكرية *',
        type: 'select',
        options: [
          { value: 'completed', label: 'مؤدى' },
          { value: 'exempted', label: 'معفى' },
          { value: 'postponed', label: 'مؤجل' }
        ],
        required: true,
        col: 1,
        className: 'bg-green-50'
      }
    ],
    [
      {
        id: 'mobile',
        label: 'رقم الجوال',
        type: 'tel',
        required: false,
        col: 1,
        icon: <FiPhone className="w-5 h-5" />,
        className: 'bg-green-50'
      },
      {
        id: 'email',
        label: 'البريد الإلكتروني',
        type: 'email',
        required: false,
        col: 1,
        icon: <FiMail className="w-5 h-5" />,
        className: 'bg-green-50'
      }
    ],
    [
      {
        id: 'emergencyNumber',
        label: 'رقم الطوارئ',
        type: 'tel',
        required: false,
        col: 1,
        icon: <FiPhone className="w-5 h-5" />,
        className: 'bg-green-50'
      },
      {
        id: 'kinshipDegree',
        label: 'صلة القرابة',
        type: 'text',
        required: false,
        col: 1,
        className: 'bg-green-50'
      }
    ],

    // بيانات إضافية
    [
      {
        id: 'isResident',
        label: 'مغترب *',
        type: 'select',
        options: [
          { value: 'yes', label: 'نعم' },
          { value: 'no', label: 'لا' }
        ],
        required: true,
        col: 1,
        className: 'bg-yellow-50'
      },
      {
        id: 'area',
        label: 'المنطقة',
        type: 'text',
        required: false,
        col: 2,
        className: 'bg-yellow-50'
      }
    ],
    
    // البيانات الدراسية
    [
      {
        id: 'education',
        label: 'المؤهل',
        type: 'text',
        required: false,
        col: 1,
        className: 'bg-purple-50'
      },
      {
        id: 'university',
        label: 'الجامعة',
        type: 'text',
        required: false,
        col: 1,
        className: 'bg-purple-50'
      },
      {
        id: 'specialization',
        label: 'التخصص',
        type: 'text',
        required: false,
        col: 1,
        className: 'bg-purple-50'
      }
    ],
    [
      {
        id: 'grade',
        label: 'التقدير',
        type: 'text',
        required: false,
        col: 1,
        className: 'bg-purple-50'
      },
      {
        id: 'graduationYear',
        label: 'سنة التخرج',
        type: 'text',
        required: false,
        col: 1,
        className: 'bg-purple-50'
      }
    ],

    // بيانات التأمين
    [
      {
        id: 'socialInsurance',
        label: 'التأمين الاجتماعي',
        type: 'select',
        options: [
          { value: 'مؤمن', label: 'مؤمن' },
          { value: 'غير مؤمن', label: 'غير مؤمن' }
        ],
        required: false,
        col: 1,
        className: 'bg-orange-50'
      },
      {
        id: 'socialInsuranceNumber',
        label: 'رقم التأمين الاجتماعي',
        type: 'text',
        required: false,
        col: 1,
        className: 'bg-orange-50'
      },
      {
        id: 'socialInsuranceDate',
        label: 'تاريخ التأمين الاجتماعي',
        type: 'date',
        required: false,
        col: 1,
        className: 'bg-orange-50'
      }
    ],
    [
      {
        id: 'medicalInsurance',
        label: 'التأمين الطبي',
        type: 'select',
        options: [
          { value: 'مؤمن', label: 'مؤمن' },
          { value: 'غير مؤمن', label: 'غير مؤمن' }
        ],
        required: false,
        col: 1,
        className: 'bg-orange-50'
      },
      {
        id: 'medicalInsuranceNumber',
        label: 'رقم التأمين الطبي',
        type: 'text',
        required: false,
        col: 1,
        className: 'bg-orange-50'
      }
    ]
  ];

  // جلب قائمة المحافظات
  useEffect(() => {
    const fetchGovernorates = async () => {
      try {
        const response = await fetch('/api/governorates');
        if (!response.ok) throw new Error('فشل في جلب قائمة المحافظات');
        const data = await response.json();
        // تعيين قائمة المحافظات مباشرة
        const governoratesList = data.map(gov => ({
          value: gov.GovernorateID.toString(),
          label: gov.GovernorateName
        }));
        setGovernorates(governoratesList);
      } catch (error) {

        alert('حدث خطأ في تحميل قائمة المحافظات');
      }
    };

    // تهيئة مسجل الإجراءات
    const userInfo = localStorage.getItem('userInfo');
    if (userInfo) {
      const user = JSON.parse(userInfo);
      actionLogger.initialize(user.username, user.username);
    }

    fetchGovernorates();
  }, []);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    let finalValue;

    switch (type) {
      case 'checkbox':
        finalValue = checked;
        break;
      case 'date':
        finalValue = value || '';
        break;
      case 'select-one':
        finalValue = value;
        break;
      default:
        // لا نقوم بـ trim هنا للسماح بالمسافات أثناء الكتابة
        finalValue = value;
    }

    setFormData(prev => ({
      ...prev,
      [name]: finalValue
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      // Required fields validation
      const requiredFields = [
        { field: 'employeeCode', label: 'كود الموظف' },
        { field: 'name', label: 'الاسم' },
        { field: 'jobTitle', label: 'المسمى الوظيفي' },
        { field: 'department', label: 'القسم' },
        { field: 'directManager', label: 'المدير المباشر' },
        { field: 'nationalId', label: 'الرقم القومي' },
        { field: 'birthDate', label: 'تاريخ الميلاد' },
        { field: 'hireDate', label: 'تاريخ التعيين' },
        { field: 'joinDate', label: 'تاريخ التواجد' },
        { field: 'governorate', label: 'المحافظة' },
        { field: 'gender', label: 'النوع' },
        { field: 'maritalStatus', label: 'الحالة الاجتماعية' },
        { field: 'employeeStatus', label: 'حالة الموظف' },
        { field: 'militaryService', label: 'الخدمة العسكرية' },
        { field: 'isResident', label: 'مغترب' },
        { field: 'housing', label: 'نوع السكن' },
        { field: 'transportation', label: 'نوع المواصلات' }
        // إزالة housingCode من الحقول المطلوبة
      ];

      const missingFields = requiredFields.filter(field => {
        const value = formData[field.field];
        if (field.field === 'isResident') {
          return value === undefined || value === null;
        }
        if (typeof value === 'string') {
          // نتحقق من أن القيمة ليست فارغة أو تحتوي على مسافات فقط
          return !value || value.trim() === '';
        }
        return !value && value !== 0 && value !== false;
      });

      if (missingFields.length > 0) {
        throw new Error(`يرجى ملء الحقول التالية: ${missingFields.map(f => f.label).join('، ')}`);
      }

      // National ID validation
      const nationalIdTrimmed = formData.nationalId.trim();
      if (nationalIdTrimmed.length !== 14) {
        throw new Error('الرقم القومي يجب أن يكون 14 رقم');
      }

      // التحقق من أن الرقم القومي يحتوي على أرقام فقط
      if (!/^\d{14}$/.test(nationalIdTrimmed)) {
        throw new Error('الرقم القومي يجب أن يحتوي على أرقام فقط');
      }

      // Date validation
      const dateFields = ['birthDate', 'hireDate', 'joinDate'];
      for (const field of dateFields) {
        if (!isValidDate(formData[field])) {
          throw new Error(`تاريخ غير صحيح: ${field === 'birthDate' ? 'تاريخ الميلاد' : field === 'hireDate' ? 'تاريخ التعيين' : 'تاريخ التواجد'}`);
        }
      }

      // تنظيف البيانات قبل الإرسال (trim للنصوص فقط)
      const cleanedData = {};
      Object.keys(formData).forEach(key => {
        const value = formData[key];
        if (typeof value === 'string') {
          cleanedData[key] = value.trim();
        } else {
          cleanedData[key] = value;
        }
      });

      const response = await fetch('/api/employee-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...cleanedData,
          hasSubsistenceAllowance: formData.hasSubsistenceAllowance || false,
          hasTransportationAllowance: formData.hasTransportationAllowance || false,
          vehicleCode: (formData.vehicleCode || '').trim(),
          address: (formData.address || '').trim(),
          area: (formData.area || '').trim(),
          mobile: (formData.mobile || '').trim(),
          email: (formData.email || '').trim(),
          education: (formData.education || '').trim(),
          university: (formData.university || '').trim(),
          specialization: (formData.specialization || '').trim(),
          graduationYear: (formData.graduationYear || '').trim(),
          emergencyNumber: (formData.emergencyNumber || '').trim(),
          kinshipDegree: (formData.kinshipDegree || '').trim()
        })
      });

      if (!response.ok) {
        const errorData = await response.json();

        throw new Error(errorData.message || errorData.error || 'حدث خطأ أثناء حفظ بيانات الموظف');
      }

      const result = await response.json();

      // تسجيل الإجراء
      actionLogger.logEmployeeAction('create', cleanedData);

      alert('تم تسجيل بيانات الموظف بنجاح');
      router.push('/employees');

    } catch (err) {

      alert(err.message);
    }
  };

  // Helper function to validate dates
  const isValidDate = (dateString) => {
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date);
  };

  return (
    <MainLayout>
      <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 overflow-x-hidden">
        {/* عنوان الصفحة */}
        <div className="bg-white dark:bg-[#1f2937] rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-800 dark:text-[#f9fafb] mb-2">
                {isArabic ? 'إضافة موظفين' : 'Add Employees'}
              </h1>
              <p className="text-gray-600 dark:text-[#d1d5db]">
                {isArabic ? 'إضافة موظف واحد أو عدة موظفين باستخدام ملف Excel' : 'Add single employee or multiple employees using Excel file'}
              </p>
            </div>
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
              <span className="text-2xl">👥</span>
            </div>
          </div>
        </div>

        {/* التبويبات */}
        <div className="bg-white dark:bg-[#1f2937] rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
          <div className="flex border-b border-gray-200 dark:border-gray-700">
            <button
              onClick={() => setActiveTab('single')}
              className={`flex items-center gap-2 px-6 py-4 font-medium transition-colors ${
                activeTab === 'single'
                  ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50 dark:bg-blue-900/20'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
              }`}
            >
              <FiUser className="text-lg" />
              {isArabic ? 'إضافة موظف واحد' : 'Add Single Employee'}
            </button>
            <button
              onClick={() => setActiveTab('bulk')}
              className={`flex items-center gap-2 px-6 py-4 font-medium transition-colors ${
                activeTab === 'bulk'
                  ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50 dark:bg-blue-900/20'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
              }`}
            >
              <FiUsers className="text-lg" />
              {isArabic ? 'إضافة مجمعة من Excel' : 'Bulk Add from Excel'}
            </button>
          </div>
        </div>

        {/* محتوى التبويبات */}
        {activeTab === 'single' ? (
          // نموذج إضافة موظف واحد
          <>
            {/* شريط الأيقونات */}
            <div className="bg-white dark:bg-[#1f2937] rounded-lg shadow-sm p-4 sm:p-6 mb-6 overflow-hidden">
              <h2 className="text-lg font-semibold text-gray-800 dark:text-[#f9fafb] mb-4 text-center">
                {isArabic ? 'رفع المستندات' : 'Upload Documents'}
              </h2>
          <div className="flex flex-wrap justify-center gap-2 sm:gap-4">
            {['صورة شخصية (PDF)', 'صورة شخصية (JPG)', 'رقم قومي', 'بيان حالة', 'استلام عمل', 'كارنية نقابة', 'بدل إعاشة', 'بدل انتقال'].map((title, index) => (
              <button
                key={`icon-${title}`}
                onClick={() => handleButtonClick(title)}
                disabled={loading}
                className={`
                  flex items-center justify-center px-3 sm:px-6 py-2 sm:py-3 rounded-xl
                  text-xs sm:text-sm font-medium whitespace-nowrap
                  ${loading ? 'opacity-50 cursor-not-allowed' : ''}
                  ${index === 0 ? 'bg-gradient-to-r from-blue-500 to-blue-600' :
                   index === 1 ? 'bg-gradient-to-r from-yellow-500 to-yellow-600' :
                   index === 2 ? 'bg-gradient-to-r from-green-500 to-green-600' :
                   index === 3 ? 'bg-gradient-to-r from-purple-500 to-purple-600' :
                   index === 4 ? 'bg-gradient-to-r from-blue-400 to-blue-500' :
                   index === 5 ? 'bg-gradient-to-r from-orange-500 to-orange-600' :
                   'bg-gradient-to-r from-teal-500 to-teal-600'}
                  text-white shadow-lg transform transition-all duration-200
                  hover:scale-105 hover:shadow-xl focus:outline-none focus:ring-2
                  focus:ring-offset-2 focus:ring-blue-500`}
              >
                <span className="flex items-center gap-2">
                  {loading && title === selectedUploadType ?
                    <span className="animate-spin">⌛</span> :
                    null}
                  {title}
                </span>
              </button>
            ))}
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6 sm:space-y-8">
          {/* البيانات الوظيفية */}
          <div className="bg-white dark:bg-[#1f2937] rounded-2xl p-4 sm:p-6 shadow-lg overflow-hidden">
            <h2 className="text-lg sm:text-xl font-bold mb-4 text-blue-600 dark:text-blue-400 flex items-center gap-2">
              <FiUser className="w-5 h-5 sm:w-6 sm:h-6" />
              {isArabic ? 'البيانات الوظيفية' : 'Job Information'}
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
              {formFields.slice(0, 3).map((section) => (
                section.map((field) => (
                  <div key={`field-${field.id}`} className={field.col === 2 ? 'md:col-span-2' : field.col === 3 ? 'lg:col-span-3' : 'col-span-1'}>
                    <label htmlFor={field.id} className="block mb-2 text-sm font-medium text-gray-700 dark:text-[#f9fafb]">
                      {field.label}
                    </label>
                    {field.type === 'select' ? (
                      <div className="relative">
                        <select
                          name={field.id}
                          id={field.id}
                          value={formData[field.id]}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border-2 border-gray-200 rounded-lg appearance-none focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200 dark:bg-[#374151] dark:border-[#374151] dark:text-[#f9fafb]"
                          required={field.required}
                        >
                          <option value="">اختر {field.label}</option>
                          {field.options && field.options.map((option) => (
                            <option key={`${field.id}-${option.value}`} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                        <div className="absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                          <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                          </svg>
                        </div>
                      </div>
                    ) : field.type === 'date' ? (
                      <DateInput
                        id={field.id}
                        name={field.id}
                        value={formData[field.id]}
                        onChange={handleInputChange}
                        required={field.required}
                        className="w-full"
                      />
                    ) : field.type === 'checkbox' ? (
                      <div className="flex items-center mt-2">
                        <input
                          type="checkbox"
                          id={field.id}
                          name={field.id}
                          checked={formData[field.id]}
                          onChange={handleInputChange}
                          className="w-5 h-5 text-blue-600 border-2 border-gray-300 rounded focus:ring-blue-500 dark:border-gray-600 transition-all duration-200"
                        />
                        <span className="mr-3 text-gray-700 dark:text-gray-300">{field.label}</span>
                      </div>
                    ) : (
                      <div className="relative">
                        <input
                          type={field.type}
                          id={field.id}
                          name={field.id}
                          value={formData[field.id]}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                          required={field.required}
                          placeholder={field.placeholder}
                        />
                      </div>
                    )}
                  </div>
                ))
              ))}
            </div>
          </div>

          {/* البيانات الشخصية */}
          <div className="bg-white dark:bg-[#1f2937] rounded-2xl p-4 sm:p-6 shadow-lg overflow-hidden">
            <h2 className="text-lg sm:text-xl font-bold mb-4 text-green-600 dark:text-green-400 flex items-center gap-2">
              <FiUser className="w-5 h-5 sm:w-6 sm:h-6" />
              {isArabic ? 'البيانات الشخصية' : 'Personal Information'}
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
              {formFields.slice(3, 8).map((section) => (
                section.map((field) => (
                  <div key={`field-${field.id}`} className={field.col === 2 ? 'md:col-span-2' : field.col === 3 ? 'lg:col-span-3' : 'col-span-1'}>
                    <label htmlFor={field.id} className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                      {field.label}
                    </label>
                    {field.type === 'select' ? (
                      <div className="relative">
                        <select
                          name={field.id}
                          id={field.id}
                          value={formData[field.id]}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border-2 border-gray-200 rounded-lg appearance-none focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                          required={field.required}
                        >
                          <option value="">اختر {field.label}</option>
                          {field.options && field.options.map((option) => (
                            <option key={`${field.id}-${option.value}`} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                        <div className="absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                          <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                          </svg>
                        </div>
                      </div>
                    ) : field.type === 'date' ? (
                      <DateInput
                        id={field.id}
                        name={field.id}
                        value={formData[field.id]}
                        onChange={handleInputChange}
                        required={field.required}
                        className="w-full"
                      />
                    ) : field.type === 'checkbox' ? (
                      <div className="flex items-center mt-2">
                        <input
                          type="checkbox"
                          id={field.id}
                          name={field.id}
                          checked={formData[field.id]}
                          onChange={handleInputChange}
                          className="w-5 h-5 text-blue-600 border-2 border-gray-300 rounded focus:ring-blue-500 dark:border-gray-600 transition-all duration-200"
                        />
                        <span className="mr-3 text-gray-700 dark:text-gray-300">{field.label}</span>
                      </div>
                    ) : (
                      <div className="relative">
                        <input
                          type={field.type}
                          id={field.id}
                          name={field.id}
                          value={formData[field.id]}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                          required={field.required}
                          placeholder={field.placeholder}
                        />
                      </div>
                    )}
                  </div>
                ))
              ))}
            </div>
          </div>

          {/* بيانات إضافية */}
          <div className="bg-white dark:bg-[#1f2937] rounded-2xl p-4 sm:p-6 shadow-lg overflow-hidden">
            <h2 className="text-lg sm:text-xl font-bold mb-4 text-yellow-600 dark:text-yellow-400 flex items-center gap-2">
              <FiHome className="w-5 h-5 sm:w-6 sm:h-6" />
              {isArabic ? 'بيانات إضافية' : 'Additional Information'}
            </h2>

            {/* ملاحظة توضيحية */}
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6">
              <div className="flex items-start gap-3">
                <span className="text-2xl">ℹ️</span>
                <div>
                  <h3 className="font-semibold text-amber-800 mb-2">ملاحظة مهمة</h3>
                  <ul className="text-amber-700 text-sm space-y-1">
                    <li>• تم فصل بيانات الشقق والسيارات عن نموذج الموظفين</li>
                    <li>• سيتم إدارة المستفيدين من الشقق والسيارات من نماذج منفصلة</li>
                    <li>• المغترب ≠ المقيم في شقة الشركة (مفهومان منفصلان)</li>
                    <li>• هذا يضمن مرونة أكبر في إدارة الاستفادة</li>
                  </ul>
                </div>
              </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
              {formFields.slice(8, 11).map((section) => (
                section.map((field) => (
                  <div key={`field-${field.id}`} className={field.col === 2 ? 'md:col-span-2' : field.col === 3 ? 'lg:col-span-3' : 'col-span-1'}>
                    <label htmlFor={field.id} className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                      {field.label}
                    </label>
                    {field.type === 'select' ? (
                      <div className="relative">
                        <select
                          name={field.id}
                          id={field.id}
                          value={formData[field.id]}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border-2 border-gray-200 rounded-lg appearance-none focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                          required={field.required}
                        >
                          <option value="">اختر {field.label}</option>
                          {field.options && field.options.map((option) => (
                            <option key={`${field.id}-${option.value}`} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                        <div className="absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                          <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                          </svg>
                        </div>
                      </div>
                    ) : field.type === 'date' ? (
                      <DateInput
                        id={field.id}
                        name={field.id}
                        value={formData[field.id]}
                        onChange={handleInputChange}
                        required={field.required}
                        className="w-full"
                      />
                    ) : field.type === 'checkbox' ? (
                      <div className="flex items-center mt-2">
                        <input
                          type="checkbox"
                          id={field.id}
                          name={field.id}
                          checked={formData[field.id]}
                          onChange={handleInputChange}
                          className="w-5 h-5 text-blue-600 border-2 border-gray-300 rounded focus:ring-blue-500 dark:border-gray-600 transition-all duration-200"
                        />
                        <span className="mr-3 text-gray-700 dark:text-gray-300">{field.label}</span>
                      </div>
                    ) : (
                      <div className="relative">
                        <input
                          type={field.type}
                          id={field.id}
                          name={field.id}
                          value={formData[field.id]}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                          required={field.required}
                          placeholder={field.placeholder}
                        />
                      </div>
                    )}
                  </div>
                ))
              ))}
            </div>
          </div>

          {/* البيانات الدراسية */}
          <div className="bg-white dark:bg-[#1f2937] rounded-2xl p-4 sm:p-6 shadow-lg overflow-hidden">
            <h2 className="text-lg sm:text-xl font-bold mb-4 text-purple-600 dark:text-purple-400 flex items-center gap-2">
              <FiMail className="w-5 h-5 sm:w-6 sm:h-6" />
              {isArabic ? 'البيانات الدراسية' : 'Educational Information'}
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
              {formFields.slice(11, 13).map((section) => (
                section.map((field) => (
                  <div key={`field-${field.id}`} className={field.col === 2 ? 'md:col-span-2' : field.col === 3 ? 'lg:col-span-3' : 'col-span-1'}>
                    <label htmlFor={field.id} className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                      {field.label}
                    </label>
                    {field.type === 'select' ? (
                      <div className="relative">
                        <select
                          name={field.id}
                          id={field.id}
                          value={formData[field.id]}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border-2 border-gray-200 rounded-lg appearance-none focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                          required={field.required}
                        >
                          <option value="">اختر {field.label}</option>
                          {field.options && field.options.map((option) => (
                            <option key={`${field.id}-${option.value}`} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                        <div className="absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                          <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                          </svg>
                        </div>
                      </div>
                    ) : field.type === 'date' ? (
                      <DateInput
                        id={field.id}
                        name={field.id}
                        value={formData[field.id]}
                        onChange={handleInputChange}
                        required={field.required}
                        className="w-full"
                      />
                    ) : field.type === 'checkbox' ? (
                      <div className="flex items-center mt-2">
                        <input
                          type="checkbox"
                          id={field.id}
                          name={field.id}
                          checked={formData[field.id]}
                          onChange={handleInputChange}
                          className="w-5 h-5 text-blue-600 border-2 border-gray-300 rounded focus:ring-blue-500 dark:border-gray-600 transition-all duration-200"
                        />
                        <span className="mr-3 text-gray-700 dark:text-gray-300">{field.label}</span>
                      </div>
                    ) : (
                      <div className="relative">
                        <input
                          type={field.type}
                          id={field.id}
                          name={field.id}
                          value={formData[field.id]}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                          required={field.required}
                          placeholder={field.placeholder}
                        />
                      </div>
                    )}
                  </div>
                ))
              ))}
            </div>
          </div>

          {/* بيانات التأمين */}
          <div className="bg-white dark:bg-[#1f2937] rounded-2xl p-4 sm:p-6 shadow-lg overflow-hidden">
            <h2 className="text-lg sm:text-xl font-bold mb-4 text-orange-600 dark:text-orange-400 flex items-center gap-2">
              <FiFileText className="w-5 h-5 sm:w-6 sm:h-6" />
              {isArabic ? 'بيانات التأمين' : 'Insurance Information'}
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
              {formFields.slice(13).map((section) => (
                section.map((field) => (
                  <div key={`field-${field.id}`} className={field.col === 2 ? 'md:col-span-2' : field.col === 3 ? 'lg:col-span-3' : 'col-span-1'}>
                    <label htmlFor={field.id} className="block mb-2 text-sm font-medium text-gray-700 dark:text-gray-300">
                      {field.label}
                    </label>
                    {field.type === 'select' ? (
                      <div className="relative">
                        <select
                          name={field.id}
                          id={field.id}
                          value={formData[field.id]}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border-2 border-gray-200 rounded-lg appearance-none focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                          required={field.required}
                        >
                          <option value="">اختر {field.label}</option>
                          {field.options && field.options.map((option) => (
                            <option key={`${field.id}-${option.value}`} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                        <div className="absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                          <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                          </svg>
                        </div>
                      </div>
                    ) : field.type === 'date' ? (
                      <DateInput
                        id={field.id}
                        name={field.id}
                        value={formData[field.id]}
                        onChange={handleInputChange}
                        required={field.required}
                        className="w-full"
                      />
                    ) : (
                      <div className="relative">
                        <input
                          type={field.type}
                          id={field.id}
                          name={field.id}
                          value={formData[field.id]}
                          onChange={handleInputChange}
                          className="w-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:border-blue-500 focus:ring focus:ring-blue-200 focus:ring-opacity-50 transition-all duration-200 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                          required={field.required}
                          placeholder={field.placeholder}
                        />
                      </div>
                    )}
                  </div>
                ))
              ))}
            </div>
          </div>

          {/* أزرار التحكم */}
          <div className="flex flex-col sm:flex-row justify-center gap-3 sm:gap-4 mt-6 sm:mt-8">
            <button
              type="submit"
              className="w-full sm:w-auto px-6 sm:px-8 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl shadow-lg transform transition-all duration-200 hover:scale-105 hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              <span className="flex items-center justify-center gap-2">
                <FiSave className="w-5 h-5" />
                {isArabic ? 'تسجيل' : 'Save'}
              </span>
            </button>
            <button
              type="button"
              onClick={() => router.back()}
              className="w-full sm:w-auto px-6 sm:px-8 py-3 bg-gradient-to-r from-gray-400 to-gray-500 text-white rounded-xl shadow-lg transform transition-all duration-200 hover:scale-105 hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              <span className="flex items-center justify-center gap-2">
                <FiArrowLeft className="w-5 h-5" />
                {isArabic ? 'عودة' : 'Back'}
              </span>
            </button>
          </div>
        </form>
          </>
        ) : (
          // واجهة الإضافة المجمعة
          <div className="space-y-6">
            {/* ملاحظة مهمة */}
            <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-6 mb-6">
              <div className="flex items-start gap-4">
                <div className="bg-amber-100 dark:bg-amber-800 p-3 rounded-lg">
                  <FiAlertCircle className="text-amber-600 dark:text-amber-400 text-xl" />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-amber-800 dark:text-amber-200 mb-2">
                    {isArabic ? 'تحديثات مهمة في النموذج' : 'Important Template Updates'}
                  </h3>
                  <ul className="text-amber-700 dark:text-amber-300 space-y-1 text-sm">
                    <li>• تم فصل بيانات الشقق والسيارات عن جدول الموظفين</li>
                    <li>• المغترب ≠ المقيم في شقة الشركة (مفهومان منفصلان)</li>
                    <li>• أضيفت حقول التأمين الاجتماعي والطبي</li>
                    <li>• الحقول الحمراء مطلوبة، الحقول الزرقاء اختيارية</li>
                    <li>• احذف الصف المثال قبل إدراج البيانات الحقيقية</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* تحميل النماذج */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* ملف الإضافة */}
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6">
                <div className="flex items-start gap-4">
                  <div className="bg-green-100 dark:bg-green-800 p-3 rounded-lg">
                    <FiDownload className="text-green-600 dark:text-green-400 text-xl" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-2">
                      {isArabic ? 'ملف الإضافة (موظفين جدد)' : 'Add Template (New Employees)'}
                    </h3>
                    <p className="text-green-700 dark:text-green-300 mb-4 text-sm">
                      {isArabic ? 'لإضافة موظفين جدد. يمكن كتابة "تعيين جديد" في كود الموظف. المدير المباشر وتاريخ التعيين والخدمة العسكرية اختيارية.' : 'For adding new employees. You can write "تعيين جديد" in employee code. Direct manager, hire date, and military service are optional.'}
                    </p>
                    <button
                      onClick={downloadTemplate}
                      disabled={isDownloading}
                      className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:bg-green-400 transition-colors flex items-center gap-2"
                    >
                      <FiDownload />
                      {isDownloading ? (isArabic ? 'جاري التحميل...' : 'Downloading...') : (isArabic ? 'تحميل ملف الإضافة' : 'Download Add Template')}
                    </button>
                  </div>
                </div>
              </div>

              {/* ملف التحديث */}
              <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
                <div className="flex items-start gap-4">
                  <div className="bg-blue-100 dark:bg-blue-800 p-3 rounded-lg">
                    <FiDownload className="text-blue-600 dark:text-blue-400 text-xl" />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-2">
                      {isArabic ? 'ملف التحديث (البيانات المسجلة)' : 'Update Template (Registered Data)'}
                    </h3>
                    <p className="text-blue-700 dark:text-blue-300 mb-4 text-sm">
                      {isArabic ? 'يحتوي على البيانات المسجلة حالياً. يمكن تعديل أي حقل أو إضافة بيانات غير مسجلة. التحديث الذكي يحافظ على البيانات الموجودة.' : 'Contains currently registered data. You can edit any field or add unregistered data. Smart update preserves existing data.'}
                    </p>
                    <button
                      onClick={downloadUpdateTemplate}
                      disabled={isDownloadingUpdate}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:bg-blue-400 transition-colors flex items-center gap-2"
                    >
                      <FiDownload />
                      {isDownloadingUpdate ? (isArabic ? 'جاري التحميل...' : 'Downloading...') : (isArabic ? 'تحميل ملف التحديث' : 'Download Update Template')}
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* رفع الملف */}
            <div className="bg-white dark:bg-[#1f2937] rounded-lg shadow-sm p-6">
              <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center">
                <div className="mb-4">
                  <FiUpload className="text-4xl text-gray-400 mx-auto mb-2" />
                  <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">
                    {isArabic ? 'رفع ملف Excel' : 'Upload Excel File'}
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-4">
                    {isArabic ? 'اختر ملف Excel يحتوي على بيانات الموظفين' : 'Choose Excel file containing employee data'}
                  </p>
                </div>

                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".xlsx,.xls"
                  onChange={handleFileUpload}
                  className="hidden"
                />

                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors"
                >
                  {isArabic ? 'اختيار ملف' : 'Choose File'}
                </button>

                {uploadedFile && (
                  <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <FiFileText className="text-gray-600 dark:text-gray-400" />
                        <span className="text-sm text-gray-700 dark:text-gray-300">{uploadedFile.name}</span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          ({(uploadedFile.size / 1024 / 1024).toFixed(2)} MB)
                        </span>
                      </div>
                      <button
                        onClick={() => setUploadedFile(null)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <FiX />
                      </button>
                    </div>

                    <button
                      onClick={processExcelFile}
                      disabled={uploadStatus === 'uploading'}
                      className="mt-3 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {uploadStatus === 'uploading' ? (isArabic ? 'جاري المعالجة...' : 'Processing...') : (isArabic ? 'معالجة الملف' : 'Process File')}
                    </button>
                  </div>
                )}
              </div>

              {/* نتائج الرفع */}
              {uploadResults && (
                <div className={`print-area mt-6 p-6 rounded-lg border ${
                  uploadStatus === 'success'
                    ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                    : 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
                }`}>
                  {/* عنوان للطباعة */}
                  <div className="print-title hidden print:block">
                    تقرير رفع بيانات الموظفين - التحديث الذكي - {new Date().toLocaleDateString('ar-EG')}
                  </div>

                  {/* معلومات التحديث الذكي */}
                  {uploadResults.info?.smartUpdate && (
                    <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg print-hide">
                      <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-3 flex items-center gap-2">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        قواعد التحديث الذكي
                      </h4>
                      <ul className="space-y-2 text-sm text-blue-700 dark:text-blue-300">
                        {uploadResults.info.rules.map((rule, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <span className="text-blue-600 dark:text-blue-400 mt-0.5">•</span>
                            <span>{rule}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  <div className="flex items-start gap-3">
                    {uploadStatus === 'success' ? (
                      <FiCheck className="text-green-600 dark:text-green-400 text-xl mt-1 print-hide" />
                    ) : (
                      <FiAlertCircle className="text-red-600 dark:text-red-400 text-xl mt-1 print-hide" />
                    )}
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className={`text-lg font-semibold ${
                          uploadStatus === 'success' ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200'
                        }`}>
                          {uploadStatus === 'success' ? (isArabic ? 'تم بنجاح!' : 'Success!') : (isArabic ? 'حدث خطأ' : 'Error Occurred')}
                        </h3>

                        {uploadStatus === 'success' && uploadResults.summary && (
                          <button
                            onClick={() => window.print()}
                            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm flex items-center gap-2 transition-colors print:hidden"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                            </svg>
                            {isArabic ? 'طباعة التقرير' : 'Print Report'}
                          </button>
                        )}
                      </div>

                      {uploadResults.summary && (
                        <div className="print-summary grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                          <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg text-center">
                            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{uploadResults.summary.total}</div>
                            <div className="text-sm text-blue-700 dark:text-blue-300">{isArabic ? 'إجمالي الصفوف' : 'Total Rows'}</div>
                          </div>
                          <div className="bg-green-100 dark:bg-green-900/30 p-3 rounded-lg text-center">
                            <div className="text-2xl font-bold text-green-600 dark:text-green-400">{uploadResults.summary.added || 0}</div>
                            <div className="text-sm text-green-700 dark:text-green-300">{isArabic ? 'تم إضافتها' : 'Added'}</div>
                          </div>
                          <div className="bg-yellow-100 dark:bg-yellow-900/30 p-3 rounded-lg text-center">
                            <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{uploadResults.summary.updated || 0}</div>
                            <div className="text-sm text-yellow-700 dark:text-yellow-300">{isArabic ? 'تم تحديثها' : 'Updated'}</div>
                          </div>
                          <div className="bg-red-100 dark:bg-red-900/30 p-3 rounded-lg text-center">
                            <div className="text-2xl font-bold text-red-600 dark:text-red-400">{uploadResults.summary.failed || 0}</div>
                            <div className="text-sm text-red-700 dark:text-red-300">{isArabic ? 'فشلت' : 'Failed'}</div>
                          </div>
                        </div>
                      )}

                      {/* جدول الموظفين المضافين */}
                      {uploadResults.details?.addedEmployees && uploadResults.details.addedEmployees.length > 0 && (
                        <div className="mb-6">
                          <h4 className="font-semibold text-green-800 dark:text-green-200 mb-3 flex items-center gap-2">
                            <FiUser className="text-lg" />
                            {isArabic ? `الموظفين المضافين (${uploadResults.details.addedEmployees.length})` : `Added Employees (${uploadResults.details.addedEmployees.length})`}
                          </h4>
                          <div className="bg-green-50 dark:bg-green-900/10 rounded-lg p-4 max-h-96 overflow-auto">
                            <div className="overflow-x-auto">
                              <table className="print-table min-w-full bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
                                <thead className="bg-green-100 dark:bg-green-900/30">
                                  <tr>
                                    <th className="px-4 py-3 text-right text-xs font-medium text-green-800 dark:text-green-200 uppercase tracking-wider">الصف</th>
                                    <th className="px-4 py-3 text-right text-xs font-medium text-green-800 dark:text-green-200 uppercase tracking-wider">كود الموظف</th>
                                    <th className="px-4 py-3 text-right text-xs font-medium text-green-800 dark:text-green-200 uppercase tracking-wider">الاسم</th>
                                    <th className="px-4 py-3 text-right text-xs font-medium text-green-800 dark:text-green-200 uppercase tracking-wider">القسم</th>
                                    <th className="px-4 py-3 text-right text-xs font-medium text-green-800 dark:text-green-200 uppercase tracking-wider">المسمى الوظيفي</th>
                                    <th className="px-4 py-3 text-right text-xs font-medium text-green-800 dark:text-green-200 uppercase tracking-wider">الرقم القومي</th>
                                    <th className="px-4 py-3 text-right text-xs font-medium text-green-800 dark:text-green-200 uppercase tracking-wider">الحالة</th>
                                  </tr>
                                </thead>
                                <tbody className="divide-y divide-green-200 dark:divide-green-800">
                                  {uploadResults.details.addedEmployees.map((emp, index) => (
                                    <tr key={index} className="hover:bg-green-50 dark:hover:bg-green-900/20">
                                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                        <span className="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-2 py-1 rounded-full text-xs font-medium">
                                          {emp.rowNumber}
                                        </span>
                                      </td>
                                      <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">{emp.employeeCode}</td>
                                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{emp.fullName}</td>
                                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{emp.department}</td>
                                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{emp.jobTitle}</td>
                                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 font-mono">{emp.nationalId}</td>
                                      <td className="px-4 py-3 whitespace-nowrap text-sm">
                                        <span className="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-2 py-1 rounded-full text-xs font-medium">
                                          ✅ {emp.status}
                                        </span>
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* جدول الموظفين المحدثين */}
                      {uploadResults.details?.updatedEmployees && uploadResults.details.updatedEmployees.length > 0 && (
                        <div className="mb-6">
                          <h4 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-3 flex items-center gap-2">
                            <FiUser className="text-lg" />
                            {isArabic ? `الموظفين المحدثين (${uploadResults.details.updatedEmployees.length})` : `Updated Employees (${uploadResults.details.updatedEmployees.length})`}
                          </h4>
                          <div className="bg-yellow-50 dark:bg-yellow-900/10 rounded-lg p-4 max-h-96 overflow-auto">
                            <div className="overflow-x-auto">
                              <table className="print-table min-w-full bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
                                <thead className="bg-yellow-100 dark:bg-yellow-900/30">
                                  <tr>
                                    <th className="px-4 py-3 text-right text-xs font-medium text-yellow-800 dark:text-yellow-200 uppercase tracking-wider">الصف</th>
                                    <th className="px-4 py-3 text-right text-xs font-medium text-yellow-800 dark:text-yellow-200 uppercase tracking-wider">كود الموظف</th>
                                    <th className="px-4 py-3 text-right text-xs font-medium text-yellow-800 dark:text-yellow-200 uppercase tracking-wider">الاسم</th>
                                    <th className="px-4 py-3 text-right text-xs font-medium text-yellow-800 dark:text-yellow-200 uppercase tracking-wider">القسم</th>
                                    <th className="px-4 py-3 text-right text-xs font-medium text-yellow-800 dark:text-yellow-200 uppercase tracking-wider">المسمى الوظيفي</th>
                                    <th className="px-4 py-3 text-right text-xs font-medium text-yellow-800 dark:text-yellow-200 uppercase tracking-wider">الرقم القومي</th>
                                    <th className="px-4 py-3 text-right text-xs font-medium text-yellow-800 dark:text-yellow-200 uppercase tracking-wider">الحقول المحدثة</th>
                                    <th className="px-4 py-3 text-right text-xs font-medium text-yellow-800 dark:text-yellow-200 uppercase tracking-wider">الحالة</th>
                                  </tr>
                                </thead>
                                <tbody className="divide-y divide-yellow-200 dark:divide-yellow-800">
                                  {uploadResults.details.updatedEmployees.map((emp, index) => (
                                    <tr key={index} className="hover:bg-yellow-50 dark:hover:bg-yellow-900/20">
                                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                        <span className="bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded-full text-xs font-medium">
                                          {emp.rowNumber}
                                        </span>
                                      </td>
                                      <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">{emp.employeeCode}</td>
                                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{emp.fullName}</td>
                                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{emp.department}</td>
                                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{emp.jobTitle}</td>
                                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 font-mono">{emp.nationalId}</td>
                                      <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                        <div className="max-w-xs">
                                          <div className="text-xs font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                                            {emp.updatedFields} حقل محدث
                                          </div>
                                          {emp.updatedFieldsList && emp.updatedFieldsList.length > 0 && (
                                            <div className="text-xs text-gray-600 dark:text-gray-400">
                                              {emp.updatedFieldsList.slice(0, 3).join('، ')}
                                              {emp.updatedFieldsList.length > 3 && '...'}
                                            </div>
                                          )}
                                        </div>
                                      </td>
                                      <td className="px-4 py-3 whitespace-nowrap text-sm">
                                        <span className="bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded-full text-xs font-medium">
                                          🔄 {emp.status}
                                        </span>
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* جدول الموظفين المرفوضين */}
                      {uploadResults.details?.failedEmployees && uploadResults.details.failedEmployees.length > 0 && (
                        <div className="mb-4">
                          <h4 className="font-semibold text-red-800 dark:text-red-200 mb-3 flex items-center gap-2">
                            <FiAlertCircle className="text-lg" />
                            {isArabic ? `الموظفين المرفوضين (${uploadResults.details.failedEmployees.length})` : `Failed Employees (${uploadResults.details.failedEmployees.length})`}
                          </h4>
                          <div className="bg-red-50 dark:bg-red-900/10 rounded-lg p-4 max-h-96 overflow-auto">
                            <div className="overflow-x-auto">
                              <table className="print-table min-w-full bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
                                <thead className="bg-red-100 dark:bg-red-900/30">
                                  <tr>
                                    <th className="px-4 py-3 text-right text-xs font-medium text-red-800 dark:text-red-200 uppercase tracking-wider">الصف</th>
                                    <th className="px-4 py-3 text-right text-xs font-medium text-red-800 dark:text-red-200 uppercase tracking-wider">كود الموظف</th>
                                    <th className="px-4 py-3 text-right text-xs font-medium text-red-800 dark:text-red-200 uppercase tracking-wider">الاسم</th>
                                    <th className="px-4 py-3 text-right text-xs font-medium text-red-800 dark:text-red-200 uppercase tracking-wider">القسم</th>
                                    <th className="px-4 py-3 text-right text-xs font-medium text-red-800 dark:text-red-200 uppercase tracking-wider">المسمى الوظيفي</th>
                                    <th className="px-4 py-3 text-right text-xs font-medium text-red-800 dark:text-red-200 uppercase tracking-wider">الرقم القومي</th>
                                    <th className="px-4 py-3 text-right text-xs font-medium text-red-800 dark:text-red-200 uppercase tracking-wider">سبب الرفض</th>
                                    <th className="px-4 py-3 text-right text-xs font-medium text-red-800 dark:text-red-200 uppercase tracking-wider">الحالة</th>
                                  </tr>
                                </thead>
                                <tbody className="divide-y divide-red-200 dark:divide-red-800">
                                  {uploadResults.details.failedEmployees.map((emp, index) => (
                                    <tr key={index} className="hover:bg-red-50 dark:hover:bg-red-900/20">
                                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                        <span className="bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 px-2 py-1 rounded-full text-xs font-medium">
                                          {emp.rowNumber}
                                        </span>
                                      </td>
                                      <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">{emp.employeeCode}</td>
                                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{emp.fullName}</td>
                                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{emp.department}</td>
                                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{emp.jobTitle}</td>
                                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 font-mono">{emp.nationalId}</td>
                                      <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                        <div className="max-w-xs">
                                          <div className="text-xs text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 p-2 rounded">
                                            {emp.error}
                                          </div>
                                        </div>
                                      </td>
                                      <td className="px-4 py-3 whitespace-nowrap text-sm">
                                        <span className="bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 px-2 py-1 rounded-full text-xs font-medium">
                                          ❌ {emp.status}
                                        </span>
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        </div>
                      )}

                      {uploadResults.errors && uploadResults.errors.length > 0 && uploadResults.errors.length > (uploadResults.details?.failedEmployees?.length || 0) && (
                        <div className="mt-4">
                          <h4 className="font-medium text-red-800 dark:text-red-200 mb-2">{isArabic ? 'أخطاء إضافية:' : 'Additional Errors:'}</h4>
                          <ul className="text-sm text-red-700 dark:text-red-300 space-y-1 bg-red-50 dark:bg-red-900/10 p-3 rounded">
                            {uploadResults.errors.slice(uploadResults.details?.failedEmployees?.length || 0, uploadResults.details?.failedEmployees?.length + 5 || 5).map((error, index) => (
                              <li key={index}>• {error}</li>
                            ))}
                            {uploadResults.errors.length > (uploadResults.details?.failedEmployees?.length || 0) + 5 && (
                              <li>... {isArabic ? `و ${uploadResults.errors.length - (uploadResults.details?.failedEmployees?.length || 0) - 5} أخطاء أخرى` : `and ${uploadResults.errors.length - (uploadResults.details?.failedEmployees?.length || 0) - 5} more errors`}</li>
                            )}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default MainComponent;
