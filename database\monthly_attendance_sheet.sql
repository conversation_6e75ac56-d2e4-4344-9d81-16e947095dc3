-- إنشاء جدول الحضور الشهري
-- Monthly Attendance Sheet Table Creation

-- حذف الجدول إذا كان موجوداً
IF EXISTS (SELECT * FROM sysobjects WHERE name='MonthlyAttendanceSheet' AND xtype='U')
BEGIN
    DROP TABLE MonthlyAttendanceSheet
END
GO

-- إن<PERSON>اء جدول الحضور الشهري
CREATE TABLE MonthlyAttendanceSheet (
    ID INT IDENTITY(1,1) PRIMARY KEY,
    EmployeeCode NVARCHAR(20) NOT NULL,
    AttendanceDate DATE NOT NULL,
    AttendanceStatus NVARCHAR(50) NOT NULL,
    AttendanceCode NVARCHAR(10) NOT NULL,
    LeaveType NVARCHAR(100),
    Notes NVARCHAR(MAX),
    CreatedAt DATETIME DEFAULT GETDATE(),
    UpdatedAt DATETIME DEFAULT GETDATE(),
    CreatedBy NVARCHAR(100),
    UpdatedBy NVARCHAR(100)
)
GO

-- إنشاء الفهارس
CREATE INDEX IX_MonthlyAttendanceSheet_Employee ON MonthlyAttendanceSheet(EmployeeCode)
GO

CREATE INDEX IX_MonthlyAttendanceSheet_Date ON MonthlyAttendanceSheet(AttendanceDate)
GO

CREATE INDEX IX_MonthlyAttendanceSheet_Status ON MonthlyAttendanceSheet(AttendanceStatus)
GO

CREATE INDEX IX_MonthlyAttendanceSheet_Code ON MonthlyAttendanceSheet(AttendanceCode)
GO

CREATE UNIQUE INDEX UX_MonthlyAttendanceSheet_Employee_Date ON MonthlyAttendanceSheet(EmployeeCode, AttendanceDate)
GO

-- إدراج بيانات تجريبية
INSERT INTO MonthlyAttendanceSheet (EmployeeCode, AttendanceDate, AttendanceStatus, AttendanceCode, LeaveType, Notes)
VALUES 
-- موظف 1 - يناير 2024
('EMP001', '2024-01-01', 'حضور', 'W', NULL, 'يوم عمل عادي'),
('EMP001', '2024-01-02', 'حضور', 'W', NULL, 'يوم عمل عادي'),
('EMP001', '2024-01-03', 'حضور', 'W', NULL, 'يوم عمل عادي'),
('EMP001', '2024-01-04', 'حضور', 'W', NULL, 'يوم عمل عادي'),
('EMP001', '2024-01-05', 'راحة', 'R', NULL, 'يوم جمعة'),
('EMP001', '2024-01-06', 'راحة', 'R', NULL, 'يوم سبت'),
('EMP001', '2024-01-07', 'حضور', 'W', NULL, 'يوم عمل عادي'),
('EMP001', '2024-01-08', 'حضور', 'W', NULL, 'يوم عمل عادي'),
('EMP001', '2024-01-09', 'حضور', 'W', NULL, 'يوم عمل عادي'),
('EMP001', '2024-01-10', 'إجازة مرضية', 'S', 'إجازة مرضية', 'إجازة مرضية بشهادة طبية'),
('EMP001', '2024-01-11', 'حضور', 'W', NULL, 'يوم عمل عادي'),
('EMP001', '2024-01-12', 'راحة', 'R', NULL, 'يوم جمعة'),
('EMP001', '2024-01-13', 'راحة', 'R', NULL, 'يوم سبت'),
('EMP001', '2024-01-14', 'حضور', 'W', NULL, 'يوم عمل عادي'),
('EMP001', '2024-01-15', 'مأمورية', 'M', 'مأمورية', 'مأمورية رسمية'),

-- موظف 2 - يناير 2024
('EMP002', '2024-01-01', 'حضور', 'W', NULL, 'يوم عمل عادي'),
('EMP002', '2024-01-02', 'حضور', 'W', NULL, 'يوم عمل عادي'),
('EMP002', '2024-01-03', 'غياب', 'Ab', NULL, 'غياب بدون عذر'),
('EMP002', '2024-01-04', 'حضور', 'W', NULL, 'يوم عمل عادي'),
('EMP002', '2024-01-05', 'راحة', 'R', NULL, 'يوم جمعة'),
('EMP002', '2024-01-06', 'راحة', 'R', NULL, 'يوم سبت'),
('EMP002', '2024-01-07', 'حضور', 'W', NULL, 'يوم عمل عادي'),
('EMP002', '2024-01-08', 'إجازة', 'AL', 'إجازة اعتيادية', 'إجازة سنوية'),
('EMP002', '2024-01-09', 'إجازة', 'AL', 'إجازة اعتيادية', 'إجازة سنوية'),
('EMP002', '2024-01-10', 'حضور', 'W', NULL, 'يوم عمل عادي'),
('EMP002', '2024-01-11', 'حضور', 'W', NULL, 'يوم عمل عادي'),
('EMP002', '2024-01-12', 'راحة', 'R', NULL, 'يوم جمعة'),
('EMP002', '2024-01-13', 'راحة', 'R', NULL, 'يوم سبت'),
('EMP002', '2024-01-14', 'حضور', 'W', NULL, 'يوم عمل عادي'),
('EMP002', '2024-01-15', 'حضور', 'W', NULL, 'يوم عمل عادي'),

-- موظف 3 - يناير 2024
('EMP003', '2024-01-01', 'عطلة رسمية', 'NH', 'عطلة رسمية', 'رأس السنة الميلادية'),
('EMP003', '2024-01-02', 'حضور', 'W', NULL, 'يوم عمل عادي'),
('EMP003', '2024-01-03', 'حضور', 'W', NULL, 'يوم عمل عادي'),
('EMP003', '2024-01-04', 'حضور', 'W', NULL, 'يوم عمل عادي'),
('EMP003', '2024-01-05', 'راحة', 'R', NULL, 'يوم جمعة'),
('EMP003', '2024-01-06', 'راحة', 'R', NULL, 'يوم سبت'),
('EMP003', '2024-01-07', 'إجازة', 'CL', 'إجازة عارضة', 'إجازة عارضة'),
('EMP003', '2024-01-08', 'حضور', 'W', NULL, 'يوم عمل عادي'),
('EMP003', '2024-01-09', 'حضور', 'W', NULL, 'يوم عمل عادي'),
('EMP003', '2024-01-10', 'حضور', 'W', NULL, 'يوم عمل عادي'),
('EMP003', '2024-01-11', 'حضور', 'W', NULL, 'يوم عمل عادي'),
('EMP003', '2024-01-12', 'راحة', 'R', NULL, 'يوم جمعة'),
('EMP003', '2024-01-13', 'راحة', 'R', NULL, 'يوم سبت'),
('EMP003', '2024-01-14', 'حضور', 'W', NULL, 'يوم عمل عادي'),
('EMP003', '2024-01-15', 'حضور', 'W', NULL, 'يوم عمل عادي')
GO

-- إنشاء view لعرض الإحصائيات الشهرية
CREATE VIEW MonthlyAttendanceStats AS
SELECT 
    YEAR(AttendanceDate) as Year,
    MONTH(AttendanceDate) as Month,
    EmployeeCode,
    COUNT(*) as TotalDays,
    SUM(CASE WHEN AttendanceCode = 'W' THEN 1 ELSE 0 END) as WorkDays,
    SUM(CASE WHEN AttendanceCode = 'Ab' THEN 1 ELSE 0 END) as AbsentDays,
    SUM(CASE WHEN AttendanceCode = 'S' THEN 1 ELSE 0 END) as SickDays,
    SUM(CASE WHEN AttendanceCode = 'R' THEN 1 ELSE 0 END) as RestDays,
    SUM(CASE WHEN AttendanceCode = 'NH' THEN 1 ELSE 0 END) as HolidayDays,
    SUM(CASE WHEN AttendanceCode = 'M' THEN 1 ELSE 0 END) as MissionDays,
    SUM(CASE WHEN AttendanceCode IN ('AL', 'CL', 'UL', 'ML', 'CR') THEN 1 ELSE 0 END) as LeaveDays,
    SUM(CASE WHEN AttendanceCode = 'AL' THEN 1 ELSE 0 END) as AnnualLeaveDays,
    SUM(CASE WHEN AttendanceCode = 'CL' THEN 1 ELSE 0 END) as CasualLeaveDays,
    SUM(CASE WHEN AttendanceCode = 'UL' THEN 1 ELSE 0 END) as UnpaidLeaveDays,
    SUM(CASE WHEN AttendanceCode = 'ML' THEN 1 ELSE 0 END) as MaternityLeaveDays,
    SUM(CASE WHEN AttendanceCode = 'CR' THEN 1 ELSE 0 END) as CompensationRestDays
FROM MonthlyAttendanceSheet
GROUP BY YEAR(AttendanceDate), MONTH(AttendanceDate), EmployeeCode
GO

-- إنشاء stored procedure لجلب بيانات الحضور الشهري
CREATE PROCEDURE GetMonthlyAttendanceSheet
    @Month INT,
    @Year INT,
    @EmployeeCode NVARCHAR(20) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        mas.EmployeeCode,
        mas.AttendanceDate,
        mas.AttendanceStatus,
        mas.AttendanceCode,
        mas.LeaveType,
        mas.Notes,
        DAY(mas.AttendanceDate) as DayOfMonth,
        DATENAME(WEEKDAY, mas.AttendanceDate) as DayName
    FROM MonthlyAttendanceSheet mas
    WHERE MONTH(mas.AttendanceDate) = @Month
        AND YEAR(mas.AttendanceDate) = @Year
        AND (@EmployeeCode IS NULL OR mas.EmployeeCode = @EmployeeCode)
    ORDER BY mas.EmployeeCode, mas.AttendanceDate
END
GO

-- إنشاء stored procedure لحفظ بيانات الحضور الشهري
CREATE PROCEDURE SaveMonthlyAttendanceSheet
    @EmployeeCode NVARCHAR(20),
    @AttendanceDate DATE,
    @AttendanceStatus NVARCHAR(50),
    @AttendanceCode NVARCHAR(10),
    @LeaveType NVARCHAR(100) = NULL,
    @Notes NVARCHAR(MAX) = NULL,
    @CreatedBy NVARCHAR(100) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    -- التحقق من وجود السجل
    IF EXISTS (SELECT 1 FROM MonthlyAttendanceSheet WHERE EmployeeCode = @EmployeeCode AND AttendanceDate = @AttendanceDate)
    BEGIN
        -- تحديث السجل الموجود
        UPDATE MonthlyAttendanceSheet 
        SET 
            AttendanceStatus = @AttendanceStatus,
            AttendanceCode = @AttendanceCode,
            LeaveType = @LeaveType,
            Notes = @Notes,
            UpdatedAt = GETDATE(),
            UpdatedBy = @CreatedBy
        WHERE EmployeeCode = @EmployeeCode AND AttendanceDate = @AttendanceDate
    END
    ELSE
    BEGIN
        -- إدراج سجل جديد
        INSERT INTO MonthlyAttendanceSheet 
        (EmployeeCode, AttendanceDate, AttendanceStatus, AttendanceCode, LeaveType, Notes, CreatedBy)
        VALUES 
        (@EmployeeCode, @AttendanceDate, @AttendanceStatus, @AttendanceCode, @LeaveType, @Notes, @CreatedBy)
    END
END
GO

-- إنشاء stored procedure للحصول على إحصائيات الحضور الشهري
CREATE PROCEDURE GetMonthlyAttendanceStatistics
    @Month INT,
    @Year INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        AttendanceCode,
        AttendanceStatus,
        COUNT(*) as TotalCount,
        COUNT(DISTINCT EmployeeCode) as EmployeeCount
    FROM MonthlyAttendanceSheet
    WHERE MONTH(AttendanceDate) = @Month
        AND YEAR(AttendanceDate) = @Year
    GROUP BY AttendanceCode, AttendanceStatus
    ORDER BY AttendanceCode
END
GO

-- منح الصلاحيات
GRANT SELECT, INSERT, UPDATE, DELETE ON MonthlyAttendanceSheet TO [public]
GO

GRANT SELECT ON MonthlyAttendanceStats TO [public]
GO

GRANT EXECUTE ON GetMonthlyAttendanceSheet TO [public]
GO

GRANT EXECUTE ON SaveMonthlyAttendanceSheet TO [public]
GO

GRANT EXECUTE ON GetMonthlyAttendanceStatistics TO [public]
GO

PRINT 'تم إنشاء جدول الحضور الشهري وجميع العناصر المرتبطة به بنجاح'
PRINT 'Monthly Attendance Sheet table and related objects created successfully'
