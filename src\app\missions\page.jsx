'use client';

import { useState, useEffect } from 'react';
import { FaPlus, FaEdit, FaTrash, FaCheck, FaTimes, FaEye } from 'react-icons/fa';

export default function MissionsPage() {
  const [missions, setMissions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingMission, setEditingMission] = useState(null);
  const [formData, setFormData] = useState({
    destination: '',
    startDate: '',
    endDate: '',
    purpose: '',
    transportMethod: 'سيارة الشركة',
    accommodationType: 'فندق'
  });

  // جلب المأموريات
  const fetchMissions = async () => {
    try {
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
      const response = await fetch('/api/mission-management', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'get-missions',
          employeeCode: userInfo.code
        })
      });

      const result = await response.json();
      if (result.success) {
        setMissions(result.data);
      }
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMissions();
  }, []);

  // إنشاء مأمورية جديدة
  const createMission = async () => {
    try {
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
      
      // حساب عدد الأيام
      const start = new Date(formData.startDate);
      const end = new Date(formData.endDate);
      const daysCount = Math.ceil((end - start) / (1000 * 60 * 60 * 24)) + 1;

      const response = await fetch('/api/paper-requests', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'create',
          requestType: 'mission',
          employeeId: userInfo.code,
          employeeName: userInfo.name,
          department: userInfo.department,
          jobTitle: userInfo.jobTitle,
          startDate: formData.startDate,
          endDate: formData.endDate,
          daysCount: daysCount,
          missionDestination: formData.destination,
          missionPurpose: formData.purpose,
          transportMethod: formData.transportMethod,
          accommodationType: formData.accommodationType
        })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم إنشاء المأمورية بنجاح');
        setShowForm(false);
        setFormData({
          destination: '',
          startDate: '',
          endDate: '',
          purpose: '',
          transportMethod: 'سيارة الشركة',
          accommodationType: 'فندق'
        });
        fetchMissions();
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {

      alert('حدث خطأ في إنشاء المأمورية');
    }
  };

  // حذف مأمورية
  const deleteMission = async (missionId) => {
    if (!confirm('هل أنت متأكد من حذف هذه المأمورية؟')) return;

    try {
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
      
      const response = await fetch('/api/mission-management', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'delete-mission',
          requestId: missionId,
          deletedBy: userInfo.name,
          reason: 'حذف من واجهة المأموريات'
        })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم حذف المأمورية بنجاح');
        fetchMissions();
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {

      alert('حدث خطأ في حذف المأمورية');
    }
  };

  // اعتماد مأمورية
  const approveMission = async (missionId) => {
    try {
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
      
      const response = await fetch('/api/paper-requests', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'update-status',
          requestId: missionId,
          status: 'معتمدة',
          notes: 'تم الاعتماد من واجهة المأموريات',
          approvedBy: userInfo.name
        })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم اعتماد المأمورية بنجاح');
        fetchMissions();
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {

      alert('حدث خطأ في اعتماد المأمورية');
    }
  };

  // إلغاء اعتماد مأمورية
  const cancelMission = async (missionId) => {
    try {
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
      
      const response = await fetch('/api/mission-management', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'cancel-mission',
          requestId: missionId,
          cancelledBy: userInfo.name,
          reason: 'إلغاء من واجهة المأموريات'
        })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم إلغاء المأمورية بنجاح');
        fetchMissions();
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {

      alert('حدث خطأ في إلغاء المأمورية');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'معتمدة': return 'text-green-600 bg-green-100';
      case 'قيد المراجعة': return 'text-yellow-600 bg-yellow-100';
      case 'مرفوضة': return 'text-red-600 bg-red-100';
      case 'ملغية': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل المأموريات...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">إدارة المأموريات</h1>
              <p className="text-gray-600 mt-1">إدارة شاملة لجميع المأموريات</p>
            </div>
            <button
              onClick={() => setShowForm(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
            >
              <FaPlus /> مأمورية جديدة
            </button>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="text-2xl font-bold text-blue-600">{missions.length}</div>
            <div className="text-gray-600">إجمالي المأموريات</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="text-2xl font-bold text-green-600">
              {missions.filter(m => m.Status === 'معتمدة').length}
            </div>
            <div className="text-gray-600">معتمدة</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="text-2xl font-bold text-yellow-600">
              {missions.filter(m => m.Status === 'قيد المراجعة').length}
            </div>
            <div className="text-gray-600">قيد المراجعة</div>
          </div>
          <div className="bg-white p-4 rounded-lg shadow-sm">
            <div className="text-2xl font-bold text-gray-600">
              {missions.filter(m => m.Status === 'ملغية').length}
            </div>
            <div className="text-gray-600">ملغية</div>
          </div>
        </div>

        {/* قائمة المأموريات */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">قائمة المأموريات</h2>
          </div>
          
          {missions.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              <p>لا توجد مأموريات حالياً</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الوجهة</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">التاريخ</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المدة</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الغرض</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الإجراءات</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {missions.map((mission) => (
                    <tr key={mission.ID} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {mission.Destination}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {new Date(mission.StartDate).toLocaleDateString('ar-EG')} - {new Date(mission.EndDate).toLocaleDateString('ar-EG')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {mission.DaysCount} أيام
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                        {mission.MissionPurpose}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(mission.Status)}`}>
                          {mission.Status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex gap-2">
                          {mission.Status === 'قيد المراجعة' && (
                            <>
                              <button
                                onClick={() => approveMission(mission.ID)}
                                className="text-green-600 hover:text-green-900"
                                title="اعتماد"
                              >
                                <FaCheck />
                              </button>
                              <button
                                onClick={() => deleteMission(mission.ID)}
                                className="text-red-600 hover:text-red-900"
                                title="حذف"
                              >
                                <FaTrash />
                              </button>
                            </>
                          )}
                          {mission.Status === 'معتمدة' && (
                            <>
                              <button
                                onClick={() => cancelMission(mission.ID)}
                                className="text-yellow-600 hover:text-yellow-900"
                                title="إلغاء"
                              >
                                <FaTimes />
                              </button>
                              <button
                                onClick={() => deleteMission(mission.ID)}
                                className="text-red-600 hover:text-red-900"
                                title="حذف نهائي"
                              >
                                <FaTrash />
                              </button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* نموذج إنشاء مأمورية جديدة */}
      {showForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold mb-4">مأمورية جديدة</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">الوجهة</label>
                <input
                  type="text"
                  value={formData.destination}
                  onChange={(e) => setFormData({...formData, destination: e.target.value})}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  placeholder="مثال: القاهرة"
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">تاريخ البداية</label>
                  <input
                    type="date"
                    value={formData.startDate}
                    onChange={(e) => setFormData({...formData, startDate: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">تاريخ النهاية</label>
                  <input
                    type="date"
                    value={formData.endDate}
                    onChange={(e) => setFormData({...formData, endDate: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">الغرض</label>
                <textarea
                  value={formData.purpose}
                  onChange={(e) => setFormData({...formData, purpose: e.target.value})}
                  className="w-full border border-gray-300 rounded-md px-3 py-2"
                  rows="3"
                  placeholder="اجتماع مع العميل، تدريب، إلخ..."
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">وسيلة النقل</label>
                  <select
                    value={formData.transportMethod}
                    onChange={(e) => setFormData({...formData, transportMethod: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                  >
                    <option value="سيارة الشركة">سيارة الشركة</option>
                    <option value="سيارة شخصية">سيارة شخصية</option>
                    <option value="طيران">طيران</option>
                    <option value="قطار">قطار</option>
                    <option value="أتوبيس">أتوبيس</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">الإقامة</label>
                  <select
                    value={formData.accommodationType}
                    onChange={(e) => setFormData({...formData, accommodationType: e.target.value})}
                    className="w-full border border-gray-300 rounded-md px-3 py-2"
                  >
                    <option value="فندق">فندق</option>
                    <option value="شقة مفروشة">شقة مفروشة</option>
                    <option value="بدون إقامة">بدون إقامة</option>
                    <option value="أخرى">أخرى</option>
                  </select>
                </div>
              </div>
            </div>
            
            <div className="flex gap-3 mt-6">
              <button
                onClick={createMission}
                className="flex-1 bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700"
              >
                إنشاء المأمورية
              </button>
              <button
                onClick={() => setShowForm(false)}
                className="flex-1 bg-gray-300 text-gray-700 py-2 rounded-md hover:bg-gray-400"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
