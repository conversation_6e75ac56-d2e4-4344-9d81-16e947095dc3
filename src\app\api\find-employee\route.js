import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const searchTerm = searchParams.get('search') || '';

    if (!searchTerm) {
      return NextResponse.json({
        success: false,
        error: 'يرجى إدخال كلمة البحث'
      }, { status: 400 });
    }

    const pool = await getConnection();
    
    // البحث بالاسم
    const nameResult = await pool.request()
      .input('searchTerm', sql.NVarChar, `%${searchTerm}%`)
      .query(`
        SELECT TOP 10
          EmployeeCode,
          EmployeeName,
          JobTitle,
          Department,
          CurrentStatus
        FROM Employees
        WHERE EmployeeName LIKE @searchTerm
        ORDER BY EmployeeName
      `);

    // البحث بالكود إذا كان رقم
    let codeResult = { recordset: [] };
    if (/^\d+$/.test(searchTerm)) {
      codeResult = await pool.request()
        .input('code', sql.Int, parseInt(searchTerm))
        .query(`
          SELECT TOP 1
            EmployeeCode,
            EmployeeName,
            JobTitle,
            Department,
            CurrentStatus
          FROM Employees
          WHERE EmployeeCode = @code
        `);
    }

    return NextResponse.json({
      success: true,
      searchTerm: searchTerm,
      nameResults: nameResult.recordset,
      codeResult: codeResult.recordset[0] || null,
      totalFound: nameResult.recordset.length + (codeResult.recordset.length > 0 ? 1 : 0)
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  }
}
