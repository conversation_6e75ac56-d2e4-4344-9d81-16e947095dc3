import { NextResponse } from 'next/server';
import { getConnection } from '@/lib/db';
import sql from 'mssql';
import * as XLSX from 'xlsx';
import { writeFile, mkdir } from 'fs/promises';
import path from 'path';

// POST - رفع ملف Excel ومعالجته
export async function POST(request) {
  try {
    const formData = await request.formData();
    const file = formData.get('file');
    const uploadedBy = formData.get('uploadedBy') || 'System';

    if (!file) {
      return NextResponse.json({
        success: false,
        error: 'لم يتم اختيار ملف'
      }, { status: 400 });
    }

    // التحقق من نوع الملف
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel'
    ];

    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({
        success: false,
        error: 'نوع الملف غير مدعوم. يرجى رفع ملف Excel (.xlsx أو .xls)'
      }, { status: 400 });
    }

    // إنشاء مجلد الرفع إذا لم يكن موجوداً
    const uploadDir = path.join(process.cwd(), 'uploads', 'direct-managers');
    await mkdir(uploadDir, { recursive: true });

    // إنشاء اسم ملف فريد
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `direct-managers-${timestamp}-${file.name}`;
    const filePath = path.join(uploadDir, fileName);

    // حفظ الملف
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filePath, buffer);

    const pool = await getConnection();

    // تسجيل رفع الملف في قاعدة البيانات
    const fileUploadResult = await pool.request()
      .input('fileName', sql.NVarChar, fileName)
      .input('originalFileName', sql.NVarChar, file.name)
      .input('filePath', sql.NVarChar, filePath)
      .input('fileSize', sql.BigInt, file.size)
      .input('fileType', sql.NVarChar, file.type)
      .input('uploadedBy', sql.NVarChar, uploadedBy)
      .query(`
        INSERT INTO DirectManagersFileUploads (
          FileName, OriginalFileName, FilePath, FileSize, FileType, UploadedBy
        ) 
        OUTPUT INSERTED.ID
        VALUES (
          @fileName, @originalFileName, @filePath, @fileSize, @fileType, @uploadedBy
        )
      `);

    const uploadId = fileUploadResult.recordset[0].ID;

    try {
      // قراءة ملف Excel
      const workbook = XLSX.readFile(filePath);
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet);

      // تحديث حالة المعالجة
      await pool.request()
        .input('uploadId', sql.Int, uploadId)
        .input('totalRecords', sql.Int, jsonData.length)
        .query(`
          UPDATE DirectManagersFileUploads 
          SET ProcessingStatus = 'PROCESSING', TotalRecords = @totalRecords
          WHERE ID = @uploadId
        `);

      let successCount = 0;
      let errorCount = 0;
      const errors = [];

      // معالجة كل سجل
      for (let i = 0; i < jsonData.length; i++) {
        try {
          const row = jsonData[i];
          
          // تنظيف أسماء الأعمدة (إزالة المسافات والأحرف الخاصة)
          const cleanRow = {};
          Object.keys(row).forEach(key => {
            const cleanKey = key.trim().replace(/\s+/g, '');
            cleanRow[cleanKey] = row[key];
          });

          // استخراج البيانات من السطر
          const employeeCode = cleanRow['كودالموظف'] || cleanRow['EmployeeCode'] || cleanRow['رقمالموظف'];
          const employeeName = cleanRow['اسمالموظف'] || cleanRow['EmployeeName'] || cleanRow['الاسم'];
          const jobTitle = cleanRow['الوظيفة'] || cleanRow['JobTitle'] || cleanRow['المسمىالوظيفي'];
          const department = cleanRow['القسم'] || cleanRow['Department'];
          
          const manager1Code = cleanRow['كودالمديرالمباشر1'] || cleanRow['DirectManager1Code'] || cleanRow['المديرالمباشر1'];
          const manager1Name = cleanRow['المديرالمباشر1'] || cleanRow['DirectManager1Name'];
          const manager2Code = cleanRow['كودالمديرالمباشر2'] || cleanRow['DirectManager2Code'] || cleanRow['المديرالمباشر2'];
          const manager2Name = cleanRow['المديرالمباشر2'] || cleanRow['DirectManager2Name'];
          const manager3Code = cleanRow['كودالمديرالمباشر3'] || cleanRow['DirectManager3Code'] || cleanRow['المديرالمباشر3'];
          const manager3Name = cleanRow['المديرالمباشر3'] || cleanRow['DirectManager3Name'];
          const manager4Code = cleanRow['كودالمديرالمباشر4'] || cleanRow['DirectManager4Code'] || cleanRow['المديرالمباشر4'];
          const manager4Name = cleanRow['المديرالمباشر4'] || cleanRow['DirectManager4Name'];

          if (!employeeCode || !employeeName) {
            errors.push(`السطر ${i + 2}: كود الموظف والاسم مطلوبان`);
            errorCount++;
            continue;
          }

          // جلب أسماء المديرين من قاعدة البيانات إذا لم تكن موجودة
          const getManagerName = async (managerCode) => {
            if (!managerCode) return null;
            try {
              const result = await pool.request()
                .input('managerCode', sql.NVarChar, managerCode.toString())
                .query('SELECT EmployeeName FROM Employees WHERE EmployeeCode = @managerCode');
              return result.recordset.length > 0 ? result.recordset[0].EmployeeName : null;
            } catch {
              return null;
            }
          };

          const finalManager1Name = manager1Name || await getManagerName(manager1Code);
          const finalManager2Name = manager2Name || await getManagerName(manager2Code);
          const finalManager3Name = manager3Name || await getManagerName(manager3Code);
          const finalManager4Name = manager4Name || await getManagerName(manager4Code);

          // حفظ البيانات باستخدام الإجراء المخزن
          await pool.request()
            .input('EmployeeCode', sql.NVarChar, employeeCode.toString())
            .input('EmployeeName', sql.NVarChar, employeeName)
            .input('JobTitle', sql.NVarChar, jobTitle)
            .input('Department', sql.NVarChar, department)
            .input('DirectManager1Code', sql.NVarChar, manager1Code ? manager1Code.toString() : null)
            .input('DirectManager1Name', sql.NVarChar, finalManager1Name)
            .input('DirectManager2Code', sql.NVarChar, manager2Code ? manager2Code.toString() : null)
            .input('DirectManager2Name', sql.NVarChar, finalManager2Name)
            .input('DirectManager3Code', sql.NVarChar, manager3Code ? manager3Code.toString() : null)
            .input('DirectManager3Name', sql.NVarChar, finalManager3Name)
            .input('DirectManager4Code', sql.NVarChar, manager4Code ? manager4Code.toString() : null)
            .input('DirectManager4Name', sql.NVarChar, finalManager4Name)
            .input('Notes', sql.NVarChar, `تم الاستيراد من الملف: ${file.name}`)
            .input('UpdatedBy', sql.NVarChar, uploadedBy)
            .execute('sp_UpdateDirectManager');

          successCount++;

        } catch (rowError) {

          errors.push(`السطر ${i + 2}: ${rowError.message}`);
          errorCount++;
        }

        // تحديث تقدم المعالجة كل 10 سجلات
        if ((i + 1) % 10 === 0) {
          await pool.request()
            .input('uploadId', sql.Int, uploadId)
            .input('processedRecords', sql.Int, i + 1)
            .input('successfulRecords', sql.Int, successCount)
            .input('failedRecords', sql.Int, errorCount)
            .query(`
              UPDATE DirectManagersFileUploads 
              SET ProcessedRecords = @processedRecords,
                  SuccessfulRecords = @successfulRecords,
                  FailedRecords = @failedRecords
              WHERE ID = @uploadId
            `);
        }
      }

      // تحديث الحالة النهائية
      await pool.request()
        .input('uploadId', sql.Int, uploadId)
        .input('processedRecords', sql.Int, jsonData.length)
        .input('successfulRecords', sql.Int, successCount)
        .input('failedRecords', sql.Int, errorCount)
        .input('errorLog', sql.NVarChar, errors.length > 0 ? JSON.stringify(errors) : null)
        .query(`
          UPDATE DirectManagersFileUploads 
          SET ProcessingStatus = 'COMPLETED',
              ProcessedRecords = @processedRecords,
              SuccessfulRecords = @successfulRecords,
              FailedRecords = @failedRecords,
              ErrorLog = @errorLog,
              ProcessedAt = GETDATE()
          WHERE ID = @uploadId
        `);

      return NextResponse.json({
        success: true,
        message: 'تم رفع ومعالجة الملف بنجاح',
        data: {
          uploadId,
          fileName: file.name,
          totalRecords: jsonData.length,
          successfulRecords: successCount,
          failedRecords: errorCount,
          errors: errors.slice(0, 10) // عرض أول 10 أخطاء فقط
        }
      });

    } catch (processingError) {

      // تحديث حالة الفشل
      await pool.request()
        .input('uploadId', sql.Int, uploadId)
        .input('errorLog', sql.NVarChar, processingError.message)
        .query(`
          UPDATE DirectManagersFileUploads 
          SET ProcessingStatus = 'FAILED',
              ErrorLog = @errorLog,
              ProcessedAt = GETDATE()
          WHERE ID = @uploadId
        `);

      return NextResponse.json({
        success: false,
        error: `خطأ في معالجة الملف: ${processingError.message}`
      }, { status: 500 });
    }

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// GET - جلب سجل رفع الملفات
export async function GET() {
  try {
    const pool = await getConnection();

    const result = await pool.request().query(`
      SELECT 
        ID,
        FileName,
        OriginalFileName,
        FileSize,
        ProcessingStatus,
        TotalRecords,
        ProcessedRecords,
        SuccessfulRecords,
        FailedRecords,
        UploadedBy,
        UploadedAt,
        ProcessedAt
      FROM DirectManagersFileUploads
      ORDER BY UploadedAt DESC
    `);

    return NextResponse.json({
      success: true,
      data: result.recordset
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
