import { NextResponse } from 'next/server';
import sql from 'mssql';

// إعدادات قاعدة البيانات
const dbConfig = {
  user: 'SA',
  password: 'admin@123',
  server: 'localhost\\DBOJESTA',
  database: 'EMP',
  options: {
    encrypt: false,
    trustServerCertificate: true,
    enableArithAbort: true,
    instanceName: 'DBOJESTA'
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
  requestTimeout: 30000,
  connectionTimeout: 30000,
};

// بناء الشجرة الهرمية للخريطة التنظيمية
function buildOrganizationalChart(units, employees) {
  const unitMap = new Map();
  const employeeMap = new Map();
  
  // إنشاء خريطة للوحدات
  units.forEach(unit => {
    unitMap.set(unit.ID, {
      id: unit.ID,
      name: unit.UnitName,
      code: unit.UnitCode,
      managerCode: unit.ManagerEmployeeCode,
      managerName: unit.ManagerName,
      level: unit.UnitLevel,
      type: unit.UnitType,
      parentId: unit.ParentUnitID,
      employees: [],
      children: []
    });
  });

  // إنشاء خريطة للموظفين
  employees.forEach(emp => {
    employeeMap.set(emp.EmployeeCode, {
      EmployeeCode: emp.EmployeeCode,
      EmployeeName: emp.EmployeeName,
      JobTitle: emp.JobTitle || emp.Position || 'موظف',
      Department: emp.UnitName || 'غير محدد',
      UnitID: emp.UnitID,
      IsDirectManager: emp.IsDirectManager,
      Mobile: emp.Mobile || '',
      Area: emp.Area || '',
      children: []
    });
  });

  // ربط الموظفين بالوحدات
  employees.forEach(emp => {
    const unit = unitMap.get(emp.UnitID);
    const employee = employeeMap.get(emp.EmployeeCode);
    if (unit && employee) {
      unit.employees.push(employee);
    }
  });

  // بناء الهيكل الهرمي للوحدات
  const rootUnits = [];
  unitMap.forEach(unit => {
    if (unit.parentId) {
      const parent = unitMap.get(unit.parentId);
      if (parent) {
        parent.children.push(unit);
      }
    } else {
      rootUnits.push(unit);
    }
  });

  // تحويل الهيكل إلى شجرة موظفين
  function convertToEmployeeTree(unit) {
    // العثور على المدير
    const manager = unit.employees.find(emp => emp.IsDirectManager);
    
    if (!manager) {
      // إذا لم يوجد مدير، إنشاء عقدة افتراضية
      const virtualManager = {
        EmployeeCode: unit.managerCode || `VIRTUAL_${unit.id}`,
        EmployeeName: unit.managerName || unit.name,
        JobTitle: `مدير ${unit.type}`,
        Department: unit.name,
        UnitID: unit.id,
        IsDirectManager: true,
        children: [],
        x: 0,
        y: 0,
        level: unit.level - 1
      };

      // إضافة الموظفين العاديين كأطفال
      unit.employees.forEach(emp => {
        if (!emp.IsDirectManager) {
          virtualManager.children.push({
            ...emp,
            children: [],
            x: 0,
            y: 0,
            level: unit.level
          });
        }
      });

      // إضافة الوحدات الفرعية
      unit.children.forEach(childUnit => {
        const childTree = convertToEmployeeTree(childUnit);
        if (childTree) {
          virtualManager.children.push(childTree);
        }
      });

      return virtualManager;
    }

    // إضافة الموظفين العاديين كأطفال للمدير
    unit.employees.forEach(emp => {
      if (!emp.IsDirectManager) {
        manager.children.push({
          ...emp,
          children: [],
          x: 0,
          y: 0,
          level: unit.level
        });
      }
    });

    // إضافة مديري الوحدات الفرعية كأطفال
    unit.children.forEach(childUnit => {
      const childTree = convertToEmployeeTree(childUnit);
      if (childTree) {
        manager.children.push(childTree);
      }
    });

    manager.x = 0;
    manager.y = 0;
    manager.level = unit.level - 1;

    return manager;
  }

  const organizationTree = rootUnits.map(convertToEmployeeTree).filter(Boolean);
  
  return organizationTree;
}

// إنشاء جدول هرمي
function createHierarchyTable(employees, units) {
  const table = [];
  let serial = 1;

  employees.forEach(emp => {
    const unit = units.find(u => u.ID === emp.UnitID);
    
    // البحث عن المديرين في التسلسل الهرمي
    let currentUnit = unit;
    const managers = [];
    
    while (currentUnit && managers.length < 3) {
      if (currentUnit.ParentUnitID) {
        const parentUnit = units.find(u => u.ID === currentUnit.ParentUnitID);
        if (parentUnit) {
          managers.push({
            code: parentUnit.ManagerEmployeeCode,
            name: parentUnit.ManagerName
          });
          currentUnit = parentUnit;
        } else {
          break;
        }
      } else {
        break;
      }
    }

    table.push({
      serial: serial++,
      employeeCode: emp.EmployeeCode,
      employeeName: emp.EmployeeName,
      jobTitle: emp.JobTitle || emp.Position || 'موظف',
      manager1Code: managers[0]?.code || '',
      manager1Name: managers[0]?.name || '',
      manager2Code: managers[1]?.code || '',
      manager2Name: managers[1]?.name || '',
      manager3Code: managers[2]?.code || '',
      manager3Name: managers[2]?.name || ''
    });
  });

  return table;
}

// GET - جلب بيانات الخريطة التنظيمية
export async function GET(request) {
  let pool;
  
  try {
    pool = await sql.connect(dbConfig);

    // جلب جميع الوحدات التنظيمية
    const unitsResult = await pool.request().query(`
      SELECT 
        ID,
        UnitName,
        UnitCode,
        ParentUnitID,
        ManagerEmployeeCode,
        ManagerName,
        UnitLevel,
        UnitType
      FROM OrganizationalUnits 
      WHERE IsActive = 1
      ORDER BY UnitLevel, UnitName
    `);

    // جلب جميع الموظفين مع وحداتهم
    const employeesResult = await pool.request().query(`
      SELECT 
        emp.EmployeeCode,
        emp.EmployeeName,
        emp.JobTitle,
        emp.Mobile,
        emp.Area,
        eu.UnitID,
        eu.Position,
        eu.IsDirectManager,
        ou.UnitName
      FROM Employees emp
      INNER JOIN EmployeeUnits eu ON emp.EmployeeCode = eu.EmployeeCode
      INNER JOIN OrganizationalUnits ou ON eu.UnitID = ou.ID
      WHERE eu.IsActive = 1 AND ou.IsActive = 1
      ORDER BY ou.UnitLevel, emp.EmployeeName
    `);

    const units = unitsResult.recordset;
    const employees = employeesResult.recordset;

    // بناء الشجرة التنظيمية
    const organizationTree = buildOrganizationalChart(units, employees);

    // إنشاء قائمة الأقسام
    const departments = [...new Set(units.map(unit => unit.UnitName))];

    // إنشاء الجدول الهرمي
    const hierarchyTable = createHierarchyTable(employees, units);

    // تحويل الشجرة إلى قائمة مسطحة للموظفين
    function flattenTree(nodes) {
      let result = [];
      nodes.forEach(node => {
        result.push(node);
        if (node.children && node.children.length > 0) {
          result = result.concat(flattenTree(node.children));
        }
      });
      return result;
    }

    const allEmployees = flattenTree(organizationTree);

    return NextResponse.json({
      success: true,
      employees: allEmployees,
      departments: departments,
      organizationTree: organizationTree,
      hierarchyTable: hierarchyTable,
      statistics: {
        totalEmployees: employees.length,
        totalUnits: units.length,
        totalManagers: employees.filter(emp => emp.IsDirectManager).length,
        maxLevels: Math.max(...units.map(unit => unit.UnitLevel))
      },
      message: 'تم جلب بيانات الخريطة التنظيمية بنجاح'
    });

  } catch (error) {
    console.error('❌ خطأ في جلب بيانات الخريطة التنظيمية:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب بيانات الخريطة التنظيمية: ' + error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      await pool.close();
    }
  }
}
