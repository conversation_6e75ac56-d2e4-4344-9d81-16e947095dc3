'use client';

import { useState } from 'react';
import { FiEdit3, <PERSON><PERSON><PERSON><PERSON>, FiX } from 'react-icons/fi';

export default function QuickEditCell({ 
  value, 
  employeeId, 
  field, 
  onUpdate,
  type = 'text',
  options = null 
}) {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value || '');
  const [isLoading, setIsLoading] = useState(false);

  const handleEdit = () => {
    setIsEditing(true);
    setEditValue(value || '');
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditValue(value || '');
  };

  const handleSave = async () => {
    if (editValue === value) {
      setIsEditing(false);
      return;
    }

    setIsLoading(true);
    
    try {
      const response = await fetch('/api/employee-quick-update', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          employeeId,
          field,
          value: editValue
        }),
      });

      const data = await response.json();

      if (data.success) {
        setIsEditing(false);
        if (onUpdate) {
          onUpdate(field, editValue);
        }
        
        // إظهار رسالة نجاح مؤقتة
        const successMsg = document.createElement('div');
        successMsg.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50';
        successMsg.textContent = 'تم التحديث بنجاح ✓';
        document.body.appendChild(successMsg);
        
        setTimeout(() => {
          document.body.removeChild(successMsg);
        }, 2000);
        
      } else {
        alert('خطأ: ' + data.message);
      }
    } catch (error) {

      alert('حدث خطأ في التحديث');
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSave();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  if (!isEditing) {
    return (
      <div className="group flex items-center justify-between">
        <span className="flex-1">{value || '-'}</span>
        <button
          onClick={handleEdit}
          className="opacity-0 group-hover:opacity-100 ml-2 text-blue-600 hover:text-blue-800 transition-opacity"
          title="تعديل سريع"
        >
          <FiEdit3 className="text-sm" />
        </button>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-1">
      {type === 'select' && options ? (
        <select
          value={editValue}
          onChange={(e) => setEditValue(e.target.value)}
          onKeyDown={handleKeyPress}
          className="flex-1 px-2 py-1 border border-blue-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          autoFocus
          disabled={isLoading}
        >
          <option value="">اختر...</option>
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      ) : (
        <input
          type={type}
          value={editValue}
          onChange={(e) => setEditValue(e.target.value)}
          onKeyDown={handleKeyPress}
          className="flex-1 px-2 py-1 border border-blue-300 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          autoFocus
          disabled={isLoading}
        />
      )}
      
      <button
        onClick={handleSave}
        disabled={isLoading}
        className="text-green-600 hover:text-green-800 disabled:opacity-50"
        title="حفظ"
      >
        <FiCheck className="text-sm" />
      </button>
      
      <button
        onClick={handleCancel}
        disabled={isLoading}
        className="text-red-600 hover:text-red-800 disabled:opacity-50"
        title="إلغاء"
      >
        <FiX className="text-sm" />
      </button>
    </div>
  );
}
