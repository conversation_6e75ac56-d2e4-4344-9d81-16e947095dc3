'use client';

import { useState, useEffect } from 'react';
import { 
  FiCalendar, 
  FiClock, 
  FiUsers, 
  FiFileText,
  FiChevronDown,
  FiInfo
} from 'react-icons/fi';
import { 
  CYCLE_TYPES, 
  getCurrentCycle, 
  getAvailableCycles,
  calculateCycleDates,
  formatDateArabic,
  getCycleDuration
} from '@/utils/dateFilters';

export default function CycleFilter({ 
  cycleType, 
  selectedCycle, 
  onCycleChange, 
  className = '',
  showDescription = true,
  isArabic = true 
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [availableCycles, setAvailableCycles] = useState([]);
  const [currentCycleInfo, setCurrentCycleInfo] = useState(null);

  useEffect(() => {
    // جلب الدورات المتاحة
    const cycles = getAvailableCycles(cycleType);
    setAvailableCycles(cycles);

    // جلب معلومات الدورة الحالية
    const current = getCurrentCycle(cycleType);
    setCurrentCycleInfo(current);

    // إذا لم يتم تحديد دورة، استخدم الحالية
    if (!selectedCycle) {
      const currentMonth = new Date().getMonth() + 1;
      const currentYear = new Date().getFullYear();
      onCycleChange(`${currentYear}-${currentMonth.toString().padStart(2, '0')}`);
    }
  }, [cycleType, selectedCycle, onCycleChange]);

  const getCycleIcon = () => {
    switch (cycleType) {
      case CYCLE_TYPES.MONTHLY_EFFECTS:
        return <FiClock className="text-blue-600" />;
      case CYCLE_TYPES.TEMP_WORKERS:
        return <FiUsers className="text-green-600" />;
      case CYCLE_TYPES.VERSION_REQUEST:
        return <FiFileText className="text-purple-600" />;
      default:
        return <FiCalendar className="text-gray-600" />;
    }
  };

  const getCycleTitle = () => {
    if (!isArabic) {
      switch (cycleType) {
        case CYCLE_TYPES.MONTHLY_EFFECTS:
          return 'Monthly Effects Period';
        case CYCLE_TYPES.TEMP_WORKERS:
          return 'Temporary Workers Period';
        case CYCLE_TYPES.VERSION_REQUEST:
          return 'Version Request Period';
        default:
          return 'Period Filter';
      }
    }

    switch (cycleType) {
      case CYCLE_TYPES.MONTHLY_EFFECTS:
        return 'فترة المؤثرات الشهرية';
      case CYCLE_TYPES.TEMP_WORKERS:
        return 'فترة العمالة المؤقتة';
      case CYCLE_TYPES.VERSION_REQUEST:
        return 'فترة طلب الإصدار';
      default:
        return 'فلتر الفترة الزمنية';
    }
  };

  const getSelectedCycleInfo = () => {
    if (!selectedCycle) return null;
    
    const [year, month] = selectedCycle.split('-').map(Number);
    return calculateCycleDates(cycleType, month, year);
  };

  const selectedInfo = getSelectedCycleInfo();

  return (
    <div className={`relative ${className}`}>
      {/* عنوان الفلتر */}
      <div className="flex items-center gap-2 mb-3">
        {getCycleIcon()}
        <h3 className="font-medium text-gray-800 dark:text-white">
          {getCycleTitle()}
        </h3>
      </div>

      {/* قائمة الاختيار */}
      <div className="relative">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="w-full flex items-center justify-between p-3 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg hover:border-blue-500 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200"
        >
          <div className="flex items-center gap-3">
            <FiCalendar className="text-gray-500" />
            <div className="text-right">
              <div className="font-medium text-gray-800 dark:text-white">
                {selectedInfo ? selectedInfo.displayName : 'اختر الفترة'}
              </div>
              {selectedInfo && showDescription && (
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  {selectedInfo.description}
                </div>
              )}
            </div>
          </div>
          <FiChevronDown className={`text-gray-400 transition-transform duration-200 ${
            isOpen ? 'rotate-180' : ''
          }`} />
        </button>

        {/* القائمة المنسدلة */}
        {isOpen && (
          <div className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto">
            {/* الدورة الحالية */}
            {currentCycleInfo && (
              <>
                <div className="p-3 border-b border-gray-200 dark:border-gray-600">
                  <div className="flex items-center gap-2 text-sm font-medium text-blue-600 dark:text-blue-400 mb-2">
                    <FiInfo className="text-xs" />
                    {isArabic ? 'الدورة الحالية' : 'Current Cycle'}
                  </div>
                  <button
                    onClick={() => {
                      const currentMonth = new Date().getMonth() + 1;
                      const currentYear = new Date().getFullYear();
                      onCycleChange(`${currentYear}-${currentMonth.toString().padStart(2, '0')}`);
                      setIsOpen(false);
                    }}
                    className="w-full text-right p-2 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors duration-200"
                  >
                    <div className="font-medium text-gray-800 dark:text-white">
                      {currentCycleInfo.displayName}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {currentCycleInfo.description}
                    </div>
                    <div className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                      {getCycleDuration(cycleType, new Date().getMonth() + 1, new Date().getFullYear())} يوم
                    </div>
                  </button>
                </div>
              </>
            )}

            {/* جميع الدورات المتاحة */}
            <div className="p-2">
              <div className="text-sm font-medium text-gray-600 dark:text-gray-400 px-2 py-1 mb-2">
                {isArabic ? 'جميع الفترات المتاحة' : 'All Available Periods'}
              </div>
              {availableCycles.map((cycle) => (
                <button
                  key={cycle.value}
                  onClick={() => {
                    onCycleChange(cycle.value);
                    setIsOpen(false);
                  }}
                  className={`w-full text-right p-3 rounded-lg transition-colors duration-200 ${
                    selectedCycle === cycle.value
                      ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200'
                      : 'hover:bg-gray-50 dark:hover:bg-gray-600'
                  }`}
                >
                  <div className="font-medium text-gray-800 dark:text-white">
                    {cycle.label}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {cycle.description}
                  </div>
                  <div className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                    {formatDateArabic(cycle.startDate)} - {formatDateArabic(cycle.endDate)}
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* معلومات إضافية عن الفترة المحددة */}
      {selectedInfo && showDescription && (
        <div className="mt-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-blue-800 dark:text-blue-200">
                {isArabic ? 'تاريخ البداية:' : 'Start Date:'}
              </span>
              <div className="text-blue-600 dark:text-blue-300">
                {formatDateArabic(selectedInfo.startDate)}
              </div>
            </div>
            <div>
              <span className="font-medium text-blue-800 dark:text-blue-200">
                {isArabic ? 'تاريخ النهاية:' : 'End Date:'}
              </span>
              <div className="text-blue-600 dark:text-blue-300">
                {formatDateArabic(selectedInfo.endDate)}
              </div>
            </div>
          </div>
          <div className="mt-2 text-xs text-blue-600 dark:text-blue-400">
            {isArabic ? 'المدة:' : 'Duration:'} {getCycleDuration(cycleType, ...selectedCycle.split('-').map(Number).reverse())} {isArabic ? 'يوم' : 'days'}
          </div>
        </div>
      )}

      {/* شرح نوع الدورة */}
      <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div className="text-xs text-gray-600 dark:text-gray-400">
          {cycleType === CYCLE_TYPES.MONTHLY_EFFECTS && (
            <>
              <strong>{isArabic ? 'دورة المؤثرات الشهرية:' : 'Monthly Effects Cycle:'}</strong>
              <br />
              {isArabic ? 'تبدأ من يوم 11 من الشهر السابق وتنتهي في يوم 10 من الشهر الحالي' : 'Starts on the 11th of the previous month and ends on the 10th of the current month'}
            </>
          )}
          {cycleType === CYCLE_TYPES.TEMP_WORKERS && (
            <>
              <strong>{isArabic ? 'دورة العمالة المؤقتة:' : 'Temporary Workers Cycle:'}</strong>
              <br />
              {isArabic ? 'تتبع الشهر الميلادي العادي من 1 إلى آخر يوم في الشهر' : 'Follows the regular calendar month from 1st to the last day of the month'}
            </>
          )}
          {cycleType === CYCLE_TYPES.VERSION_REQUEST && (
            <>
              <strong>{isArabic ? 'دورة طلب الإصدار:' : 'Version Request Cycle:'}</strong>
              <br />
              {isArabic ? 'تبدأ من يوم 14 من الشهر الحالي وتنتهي في يوم 15 من الشهر التالي' : 'Starts on the 14th of the current month and ends on the 15th of the next month'}
            </>
          )}
        </div>
      </div>

      {/* إغلاق القائمة عند النقر خارجها */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
