'use client';
import React from 'react';

import { useUpload } from '@/app/utilities/runtime-helpers';

function MainComponent() {
  const [selectedLang, setSelectedLang] = useState('ar');
  const [employeeData, setEmployeeData] = useState({
    personal: {
      name: 'أحم<PERSON> محمد',
      nameEn: '<PERSON>',
      birthDate: '1990-01-01',
      nationality: 'سعودي',
      nationalityEn: 'Saudi',
      idNumber: '**********',
      phone: '+************',
      email: '<EMAIL>',
      address: 'الرياض، السعودية',
      addressEn: 'Riyadh, Saudi Arabia',
    },
    employment: {
      position: 'مهندس برمجيات',
      positionEn: 'Software Engineer',
      department: 'تقنية المعلومات',
      departmentEn: 'IT',
      startDate: '2022-01-01',
      employeeId: 'EMP001',
      status: 'نشط',
      statusEn: 'Active',
    },
    financial: {
      salary: 15000,
      bankName: 'البنك الأهلي',
      bankNameEn: 'NCB',
      accountNumber: 'SA0123456789',
      iban: '************************',
    },
  });

  const [documents, setDocuments] = useState([
    { id: 1, name: 'الهوية الوطنية', nameEn: 'National ID', status: 'valid' },
    {
      id: 2,
      name: 'عقد العمل',
      nameEn: 'Employment Contract',
      status: 'valid',
    },
    {
      id: 3,
      name: 'الشهادة الجامعية',
      nameEn: 'University Degree',
      status: 'expired',
    },
  ]);

  const [editMode, setEditMode] = useState(false);
  const [upload, { loading: uploading }] = useUpload();
  const dir = selectedLang === 'ar' ? 'rtl' : 'ltr';

  const handleDocumentUpload = async (e) => {
    const file = e.target.files[0];
    if (file) {
      try {
        const { url } = await upload({ file });
        setDocuments((prev) => [
          ...prev,
          {
            id: Date.now(),
            name: file.name,
            nameEn: file.name,
            url,
            status: 'valid',
          },
        ]);
      } catch (error) {

      }
    }
  };

  const handleSave = async () => {
    try {
      const response = await fetch('/api/employee-data', {
        method: 'PUT',
        body: JSON.stringify(employeeData),
      });

      if (!response.ok) {
        throw new Error('Failed to save');
      }

      setEditMode(false);
    } catch (error) {

    }
  };

  return (
    <div
      dir={dir}
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <a
            href="/employees"
            className="flex items-center text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
          >
            <i
              className={`fas fa-arrow-${
                selectedLang === 'ar' ? 'left' : 'right'
              } ml-2`}
            ></i>
            {selectedLang === 'ar' ? 'عودة' : 'Back'}
          </a>
          <button
            onClick={() => setSelectedLang(selectedLang === 'ar' ? 'en' : 'ar')}
            className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
          >
            {selectedLang === 'ar' ? 'English' : 'العربية'}
          </button>
        </div>

        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {selectedLang === 'ar' ? 'بيانات الموظف' : 'Employee Data'}
          </h1>
          <button
            onClick={() => (editMode ? handleSave() : setEditMode(true))}
            className={`px-4 py-2 rounded-md ${
              editMode
                ? 'bg-green-600 hover:bg-green-700'
                : 'bg-blue-600 hover:bg-blue-700'
            } text-white`}
          >
            {editMode
              ? selectedLang === 'ar'
                ? 'حفظ التغييرات'
                : 'Save Changes'
              : selectedLang === 'ar'
                ? 'تعديل البيانات'
                : 'Edit Data'}
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              {selectedLang === 'ar'
                ? 'البيانات الشخصية'
                : 'Personal Information'}
            </h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm text-gray-600 dark:text-gray-400">
                  {selectedLang === 'ar' ? 'الاسم' : 'Name'}
                </label>
                <input
                  type="text"
                  value={
                    selectedLang === 'ar'
                      ? employeeData.personal.name
                      : employeeData.personal.nameEn
                  }
                  disabled={!editMode}
                  className="w-full mt-1 p-2 border rounded-md"
                  onChange={(e) =>
                    setEmployeeData((prev) => ({
                      ...prev,
                      personal: {
                        ...prev.personal,
                        [selectedLang === 'ar' ? 'name' : 'nameEn']:
                          e.target.value,
                      },
                    }))
                  }
                />
              </div>
              <div>
                <label className="block text-sm text-gray-600 dark:text-gray-400">
                  {selectedLang === 'ar' ? 'تاريخ الميلاد' : 'Birth Date'}
                </label>
                <input
                  type="date"
                  value={employeeData.personal.birthDate}
                  disabled={!editMode}
                  className="w-full mt-1 p-2 border rounded-md"
                  onChange={(e) =>
                    setEmployeeData((prev) => ({
                      ...prev,
                      personal: {
                        ...prev.personal,
                        birthDate: e.target.value,
                      },
                    }))
                  }
                />
              </div>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              {selectedLang === 'ar'
                ? 'البيانات الوظيفية'
                : 'Employment Information'}
            </h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm text-gray-600 dark:text-gray-400">
                  {selectedLang === 'ar' ? 'المنصب' : 'Position'}
                </label>
                <input
                  type="text"
                  value={
                    selectedLang === 'ar'
                      ? employeeData.employment.position
                      : employeeData.employment.positionEn
                  }
                  disabled={!editMode}
                  className="w-full mt-1 p-2 border rounded-md"
                  onChange={(e) =>
                    setEmployeeData((prev) => ({
                      ...prev,
                      employment: {
                        ...prev.employment,
                        [selectedLang === 'ar' ? 'position' : 'positionEn']:
                          e.target.value,
                      },
                    }))
                  }
                />
              </div>
              <div>
                <label className="block text-sm text-gray-600 dark:text-gray-400">
                  {selectedLang === 'ar' ? 'تاريخ التعيين' : 'Start Date'}
                </label>
                <input
                  type="date"
                  value={employeeData.employment.startDate}
                  disabled={!editMode}
                  className="w-full mt-1 p-2 border rounded-md"
                  onChange={(e) =>
                    setEmployeeData((prev) => ({
                      ...prev,
                      employment: {
                        ...prev.employment,
                        startDate: e.target.value,
                      },
                    }))
                  }
                />
              </div>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              {selectedLang === 'ar'
                ? 'البيانات المالية'
                : 'Financial Information'}
            </h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm text-gray-600 dark:text-gray-400">
                  {selectedLang === 'ar' ? 'الراتب' : 'Salary'}
                </label>
                <input
                  type="number"
                  value={employeeData.financial.salary}
                  disabled={!editMode}
                  className="w-full mt-1 p-2 border rounded-md"
                  onChange={(e) =>
                    setEmployeeData((prev) => ({
                      ...prev,
                      financial: {
                        ...prev.financial,
                        salary: e.target.value,
                      },
                    }))
                  }
                />
              </div>
              <div>
                <label className="block text-sm text-gray-600 dark:text-gray-400">
                  {selectedLang === 'ar' ? 'رقم الحساب' : 'Account Number'}
                </label>
                <input
                  type="text"
                  value={employeeData.financial.accountNumber}
                  disabled={!editMode}
                  className="w-full mt-1 p-2 border rounded-md"
                  onChange={(e) =>
                    setEmployeeData((prev) => ({
                      ...prev,
                      financial: {
                        ...prev.financial,
                        accountNumber: e.target.value,
                      },
                    }))
                  }
                />
              </div>
            </div>
          </div>
        </div>

        <div className="mt-8 bg-gray-50 dark:bg-gray-800 p-6 rounded-lg">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              {selectedLang === 'ar' ? 'المستندات' : 'Documents'}
            </h2>
            {editMode && (
              <div className="relative">
                <input
                  type="file"
                  onChange={handleDocumentUpload}
                  className="hidden"
                  id="document-upload"
                />
                <label
                  htmlFor="document-upload"
                  className="cursor-pointer bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                >
                  {selectedLang === 'ar' ? 'إضافة مستند' : 'Add Document'}
                </label>
              </div>
            )}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {documents.map((doc) => (
              <div key={doc.id} className="border p-4 rounded-md">
                <div className="flex items-center justify-between">
                  <span className="text-gray-900 dark:text-white">
                    {selectedLang === 'ar' ? doc.name : doc.nameEn}
                  </span>
                  <span
                    className={`px-2 py-1 rounded-full text-xs ${
                      doc.status === 'valid'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {selectedLang === 'ar'
                      ? doc.status === 'valid'
                        ? 'ساري'
                        : 'منتهي'
                      : doc.status === 'valid'
                        ? 'Valid'
                        : 'Expired'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default MainComponent;
