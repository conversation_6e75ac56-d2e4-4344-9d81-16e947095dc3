import fs from 'fs';
import { NextResponse } from 'next/server';
import path from 'path';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const fileName = searchParams.get('file');
    const type = searchParams.get('type');

    if (!fileName || !type) {
      return NextResponse.json({
        success: false,
        error: 'اسم الملف ونوع التكلفة مطلوبان'
      }, { status: 400 });
    }

    // تحديد المسار حسب النوع
    const archivePaths = {
      'cars': 'E:\\web\\project\\archiv\\carscost',
      'apartments': 'E:\\web\\project\\archiv\\housingcost',
      'apartments-annex': 'E:\\web\\project\\archiv\\apartments_annex',
      'temp-workers': 'E:\\web\\project\\archiv\\3amala'
    };

    const folderPath = archivePaths[type];
    if (!folderPath) {
      return NextResponse.json({
        success: false,
        error: 'نوع تكلفة غير صحيح'
      }, { status: 400 });
    }

    const filePath = path.join(folderPath, fileName);

    // التحقق من وجود الملف
    if (!fs.existsSync(filePath)) {
      return NextResponse.json({
        success: false,
        error: 'الملف غير موجود'
      }, { status: 404 });
    }

    // قراءة الملف
    const fileBuffer = fs.readFileSync(filePath);

    // إرجاع الملف مع headers صحيحة
    const response = new NextResponse(fileBuffer);
    response.headers.set('Content-Type', 'application/pdf');
    response.headers.set('Content-Disposition', `inline; filename="${fileName}"`);
    response.headers.set('Cache-Control', 'public, max-age=3600'); // cache لمدة ساعة

    return response;

  } catch (error) {
    console.error('❌ خطأ في عرض الملف:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في عرض الملف: ' + error.message
    }, { status: 500 });
  }
}
