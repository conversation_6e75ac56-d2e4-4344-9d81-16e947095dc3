'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';

export default function CarsData() {
  const [cars, setCars] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadCarsData();
  }, []);

  const loadCarsData = async () => {
    try {
      // Simulate API call - replace with actual API
      setTimeout(() => {
        setCars([
          {
            id: 1,
            plateNumber: 'أ ب ج 123',
            model: 'تويوتا كامري',
            year: 2020,
            status: 'مؤجرة',
            renterName: 'أحمد محمد',
            monthlyRent: 2500,
            lastMaintenance: '2024-01-15'
          },
          {
            id: 2,
            plateNumber: 'د هـ و 456',
            model: 'نيسان التيما',
            year: 2019,
            status: 'متاحة',
            renterName: '',
            monthlyRent: 2200,
            lastMaintenance: '2024-02-10'
          },
          {
            id: 3,
            plateNumber: 'ز ح ط 789',
            model: 'هيونداي إلنترا',
            year: 2021,
            status: 'صيانة',
            renterName: '',
            monthlyRent: 2300,
            lastMaintenance: '2024-01-20'
          }
        ]);
        setLoading(false);
      }, 1000);
    } catch (error) {

      setLoading(false);
    }
  };

  const filteredCars = cars.filter(car =>
    car.plateNumber.includes(searchTerm) ||
    car.model.includes(searchTerm) ||
    car.renterName.includes(searchTerm)
  );

  const getStatusColor = (status) => {
    switch (status) {
      case 'مؤجرة':
        return 'bg-green-100 text-green-800';
      case 'متاحة':
        return 'bg-blue-100 text-blue-800';
      case 'صيانة':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <span className="text-3xl">🚗</span>
              <div>
                <h1 className="text-3xl font-bold text-gray-800">عرض بيانات السيارات</h1>
                <p className="text-gray-600">إدارة ومتابعة جميع السيارات المؤجرة</p>
              </div>
            </div>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              إضافة سيارة جديدة
            </button>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                البحث في السيارات
              </label>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="ابحث برقم اللوحة، الموديل، أو اسم المستأجر..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <button className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors">
              تصدير إلى Excel
            </button>
          </div>
        </div>

        {/* Cars Table */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-800">
              قائمة السيارات ({filteredCars.length})
            </h3>
          </div>

          {loading ? (
            <div className="p-12 text-center">
              <div className="animate-spin text-4xl mb-4">⏳</div>
              <p className="text-gray-600">جاري تحميل بيانات السيارات...</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full divide-y divide-gray-200 table-auto table-no-truncate">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      رقم اللوحة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الموديل
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      السنة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الحالة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      المستأجر
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الإيجار الشهري
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      آخر صيانة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredCars.map((car) => (
                    <tr key={car.id} className="hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {car.plateNumber}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {car.model}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {car.year}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(car.status)}`}>
                          {car.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {car.renterName || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatCurrency(car.monthlyRent)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {car.lastMaintenance}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex gap-2">
                          <button className="text-blue-600 hover:text-blue-900">
                            عرض
                          </button>
                          <button className="text-green-600 hover:text-green-900">
                            تعديل
                          </button>
                          <button className="text-red-600 hover:text-red-900">
                            حذف
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {!loading && filteredCars.length === 0 && (
            <div className="p-12 text-center">
              <span className="text-6xl mb-4 block">🚗</span>
              <h3 className="text-xl font-medium text-gray-600 mb-2">لا توجد سيارات</h3>
              <p className="text-gray-500">لم يتم العثور على سيارات تطابق البحث</p>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
