'use client';

import React, { useState, useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { useLanguage } from '@/contexts/LanguageContext';
import MainLayout from '@/components/MainLayout';
import {
  BarChart3,
  FileText,
  Download,
  Calendar,
  Users,
  Clock,
  DollarSign,
  Building,
  Car,
  UserCheck,
  TrendingUp,
  Filter,
  Search,
  Eye,
  Printer,
  Share2,
  RefreshCw
} from 'lucide-react';

export default function ReportsDashboard() {
  const { isDarkMode } = useTheme();
  const { isRTL, isArabic } = useLanguage();

  const [loading, setLoading] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [reportStats, setReportStats] = useState({
    totalReports: 0,
    generatedToday: 0,
    pendingReports: 0,
    lastGenerated: null
  });

  // أنواع التقارير المتاحة
  const reportCategories = [
    {
      id: 'employees',
      title: 'تقارير الموظفين',
      description: 'تقارير شاملة عن بيانات الموظفين والإحصائيات',
      icon: Users,
      color: 'blue',
      count: 8,
      reports: [
        { name: 'قائمة الموظفين الشاملة', link: '/reports/employees/complete-list' },
        { name: 'تقرير الموظفين حسب القسم', link: '/reports/employees/by-department' },
        { name: 'تقرير الموظفين الجدد', link: '/reports/employees/new-hires' },
        { name: 'تقرير المغادرين', link: '/reports/employees/departures' },
        { name: 'تقرير التأمينات', link: '/reports/employees/insurance' },
        { name: 'تقرير السكن', link: '/reports/employees/housing' },
        { name: 'تقرير المؤهلات', link: '/reports/employees/qualifications' },
        { name: 'إحصائيات الموظفين', link: '/reports/employees/statistics' }
      ]
    },
    {
      id: 'attendance',
      title: 'تقارير الحضور والغياب',
      description: 'تقارير الحضور اليومي والشهري والمؤثرات',
      icon: Clock,
      color: 'green',
      count: 6,
      reports: [
        { name: 'تقرير الحضور اليومي', link: '/reports/attendance/daily' },
        { name: 'تقرير الحضور الشهري', link: '/reports/attendance/monthly' },
        { name: 'تقرير المؤثرات الشهرية', link: '/reports/attendance/monthly-effects' },
        { name: 'تقرير التأخير والانصراف', link: '/reports/attendance/delays' },
        { name: 'تقرير الغياب', link: '/reports/attendance/absences' },
        { name: 'إحصائيات الحضور', link: '/reports/attendance/statistics' }
      ]
    },
    {
      id: 'leaves',
      title: 'تقارير الإجازات',
      description: 'تقارير الإجازات وأرصدة الموظفين',
      icon: Calendar,
      color: 'purple',
      count: 5,
      reports: [
        { name: 'تقرير طلبات الإجازات', link: '/reports/leaves' },
        { name: 'تقرير أرصدة الإجازات', link: '/reports/leaves' },
        { name: 'تقرير الإجازات المعتمدة', link: '/reports/leaves' },
        { name: 'تقرير الإجازات المرفوضة', link: '/reports/leaves' },
        { name: 'إحصائيات الإجازات', link: '/reports/leaves' }
      ]
    },
    {
      id: 'costs',
      title: 'تقارير التكاليف',
      description: 'تقارير التكاليف الشهرية والسنوية',
      icon: DollarSign,
      color: 'red',
      count: 4,
      reports: [
        { name: 'تقرير تكاليف السيارات', link: '/reports/costs' },
        { name: 'تقرير تكاليف الشقق', link: '/reports/costs' },
        { name: 'تقرير تكاليف العمالة المؤقتة', link: '/reports/costs' },
        { name: 'تقرير إجمالي التكاليف', link: '/reports/costs' }
      ]
    },
    {
      id: 'assets',
      title: 'تقارير الأصول',
      description: 'تقارير السيارات والشقق والممتلكات',
      icon: Building,
      color: 'indigo',
      count: 6,
      reports: [
        { name: 'تقرير السيارات المؤجرة', link: '/reports/assets' },
        { name: 'تقرير الشقق المؤجرة', link: '/reports/assets' },
        { name: 'تقرير العقود المنتهية', link: '/reports/assets' },
        { name: 'تقرير العقود المجددة', link: '/reports/assets' },
        { name: 'تقرير الصيانة', link: '/reports/assets' },
        { name: 'إحصائيات الأصول', link: '/reports/assets' }
      ]
    },
    {
      id: 'temp-workers',
      title: 'تقارير العمالة المؤقتة',
      description: 'تقارير العمالة المؤقتة والتكاليف',
      icon: UserCheck,
      color: 'orange',
      count: 5,
      reports: [
        { name: 'تقرير العمالة المؤقتة', link: '/reports/temp-workers' },
        { name: 'تقرير حضور العمالة', link: '/reports/temp-workers' },
        { name: 'تقرير المدفوعات', link: '/reports/temp-workers' },
        { name: 'تقرير الأداء', link: '/reports/temp-workers' },
        { name: 'إحصائيات العمالة المؤقتة', link: '/reports/temp-workers' }
      ]
    }
  ];

  // تحميل إحصائيات التقارير
  useEffect(() => {
    loadReportStats();
  }, []);

  const loadReportStats = async () => {
    setLoading(true);
    try {
      // محاكاة تحميل الإحصائيات
      setTimeout(() => {
        setReportStats({
          totalReports: 33,
          generatedToday: 12,
          pendingReports: 3,
          lastGenerated: new Date().toLocaleString('ar-EG')
        });
        setLoading(false);
      }, 1000);
    } catch (error) {

      setLoading(false);
    }
  };

  // تصدير جميع التقارير
  const exportAllReports = () => {
    // سيتم تنفيذه لاحقاً
    alert('جاري تطوير ميزة تصدير جميع التقارير...');
  };

  return (
    <MainLayout>
      <div className={`p-6 ${isDarkMode ? 'bg-[#0f172a] text-white' : 'bg-gray-50 text-gray-900'}`}>
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm p-6 mb-6`}>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <BarChart3 className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-2xl font-bold">مركز التقارير</h1>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  مركز شامل لجميع التقارير والإحصائيات
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <button
                onClick={exportAllReports}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <Download className="h-4 w-4" />
                تصدير جميع التقارير
              </button>

              <button
                onClick={loadReportStats}
                disabled={loading}
                className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-colors ${
                  isDarkMode
                    ? 'bg-gray-700 hover:bg-gray-600 text-white'
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                } disabled:opacity-50`}
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                تحديث
              </button>
            </div>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm p-6`}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} truncate`}>
                    إجمالي التقارير
                  </dt>
                  <dd className={`text-lg font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {reportStats.totalReports}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm p-6`}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUp className="h-8 w-8 text-green-600" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} truncate`}>
                    تم إنشاؤها اليوم
                  </dt>
                  <dd className={`text-lg font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {reportStats.generatedToday}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm p-6`}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-8 w-8 text-orange-600" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} truncate`}>
                    قيد الإنشاء
                  </dt>
                  <dd className={`text-lg font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {reportStats.pendingReports}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm p-6`}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Calendar className="h-8 w-8 text-purple-600" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className={`text-sm font-medium ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} truncate`}>
                    آخر تحديث
                  </dt>
                  <dd className={`text-xs font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {reportStats.lastGenerated || 'لم يتم التحديث'}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        {/* فئات التقارير */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {reportCategories.map((category) => {
            const IconComponent = category.icon;
            return (
              <div key={category.id} className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm overflow-hidden`}>
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className={`w-12 h-12 rounded-lg bg-${category.color}-100 dark:bg-${category.color}-900 flex items-center justify-center`}>
                        <IconComponent className={`h-6 w-6 text-${category.color}-600 dark:text-${category.color}-400`} />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold">{category.title}</h3>
                        <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                          {category.description}
                        </p>
                      </div>
                    </div>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium bg-${category.color}-100 text-${category.color}-800 dark:bg-${category.color}-900 dark:text-${category.color}-200`}>
                      {category.count} تقارير
                    </span>
                  </div>

                  <div className="space-y-2">
                    {category.reports.slice(0, 4).map((report, index) => (
                      <a
                        key={index}
                        href={report.link}
                        className={`block p-3 rounded-lg transition-colors ${
                          isDarkMode
                            ? 'hover:bg-gray-800 text-gray-300 hover:text-white'
                            : 'hover:bg-gray-50 text-gray-700 hover:text-gray-900'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">{report.name}</span>
                          <div className="flex items-center gap-2">
                            <Eye className="h-4 w-4" />
                            <Download className="h-4 w-4" />
                          </div>
                        </div>
                      </a>
                    ))}

                    {category.reports.length > 4 && (
                      <a
                        href={`/reports/${category.id}`}
                        className={`block p-3 rounded-lg text-center transition-colors ${
                          isDarkMode
                            ? 'bg-gray-800 text-blue-400 hover:bg-gray-700'
                            : 'bg-gray-50 text-blue-600 hover:bg-gray-100'
                        }`}
                      >
                        <span className="text-sm font-medium">
                          عرض جميع التقارير ({category.count})
                        </span>
                      </a>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </MainLayout>
  );
}
