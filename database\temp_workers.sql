-- إنشاء جدول العمالة المؤقتة
-- Temporary Workers Table Creation

-- حذف الجدول إذا كان موجوداً
IF EXISTS (SELECT * FROM sysobjects WHERE name='TempWorkers' AND xtype='U')
BEGIN
    DROP TABLE TempWorkers
END
GO

-- إنشاء جدول العمالة المؤقتة
CREATE TABLE TempWorkers (
    ID INT IDENTITY(1,1) PRIMARY KEY,
    WorkerCode NVARCHAR(20) NOT NULL UNIQUE,
    WorkerName NVARCHAR(100) NOT NULL,
    JobTitle NVARCHAR(100) NOT NULL,
    WorkerType NVARCHAR(20) NOT NULL CHECK (WorkerType IN ('خدمي', 'إنتاجي')),
    FirstWorkDay DATE NOT NULL,
    DailyRate DECIMAL(10,2) NOT NULL,
    NationalIdImagePath NVARCHAR(500),
    Notes NVARCHAR(MAX),
    IsActive BIT DEFAULT 1,
    CreatedAt DATETIME DEFAULT GETDATE(),
    UpdatedAt DATETIME DEFAULT GETDATE(),
    CreatedBy NVARCHAR(100),
    UpdatedBy NVARCHAR(100)
)
GO

-- إنشاء الفهارس
CREATE INDEX IX_TempWorkers_WorkerCode ON TempWorkers(WorkerCode)
GO

CREATE INDEX IX_TempWorkers_WorkerName ON TempWorkers(WorkerName)
GO

CREATE INDEX IX_TempWorkers_WorkerType ON TempWorkers(WorkerType)
GO

CREATE INDEX IX_TempWorkers_FirstWorkDay ON TempWorkers(FirstWorkDay)
GO

CREATE INDEX IX_TempWorkers_IsActive ON TempWorkers(IsActive)
GO

-- إدراج بيانات تجريبية
INSERT INTO TempWorkers (WorkerCode, WorkerName, JobTitle, WorkerType, FirstWorkDay, DailyRate, Notes)
VALUES 
-- عمالة خدمية
('TW001', 'أحمد محمد علي', 'عامل نظافة', 'خدمي', '2024-01-15', 120.00, 'عامل نظافة مكاتب إدارية'),
('TW002', 'محمد أحمد حسن', 'حارس أمن', 'خدمي', '2024-01-20', 150.00, 'حارس أمن ليلي'),
('TW003', 'فاطمة علي محمد', 'عاملة نظافة', 'خدمي', '2024-02-01', 110.00, 'تنظيف المرافق العامة'),
('TW004', 'عبد الله سعد', 'سائق', 'خدمي', '2024-02-10', 180.00, 'سائق نقل موظفين'),
('TW005', 'مريم حسن', 'مضيفة استقبال', 'خدمي', '2024-02-15', 140.00, 'استقبال وتوجيه الزوار'),

-- عمالة إنتاجية
('TW006', 'خالد محمود', 'فني كهرباء', 'إنتاجي', '2024-01-10', 250.00, 'صيانة الأنظمة الكهربائية'),
('TW007', 'سامي عبد الرحمن', 'فني سباكة', 'إنتاجي', '2024-01-25', 220.00, 'صيانة شبكات المياه'),
('TW008', 'أمين طارق', 'فني تكييف', 'إنتاجي', '2024-02-05', 280.00, 'صيانة أنظمة التكييف'),
('TW009', 'حسام الدين', 'نجار', 'إنتاجي', '2024-02-12', 200.00, 'أعمال النجارة والتشطيبات'),
('TW010', 'ياسر محمد', 'دهان', 'إنتاجي', '2024-02-20', 190.00, 'أعمال الدهان والتشطيبات'),

-- عمالة إضافية
('TW011', 'نادية أحمد', 'عاملة مطبخ', 'خدمي', '2024-03-01', 130.00, 'إعداد وتقديم المشروبات'),
('TW012', 'محمود سليم', 'عامل حديقة', 'خدمي', '2024-03-05', 125.00, 'صيانة المساحات الخضراء'),
('TW013', 'علي حسين', 'فني كمبيوتر', 'إنتاجي', '2024-03-10', 300.00, 'صيانة الأجهزة والشبكات'),
('TW014', 'سعاد محمد', 'عاملة تنظيف', 'خدمي', '2024-03-15', 115.00, 'تنظيف المكاتب والممرات'),
('TW015', 'طارق عبد الله', 'فني ميكانيكا', 'إنتاجي', '2024-03-20', 270.00, 'صيانة المعدات الميكانيكية')
GO

-- إنشاء view لإحصائيات العمالة المؤقتة
CREATE VIEW TempWorkersStats AS
SELECT 
    COUNT(*) as TotalWorkers,
    COUNT(CASE WHEN WorkerType = 'خدمي' THEN 1 END) as ServiceWorkers,
    COUNT(CASE WHEN WorkerType = 'إنتاجي' THEN 1 END) as ProductionWorkers,
    COUNT(CASE WHEN IsActive = 1 THEN 1 END) as ActiveWorkers,
    COUNT(CASE WHEN IsActive = 0 THEN 1 END) as InactiveWorkers,
    AVG(DailyRate) as AverageDailyRate,
    SUM(DailyRate) as TotalDailyRates,
    MIN(FirstWorkDay) as EarliestStartDate,
    MAX(FirstWorkDay) as LatestStartDate
FROM TempWorkers
GO

-- إنشاء view لإحصائيات حسب النوع
CREATE VIEW TempWorkersByType AS
SELECT 
    WorkerType,
    COUNT(*) as WorkerCount,
    AVG(DailyRate) as AverageDailyRate,
    MIN(DailyRate) as MinDailyRate,
    MAX(DailyRate) as MaxDailyRate,
    SUM(DailyRate) as TotalDailyRates
FROM TempWorkers
WHERE IsActive = 1
GROUP BY WorkerType
GO

-- إنشاء stored procedure لجلب العمالة المؤقتة
CREATE PROCEDURE GetTempWorkers
    @WorkerType NVARCHAR(20) = NULL,
    @IsActive BIT = NULL,
    @SearchTerm NVARCHAR(100) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        ID,
        WorkerCode,
        WorkerName,
        JobTitle,
        WorkerType,
        FirstWorkDay,
        DailyRate,
        NationalIdImagePath,
        Notes,
        IsActive,
        CreatedAt,
        UpdatedAt
    FROM TempWorkers
    WHERE (@WorkerType IS NULL OR WorkerType = @WorkerType)
        AND (@IsActive IS NULL OR IsActive = @IsActive)
        AND (@SearchTerm IS NULL OR 
             WorkerName LIKE '%' + @SearchTerm + '%' OR 
             WorkerCode LIKE '%' + @SearchTerm + '%' OR 
             JobTitle LIKE '%' + @SearchTerm + '%')
    ORDER BY CreatedAt DESC
END
GO

-- إنشاء stored procedure لإضافة عامل مؤقت
CREATE PROCEDURE AddTempWorker
    @WorkerCode NVARCHAR(20),
    @WorkerName NVARCHAR(100),
    @JobTitle NVARCHAR(100),
    @WorkerType NVARCHAR(20),
    @FirstWorkDay DATE,
    @DailyRate DECIMAL(10,2),
    @NationalIdImagePath NVARCHAR(500) = NULL,
    @Notes NVARCHAR(MAX) = NULL,
    @CreatedBy NVARCHAR(100) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    -- التحقق من عدم تكرار الكود
    IF EXISTS (SELECT 1 FROM TempWorkers WHERE WorkerCode = @WorkerCode)
    BEGIN
        RAISERROR('كود العامل موجود مسبقاً', 16, 1)
        RETURN
    END
    
    INSERT INTO TempWorkers 
    (WorkerCode, WorkerName, JobTitle, WorkerType, FirstWorkDay, DailyRate, 
     NationalIdImagePath, Notes, CreatedBy)
    VALUES 
    (@WorkerCode, @WorkerName, @JobTitle, @WorkerType, @FirstWorkDay, @DailyRate,
     @NationalIdImagePath, @Notes, @CreatedBy)
     
    SELECT SCOPE_IDENTITY() as NewWorkerID
END
GO

-- إنشاء stored procedure لتحديث عامل مؤقت
CREATE PROCEDURE UpdateTempWorker
    @ID INT,
    @WorkerCode NVARCHAR(20),
    @WorkerName NVARCHAR(100),
    @JobTitle NVARCHAR(100),
    @WorkerType NVARCHAR(20),
    @FirstWorkDay DATE,
    @DailyRate DECIMAL(10,2),
    @NationalIdImagePath NVARCHAR(500) = NULL,
    @Notes NVARCHAR(MAX) = NULL,
    @IsActive BIT = NULL,
    @UpdatedBy NVARCHAR(100) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    -- التحقق من وجود العامل
    IF NOT EXISTS (SELECT 1 FROM TempWorkers WHERE ID = @ID)
    BEGIN
        RAISERROR('العامل غير موجود', 16, 1)
        RETURN
    END
    
    -- التحقق من عدم تكرار الكود مع عامل آخر
    IF EXISTS (SELECT 1 FROM TempWorkers WHERE WorkerCode = @WorkerCode AND ID != @ID)
    BEGIN
        RAISERROR('كود العامل موجود مسبقاً', 16, 1)
        RETURN
    END
    
    UPDATE TempWorkers 
    SET 
        WorkerCode = @WorkerCode,
        WorkerName = @WorkerName,
        JobTitle = @JobTitle,
        WorkerType = @WorkerType,
        FirstWorkDay = @FirstWorkDay,
        DailyRate = @DailyRate,
        NationalIdImagePath = ISNULL(@NationalIdImagePath, NationalIdImagePath),
        Notes = @Notes,
        IsActive = ISNULL(@IsActive, IsActive),
        UpdatedAt = GETDATE(),
        UpdatedBy = @UpdatedBy
    WHERE ID = @ID
END
GO

-- إنشاء stored procedure لحذف عامل مؤقت
CREATE PROCEDURE DeleteTempWorker
    @ID INT,
    @UpdatedBy NVARCHAR(100) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    -- التحقق من وجود العامل
    IF NOT EXISTS (SELECT 1 FROM TempWorkers WHERE ID = @ID)
    BEGIN
        RAISERROR('العامل غير موجود', 16, 1)
        RETURN
    END
    
    -- حذف العامل (يمكن تغييرها إلى تعطيل بدلاً من الحذف)
    DELETE FROM TempWorkers WHERE ID = @ID
    
    -- أو تعطيل العامل بدلاً من الحذف
    -- UPDATE TempWorkers SET IsActive = 0, UpdatedAt = GETDATE(), UpdatedBy = @UpdatedBy WHERE ID = @ID
END
GO

-- إنشاء stored procedure للحصول على إحصائيات العمالة
CREATE PROCEDURE GetTempWorkersStatistics
AS
BEGIN
    SET NOCOUNT ON;
    
    -- إحصائيات عامة
    SELECT * FROM TempWorkersStats
    
    -- إحصائيات حسب النوع
    SELECT * FROM TempWorkersByType
    
    -- إحصائيات شهرية (العمال الجدد)
    SELECT 
        YEAR(FirstWorkDay) as Year,
        MONTH(FirstWorkDay) as Month,
        DATENAME(MONTH, FirstWorkDay) as MonthName,
        COUNT(*) as NewWorkersCount,
        AVG(DailyRate) as AverageDailyRate
    FROM TempWorkers
    WHERE FirstWorkDay >= DATEADD(MONTH, -12, GETDATE())
    GROUP BY YEAR(FirstWorkDay), MONTH(FirstWorkDay), DATENAME(MONTH, FirstWorkDay)
    ORDER BY Year DESC, Month DESC
END
GO

-- منح الصلاحيات
GRANT SELECT, INSERT, UPDATE, DELETE ON TempWorkers TO [public]
GO

GRANT SELECT ON TempWorkersStats TO [public]
GO

GRANT SELECT ON TempWorkersByType TO [public]
GO

GRANT EXECUTE ON GetTempWorkers TO [public]
GO

GRANT EXECUTE ON AddTempWorker TO [public]
GO

GRANT EXECUTE ON UpdateTempWorker TO [public]
GO

GRANT EXECUTE ON DeleteTempWorker TO [public]
GO

GRANT EXECUTE ON GetTempWorkersStatistics TO [public]
GO

PRINT 'تم إنشاء جدول العمالة المؤقتة وجميع العناصر المرتبطة به بنجاح'
PRINT 'Temporary Workers table and related objects created successfully'
