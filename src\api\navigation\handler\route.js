async function handler({ userId, role, language = 'en' }) {
  const session = getSession();
  if (!session?.user?.id) {
    return {
      error: 'Unauthorized',
      status: 401,
    };
  }

  const userPermissions = await sql`
    SELECT p.code 
    FROM permissions p
    JOIN role_permissions rp ON p.id = rp.permission_id
    JOIN users u ON u.role_id = rp.role_id
    WHERE u.id = ${session.user.id}
  `;

  const permissionCodes = userPermissions.map((p) => p.code);

  const baseRoutes = [
    {
      id: 'dashboard',
      path: '/dashboard',
      nameEn: 'Dashboard',
      nameAr: 'لوحة التحكم',
      icon: 'dashboard',
      permission: 'view_dashboard',
    },
    {
      id: 'employee',
      path: '/employee-management',
      nameEn: 'Employee Management',
      nameAr: 'إدارة الموظفين',
      icon: 'users',
      permission: 'view_employees',
      children: [
        {
          id: 'employee-list',
          path: '/employee-management/list',
          nameEn: 'Employee List',
          nameAr: 'قائمة الموظفين',
          permission: 'view_employees',
        },
        {
          id: 'employee-documents',
          path: '/employee-management/documents',
          nameEn: 'Documents',
          nameAr: 'المستندات',
          permission: 'view_documents',
        },
      ],
    },
    {
      id: 'attendance',
      path: '/attendance',
      nameEn: 'Attendance',
      nameAr: 'الحضور',
      icon: 'calendar',
      permission: 'view_attendance',
      children: [
        {
          id: 'daily-attendance',
          path: '/attendance/daily',
          nameEn: 'Daily Attendance',
          nameAr: 'الحضور اليومي',
          permission: 'view_daily_attendance',
        },
        {
          id: 'monthly-report',
          path: '/attendance/monthly',
          nameEn: 'Monthly Report',
          nameAr: 'التقرير الشهري',
          permission: 'view_monthly_report',
        },
      ],
    },
    {
      id: 'assets',
      path: '/assets',
      nameEn: 'Asset Management',
      nameAr: 'إدارة الأصول',
      icon: 'building',
      permission: 'view_assets',
      children: [
        {
          id: 'apartments',
          path: '/assets/apartments',
          nameEn: 'Apartments',
          nameAr: 'الشقق',
          permission: 'view_apartments',
        },
        {
          id: 'cars',
          path: '/assets/cars',
          nameEn: 'Cars',
          nameAr: 'السيارات',
          permission: 'view_cars',
        },
      ],
    },
    {
      id: 'reports',
      path: '/reports',
      nameEn: 'Reports',
      nameAr: 'التقارير',
      icon: 'file',
      permission: 'view_reports',
    },
  ];

  const filterRoutesByPermission = (routes) => {
    return routes.filter((route) => {
      const hasPermission =
        !route.permission || permissionCodes.includes(route.permission);

      if (hasPermission && route.children) {
        route.children = filterRoutesByPermission(route.children);
        return route.children.length > 0;
      }

      return hasPermission;
    });
  };

  const accessibleRoutes = filterRoutesByPermission(baseRoutes);

  return {
    routes: accessibleRoutes,
    language,
    userId: session.user.id,
  };
}
