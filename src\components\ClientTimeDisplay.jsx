'use client';

import { useState, useEffect } from 'react';

export default function ClientTimeDisplay({ time, className = '' }) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <span className={className}>--:--:--</span>;
  }

  return (
    <span className={className}>
      {time ? time.toLocaleTimeString('ar-EG') : '--:--:--'}
    </span>
  );
}
