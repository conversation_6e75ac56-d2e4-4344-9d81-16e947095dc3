// سكريبت بناء النسخة المحمية
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const LicenseSystem = require('./license-system');

class ProtectedBuilder {
  constructor() {
    this.licenseSystem = new LicenseSystem();
    this.buildDir = path.join(__dirname, '../build-protected');
    this.sourceDir = path.join(__dirname, '../src');
  }

  // إنشاء نسخة محمية للبيع
  async buildProtectedVersion(customerInfo) {
    console.log('🔐 بدء إنشاء النسخة المحمية...');

    try {
      // 1. تنظيف مجلد البناء
      this.cleanBuildDirectory();

      // 2. نسخ الملفات الأساسية
      this.copyEssentialFiles();

      // 3. تشويش الكود
      this.obfuscateSourceCode();

      // 4. إنشاء ترخيص العميل
      this.generateCustomerLicense(customerInfo);

      // 5. إضافة نظام الحماية
      this.addProtectionSystem();

      // 6. بناء المشروع
      this.buildProject();

      // 7. إنشاء حزمة التوزيع
      this.createDistributionPackage(customerInfo);

      console.log('✅ تم إنشاء النسخة المحمية بنجاح!');

    } catch (error) {
      console.error('❌ خطأ في إنشاء النسخة المحمية:', error.message);
      throw error;
    }
  }

  // تنظيف مجلد البناء
  cleanBuildDirectory() {
    console.log('🧹 تنظيف مجلد البناء...');
    if (fs.existsSync(this.buildDir)) {
      fs.rmSync(this.buildDir, { recursive: true, force: true });
    }
    fs.mkdirSync(this.buildDir, { recursive: true });
  }

  // نسخ الملفات الأساسية
  copyEssentialFiles() {
    console.log('📁 نسخ الملفات الأساسية...');

    const filesToCopy = [
      'package.json',
      'next.config.js',
      '.env.example',
      'public',
      'styles'
    ];

    for (const file of filesToCopy) {
      const sourcePath = path.join(__dirname, '..', file);
      const destPath = path.join(this.buildDir, file);

      if (fs.existsSync(sourcePath)) {
        if (fs.statSync(sourcePath).isDirectory()) {
          this.copyDirectory(sourcePath, destPath);
        } else {
          fs.copyFileSync(sourcePath, destPath);
        }
      }
    }
  }

  // نسخ مجلد
  copyDirectory(source, destination) {
    if (!fs.existsSync(destination)) {
      fs.mkdirSync(destination, { recursive: true });
    }

    const files = fs.readdirSync(source);

    for (const file of files) {
      const sourcePath = path.join(source, file);
      const destPath = path.join(destination, file);

      if (fs.statSync(sourcePath).isDirectory()) {
        this.copyDirectory(sourcePath, destPath);
      } else {
        fs.copyFileSync(sourcePath, destPath);
      }
    }
  }

  // تشويش كود المصدر
  obfuscateSourceCode() {
    console.log('🔒 تشويش كود المصدر...');
    const protectedSrcDir = path.join(this.buildDir, 'src');
    this.obfuscator.obfuscateDirectory(this.sourceDir, protectedSrcDir);
  }

  // إنشاء ترخيص العميل
  generateCustomerLicense(customerInfo) {
    console.log('📜 إنشاء ترخيص العميل...');
    const license = this.licenseSystem.generateLicense(customerInfo);
    const licensePath = path.join(this.buildDir, '.license');
    fs.writeFileSync(licensePath, JSON.stringify(license, null, 2));
  }

  // إضافة نظام الحماية
  addProtectionSystem() {
    console.log('🛡️ إضافة نظام الحماية...');

    // نسخ ملفات الحماية
    const protectionDir = path.join(this.buildDir, 'protection');
    fs.mkdirSync(protectionDir, { recursive: true });

    fs.copyFileSync(
      path.join(__dirname, 'license-system.js'),
      path.join(protectionDir, 'license-system.js')
    );

    // إضافة middleware للتحقق من الترخيص
    const middlewarePath = path.join(this.buildDir, 'src', 'middleware.js');
    const middlewareCode = `
import { NextResponse } from 'next/server';
const LicenseSystem = require('./protection/license-system');

export function middleware(request) {
  // التحقق من الترخيص
  const licenseSystem = new LicenseSystem();
  const validation = licenseSystem.validateLicense();

  if (!validation.valid) {
    console.error('خطأ في الترخيص:', validation.error);
    return new NextResponse('ترخيص غير صالح', { status: 403 });
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/((?!api/health|_next/static|_next/image|favicon.ico).*)',
  ],
};
`;

    fs.writeFileSync(middlewarePath, middlewareCode);
  }

  // بناء المشروع
  buildProject() {
    console.log('🔨 بناء المشروع...');

    // تعديل package.json لإزالة scripts التطوير
    const packageJsonPath = path.join(this.buildDir, 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

    // إزالة scripts التطوير
    delete packageJson.scripts.dev;
    delete packageJson.devDependencies;

    // إضافة script للتشغيل فقط
    packageJson.scripts = {
      start: "next start",
      build: "next build"
    };

    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));

    // تثبيت التبعيات وبناء المشروع
    process.chdir(this.buildDir);
    execSync('npm install --production', { stdio: 'inherit' });
    execSync('npm run build', { stdio: 'inherit' });
  }

  // إنشاء حزمة التوزيع
  createDistributionPackage(customerInfo) {
    console.log('📦 إنشاء حزمة التوزيع...');

    const packageName = `cost-management-system-${customerInfo.id}-${Date.now()}`;
    const packageDir = path.join(__dirname, '../packages', packageName);

    // إنشاء مجلد الحزمة
    fs.mkdirSync(packageDir, { recursive: true });

    // نسخ الملفات المطلوبة للتشغيل
    const filesToPackage = [
      '.next',
      'public',
      'package.json',
      'next.config.js',
      '.license',
      'protection'
    ];

    for (const file of filesToPackage) {
      const sourcePath = path.join(this.buildDir, file);
      const destPath = path.join(packageDir, file);

      if (fs.existsSync(sourcePath)) {
        if (fs.statSync(sourcePath).isDirectory()) {
          this.copyDirectory(sourcePath, destPath);
        } else {
          fs.copyFileSync(sourcePath, destPath);
        }
      }
    }

    // إنشاء ملفات التشغيل للعميل
    this.createCustomerStartupFiles(packageDir, customerInfo);

    // إنشاء ملف معلومات العميل
    const customerInfoPath = path.join(packageDir, 'customer-info.json');
    fs.writeFileSync(customerInfoPath, JSON.stringify({
      customerId: customerInfo.id,
      customerName: customerInfo.name,
      email: customerInfo.email,
      purchaseDate: new Date().toISOString(),
      version: customerInfo.version || '1.0.0',
      features: customerInfo.features || ['all']
    }, null, 2));

    console.log(`📦 تم إنشاء الحزمة: ${packageDir}`);
    return packageDir;
  }

  // إنشاء ملفات التشغيل للعميل
  createCustomerStartupFiles(packageDir, customerInfo) {
    // ملف تشغيل Windows
    const startBatContent = `@echo off
title ${customerInfo.name} - Cost Management System
color 0A

echo ========================================
echo    Cost Management System
echo    Licensed to: ${customerInfo.name}
echo ========================================
echo.

echo Starting system...
npm start

pause`;

    fs.writeFileSync(path.join(packageDir, 'START.bat'), startBatContent);

    // ملف README للعميل
    const readmeContent = `# نظام إدارة التكاليف

## معلومات الترخيص
- العميل: ${customerInfo.name}
- البريد الإلكتروني: ${customerInfo.email}
- رقم الترخيص: ${customerInfo.id}

## التشغيل
انقر مرتين على START.bat لتشغيل النظام

## الدعم الفني
للدعم الفني تواصل معنا على: <EMAIL>

⚠️ تحذير: هذا النظام محمي بحقوق الطبع والنشر
`;

    fs.writeFileSync(path.join(packageDir, 'README.txt'), readmeContent);
  }
}

module.exports = ProtectedBuilder;
