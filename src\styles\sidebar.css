/* Sidebar Styles */
.sidebar {
  --sidebar-width: 16rem;
  --sidebar-collapsed-width: 4rem;
  --primary-color: #003366;
  --primary-dark: #002244;
  --primary-light: #004488;
  --text-primary: #333333;
  --text-secondary: #666666;
  --bg-hover: rgba(0, 51, 102, 0.1);
}

/* Animations */
@keyframes slideIn {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Custom Scrollbar */
.sidebar::-webkit-scrollbar,
.nav-scroll::-webkit-scrollbar {
  width: 4px;
}

.sidebar::-webkit-scrollbar-track,
.nav-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar::-webkit-scrollbar-thumb,
.nav-scroll::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 4px;
}

.sidebar::-webkit-scrollbar-thumb:hover,
.nav-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.8);
}

/* Dark mode scrollbar */
.dark .nav-scroll::-webkit-scrollbar-thumb {
  background: rgba(71, 85, 105, 0.5);
}

.dark .nav-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(71, 85, 105, 0.8);
}

/* Hover Effects */
.menu-item:hover {
  background: var(--bg-hover);
  transform: translateX(-4px);
  transition: all 0.3s ease;
}

.sub-menu-item {
  opacity: 0.8;
  transition: all 0.2s ease;
}

.sub-menu-item:hover {
  opacity: 1;
  transform: translateX(-2px);
}

/* Icon Animations */
.menu-icon {
  transition: transform 0.2s ease;
}

.menu-item:hover .menu-icon {
  transform: scale(1.1);
}

/* Collapse Button */
.collapse-button {
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.collapse-button:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}