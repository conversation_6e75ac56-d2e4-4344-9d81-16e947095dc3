import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';
import fs from 'fs';
import path from 'path';

export async function POST(request) {
  try {
    const formData = await request.formData();
    const file = formData.get('file');
    const costType = formData.get('costType');
    const month = parseInt(formData.get('month'));
    const year = parseInt(formData.get('year'));
    const isAttachment = formData.get('isAttachment') === 'true';

    if (!file || !costType || !month || !year) {
      return NextResponse.json({
        success: false,
        error: 'بيانات غير مكتملة'
      }, { status: 400 });
    }

    // التحقق من نوع الملف
    if (!file.name.toLowerCase().endsWith('.pdf')) {
      return NextResponse.json({
        success: false,
        error: 'يجب أن يكون الملف من نوع PDF'
      }, { status: 400 });
    }

    const pool = await getConnection();

    // تحديد مسار الحفظ حسب جدول ARCHIV
    const archiveFolders = {
      'carscost': 'carscost',
      'housingcost': 'housingcost',
      '3amala': '3amala'
    };

    const archivePaths = {};
    Object.keys(archiveFolders).forEach(key => {
      archivePaths[key] = path.join(process.cwd(), 'public', 'archiv', archiveFolders[key]);
    });

    if (!archivePaths[costType]) {
      return NextResponse.json({
        success: false,
        error: 'نوع التكلفة غير صحيح'
      }, { status: 400 });
    }

    // تحديد اسم الملف (مع إضافة "ملحق" للشقق إذا كان ملحق)
    const attachmentSuffix = (costType === 'housingcost' && isAttachment) ? '-ملحق' : '';
    const documentName = `${month}-${year}${attachmentSuffix}.pdf`;
    const documentPath = path.join(archivePaths[costType], documentName);

    // التأكد من وجود المجلد
    const archiveDir = archivePaths[costType];
    if (!fs.existsSync(archiveDir)) {
      fs.mkdirSync(archiveDir, { recursive: true });

    }

    // حفظ الملف
    try {
      const bytes = await file.arrayBuffer();
      const buffer = Buffer.from(bytes);
      
      fs.writeFileSync(documentPath, buffer);

    } catch (fileError) {

      return NextResponse.json({
        success: false,
        error: 'فشل في حفظ الملف: ' + fileError.message
      }, { status: 500 });
    }

    // تحديث قاعدة البيانات
    try {
      // البحث عن السجل الموجود
      const existingRecord = await pool.request()
        .input('costType', sql.NVarChar, costType)
        .input('month', sql.Int, month)
        .input('year', sql.Int, year)
        .query(`
          SELECT ID FROM MonthlyCosts 
          WHERE CostType = @costType AND Month = @month AND Year = @year
        `);

      if (existingRecord.recordset.length > 0) {
        // تحديث السجل الموجود
        await pool.request()
          .input('costType', sql.NVarChar, costType)
          .input('month', sql.Int, month)
          .input('year', sql.Int, year)
          .input('documentPath', sql.NVarChar, documentPath)
          .query(`
            UPDATE MonthlyCosts 
            SET HasDocument = 1, DocumentPath = @documentPath, UpdatedAt = GETDATE()
            WHERE CostType = @costType AND Month = @month AND Year = @year
          `);
      } else {
        // إنشاء سجل جديد
        await pool.request()
          .input('costType', sql.NVarChar, costType)
          .input('month', sql.Int, month)
          .input('year', sql.Int, year)
          .input('documentPath', sql.NVarChar, documentPath)
          .query(`
            INSERT INTO MonthlyCosts (CostType, Month, Year, TotalAmount, HasDocument, DocumentPath)
            VALUES (@costType, @month, @year, 0, 1, @documentPath)
          `);
      }

    } catch (dbError) {

      // حذف الملف المحفوظ في حالة فشل قاعدة البيانات
      try {
        fs.unlinkSync(documentPath);
      } catch (unlinkError) {

      }
      
      return NextResponse.json({
        success: false,
        error: 'فشل في تحديث قاعدة البيانات: ' + dbError.message
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: 'تم رفع وحفظ ملف طلب الإصدار بنجاح',
      data: { 
        documentPath, 
        documentName,
        fileSize: file.size,
        originalName: file.name
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  }
}
