'use client';

import { useState } from 'react';

export default function SetupDepartments() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);

  const createDepartments = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/setup-departments', {
        method: 'POST'
      });
      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({
        success: false,
        message: 'خطأ في الاتصال',
        error: error.message
      });
    } finally {
      setLoading(false);
    }
  };

  const checkDepartments = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/setup-departments');
      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({
        success: false,
        message: 'خطأ في الاتصال',
        error: error.message
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-2xl font-bold text-gray-800 mb-6">
            إعداد جدول الأقسام
          </h1>

          <div className="space-y-4">
            <button
              onClick={createDepartments}
              disabled={loading}
              className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'جاري الإنشاء...' : 'إنشاء جدول الأقسام'}
            </button>

            <button
              onClick={checkDepartments}
              disabled={loading}
              className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 disabled:opacity-50 ml-4"
            >
              {loading ? 'جاري التحقق...' : 'التحقق من الأقسام'}
            </button>
          </div>

          {result && (
            <div className={`mt-6 p-4 rounded-lg ${
              result.success ? 'bg-green-100 border border-green-400' : 'bg-red-100 border border-red-400'
            }`}>
              <h3 className={`font-bold ${result.success ? 'text-green-800' : 'text-red-800'}`}>
                {result.success ? 'نجح' : 'فشل'}
              </h3>
              <p className={result.success ? 'text-green-700' : 'text-red-700'}>
                {result.message}
              </p>
              
              {result.departmentsCount && (
                <p className="text-green-700 mt-2">
                  عدد الأقسام: {result.departmentsCount}
                </p>
              )}

              {result.departments && (
                <div className="mt-4">
                  <h4 className="font-bold text-green-800 mb-2">الأقسام الموجودة:</h4>
                  <div className="overflow-x-auto">
                    <table className="min-w-full bg-white border border-gray-300">
                      <thead>
                        <tr className="bg-gray-100">
                          <th className="border border-gray-300 px-4 py-2 text-right">الرقم</th>
                          <th className="border border-gray-300 px-4 py-2 text-right">اسم القسم</th>
                          <th className="border border-gray-300 px-4 py-2 text-right">كود القسم</th>
                          <th className="border border-gray-300 px-4 py-2 text-right">كود المدير</th>
                          <th className="border border-gray-300 px-4 py-2 text-right">المستوى</th>
                        </tr>
                      </thead>
                      <tbody>
                        {result.departments.map((dept) => (
                          <tr key={dept.ID}>
                            <td className="border border-gray-300 px-4 py-2">{dept.ID}</td>
                            <td className="border border-gray-300 px-4 py-2">{dept.DepartmentName}</td>
                            <td className="border border-gray-300 px-4 py-2">{dept.DepartmentCode}</td>
                            <td className="border border-gray-300 px-4 py-2">{dept.ManagerCode || '-'}</td>
                            <td className="border border-gray-300 px-4 py-2">{dept.Level}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}

              {result.error && (
                <pre className="mt-2 text-sm text-red-600 bg-red-50 p-2 rounded">
                  {result.error}
                </pre>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
