import { NextResponse } from 'next/server';
import { getConnection } from '@/utils/db';
import sql from 'mssql';

export async function PUT(httpRequest) {
  let pool;

  try {
    const { employeeId, employeeData } = await httpRequest.json();
    if (!employeeId || !employeeData) {
      return NextResponse.json({
        success: false,
        message: 'بيانات غير مكتملة'
      }, { status: 400 });
    }

    // الاتصال بقاعدة البيانات
    pool = await getConnection();
    // أولاً، جلب البيانات الحالية للموظف
    const currentDataResult = await pool.request()
      .input('CurrentEmployeeCode', sql.NVarChar, employeeId)
      .query(`
        SELECT
          EmployeeName, JobTitle, Department, direct, NationalID, BirthDate, Gender,
          Governorate, area, MaritalStatus, Mobile, email, emrnum, Kinship,
          Education, University, Major, Grade, Batch, IsResidentEmployee,
          CompanyHousing, codeHousing, TransportMethod, SocialInsurance,
          SocialInsureNum, spcialInsDate, MedicalInsurance, MedicalInsuranceNum,
          HireDate, JoinDate, CurrentStatus
        FROM Employees
        WHERE EmployeeCode = @CurrentEmployeeCode
      `);

    if (currentDataResult.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'لم يتم العثور على الموظف'
      }, { status: 404 });
    }

    const currentData = currentDataResult.recordset[0];
    // إنشاء استعلام التحديث الديناميكي (فقط للحقول المتغيرة)
    const updateFields = [];
    const request = pool.request();
    request.input('EmployeeID', sql.Int, parseInt(employeeId));

    // دالة مساعدة للمقارنة والتحديث
    const addFieldIfChanged = (fieldName, dbFieldName, newValue, currentValue, sqlType = sql.NVarChar) => {
      // تنظيف القيم للمقارنة
      const cleanNew = newValue === undefined || newValue === null || newValue === '' ? null : newValue;
      const cleanCurrent = currentValue === undefined || currentValue === null || currentValue === '' ? null : currentValue;

      if (cleanNew !== cleanCurrent) {
        updateFields.push(`${dbFieldName} = @${fieldName}`);
        request.input(fieldName, sqlType, cleanNew);
      }
    };

    // فحص وإضافة الحقول المتغيرة
    addFieldIfChanged('EmployeeName', 'EmployeeName', employeeData.EmployeeName || employeeData.FullName, currentData.EmployeeName);
    addFieldIfChanged('JobTitle', 'JobTitle', employeeData.JobTitle, currentData.JobTitle);
    addFieldIfChanged('Department', 'Department', employeeData.Department, currentData.Department);
    addFieldIfChanged('DirectManager', 'direct', employeeData.DirectManager || employeeData.direct, currentData.direct);
    addFieldIfChanged('NationalID', 'NationalID', employeeData.NationalID, currentData.NationalID);
    addFieldIfChanged('Gender', 'Gender', employeeData.Gender, currentData.Gender);
    addFieldIfChanged('Governorate', 'Governorate', employeeData.Governorate, currentData.Governorate);
    addFieldIfChanged('Area', 'area', employeeData.Area || employeeData.area, currentData.area);
    addFieldIfChanged('MaritalStatus', 'MaritalStatus', employeeData.MaritalStatus, currentData.MaritalStatus);
    addFieldIfChanged('Mobile', 'Mobile', employeeData.Mobile, currentData.Mobile);
    addFieldIfChanged('Email', 'email', employeeData.Email || employeeData.email, currentData.email);
    addFieldIfChanged('EmergencyNumber', 'emrnum', employeeData.EmergencyNumber || employeeData.emrnum, currentData.emrnum);
    addFieldIfChanged('Kinship', 'Kinship', employeeData.Kinship, currentData.Kinship);
    addFieldIfChanged('Education', 'Education', employeeData.Education, currentData.Education);
    addFieldIfChanged('University', 'University', employeeData.University, currentData.University);
    addFieldIfChanged('Major', 'Major', employeeData.Major, currentData.Major);
    addFieldIfChanged('Grade', 'Grade', employeeData.Grade, currentData.Grade);
    addFieldIfChanged('Batch', 'Batch', employeeData.Batch, currentData.Batch);
    addFieldIfChanged('IsResidentEmployee', 'IsResidentEmployee', employeeData.IsResidentEmployee, currentData.IsResidentEmployee);
    addFieldIfChanged('CompanyHousing', 'CompanyHousing', employeeData.CompanyHousing, currentData.CompanyHousing);
    addFieldIfChanged('codeHousing', 'codeHousing', employeeData.HousingCode || employeeData.codeHousing, currentData.codeHousing);
    addFieldIfChanged('TransportMethod', 'TransportMethod', employeeData.TransportMethod, currentData.TransportMethod);
    addFieldIfChanged('SocialInsurance', 'SocialInsurance', employeeData.SocialInsurance, currentData.SocialInsurance);
    addFieldIfChanged('SocialInsuranceNumber', 'SocialInsureNum', employeeData.SocialInsuranceNumber || employeeData.SocialInsureNum, currentData.SocialInsureNum);
    addFieldIfChanged('SocialInsuranceDate', 'spcialInsDate', employeeData.SocialInsuranceDate || employeeData.spcialInsDate, currentData.spcialInsDate);
    addFieldIfChanged('MedicalInsurance', 'MedicalInsurance', employeeData.MedicalInsurance, currentData.MedicalInsurance);
    addFieldIfChanged('MedicalInsuranceNumber', 'MedicalInsuranceNum', employeeData.MedicalInsuranceNumber || employeeData.MedicalInsuranceNum, currentData.MedicalInsuranceNum);

    // معالجة التواريخ بشكل خاص
    if (employeeData.BirthDate) {
      const newBirthDate = new Date(employeeData.BirthDate);
      const currentBirthDate = currentData.BirthDate ? new Date(currentData.BirthDate) : null;
      if (!currentBirthDate || newBirthDate.getTime() !== currentBirthDate.getTime()) {
        updateFields.push('BirthDate = @BirthDate');
        request.input('BirthDate', sql.Date, newBirthDate);
      }
    }

    if (employeeData.HireDate) {
      const newHireDate = new Date(employeeData.HireDate);
      const currentHireDate = currentData.HireDate ? new Date(currentData.HireDate) : null;
      if (!currentHireDate || newHireDate.getTime() !== currentHireDate.getTime()) {
        updateFields.push('HireDate = @HireDate');
        request.input('HireDate', sql.Date, newHireDate);
      }
    }

    if (employeeData.JoinDate) {
      const newJoinDate = new Date(employeeData.JoinDate);
      const currentJoinDate = currentData.JoinDate ? new Date(currentData.JoinDate) : null;
      if (!currentJoinDate || newJoinDate.getTime() !== currentJoinDate.getTime()) {
        updateFields.push('JoinDate = @JoinDate');
        request.input('JoinDate', sql.Date, newJoinDate);
      }
    }

    // إذا لم تكن هناك تغييرات، لا نحتاج للتحديث
    if (updateFields.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'لا توجد تغييرات للحفظ',
        updatedRows: 0
      });
    }

    // إنشاء استعلام التحديث
    const updateQuery = `
      UPDATE Employees
      SET ${updateFields.join(', ')}
      WHERE EmployeeCode = @EmployeeID
    `;
    // تنفيذ الاستعلام
    const result = await request.query(updateQuery);
    if (result.rowsAffected[0] > 0) {
      return NextResponse.json({
        success: true,
        message: 'تم تحديث بيانات الموظف بنجاح',
        updatedRows: result.rowsAffected[0]
      });
    } else {
      return NextResponse.json({
        success: false,
        message: 'لم يتم العثور على الموظف أو لم يتم تحديث أي بيانات'
      }, { status: 404 });
    }

  } catch (error) {
    // رسائل خطأ مخصصة
    let errorMessage = 'حدث خطأ في تحديث البيانات';

    if (error.code === 'ECONNREFUSED') {
      errorMessage = 'خطأ في الاتصال بقاعدة البيانات';
    } else if (error.code === 'ELOGIN') {
      errorMessage = 'خطأ في تسجيل الدخول لقاعدة البيانات';
    } else if (error.number === 207) {
      errorMessage = 'خطأ في أسماء الأعمدة في قاعدة البيانات';
    } else if (error.number === 2627) {
      errorMessage = 'قيمة مكررة في قاعدة البيانات';
    }

    return NextResponse.json({
      success: false,
      message: errorMessage,
      error: error.message,
      details: process.env.NODE_ENV === 'development' ? {
        code: error.code,
        state: error.state,
        number: error.number
      } : undefined
    }, { status: 500 });

  } finally {
    // لا نحتاج لإغلاق الاتصال لأن getConnection تستخدم connection pool
  }
}
