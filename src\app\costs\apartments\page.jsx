'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';

export default function ApartmentCostsPage() {
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [showAddCostModal, setShowAddCostModal] = useState(false);
  const [costs, setCosts] = useState([]);
  const [loading, setLoading] = useState(false);

  // رفع ملف طلب الإصدار
  const uploadVersionDocument = async (file) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('costType', 'housingcost');
      formData.append('month', selectedMonth);
      formData.append('year', selectedYear);

      const response = await fetch('/api/upload-version', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();
      if (result.success) {
        alert(`تم رفع ملف ${selectedMonth}-${selectedYear}.pdf بنجاح`);
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {
      alert('خطأ في رفع الملف: ' + error.message);
    }
  };

  // فتح نافذة اختيار الملف
  const openFileUpload = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.pdf';
    input.onchange = (e) => {
      const file = e.target.files[0];
      if (file) {
        if (file.type !== 'application/pdf') {
          alert('يجب اختيار ملف PDF فقط');
          return;
        }
        uploadVersionDocument(file);
      }
    };
    input.click();
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto">
        {/* رأس الصفحة */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="text-3xl text-purple-600">🏢</div>
              <div>
                <h1 className="text-3xl font-bold text-gray-800">تكاليف الشقق</h1>
                <p className="text-gray-600">إدارة ومتابعة تكاليف الشقق الشهرية</p>
              </div>
            </div>
            <div className="flex gap-3">
              <select
                value={selectedMonth}
                onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
              >
                {[
                  { value: 1, label: 'يناير' },
                  { value: 2, label: 'فبراير' },
                  { value: 3, label: 'مارس' },
                  { value: 4, label: 'أبريل' },
                  { value: 5, label: 'مايو' },
                  { value: 6, label: 'يونيو' },
                  { value: 7, label: 'يوليو' },
                  { value: 8, label: 'أغسطس' },
                  { value: 9, label: 'سبتمبر' },
                  { value: 10, label: 'أكتوبر' },
                  { value: 11, label: 'نوفمبر' },
                  { value: 12, label: 'ديسمبر' }
                ].map(month => (
                  <option key={month.value} value={month.value}>
                    {month.label}
                  </option>
                ))}
              </select>
              <select
                value={selectedYear}
                onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
              >
                {[2023, 2024, 2025, 2026].map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
              <button
                onClick={openFileUpload}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
              >
                📎 رفع طلب إصدار
              </button>
              <a
                href="/costs/version-requests?type=apartments"
                className="bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700 flex items-center gap-2"
              >
                📋 عرض طلبات الإصدار
              </a>
              <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                تصدير البيانات
              </button>
              <button
                onClick={() => setShowAddCostModal(true)}
                className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700"
              >
                إضافة تكلفة جديدة
              </button>
            </div>
          </div>
        </div>

        {/* الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-purple-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي التكاليف</p>
                <p className="text-3xl font-bold text-purple-600">8,500 ج.م</p>
              </div>
              <div className="text-2xl text-purple-600">💰</div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-blue-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إيجار الشقق</p>
                <p className="text-2xl font-bold text-blue-600">6,000 ج.م</p>
              </div>
              <div className="text-2xl text-blue-600">🏠</div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-green-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">فواتير الخدمات</p>
                <p className="text-2xl font-bold text-green-600">1,500 ج.م</p>
              </div>
              <div className="text-2xl text-green-600">⚡</div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-red-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">صيانة وإصلاحات</p>
                <p className="text-2xl font-bold text-red-600">1,000 ج.م</p>
              </div>
              <div className="text-2xl text-red-600">🔧</div>
            </div>
          </div>
        </div>

        {/* جدول التكاليف */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-800">تكاليف الشقق - يناير 2024</h3>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    كود الشقة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    العنوان
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    المالك
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    نوع التكلفة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    المبلغ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    التاريخ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                <tr className="hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    APT-001
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    شارع النيل، المعادي
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    أحمد محمد
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                      إيجار
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    3,000 ج.م
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    01/01/2024
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex gap-2">
                      <button className="text-blue-600 hover:text-blue-900" title="تعديل">
                        ✏️
                      </button>
                      <button className="text-red-600 hover:text-red-900" title="حذف">
                        🗑️
                      </button>
                    </div>
                  </td>
                </tr>
                <tr className="hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    APT-002
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    شارع التحرير، وسط البلد
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    فاطمة علي
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                      إيجار
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    3,000 ج.م
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    01/01/2024
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex gap-2">
                      <button className="text-blue-600 hover:text-blue-900" title="تعديل">
                        ✏️
                      </button>
                      <button className="text-red-600 hover:text-red-900" title="حذف">
                        🗑️
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
