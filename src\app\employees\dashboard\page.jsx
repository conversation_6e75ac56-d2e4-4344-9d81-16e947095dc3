'use client';
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTheme } from '@/contexts/ThemeContext';
import {
  FiUsers, FiUserPlus, FiSearch, FiFileText, FiBarChart,
  FiTrendingUp, FiCalendar, FiMapPin, FiPhone, FiMail,
  FiHome, FiArrowRight, FiRefreshCw
} from 'react-icons/fi';

export default function EmployeesDashboard() {
  const router = useRouter();
  const { isDarkMode, themeClasses } = useTheme();
  
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    male: 0,
    female: 0,
    // إحصائيات المغتربين والمحليين
    expatriates: 0, // إجمالي المغتربين
    locals: 0, // إجمالي المحليين
    expatriatesPrivateHousing: 0, // مغتربين بسكن خاص
    expatriatesCompanyHousing: 0, // مغتربين بسكن شركة
    housed: 0,
    governorates: 0,
    // إحصائيات التأمينات
    socialInsured: 0,
    socialUninsured: 0,
    medicalInsured: 0,
    medicalUninsured: 0,
    bothInsured: 0,
    noInsuranceData: 0,
    departments: [],
    recentAdditions: [],
    loading: true
  });

  useEffect(() => {
    // التحقق من تسجيل الدخول
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    if (!isLoggedIn) {
      router.push('/login');
      return;
    }

    fetchEmployeeStats();
  }, [router]);

  const fetchEmployeeStats = async () => {
    try {
      setStats(prev => ({ ...prev, loading: true }));

      // جلب إحصائيات الموظفين الحقيقية من قاعدة البيانات
      const response = await fetch('/api/test-dashboard-stats');
      const result = await response.json();

      if (result.success) {

        setStats({
          ...result.data,
          loading: false
        });
      } else {

        // بيانات افتراضية في حالة الفشل
        setStats({
          total: 0,
          active: 0,
          inactive: 0,
          male: 0,
          female: 0,
          resident: 0,
          housed: 0,
          governorates: 0,
          departments: [],
          recentAdditions: [],
          loading: false
        });
      }
    } catch (error) {

      setStats(prev => ({ ...prev, loading: false }));
    }
  };

  const quickActions = [
    {
      title: 'إضافة موظف جديد',
      description: 'إضافة موظف جديد للنظام',
      icon: FiUserPlus,
      color: 'bg-green-600',
      link: '/employees/add'
    },
    {
      title: 'البحث عن موظف',
      description: 'البحث الفردي والمجمع عن الموظفين',
      icon: FiSearch,
      color: 'bg-blue-600',
      link: '/employees/search'
    },
    {
      title: 'تقارير الموظفين',
      description: 'عرض وتصدير التقارير',
      icon: FiFileText,
      color: 'bg-purple-600',
      link: '/employees/reports'
    },
    {
      title: 'إدارة الأرشيف',
      description: 'إدارة مستندات الموظفين',
      icon: FiHome,
      color: 'bg-orange-600',
      link: '/employees/archive'
    }
  ];

  return (
    <div className={`min-h-screen ${themeClasses.bg.primary} p-6`}>
      <div className="max-w-7xl mx-auto">
        {/* رأس الصفحة */}
        <div className={`${themeClasses.bg.secondary} rounded-2xl p-6 shadow-lg mb-8`}>
          <div className="flex justify-between items-center">
            <div>
              <h1 className={`text-3xl font-bold ${themeClasses.text.primary} mb-2`}>
                لوحة تحكم الموظفين
              </h1>
              <p className={`${themeClasses.text.secondary}`}>
                إدارة شاملة لجميع بيانات وشؤون الموظفين
              </p>
            </div>
            <div className="flex items-center gap-4">
              <button
                onClick={fetchEmployeeStats}
                disabled={stats.loading}
                className={`${themeClasses.button.secondary} p-2 rounded-lg transition-colors disabled:opacity-50`}
                title="تحديث البيانات"
              >
                <FiRefreshCw className={`${stats.loading ? 'animate-spin' : ''}`} />
              </button>
              <button
                onClick={() => router.push('/dashboard')}
                className={`${themeClasses.button.primary} px-4 py-2 rounded-lg transition-colors flex items-center gap-2`}
              >
                <FiArrowRight className="rotate-180" />
                العودة للرئيسية
              </button>
            </div>
          </div>
        </div>

        {/* الإحصائيات الرئيسية */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className={`${themeClasses.bg.secondary} rounded-xl p-6 shadow-lg`}>
            <div className="flex items-center justify-between">
              <div>
                <h3 className={`text-lg font-semibold ${themeClasses.text.primary} mb-2`}>
                  إجمالي الموظفين
                </h3>
                <p className="text-3xl font-bold text-blue-600">
                  {stats.loading ? '...' : (stats.total || 0).toLocaleString()}
                </p>
                <p className={`text-sm ${themeClasses.text.secondary} mt-1`}>
                  من قاعدة البيانات
                </p>
              </div>
              <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                <FiUsers className="text-white text-xl" />
              </div>
            </div>
          </div>

          <div className={`${themeClasses.bg.secondary} rounded-xl p-6 shadow-lg`}>
            <div className="flex items-center justify-between">
              <div>
                <h3 className={`text-lg font-semibold ${themeClasses.text.primary} mb-2`}>
                  الموظفين النشطين
                </h3>
                <p className="text-3xl font-bold text-green-600">
                  {stats.loading ? '...' : (stats.active || 0).toLocaleString()}
                </p>
                <p className={`text-sm ${themeClasses.text.secondary} mt-1`}>
                  {(stats.total || 0) > 0 ? `${Math.round(((stats.active || 0) / (stats.total || 1)) * 100)}%` : '0%'} من الإجمالي
                </p>
              </div>
              <div className="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center">
                <FiTrendingUp className="text-white text-xl" />
              </div>
            </div>
          </div>

          <div className={`${themeClasses.bg.secondary} rounded-xl p-6 shadow-lg`}>
            <div className="flex items-center justify-between">
              <div>
                <h3 className={`text-lg font-semibold ${themeClasses.text.primary} mb-2`}>
                  عدد الأقسام
                </h3>
                <p className="text-3xl font-bold text-purple-600">
                  {stats.loading ? '...' : (stats.departments || []).length}
                </p>
                <p className={`text-sm ${themeClasses.text.secondary} mt-1`}>
                  أقسام مختلفة
                </p>
              </div>
              <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center">
                <FiBarChart className="text-white text-xl" />
              </div>
            </div>
          </div>

          <div className={`${themeClasses.bg.secondary} rounded-xl p-6 shadow-lg`}>
            <div className="flex items-center justify-between">
              <div>
                <h3 className={`text-lg font-semibold ${themeClasses.text.primary} mb-2`}>
                  المغتربين
                </h3>
                <p className="text-3xl font-bold text-orange-600">
                  {stats.loading ? '...' : (stats.expatriates || 0).toLocaleString()}
                </p>
                <p className={`text-sm ${themeClasses.text.secondary} mt-1`}>
                  {(stats.total || 0) > 0 ? `${Math.round(((stats.expatriates || 0) / (stats.total || 1)) * 100)}%` : '0%'} من الإجمالي
                </p>
              </div>
              <div className="w-12 h-12 bg-orange-600 rounded-lg flex items-center justify-center">
                <span className="text-white text-xl">🌍</span>
              </div>
            </div>
          </div>
        </div>

        {/* إحصائيات إضافية */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6 mb-8">
          <div className={`${themeClasses.bg.secondary} rounded-xl p-4 shadow-lg`}>
            <div className="text-center">
              <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center mx-auto mb-2">
                <FiUsers className="text-white text-sm" />
              </div>
              <h4 className={`text-sm font-semibold ${themeClasses.text.primary}`}>ذكور</h4>
              <p className="text-xl font-bold text-blue-500">
                {stats.loading ? '...' : (stats.male || 0).toLocaleString()}
              </p>
            </div>
          </div>

          <div className={`${themeClasses.bg.secondary} rounded-xl p-4 shadow-lg`}>
            <div className="text-center">
              <div className="w-8 h-8 bg-pink-500 rounded-lg flex items-center justify-center mx-auto mb-2">
                <FiUsers className="text-white text-sm" />
              </div>
              <h4 className={`text-sm font-semibold ${themeClasses.text.primary}`}>إناث</h4>
              <p className="text-xl font-bold text-pink-500">
                {stats.loading ? '...' : (stats.female || 0).toLocaleString()}
              </p>
            </div>
          </div>

          <div className={`${themeClasses.bg.secondary} rounded-xl p-4 shadow-lg`}>
            <div className="text-center">
              <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center mx-auto mb-2">
                <span className="text-white text-sm">🌍</span>
              </div>
              <h4 className={`text-sm font-semibold ${themeClasses.text.primary}`}>مغتربين</h4>
              <p className="text-xl font-bold text-orange-500">
                {stats.loading ? '...' : (stats.expatriates || 0).toLocaleString()}
              </p>
            </div>
          </div>

          <div className={`${themeClasses.bg.secondary} rounded-xl p-4 shadow-lg`}>
            <div className="text-center">
              <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center mx-auto mb-2">
                <span className="text-white text-sm">🏠</span>
              </div>
              <h4 className={`text-sm font-semibold ${themeClasses.text.primary}`}>محليين</h4>
              <p className="text-xl font-bold text-green-500">
                {stats.loading ? '...' : (stats.locals || 0).toLocaleString()}
              </p>
            </div>
          </div>

          <div className={`${themeClasses.bg.secondary} rounded-xl p-4 shadow-lg`}>
            <div className="text-center">
              <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center mx-auto mb-2">
                <span className="text-white text-sm">🛡️</span>
              </div>
              <h4 className={`text-sm font-semibold ${themeClasses.text.primary}`}>مؤمن عليهم</h4>
              <p className="text-xl font-bold text-purple-500">
                {stats.loading ? '...' : (stats.bothInsured || 0).toLocaleString()}
              </p>
            </div>
          </div>

          <div className={`${themeClasses.bg.secondary} rounded-xl p-4 shadow-lg`}>
            <div className="text-center">
              <div className="w-8 h-8 bg-indigo-500 rounded-lg flex items-center justify-center mx-auto mb-2">
                <FiMapPin className="text-white text-sm" />
              </div>
              <h4 className={`text-sm font-semibold ${themeClasses.text.primary}`}>محافظات</h4>
              <p className="text-xl font-bold text-indigo-500">
                {stats.loading ? '...' : (stats.governorates || 0).toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        {/* إحصائيات التأمينات */}
        <div className={`${themeClasses.bg.secondary} rounded-2xl p-6 shadow-lg mb-8`}>
          <h2 className={`text-2xl font-bold ${themeClasses.text.primary} mb-6 flex items-center gap-2`}>
            🏥 إحصائيات التأمينات
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* التأمين الاجتماعي */}
            <div className={`${themeClasses.bg.hover} rounded-xl p-4 border-r-4 border-blue-500`}>
              <div className="text-center">
                <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <span className="text-white text-lg">🛡️</span>
                </div>
                <h4 className={`text-sm font-semibold ${themeClasses.text.primary} mb-1`}>
                  مؤمن اجتماعي
                </h4>
                <p className="text-2xl font-bold text-blue-500 mb-1">
                  {stats.loading ? '...' : (stats.socialInsured || 0).toLocaleString()}
                </p>
                <p className={`text-xs ${themeClasses.text.secondary}`}>
                  اجتماعي
                </p>
              </div>
            </div>

            {/* التأمين الطبي */}
            <div className={`${themeClasses.bg.hover} rounded-xl p-4 border-r-4 border-green-500`}>
              <div className="text-center">
                <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <span className="text-white text-lg">🏥</span>
                </div>
                <h4 className={`text-sm font-semibold ${themeClasses.text.primary} mb-1`}>
                  التأمين الطبي
                </h4>
                <p className="text-2xl font-bold text-green-500 mb-1">
                  {stats.loading ? '...' : (stats.medicalInsured || 0).toLocaleString()}
                </p>
                <p className={`text-xs ${themeClasses.text.secondary}`}>
                  {(stats.total || 0) > 0 ? `${Math.round(((stats.medicalInsured || 0) / (stats.total || 1)) * 100)}%` : '0%'} من الإجمالي
                </p>
              </div>
            </div>

            {/* مؤمن بالاثنين */}
            <div className={`${themeClasses.bg.hover} rounded-xl p-4 border-r-4 border-purple-500`}>
              <div className="text-center">
                <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <span className="text-white text-lg">✅</span>
                </div>
                <h4 className={`text-sm font-semibold ${themeClasses.text.primary} mb-1`}>
                  مؤمن بالاثنين
                </h4>
                <p className="text-2xl font-bold text-purple-500 mb-1">
                  {stats.loading ? '...' : (stats.bothInsured || 0).toLocaleString()}
                </p>
                <p className={`text-xs ${themeClasses.text.secondary}`}>
                  {(stats.total || 0) > 0 ? `${Math.round(((stats.bothInsured || 0) / (stats.total || 1)) * 100)}%` : '0%'} من الإجمالي
                </p>
              </div>
            </div>

            {/* بدون بيانات تأمين */}
            <div className={`${themeClasses.bg.hover} rounded-xl p-4 border-r-4 border-gray-500`}>
              <div className="text-center">
                <div className="w-10 h-10 bg-gray-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <span className="text-white text-lg">❓</span>
                </div>
                <h4 className={`text-sm font-semibold ${themeClasses.text.primary} mb-1`}>
                  بدون بيانات
                </h4>
                <p className="text-2xl font-bold text-gray-500 mb-1">
                  {stats.loading ? '...' : (stats.noInsuranceData || 0).toLocaleString()}
                </p>
                <p className={`text-xs ${themeClasses.text.secondary}`}>
                  {(stats.total || 0) > 0 ? `${Math.round(((stats.noInsuranceData || 0) / (stats.total || 1)) * 100)}%` : '0%'} من الإجمالي
                </p>
              </div>
            </div>
          </div>

          {/* ملخص التأمينات */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className={`${themeClasses.bg.primary} rounded-lg p-4`}>
              <h5 className={`font-semibold ${themeClasses.text.primary} mb-2`}>
                📊 ملخص التأمين الاجتماعي
              </h5>
              <div className="flex justify-between items-center">
                <span className={`text-sm ${themeClasses.text.secondary}`}>مؤمن:</span>
                <span className="font-bold text-blue-500">
                  {stats.loading ? '...' : (stats.socialInsured || 0).toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className={`text-sm ${themeClasses.text.secondary}`}>غير مؤمن:</span>
                <span className="font-bold text-red-500">
                  {stats.loading ? '...' : (stats.socialUninsured || 0).toLocaleString()}
                </span>
              </div>
            </div>

            <div className={`${themeClasses.bg.primary} rounded-lg p-4`}>
              <h5 className={`font-semibold ${themeClasses.text.primary} mb-2`}>
                🏥 ملخص التأمين الطبي
              </h5>
              <div className="flex justify-between items-center">
                <span className={`text-sm ${themeClasses.text.secondary}`}>مؤمن:</span>
                <span className="font-bold text-green-500">
                  {stats.loading ? '...' : (stats.medicalInsured || 0).toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className={`text-sm ${themeClasses.text.secondary}`}>غير مؤمن:</span>
                <span className="font-bold text-red-500">
                  {stats.loading ? '...' : (stats.medicalUninsured || 0).toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* الإجراءات السريعة */}
        <div className={`${themeClasses.bg.secondary} rounded-2xl p-6 shadow-lg mb-8`}>
          <h2 className={`text-2xl font-bold ${themeClasses.text.primary} mb-6`}>
            الإجراءات السريعة
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {quickActions.map((action, index) => (
              <button
                key={index}
                onClick={() => router.push(action.link)}
                className={`${themeClasses.bg.hover} p-6 rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg group`}
              >
                <div className={`w-12 h-12 ${action.color} rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform`}>
                  <action.icon className="text-white text-xl" />
                </div>
                <h3 className={`font-semibold ${themeClasses.text.primary} mb-2`}>
                  {action.title}
                </h3>
                <p className={`text-sm ${themeClasses.text.secondary}`}>
                  {action.description}
                </p>
              </button>
            ))}
          </div>
        </div>

        {/* الأقسام والإضافات الحديثة */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* توزيع الأقسام */}
          <div className={`${themeClasses.bg.secondary} rounded-2xl p-6 shadow-lg`}>
            <h2 className={`text-2xl font-bold ${themeClasses.text.primary} mb-6`}>
              توزيع الموظفين حسب الأقسام
            </h2>
            <div className="space-y-4">
              {(stats.departments || []).map((dept, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className={`font-medium ${themeClasses.text.primary}`}>
                    {dept.name}
                  </span>
                  <div className="flex items-center gap-3">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                        style={{ width: `${((dept.count || 0) / (stats.total || 1)) * 100}%` }}
                      ></div>
                    </div>
                    <span className={`text-sm font-semibold ${themeClasses.text.secondary} min-w-[3rem] text-left`}>
                      {dept.count || 0}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* الإضافات الحديثة */}
          <div className={`${themeClasses.bg.secondary} rounded-2xl p-6 shadow-lg`}>
            <h2 className={`text-2xl font-bold ${themeClasses.text.primary} mb-6`}>
              أحدث الموظفين حسب تاريخ التواجد
            </h2>
            <div className="space-y-4">
              {stats.loading ? (
                <div className="space-y-4">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <div key={i} className={`${themeClasses.bg.hover} p-4 rounded-lg animate-pulse`}>
                      <div className="flex items-center justify-between">
                        <div className="space-y-2">
                          <div className="h-4 bg-gray-300 rounded w-32"></div>
                          <div className="h-3 bg-gray-200 rounded w-24"></div>
                        </div>
                        <div className="h-3 bg-gray-200 rounded w-20"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (stats.recentAdditions || []).length > 0 ? (
                (stats.recentAdditions || []).map((employee, index) => (
                  <div key={index} className={`${themeClasses.bg.hover} p-4 rounded-lg border-r-4 border-blue-500`}>
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className={`font-semibold ${themeClasses.text.primary}`}>
                            {employee.name}
                          </h3>
                          <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                            {employee.id}
                          </span>
                        </div>
                        <div className="flex items-center gap-4 text-sm">
                          <div className="flex items-center gap-1">
                            <FiUsers className="text-gray-400" />
                            <span className={themeClasses.text.secondary}>
                              {employee.department}
                            </span>
                          </div>
                          {employee.jobTitle && (
                            <div className="flex items-center gap-1">
                              <FiFileText className="text-gray-400" />
                              <span className={themeClasses.text.secondary}>
                                {employee.jobTitle}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="text-left">
                        <p className={`text-sm font-medium ${themeClasses.text.primary}`}>
                          {employee.date ? new Date(employee.date).toLocaleDateString('ar-EG') : 'غير محدد'}
                        </p>
                        <p className="text-xs text-gray-500">
                          {employee.dateType || 'تاريخ التواجد'}
                        </p>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className={`${themeClasses.bg.hover} p-8 rounded-lg text-center`}>
                  <FiCalendar className="mx-auto text-gray-400 text-3xl mb-2" />
                  <p className={`${themeClasses.text.secondary}`}>
                    لا توجد بيانات للموظفين المضافين حديثاً
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
