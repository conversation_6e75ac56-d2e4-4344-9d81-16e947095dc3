const { getConnection } = require('./src/utils/db.js');

async function addSampleCustodyCosts() {
  let pool = null;
  
  try {

    pool = await getConnection();
    
    // جلب البنود الرئيسية والفرعية
    const categoriesResult = await pool.request().query(`
      SELECT 
        mc.ID as MainID, 
        mc.CategoryName as MainName,
        mc.CategoryCode as MainCode,
        sc.ID as SubID,
        sc.CategoryName as SubName,
        sc.CategoryCode as SubCode
      FROM CostCategories mc
      LEFT JOIN CostCategories sc ON sc.ParentID = mc.ID AND sc.IsActive = 1
      WHERE mc.IsActive = 1 AND mc.ParentID IS NULL
      ORDER BY mc.ID, sc.ID
    `);
    
    // جلب العُهدة المستديمة
    const custodyResult = await pool.request().query(`
      SELECT TOP 1 ID, CustodyNumber, CustodianName
      FROM PermanentCustody
      WHERE IsActive = 1 AND Status = N'نشطة'
    `);
    
    if (custodyResult.recordset.length === 0) {

      return;
    }
    
    const custody = custodyResult.recordset[0];

    // تكاليف تجريبية متنوعة
    const sampleCosts = [
      {
        custodyType: 'مستديمة',
        permanentCustodyId: custody.ID,
        mainCategoryCode: 'MEALS',
        subCategoryCode: 'MEALS_CONCORD',
        description: 'وجبات غداء لفريق كونكورد - شهر يونيو',
        amount: 1500,
        settlementNumber: '191',
        receiptNumber: 'REC-001'
      },
      {
        custodyType: 'مستديمة',
        permanentCustodyId: custody.ID,
        mainCategoryCode: 'CARS',
        subCategoryCode: 'CARS_MAINTENANCE',
        description: 'صيانة دورية للسيارة رقم 123',
        amount: 800,
        settlementNumber: '192',
        receiptNumber: 'REC-002'
      },
      {
        custodyType: 'مستديمة',
        permanentCustodyId: custody.ID,
        mainCategoryCode: 'SERVICES',
        subCategoryCode: 'SERVICES_INTERNET_CONCORD',
        description: 'فاتورة إنترنت كونكورد - يونيو 2025',
        amount: 450,
        settlementNumber: '193',
        receiptNumber: 'REC-003'
      },
      {
        custodyType: 'مؤقتة',
        permanentCustodyId: null,
        mainCategoryCode: 'TEMP_NEW_APARTMENT_RENT',
        subCategoryCode: null,
        description: 'إيجار شقة جديدة للموظف الجديد',
        amount: 3000,
        settlementNumber: null,
        receiptNumber: 'REC-004'
      },
      {
        custodyType: 'مؤقتة',
        permanentCustodyId: null,
        mainCategoryCode: 'TEMP_TESTS',
        subCategoryCode: null,
        description: 'اختبارات طبية للموظفين الجدد',
        amount: 1200,
        settlementNumber: null,
        receiptNumber: 'REC-005'
      }
    ];

    for (let i = 0; i < sampleCosts.length; i++) {
      const cost = sampleCosts[i];
      
      // جلب معرف البند الرئيسي
      const mainCatResult = await pool.request()
        .input('categoryCode', cost.mainCategoryCode)
        .query(`
          SELECT ID FROM CostCategories 
          WHERE CategoryCode = @categoryCode AND IsActive = 1
        `);
      
      if (mainCatResult.recordset.length === 0) {

        continue;
      }
      
      const mainCategoryId = mainCatResult.recordset[0].ID;
      let subCategoryId = null;
      
      // جلب معرف البند الفرعي إذا كان موجوداً
      if (cost.subCategoryCode) {
        const subCatResult = await pool.request()
          .input('categoryCode', cost.subCategoryCode)
          .query(`
            SELECT ID FROM CostCategories 
            WHERE CategoryCode = @categoryCode AND IsActive = 1
          `);
        
        if (subCatResult.recordset.length > 0) {
          subCategoryId = subCatResult.recordset[0].ID;
        }
      }
      
      // إضافة التكلفة
      const insertResult = await pool.request()
        .input('custodyType', cost.custodyType)
        .input('permanentCustodyId', cost.permanentCustodyId)
        .input('mainCategoryId', mainCategoryId)
        .input('subCategoryId', subCategoryId)
        .input('description', cost.description)
        .input('amount', cost.amount)
        .input('costDate', new Date())
        .input('receiptNumber', cost.receiptNumber)
        .input('settlementNumber', cost.settlementNumber)
        .input('status', cost.settlementNumber ? 'تم التسوية' : 'قيد المراجعة')
        .query(`
          INSERT INTO IntegratedCosts (
            CustodyType, PermanentCustodyID, MainCategoryID, SubCategoryID,
            CostDescription, Amount, CostDate, ReceiptNumber, 
            SettlementNumber, Status, IsActive, CreatedAt
          )
          OUTPUT INSERTED.ID
          VALUES (
            @custodyType, @permanentCustodyId, @mainCategoryId, @subCategoryId,
            @description, @amount, @costDate, @receiptNumber,
            @settlementNumber, @status, 1, GETDATE()
          )
        `);
      
      const newCostId = insertResult.recordset[0].ID;
      console.log(`   ✅ تم إضافة التكلفة ${newCostId}: ${cost.description.substring(0, 50)}...`);
    }
    
    // اختبار نهائي

    const finalResult = await pool.request().query(`
      SELECT TOP 5
        ic.ID,
        ic.CustodyType,
        mc.CategoryName as MainCategoryName,
        sc.CategoryName as SubCategoryName,
        ic.CostDescription,
        ic.Amount,
        ic.Status,
        ic.SettlementNumber
      FROM IntegratedCosts ic
      LEFT JOIN CostCategories mc ON ic.MainCategoryID = mc.ID
      LEFT JOIN CostCategories sc ON ic.SubCategoryID = sc.ID
      WHERE ic.IsActive = 1
      ORDER BY ic.CreatedAt DESC
    `);
    
    finalResult.recordset.forEach((cost, index) => {

      console.log(`      ${cost.CostDescription.substring(0, 60)}... (${cost.Amount} جنيه)`);

    });

  } catch (error) {

  } finally {
    if (pool) {
      await pool.close();
    }
  }
}

addSampleCustodyCosts();
