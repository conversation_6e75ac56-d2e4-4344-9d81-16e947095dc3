'use client';

import Al<PERSON><PERSON>enter from '@/components/AlertCenter';
import FloatingBackupButton from '@/components/FloatingBackupButton';
import NotificationCenter from '@/components/NotificationCenter';
import { useLanguage, useTranslation } from '@/contexts/LanguageContext';
import { useTheme } from '@/contexts/ThemeContext';
import {
    Activity,
    AlertTriangle,
    BarChart3,
    Bell,
    Building,
    Calendar,
    CheckCircle,
    ChevronDown,
    ChevronRight,
    Clock,
    Code,
    Database,
    DollarSign,
    FileSpreadsheet,
    FileText,
    Filter,
    FolderOpen,
    Home,
    Languages,
    LogOut,
    Menu,
    Moon,
    Plus,
    Search,
    Settings,
    Shield,
    Sun,
    Table,
    Truck,
    Upload,
    User,
    UserCheck,
    UserPlus,
    Users,
    X
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function MainLayout({ children }) {
  const [sidebarOpen, setSidebarOpen] = useState(true); // افتراضياً مفتوحة
  const [sidebarHovered, setSidebarHovered] = useState(false);
  const [sidebarVisible, setSidebarVisible] = useState(true); // للإخفاء الكامل
  const [expandedMenus, setExpandedMenus] = useState({});
  const [userInfo, setUserInfo] = useState(null);
  const { isDarkMode, toggleTheme } = useTheme();
  const { language, isRTL, toggleLanguage, isArabic } = useLanguage();
  const { t } = useTranslation();
  const router = useRouter();

  const isExpanded = (sidebarOpen || sidebarHovered) && sidebarVisible;

  // جلب بيانات المستخدم
  useEffect(() => {
    const user = localStorage.getItem('userInfo');
    if (user) {
      const userInfoData = JSON.parse(user);
      setUserInfo(userInfoData);

    }
  }, []);

  const toggleMenu = (menuId) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menuId]: !prev[menuId]
    }));
  };

  const handleLogout = () => {
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('userInfo');
    router.push('/login');
  };

  const handleContentClick = () => {
    if (isExpanded && window.innerWidth < 1024) {
      setSidebarOpen(false);
    }
  };

  const toggleSidebar = () => {
    if (sidebarOpen) {
      // إذا كان مفتوحاً، اطويه (أيقونات فقط)
      setSidebarOpen(false);
    } else if (sidebarVisible) {
      // إذا كان مطوياً، افتحه
      setSidebarOpen(true);
    } else {
      // إذا كان مخفياً، أظهره مطوياً
      setSidebarVisible(true);
      setSidebarOpen(false);
    }
  };

  const hideSidebar = () => {
    setSidebarVisible(false);
    setSidebarOpen(false);
  };

  const showSidebar = () => {
    setSidebarVisible(true);
    setSidebarOpen(true);
  };

  const menuItems = [
    {
      id: 'home',
      title: t('dashboard'),
      icon: Home,
      link: '/dashboard'
    },
    {
      id: 'employees',
      title: t('employees'),
      icon: Users,
      color: 'blue',
      subItems: [
        { title: isArabic ? 'داش بورد الموظفين' : 'Employee Dashboard', link: '/employees/dashboard', icon: BarChart3 },
        { title: isArabic ? 'الهيكل التنظيمي الشامل' : 'Comprehensive Organizational Structure', link: '/org-structure', icon: Users },
        { title: t('searchEmployee'), link: '/employees/search', icon: Search },
        { title: t('addEmployee'), link: '/employees/add', icon: UserPlus },
        { title: t('employeeArchive'), link: '/employees/archive', icon: FolderOpen }
      ]
    },
    {
      id: 'requests',
      title: isArabic ? 'الطلبات الورقية' : 'Paper Requests',
      icon: FileText,
      color: 'purple',
      subItems: [
        { title: isArabic ? 'مركز الطلبات' : 'Requests Center', link: '/requests', icon: FileText },
        { title: isArabic ? 'طلباتي' : 'My Requests', link: '/my-requests', icon: Calendar },
        { title: isArabic ? 'داش بورد الإجازات' : 'Leaves Dashboard', link: '/leaves-dashboard', icon: BarChart3 },
        { title: isArabic ? 'الإجازات المعتمدة' : 'Approved Leaves', link: '/approved-leaves', icon: CheckCircle },
        { title: isArabic ? 'رصيد الإجازات' : 'Leave Balances', link: '/leave-balances', icon: BarChart3 },
        { title: isArabic ? 'تقارير الطلبات' : 'Requests Reports', link: '/requests/reports', icon: FileText },
        { title: isArabic ? 'طباعة الطلبات' : 'Print Requests', link: '/requests/print', icon: FileText }
      ]
    },
    {
      id: 'attendance',
      title: isArabic ? 'التمام اليومى والمؤثرات الشهرية' : 'Daily Attendance & Monthly Effects',
      icon: Clock,
      color: 'green',
      subItems: [
        { title: isArabic ? 'التمام اليومى' : 'Daily Attendance', link: '/attendance/daily', icon: Clock },
        { title: isArabic ? 'كشف التمام الشهري' : 'Monthly Attendance Report', link: '/attendance/monthly-report', icon: Calendar },
        { title: isArabic ? 'رفع التمام اليومي' : 'Upload Daily Attendance', link: '/attendance/upload', icon: Upload },
        { title: isArabic ? 'إصلاح حساب التمام' : 'Fix Attendance Calculation', link: '/fix-attendance-calculation', icon: Settings },
        { title: isArabic ? 'إصلاح مباشر للتمام' : 'Direct Attendance Fix', link: '/direct-fix', icon: Settings },
        { title: isArabic ? 'كشف المؤثرات الشهرية' : 'Monthly Effects Report', link: '/attendance/monthly', icon: BarChart3 },
        { title: isArabic ? 'كشف الرواتب الشهرية' : 'Monthly Payroll Report', link: '/payroll/monthly', icon: DollarSign },
        { title: isArabic ? 'تقرير الحضور' : 'Attendance Report', link: '/attendance/report', icon: FileText }
      ]
    },
    {
      id: 'cars',
      title: t('cars'),
      icon: Truck,
      color: 'orange',
      subItems: [
        { title: isArabic ? 'إدارة السيارات' : 'Car Management', link: '/cars', icon: Truck },
        { title: isArabic ? 'عرض بيانات السيارات' : 'Car Data', link: '/cars/data', icon: Truck },
        { title: isArabic ? 'أرشيف مستندات السيارات' : 'Car Archive', link: '/cars/archive', icon: FolderOpen },
        { title: isArabic ? 'مذكرات' : 'Memos', link: '/cars/memos', icon: FileText }
      ]
    },
    {
      id: 'apartments',
      title: t('apartments'),
      icon: Building,
      color: 'purple',
      subItems: [
        { title: isArabic ? 'إدارة الشقق' : 'Apartment Management', link: '/apartments', icon: Building },
        { title: isArabic ? 'عرض بيانات الشقق' : 'Apartment Data', link: '/apartments/data', icon: Building },
        { title: isArabic ? 'أرشيف مستندات الشقق' : 'Apartment Archive', link: '/apartments/archive', icon: FolderOpen },
        { title: isArabic ? 'مذكرات' : 'Memos', link: '/apartments/memos', icon: FileText }
      ]
    },

    {
      id: 'custody-costs',
      title: isArabic ? 'إدارة العهد والتكاليف' : 'Custody & Costs Management',
      icon: DollarSign,
      color: 'red',
      subItems: [
        { title: isArabic ? 'إدارة العهد والتكاليف' : 'Custody & Costs Management', link: '/custody-costs', icon: DollarSign },
        { title: isArabic ? 'تفاصيل العُهد المستديمة' : 'Custody Details', link: '/custody-details', icon: Activity },
        { title: isArabic ? 'لوحة تحكم تكاليف العُهد' : 'Custody Costs Dashboard', link: '/custody-costs/dashboard', icon: BarChart3 },
        { title: isArabic ? 'داش بورد التكاليف' : 'Costs Dashboard', link: '/costs/dashboard', icon: BarChart3 },
        { title: isArabic ? 'تكاليف السيارات' : 'Car Costs', link: '/costs/cars', icon: Truck },
        { title: isArabic ? 'تكاليف الشقق' : 'Apartment Costs', link: '/costs/apartments', icon: Building },
        { title: isArabic ? 'تكاليف العمالة المؤقتة' : 'Temp Worker Costs', link: '/costs/temp-workers', icon: Users },
        { title: isArabic ? 'عهد المشروع' : 'Project Custody', link: '/costs/project-custody', icon: DollarSign },
        { title: isArabic ? 'إضافة تكلفة' : 'Add Cost', link: '/costs/project-custody/add-cost', icon: Plus }
      ]
    },
    {
      id: 'scan',
      title: isArabic ? 'نظام SCAN' : 'SCAN System',
      icon: FolderOpen,
      color: 'purple',
      subItems: [
        { title: isArabic ? 'مذكرات الموظفين' : 'Employee Memos', link: '/scan?section=employees', icon: Users },
        { title: isArabic ? 'مذكرات السيارات' : 'Car Documents', link: '/scan?section=cars', icon: Truck },
        { title: isArabic ? 'مذكرات الشقق' : 'Apartment Documents', link: '/scan?section=apartments', icon: Building },
        { title: isArabic ? 'مذكرات العمالة المؤقتة' : 'Temp Worker Memos', link: '/scan?section=tempWorkers', icon: UserCheck }
      ]
    },
    {
      id: 'reports',
      title: isArabic ? 'التقارير' : 'Reports',
      icon: BarChart3,
      color: 'teal',
      subItems: [
        { title: isArabic ? 'مركز التقارير' : 'Reports Dashboard', link: '/reports/dashboard', icon: BarChart3 },
        { title: isArabic ? 'تقارير الموظفين' : 'Employee Reports', link: '/reports/employees', icon: Users },
        { title: isArabic ? 'تقارير الحضور' : 'Attendance Reports', link: '/reports/attendance', icon: Clock },
        { title: isArabic ? 'تقارير الإجازات' : 'Leave Reports', link: '/reports/leaves', icon: Calendar },
        { title: isArabic ? 'تقارير التكاليف' : 'Cost Reports', link: '/reports/costs', icon: DollarSign },
        { title: isArabic ? 'تقارير الأصول' : 'Asset Reports', link: '/reports/assets', icon: Building },
        { title: isArabic ? 'تقارير العمالة المؤقتة' : 'Temp Workers Reports', link: '/reports/temp-workers', icon: UserCheck }
      ]
    },
    {
      id: 'data-templates',
      title: isArabic ? 'نماذج إدخال البيانات' : 'Data Templates',
      icon: FileText,
      color: 'indigo',
      subItems: [
        { title: isArabic ? 'نماذج Excel' : 'Excel Templates', link: '/data-templates', icon: FileSpreadsheet },
        { title: isArabic ? 'رفع التمام اليومي' : 'Upload Daily Attendance', link: '/upload/daily-attendance', icon: Clock },
        { title: isArabic ? 'رفع العمالة المؤقتة' : 'Upload Temp Workers', link: '/upload/temp-workers-daily', icon: UserCheck },
        { title: isArabic ? 'رفع الموظفين' : 'Upload Employees', link: '/upload/employees-bulk', icon: Users }
      ]
    },
    {
      id: 'notifications-center',
      title: isArabic ? 'مركز الإشعارات والتنبيهات' : 'Notifications & Alerts Center',
      icon: Bell,
      color: 'yellow',
      subItems: [
        { title: isArabic ? 'الإشعارات وسجل الإجراءات' : 'Notifications & Action Log', link: '/notifications', icon: Bell },
        { title: isArabic ? 'التنبيهات الذكية' : 'Smart Alerts', link: '/alerts', icon: AlertTriangle },
        { title: isArabic ? 'الإشعارات الذكية' : 'Smart Notifications', link: '/smart-notifications', icon: Bell }
      ]
    },
    {
      id: 'backup-system',
      title: isArabic ? 'النسخ الاحتياطي والاسترداد' : 'Backup & Restore',
      icon: Database,
      color: 'green',
      link: '/backup-system'
    },
    {
      id: 'system-testing',
      title: isArabic ? 'إعداد اختبارات النظام' : 'System Testing Setup',
      icon: Settings,
      color: 'gray',
      subItems: [
        { title: isArabic ? 'اختبار اتصالات النظام' : 'System Health Test', link: '/system-health', icon: Activity },
        { title: isArabic ? 'فحص قاعدة البيانات' : 'Database Scan', link: '/database-scan', icon: Database },
        { title: isArabic ? 'مراجعة قاعدة البيانات' : 'Database Audit', link: '/database-audit', icon: Shield },
        { title: isArabic ? 'تحليل تأثير الحقول' : 'Field Impact Analysis', link: '/column-impact', icon: Search },
        { title: isArabic ? 'مراجعة APIs' : 'APIs Review', link: '/api-audit', icon: Code },
        { title: isArabic ? 'فحص الجداول المطلوبة' : 'Required Tables Check', link: '/check-tables', icon: Table },
        { title: isArabic ? 'اختبار البيانات' : 'Data Testing', link: '/test-data', icon: Database },
        { title: isArabic ? 'إعداد النظام الشامل' : 'Complete System Setup', link: '/complete-setup', icon: Settings },
        { title: isArabic ? 'إدارة النظام' : 'System Management', link: '/system-management', icon: Settings },
        { title: isArabic ? 'تحليل قاعدة البيانات' : 'Database Analysis', link: '/database-analysis', icon: BarChart3 },
        { title: isArabic ? 'نظام الفلاتر المتقدم' : 'Advanced Filters Demo', link: '/cycle-filters-demo', icon: Filter }
      ]
    }
  ];

  return (
    <div className={`min-h-screen ${isDarkMode ? 'bg-[#0f172a] dark' : 'bg-white'} flex`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Sidebar */}
      {sidebarVisible && (
        <div
          className={`fixed right-0 top-0 h-full ${
            isDarkMode
              ? 'bg-[#1e293b] border-slate-700'
              : 'bg-white border-gray-200'
          } shadow-xl z-40 transition-all duration-300 ease-in-out ${
            isExpanded ? 'w-72' : 'w-16'
          } border-l overflow-hidden`}
          onMouseEnter={() => setSidebarHovered(true)}
          onMouseLeave={() => setSidebarHovered(false)}
        >
        <div className="p-4 h-full flex flex-col">
          {/* شعار النظام وزر التبديل */}
          <div className={`flex items-center ${isExpanded ? 'justify-between' : 'justify-center'} mb-6`}>
            {isExpanded ? (
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-lg">
                  <Building className="text-lg text-white" />
                </div>
                <div className="mr-3">
                  <h1 className={`font-bold text-lg ${
                    isDarkMode ? 'text-white' : 'text-gray-800'
                  } tracking-tight`}>نظام إدارة الشركة</h1>
                  <p className={`text-xs ${
                    isDarkMode ? 'text-slate-400' : 'text-gray-500'
                  } mt-1`}>إدارة شاملة ومتطورة</p>
                </div>
              </div>
            ) : (
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-lg">
                <Building className="text-base text-white" />
              </div>
            )}

            {/* أزرار التحكم في القائمة */}
            {isExpanded && (
              <div className="flex gap-1">
                <button
                  onClick={toggleSidebar}
                  className={`p-1.5 rounded-lg transition-all duration-300 ${
                    isDarkMode
                      ? 'hover:bg-slate-700 text-slate-400 hover:text-white'
                      : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
                  }`}
                  title="طي القائمة (أيقونات فقط)"
                >
                  <ChevronRight className="w-4 h-4" />
                </button>
                <button
                  onClick={hideSidebar}
                  className={`p-1.5 rounded-lg transition-all duration-300 ${
                    isDarkMode
                      ? 'hover:bg-red-700 text-red-400 hover:text-white'
                      : 'hover:bg-red-100 text-red-500 hover:text-red-700'
                  }`}
                  title="إخفاء القائمة بالكامل"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            )}
          </div>

          {/* معلومات المستخدم */}
          {isExpanded && userInfo && (
            <div className={`${
              isDarkMode
                ? 'bg-slate-800 border-slate-700'
                : 'bg-blue-50 border-blue-200'
            } rounded-xl p-3 mb-6 border`}>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 rounded-xl flex items-center justify-center shadow-lg">
                  <User className="text-white text-base" />
                </div>
                <div className="flex-1">
                  <div className={`font-semibold text-sm ${
                    isDarkMode ? 'text-white' : 'text-gray-800'
                  }`}>{userInfo.username}</div>
                  <div className={`text-xs ${
                    isDarkMode ? 'text-slate-400' : 'text-blue-600'
                  } mt-0.5`}>مدير النظام</div>
                </div>
              </div>
            </div>
          )}

          {/* عناصر القائمة */}
          <nav className={`${isExpanded ? 'space-y-2' : 'space-y-3'} flex-1 overflow-y-auto nav-scroll`}>
            {menuItems.map((item) => (
              <div key={item.id}>
                {item.subItems ? (
                  <div className="relative">
                    <button
                      onClick={() => toggleMenu(item.id)}
                      className={`w-full flex items-center ${
                        isExpanded ? 'p-3' : 'p-2'
                      } rounded-xl transition-all duration-300 group ${
                        expandedMenus[item.id]
                          ? `${isDarkMode
                              ? 'bg-blue-600/20 text-blue-400 border border-blue-500/30'
                              : 'bg-blue-600 text-white shadow-md'}`
                          : `${isDarkMode
                              ? 'text-slate-300 hover:bg-slate-700 hover:text-white'
                              : 'text-gray-600 hover:bg-blue-50 hover:text-blue-700'}`
                      }`}
                      title={!isExpanded ? item.title : ''}
                      style={!isExpanded ? { position: 'relative' } : {}}
                    >
                      <div className={`${
                        isExpanded ? 'w-8 h-8' : 'w-7 h-7'
                      } ${
                        expandedMenus[item.id]
                          ? 'bg-blue-600'
                          : isDarkMode
                            ? 'bg-slate-600 group-hover:bg-blue-600'
                            : 'bg-blue-100 group-hover:bg-blue-600'
                      } rounded-lg flex items-center justify-center transition-all duration-300 ${!isExpanded ? 'ml-2' : ''}`}>
                        <item.icon className={`${
                          isExpanded ? 'text-base' : 'text-sm'
                        } ${
                          expandedMenus[item.id] ? 'text-white' : isDarkMode ? 'text-slate-300 group-hover:text-white' : 'text-blue-600 group-hover:text-white'
                        } transition-colors duration-300`} />
                      </div>

                      {/* النص يظهر فقط عند التوسع */}
                      {isExpanded && (
                        <span className="font-medium text-sm mr-3">
                          {item.title}
                        </span>
                      )}

                      {isExpanded && (
                        <div className="mr-auto">
                          <ChevronDown className={`w-4 h-4 transition-transform duration-300 ${
                            expandedMenus[item.id] ? 'rotate-180' : ''
                          }`} />
                        </div>
                      )}
                    </button>



                    {expandedMenus[item.id] && (
                      <div className={`mt-4 space-y-2 ${isExpanded ? 'mr-12' : 'mr-2'}`}>
                        {item.subItems.map((subItem, index) => (
                          <a
                            key={index}
                            href={subItem.link}
                            className={`w-full flex items-center ${
                              isExpanded ? 'p-3' : 'p-2'
                            } rounded-xl transition-all duration-300 group ${
                              isDarkMode
                                ? 'text-gray-300 hover:bg-[#2a2a2a] hover:text-white'
                                : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                            } hover:shadow-md hover:scale-[1.02]`}
                          >
                            <div className={`${
                              isExpanded ? 'w-8 h-8' : 'w-6 h-6'
                            } rounded-lg ${
                              isDarkMode
                                ? 'bg-[#404040] group-hover:bg-blue-600'
                                : 'bg-gray-100 group-hover:bg-blue-600'
                            } flex items-center justify-center transition-all duration-300 group-hover:scale-110 ${!isExpanded ? 'ml-2' : ''}`}>
                              <subItem.icon className={`${
                                isExpanded ? 'text-sm' : 'text-xs'
                              } text-blue-600 group-hover:text-white transition-colors duration-300`} />
                            </div>
                            <span className={`text-sm font-medium ${
                              isExpanded ? 'mr-3' : 'flex-1 text-right truncate'
                            }`}>{subItem.title}</span>
                          </a>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="relative">
                    <a
                      href={item.link}
                      className={`w-full flex items-center ${
                        isExpanded ? 'p-3' : 'p-2'
                      } rounded-xl transition-all duration-300 group ${
                        item.id === 'home'
                          ? `${isDarkMode
                              ? 'bg-blue-600/20 text-blue-400 border border-blue-500/30'
                              : 'bg-blue-600 text-white shadow-md'}`
                          : `${isDarkMode
                              ? 'text-slate-300 hover:bg-slate-700 hover:text-white'
                              : 'text-gray-600 hover:bg-blue-50 hover:text-blue-700'}`
                      }`}
                      title={!isExpanded ? item.title : ''}
                    >
                      <div className={`${
                        isExpanded ? 'w-8 h-8' : 'w-7 h-7'
                      } ${
                        item.id === 'home'
                          ? 'bg-blue-600'
                          : isDarkMode
                            ? 'bg-slate-600 group-hover:bg-blue-600'
                            : 'bg-blue-100 group-hover:bg-blue-600'
                      } rounded-lg flex items-center justify-center transition-all duration-300 ${!isExpanded ? 'ml-2' : ''}`}>
                        <item.icon className={`${
                          isExpanded ? 'text-base' : 'text-sm'
                        } ${
                          item.id === 'home' ? 'text-white' : isDarkMode ? 'text-slate-300 group-hover:text-white' : 'text-blue-600 group-hover:text-white'
                        } transition-colors duration-300`} />
                      </div>

                      {/* النص يظهر فقط عند التوسع */}
                      {isExpanded && (
                        <span className="font-medium text-sm mr-3">
                          {item.title}
                        </span>
                      )}
                    </a>


                  </div>
                )}
              </div>
            ))}
          </nav>

          {/* تبديل الوضع واللغة وتسجيل الخروج */}
          <div className={`${isExpanded ? 'space-y-3' : 'space-y-4'} pt-6 border-t ${
            isDarkMode ? 'border-[#404040]' : 'border-gray-200/60'
          }`}>
            {/* زر تبديل اللغة */}
            <div className="relative">
              <button
                onClick={toggleLanguage}
                className={`w-full flex items-center ${
                  isExpanded ? 'p-3' : 'p-2'
                } rounded-xl transition-all duration-300 group ${
                  isDarkMode
                    ? 'text-blue-400 hover:bg-blue-600/20'
                    : 'text-gray-600 hover:bg-blue-50 hover:text-blue-700'
                }`}
                title={!isExpanded ? (isArabic ? 'تبديل اللغة' : 'Switch Language') : ''}
              >
                <div className={`${
                  isExpanded ? 'w-8 h-8' : 'w-7 h-7'
                } bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 rounded-lg flex items-center justify-center transition-all duration-300 ${!isExpanded ? 'ml-2' : ''}`}>
                  <Languages className={`${isExpanded ? 'text-base' : 'text-sm'} text-white`} />
                </div>
                {isExpanded && (
                  <span className="font-medium text-sm mr-3">
                    {isArabic ? 'English' : 'العربية'}
                  </span>
                )}
              </button>


            </div>

            {/* زر تبديل الوضع */}
            <div className="relative">
              <button
                onClick={toggleTheme}
                className={`w-full flex items-center ${
                  isExpanded ? 'p-3' : 'p-2'
                } rounded-xl transition-all duration-300 group ${
                  isDarkMode
                    ? 'text-yellow-400 hover:bg-yellow-600/20'
                    : 'text-gray-600 hover:bg-yellow-50 hover:text-yellow-700'
                }`}
                title={!isExpanded ? (isArabic ? 'تبديل الوضع' : 'Toggle Theme') : ''}
              >
                <div className={`${
                  isExpanded ? 'w-8 h-8' : 'w-7 h-7'
                } bg-gradient-to-br from-yellow-500 via-yellow-600 to-yellow-700 rounded-lg flex items-center justify-center transition-all duration-300 ${!isExpanded ? 'ml-2' : ''}`}>
                  {isDarkMode ? (
                    <Sun className={`${isExpanded ? 'text-base' : 'text-sm'} text-white`} />
                  ) : (
                    <Moon className={`${isExpanded ? 'text-base' : 'text-sm'} text-white`} />
                  )}
                </div>
                {isExpanded && (
                  <span className="font-medium text-sm mr-3">
                    {isArabic ? 'تبديل الوضع' : 'Toggle Theme'}
                  </span>
                )}
              </button>


            </div>

            {/* زر تسجيل الخروج */}
            <div className="relative">
              <button
                onClick={handleLogout}
                className={`w-full flex items-center ${
                  isExpanded ? 'p-3' : 'p-2'
                } rounded-xl transition-all duration-300 group ${
                  isDarkMode
                    ? 'text-red-400 hover:bg-red-600/20'
                    : 'text-red-600 hover:bg-red-50 hover:text-red-700'
                }`}
                title={!isExpanded ? t('logout') : ''}
              >
                <div className={`${
                  isExpanded ? 'w-8 h-8' : 'w-7 h-7'
                } bg-gradient-to-br from-red-500 via-red-600 to-red-700 rounded-lg flex items-center justify-center transition-all duration-300 ${!isExpanded ? 'ml-2' : ''}`}>
                  <LogOut className={`${isExpanded ? 'text-base' : 'text-sm'} text-white`} />
                </div>
                {isExpanded && (
                  <span className="font-medium text-sm mr-3">{t('logout')}</span>
                )}
              </button>


            </div>
          </div>
        </div>
      </div>
      )}

      {/* زر إظهار الشريط الجانبي عند إخفائه */}
      {!sidebarVisible && (
        <button
          onClick={showSidebar}
          className={`fixed top-4 right-4 z-50 p-3 rounded-lg shadow-lg transition-all duration-300 ${
            isDarkMode
              ? 'bg-blue-600 hover:bg-blue-700 text-white'
              : 'bg-blue-500 hover:bg-blue-600 text-white'
          }`}
          title="إظهار القائمة"
        >
          <Menu className="w-5 h-5" />
        </button>
      )}

      {/* المحتوى الرئيسي */}
      <div
        className={`flex-1 transition-all duration-300 ease-in-out ${
          sidebarVisible ? (isExpanded ? 'mr-72' : 'mr-16') : 'mr-0'
        }`}
        onClick={handleContentClick}
      >
        {/* شريط علوي للإشعارات */}
        <div className={`${
          isDarkMode ? 'bg-[#1e293b] border-slate-700' : 'bg-white border-gray-200'
        } border-b px-6 py-3 flex items-center justify-between`}>
          <div className="flex items-center gap-4">
            <h2 className={`text-lg font-semibold ${
              isDarkMode ? 'text-white' : 'text-gray-800'
            }`}>
              {/* يمكن إضافة عنوان الصفحة هنا */}
            </h2>
          </div>

          <div className="flex items-center gap-4">
            {/* مكون التنبيهات الذكية */}
            {userInfo && (
              <AlertCenter
                userCode={userInfo.username}
                className="relative"
              />
            )}

            {/* مكون الإشعارات */}
            {userInfo && (
              <NotificationCenter
                userCode={userInfo.username}
                className="relative"
              />
            )}

            {/* معلومات المستخدم المختصرة */}
            {userInfo && (
              <div className="flex items-center gap-3">
                <div className={`text-sm ${
                  isDarkMode ? 'text-gray-300' : 'text-gray-600'
                }`}>
                  مرحباً، {userInfo.username}
                </div>
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 rounded-full flex items-center justify-center">
                  <User className="text-white text-sm" />
                </div>
              </div>
            )}
          </div>
        </div>

        <div className={`min-h-screen ${
          isDarkMode ? 'bg-[#0f172a]' : 'bg-white'
        } p-6`}>
          {children}
        </div>

        {/* زر النسخ الاحتياطي العائم */}
        <FloatingBackupButton />
      </div>
    </div>
  );
}
