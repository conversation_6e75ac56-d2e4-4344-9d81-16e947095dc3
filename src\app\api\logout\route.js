import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function POST(request) {
  try {

    // الحصول على معلومات المستخدم من الهيدر (من middleware)
    const userId = request.headers.get('x-user-id');
    const username = request.headers.get('x-username');
    
    // تسجيل نشاط تسجيل الخروج
    if (userId && username) {
      try {
        const pool = await getConnection();
        await pool.request()
          .input('userCode', sql.NVarChar, userId)
          .input('userName', sql.NVarChar, username)
          .input('actionType', sql.NVarChar, 'LOGOUT')
          .input('actionDescription', sql.NVarChar, `تم تسجيل الخروج للمستخدم: ${username}`)
          .input('ipAddress', sql.<PERSON>Var<PERSON>har, request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'Unknown')
          .input('userAgent', sql.NVarChar, request.headers.get('user-agent') || 'Unknown')
          .input('isSuccess', sql.Bit, 1)
          .query(`
            INSERT INTO UserActions (UserCode, UserName, ActionType, ActionDescription, IPAddress, UserAgent, IsSuccess, ActionDate)
            VALUES (@userCode, @userName, @actionType, @actionDescription, @ipAddress, @userAgent, @isSuccess, GETDATE())
          `);

      } catch (logError) {

      }
    }

    // إنشاء الاستجابة
    const response = NextResponse.json({
      success: true,
      message: 'تم تسجيل الخروج بنجاح'
    });

    // حذف التوكن من الكوكيز
    response.cookies.delete('auth-token');

    return response;

  } catch (error) {

    // حذف التوكن حتى لو حدث خطأ
    const response = NextResponse.json({
      success: false,
      error: 'حدث خطأ أثناء تسجيل الخروج'
    }, { status: 500 });
    
    response.cookies.delete('auth-token');
    
    return response;
  }
}
