import { sql } from '@/lib/db';
import { NextResponse } from 'next/server';

export async function POST(request) {
  try {
    const { action, filters, page = 1, limit = 10 } = await request.json();

    if (action === 'search') {      let queryStr = `
        SELECT * FROM Employees 
        WHERE 1=1
      `;
      const values = [];
      let paramCount = 1;

      // Add search conditions
      if (filters.code) {        queryStr += ` AND EmployeeID LIKE @code`;
        values.push(`%${filters.code}%`);
        paramCount++;
      }

      if (filters.name) {        queryStr += ` AND FullName LIKE @name`;
        values.push(`%${filters.name}%`);
        paramCount++;
      }

      if (filters.department) {        queryStr += ` AND Department LIKE @department`;
        values.push(`%${filters.department}%`);
        paramCount++;
      }

      if (filters.governorate) {        queryStr += ` AND Governorate LIKE @governorate`;
        values.push(`%${filters.governorate}%`);
        paramCount++;
      }

      // Add pagination
      const offset = (page - 1) * limit;
      queryStr += ` ORDER BY employee_name ASC LIMIT $${paramCount} OFFSET $${paramCount + 1}`;
      values.push(limit, offset);

      // Get total count for pagination
      const countQuery = queryStr.replace('SELECT *', 'SELECT COUNT(*)');
      const totalCountResult = await sql(countQuery, values);
      const total = parseInt(totalCountResult[0].count);

      // Get paginated results
      const employees = await sql(queryStr, values);

      return NextResponse.json({
        success: true,
        employees,
        pagination: {
          total,
          limit,
          totalPages: Math.ceil(total / limit)
        }
      });
    }

    return NextResponse.json({
      success: false,
      error: 'Invalid action'
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message || 'An error occurred while processing the request'
    });
  }
}
