// نظام الدورات الزمنية المتقدم
export const CYCLE_TYPES = {
  MONTHLY_EFFECTS: 'monthly_effects',    // المؤثرات الشهرية والحضور: 11 → 10
  TEMP_WORKERS: 'temp_workers',          // العمالة المؤقتة: 1 → 30/31
  VERSION_REQUEST: 'version_request'     // طلب الإصدار: 14 → 15
};

// حساب تواريخ الدورة بناءً على النوع والشهر/السنة
export function calculateCycleDates(cycleType, month, year) {
  const result = {
    startDate: null,
    endDate: null,
    displayName: '',
    description: ''
  };

  switch (cycleType) {
    case CYCLE_TYPES.MONTHLY_EFFECTS:
      // دورة المؤثرات الشهرية: من 11 الشهر السابق إلى 10 الشهر الحالي
      const prevMonth = month === 1 ? 12 : month - 1;
      const prevYear = month === 1 ? year - 1 : year;
      
      result.startDate = new Date(prevYear, prevMonth - 1, 11); // 11 من الشهر السابق
      result.endDate = new Date(year, month - 1, 10); // 10 من الشهر الحالي
      result.displayName = `مؤثرات ${getMonthNameArabic(month)} ${year}`;
      result.description = `من 11 ${getMonthNameArabic(prevMonth)} إلى 10 ${getMonthNameArabic(month)}`;
      break;

    case CYCLE_TYPES.TEMP_WORKERS:
      // دورة العمالة المؤقتة: من 1 إلى آخر يوم في الشهر
      const lastDay = new Date(year, month, 0).getDate(); // آخر يوم في الشهر
      
      result.startDate = new Date(year, month - 1, 1); // 1 من الشهر
      result.endDate = new Date(year, month - 1, lastDay); // آخر يوم من الشهر
      result.displayName = `عمالة مؤقتة ${getMonthNameArabic(month)} ${year}`;
      result.description = `من 1 إلى ${lastDay} ${getMonthNameArabic(month)}`;
      break;

    case CYCLE_TYPES.VERSION_REQUEST:
      // دورة طلب الإصدار: من 14 الشهر الحالي إلى 15 الشهر التالي
      const nextMonth = month === 12 ? 1 : month + 1;
      const nextYear = month === 12 ? year + 1 : year;
      
      result.startDate = new Date(year, month - 1, 14); // 14 من الشهر الحالي
      result.endDate = new Date(nextYear, nextMonth - 1, 15); // 15 من الشهر التالي
      result.displayName = `طلب إصدار ${getMonthNameArabic(month)} ${year}`;
      result.description = `من 14 ${getMonthNameArabic(month)} إلى 15 ${getMonthNameArabic(nextMonth)}`;
      break;

    default:
      throw new Error(`نوع دورة غير مدعوم: ${cycleType}`);
  }

  return result;
}

// حساب الدورة الحالية بناءً على التاريخ الحالي
export function getCurrentCycle(cycleType) {
  const now = new Date();
  const currentMonth = now.getMonth() + 1;
  const currentYear = now.getFullYear();
  const currentDay = now.getDate();

  let targetMonth = currentMonth;
  let targetYear = currentYear;

  switch (cycleType) {
    case CYCLE_TYPES.MONTHLY_EFFECTS:
      // إذا كنا قبل يوم 11، فنحن في دورة الشهر السابق
      if (currentDay < 11) {
        targetMonth = currentMonth === 1 ? 12 : currentMonth - 1;
        targetYear = currentMonth === 1 ? currentYear - 1 : currentYear;
      }
      break;

    case CYCLE_TYPES.VERSION_REQUEST:
      // إذا كنا قبل يوم 14، فنحن في دورة الشهر السابق
      if (currentDay < 14) {
        targetMonth = currentMonth === 1 ? 12 : currentMonth - 1;
        targetYear = currentMonth === 1 ? currentYear - 1 : currentYear;
      }
      break;

    case CYCLE_TYPES.TEMP_WORKERS:
      // العمالة المؤقتة تتبع الشهر الميلادي العادي
      break;
  }

  return calculateCycleDates(cycleType, targetMonth, targetYear);
}

// إنشاء قائمة بالدورات المتاحة للاختيار
export function getAvailableCycles(cycleType, yearsBack = 2, yearsForward = 1) {
  const cycles = [];
  const currentYear = new Date().getFullYear();
  
  for (let year = currentYear - yearsBack; year <= currentYear + yearsForward; year++) {
    for (let month = 1; month <= 12; month++) {
      const cycle = calculateCycleDates(cycleType, month, year);
      cycles.push({
        value: `${year}-${month.toString().padStart(2, '0')}`,
        label: cycle.displayName,
        description: cycle.description,
        startDate: cycle.startDate,
        endDate: cycle.endDate,
        month,
        year
      });
    }
  }

  return cycles.reverse(); // الأحدث أولاً
}

// تحويل التاريخ إلى SQL WHERE clause
export function getCycleSQLFilter(cycleType, month, year, dateColumn = 'CreatedAt') {
  const cycle = calculateCycleDates(cycleType, month, year);
  
  const startDateStr = cycle.startDate.toISOString().split('T')[0];
  const endDateStr = cycle.endDate.toISOString().split('T')[0];
  
  return {
    whereClause: `${dateColumn} >= '${startDateStr}' AND ${dateColumn} <= '${endDateStr} 23:59:59'`,
    startDate: startDateStr,
    endDate: endDateStr,
    displayName: cycle.displayName
  };
}

// أسماء الشهور بالعربية
export function getMonthNameArabic(month) {
  const months = {
    1: 'يناير',
    2: 'فبراير', 
    3: 'مارس',
    4: 'أبريل',
    5: 'مايو',
    6: 'يونيو',
    7: 'يوليو',
    8: 'أغسطس',
    9: 'سبتمبر',
    10: 'أكتوبر',
    11: 'نوفمبر',
    12: 'ديسمبر'
  };
  return months[month] || 'غير محدد';
}

// أسماء الشهور بالإنجليزية
export function getMonthNameEnglish(month) {
  const months = {
    1: 'January',
    2: 'February',
    3: 'March', 
    4: 'April',
    5: 'May',
    6: 'June',
    7: 'July',
    8: 'August',
    9: 'September',
    10: 'October',
    11: 'November',
    12: 'December'
  };
  return months[month] || 'Unknown';
}

// تحليل قيمة الفلتر المرسلة من الواجهة
export function parseCycleFilter(filterValue) {
  if (!filterValue || filterValue === 'current') {
    return null; // استخدام الدورة الحالية
  }
  
  const [year, month] = filterValue.split('-').map(Number);
  return { year, month };
}

// تنسيق التاريخ للعرض
export function formatDateArabic(date) {
  if (!date) return '';
  
  const day = date.getDate();
  const month = date.getMonth() + 1;
  const year = date.getFullYear();
  
  return `${day} ${getMonthNameArabic(month)} ${year}`;
}

// حساب عدد الأيام في الدورة
export function getCycleDuration(cycleType, month, year) {
  const cycle = calculateCycleDates(cycleType, month, year);
  const diffTime = Math.abs(cycle.endDate - cycle.startDate);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 لتضمين اليوم الأخير
  return diffDays;
}

// التحقق من وقوع تاريخ معين ضمن دورة
export function isDateInCycle(date, cycleType, month, year) {
  const cycle = calculateCycleDates(cycleType, month, year);
  return date >= cycle.startDate && date <= cycle.endDate;
}

// إنشاء خيارات الفلتر للواجهة
export function createFilterOptions(cycleType, isArabic = true) {
  const currentCycle = getCurrentCycle(cycleType);
  const availableCycles = getAvailableCycles(cycleType);
  
  return {
    current: {
      value: 'current',
      label: isArabic ? 'الدورة الحالية' : 'Current Cycle',
      description: currentCycle.description
    },
    cycles: availableCycles.map(cycle => ({
      value: cycle.value,
      label: cycle.label,
      description: cycle.description
    }))
  };
}
