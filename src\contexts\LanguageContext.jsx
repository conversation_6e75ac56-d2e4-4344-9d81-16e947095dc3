'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

const LanguageContext = createContext();

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState('ar');
  const [isRTL, setIsRTL] = useState(true);

  useEffect(() => {
    // جلب اللغة المحفوظة من localStorage
    const savedLanguage = localStorage.getItem('language');
    if (savedLanguage) {
      setLanguage(savedLanguage);
      setIsRTL(savedLanguage === 'ar');
      
      // تطبيق اتجاه النص على الصفحة
      document.documentElement.dir = savedLanguage === 'ar' ? 'rtl' : 'ltr';
      document.documentElement.lang = savedLanguage;
    }
  }, []);

  const toggleLanguage = () => {
    const newLanguage = language === 'ar' ? 'en' : 'ar';
    const newIsRTL = newLanguage === 'ar';
    
    setLanguage(newLanguage);
    setIsRTL(newIsRTL);
    
    // حفظ اللغة في localStorage
    localStorage.setItem('language', newLanguage);
    
    // تطبيق اتجاه النص على الصفحة
    document.documentElement.dir = newIsRTL ? 'rtl' : 'ltr';
    document.documentElement.lang = newLanguage;
  };

  const value = {
    language,
    isRTL,
    toggleLanguage,
    isArabic: language === 'ar',
    isEnglish: language === 'en'
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

// ملف الترجمات
export const translations = {
  ar: {
    // Navigation
    dashboard: 'لوحة التحكم',
    employees: 'الموظفين',
    leaves: 'الإجازات',
    cars: 'السيارات',
    apartments: 'الشقق',
    costs: 'التكاليف',
    settings: 'الإعدادات',
    logout: 'تسجيل الخروج',
    
    // Employee Management
    employeeManagement: 'إدارة الموظفين',
    addEmployee: 'إضافة موظف',
    searchEmployee: 'البحث عن موظف',
    employeeArchive: 'أرشيف الموظفين',
    employeeCode: 'كود الموظف',
    employeeName: 'اسم الموظف',
    firstName: 'الاسم الأول',
    lastName: 'الاسم الأخير',
    jobTitle: 'المسمى الوظيفي',
    department: 'القسم',
    
    // Leave Management
    leaveManagement: 'إدارة الإجازات',
    newLeaveRequest: 'طلب إجازة جديد',
    leaveRequests: 'طلبات الإجازات',
    leaveBalance: 'رصيد الإجازات',
    leaveType: 'نوع الإجازة',
    startDate: 'تاريخ البداية',
    endDate: 'تاريخ النهاية',
    duration: 'مدة الإجازة',
    reason: 'سبب الإجازة',
    status: 'الحالة',
    
    // Leave Types
    annualLeave: 'إجازة سنوية عادية',
    unpaidLeave: 'إجازة بدون أجر',
    sickLeave: 'إجازة مرضية',
    maternityLeave: 'إجازة أمومة',
    paternityLeave: 'إجازة أبوة',
    other: 'أخرى',
    
    // Status
    pending: 'قيد المراجعة',
    approved: 'معتمد',
    rejected: 'مرفوض',
    
    // Actions
    save: 'حفظ',
    cancel: 'إلغاء',
    edit: 'تعديل',
    delete: 'حذف',
    view: 'عرض',
    print: 'طباعة',
    export: 'تصدير',
    search: 'بحث',
    add: 'إضافة',
    submit: 'إرسال',
    approve: 'اعتماد',
    reject: 'رفض',
    
    // Messages
    success: 'تم بنجاح',
    error: 'حدث خطأ',
    loading: 'جاري التحميل...',
    noData: 'لا توجد بيانات',
    confirmDelete: 'هل أنت متأكد من الحذف؟',
    
    // Form Labels
    required: 'مطلوب',
    optional: 'اختياري',
    pleaseSelect: 'يرجى الاختيار',
    
    // Company Info
    companyName: 'شركة كونكورد للهندسة والمقاولات',
    companyNameEn: 'Concord for Engineering & Contracting',
    
    // Leave Form
    leaveRequestForm: 'نموذج طلب الإجازات',
    lastLeaveDate: 'تاريخ آخر إجازة',
    requestAfterWeeklyLeave: 'الطلب من الإجازة الاعتيادية بعد الإجازة الأسبوعية',
    employeeSignature: 'توقيع الموظف',
    directManagerApproval: 'اعتماد الرئيس المباشر',
    projectManagerApproval: 'اعتماد مدير المشروع',
    hrManagerApproval: 'المدير الإداري',
    hrOperations: 'عمليات الموارد البشرية',
    notes: 'ملاحظات',
    
    // Statistics
    totalEmployees: 'إجمالي الموظفين',
    totalRequests: 'إجمالي الطلبات',
    pendingRequests: 'طلبات قيد المراجعة',
    approvedRequests: 'طلبات معتمدة',
    rejectedRequests: 'طلبات مرفوضة',
    lowBalance: 'رصيد منخفض',
    expiredBalance: 'رصيد منتهي',
    averageBalance: 'متوسط الرصيد المتبقي',

    // Dashboard
    'dashboard.title': 'لوحة التحكم الرئيسية',
    'dashboard.subtitle': 'نظرة شاملة على جميع أنشطة الشركة والإحصائيات المالية',
    'dashboard.lastUpdate': 'آخر تحديث',
    'dashboard.loading': 'جاري التحميل...',
    'dashboard.connected': 'متصل بقاعدة البيانات',
    'dashboard.totalEmployees': 'إجمالي الموظفين',
    'dashboard.active': 'نشط',
    'dashboard.resident': 'مقيم'
  },
  
  en: {
    // Navigation
    dashboard: 'Dashboard',
    employees: 'Employees',
    leaves: 'Leaves',
    cars: 'Cars',
    apartments: 'Apartments',
    costs: 'Costs',
    settings: 'Settings',
    logout: 'Logout',
    
    // Employee Management
    employeeManagement: 'Employee Management',
    addEmployee: 'Add Employee',
    searchEmployee: 'Search Employee',
    employeeArchive: 'Employee Archive',
    employeeCode: 'Employee Code',
    employeeName: 'Employee Name',
    firstName: 'First Name',
    lastName: 'Last Name',
    jobTitle: 'Job Title',
    department: 'Department',
    
    // Leave Management
    leaveManagement: 'Leave Management',
    newLeaveRequest: 'New Leave Request',
    leaveRequests: 'Leave Requests',
    leaveBalance: 'Leave Balance',
    leaveType: 'Leave Type',
    startDate: 'Start Date',
    endDate: 'End Date',
    duration: 'Duration',
    reason: 'Reason',
    status: 'Status',
    
    // Leave Types
    annualLeave: 'Annual Leave',
    unpaidLeave: 'Unpaid Leave',
    sickLeave: 'Sick Leave',
    maternityLeave: 'Maternity Leave',
    paternityLeave: 'Paternity Leave',
    other: 'Other',
    
    // Status
    pending: 'Pending',
    approved: 'Approved',
    rejected: 'Rejected',
    
    // Actions
    save: 'Save',
    cancel: 'Cancel',
    edit: 'Edit',
    delete: 'Delete',
    view: 'View',
    print: 'Print',
    export: 'Export',
    search: 'Search',
    add: 'Add',
    submit: 'Submit',
    approve: 'Approve',
    reject: 'Reject',
    
    // Messages
    success: 'Success',
    error: 'Error',
    loading: 'Loading...',
    noData: 'No Data',
    confirmDelete: 'Are you sure you want to delete?',
    
    // Form Labels
    required: 'Required',
    optional: 'Optional',
    pleaseSelect: 'Please Select',
    
    // Company Info
    companyName: 'Concord for Engineering & Contracting',
    companyNameEn: 'شركة كونكورد للهندسة والمقاولات',
    
    // Leave Form
    leaveRequestForm: 'Leave Request Form',
    lastLeaveDate: 'Last Leave Date',
    requestAfterWeeklyLeave: 'Request for regular leave after weekly leave',
    employeeSignature: 'Employee Signature',
    directManagerApproval: 'Direct Manager Approval',
    projectManagerApproval: 'Project Manager Approval',
    hrManagerApproval: 'HR Manager',
    hrOperations: 'HR Operations',
    notes: 'Notes',
    
    // Statistics
    totalEmployees: 'Total Employees',
    totalRequests: 'Total Requests',
    pendingRequests: 'Pending Requests',
    approvedRequests: 'Approved Requests',
    rejectedRequests: 'Rejected Requests',
    lowBalance: 'Low Balance',
    expiredBalance: 'Expired Balance',
    averageBalance: 'Average Remaining Balance',

    // Dashboard
    'dashboard.title': 'Main Dashboard',
    'dashboard.subtitle': 'Comprehensive overview of all company activities and financial statistics',
    'dashboard.lastUpdate': 'Last Update',
    'dashboard.loading': 'Loading...',
    'dashboard.connected': 'Connected to Database',
    'dashboard.totalEmployees': 'Total Employees',
    'dashboard.active': 'Active',
    'dashboard.resident': 'Resident'
  }
};

// Hook للحصول على النص المترجم
export const useTranslation = () => {
  const { language } = useLanguage();
  
  const t = (key) => {
    return translations[language][key] || key;
  };
  
  return { t };
};
