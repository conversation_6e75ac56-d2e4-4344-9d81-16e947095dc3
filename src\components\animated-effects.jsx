'use client';
import React, { useState } from 'react';

function StoryComponent() {
  const [visible1, setVisible1] = useState(true);
  const [visible2, setVisible2] = useState(true);
  const [visible3, setVisible3] = useState(true);

  return (
    <div>
      <button onClick={() => setVisible1(!visible1)}>Toggle 1</button>
      <button onClick={() => setVisible2(!visible2)}>Toggle 2</button>
      <button onClick={() => setVisible3(!visible3)}>Toggle 3</button>
    </div>
  );
}

export default StoryComponent;
