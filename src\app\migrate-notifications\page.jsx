'use client';
import React, { useState } from 'react';
import MainLayout from '@/components/MainLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTheme } from '@/contexts/ThemeContext';
import { RefreshCw, Trash2, Plus, CheckCircle, AlertTriangle } from 'lucide-react';

export default function MigrateNotificationsPage() {
  const { isArabic } = useLanguage();
  const { isDarkMode } = useTheme();
  
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);

  // تشغيل ترحيل الإشعارات
  const migrateNotifications = async () => {
    setLoading(true);
    setResult(null);

    try {
      const response = await fetch('/api/migrate-notifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      const data = await response.json();
      setResult(data);

    } catch (error) {

      setResult({ success: false, error: error.message });
    }

    setLoading(false);
  };

  // إنشاء إشعار تجريبي جديد
  const createTestNotification = async () => {
    setLoading(true);

    try {
      const userInfo = localStorage.getItem('userInfo');
      const userCode = userInfo ? JSON.parse(userInfo).code || '1450' : '1450';

      const response = await fetch('/api/paper-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-code': userCode
        },
        body: JSON.stringify({
          action: 'create',
          requestType: 'leave',
          employeeName: 'موظف تجريبي جديد',
          employeeId: 'TEST' + Date.now(),
          leaveType: 'إعتيادية',
          startDate: new Date().toLocaleDateString('en-GB'),
          totalDays: '4',
          department: 'قسم تجريبي',
          jobTitle: 'موظف تجريبي'
        })
      });

      const data = await response.json();
      setResult(data);

    } catch (error) {

      setResult({ success: false, error: error.message });
    }

    setLoading(false);
  };

  return (
    <MainLayout>
      <div className="max-w-4xl mx-auto">
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="flex items-center gap-3">
            <RefreshCw className="w-8 h-8 text-blue-600" />
            <div>
              <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
                ترحيل الإشعارات إلى التنسيق الجديد
              </h1>
              <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                حذف الإشعارات القديمة وإنشاء إشعارات جديدة بالتنسيق المطلوب
              </p>
            </div>
          </div>
        </div>

        {/* تحذير */}
        <div className={`${isDarkMode ? 'bg-orange-900/20 border-orange-500' : 'bg-orange-50 border-orange-200'} rounded-lg border p-6 mb-6`}>
          <div className="flex items-center gap-3">
            <AlertTriangle className="w-6 h-6 text-orange-500" />
            <div>
              <h3 className={`font-semibold ${isDarkMode ? 'text-orange-400' : 'text-orange-800'} mb-2`}>
                تحذير مهم
              </h3>
              <p className={`text-sm ${isDarkMode ? 'text-orange-300' : 'text-orange-700'}`}>
                هذه العملية ستحذف جميع الإشعارات القديمة المتعلقة بالإجازات وتنشئ إشعارات تجريبية جديدة بالتنسيق المطلوب.
              </p>
            </div>
          </div>
        </div>

        {/* أزرار العمليات */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <h2 className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-4`}>
            العمليات المتاحة
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button
              onClick={migrateNotifications}
              disabled={loading}
              className="bg-red-600 hover:bg-red-700 text-white px-6 py-4 rounded-lg flex items-center justify-center gap-3 transition-colors disabled:opacity-50"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              ) : (
                <Trash2 className="w-5 h-5" />
              )}
              <div className="text-left">
                <div className="font-semibold">تنظيف وترحيل الإشعارات</div>
                <div className="text-sm opacity-90">حذف القديم وإنشاء إشعارات تجريبية</div>
              </div>
            </button>

            <button
              onClick={createTestNotification}
              disabled={loading}
              className="bg-green-600 hover:bg-green-700 text-white px-6 py-4 rounded-lg flex items-center justify-center gap-3 transition-colors disabled:opacity-50"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
              ) : (
                <Plus className="w-5 h-5" />
              )}
              <div className="text-left">
                <div className="font-semibold">إنشاء إشعار تجريبي جديد</div>
                <div className="text-sm opacity-90">طلب إجازة بالتنسيق الجديد</div>
              </div>
            </button>
          </div>
        </div>

        {/* نتيجة العملية */}
        {result && (
          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
            <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-4`}>
              نتيجة العملية
            </h3>
            
            <div className={`p-4 rounded-lg border ${
              result.success 
                ? isDarkMode ? 'bg-green-900/20 border-green-500' : 'bg-green-50 border-green-200'
                : isDarkMode ? 'bg-red-900/20 border-red-500' : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-center gap-2 mb-2">
                {result.success ? (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                ) : (
                  <AlertTriangle className="w-5 h-5 text-red-500" />
                )}
                <span className={`font-medium ${
                  result.success 
                    ? isDarkMode ? 'text-green-400' : 'text-green-800'
                    : isDarkMode ? 'text-red-400' : 'text-red-800'
                }`}>
                  {result.success ? 'نجحت العملية!' : 'فشلت العملية!'}
                </span>
              </div>
              
              <p className={`text-sm ${
                result.success 
                  ? isDarkMode ? 'text-green-300' : 'text-green-700'
                  : isDarkMode ? 'text-red-300' : 'text-red-700'
              }`}>
                {result.success 
                  ? result.message || 'تمت العملية بنجاح'
                  : `خطأ: ${result.error || result.message}`
                }
              </p>

              {result.requestId && (
                <p className={`text-xs mt-2 ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                  رقم الطلب: {result.requestId}
                </p>
              )}
            </div>
          </div>
        )}

        {/* تعليمات */}
        <div className={`${isDarkMode ? 'bg-blue-900/20 border-blue-500' : 'bg-blue-50 border-blue-200'} rounded-lg border p-6`}>
          <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-blue-400' : 'text-blue-800'} mb-3`}>
            خطوات الاختبار:
          </h3>
          <ol className={`list-decimal list-inside space-y-2 text-sm ${isDarkMode ? 'text-blue-300' : 'text-blue-700'}`}>
            <li>اضغط على "تنظيف وترحيل الإشعارات" لحذف الإشعارات القديمة وإنشاء إشعارات تجريبية</li>
            <li>تحقق من مركز الإشعارات في الشريط العلوي - يجب أن تظهر الإشعارات بالتنسيق الجديد</li>
            <li>اضغط على "إنشاء إشعار تجريبي جديد" لإنشاء إشعار جديد بالتنسيق المطلوب</li>
            <li>تأكد من أن الإشعارات تظهر بالشكل: "تم تقديم إجازة [نوع] لـ ([اسم]) ([أيام] أيام) تبدأ من ([تاريخ]) بواسطة ([مسؤول])"</li>
            <li>انتقل إلى صفحة "الإشعارات الذكية" لرؤية جميع الإشعارات</li>
          </ol>
        </div>
      </div>
    </MainLayout>
  );
}
