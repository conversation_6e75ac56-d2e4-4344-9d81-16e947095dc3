import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const requestId = searchParams.get('requestId');
    const sourceTable = searchParams.get('sourceTable') || 'auto'; // auto, PaperRequests, LeaveRequests

    if (!requestId) {
      return NextResponse.json({
        success: false,
        error: 'معرف الطلب مطلوب'
      }, { status: 400 });
    }

    const pool = await getConnection();
    let requestData = null;

    // البحث في الجدولين إذا لم يتم تحديد المصدر
    if (sourceTable === 'auto') {
      // البحث في PaperRequests أولاً
      try {
        const paperResult = await pool.request()
          .input('requestId', sql.Int, requestId)
          .query(`
            SELECT 
              'PaperRequests' as SourceTable,
              ID, EmployeeCode, EmployeeName, Department, JobTitle,
              RequestType, LeaveType, StartDate, EndDate, DaysCount,
              LeaveReason as Reason, Status, RequestDate, ApprovalDate, ApprovedBy
            FROM PaperRequests 
            WHERE ID = @requestId
          `);

        if (paperResult.recordset.length > 0) {
          requestData = paperResult.recordset[0];
        }
      } catch (error) {

      }

      // إذا لم يتم العثور عليه، ابحث في LeaveRequests
      if (!requestData) {
        try {
          const leaveResult = await pool.request()
            .input('requestId', sql.Int, requestId)
            .query(`
              SELECT
                'LeaveRequests' as SourceTable,
                ID, EmployeeCode, EmployeeName,
                ISNULL(Department, '') as Department,
                ISNULL(JobTitle, '') as JobTitle,
                'leave' as RequestType, LeaveType, StartDate, EndDate, DaysCount,
                Reason, Status, RequestDate
              FROM LeaveRequests
              WHERE ID = @requestId
            `);

          if (leaveResult.recordset.length > 0) {
            requestData = leaveResult.recordset[0];
          }
        } catch (error) {

        }
      }
    } else if (sourceTable === 'PaperRequests') {
      // البحث في PaperRequests فقط
      const result = await pool.request()
        .input('requestId', sql.Int, requestId)
        .query(`
          SELECT 
            'PaperRequests' as SourceTable,
            ID, EmployeeCode, EmployeeName, Department, JobTitle,
            RequestType, LeaveType, StartDate, EndDate, DaysCount,
            LeaveReason as Reason, Status, RequestDate, ApprovalDate, ApprovedBy
          FROM PaperRequests 
          WHERE ID = @requestId
        `);

      if (result.recordset.length > 0) {
        requestData = result.recordset[0];
      }
    } else if (sourceTable === 'LeaveRequests') {
      // البحث في LeaveRequests فقط
      const result = await pool.request()
        .input('requestId', sql.Int, requestId)
        .query(`
          SELECT
            'LeaveRequests' as SourceTable,
            ID, EmployeeCode, EmployeeName,
            ISNULL(Department, '') as Department,
            ISNULL(JobTitle, '') as JobTitle,
            'leave' as RequestType, LeaveType, StartDate, EndDate, DaysCount,
            Reason, Status, RequestDate
          FROM LeaveRequests
          WHERE ID = @requestId
        `);

      if (result.recordset.length > 0) {
        requestData = result.recordset[0];
      }
    }

    if (!requestData) {
      return NextResponse.json({
        success: false,
        error: `لم يتم العثور على الطلب رقم ${requestId} في أي من الجدولين`
      }, { status: 404 });
    }

    // جلب بيانات إضافية للطباعة (رصيد الإجازات، آخر إجازة، إلخ)
    let additionalData = {};

    if (requestData.RequestType === 'leave' && requestData.EmployeeCode) {
      try {
        // جلب رصيد الإجازات
        const balanceResult = await pool.request()
          .input('employeeCode', sql.NVarChar, requestData.EmployeeCode)
          .query(`
            SELECT AnnualBalance, CasualBalance, SickBalance 
            FROM LeaveBalances 
            WHERE EmployeeCode = @employeeCode
          `);

        if (balanceResult.recordset.length > 0) {
          additionalData.leaveBalance = balanceResult.recordset[0];
        }

        // جلب آخر إجازة معتمدة
        const lastLeaveResult = await pool.request()
          .input('employeeCode', sql.NVarChar, requestData.EmployeeCode)
          .input('currentRequestId', sql.Int, requestId)
          .query(`
            SELECT TOP 1 EndDate 
            FROM (
              SELECT EndDate FROM PaperRequests 
              WHERE EmployeeCode = @employeeCode 
              AND RequestType = 'leave' 
              AND Status = 'معتمدة' 
              AND ID != @currentRequestId
              UNION ALL
              SELECT EndDate FROM LeaveRequests 
              WHERE EmployeeCode = @employeeCode 
              AND Status = 'معتمدة' 
              AND ID != @currentRequestId
            ) AS AllLeaves
            ORDER BY EndDate DESC
          `);

        if (lastLeaveResult.recordset.length > 0) {
          additionalData.lastLeaveEndDate = lastLeaveResult.recordset[0].EndDate;
        }
      } catch (error) {

      }
    }

    // تنسيق البيانات للطباعة
    const printData = {
      ...requestData,
      ...additionalData,
      // تنسيق التواريخ
      FormattedStartDate: requestData.StartDate ? formatDateToDDMMYYYY(requestData.StartDate) : '',
      FormattedEndDate: requestData.EndDate ? formatDateToDDMMYYYY(requestData.EndDate) : '',
      FormattedRequestDate: requestData.RequestDate ? formatDateToDDMMYYYY(requestData.RequestDate) : '',
      FormattedLastLeaveEndDate: additionalData.lastLeaveEndDate ? formatDateToDDMMYYYY(additionalData.lastLeaveEndDate) : '',
      
      // حساب الرصيد المتبقي
      RemainingBalance: calculateRemainingBalance(requestData, additionalData.leaveBalance),
      
      // معلومات إضافية للطباعة
      PrintTimestamp: new Date().toLocaleString('ar-EG'),
      CanPrint: true
    };

    return NextResponse.json({
      success: true,
      data: printData,
      message: `تم جلب بيانات الطلب من ${requestData.SourceTable}`
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب بيانات الطباعة: ' + error.message
    }, { status: 500 });
  }
}

// دالة تنسيق التاريخ
function formatDateToDDMMYYYY(dateInput) {
  if (!dateInput) return '';
  const date = new Date(dateInput);
  if (isNaN(date.getTime())) return '';

  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
}

// دالة حساب الرصيد المتبقي
function calculateRemainingBalance(requestData, leaveBalance) {
  if (!leaveBalance || requestData.RequestType !== 'leave') {
    return 'غير محدد';
  }

  const leaveType = requestData.LeaveType?.toLowerCase();
  const daysCount = requestData.DaysCount || 0;

  if (leaveType === 'إعتيادية' || leaveType === 'اعتيادية' || leaveType === 'annual') {
    const remaining = (leaveBalance.AnnualBalance || 0) - daysCount;
    return remaining >= 0 ? `${remaining} يوم` : 'غير كافي';
  } else if (leaveType === 'عارضة' || leaveType === 'casual') {
    const remaining = (leaveBalance.CasualBalance || 0) - daysCount;
    return remaining >= 0 ? `${remaining} يوم` : 'غير كافي';
  } else if (leaveType === 'بدون أجر' || leaveType === 'unpaid') {
    return 'غير محدود';
  } else if (leaveType === 'مرضية' || leaveType === 'sick') {
    return 'حسب التقرير الطبي';
  }

  return 'غير محدد';
}
