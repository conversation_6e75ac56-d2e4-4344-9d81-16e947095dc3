'use client';
import React, { useState } from 'react';

function MainComponent() {
  const [selectedLang, setSelectedLang] = useState('ar');
  const [pendingRequests, setPendingRequests] = useState([
    {
      id: 1,
      employeeId: '1001',
      employeeName: 'أحمد محمد',
      type: 'annual',
      startDate: '2025-02-15',
      endDate: '2025-02-20',
      reason: 'إجازة سنوية',
      status: 'pending',
      submissionDate: '2025-02-01',
    },
    {
      id: 2,
      employeeId: '1002',
      employeeName: 'سارة خالد',
      type: 'sick',
      startDate: '2025-02-10',
      endDate: '2025-02-12',
      reason: 'ظروف صحية',
      status: 'pending',
      submissionDate: '2025-02-08',
    },
  ]);
  const [error, setError] = useState('');

  const dir = selectedLang === 'ar' ? 'rtl' : 'ltr';

  const handleApprove = async (requestId) => {
    try {
      setPendingRequests((prev) =>
        prev.filter((request) => request.id !== requestId)
      );
    } catch (error) {
      setError(
        selectedLang === 'ar'
          ? 'حدث خطأ أثناء الموافقة على الطلب'
          : 'Error approving request'
      );
    }
  };

  const handleReject = async (requestId) => {
    try {
      setPendingRequests((prev) =>
        prev.filter((request) => request.id !== requestId)
      );
    } catch (error) {
      setError(
        selectedLang === 'ar'
          ? 'حدث خطأ أثناء رفض الطلب'
          : 'Error rejecting request'
      );
    }
  };

  const exportToExcel = async () => {
    try {
      const response = await fetch('/api/excel', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'export',
          data: pendingRequests,
          template: {
            employeeId: selectedLang === 'ar' ? 'رقم الموظف' : 'Employee ID',
            employeeName:
              selectedLang === 'ar' ? 'اسم الموظف' : 'Employee Name',
            type: selectedLang === 'ar' ? 'نوع الإجازة' : 'Leave Type',
            startDate: selectedLang === 'ar' ? 'تاريخ البداية' : 'Start Date',
            endDate: selectedLang === 'ar' ? 'تاريخ النهاية' : 'End Date',
            reason: selectedLang === 'ar' ? 'السبب' : 'Reason',
            status: selectedLang === 'ar' ? 'الحالة' : 'Status',
            submissionDate:
              selectedLang === 'ar' ? 'تاريخ التقديم' : 'Submission Date',
          },
        }),
      });

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error);
      }
    } catch (error) {
      setError(error.message);
    }
  };

  return (
    <div
      dir={dir}
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {selectedLang === 'ar'
              ? 'طلبات الإجازات المعلقة'
              : 'Pending Leave Requests'}
          </h1>
          <div className="flex gap-4">
            <button
              onClick={exportToExcel}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              {selectedLang === 'ar' ? 'تصدير إلى Excel' : 'Export to Excel'}
            </button>
            <button
              onClick={() =>
                setSelectedLang(selectedLang === 'ar' ? 'en' : 'ar')
              }
              className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
            >
              {selectedLang === 'ar' ? 'English' : 'العربية'}
            </button>
          </div>
        </div>

        {error && (
          <div className="mb-4 p-4 bg-red-100 text-red-700 rounded-md">
            {error}
          </div>
        )}

        <div className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-100 dark:bg-gray-700">
              <tr>
                <th className="px-4 py-2 text-right">
                  {selectedLang === 'ar' ? 'رقم الموظف' : 'Employee ID'}
                </th>
                <th className="px-4 py-2 text-right">
                  {selectedLang === 'ar' ? 'اسم الموظف' : 'Employee Name'}
                </th>
                <th className="px-4 py-2 text-right">
                  {selectedLang === 'ar' ? 'نوع الإجازة' : 'Leave Type'}
                </th>
                <th className="px-4 py-2 text-right">
                  {selectedLang === 'ar' ? 'تاريخ البداية' : 'Start Date'}
                </th>
                <th className="px-4 py-2 text-right">
                  {selectedLang === 'ar' ? 'تاريخ النهاية' : 'End Date'}
                </th>
                <th className="px-4 py-2 text-right">
                  {selectedLang === 'ar' ? 'السبب' : 'Reason'}
                </th>
                <th className="px-4 py-2 text-right">
                  {selectedLang === 'ar' ? 'الإجراءات' : 'Actions'}
                </th>
              </tr>
            </thead>
            <tbody>
              {pendingRequests.map((request) => (
                <tr key={request.id} className="border-b">
                  <td className="px-4 py-2">{request.employeeId}</td>
                  <td className="px-4 py-2">{request.employeeName}</td>
                  <td className="px-4 py-2">
                    {selectedLang === 'ar'
                      ? request.type === 'annual'
                        ? 'سنوية'
                        : 'مرضية'
                      : request.type === 'annual'
                        ? 'Annual'
                        : 'Sick'}
                  </td>
                  <td className="px-4 py-2">{request.startDate}</td>
                  <td className="px-4 py-2">{request.endDate}</td>
                  <td className="px-4 py-2">{request.reason}</td>
                  <td className="px-4 py-2">
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleApprove(request.id)}
                        className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700"
                      >
                        {selectedLang === 'ar' ? 'موافقة' : 'Approve'}
                      </button>
                      <button
                        onClick={() => handleReject(request.id)}
                        className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700"
                      >
                        {selectedLang === 'ar' ? 'رفض' : 'Reject'}
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {pendingRequests.length === 0 && (
            <div className="p-4 text-center text-gray-500">
              {selectedLang === 'ar'
                ? 'لا توجد طلبات إجازة معلقة'
                : 'No pending leave requests'}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default MainComponent;
