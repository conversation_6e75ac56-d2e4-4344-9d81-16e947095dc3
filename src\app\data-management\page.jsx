'use client';

import { useState, useEffect } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import MainLayout from '@/components/MainLayout';
import {
  FiDatabase, FiRefreshCw, FiCheck, FiX, FiAlertTriangle, 
  FiInfo, FiArrowRight, FiSettings, FiSearch
} from 'react-icons/fi';

export default function DataManagementPage() {
  const { isArabic } = useLanguage();
  const [loading, setLoading] = useState(false);
  const [tablesStatus, setTablesStatus] = useState(null);
  const [migrationResult, setMigrationResult] = useState(null);

  // فحص حالة الجدولين عند تحميل الصفحة
  useEffect(() => {
    checkTablesStatus();
  }, []);

  // فحص حالة الجدولين
  const checkTablesStatus = async () => {
    try {
      setLoading(true);

      const response = await fetch('/api/unified-requests', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'check-tables'
        })
      });

      const result = await response.json();

      if (result.success) {
        setTablesStatus(result.result);
      } else {

      }
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  // نقل البيانات
  const migrateData = async () => {
    if (!confirm('هل أنت متأكد من نقل جميع البيانات من جدول LeaveRequests إلى جدول PaperRequests؟\n\nهذا سيوحد جميع الطلبات في مكان واحد ولن يؤثر على البيانات الأصلية.')) {
      return;
    }

    try {
      setLoading(true);
      setMigrationResult(null);

      const response = await fetch('/api/unified-requests', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'migrate-data'
        })
      });

      const result = await response.json();
      setMigrationResult(result);

      if (result.success) {
        // إعادة فحص حالة الجدولين بعد النقل
        await checkTablesStatus();
      }
    } catch (error) {

      setMigrationResult({
        success: false,
        error: 'خطأ في نقل البيانات: ' + error.message
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <MainLayout>
      <div className="p-6 max-w-6xl mx-auto">
        {/* العنوان */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <FiDatabase className="text-3xl text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                {isArabic ? 'إدارة البيانات' : 'Data Management'}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {isArabic ? 'إدارة وتوحيد بيانات الطلبات من الجدولين' : 'Manage and unify request data from both tables'}
              </p>
            </div>
          </div>

          <button
            onClick={checkTablesStatus}
            disabled={loading}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            <FiRefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            {isArabic ? 'تحديث الحالة' : 'Refresh Status'}
          </button>
        </div>

        {/* حالة الجدولين */}
        {tablesStatus && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            {/* جدول LeaveRequests */}
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
              <div className="flex items-center gap-3 mb-4">
                <FiDatabase className="text-2xl text-blue-600" />
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
                  جدول LeaveRequests (القديم)
                </h3>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">الحالة:</span>
                  <span className={`flex items-center gap-1 ${
                    tablesStatus.tables.LeaveRequests ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {tablesStatus.tables.LeaveRequests ? <FiCheck /> : <FiX />}
                    {tablesStatus.tables.LeaveRequests ? 'موجود' : 'غير موجود'}
                  </span>
                </div>

                {tablesStatus.tables.LeaveRequests && (
                  <>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">عدد الأعمدة:</span>
                      <span className="font-medium">{tablesStatus.tables.LeaveRequests.columnCount}</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">عدد الطلبات:</span>
                      <span className="font-medium text-blue-600">
                        {tablesStatus.counts.LeaveRequests || 0} طلب
                      </span>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* جدول PaperRequests */}
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
              <div className="flex items-center gap-3 mb-4">
                <FiDatabase className="text-2xl text-green-600" />
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
                  جدول PaperRequests (الجديد)
                </h3>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600 dark:text-gray-400">الحالة:</span>
                  <span className={`flex items-center gap-1 ${
                    tablesStatus.tables.PaperRequests ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {tablesStatus.tables.PaperRequests ? <FiCheck /> : <FiX />}
                    {tablesStatus.tables.PaperRequests ? 'موجود' : 'غير موجود'}
                  </span>
                </div>

                {tablesStatus.tables.PaperRequests && (
                  <>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">عدد الأعمدة:</span>
                      <span className="font-medium">{tablesStatus.tables.PaperRequests.columnCount}</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 dark:text-gray-400">عدد الطلبات:</span>
                      <span className="font-medium text-green-600">
                        {tablesStatus.counts.PaperRequests || 0} طلب
                      </span>
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        )}

        {/* التوصيات */}
        {tablesStatus && tablesStatus.recommendations && tablesStatus.recommendations.length > 0 && (
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-6 mb-8">
            <div className="flex items-center gap-3 mb-4">
              <FiAlertTriangle className="text-2xl text-yellow-600" />
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">
                {isArabic ? 'التوصيات' : 'Recommendations'}
              </h3>
            </div>

            <ul className="space-y-2">
              {tablesStatus.recommendations.map((recommendation, index) => (
                <li key={index} className="flex items-start gap-2 text-yellow-700 dark:text-yellow-300">
                  <FiInfo className="w-4 h-4 mt-0.5 flex-shrink-0" />
                  <span>{recommendation}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* عملية نقل البيانات */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm mb-8">
          <div className="flex items-center gap-3 mb-4">
            <FiArrowRight className="text-2xl text-purple-600" />
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
              {isArabic ? 'نقل البيانات' : 'Data Migration'}
            </h3>
          </div>

          <p className="text-gray-600 dark:text-gray-400 mb-6">
            {isArabic 
              ? 'نقل جميع الطلبات من جدول LeaveRequests القديم إلى جدول PaperRequests الجديد لتوحيد البيانات.'
              : 'Transfer all requests from the old LeaveRequests table to the new PaperRequests table to unify data.'
            }
          </p>

          <div className="flex items-center gap-4">
            <button
              onClick={migrateData}
              disabled={loading || !tablesStatus?.tables?.LeaveRequests || !tablesStatus?.tables?.PaperRequests}
              className="flex items-center gap-2 px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <FiArrowRight className="w-4 h-4" />
              {loading ? 'جاري النقل...' : 'نقل البيانات'}
            </button>

            {(!tablesStatus?.tables?.LeaveRequests || !tablesStatus?.tables?.PaperRequests) && (
              <span className="text-sm text-red-600 dark:text-red-400">
                يجب أن يكون كلا الجدولين موجودين لتنفيذ عملية النقل
              </span>
            )}
          </div>
        </div>

        {/* نتيجة النقل */}
        {migrationResult && (
          <div className={`rounded-lg p-6 shadow-sm ${
            migrationResult.success 
              ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700'
              : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700'
          }`}>
            <div className="flex items-center gap-3 mb-4">
              {migrationResult.success ? (
                <FiCheck className="text-2xl text-green-600" />
              ) : (
                <FiX className="text-2xl text-red-600" />
              )}
              <h3 className={`text-lg font-semibold ${
                migrationResult.success ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200'
              }`}>
                {migrationResult.success ? 'نجح النقل' : 'فشل النقل'}
              </h3>
            </div>

            <p className={`${
              migrationResult.success ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'
            }`}>
              {migrationResult.success 
                ? `تم نقل ${migrationResult.migratedCount} طلب بنجاح من LeaveRequests إلى PaperRequests`
                : migrationResult.error
              }
            </p>
          </div>
        )}

        {/* معلومات إضافية */}
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-6">
          <div className="flex items-center gap-3 mb-4">
            <FiInfo className="text-2xl text-blue-600" />
            <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200">
              {isArabic ? 'معلومات مهمة' : 'Important Information'}
            </h3>
          </div>

          <ul className="space-y-2 text-blue-700 dark:text-blue-300">
            <li className="flex items-start gap-2">
              <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></span>
              <span>عملية النقل آمنة ولا تحذف البيانات الأصلية من جدول LeaveRequests</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></span>
              <span>يتم تجنب نقل الطلبات المكررة تلقائياً</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></span>
              <span>بعد النقل، ستظهر جميع الطلبات في صفحة "طلباتي" موحدة</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></span>
              <span>يمكن تشغيل عملية النقل عدة مرات بأمان</span>
            </li>
          </ul>
        </div>
      </div>
    </MainLayout>
  );
}
