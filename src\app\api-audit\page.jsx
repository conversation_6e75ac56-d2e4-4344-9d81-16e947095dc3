'use client';

import React, { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import {
  FiCode,
  FiCheckCircle,
  FiAlertTriangle,
  FiRefreshCw,
  FiTool,
  FiPlay,
  FiList,
  FiSettings
} from 'react-icons/fi';

export default function APIAuditPage() {
  const { isArabic } = useLanguage();
  const [auditData, setAuditData] = useState(null);
  const [testResults, setTestResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);

  // جلب تقرير المراجعة
  const fetchAuditReport = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/fix-all-apis?action=audit');
      const result = await response.json();
      
      if (result.success) {
        setAuditData(result.data);
      } else {
        alert('خطأ في جلب التقرير: ' + result.message);
      }
    } catch (error) {

      alert('خطأ في الاتصال بالخادم');
    } finally {
      setLoading(false);
    }
  };

  // اختبار APIs
  const testAPIs = async () => {
    setTesting(true);
    try {
      const response = await fetch('/api/fix-all-apis?action=test');
      const result = await response.json();
      
      if (result.success) {
        setTestResults(result);
      } else {
        alert('خطأ في اختبار APIs: ' + result.message);
      }
    } catch (error) {

      alert('خطأ في الاتصال بالخادم');
    } finally {
      setTesting(false);
    }
  };

  useEffect(() => {
    fetchAuditReport();
  }, []);

  const getStatusColor = (status) => {
    switch (status) {
      case 'نجح': return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      case 'فشل': return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      case 'يحتاج تدخل يدوي': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20';
    }
  };

  return (
    <MainLayout>
      <div className="p-6 max-w-7xl mx-auto">
        {/* العنوان */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FiCode className="text-3xl text-blue-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                  {isArabic ? 'مراجعة جميع APIs' : 'All APIs Audit'}
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  {isArabic ? 'مراجعة شاملة لجميع APIs للتوافق مع التسميات الموحدة' : 'Comprehensive review of all APIs for unified naming compatibility'}
                </p>
              </div>
            </div>
            
            <div className="flex gap-3">
              <button
                onClick={fetchAuditReport}
                disabled={loading}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 flex items-center gap-2"
              >
                <FiRefreshCw className={loading ? 'animate-spin' : ''} />
                {loading ? (isArabic ? 'جاري المراجعة...' : 'Auditing...') : (isArabic ? 'إعادة مراجعة' : 'Re-audit')}
              </button>
              
              <button
                onClick={testAPIs}
                disabled={testing}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 flex items-center gap-2"
              >
                <FiPlay className={testing ? 'animate-spin' : ''} />
                {testing ? (isArabic ? 'جاري الاختبار...' : 'Testing...') : (isArabic ? 'اختبار APIs' : 'Test APIs')}
              </button>
            </div>
          </div>
        </div>

        {loading && !auditData && (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            <span className="mr-3 text-gray-600 dark:text-gray-400">
              {isArabic ? 'جاري مراجعة APIs...' : 'Auditing APIs...'}
            </span>
          </div>
        )}

        {auditData && (
          <div className="space-y-6">
            {/* ملخص المراجعة */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <div className="flex items-center gap-3">
                  <FiList className="text-2xl text-blue-600" />
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {isArabic ? 'إجمالي APIs' : 'Total APIs'}
                    </p>
                    <p className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                      {auditData.totalAPIs}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <div className="flex items-center gap-3">
                  <FiAlertTriangle className="text-2xl text-yellow-600" />
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {isArabic ? 'APIs تحتاج إصلاح' : 'APIs Need Fixing'}
                    </p>
                    <p className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                      {auditData.apisWithIssues}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <div className="flex items-center gap-3">
                  <FiCheckCircle className="text-2xl text-green-600" />
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {isArabic ? 'APIs سليمة' : 'Healthy APIs'}
                    </p>
                    <p className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                      {auditData.totalAPIs - auditData.apisWithIssues}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* قائمة APIs */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
                  <FiCode className="text-blue-600" />
                  {isArabic ? 'تفاصيل APIs' : 'APIs Details'}
                </h3>
              </div>
              
              <div className="p-6">
                <div className="space-y-4">
                  {auditData.apis.map((api, index) => (
                    <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div>
                          <h4 className="font-medium text-gray-800 dark:text-gray-200">
                            {api.name}
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {api.path}
                          </p>
                        </div>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                          api.issues.length === 0 
                            ? 'text-green-600 bg-green-100 dark:bg-green-900/20'
                            : 'text-red-600 bg-red-100 dark:bg-red-900/20'
                        }`}>
                          {api.issues.length === 0 
                            ? (isArabic ? 'سليم' : 'Healthy')
                            : (isArabic ? 'يحتاج إصلاح' : 'Needs Fix')
                          }
                        </span>
                      </div>
                      
                      {api.issues.length > 0 && (
                        <div>
                          <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            {isArabic ? 'المشاكل:' : 'Issues:'}
                          </p>
                          <ul className="list-disc list-inside text-sm text-gray-600 dark:text-gray-400 space-y-1">
                            {api.issues.map((issue, issueIndex) => (
                              <li key={issueIndex}>{issue}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* نتائج الاختبار */}
            {testResults && (
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 flex items-center gap-2">
                    <FiPlay className="text-green-600" />
                    {isArabic ? 'نتائج الاختبار' : 'Test Results'}
                  </h3>
                </div>
                
                <div className="p-6">
                  {/* ملخص الاختبار */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div className="text-center">
                      <p className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                        {testResults.summary?.totalTests || 0}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {isArabic ? 'إجمالي الاختبارات' : 'Total Tests'}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-green-600">
                        {testResults.summary?.passedTests || 0}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {isArabic ? 'نجحت' : 'Passed'}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold text-red-600">
                        {testResults.summary?.failedTests || 0}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {isArabic ? 'فشلت' : 'Failed'}
                      </p>
                    </div>
                  </div>

                  {/* تفاصيل الاختبارات */}
                  <div className="space-y-3">
                    {testResults.data?.map((test, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div>
                          <h4 className="font-medium text-gray-800 dark:text-gray-200">
                            {test.api}
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {test.test}
                          </p>
                        </div>
                        <div className="flex items-center gap-3">
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {test.results} {isArabic ? 'نتيجة' : 'results'}
                          </span>
                          <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(test.status)}`}>
                            {test.status}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </MainLayout>
  );
}
