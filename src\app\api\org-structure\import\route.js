import { getConnection } from '@/lib/db';
import ExcelJS from 'exceljs';
import sql from 'mssql';
import { NextResponse } from 'next/server';

export async function POST(request) {
  try {
    const formData = await request.formData();
    const file = formData.get('file');
    const type = formData.get('type');

    if (!file) {
      return NextResponse.json({
        success: false,
        error: 'لم يتم رفع أي ملف'
      }, { status: 400 });
    }

    // قراءة ملف Excel
    const buffer = await file.arrayBuffer();
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(buffer);

    const worksheet = workbook.getWorksheet(1);
    const data = [];

    // قراءة البيانات من الورقة
    const headers = [];
    worksheet.getRow(1).eachCell((cell, colNumber) => {
      headers[colNumber] = cell.value;
    });

    worksheet.eachRow((row, rowNumber) => {
      if (rowNumber === 1) return; // تخطي الصف الأول (العناوين)

      const rowData = {};
      row.eachCell((cell, colNumber) => {
        if (headers[colNumber]) {
          rowData[headers[colNumber]] = cell.value;
        }
      });

      if (Object.keys(rowData).length > 0) {
        data.push(rowData);
      }
    });

    const pool = await getConnection();

    if (type === 'direct-managers') {
      return await importDirectManagers(pool, data);
    }

    return NextResponse.json({
      success: false,
      error: 'نوع الاستيراد غير مدعوم'
    }, { status: 400 });

  } catch (error) {
    console.error('خطأ في استيراد الملف:', error);
    return NextResponse.json({
      success: false,
      error: 'فشل في استيراد الملف: ' + error.message
    }, { status: 500 });
  }
}

// استيراد بيانات المديرين المباشرين
async function importDirectManagers(pool, data) {
  try {
    // اكتشاف العمود الصحيح لكود الموظف
    const { employeeCodeColumn } = await discoverCorrectColumns(pool);

    // التأكد من وجود الأعمدة المطلوبة
    await ensureDirectManagerColumns(pool);

    let successCount = 0;
    let errorCount = 0;
    const errors = [];

    for (const row of data) {
      try {
        // تحويل أسماء الأعمدة المختلفة إلى تنسيق موحد
        const employeeCode = row['رقم الموظف'] || row['EmployeeCode'] || row['كود الموظف'];
        const managerCode1 = row['كود المدير المباشر 1'] || row['DirectManagerCode1'] || '';
        const managerName1 = row['المدير المباشر 1'] || row['DirectManagerName1'] || '';
        const managerCode2 = row['كود المدير المباشر 2'] || row['DirectManagerCode2'] || '';
        const managerName2 = row['المدير المباشر 2'] || row['DirectManagerName2'] || '';
        const managerCode3 = row['كود المدير المباشر 3'] || row['DirectManagerCode3'] || '';
        const managerName3 = row['المدير المباشر 3'] || row['DirectManagerName3'] || '';
        const managerCode4 = row['كود المدير المباشر 4'] || row['DirectManagerCode4'] || '';
        const managerName4 = row['المدير المباشر 4'] || row['DirectManagerName4'] || '';

        if (!employeeCode) {
          errors.push(`الصف ${data.indexOf(row) + 1}: كود الموظف مفقود`);
          errorCount++;
          continue;
        }

        // تحديث بيانات الموظف
        const updateQuery = `
          UPDATE Employees SET
            DirectManagerCode1 = @managerCode1,
            DirectManagerName1 = @managerName1,
            DirectManagerCode2 = @managerCode2,
            DirectManagerName2 = @managerName2,
            DirectManagerCode3 = @managerCode3,
            DirectManagerName3 = @managerName3,
            DirectManagerCode4 = @managerCode4,
            DirectManagerName4 = @managerName4
          WHERE ${employeeCodeColumn} = @employeeCode
        `;

        const result = await pool.request()
          .input('employeeCode', sql.NVarChar, employeeCode.toString())
          .input('managerCode1', sql.NVarChar, managerCode1 ? managerCode1.toString() : null)
          .input('managerName1', sql.NVarChar, managerName1 || null)
          .input('managerCode2', sql.NVarChar, managerCode2 ? managerCode2.toString() : null)
          .input('managerName2', sql.NVarChar, managerName2 || null)
          .input('managerCode3', sql.NVarChar, managerCode3 ? managerCode3.toString() : null)
          .input('managerName3', sql.NVarChar, managerName3 || null)
          .input('managerCode4', sql.NVarChar, managerCode4 ? managerCode4.toString() : null)
          .input('managerName4', sql.NVarChar, managerName4 || null)
          .query(updateQuery);

        if (result.rowsAffected[0] > 0) {
          successCount++;
        } else {
          errors.push(`الصف ${data.indexOf(row) + 1}: لم يتم العثور على الموظف ${employeeCode}`);
          errorCount++;
        }

      } catch (rowError) {
        errors.push(`الصف ${data.indexOf(row) + 1}: ${rowError.message}`);
        errorCount++;
      }
    }

    return NextResponse.json({
      success: true,
      message: `تم تحديث ${successCount} موظف بنجاح`,
      details: {
        successCount,
        errorCount,
        errors: errors.slice(0, 10) // أول 10 أخطاء فقط
      }
    });

  } catch (error) {
    console.error('خطأ في استيراد المديرين المباشرين:', error);
    return NextResponse.json({
      success: false,
      error: 'فشل في استيراد بيانات المديرين المباشرين: ' + error.message
    }, { status: 500 });
  }
}

// اكتشاف الأعمدة الصحيحة
async function discoverCorrectColumns(pool) {
  try {
    const columnsResult = await pool.request().query(`
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'Employees'
      AND COLUMN_NAME IN ('EmployeeCode', 'EmpCode', 'Code', 'EmployeeName', 'EmpName', 'Name', 'FullName')
    `);

    const columns = columnsResult.recordset.map(row => row.COLUMN_NAME);

    // اكتشاف عمود كود الموظف
    let employeeCodeColumn = 'EmployeeCode';
    if (columns.includes('EmpCode')) {
      employeeCodeColumn = 'EmpCode';
    } else if (columns.includes('Code')) {
      employeeCodeColumn = 'Code';
    }

    // اكتشاف عمود اسم الموظف
    let employeeNameColumn = 'EmployeeName';
    if (columns.includes('EmpName')) {
      employeeNameColumn = 'EmpName';
    } else if (columns.includes('Name')) {
      employeeNameColumn = 'Name';
    } else if (columns.includes('FullName')) {
      employeeNameColumn = 'FullName';
    }

    return { employeeCodeColumn, employeeNameColumn };
  } catch (error) {
    return { employeeCodeColumn: 'EmployeeCode', employeeNameColumn: 'EmployeeName' };
  }
}

// التأكد من وجود أعمدة المديرين المباشرين
async function ensureDirectManagerColumns(pool) {
  try {
    const requiredColumns = [
      { name: 'DirectManagerCode1', type: 'NVARCHAR(50)' },
      { name: 'DirectManagerName1', type: 'NVARCHAR(255)' },
      { name: 'DirectManagerCode2', type: 'NVARCHAR(50)' },
      { name: 'DirectManagerName2', type: 'NVARCHAR(255)' },
      { name: 'DirectManagerCode3', type: 'NVARCHAR(50)' },
      { name: 'DirectManagerName3', type: 'NVARCHAR(255)' },
      { name: 'DirectManagerCode4', type: 'NVARCHAR(50)' },
      { name: 'DirectManagerName4', type: 'NVARCHAR(255)' }
    ];

    for (const column of requiredColumns) {
      const columnExists = await pool.request()
        .input('columnName', sql.NVarChar, column.name)
        .query(`
          SELECT COUNT(*) as count
          FROM INFORMATION_SCHEMA.COLUMNS
          WHERE TABLE_NAME = 'Employees' AND COLUMN_NAME = @columnName
        `);

      if (columnExists.recordset[0].count === 0) {
        await pool.request().query(`
          ALTER TABLE Employees ADD ${column.name} ${column.type} NULL
        `);
        console.log(`تم إضافة العمود: ${column.name}`);
      }
    }
  } catch (error) {
    console.error('خطأ في إضافة أعمدة المديرين المباشرين:', error);
  }
}
