const ExcelUpload = ({ onUpload, lang = 'ar' }) => {
  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const response = await fetch('/api/import-excel/consolidated', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            fileContent: e.target.result,
            lang
          })
        });
        const result = await response.json();
        onUpload(result);
      } catch (error) {
      }
    };
    reader.readAsDataURL(file);
  };

  return (
    <div className="flex items-center justify-center w-full">
      <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer hover:bg-gray-50">
        <div className="flex flex-col items-center justify-center pt-5 pb-6">
          <svg className="w-8 h-8 mb-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 16">
            <path stroke="currentColor" d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"/>
          </svg>
          <p className="mb-2 text-sm text-gray-500">
            {lang === 'ar' ? 'انقر لتحميل ملف Excel' : 'Click to upload Excel file'}
          </p>
        </div>
        <input type="file" className="hidden" accept=".xlsx,.xls,.csv" onChange={handleFileUpload} />
      </label>
    </div>
  );
};