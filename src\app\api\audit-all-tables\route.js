import { NextResponse } from 'next/server';
import sql from 'mssql';

// إعداد قاعدة البيانات
const dbConfig = {
  user: process.env.DB_USER || 'sa',
  password: process.env.DB_PASSWORD || 'ojesta',
  server: process.env.DB_SERVER || 'HR-Samy\\DBOJESTA',
  database: process.env.DB_NAME || 'OJESTA',
  options: {
    encrypt: false,
    trustServerCertificate: true,
    enableArithAbort: true,
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
};

let pool;

async function getPool() {
  if (!pool) {
    pool = await sql.connect(dbConfig);
  }
  return pool;
}

export async function GET(request) {
  try {

    const pool = await getPool();
    
    // 1. جلب جميع الجداول
    const tablesResult = await pool.request().query(`
      SELECT 
        TABLE_NAME,
        TABLE_TYPE
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_TYPE = 'BASE TABLE'
      ORDER BY TABLE_NAME
    `);

    const auditReport = {
      totalTables: tablesResult.recordset.length,
      tables: [],
      inconsistencies: [],
      recommendations: []
    };

    // 2. فحص كل جدول وأعمدته
    for (const table of tablesResult.recordset) {
      const tableName = table.TABLE_NAME;
      
      try {
        // جلب أعمدة الجدول
        const columnsResult = await pool.request()
          .input('tableName', sql.NVarChar, tableName)
          .query(`
            SELECT 
              COLUMN_NAME,
              DATA_TYPE,
              IS_NULLABLE,
              COLUMN_DEFAULT,
              CHARACTER_MAXIMUM_LENGTH
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = @tableName
            ORDER BY ORDINAL_POSITION
          `);

        const tableInfo = {
          name: tableName,
          columns: columnsResult.recordset,
          employeeCodeColumn: null,
          employeeNameColumn: null,
          apartmentCodeColumn: null,
          carCodeColumn: null,
          issues: []
        };

        // 3. فحص أعمدة الموظفين
        const employeeCodeColumns = columnsResult.recordset.filter(col => 
          col.COLUMN_NAME.toLowerCase().includes('employee') && 
          (col.COLUMN_NAME.toLowerCase().includes('code') || col.COLUMN_NAME.toLowerCase().includes('id'))
        );

        const employeeNameColumns = columnsResult.recordset.filter(col => 
          col.COLUMN_NAME.toLowerCase().includes('employee') && 
          col.COLUMN_NAME.toLowerCase().includes('name')
        );

        // 4. فحص أعمدة الشقق
        const apartmentCodeColumns = columnsResult.recordset.filter(col => 
          col.COLUMN_NAME.toLowerCase().includes('apartment') && 
          (col.COLUMN_NAME.toLowerCase().includes('code') || col.COLUMN_NAME.toLowerCase().includes('id'))
        );

        // 5. فحص أعمدة السيارات
        const carCodeColumns = columnsResult.recordset.filter(col => 
          col.COLUMN_NAME.toLowerCase().includes('car') && 
          (col.COLUMN_NAME.toLowerCase().includes('code') || col.COLUMN_NAME.toLowerCase().includes('id'))
        );

        // 6. تسجيل النتائج
        if (employeeCodeColumns.length > 0) {
          tableInfo.employeeCodeColumn = employeeCodeColumns[0].COLUMN_NAME;
          
          // فحص التسمية
          if (!['EmployeeCode', 'employeeCode'].includes(employeeCodeColumns[0].COLUMN_NAME)) {
            tableInfo.issues.push({
              type: 'naming_inconsistency',
              column: employeeCodeColumns[0].COLUMN_NAME,
              expected: 'EmployeeCode',
              severity: 'medium'
            });
          }
        }

        if (employeeNameColumns.length > 0) {
          tableInfo.employeeNameColumn = employeeNameColumns[0].COLUMN_NAME;
          
          // فحص التسمية
          if (!['EmployeeName', 'employeeName'].includes(employeeNameColumns[0].COLUMN_NAME)) {
            tableInfo.issues.push({
              type: 'naming_inconsistency',
              column: employeeNameColumns[0].COLUMN_NAME,
              expected: 'EmployeeName',
              severity: 'medium'
            });
          }
        }

        if (apartmentCodeColumns.length > 0) {
          tableInfo.apartmentCodeColumn = apartmentCodeColumns[0].COLUMN_NAME;
          
          // فحص التسمية
          if (!['ApartmentCode', 'apartmentCode'].includes(apartmentCodeColumns[0].COLUMN_NAME)) {
            tableInfo.issues.push({
              type: 'naming_inconsistency',
              column: apartmentCodeColumns[0].COLUMN_NAME,
              expected: 'ApartmentCode',
              severity: 'medium'
            });
          }
        }

        if (carCodeColumns.length > 0) {
          tableInfo.carCodeColumn = carCodeColumns[0].COLUMN_NAME;
          
          // فحص التسمية
          if (!['CarCode', 'carCode'].includes(carCodeColumns[0].COLUMN_NAME)) {
            tableInfo.issues.push({
              type: 'naming_inconsistency',
              column: carCodeColumns[0].COLUMN_NAME,
              expected: 'CarCode',
              severity: 'medium'
            });
          }
        }

        // 7. فحص مشاكل أخرى
        if (employeeCodeColumns.length > 1) {
          tableInfo.issues.push({
            type: 'multiple_employee_code_columns',
            columns: employeeCodeColumns.map(c => c.COLUMN_NAME),
            severity: 'high'
          });
        }

        if (employeeNameColumns.length > 1) {
          tableInfo.issues.push({
            type: 'multiple_employee_name_columns',
            columns: employeeNameColumns.map(c => c.COLUMN_NAME),
            severity: 'high'
          });
        }

        auditReport.tables.push(tableInfo);

        // إضافة المشاكل إلى قائمة المشاكل العامة
        if (tableInfo.issues.length > 0) {
          auditReport.inconsistencies.push({
            table: tableName,
            issues: tableInfo.issues
          });
        }

      } catch (error) {

        auditReport.tables.push({
          name: tableName,
          error: error.message
        });
      }
    }

    // 8. إنشاء التوصيات
    const employeeCodeVariations = new Set();
    const employeeNameVariations = new Set();
    const apartmentCodeVariations = new Set();
    const carCodeVariations = new Set();

    auditReport.tables.forEach(table => {
      if (table.employeeCodeColumn) employeeCodeVariations.add(table.employeeCodeColumn);
      if (table.employeeNameColumn) employeeNameVariations.add(table.employeeNameColumn);
      if (table.apartmentCodeColumn) apartmentCodeVariations.add(table.apartmentCodeColumn);
      if (table.carCodeColumn) carCodeVariations.add(table.carCodeColumn);
    });

    // إضافة التوصيات
    if (employeeCodeVariations.size > 1) {
      auditReport.recommendations.push({
        type: 'standardize_employee_code',
        message: 'توحيد تسمية عمود كود الموظف',
        variations: Array.from(employeeCodeVariations),
        recommended: 'EmployeeCode'
      });
    }

    if (employeeNameVariations.size > 1) {
      auditReport.recommendations.push({
        type: 'standardize_employee_name',
        message: 'توحيد تسمية عمود اسم الموظف',
        variations: Array.from(employeeNameVariations),
        recommended: 'EmployeeName'
      });
    }

    if (apartmentCodeVariations.size > 1) {
      auditReport.recommendations.push({
        type: 'standardize_apartment_code',
        message: 'توحيد تسمية عمود كود الشقة',
        variations: Array.from(apartmentCodeVariations),
        recommended: 'ApartmentCode'
      });
    }

    if (carCodeVariations.size > 1) {
      auditReport.recommendations.push({
        type: 'standardize_car_code',
        message: 'توحيد تسمية عمود كود السيارة',
        variations: Array.from(carCodeVariations),
        recommended: 'CarCode'
      });
    }

    return NextResponse.json({
      success: true,
      data: auditReport
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'حدث خطأ في مراجعة الجداول',
      error: error.message
    }, { status: 500 });
  }
}
