import { NextResponse } from 'next/server';
import { getConnection } from '@/utils/db';

export async function GET(request) {
  try {

    const pool = await getConnection();
    
    // 1. اكتشاف أسماء الأعمدة الصحيحة
    let correctEmployeeCodeColumn = null;
    let correctEmployeeNameColumn = null;
    
    const possibleEmployeeCodeColumns = ['EmployeeCode', 'EmployeeID', 'ID', 'Code'];
    const possibleEmployeeNameColumns = ['EmployeeName', 'FullName', 'Name', 'EmpName'];
    
    for (const colName of possibleEmployeeCodeColumns) {
      try {
        await pool.request().query(`SELECT TOP 1 ${colName} FROM Employees`);
        correctEmployeeCodeColumn = colName;
        break;
      } catch (error) {
        // العمود غير موجود
      }
    }
    
    for (const colName of possibleEmployeeNameColumns) {
      try {
        await pool.request().query(`SELECT TOP 1 ${colName} FROM Employees`);
        correctEmployeeNameColumn = colName;
        break;
      } catch (error) {
        // العمود غير موجود
      }
    }

    // 2. إحصائيات الموظفين الصحيحة
    const employeeStatsResult = await pool.request().query(`
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN CurrentStatus = N'ساري' OR CurrentStatus = N'نشط' OR CurrentStatus IS NULL THEN 1 END) as active,
        COUNT(CASE WHEN CurrentStatus = N'منقول' THEN 1 END) as transferred,
        COUNT(CASE WHEN CurrentStatus = N'مستقيل' THEN 1 END) as resigned,
        COUNT(CASE WHEN CompanyHousing = N'نعم' THEN 1 END) as resident,
        COUNT(CASE WHEN CompanyHousing = N'لا' OR CompanyHousing IS NULL THEN 1 END) as nonResident,
        COUNT(CASE WHEN SocialInsurance = N'مؤمن' AND MedicalInsurance = N'مؤمن' THEN 1 END) as bothInsured,
        COUNT(CASE WHEN SocialInsurance = N'مؤمن' AND (MedicalInsurance != N'مؤمن' OR MedicalInsurance IS NULL) THEN 1 END) as socialOnly,
        COUNT(CASE WHEN MedicalInsurance = N'مؤمن' AND (SocialInsurance != N'مؤمن' OR SocialInsurance IS NULL) THEN 1 END) as medicalOnly,
        COUNT(CASE WHEN (SocialInsurance != N'مؤمن' OR SocialInsurance IS NULL) AND (MedicalInsurance != N'مؤمن' OR MedicalInsurance IS NULL) THEN 1 END) as notInsured
      FROM Employees
    `);
    
    const employeeStats = employeeStatsResult.recordset[0];
    
    // 3. إحصائيات الشقق
    const apartmentStatsResult = await pool.request().query(`
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN IsActive = 1 THEN 1 END) as active,
        ISNULL(SUM(CASE WHEN IsActive = 1 THEN RentAmount ELSE 0 END), 0) as totalRent,
        (SELECT COUNT(DISTINCT a.ID) 
         FROM Apartments a 
         INNER JOIN ApartmentBeneficiaries ab ON a.ID = ab.ApartmentID 
         WHERE a.IsActive = 1 AND ab.IsActive = 1) as occupied,
        (SELECT COUNT(*) 
         FROM Apartments a 
         WHERE a.IsActive = 1 
         AND NOT EXISTS (SELECT 1 FROM ApartmentBeneficiaries ab WHERE ab.ApartmentID = a.ID AND ab.IsActive = 1)) as vacant
      FROM Apartments
    `);
    
    const apartmentStats = apartmentStatsResult.recordset[0];
    
    // 4. إحصائيات مستفيدي الشقق
    const beneficiariesStatsResult = await pool.request().query(`
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN IsActive = 1 THEN 1 END) as active,
        COUNT(DISTINCT EmployeeCode) as uniqueEmployees,
        COUNT(DISTINCT ApartmentID) as apartmentsWithBeneficiaries
      FROM ApartmentBeneficiaries
    `);
    
    const beneficiariesStats = beneficiariesStatsResult.recordset[0];
    
    // 5. اختبار البحث عن الموظف 1450
    const searchTestResult = await pool.request().query(`
      SELECT TOP 1
        ${correctEmployeeCodeColumn} as EmployeeCode,
        ${correctEmployeeNameColumn} as EmployeeName,
        JobTitle,
        Department,
        CurrentStatus,
        CompanyHousing,
        SocialInsurance,
        MedicalInsurance
      FROM Employees
      WHERE ${correctEmployeeCodeColumn} = 1450
    `);
    
    const searchTest = searchTestResult.recordset[0] || null;
    
    // 6. عينة من الموظفين
    const sampleResult = await pool.request().query(`
      SELECT TOP 5
        ${correctEmployeeCodeColumn} as EmployeeCode,
        ${correctEmployeeNameColumn} as EmployeeName,
        JobTitle,
        Department,
        CurrentStatus,
        CompanyHousing
      FROM Employees
      ORDER BY ${correctEmployeeCodeColumn}
    `);
    
    const employeesSample = sampleResult.recordset;
    
    // 7. عينة من الشقق
    const apartmentsSampleResult = await pool.request().query(`
      SELECT TOP 5
        ID,
        ApartmentCode,
        LandlordName,
        Address,
        RentAmount,
        IsActive
      FROM Apartments
      ORDER BY ApartmentCode
    `);
    
    const apartmentsSample = apartmentsSampleResult.recordset;
    
    // إنشاء التقرير
    const report = {
      databaseConnection: '✅ متصل',
      correctColumns: {
        employeeCode: correctEmployeeCodeColumn,
        employeeName: correctEmployeeNameColumn
      },
      employees: {
        stats: employeeStats,
        sample: employeesSample,
        searchTest: searchTest
      },
      apartments: {
        stats: apartmentStats,
        sample: apartmentsSample
      },
      apartmentBeneficiaries: {
        stats: beneficiariesStats
      },
      summary: {
        totalEmployees: employeeStats.total,
        activeEmployees: employeeStats.active,
        residentEmployees: employeeStats.resident,
        totalApartments: apartmentStats.total,
        occupiedApartments: apartmentStats.occupied,
        vacantApartments: apartmentStats.vacant,
        totalBeneficiaries: beneficiariesStats.active,
        bothInsured: employeeStats.bothInsured,
        socialOnly: employeeStats.socialOnly,
        medicalOnly: employeeStats.medicalOnly,
        notInsured: employeeStats.notInsured
      }
    };

    console.log(`- المؤمن عليهم (الاثنين): ${report.summary.bothInsured}`);
    
    return NextResponse.json({
      success: true,
      data: report,
      message: 'تم إنشاء تقرير قاعدة البيانات بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message,
      details: {
        name: error.name,
        code: error.code,
        state: error.state
      }
    }, { status: 500 });
  }
}
