{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "typeRoots": ["./node_modules/@types", "./@types", "./src/types"], "strictNullChecks": true}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/types/**/*.d.ts", "@types/**/*.d.ts"], "exclude": ["node_modules", ".next", "out", "build", "dist", "archiv", "backups", "exports", "Augment-free", "oje", "ojesta", "scripts/clean-*.js", "**/*.test.js", "**/*.test.ts", "**/*.spec.js", "**/*.spec.ts"]}