import { NextResponse } from 'next/server';
import ExcelJS from 'exceljs';

export async function GET(request, { params }) {
  try {
    const { templateId } = params;
    
    if (!templateId) {
      return NextResponse.json({
        success: false,
        error: 'معرف النموذج مطلوب'
      }, { status: 400 });
    }

    // إنشاء workbook جديد
    const workbook = new ExcelJS.Workbook();
    let worksheet;
    let fileName;

    switch (templateId) {
      case 'daily-attendance':
        worksheet = workbook.addWorksheet('التمام اليومي');
        fileName = 'daily_attendance_template.xlsx';
        
        // إضافة العناوين
        worksheet.columns = [
          { header: 'كود الموظف', key: 'employeeCode', width: 15 },
          { header: 'اسم الموظف', key: 'employeeName', width: 25 },
          { header: 'القسم', key: 'department', width: 20 },
          { header: 'التاريخ', key: 'date', width: 15 },
          { header: 'نوع الحضور', key: 'attendanceType', width: 15 },
          { header: 'وقت الدخول', key: 'checkIn', width: 15 },
          { header: 'وقت الخروج', key: 'checkOut', width: 15 },
          { header: 'ملاحظات', key: 'notes', width: 30 }
        ];

        // إضافة بيانات تجريبية
        worksheet.addRows([
          {
            employeeCode: 'EMP001',
            employeeName: 'أحمد محمد علي',
            department: 'الإدارة العامة',
            date: '2024-01-15',
            attendanceType: 'حضور',
            checkIn: '08:30',
            checkOut: '17:00',
            notes: 'يوم عمل عادي'
          },
          {
            employeeCode: 'EMP002',
            employeeName: 'فاطمة سعد',
            department: 'الموارد البشرية',
            date: '2024-01-15',
            attendanceType: 'غياب',
            checkIn: '',
            checkOut: '',
            notes: 'غياب بدون عذر'
          }
        ]);

        // إضافة ورقة التعليمات
        const instructionsSheet = workbook.addWorksheet('التعليمات');
        instructionsSheet.columns = [
          { header: 'الحقل', key: 'field', width: 20 },
          { header: 'الوصف', key: 'description', width: 50 },
          { header: 'مثال', key: 'example', width: 20 }
        ];

        instructionsSheet.addRows([
          { field: 'كود الموظف', description: 'كود الموظف كما هو مسجل في النظام (مطلوب)', example: 'EMP001' },
          { field: 'اسم الموظف', description: 'الاسم الكامل للموظف', example: 'أحمد محمد علي' },
          { field: 'القسم', description: 'القسم الذي يعمل به الموظف', example: 'الإدارة العامة' },
          { field: 'التاريخ', description: 'تاريخ الحضور بصيغة YYYY-MM-DD (مطلوب)', example: '2024-01-15' },
          { field: 'نوع الحضور', description: 'حضور، غياب، إجازة، مرضي، مأمورية (مطلوب)', example: 'حضور' },
          { field: 'وقت الدخول', description: 'وقت دخول الموظف بصيغة HH:MM', example: '08:30' },
          { field: 'وقت الخروج', description: 'وقت خروج الموظف بصيغة HH:MM', example: '17:00' },
          { field: 'ملاحظات', description: 'أي ملاحظات إضافية', example: 'يوم عمل عادي' }
        ]);
        break;

      case 'temp-workers-daily':
        worksheet = workbook.addWorksheet('العمالة المؤقتة اليومية');
        fileName = 'temp_workers_daily_template.xlsx';
        
        worksheet.columns = [
          { header: 'كود العامل', key: 'workerCode', width: 15 },
          { header: 'اسم العامل', key: 'workerName', width: 25 },
          { header: 'نوع العمالة', key: 'workerType', width: 15 },
          { header: 'التاريخ', key: 'date', width: 15 },
          { header: 'ساعات العمل', key: 'workHours', width: 15 },
          { header: 'نوع العمل', key: 'workType', width: 20 },
          { header: 'الموقع', key: 'location', width: 20 },
          { header: 'ملاحظات', key: 'notes', width: 30 }
        ];

        worksheet.addRows([
          {
            workerCode: 'TW001',
            workerName: 'أحمد محمد',
            workerType: 'خدمي',
            date: '2024-01-15',
            workHours: 8,
            workType: 'تنظيف مكاتب',
            location: 'المبنى الرئيسي',
            notes: 'عمل ممتاز'
          },
          {
            workerCode: 'TW002',
            workerName: 'محمد علي',
            workerType: 'إنتاجي',
            date: '2024-01-15',
            workHours: 10,
            workType: 'صيانة كهرباء',
            location: 'الطابق الثاني',
            notes: 'عمل إضافي'
          }
        ]);

        // إضافة ورقة التعليمات
        const tempWorkersInstructions = workbook.addWorksheet('التعليمات');
        tempWorkersInstructions.columns = [
          { header: 'الحقل', key: 'field', width: 20 },
          { header: 'الوصف', key: 'description', width: 50 },
          { header: 'مثال', key: 'example', width: 20 }
        ];

        tempWorkersInstructions.addRows([
          { field: 'كود العامل', description: 'كود العامل كما هو مسجل في النظام (مطلوب)', example: 'TW001' },
          { field: 'اسم العامل', description: 'الاسم الكامل للعامل', example: 'أحمد محمد' },
          { field: 'نوع العمالة', description: 'خدمي أو إنتاجي', example: 'خدمي' },
          { field: 'التاريخ', description: 'تاريخ العمل بصيغة YYYY-MM-DD (مطلوب)', example: '2024-01-15' },
          { field: 'ساعات العمل', description: 'عدد ساعات العمل من 1 إلى 12 (مطلوب)', example: '8' },
          { field: 'نوع العمل', description: 'وصف نوع العمل المنجز', example: 'تنظيف مكاتب' },
          { field: 'الموقع', description: 'مكان تنفيذ العمل', example: 'المبنى الرئيسي' },
          { field: 'ملاحظات', description: 'أي ملاحظات إضافية', example: 'عمل ممتاز' }
        ]);
        break;

      case 'employees-bulk':
        worksheet = workbook.addWorksheet('إضافة موظفين');
        fileName = 'employees_bulk_template.xlsx';
        
        worksheet.columns = [
          { header: 'كود الموظف', key: 'employeeCode', width: 15 },
          { header: 'الاسم الكامل', key: 'fullName', width: 25 },
          { header: 'القسم', key: 'department', width: 20 },
          { header: 'المسمى الوظيفي', key: 'jobTitle', width: 25 },
          { header: 'المحافظة', key: 'governorate', width: 15 },
          { header: 'تاريخ التعيين', key: 'hireDate', width: 15 },
          { header: 'الراتب', key: 'salary', width: 15 },
          { header: 'رقم الهاتف', key: 'phone', width: 15 },
          { header: 'البريد الإلكتروني', key: 'email', width: 25 },
          { header: 'الجنس', key: 'gender', width: 10 },
          { header: 'الحالة الاجتماعية', key: 'maritalStatus', width: 15 }
        ];

        worksheet.addRows([
          {
            employeeCode: 'EMP100',
            fullName: 'أحمد محمد علي',
            department: 'الإدارة العامة',
            jobTitle: 'مدير إداري',
            governorate: 'القاهرة',
            hireDate: '2024-01-15',
            salary: 5000,
            phone: '01234567890',
            email: '<EMAIL>',
            gender: 'ذكر',
            maritalStatus: 'متزوج'
          },
          {
            employeeCode: 'EMP101',
            fullName: 'فاطمة سعد حسن',
            department: 'الموارد البشرية',
            jobTitle: 'أخصائي موارد بشرية',
            governorate: 'الجيزة',
            hireDate: '2024-01-20',
            salary: 4500,
            phone: '01234567891',
            email: '<EMAIL>',
            gender: 'أنثى',
            maritalStatus: 'أعزب'
          }
        ]);

        // إضافة ورقة التعليمات
        const employeesInstructions = workbook.addWorksheet('التعليمات');
        employeesInstructions.columns = [
          { header: 'الحقل', key: 'field', width: 20 },
          { header: 'الوصف', key: 'description', width: 50 },
          { header: 'مثال', key: 'example', width: 20 }
        ];

        employeesInstructions.addRows([
          { field: 'كود الموظف', description: 'كود فريد للموظف (مطلوب)', example: 'EMP100' },
          { field: 'الاسم الكامل', description: 'الاسم الكامل للموظف (مطلوب)', example: 'أحمد محمد علي' },
          { field: 'القسم', description: 'القسم الذي سيعمل به الموظف (مطلوب)', example: 'الإدارة العامة' },
          { field: 'المسمى الوظيفي', description: 'المسمى الوظيفي للموظف (مطلوب)', example: 'مدير إداري' },
          { field: 'المحافظة', description: 'محافظة إقامة الموظف', example: 'القاهرة' },
          { field: 'تاريخ التعيين', description: 'تاريخ بداية العمل بصيغة YYYY-MM-DD (مطلوب)', example: '2024-01-15' },
          { field: 'الراتب', description: 'الراتب الأساسي (رقم موجب)', example: '5000' },
          { field: 'رقم الهاتف', description: 'رقم الهاتف (11 رقم)', example: '01234567890' },
          { field: 'البريد الإلكتروني', description: 'عنوان البريد الإلكتروني', example: '<EMAIL>' },
          { field: 'الجنس', description: 'ذكر أو أنثى', example: 'ذكر' },
          { field: 'الحالة الاجتماعية', description: 'متزوج، أعزب، مطلق، أرمل', example: 'متزوج' }
        ]);
        break;

      default:
        return NextResponse.json({
          success: false,
          error: 'نموذج غير موجود'
        }, { status: 404 });
    }

    // تنسيق العناوين
    worksheet.getRow(1).font = { bold: true, size: 12 };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE6F3FF' }
    };

    // إضافة حدود للجدول
    worksheet.eachRow((row, rowNumber) => {
      row.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' }
        };
      });
    });

    // تحويل إلى buffer
    const buffer = await workbook.xlsx.writeBuffer();

    // إرجاع الملف
    return new NextResponse(buffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="${fileName}"`,
        'Content-Length': buffer.length.toString(),
      },
    });

  } catch (error) {
    console.error('خطأ في إنشاء نموذج Excel:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في إنشاء النموذج',
      details: error.message
    }, { status: 500 });
  }
}
