import { NextResponse } from 'next/server';
import { getConnection } from '@/utils/db';
import sql from 'mssql';

export async function POST(request) {
  let pool;

  try {
    const body = await request.json();
    const { action } = body;

    pool = await getConnection();

    // التحقق من صحة الاتصال
    if (!pool || !pool.connected) {
      throw new Error('فشل في الاتصال بقاعدة البيانات');
    }

    // إنشاء جدول الإشعارات الذكية إذا لم يكن موجوداً
    try {

      await pool.request().query(`
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SmartNotifications' AND xtype='U')
        BEGIN
          CREATE TABLE SmartNotifications (
            ID INT IDENTITY(1,1) PRIMARY KEY,
            NotificationType NVARCHAR(100) NOT NULL,
            Title NVARCHAR(255) NOT NULL,
            Message NVARCHAR(1000) NOT NULL,
            EmployeeID NVARCHAR(50),
            EmployeeName NVARCHAR(255),
            SystemUserID NVARCHAR(50),
            LeaveStartDate DATE,
            RelatedData NVARCHAR(MAX),
            Priority NVARCHAR(20) DEFAULT 'medium',
            Status NVARCHAR(20) DEFAULT 'pending',
            CreatedAt DATETIME DEFAULT GETDATE(),
            ActionTaken BIT DEFAULT 0,
            ActionDate DATETIME,
            ActionBy NVARCHAR(100),
            IsActive BIT DEFAULT 1
          )
          PRINT 'تم إنشاء جدول SmartNotifications بنجاح'
        END
        ELSE
        BEGIN
          PRINT 'جدول SmartNotifications موجود بالفعل'
        END
      `);

      // إضافة الأعمدة الجديدة إذا لم تكن موجودة
      try {
        await pool.request().query(`
          IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'SmartNotifications' AND COLUMN_NAME = 'SystemUserID')
          ALTER TABLE SmartNotifications ADD SystemUserID nvarchar(50)
        `);
      } catch (e) { /* تجاهل إذا كان العمود موجود */ }

      try {
        await pool.request().query(`
          IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'SmartNotifications' AND COLUMN_NAME = 'LeaveStartDate')
          ALTER TABLE SmartNotifications ADD LeaveStartDate date
        `);
      } catch (e) { /* تجاهل إذا كان العمود موجود */ }

      try {
        await pool.request().query(`
          IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'SmartNotifications' AND COLUMN_NAME = 'IsRead')
          ALTER TABLE SmartNotifications ADD IsRead BIT DEFAULT 0
        `);
      } catch (e) { /* تجاهل إذا كان العمود موجود */ }

      try {
        await pool.request().query(`
          IF COL_LENGTH('SmartNotifications', 'Message') < 1000
          ALTER TABLE SmartNotifications ALTER COLUMN Message nvarchar(1000)
        `);
      } catch (e) { /* تجاهل إذا كان العمود محدث بالفعل */ }

      // إضافة بيانات تجريبية إذا كان الجدول فارغ
      try {
        const countResult = await pool.request().query(`SELECT COUNT(*) as count FROM SmartNotifications`);
        const recordCount = countResult.recordset[0].count;

        if (recordCount === 0) {

          await pool.request().query(`
            INSERT INTO SmartNotifications (NotificationType, Title, Message, Priority, Status, IsRead, CreatedAt)
            VALUES
            (N'contract_expiry', N'انتهاء صلاحية عقد', N'عقد الموظف أحمد محمد ينتهي خلال 30 يوم', N'high', N'pending', 0, GETDATE()),
            (N'attendance', N'غياب بدون إذن', N'الموظف سارة أحمد غائب لمدة 3 أيام متتالية', N'medium', N'pending', 0, GETDATE()),
            (N'documents', N'مستندات ناقصة', N'مستندات التأمين الطبي ناقصة لـ 5 موظفين', N'medium', N'read', 1, GETDATE()),
            (N'license', N'تجديد رخصة قيادة', N'رخصة قيادة السائق محمد علي تنتهي قريباً', N'low', N'pending', 0, GETDATE()),
            (N'salary', N'مراجعة راتب', N'طلب مراجعة راتب من الموظف فاطمة حسن', N'medium', N'read', 1, GETDATE()),
            (N'data_update', N'تحديث بيانات', N'يجب تحديث بيانات الاتصال لـ 8 موظفين', N'low', N'pending', 0, GETDATE()),
            (N'probation', N'انتهاء فترة تجريبية', N'انتهاء الفترة التجريبية للموظف خالد أحمد', N'high', N'pending', 0, GETDATE()),
            (N'medical_leave', N'إجازة مرضية طويلة', N'الموظف نادية محمد في إجازة مرضية لأكثر من أسبوعين', N'medium', N'read', 1, GETDATE()),
            (N'overtime', N'ساعات عمل إضافية', N'الموظف محمد أحمد تجاوز الحد المسموح للعمل الإضافي', N'medium', N'pending', 0, GETDATE()),
            (N'training', N'دورة تدريبية مطلوبة', N'يجب على 12 موظف حضور دورة السلامة المهنية', N'low', N'pending', 0, GETDATE()),
            (N'insurance', N'تجديد التأمين', N'تأمين السيارة رقم ر-ن-ج-825 ينتهي خلال أسبوع', N'high', N'pending', 0, GETDATE()),
            (N'maintenance', N'صيانة دورية', N'موعد الصيانة الدورية للسيارة ط-د-د-284', N'medium', N'pending', 0, GETDATE()),
            (N'budget', N'تجاوز الميزانية', N'تم تجاوز ميزانية قسم النقل بنسبة 15%', N'high', N'pending', 0, GETDATE()),
            (N'vacation', N'إجازة معلقة', N'طلب إجازة الموظف علي حسن معلق منذ أسبوع', N'medium', N'pending', 0, GETDATE()),
            (N'security', N'تحديث كلمة المرور', N'يجب على 25 موظف تحديث كلمات المرور', N'low', N'read', 1, GETDATE()),
            (N'payroll', N'مراجعة كشف المرتبات', N'كشف مرتبات شهر مايو يحتاج مراجعة', N'medium', N'pending', 0, GETDATE()),
            (N'compliance', N'مراجعة الامتثال', N'مراجعة سنوية لسياسات الشركة مطلوبة', N'low', N'pending', 0, GETDATE()),
            (N'emergency', N'حالة طوارئ', N'انقطاع الكهرباء في المبنى الرئيسي', N'high', N'read', 1, GETDATE())
          `);

        } else {

        }
      } catch (insertError) {

      }

    } catch (dbError) {

      // لا نوقف العملية، فقط نسجل التحذير
    }

    switch (action) {
      case 'list':
        return await getSmartNotifications(pool, body);
      case 'create':
        return await createSmartNotification(pool, body);
      case 'markAsRead':
        return await markNotificationAsRead(pool, body);
      case 'takeAction':
        return await takeNotificationAction(pool, body);
      case 'dismiss':
        return await dismissNotification(pool, body);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  }
  // لا نغلق الاتصال هنا لأنه مُدار بواسطة connection pool
  // الاتصال سيُغلق تلقائياً عند انتهاء العملية
}

export async function GET() {
  try {
    // محاكاة request object للـ GET
    const mockRequest = {
      json: () => Promise.resolve({ action: 'list', limit: 100, status: 'all' })
    };
    return await POST(mockRequest);
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب الإشعارات: ' + error.message
    }, { status: 500 });
  }
}

// إنشاء إشعار ذكي جديد
async function createSmartNotification(pool, data) {
  try {
    // التحقق من صحة الاتصال
    if (!pool || !pool.connected) {
      throw new Error('الاتصال بقاعدة البيانات غير متاح');
    }

    const {
      type,
      title,
      message,
      employeeId,
      employeeName,
      systemUserId,
      leaveStartDate,
      priority = 'medium',
      relatedData
    } = data;

    // التحقق من البيانات المطلوبة
    if (!type || !title || !message) {
      return NextResponse.json({
        success: false,
        error: 'البيانات المطلوبة مفقودة (type, title, message)'
      }, { status: 400 });
    }

    // إدراج الإشعار
    const result = await pool.request()
      .input('notificationType', sql.NVarChar, type)
      .input('title', sql.NVarChar, title)
      .input('message', sql.NVarChar, message)
      .input('employeeId', sql.NVarChar, employeeId)
      .input('employeeName', sql.NVarChar, employeeName)
      .input('systemUserId', sql.NVarChar, systemUserId)
      .input('leaveStartDate', sql.Date, leaveStartDate ? new Date(leaveStartDate) : null)
      .input('relatedData', sql.NVarChar, relatedData ? JSON.stringify(relatedData) : null)
      .input('priority', sql.NVarChar, priority)
      .query(`
        INSERT INTO SmartNotifications (
          NotificationType, Title, Message, EmployeeID, EmployeeName,
          SystemUserID, LeaveStartDate, RelatedData, Priority
        )
        OUTPUT INSERTED.ID, INSERTED.CreatedAt
        VALUES (
          @notificationType, @title, @message, @employeeId, @employeeName,
          @systemUserId, @leaveStartDate, @relatedData, @priority
        )
      `);

    const newNotification = result.recordset[0];

    return NextResponse.json({
      success: true,
      message: 'تم إنشاء الإشعار بنجاح',
      notification: {
        ID: newNotification.ID,
        CreatedAt: newNotification.CreatedAt,
        ...data
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في إنشاء الإشعار: ' + error.message
    }, { status: 500 });
  }
}

// جلب الإشعارات الذكية
async function getSmartNotifications(pool, data) {
  try {
    // التحقق من صحة الاتصال
    if (!pool || !pool.connected) {
      throw new Error('الاتصال بقاعدة البيانات غير متاح');
    }

    const { employeeId, limit = 50, status = 'all' } = data;

    let whereClause = 'WHERE IsActive = 1';
    if (employeeId) {
      whereClause += ' AND EmployeeID = @employeeId';
    }
    if (status !== 'all') {
      whereClause += ' AND Status = @status';
    }

    const query = `
      SELECT
        ID,
        NotificationType,
        Title,
        Message,
        EmployeeID,
        EmployeeName,
        SystemUserID,
        LeaveStartDate,
        Priority,
        Status,
        CreatedAt,
        ActionTaken,
        ActionDate,
        ActionBy,
        RelatedData,
        ISNULL(IsRead, 0) as IsRead
      FROM SmartNotifications
      ${whereClause}
      ORDER BY CreatedAt DESC
      OFFSET 0 ROWS FETCH NEXT @limit ROWS ONLY
    `;

    const request = pool.request()
      .input('limit', sql.Int, limit);

    if (employeeId) {
      request.input('employeeId', sql.NVarChar, employeeId);
    }
    if (status !== 'all') {
      request.input('status', sql.NVarChar, status);
    }

    const result = await request.query(query);

    // تحويل البيانات لتشمل معلومات إضافية
    const notifications = result.recordset.map(notification => ({
      ...notification,
      formattedDate: notification.CreatedAt ? new Date(notification.CreatedAt).toLocaleString('ar-EG') : '',
      formattedLeaveDate: notification.LeaveStartDate ? new Date(notification.LeaveStartDate).toLocaleDateString('ar-EG') : '',
      relatedDataParsed: notification.RelatedData ? JSON.parse(notification.RelatedData) : null,
      priorityLabel: getPriorityLabel(notification.Priority),
      typeLabel: getNotificationTypeLabel(notification.NotificationType)
    }));

    return NextResponse.json({
      success: true,
      notifications: notifications,
      total: notifications.length
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب الإشعارات: ' + error.message
    }, { status: 500 });
  }
}

// تحديد الإشعار كمقروء
async function markNotificationAsRead(pool, data) {
  try {
    const { notificationId } = data;

    await pool.request()
      .input('notificationId', sql.Int, notificationId)
      .query(`
        UPDATE SmartNotifications
        SET Status = 'read', IsRead = 1, ActionDate = GETDATE()
        WHERE ID = @notificationId
      `);

    return NextResponse.json({
      success: true,
      message: 'تم تحديد الإشعار كمقروء'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في تحديد الإشعار كمقروء: ' + error.message
    }, { status: 500 });
  }
}

// اتخاذ إجراء على الإشعار
async function takeNotificationAction(pool, data) {
  try {
    const { notificationId, actionBy, notes } = data;

    await pool.request()
      .input('notificationId', sql.Int, notificationId)
      .input('actionBy', sql.NVarChar, actionBy)
      .query(`
        UPDATE SmartNotifications
        SET ActionTaken = 1, ActionDate = GETDATE(), ActionBy = @actionBy, Status = 'action_taken'
        WHERE ID = @notificationId
      `);

    return NextResponse.json({
      success: true,
      message: 'تم اتخاذ إجراء على الإشعار'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في اتخاذ إجراء على الإشعار: ' + error.message
    }, { status: 500 });
  }
}

// تجاهل الإشعار
async function dismissNotification(pool, data) {
  try {
    const { notificationId } = data;

    await pool.request()
      .input('notificationId', sql.Int, notificationId)
      .query(`
        UPDATE SmartNotifications
        SET IsActive = 0, Status = 'dismissed', ActionDate = GETDATE()
        WHERE ID = @notificationId
      `);

    return NextResponse.json({
      success: true,
      message: 'تم تجاهل الإشعار'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في تجاهل الإشعار: ' + error.message
    }, { status: 500 });
  }
}

// ترجمة أولوية الإشعار
function getPriorityLabel(priority) {
  const priorities = {
    'low': 'منخفضة',
    'medium': 'متوسطة',
    'high': 'عالية',
    'urgent': 'عاجلة'
  };
  return priorities[priority] || priority;
}

// ترجمة نوع الإشعار
function getNotificationTypeLabel(type) {
  const types = {
    'LEAVE_REQUEST_SUBMITTED': 'طلب إجازة جديد',
    'LEAVE_REQUEST_APPROVED': 'اعتماد طلب إجازة',
    'LEAVE_REQUEST_REJECTED': 'رفض طلب إجازة',
    'LEAVE_REQUEST_UPDATED': 'تحديث طلب إجازة',
    'SYSTEM_ALERT': 'تنبيه النظام',
    'DATA_MISSING': 'بيانات مفقودة',
    'EXPIRY_WARNING': 'تحذير انتهاء صلاحية'
  };
  return types[type] || type;
}

// تحديث الإشعار (PUT)
export async function PUT(request) {
  try {
    const { notificationId } = await request.json();

    if (!notificationId) {
      return NextResponse.json({ error: 'معرف الإشعار مطلوب' }, { status: 400 });
    }

    const pool = await connectToDatabase();

    await pool.request()
      .input('notificationId', sql.Int, notificationId)
      .query(`
        UPDATE SmartNotifications
        SET Status = 'read', IsRead = 1, ActionDate = GETDATE()
        WHERE ID = @notificationId
      `);

    return NextResponse.json({ success: true, message: 'تم تحديث الإشعار بنجاح' });

  } catch (error) {

    return NextResponse.json({ error: 'خطأ في تحديث الإشعار' }, { status: 500 });
  }
}

// إعادة تعيين التنبيهات (DELETE)
export async function DELETE(request) {
  try {
    const url = new URL(request.url);
    const action = url.searchParams.get('action');

    if (action === 'reset') {

      const dbConfig = {
        user: 'sa',
        password: '123',
        server: 'localhost',
        database: 'emp',
        options: {
          encrypt: false,
          trustServerCertificate: true,
        },
        pool: {
          max: 10,
          min: 0,
          idleTimeoutMillis: 30000,
        },
      };
      const pool = new sql.ConnectionPool(dbConfig);
      await pool.connect();

      // حذف جميع التنبيهات
      await pool.request().query(`DELETE FROM SmartNotifications`);

      // إضافة التنبيهات التجريبية
      await pool.request().query(`
        INSERT INTO SmartNotifications (NotificationType, Title, Message, Priority, Status, IsRead, CreatedAt)
        VALUES
        (N'contract_expiry', N'انتهاء صلاحية عقد', N'عقد الموظف أحمد محمد ينتهي خلال 30 يوم', N'high', N'pending', 0, GETDATE()),
        (N'attendance', N'غياب بدون إذن', N'الموظف سارة أحمد غائب لمدة 3 أيام متتالية', N'medium', N'pending', 0, GETDATE()),
        (N'documents', N'مستندات ناقصة', N'مستندات التأمين الطبي ناقصة لـ 5 موظفين', N'medium', N'read', 1, GETDATE()),
        (N'license', N'تجديد رخصة قيادة', N'رخصة قيادة السائق محمد علي تنتهي قريباً', N'low', N'pending', 0, GETDATE()),
        (N'salary', N'مراجعة راتب', N'طلب مراجعة راتب من الموظف فاطمة حسن', N'medium', N'read', 1, GETDATE()),
        (N'data_update', N'تحديث بيانات', N'يجب تحديث بيانات الاتصال لـ 8 موظفين', N'low', N'pending', 0, GETDATE()),
        (N'probation', N'انتهاء فترة تجريبية', N'انتهاء الفترة التجريبية للموظف خالد أحمد', N'high', N'pending', 0, GETDATE()),
        (N'medical_leave', N'إجازة مرضية طويلة', N'الموظف نادية محمد في إجازة مرضية لأكثر من أسبوعين', N'medium', N'read', 1, GETDATE()),
        (N'overtime', N'ساعات عمل إضافية', N'الموظف محمد أحمد تجاوز الحد المسموح للعمل الإضافي', N'medium', N'pending', 0, GETDATE()),
        (N'training', N'دورة تدريبية مطلوبة', N'يجب على 12 موظف حضور دورة السلامة المهنية', N'low', N'pending', 0, GETDATE()),
        (N'insurance', N'تجديد التأمين', N'تأمين السيارة رقم ر-ن-ج-825 ينتهي خلال أسبوع', N'high', N'pending', 0, GETDATE()),
        (N'maintenance', N'صيانة دورية', N'موعد الصيانة الدورية للسيارة ط-د-د-284', N'medium', N'pending', 0, GETDATE()),
        (N'budget', N'تجاوز الميزانية', N'تم تجاوز ميزانية قسم النقل بنسبة 15%', N'high', N'pending', 0, GETDATE()),
        (N'vacation', N'إجازة معلقة', N'طلب إجازة الموظف علي حسن معلق منذ أسبوع', N'medium', N'pending', 0, GETDATE()),
        (N'security', N'تحديث كلمة المرور', N'يجب على 25 موظف تحديث كلمات المرور', N'low', N'read', 1, GETDATE()),
        (N'payroll', N'مراجعة كشف المرتبات', N'كشف مرتبات شهر مايو يحتاج مراجعة', N'medium', N'pending', 0, GETDATE()),
        (N'compliance', N'مراجعة الامتثال', N'مراجعة سنوية لسياسات الشركة مطلوبة', N'low', N'pending', 0, GETDATE()),
        (N'emergency', N'حالة طوارئ', N'انقطاع الكهرباء في المبنى الرئيسي', N'high', N'read', 1, GETDATE())
      `);

      // التحقق من النتائج
      const result = await pool.request().query(`
        SELECT
          COUNT(*) as total,
          COUNT(CASE WHEN ISNULL(IsRead, 0) = 0 THEN 1 END) as unread
        FROM SmartNotifications
      `);

      const stats = result.recordset[0];

      return NextResponse.json({
        success: true,
        message: 'تم إعادة تعيين التنبيهات بنجاح',
        stats: {
          total: stats.total,
          unread: stats.unread
        }
      });
    }

    return NextResponse.json({ error: 'إجراء غير صحيح' }, { status: 400 });

  } catch (error) {

    return NextResponse.json({ error: 'خطأ في إعادة تعيين التنبيهات' }, { status: 500 });
  }
}

