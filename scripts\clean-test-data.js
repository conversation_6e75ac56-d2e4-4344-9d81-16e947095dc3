const { getConnection } = require('../src/utils/db');

async function cleanTestData() {
  let pool;
  
  try {
    console.log('🔗 الاتصال بقاعدة البيانات...');
    pool = await getConnection();
    
    // عرض البيانات الحالية قبل الحذف
    console.log('📊 البيانات الحالية:');
    const currentData = await pool.request().query(`
      SELECT 
        CostType, Month, Year, TotalAmount, ItemsCount, CreatedBy
      FROM MonthlyCosts 
      ORDER BY Year DESC, Month DESC, CostType
    `);
    
    const monthNames = ['', 'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو', 
                       'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
    
    currentData.recordset.forEach(record => {
      const costTypeName = record.CostType === 'carscost' ? 'السيارات' : 
                          record.CostType === 'housingcost' ? 'الشقق' : 'العمالة المؤقتة';
      console.log(`- ${costTypeName} - ${monthNames[record.Month]} ${record.Year}: ${record.TotalAmount.toLocaleString()} جنيه (${record.CreatedBy})`);
    });
    
    // حذف البيانات التجريبية فقط
    console.log('\n🗑️ حذف البيانات التجريبية...');
    const deleteResult = await pool.request().query(`
      DELETE FROM MonthlyCosts 
      WHERE CreatedBy = 'سكريبت البيانات التجريبية'
    `);
    
    console.log(`✅ تم حذف ${deleteResult.rowsAffected[0]} سجل من البيانات التجريبية`);
    
    // عرض البيانات المتبقية
    console.log('\n📊 البيانات المتبقية بعد التنظيف:');
    const remainingData = await pool.request().query(`
      SELECT 
        CostType, Month, Year, TotalAmount, ItemsCount, CreatedBy
      FROM MonthlyCosts 
      ORDER BY Year DESC, Month DESC, CostType
    `);
    
    if (remainingData.recordset.length > 0) {
      remainingData.recordset.forEach(record => {
        const costTypeName = record.CostType === 'carscost' ? 'السيارات' : 
                            record.CostType === 'housingcost' ? 'الشقق' : 'العمالة المؤقتة';
        console.log(`- ${costTypeName} - ${monthNames[record.Month]} ${record.Year}: ${record.TotalAmount.toLocaleString()} جنيه (${record.CreatedBy})`);
      });
    } else {
      console.log('لا توجد بيانات متبقية');
    }
    
    // إحصائيات نهائية
    const finalCount = await pool.request().query('SELECT COUNT(*) as count FROM MonthlyCosts');
    console.log(`\n📈 إجمالي السجلات المتبقية: ${finalCount.recordset[0].count}`);
    
    console.log('\n🎉 تم تنظيف البيانات التجريبية بنجاح!');
    console.log('الآن تحتوي قاعدة البيانات على البيانات الفعلية المزامنة فقط');
    
  } catch (error) {
    console.error('❌ خطأ في تنظيف البيانات:', error);
  } finally {
    if (pool) {
      await pool.close();
    }
  }
}

// تشغيل السكريبت
if (require.main === module) {
  cleanTestData();
}

module.exports = { cleanTestData };
