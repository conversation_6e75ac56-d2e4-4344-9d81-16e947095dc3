async function handler({ email, password }) {
  if (!email || !password) {
    return {
      success: false,
      error: 'البريد الإلكتروني وكلمة المرور مطلوبة',
    };
  }

  try {
    const users = await sql`
      SELECT 
        u.id,
        u.email,
        u.password_hash,
        u.role_id,
        u.employee_id,
        u.is_active,
        r.name as role_name,
        ma.employee_name,
        ma.department,
        ma.job_title
      FROM users u
      LEFT JOIN roles r ON u.role_id = r.id
      LEFT JOIN monthly_attendance ma ON u.employee_id = ma.employee_id
      WHERE u.email = ${email}
      AND u.is_active = true
    `;

    if (users.length === 0) {
      return {
        success: false,
        error: 'البريد الإلكتروني غير موجود',
      };
    }

    const user = users[0];

    if (user.password_hash !== password) {
      return {
        success: false,
        error: 'كلمة المرور غير صحيحة',
      };
    }

    const permissions = await sql`
      SELECT p.code, p.name_ar, p.name_en
      FROM permissions p
      JOIN role_permissions rp ON p.id = rp.permission_id
      WHERE rp.role_id = ${user.role_id}
    `;

    return {
      success: true,
      user: {
        id: user.id,
        email: user.email,
        role: user.role_name,
        employeeId: user.employee_id,
        employeeName: user.employee_name,
        department: user.department,
        jobTitle: user.job_title,
      },
      permissions: permissions.map((p) => ({
        code: p.code,
        nameAr: p.name_ar,
        nameEn: p.name_en,
      })),
    };
  } catch (error) {
    return {
      success: false,
      error: 'حدث خطأ أثناء تسجيل الدخول',
    };
  }
}
