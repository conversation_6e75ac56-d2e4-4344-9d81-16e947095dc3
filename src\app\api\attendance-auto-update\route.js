import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

/**
 * نظام موحد لتحديث التمام اليومي تلقائياً عند اعتماد الطلبات
 */

export async function POST(request) {
  try {
    const body = await request.json();
    const { action } = body;

    const pool = await getConnection();

    switch (action) {
      case 'update-from-request':
        return await updateAttendanceFromRequest(pool, body);
      case 'remove-from-request':
        return await removeAttendanceFromRequest(pool, body);
      case 'sync-all-approved':
        return await syncAllApprovedRequests(pool);
      case 'fix-attendance':
        return await fixAttendanceIssues(pool);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في النظام: ' + error.message
    }, { status: 500 });
  }
}

// تحديث التمام من طلب معتمد
async function updateAttendanceFromRequest(pool, data) {
  try {
    const { requestId, requestType, employeeCode, startDate, endDate, leaveType, destination } = data;

    // جلب بيانات الموظف
    const employeeResult = await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .query(`
        SELECT EmployeeName, Department, JobTitle
        FROM Employees
        WHERE EmployeeCode = @employeeCode
      `);

    if (employeeResult.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الموظف غير موجود'
      }, { status: 404 });
    }

    const employee = employeeResult.recordset[0];

    // تحديد نوع التمام والملاحظات
    let attendanceType = '';
    let notes = '';

    if (requestType === 'leave') {
      attendanceType = getLeaveAttendanceType(leaveType);
      notes = `تنتهي في ${endDate}`;
    } else if (requestType === 'mission') {
      attendanceType = 'مأمورية';
      notes = `مأمورية إلى ${destination} - تنتهي في ${endDate}`;
    } else {
      attendanceType = requestType;
      notes = `${requestType} - تنتهي في ${endDate}`;
    }

    // إنشاء قائمة التواريخ
    const dates = generateDateRange(startDate, endDate);
    let updatedDays = 0;

    for (const date of dates) {
      try {
        // التحقق من وجود سجل تمام لهذا اليوم
        const existingAttendance = await pool.request()
          .input('employeeCode', sql.NVarChar, employeeCode)
          .input('date', sql.Date, date)
          .query(`
            SELECT ID, Attendance, IsFromRequest
            FROM DailyAttendance
            WHERE EmployeeCode = @employeeCode AND AttendanceDate = @date
          `);

        if (existingAttendance.recordset.length > 0) {
          // تحديث السجل الموجود
          await pool.request()
            .input('employeeCode', sql.NVarChar, employeeCode)
            .input('date', sql.Date, date)
            .input('attendance', sql.NVarChar, attendanceType)
            .input('notes', sql.NVarChar, notes)
            .input('requestId', sql.Int, requestId)
            .query(`
              UPDATE DailyAttendance 
              SET 
                Attendance = @attendance,
                Notes = @notes,
                IsFromRequest = 1,
                RequestID = @requestId,
                UpdatedAt = GETDATE()
              WHERE EmployeeCode = @employeeCode AND AttendanceDate = @date
            `);
        } else {
          // إنشاء سجل جديد
          await pool.request()
            .input('employeeCode', sql.NVarChar, employeeCode)
            .input('employeeName', sql.NVarChar, employee.EmployeeName)
            .input('department', sql.NVarChar, employee.Department)
            .input('jobTitle', sql.NVarChar, employee.JobTitle)
            .input('date', sql.Date, date)
            .input('attendance', sql.NVarChar, attendanceType)
            .input('attendanceStatus', sql.NVarChar, attendanceType) // إضافة attendanceStatus
            .input('notes', sql.NVarChar, notes)
            .input('requestId', sql.Int, requestId)
            .query(`
              INSERT INTO DailyAttendance (
                AttendanceDate, EmployeeCode, EmployeeName, Department, JobTitle,
                Attendance, attendanceStatus, Notes, IsFromRequest, RequestID, CreatedAt
              ) VALUES (
                @date, @employeeCode, @employeeName, @department, @jobTitle,
                @attendance, @attendanceStatus, @notes, 1, @requestId, GETDATE()
              )
            `);
        }

        updatedDays++;

      } catch (dayError) {

      }
    }

    // تحديث كشف التمام الشهري أيضاً
    await updateMonthlyAttendanceSheet(pool, employeeCode, dates, attendanceType, leaveType);

    return NextResponse.json({
      success: true,
      message: `تم تحديث التمام لـ ${updatedDays} يوم`,
      updatedDays: updatedDays,
      attendanceType: attendanceType
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في تحديث التمام: ' + error.message
    }, { status: 500 });
  }
}

// إزالة التمام من طلب ملغي
async function removeAttendanceFromRequest(pool, data) {
  try {
    const { requestId, employeeCode, startDate, endDate } = data;

    // حذف سجلات التمام المرتبطة بهذا الطلب
    const deleteResult = await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .input('startDate', sql.Date, startDate)
      .input('endDate', sql.Date, endDate)
      .input('requestId', sql.Int, requestId)
      .query(`
        DELETE FROM DailyAttendance
        WHERE EmployeeCode = @employeeCode 
          AND AttendanceDate BETWEEN @startDate AND @endDate
          AND IsFromRequest = 1
          AND RequestID = @requestId
      `);

    const deletedRows = deleteResult.rowsAffected[0];

    // حذف من كشف التمام الشهري أيضاً
    await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .input('startDate', sql.Date, startDate)
      .input('endDate', sql.Date, endDate)
      .query(`
        DELETE FROM MonthlyAttendanceSheet
        WHERE EmployeeCode = @employeeCode 
          AND AttendanceDate BETWEEN @startDate AND @endDate
          AND (AttendanceStatus LIKE '%إجازة%' OR AttendanceStatus = 'مأمورية')
      `);

    return NextResponse.json({
      success: true,
      message: `تم حذف ${deletedRows} سجل تمام`,
      deletedRows: deletedRows
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في حذف التمام: ' + error.message
    }, { status: 500 });
  }
}

// مزامنة جميع الطلبات المعتمدة مع التمام
async function syncAllApprovedRequests(pool) {
  try {

    // جلب جميع الطلبات المعتمدة
    const approvedRequests = await pool.request().query(`
      SELECT 
        ID, RequestType, EmployeeCode, EmployeeName, Department, JobTitle,
        LeaveType, StartDate, EndDate, DaysCount,
        MissionDestination as Destination, Status
      FROM PaperRequests
      WHERE Status = N'معتمدة'
        AND StartDate IS NOT NULL
        AND EndDate IS NOT NULL
      ORDER BY StartDate
    `);

    const requests = approvedRequests.recordset;

    let successCount = 0;
    let errorCount = 0;

    for (const request of requests) {
      try {
        await updateAttendanceFromRequest(pool, {
          requestId: request.ID,
          requestType: request.RequestType,
          employeeCode: request.EmployeeCode,
          startDate: request.StartDate,
          endDate: request.EndDate,
          leaveType: request.LeaveType,
          destination: request.Destination
        });

        successCount++;

      } catch (error) {

        errorCount++;
      }
    }

    return NextResponse.json({
      success: true,
      message: `تم مزامنة ${successCount} طلب من أصل ${requests.length}`,
      summary: {
        total: requests.length,
        success: successCount,
        errors: errorCount
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في المزامنة: ' + error.message
    }, { status: 500 });
  }
}

// إصلاح مشاكل التمام
async function fixAttendanceIssues(pool) {
  try {

    const issues = [];

    // 1. البحث عن طلبات معتمدة بدون تمام
    const missingAttendance = await pool.request().query(`
      SELECT pr.ID, pr.EmployeeCode, pr.StartDate, pr.EndDate, pr.RequestType, pr.LeaveType
      FROM PaperRequests pr
      WHERE pr.Status = N'معتمدة'
        AND pr.StartDate IS NOT NULL
        AND pr.EndDate IS NOT NULL
        AND NOT EXISTS (
          SELECT 1 FROM DailyAttendance da
          WHERE da.EmployeeCode = pr.EmployeeCode
            AND da.AttendanceDate BETWEEN pr.StartDate AND pr.EndDate
            AND da.IsFromRequest = 1
            AND da.RequestID = pr.ID
        )
    `);

    if (missingAttendance.recordset.length > 0) {
      issues.push({
        type: 'طلبات معتمدة بدون تمام',
        count: missingAttendance.recordset.length,
        severity: 'عالي'
      });
    }

    // 2. البحث عن تمام بدون طلبات
    const orphanedAttendance = await pool.request().query(`
      SELECT COUNT(*) as Count
      FROM DailyAttendance da
      WHERE da.IsFromRequest = 1
        AND da.RequestID IS NOT NULL
        AND NOT EXISTS (
          SELECT 1 FROM PaperRequests pr
          WHERE pr.ID = da.RequestID
        )
    `);

    if (orphanedAttendance.recordset[0].Count > 0) {
      issues.push({
        type: 'تمام بدون طلبات',
        count: orphanedAttendance.recordset[0].Count,
        severity: 'متوسط'
      });
    }

    return NextResponse.json({
      success: true,
      issues: issues,
      needsSync: missingAttendance.recordset.length > 0
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في إصلاح التمام: ' + error.message
    }, { status: 500 });
  }
}

// دوال مساعدة

// تحديد نوع التمام للإجازة
function getLeaveAttendanceType(leaveType) {
  const leaveTypes = {
    'إعتيادية': 'إجازة إعتيادية',
    'اعتيادية': 'إجازة إعتيادية',
    'عارضة': 'إجازة عارضة',
    'مرضية': 'إجازة مرضية',
    'أمومة': 'إجازة أمومة',
    'أبوة': 'إجازة أبوة',
    'بدون أجر': 'إجازة بدون أجر',
    'بدل': 'إجازة بدل'
  };

  return leaveTypes[leaveType] || `إجازة ${leaveType}`;
}

// إنشاء قائمة التواريخ
function generateDateRange(startDate, endDate) {
  const dates = [];
  const start = new Date(startDate);
  const end = new Date(endDate);

  for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
    dates.push(date.toISOString().split('T')[0]);
  }

  return dates;
}

// تحديث كشف التمام الشهري
async function updateMonthlyAttendanceSheet(pool, employeeCode, dates, attendanceType, leaveType) {
  try {
    for (const date of dates) {
      // تحديد رمز التمام
      let attendanceCode = 'ح'; // حاضر افتراضي
      
      if (attendanceType.includes('إجازة إعتيادية')) attendanceCode = 'ج';
      else if (attendanceType.includes('إجازة عارضة')) attendanceCode = 'ع';
      else if (attendanceType.includes('إجازة مرضية')) attendanceCode = 'م';
      else if (attendanceType.includes('مأمورية')) attendanceCode = 'أ';
      else if (attendanceType.includes('بدون أجر')) attendanceCode = 'ب';

      // التحقق من وجود السجل
      const existingMonthly = await pool.request()
        .input('employeeCode', sql.NVarChar, employeeCode)
        .input('date', sql.Date, date)
        .query(`
          SELECT ID FROM MonthlyAttendanceSheet
          WHERE EmployeeCode = @employeeCode AND AttendanceDate = @date
        `);

      if (existingMonthly.recordset.length > 0) {
        // تحديث السجل الموجود
        await pool.request()
          .input('employeeCode', sql.NVarChar, employeeCode)
          .input('date', sql.Date, date)
          .input('attendanceType', sql.NVarChar, attendanceType)
          .input('leaveType', sql.NVarChar, leaveType)
          .input('attendanceCode', sql.NVarChar, attendanceCode)
          .query(`
            UPDATE MonthlyAttendanceSheet 
            SET 
              AttendanceStatus = @attendanceType,
              LeaveType = @leaveType,
              AttendanceCode = @attendanceCode,
              UpdatedAt = GETDATE()
            WHERE EmployeeCode = @employeeCode AND AttendanceDate = @date
          `);
      } else {
        // إنشاء سجل جديد
        await pool.request()
          .input('employeeCode', sql.NVarChar, employeeCode)
          .input('date', sql.Date, date)
          .input('attendanceType', sql.NVarChar, attendanceType)
          .input('leaveType', sql.NVarChar, leaveType)
          .input('attendanceCode', sql.NVarChar, attendanceCode)
          .query(`
            INSERT INTO MonthlyAttendanceSheet (
              EmployeeCode, AttendanceDate, AttendanceStatus, 
              LeaveType, AttendanceCode, CreatedAt
            ) VALUES (
              @employeeCode, @date, @attendanceType, 
              @leaveType, @attendanceCode, GETDATE()
            )
          `);
      }
    }
  } catch (error) {

    // لا نوقف العملية في حالة فشل تحديث الكشف الشهري
  }
}
