/**
 * فرض تحديث الصفحة وإعادة تشغيل الخادم
 */

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

async function forceRefresh() {

  try {
    // 1. إنشاء ملف تحديث مؤقت لفرض إعادة التحميل

    const updateFile = path.join(__dirname, 'src', 'app', 'employees', 'individual-search', 'force-update.js');
    const updateContent = `// Force update: ${new Date().toISOString()}\nexport const forceUpdate = true;`;
    
    fs.writeFileSync(updateFile, updateContent);

    // 2. تحديث ملف الصفحة لفرض إعادة التحميل

    const pageFile = path.join(__dirname, 'src', 'app', 'employees', 'individual-search', 'page.jsx');
    let pageContent = fs.readFileSync(pageFile, 'utf8');
    
    // إضافة تعليق بالوقت الحالي لفرض إعادة التحميل
    const timestamp = `// Updated: ${new Date().toISOString()}`;
    
    if (!pageContent.includes('// Updated:')) {
      pageContent = timestamp + '\n' + pageContent;
    } else {
      pageContent = pageContent.replace(/\/\/ Updated:.*\n/, timestamp + '\n');
    }
    
    fs.writeFileSync(pageFile, pageContent);

    // 3. حذف ملف التحديث المؤقت
    setTimeout(() => {
      try {
        fs.unlinkSync(updateFile);

      } catch (error) {

      }
    }, 2000);
    
    // 4. إرشادات للمستخدم

    console.log('4. ابحث عن موظف (مثل: 1450) لرؤية التحديثات الجديدة');

    console.log('   - مربع ملون (تيل فاتح) يحتوي على:');

    console.log('     • اسم المؤجر (إذا كان مسجل بشقة)');

    console.log('   - مربع ملون (نيلي فاتح) يحتوي على:');

    console.log('     • خط السير (إذا كان مسجل بسيارة)');

    console.log('🟢 أخضر: للحالات الإيجابية (مسجل)');
    console.log('🔴 أحمر: للحالات السلبية (غير مسجل)');

    console.log('2. امسح cache المتصفح (Ctrl+Shift+Delete)');

    console.log('4. تأكد من عدم وجود أخطاء في console المتصفح (F12)');
    
    return {
      success: true,
      message: 'تم فرض التحديث بنجاح'
    };
    
  } catch (error) {

    return {
      success: false,
      error: error.message
    };
  }
}

// تشغيل فرض التحديث
if (require.main === module) {
  forceRefresh()
    .then(result => {
      if (result.success) {

      } else {

      }
    })
    .catch(error => {

    });
}

module.exports = { forceRefresh };
