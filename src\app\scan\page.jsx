'use client';

import MainLayout from '@/components/MainLayout';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { FiFile, FiFolder, FiPlus, FiSearch } from 'react-icons/fi';

export default function ScanDashboard() {
  const searchParams = useSearchParams();
  const [activeSection, setActiveSection] = useState(searchParams.get('section') || 'employees');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSubCategory, setSelectedSubCategory] = useState('');
  const [showAddCategory, setShowAddCategory] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadFile, setUploadFile] = useState(null);
  const [uploadCode, setUploadCode] = useState('');
  const [uploadDocType, setUploadDocType] = useState('');
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [dateFilter, setDateFilter] = useState('all'); // all, today, week, month, custom

  // أقسام النظام الرئيسية
  const mainSections = {
    employees: {
      title: 'مذكرات الموظفين',
      icon: '👥',
      type: 'category-based', // تنظيم حسب نوع المعاملة
      defaultCategories: [
        'تعديل راتب',
        'طلب مستحقات',
        'إيقاف مستحقات',
        'تعديل مسمى وظيفي',
        'إجازات',
        'تحقيقات'
      ]
    },
    cars: {
      title: 'مذكرات السيارات',
      icon: '🚗',
      type: 'code-based', // تنظيم حسب كود السيارة
      documentTypes: [
        'طلب إيجار',
        'طلب إيقاف',
        'تجديد رخصة',
        'صيانة',
        'حوادث',
        'تأمين'
      ]
    },
    apartments: {
      title: 'مذكرات الشقق',
      icon: '🏠',
      type: 'code-based', // تنظيم حسب كود الشقة
      documentTypes: [
        'عقد إيجار',
        'طلب إخلاء',
        'صيانة',
        'شكاوى',
        'تجديد عقد',
        'تحصيل'
      ]
    },
    tempWorkers: {
      title: 'مذكرات العمالة المؤقتة',
      icon: '👷',
      type: 'category-based', // تنظيم حسب نوع المعاملة
      defaultCategories: [
        'تعديل الفئة',
        'إيقاف مستحقات',
        'تحقيقات',
        'تعديل أجر',
        'إنهاء خدمة',
        'مكافآت'
      ]
    }
  };

  const [categories, setCategories] = useState({
    employees: mainSections.employees.defaultCategories || [],
    tempWorkers: mainSections.tempWorkers.defaultCategories || [],
    cars: mainSections.cars.documentTypes || [],
    apartments: mainSections.apartments.documentTypes || []
  });

  useEffect(() => {
    loadDocuments();
  }, [activeSection, selectedSubCategory, searchTerm]);

  const loadDocuments = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/scan-documents', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'list',
          section: activeSection,
          subCategory: selectedSubCategory,
          searchTerm: searchTerm
        })
      });

      const result = await response.json();
      if (result.success) {
        setDocuments(result.data || []);
      }
    } catch (error) {
      console.error('خطأ في تحميل المستندات:', error);
    }
    setLoading(false);
  };

  const addNewCategory = async () => {
    if (!newCategoryName.trim()) return;

    try {
      const response = await fetch('/api/scan-documents', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'addCategory',
          section: activeSection,
          categoryName: newCategoryName.trim(),
          categoryType: mainSections[activeSection].type
        })
      });

      const result = await response.json();
      if (result.success) {
        setCategories(prev => ({
          ...prev,
          [activeSection]: [...(prev[activeSection] || []), newCategoryName.trim()]
        }));
        setNewCategoryName('');
        setShowAddCategory(false);

        // إعادة تحميل البيانات
        loadDocuments();
      }
    } catch (error) {
      console.error('خطأ في إضافة القسم:', error);
    }
  };

  const renderSectionContent = () => {
    const section = mainSections[activeSection];

    if (section.type === 'category-based') {
      return (
        <div className="space-y-4">
          {/* أقسام فرعية */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {categories[activeSection]?.map((category, index) => (
              <div
                key={index}
                onClick={() => setSelectedSubCategory(category)}
                className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                  selectedSubCategory === category
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <FiFolder className="text-blue-500" />
                  <span className="font-medium">{category}</span>
                </div>
              </div>
            ))}

            {/* زر إضافة قسم جديد */}
            <div
              onClick={() => setShowAddCategory(true)}
              className="p-4 rounded-lg border-2 border-dashed border-gray-300 cursor-pointer hover:border-blue-400 transition-all flex items-center justify-center"
            >
              <div className="text-center">
                <FiPlus className="mx-auto text-gray-400 mb-1" size={20} />
                <span className="text-sm text-gray-500">إضافة قسم</span>
              </div>
            </div>
          </div>
        </div>
      );
    } else {
      // code-based sections (cars, apartments)
      return (
        <div className="space-y-4">
          {/* أنواع المستندات كأقسام فرعية */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-6">
            {section.documentTypes?.map((docType, index) => (
              <div
                key={index}
                onClick={() => setSelectedSubCategory(docType)}
                className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                  selectedSubCategory === docType
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <FiFolder className="text-blue-500" />
                  <span className="font-medium">{docType}</span>
                </div>
              </div>
            ))}

            {/* زر إضافة نوع مستند جديد */}
            <div
              onClick={() => setShowAddCategory(true)}
              className="p-4 rounded-lg border-2 border-dashed border-gray-300 cursor-pointer hover:border-blue-400 transition-all flex items-center justify-center"
            >
              <div className="text-center">
                <FiPlus className="mx-auto text-gray-400 mb-1" size={20} />
                <span className="text-sm text-gray-500">إضافة نوع</span>
              </div>
            </div>
          </div>

          {/* البحث بالكود */}
          {selectedSubCategory && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-semibold mb-3">البحث بالكود في: {selectedSubCategory}</h3>
              <div className="flex gap-3">
                <input
                  type="text"
                  placeholder={`أدخل ${section.title === 'مذكرات السيارات' ? 'كود السيارة' : 'كود الشقة'}`}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                />
                <button
                  onClick={loadDocuments}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                  <FiSearch />
                </button>
              </div>
            </div>
          )}
        </div>
      );
    }
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <span className="text-3xl">📁</span>
              <div>
                <h1 className="text-3xl font-bold text-gray-800">نظام SCAN</h1>
                <p className="text-gray-600">إدارة المستندات والمذكرات حسب التخصص</p>
              </div>
            </div>
          </div>
        </div>

        {/* أقسام رئيسية */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.entries(mainSections).map(([key, section]) => (
              <button
                key={key}
                onClick={() => {
                  setActiveSection(key);
                  setSelectedSubCategory('');
                  setSearchTerm('');
                }}
                className={`p-4 rounded-lg border-2 transition-all ${
                  activeSection === key
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="text-center">
                  <div className="text-2xl mb-2">{section.icon}</div>
                  <div className="font-medium">{section.title}</div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* محتوى القسم المختار */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold">
              {mainSections[activeSection]?.title}
            </h2>

            {/* شريط البحث العام وأزرار الإجراءات */}
            <div className="flex items-center gap-3 flex-wrap">
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="البحث في المستندات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* فلاتر التاريخ */}
              <select
                value={dateFilter}
                onChange={(e) => setDateFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">جميع التواريخ</option>
                <option value="today">اليوم</option>
                <option value="week">هذا الأسبوع</option>
                <option value="month">هذا الشهر</option>
                <option value="custom">تاريخ محدد</option>
              </select>

              {/* فلتر الشهر والسنة */}
              {dateFilter === 'custom' && (
                <>
                  <select
                    value={selectedMonth}
                    onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  >
                    {Array.from({ length: 12 }, (_, i) => (
                      <option key={i + 1} value={i + 1}>
                        {new Date(2024, i).toLocaleDateString('ar-EG', { month: 'long' })}
                      </option>
                    ))}
                  </select>

                  <select
                    value={selectedYear}
                    onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  >
                    {Array.from({ length: 5 }, (_, i) => (
                      <option key={2023 + i} value={2023 + i}>
                        {2023 + i}
                      </option>
                    ))}
                  </select>
                </>
              )}

              {/* زر رفع ملف */}
              <button
                onClick={() => setShowUploadModal(true)}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center gap-2"
              >
                <FiPlus />
                رفع ملف
              </button>
            </div>
          </div>

          {renderSectionContent()}

          {/* قائمة المستندات */}
          {selectedSubCategory && (
            <div className="mt-6">
              <h3 className="font-semibold mb-4">مستندات: {selectedSubCategory}</h3>
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
                  جاري التحميل...
                </div>
              ) : documents.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {documents.map((doc, index) => (
                    <div key={index} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="flex items-center gap-2 mb-2">
                        <FiFile className="text-blue-500" />
                        <span className="font-medium">{doc.name}</span>
                      </div>
                      <div className="text-sm text-gray-500">
                        <div>التاريخ: {doc.date}</div>
                        <div>الحجم: {doc.size}</div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <FiFolder className="mx-auto text-4xl mb-2" />
                  <p>لا توجد مستندات في هذا القسم</p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* نموذج إضافة قسم جديد */}
        {showAddCategory && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-bold mb-4">إضافة قسم فرعي جديد</h3>
              <input
                type="text"
                placeholder="اسم القسم الجديد"
                value={newCategoryName}
                onChange={(e) => setNewCategoryName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 mb-4"
              />
              <div className="flex gap-3">
                <button
                  onClick={addNewCategory}
                  className="flex-1 bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700"
                >
                  إضافة
                </button>
                <button
                  onClick={() => {
                    setShowAddCategory(false);
                    setNewCategoryName('');
                  }}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        )}

        {/* نموذج رفع الملفات */}
        {showUploadModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-bold mb-4">رفع ملف جديد</h3>

              {/* اختيار نوع القسم */}
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">القسم</label>
                <select
                  value={activeSection}
                  onChange={(e) => setActiveSection(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                >
                  {Object.entries(mainSections).map(([key, section]) => (
                    <option key={key} value={key}>{section.title}</option>
                  ))}
                </select>
              </div>

              {/* للأقسام المبنية على الكود */}
              {mainSections[activeSection]?.type === 'code-based' && (
                <>
                  <div className="mb-4">
                    <label className="block text-sm font-medium mb-2">
                      {activeSection === 'cars' ? 'كود السيارة' : 'كود الشقة'}
                    </label>
                    <input
                      type="text"
                      placeholder={`أدخل ${activeSection === 'cars' ? 'كود السيارة' : 'كود الشقة'}`}
                      value={uploadCode}
                      onChange={(e) => setUploadCode(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div className="mb-4">
                    <label className="block text-sm font-medium mb-2">نوع المستند</label>
                    <select
                      value={uploadDocType}
                      onChange={(e) => setUploadDocType(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">اختر نوع المستند</option>
                      {mainSections[activeSection]?.documentTypes?.map((type, index) => (
                        <option key={index} value={type}>{type}</option>
                      ))}
                    </select>
                  </div>
                </>
              )}

              {/* للأقسام المبنية على الفئات */}
              {mainSections[activeSection]?.type === 'category-based' && (
                <div className="mb-4">
                  <label className="block text-sm font-medium mb-2">الفئة</label>
                  <select
                    value={selectedSubCategory}
                    onChange={(e) => setSelectedSubCategory(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">اختر الفئة</option>
                    {categories[activeSection]?.map((category, index) => (
                      <option key={index} value={category}>{category}</option>
                    ))}
                  </select>
                </div>
              )}

              {/* تاريخ المستند */}
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">تاريخ المستند</label>
                <input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* معلومات إضافية */}
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">ملاحظات (اختياري)</label>
                <textarea
                  placeholder="أدخل أي ملاحظات إضافية..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 h-20 resize-none"
                />
              </div>

              {/* اختيار الملف */}
              <div className="mb-4">
                <label className="block text-sm font-medium mb-2">الملف</label>
                <input
                  type="file"
                  onChange={(e) => setUploadFile(e.target.files[0])}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                />
                <p className="text-xs text-gray-500 mt-1">
                  الصيغ المدعومة: PDF, DOC, DOCX, JPG, PNG
                </p>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => {
                    // سيتم تطوير وظيفة الرفع لاحقاً
                    alert('سيتم تطوير وظيفة رفع الملفات قريباً');
                    setShowUploadModal(false);
                  }}
                  className="flex-1 bg-green-600 text-white py-2 rounded-lg hover:bg-green-700"
                >
                  رفع الملف
                </button>
                <button
                  onClick={() => {
                    setShowUploadModal(false);
                    setUploadFile(null);
                    setUploadCode('');
                    setUploadDocType('');
                  }}
                  className="flex-1 bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400"
                >
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
