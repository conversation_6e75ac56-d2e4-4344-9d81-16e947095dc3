'use client';

import React, { useState, useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { useLanguage } from '@/contexts/LanguageContext';
import MainLayout from '@/components/MainLayout';
import { Calendar, User, FileText, Clock, Search, Download, RefreshCw } from 'lucide-react';

export default function LeaveBalancesPage() {
  const { isDarkMode } = useTheme();
  const { isRTL, isArabic } = useLanguage();
  
  const [balances, setBalances] = useState([]);
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  // جلب أرصدة الإجازات
  const fetchBalances = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/leave-balances', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'list',
          searchTerm
        })
      });

      const result = await response.json();
      
      if (result.success) {
        setBalances(result.data || []);
        setMessage('');
      } else {
        setMessage(result.error || 'فشل في جلب البيانات');
        setBalances([]);
      }
    } catch (error) {

      setMessage('خطأ في الاتصال بالخادم');
      setBalances([]);
    }
    setLoading(false);
  };

  // تحديث رصيد موظف
  const updateBalance = async (employeeCode, regularBalance, casualBalance) => {
    try {
      const response = await fetch('/api/leave-balances', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'update',
          employeeCode,
          regularBalance,
          casualBalance
        })
      });

      const result = await response.json();
      
      if (result.success) {
        setMessage('تم تحديث الرصيد بنجاح');
        fetchBalances(); // إعادة جلب البيانات
      } else {
        setMessage(result.error || 'فشل في تحديث الرصيد');
      }
    } catch (error) {

      setMessage('خطأ في تحديث الرصيد');
    }
  };

  // تحميل البيانات عند بداية الصفحة
  useEffect(() => {
    fetchBalances();
  }, []);

  // فلترة البيانات حسب البحث
  const filteredBalances = balances.filter(balance => 
    balance.EmployeeCode?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    balance.EmployeeName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    balance.JobTitle?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // حساب الإحصائيات
  const stats = {
    totalEmployees: filteredBalances.length,
    totalRegularDays: filteredBalances.reduce((sum, b) => sum + (b.RemainingRegular || b.RegularBalance || 0), 0),
    totalCasualDays: filteredBalances.reduce((sum, b) => sum + (b.RemainingCasual || b.CasualBalance || 0), 0),
    employeesWithFullBalance: filteredBalances.filter(b => 
      (b.RemainingRegular || b.RegularBalance) >= 15 && 
      (b.RemainingCasual || b.CasualBalance) >= 6
    ).length
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto">
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="flex items-center justify-between">
            <div>
              <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
                أرصدة الإجازات
              </h1>
              <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                عرض وإدارة أرصدة إجازات جميع الموظفين
              </p>
            </div>
            <div className="flex gap-3">
              <button
                onClick={fetchBalances}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
              >
                <RefreshCw className="w-4 h-4" />
                {loading ? 'جاري التحميل...' : 'تحديث'}
              </button>
              <a
                href="/leaves/request"
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <FileText className="w-4 h-4" />
                طلب إجازة جديد
              </a>
            </div>
          </div>
        </div>

        {/* رسائل التنبيه */}
        {message && (
          <div className={`p-4 rounded-lg mb-6 ${
            message.includes('نجاح') || message.includes('تم')
              ? 'bg-green-100 text-green-800 border border-green-200'
              : 'bg-red-100 text-red-800 border border-red-200'
          }`}>
            <div className="flex items-center gap-2">
              {message.includes('نجاح') || message.includes('تم') ? (
                <span className="text-green-600 font-bold">✓</span>
              ) : (
                <span className="text-red-600 font-bold">✗</span>
              )}
              {message}
            </div>
          </div>
        )}

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <User className="h-8 w-8 text-blue-600" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className={`text-sm font-medium ${isDarkMode ? 'text-slate-400' : 'text-gray-500'} truncate`}>
                    إجمالي الموظفين
                  </dt>
                  <dd className={`text-lg font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {stats.totalEmployees}
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Calendar className="h-8 w-8 text-green-600" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className={`text-sm font-medium ${isDarkMode ? 'text-slate-400' : 'text-gray-500'} truncate`}>
                    إجمالي الأيام الاعتيادية
                  </dt>
                  <dd className={`text-lg font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {stats.totalRegularDays} يوم
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-8 w-8 text-yellow-600" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className={`text-sm font-medium ${isDarkMode ? 'text-slate-400' : 'text-gray-500'} truncate`}>
                    إجمالي الأيام العارضة
                  </dt>
                  <dd className={`text-lg font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {stats.totalCasualDays} يوم
                  </dd>
                </dl>
              </div>
            </div>
          </div>

          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FileText className="h-8 w-8 text-purple-600" />
              </div>
              <div className="mr-5 w-0 flex-1">
                <dl>
                  <dt className={`text-sm font-medium ${isDarkMode ? 'text-slate-400' : 'text-gray-500'} truncate`}>
                    رصيد كامل
                  </dt>
                  <dd className={`text-lg font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {stats.employeesWithFullBalance} موظف
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        {/* شريط البحث */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`w-full pr-10 pl-4 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                    isDarkMode 
                      ? 'bg-slate-800 border-slate-600 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                  placeholder="البحث بكود الموظف أو الاسم أو الوظيفة..."
                />
              </div>
            </div>
            <button
              onClick={() => setSearchTerm('')}
              className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors"
            >
              مسح
            </button>
          </div>
        </div>

        {/* جدول أرصدة الإجازات */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border overflow-hidden`}>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className={`${isDarkMode ? 'bg-slate-800' : 'bg-gray-50'}`}>
                <tr>
                  <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    كود الموظف
                  </th>
                  <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    اسم الموظف
                  </th>
                  <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    الوظيفة
                  </th>
                  <th className={`px-6 py-3 text-center text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    الإجازة الاعتيادية
                  </th>
                  <th className={`px-6 py-3 text-center text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    الإجازة العارضة
                  </th>
                  <th className={`px-6 py-3 text-center text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    آخر إجازة
                  </th>
                  <th className={`px-6 py-3 text-center text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} divide-y ${isDarkMode ? 'divide-slate-700' : 'divide-gray-200'}`}>
                {loading ? (
                  <tr>
                    <td colSpan="7" className="px-6 py-12 text-center">
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        <span className={`mr-3 ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                          جاري تحميل البيانات...
                        </span>
                      </div>
                    </td>
                  </tr>
                ) : filteredBalances.length === 0 ? (
                  <tr>
                    <td colSpan="7" className="px-6 py-12 text-center">
                      <div className="flex flex-col items-center">
                        <User className={`w-12 h-12 ${isDarkMode ? 'text-slate-400' : 'text-gray-400'} mb-4`} />
                        <p className={`text-lg font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                          لا توجد بيانات أرصدة
                        </p>
                        <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-500'} mt-2`}>
                          لم يتم العثور على أي أرصدة إجازات
                        </p>
                      </div>
                    </td>
                  </tr>
                ) : (
                  filteredBalances.map((balance) => (
                    <tr key={balance.EmployeeCode} className={`hover:${isDarkMode ? 'bg-slate-800' : 'bg-gray-50'} transition-colors`}>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {balance.EmployeeCode}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                        {balance.EmployeeName}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                        {balance.JobTitle}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <div className="flex flex-col items-center space-y-1">
                          <div className="flex items-center space-x-2">
                            <span className={`text-lg font-bold ${
                              (balance.RemainingRegular || balance.RegularBalance) > 10
                                ? 'text-green-600'
                                : (balance.RemainingRegular || balance.RegularBalance) > 5
                                  ? 'text-yellow-600'
                                  : 'text-red-600'
                            }`}>
                              {balance.RemainingRegular || balance.RegularBalance || 0}
                            </span>
                            <span className="text-xs text-gray-400">/ 15</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${
                                (balance.RemainingRegular || balance.RegularBalance) > 10
                                  ? 'bg-green-500'
                                  : (balance.RemainingRegular || balance.RegularBalance) > 5
                                    ? 'bg-yellow-500'
                                    : 'bg-red-500'
                              }`}
                              style={{
                                width: `${Math.min(((balance.RemainingRegular || balance.RegularBalance) / 15) * 100, 100)}%`
                              }}
                            ></div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        <div className="flex flex-col items-center space-y-1">
                          <div className="flex items-center space-x-2">
                            <span className={`text-lg font-bold ${
                              (balance.RemainingCasual || balance.CasualBalance) > 4
                                ? 'text-green-600'
                                : (balance.RemainingCasual || balance.CasualBalance) > 2
                                  ? 'text-yellow-600'
                                  : 'text-red-600'
                            }`}>
                              {balance.RemainingCasual || balance.CasualBalance || 0}
                            </span>
                            <span className="text-xs text-gray-400">/ 6</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${
                                (balance.RemainingCasual || balance.CasualBalance) > 4
                                  ? 'bg-green-500'
                                  : (balance.RemainingCasual || balance.CasualBalance) > 2
                                    ? 'bg-yellow-500'
                                    : 'bg-red-500'
                              }`}
                              style={{
                                width: `${Math.min(((balance.RemainingCasual || balance.CasualBalance) / 6) * 100, 100)}%`
                              }}
                            ></div>
                          </div>
                        </div>
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-center text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                        {balance.LastLeaveDate ? new Date(balance.LastLeaveDate).toLocaleDateString('ar-EG') : 'لا توجد'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center text-sm font-medium">
                        <div className="flex justify-center gap-2">
                          <button
                            onClick={() => {
                              const newRegular = prompt('الرصيد الاعتيادي الجديد:', balance.RemainingRegular || balance.RegularBalance);
                              const newCasual = prompt('الرصيد العارض الجديد:', balance.RemainingCasual || balance.CasualBalance);
                              if (newRegular !== null && newCasual !== null) {
                                updateBalance(balance.EmployeeCode, parseInt(newRegular), parseInt(newCasual));
                              }
                            }}
                            className="text-blue-600 hover:text-blue-900 transition-colors"
                            title="تعديل الرصيد"
                          >
                            <FileText className="w-4 h-4" />
                          </button>
                          <a
                            href={`/leaves/request?employee=${balance.EmployeeCode}`}
                            className="text-green-600 hover:text-green-900 transition-colors"
                            title="طلب إجازة"
                          >
                            <Calendar className="w-4 h-4" />
                          </a>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
