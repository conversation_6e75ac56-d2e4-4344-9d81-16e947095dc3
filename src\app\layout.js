'use client';
import { LanguageProvider } from '@/contexts/LanguageContext';
import { ThemeProvider } from '@/contexts/ThemeContext';
import { initializeFonts } from '@/utils/fontLoader';
import { useEffect } from 'react';
import '../styles/date-input.css';
import '../styles/sidebar.css';
import './globals.css';

export default function RootLayout({ children }) {
  useEffect(() => {
    // تهيئة الخطوط المحلية
    initializeFonts();
  }, []);

  return (
    <html lang="ar" dir="rtl">
      <head>
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
        />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // تطبيق الثيم المحفوظ فوراً لتجنب الوميض
              (function() {
                try {
                  const savedTheme = localStorage.getItem('theme');
                  if (savedTheme === 'dark') {
                    document.documentElement.classList.add('dark');
                  } else if (savedTheme === 'light') {
                    document.documentElement.classList.remove('dark');
                  } else {
                    // استخدام تفضيل النظام
                    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                    if (prefersDark) {
                      document.documentElement.classList.add('dark');
                      localStorage.setItem('theme', 'dark');
                    } else {
                      localStorage.setItem('theme', 'light');
                    }
                  }
                } catch (e) {

                }
              })();

              // سكريپت لتحسين عرض الجداول ومنع كسر النصوص
              function applyTableOptimizations() {
                const tables = document.querySelectorAll('table');
                tables.forEach(table => {
                  // تحسين تخطيط الجدول
                  if (table.classList.contains('table-fixed')) {
                    table.style.tableLayout = 'fixed';
                  } else {
                    table.style.tableLayout = 'auto';
                  }
                  table.style.width = '100%';

                  const cells = table.querySelectorAll('td, th');
                  cells.forEach(cell => {
                    cell.style.whiteSpace = 'nowrap';
                    cell.style.wordBreak = 'keep-all';
                    cell.style.overflowWrap = 'normal';

                    // للجداول ذات العرض الثابت، استخدم ellipsis
                    if (table.classList.contains('table-fixed')) {
                      cell.style.textOverflow = 'ellipsis';
                      cell.style.overflow = 'hidden';
                    } else {
                      cell.style.textOverflow = 'clip';
                      cell.style.overflow = 'visible';
                    }

                    // تطبيق على جميع العناصر الفرعية
                    const childElements = cell.querySelectorAll('*');
                    childElements.forEach(child => {
                      child.style.whiteSpace = 'nowrap';
                      child.style.wordBreak = 'keep-all';
                      child.style.overflowWrap = 'normal';
                    });
                  });
                });
              }

              // تطبيق عند تحميل الصفحة
              document.addEventListener('DOMContentLoaded', applyTableOptimizations);

              // تطبيق عند إضافة عناصر جديدة
              const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                  if (mutation.addedNodes.length > 0) {
                    setTimeout(applyTableOptimizations, 100);
                  }
                });
              });

              if (typeof document !== 'undefined') {
                observer.observe(document.body, {
                  childList: true,
                  subtree: true
                });
              }
            `,
          }}
        />
      </head>
      <body className="font-sans">
        <LanguageProvider>
          <ThemeProvider>
            <div className="min-h-screen">
              {children}
            </div>
          </ThemeProvider>
        </LanguageProvider>
      </body>
    </html>
  );
}
