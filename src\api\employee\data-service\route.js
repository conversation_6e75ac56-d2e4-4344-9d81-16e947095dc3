async function handler({ action, data }) {
  const session = getSession();
  if (!session?.user) {
    return { error: 'غير مصرح به' };
  }

  switch (action) {
    case 'create':
      return await createEmployee(data);
    case 'update':
      return await updateEmployee(data);
    case 'search':
      return await searchEmployees(data);
    case 'delete':
      return await deleteEmployee(data);
    case 'list':
      return await listEmployees(data);
    case 'upload':
      return await handleDocument(data);
    default:
      return { error: 'إجراء غير صالح' };
  }
}

async function createEmployee(data) {
  try {
    const result = await sql`
      INSERT INTO monthly_attendance (
        employee_id, employee_name, job_title, department,
        birth_date, national_id, phone_number, education_level,
        hire_date, status
      ) VALUES (
        ${data.employee_id}, ${data.name}, ${data.job_title}, ${data.department},
        ${data.birth_date}, ${data.national_id}, ${data.phone_number}, ${data.education_level},
        ${data.hire_date}, 'active'
      ) RETURNING *`;
    return { success: true, employee: result[0] };
  } catch (error) {
    return { error: 'فشل في إنشاء الموظف' };
  }
}

async function updateEmployee(data) {
  try {
    const setValues = [];
    const queryParams = [];
    let paramCount = 1;

    Object.entries(data.updates).forEach(([key, value]) => {
      if (value !== undefined && key !== 'employee_id') {
        setValues.push(`${key} = $${paramCount}`);
        queryParams.push(value);
        paramCount++;
      }
    });

    queryParams.push(data.employee_id);

    const query = `
      UPDATE monthly_attendance 
      SET ${setValues.join(', ')}, 
          updated_at = CURRENT_TIMESTAMP 
      WHERE employee_id = $${paramCount} 
      RETURNING *`;

    const result = await sql(query, queryParams);
    return { success: true, employee: result[0] };
  } catch (error) {
    return { error: 'فشل في تحديث الموظف' };
  }
}

async function searchEmployees(data) {
  try {
    const searchTerm = `%${data.term}%`;
    const results = await sql`
      SELECT * FROM monthly_attendance 
      WHERE 
        employee_name ILIKE ${searchTerm} 
        OR employee_id ILIKE ${searchTerm}
        OR national_id ILIKE ${searchTerm}
      ORDER BY employee_name
      LIMIT 50`;
    return { employees: results };
  } catch (error) {
    return { error: 'فشل في البحث' };
  }
}

async function deleteEmployee(data) {
  try {
    await sql.transaction([
      sql`DELETE FROM employee_documents WHERE employee_id = ${data.employee_id}`,
      sql`DELETE FROM monthly_attendance WHERE employee_id = ${data.employee_id}`,
    ]);
    return { success: true };
  } catch (error) {
    return { error: 'فشل في حذف الموظف' };
  }
}

async function listEmployees({
  sort = 'FullName',
  order = 'asc',
  filter = {},
  page = 1,
  limit = 50,
}) {
  try {
    const offset = (page - 1) * limit;
    const whereConditions = [];
    const queryParams = [];
    let paramCount = 1;

    Object.entries(filter).forEach(([key, value]) => {
      if (value) {
        whereConditions.push(`${key} = $${paramCount}`);
        queryParams.push(value);
        paramCount++;
      }
    });

    const whereClause = whereConditions.length
      ? `WHERE ${whereConditions.join(' AND ')}`
      : '';

    const query = `
      SELECT 
        emp.*,
        dept.Name as DepartmentName,
        job.Title as JobTitle
      FROM Employees emp
      LEFT JOIN Departments dept ON emp.DepartmentID = dept.DepartmentID
      LEFT JOIN JobTitles job ON emp.JobTitleID = job.JobTitleID
      ${whereClause} 
      ORDER BY ${sort} ${order}
      LIMIT $${paramCount} OFFSET $${paramCount + 1}`;

    queryParams.push(limit, offset);

    const results = await sql(query, queryParams);
    const totalCount = await sql`
      SELECT COUNT(*) FROM Employees emp
      ${whereClause ? sql(whereClause, queryParams.slice(0, -2)) : sql``}
    `;

    return {
      employees: results,
      total: parseInt(totalCount[0].count),
      page,
      totalPages: Math.ceil(parseInt(totalCount[0].count) / limit),
    };
  } catch (error) {
    return { error: 'فشل في استرجاع قائمة الموظفين' };
  }
}

async function handleDocument({ employee_id, document_type_id, file }) {
  try {
    const { url, error } = await upload({ base64: file });
    if (error) throw new Error(error);

    const result = await sql`
      INSERT INTO employee_documents (
        employee_id, document_type_id, document_url, status
      ) VALUES (
        ${employee_id}, ${document_type_id}, ${url}, 'active'
      ) RETURNING *`;

    return { success: true, document: result[0] };
  } catch (error) {
    return { error: 'فشل في معالجة الوثيقة' };
  }
}
