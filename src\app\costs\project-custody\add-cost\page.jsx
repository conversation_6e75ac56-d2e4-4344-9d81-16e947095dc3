'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import { useTheme } from '@/contexts/ThemeContext';
import {
  FiPlus,
  FiSave,
  FiRefreshCw,
  FiSearch,
  FiEdit3,
  FiTrash2,
  FiEye,
  FiClock,
  FiCheck,
  FiX,
  FiAlertCircle
} from 'react-icons/fi';

export default function AddCostPage() {
  const { isDarkMode } = useTheme();
  const [loading, setLoading] = useState(false);
  const [costs, setCosts] = useState([]);
  const [filteredCosts, setFilteredCosts] = useState([]);
  const [mainCategories, setMainCategories] = useState([]);
  const [subCategories, setSubCategories] = useState([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showAddMainCategoryModal, setShowAddMainCategoryModal] = useState(false);
  const [showAddSubCategoryModal, setShowAddSubCategoryModal] = useState(false);

  // فلاتر
  const [filterMainCategory, setFilterMainCategory] = useState('all');
  const [filterSubCategory, setFilterSubCategory] = useState('all');
  const [filterStatus, setFilterStatus] = useState('all');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  // بيانات النموذج
  const [formData, setFormData] = useState({
    mainCategoryId: '',
    subCategoryId: '',
    settlementNumber: '',
    amount: '',
    date: '',
    description: '',
    notes: ''
  });

  // بيانات نموذج البند الرئيسي
  const [mainCategoryForm, setMainCategoryForm] = useState({
    categoryName: '',
    categoryCode: '',
    description: ''
  });

  // بيانات نموذج البند الفرعي
  const [subCategoryForm, setSubCategoryForm] = useState({
    mainCategoryId: '',
    subCategoryName: '',
    subCategoryCode: '',
    description: ''
  });

  // حالات التسوية
  const settlementStatuses = [
    { key: 'pending', label: 'قيد المراجعة', color: 'bg-yellow-500', icon: FiClock },
    { key: 'approved', label: 'تمت التسوية', color: 'bg-green-500', icon: FiCheck },
    { key: 'rejected', label: 'رفض التسوية', color: 'bg-red-500', icon: FiX },
    { key: 'suspended', label: 'تسوية معطلة', color: 'bg-gray-500', icon: FiAlertCircle }
  ];

  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    filterCosts();
  }, [costs, filterMainCategory, filterSubCategory, filterStatus, startDate, endDate, searchTerm]);

  // تحميل البيانات الأولية
  const loadInitialData = async () => {
    setLoading(true);
    try {
      // تحميل البنود الرئيسية
      const categoriesResponse = await fetch('/api/project-custody', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'getMainCategories' })
      });

      const categoriesResult = await categoriesResponse.json();
      if (categoriesResult.success) {
        setMainCategories(categoriesResult.data || []);
      }

      // تحميل التكاليف
      await loadCosts();

    } catch (error) {

    }
    setLoading(false);
  };

  // تحميل البنود الفرعية حسب البند الرئيسي
  const loadSubCategories = async (mainCategoryId) => {
    if (mainCategoryId === 'all') {
      setSubCategories([]);
      return;
    }

    try {
      const response = await fetch('/api/project-custody', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getSubCategories',
          data: { mainCategoryId: parseInt(mainCategoryId) }
        })
      });

      const result = await response.json();
      if (result.success) {
        setSubCategories(result.data || []);

      } else {

        // بيانات تجريبية للبنود الفرعية
        const demoSubCategories = {
          1: [ // وجبات
            { id: 1, subCategoryName: 'وجبات كونكورد', mainCategoryId: 1 },
            { id: 2, subCategoryName: 'وجبات استشاري', mainCategoryId: 1 },
            { id: 3, subCategoryName: 'وجبات مالك', mainCategoryId: 1 },
            { id: 4, subCategoryName: 'قاعة اجتماعات', mainCategoryId: 1 }
          ],
          2: [ // مشروبات وأدوات نظافة
            { id: 5, subCategoryName: 'مشروبات كونكورد', mainCategoryId: 2 },
            { id: 6, subCategoryName: 'مشروبات استشاري', mainCategoryId: 2 },
            { id: 7, subCategoryName: 'مشروبات مالك', mainCategoryId: 2 },
            { id: 8, subCategoryName: 'قاعة اجتماعات', mainCategoryId: 2 }
          ],
          3: [ // انتقالات
            { id: 9, subCategoryName: 'انتقالات استشاري', mainCategoryId: 3 },
            { id: 10, subCategoryName: 'انتقالات مأموريات', mainCategoryId: 3 },
            { id: 11, subCategoryName: 'انتقالات بدل', mainCategoryId: 3 },
            { id: 12, subCategoryName: 'انتقالات بالخصم', mainCategoryId: 3 }
          ],
          4: [ // مشتريات
            { id: 13, subCategoryName: 'مشتريات موقع', mainCategoryId: 4 },
            { id: 14, subCategoryName: 'مشتريات مالك', mainCategoryId: 4 },
            { id: 15, subCategoryName: 'مشتريات استشاري', mainCategoryId: 4 }
          ],
          5: [ // سيارات الشركة
            { id: 16, subCategoryName: 'جراج', mainCategoryId: 5 },
            { id: 17, subCategoryName: 'جاز بدل', mainCategoryId: 5 },
            { id: 18, subCategoryName: 'مخالفة', mainCategoryId: 5 },
            { id: 19, subCategoryName: 'مأمورية', mainCategoryId: 5 },
            { id: 20, subCategoryName: 'ميزان', mainCategoryId: 5 },
            { id: 21, subCategoryName: 'كارتات', mainCategoryId: 5 }
          ],
          6: [ // شقق
            { id: 22, subCategoryName: 'عهدة مؤقتة لإيجار شقة', mainCategoryId: 6 },
            { id: 23, subCategoryName: 'صيانة', mainCategoryId: 6 },
            { id: 24, subCategoryName: 'مصاريف دورية', mainCategoryId: 6 }
          ],
          7: [ // خدمات موقع
            { id: 25, subCategoryName: 'كهرباء', mainCategoryId: 7 },
            { id: 26, subCategoryName: 'مياه', mainCategoryId: 7 },
            { id: 27, subCategoryName: 'إنترنت', mainCategoryId: 7 }
          ]
        };
        setSubCategories(demoSubCategories[mainCategoryId] || []);
      }
    } catch (error) {

      setSubCategories([]);
    }
  };

  // تحميل التكاليف
  const loadCosts = async () => {
    try {
      const response = await fetch('/api/project-custody-costs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'getCosts' })
      });

      const result = await response.json();
      if (result.success) {
        setCosts(result.data || []);
      } else {
        // بيانات تجريبية
        setCosts([
          {
            id: 1,
            settlementNumber: 'SET-2025-001',
            mainCategoryId: 1,
            subCategoryId: 1,
            mainCategoryName: 'وجبات',
            subCategoryName: 'وجبات كونكورد',
            amount: 5000,
            date: '2025-06-20',
            status: 'pending',
            description: 'وجبات للموظفين',
            notes: 'تسوية شهرية'
          },
          {
            id: 2,
            settlementNumber: 'SET-2025-002',
            mainCategoryId: 2,
            subCategoryId: 4,
            mainCategoryName: 'انتقالات',
            subCategoryName: 'انتقالات استشاري',
            amount: 3000,
            date: '2025-06-19',
            status: 'approved',
            description: 'مصاريف انتقالات',
            notes: 'تم الموافقة'
          },
          {
            id: 3,
            settlementNumber: 'SET-2025-003',
            mainCategoryId: 3,
            subCategoryId: 7,
            mainCategoryName: 'سيارات الشركة',
            subCategoryName: 'جراج',
            amount: 2500,
            date: '2025-06-18',
            status: 'rejected',
            description: 'صيانة السيارات',
            notes: 'مرفوض لعدم اكتمال المستندات'
          }
        ]);
      }

      // إضافة بيانات تجريبية للبنود الرئيسية إذا لم تكن موجودة
      if (mainCategories.length === 0) {
        setMainCategories([
          { id: 1, categoryName: 'وجبات', categoryCode: 'MEALS' },
          { id: 2, categoryName: 'مشروبات وأدوات نظافة', categoryCode: 'DRINKS_CLEANING' },
          { id: 3, categoryName: 'انتقالات', categoryCode: 'TRANSPORT' },
          { id: 4, categoryName: 'مشتريات', categoryCode: 'PURCHASES' },
          { id: 5, categoryName: 'سيارات الشركة', categoryCode: 'COMPANY_CARS' },
          { id: 6, categoryName: 'شقق', categoryCode: 'APARTMENTS' },
          { id: 7, categoryName: 'خدمات موقع', categoryCode: 'SITE_SERVICES' }
        ]);
      }
    } catch (error) {

    }
  };

  // فلترة التكاليف
  const filterCosts = () => {
    let filtered = costs;

    // فلترة حسب البند الرئيسي
    if (filterMainCategory !== 'all') {
      filtered = filtered.filter(cost => cost.mainCategoryId == filterMainCategory);
    }

    // فلترة حسب البند الفرعي
    if (filterSubCategory !== 'all') {
      filtered = filtered.filter(cost => cost.subCategoryId == filterSubCategory);
    }

    // فلترة حسب حالة التسوية
    if (filterStatus !== 'all') {
      filtered = filtered.filter(cost => cost.status === filterStatus);
    }

    // فلترة حسب التاريخ
    if (startDate) {
      filtered = filtered.filter(cost => new Date(cost.date) >= new Date(startDate));
    }

    if (endDate) {
      filtered = filtered.filter(cost => new Date(cost.date) <= new Date(endDate));
    }

    // فلترة حسب البحث (رقم التسوية، الوصف، الملاحظات)
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(cost =>
        (cost.settlementNumber && cost.settlementNumber.toLowerCase().includes(searchLower)) ||
        (cost.description && cost.description.toLowerCase().includes(searchLower)) ||
        (cost.notes && cost.notes.toLowerCase().includes(searchLower)) ||
        (cost.mainCategoryName && cost.mainCategoryName.toLowerCase().includes(searchLower)) ||
        (cost.subCategoryName && cost.subCategoryName.toLowerCase().includes(searchLower))
      );
    }

    setFilteredCosts(filtered);
  };

  // إضافة تكلفة جديدة
  const handleAddCost = async () => {
    if (!formData.settlementNumber || !formData.amount || !formData.mainCategoryId) {
      alert('يرجى ملء الحقول المطلوبة');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/project-custody-costs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'addCost',
          data: formData
        })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم إضافة التكلفة بنجاح');
        setShowAddModal(false);
        resetForm();
        loadCosts();
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {

      alert('خطأ في إضافة التكلفة');
    }
    setLoading(false);
  };

  // إعادة تعيين النموذج
  const resetForm = () => {
    setFormData({
      mainCategoryId: '',
      subCategoryId: '',
      settlementNumber: '',
      amount: '',
      date: '',
      description: '',
      notes: ''
    });
  };

  // إضافة بند رئيسي جديد
  const handleAddMainCategory = async () => {
    if (!mainCategoryForm.categoryName) {
      alert('يرجى إدخال اسم البند الرئيسي');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/project-custody', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'addMainCategory',
          data: mainCategoryForm
        })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم إضافة البند الرئيسي بنجاح');
        setShowAddMainCategoryModal(false);
        setMainCategoryForm({ categoryName: '', categoryCode: '', description: '' });
        loadInitialData(); // إعادة تحميل البيانات
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {

      alert('خطأ في إضافة البند الرئيسي');
    }
    setLoading(false);
  };

  // إضافة بند فرعي جديد
  const handleAddSubCategory = async () => {
    if (!subCategoryForm.subCategoryName || !subCategoryForm.mainCategoryId) {
      alert('يرجى إدخال اسم البند الفرعي واختيار البند الرئيسي');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/project-custody', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'addSubCategory',
          data: subCategoryForm
        })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم إضافة البند الفرعي بنجاح');
        setShowAddSubCategoryModal(false);
        setSubCategoryForm({ mainCategoryId: '', subCategoryName: '', subCategoryCode: '', description: '' });
        // إعادة تحميل البنود الفرعية للبند المختار
        if (subCategoryForm.mainCategoryId) {
          loadSubCategories(subCategoryForm.mainCategoryId);
        }
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {

      alert('خطأ في إضافة البند الفرعي');
    }
    setLoading(false);
  };

  // تنسيق المبلغ
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG').format(amount) + ' جنيه';
  };

  // تنسيق التاريخ
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('ar-EG');
  };

  // الحصول على معلومات الحالة
  const getStatusInfo = (status) => {
    return settlementStatuses.find(s => s.key === status) || settlementStatuses[0];
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto p-6">
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FiPlus className="text-3xl text-green-600" />
              <div>
                <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  إضافة تكلفة
                </h1>
                <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                  إدارة وإضافة تكاليف المشروع
                </p>
              </div>
            </div>
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
            >
              <FiPlus className="w-4 h-4" />
              إضافة تكلفة جديدة
            </button>
          </div>
        </div>

        {/* الفلاتر */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {/* البند الرئيسي */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                البند الرئيسي
              </label>
              <select
                value={filterMainCategory}
                onChange={(e) => {
                  setFilterMainCategory(e.target.value);
                  // إعادة تعيين البند الفرعي عند تغيير البند الرئيسي
                  setFilterSubCategory('all');
                  // تحميل البنود الفرعية للبند المختار
                  loadSubCategories(e.target.value);
                }}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="all">جميع البنود الرئيسية</option>
                {mainCategories.map((category) => (
                  <option key={category.id} value={category.id}>{category.categoryName}</option>
                ))}
              </select>
            </div>

            {/* البند الفرعي */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                البند الفرعي
              </label>
              <select
                value={filterSubCategory}
                onChange={(e) => setFilterSubCategory(e.target.value)}
                disabled={filterMainCategory === 'all'}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  filterMainCategory === 'all' ? 'opacity-50 cursor-not-allowed' : ''
                } ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="all">
                  {filterMainCategory === 'all' ? 'اختر البند الرئيسي أولاً' : 'جميع البنود الفرعية'}
                </option>
                {subCategories.map((subCategory) => (
                  <option key={subCategory.id} value={subCategory.id}>
                    {subCategory.subCategoryName}
                  </option>
                ))}
              </select>
            </div>

            {/* من تاريخ */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                من تاريخ
              </label>
              <input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>

            {/* إلى تاريخ */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                إلى تاريخ
              </label>
              <input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>

            {/* البحث */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                البحث
              </label>
              <div className="relative">
                <FiSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="رقم التسوية، الوصف، الملاحظات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`w-full pr-10 pl-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                    isDarkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                />
              </div>
            </div>
          </div>

          {/* الصف الثاني للفلاتر */}
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4 mt-4">
            {/* حالة التسوية */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                حالة التسوية
              </label>
              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="all">جميع الحالات</option>
                <option value="pending">قيد المراجعة</option>
                <option value="approved">تمت التسوية</option>
                <option value="rejected">رفض التسوية</option>
                <option value="suspended">تسوية معطلة</option>
              </select>
            </div>

            {/* إضافة بند رئيسي */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                إدارة البنود
              </label>
              <button
                onClick={() => setShowAddMainCategoryModal(true)}
                className="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors"
              >
                <FiPlus className="w-4 h-4" />
                بند رئيسي
              </button>
            </div>

            {/* إضافة بند فرعي */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                &nbsp;
              </label>
              <button
                onClick={() => setShowAddSubCategoryModal(true)}
                className="w-full bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors"
              >
                <FiPlus className="w-4 h-4" />
                بند فرعي
              </button>
            </div>

            {/* مسح الفلاتر */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                إعادة تعيين
              </label>
              <button
                onClick={() => {
                  setFilterMainCategory('all');
                  setFilterSubCategory('all');
                  setFilterStatus('all');
                  setStartDate('');
                  setEndDate('');
                  setSearchTerm('');
                  setSubCategories([]);
                }}
                className="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors"
              >
                <FiX className="w-4 h-4" />
                مسح الفلاتر
              </button>
            </div>

            {/* أزرار العمليات */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                العمليات
              </label>
              <button
                onClick={() => setShowAddModal(true)}
                className="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors"
              >
                <FiPlus className="w-4 h-4" />
                إضافة تكلفة
              </button>
            </div>

            {/* تحديث */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                تحديث البيانات
              </label>
              <button
                onClick={loadCosts}
                disabled={loading}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors disabled:opacity-50"
              >
                <FiRefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                تحديث
              </button>
            </div>
          </div>
        </div>

        {/* جدول التكاليف */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border`}>
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              قائمة التكاليف ({filteredCosts.length})
            </h2>
          </div>

          <div className="overflow-x-auto">
            {loading ? (
              <div className="flex justify-center items-center py-12">
                <FiRefreshCw className="w-6 h-6 animate-spin text-green-500" />
                <span className="mr-2">جاري التحميل...</span>
              </div>
            ) : filteredCosts.length === 0 ? (
              <div className="text-center py-12">
                <FiPlus className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className={`text-lg ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                  لا توجد تكاليف مسجلة
                </p>
              </div>
            ) : (
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className={isDarkMode ? 'bg-gray-800' : 'bg-gray-50'}>
                  <tr>
                    <th className={`px-6 py-3 text-right text-xs font-medium uppercase tracking-wider ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-500'
                    }`}>
                      م
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium uppercase tracking-wider ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-500'
                    }`}>
                      رقم التسوية
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium uppercase tracking-wider ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-500'
                    }`}>
                      اسم البند الأساسي
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium uppercase tracking-wider ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-500'
                    }`}>
                      البند الفرعي
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium uppercase tracking-wider ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-500'
                    }`}>
                      القيمة
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium uppercase tracking-wider ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-500'
                    }`}>
                      التاريخ
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium uppercase tracking-wider ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-500'
                    }`}>
                      إجراء
                    </th>
                  </tr>
                </thead>
                <tbody className={`divide-y ${isDarkMode ? 'divide-gray-700 bg-gray-800' : 'divide-gray-200 bg-white'}`}>
                  {filteredCosts.map((cost, index) => {
                    const statusInfo = getStatusInfo(cost.status);
                    const StatusIcon = statusInfo.icon;

                    return (
                      <tr key={cost.id} className={isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'}>
                        <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`}>
                          {index + 1}
                        </td>
                        <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          {cost.settlementNumber}
                        </td>
                        <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          {cost.mainCategoryName}
                        </td>
                        <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                          {cost.subCategoryName || '-'}
                        </td>
                        <td className={`px-6 py-4 whitespace-nowrap text-sm font-bold ${isDarkMode ? 'text-green-400' : 'text-green-600'}`}>
                          {formatCurrency(cost.amount)}
                        </td>
                        <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                          {formatDate(cost.date)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center gap-2">
                            <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full text-white ${statusInfo.color}`}>
                              <StatusIcon className="w-3 h-3 mr-1" />
                              {statusInfo.label}
                            </span>
                            <button
                              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                              title="عرض التفاصيل"
                            >
                              <FiEye className="w-4 h-4" />
                            </button>
                            <button
                              className="text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300"
                              title="تعديل"
                            >
                              <FiEdit3 className="w-4 h-4" />
                            </button>
                            <button
                              className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                              title="حذف"
                            >
                              <FiTrash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            )}
          </div>
        </div>

        {/* نموذج إضافة تكلفة جديدة */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-xl max-w-2xl w-full`}>
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h3 className={`text-lg font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    إضافة تكلفة جديدة
                  </h3>
                  <button
                    onClick={() => {
                      setShowAddModal(false);
                      resetForm();
                    }}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <FiX className="w-6 h-6" />
                  </button>
                </div>
              </div>

              <div className="p-6">
                <div className="grid grid-cols-1 gap-4">
                  {/* رقم التسوية */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      رقم التسوية *
                    </label>
                    <input
                      type="text"
                      value={formData.settlementNumber}
                      onChange={(e) => setFormData(prev => ({ ...prev, settlementNumber: e.target.value }))}
                      placeholder="مثال: SET-2025-001"
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>

                  {/* البند الرئيسي */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      البند الرئيسي *
                    </label>
                    <select
                      value={formData.mainCategoryId}
                      onChange={(e) => setFormData(prev => ({ ...prev, mainCategoryId: e.target.value }))}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    >
                      <option value="">اختر البند الرئيسي</option>
                      {mainCategories.map((category) => (
                        <option key={category.id} value={category.id}>{category.categoryName}</option>
                      ))}
                    </select>
                  </div>

                  {/* القيمة */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      القيمة (جنيه) *
                    </label>
                    <input
                      type="number"
                      value={formData.amount}
                      onChange={(e) => setFormData(prev => ({ ...prev, amount: e.target.value }))}
                      placeholder="0.00"
                      min="0"
                      step="0.01"
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>

                  {/* التاريخ */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      التاريخ *
                    </label>
                    <input
                      type="date"
                      value={formData.date}
                      onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>

                  {/* الوصف */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      الوصف
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      rows={3}
                      placeholder="وصف التكلفة..."
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>

                  {/* الملاحظات */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      الملاحظات
                    </label>
                    <textarea
                      value={formData.notes}
                      onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                      rows={2}
                      placeholder="ملاحظات إضافية..."
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-3 mt-6">
                  <button
                    onClick={() => {
                      setShowAddModal(false);
                      resetForm();
                    }}
                    className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    إلغاء
                  </button>
                  <button
                    onClick={handleAddCost}
                    disabled={loading}
                    className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
                  >
                    <FiSave className="w-4 h-4" />
                    حفظ التكلفة
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* نموذج إضافة بند رئيسي */}
        {showAddMainCategoryModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-xl max-w-lg w-full`}>
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h3 className={`text-lg font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    إضافة بند رئيسي جديد
                  </h3>
                  <button
                    onClick={() => {
                      setShowAddMainCategoryModal(false);
                      setMainCategoryForm({ categoryName: '', categoryCode: '', description: '' });
                    }}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <FiX className="w-6 h-6" />
                  </button>
                </div>
              </div>

              <div className="p-6">
                <div className="space-y-4">
                  {/* اسم البند الرئيسي */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      اسم البند الرئيسي *
                    </label>
                    <input
                      type="text"
                      value={mainCategoryForm.categoryName}
                      onChange={(e) => setMainCategoryForm(prev => ({ ...prev, categoryName: e.target.value }))}
                      placeholder="مثال: وجبات"
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>

                  {/* كود البند */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      كود البند (اختياري)
                    </label>
                    <input
                      type="text"
                      value={mainCategoryForm.categoryCode}
                      onChange={(e) => setMainCategoryForm(prev => ({ ...prev, categoryCode: e.target.value }))}
                      placeholder="مثال: MEALS"
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>

                  {/* الوصف */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      الوصف (اختياري)
                    </label>
                    <textarea
                      value={mainCategoryForm.description}
                      onChange={(e) => setMainCategoryForm(prev => ({ ...prev, description: e.target.value }))}
                      rows={3}
                      placeholder="وصف البند الرئيسي..."
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-3 mt-6">
                  <button
                    onClick={() => {
                      setShowAddMainCategoryModal(false);
                      setMainCategoryForm({ categoryName: '', categoryCode: '', description: '' });
                    }}
                    className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    إلغاء
                  </button>
                  <button
                    onClick={handleAddMainCategory}
                    disabled={loading}
                    className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
                  >
                    <FiSave className="w-4 h-4" />
                    حفظ البند الرئيسي
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* نموذج إضافة بند فرعي */}
        {showAddSubCategoryModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-xl max-w-lg w-full`}>
              <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h3 className={`text-lg font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    إضافة بند فرعي جديد
                  </h3>
                  <button
                    onClick={() => {
                      setShowAddSubCategoryModal(false);
                      setSubCategoryForm({ mainCategoryId: '', subCategoryName: '', subCategoryCode: '', description: '' });
                    }}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <FiX className="w-6 h-6" />
                  </button>
                </div>
              </div>

              <div className="p-6">
                <div className="space-y-4">
                  {/* البند الرئيسي */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      البند الرئيسي *
                    </label>
                    <select
                      value={subCategoryForm.mainCategoryId}
                      onChange={(e) => setSubCategoryForm(prev => ({ ...prev, mainCategoryId: e.target.value }))}
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    >
                      <option value="">اختر البند الرئيسي</option>
                      {mainCategories.map((category) => (
                        <option key={category.id} value={category.id}>{category.categoryName}</option>
                      ))}
                    </select>
                  </div>

                  {/* اسم البند الفرعي */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      اسم البند الفرعي *
                    </label>
                    <input
                      type="text"
                      value={subCategoryForm.subCategoryName}
                      onChange={(e) => setSubCategoryForm(prev => ({ ...prev, subCategoryName: e.target.value }))}
                      placeholder="مثال: وجبات كونكورد"
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>

                  {/* كود البند الفرعي */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      كود البند الفرعي (اختياري)
                    </label>
                    <input
                      type="text"
                      value={subCategoryForm.subCategoryCode}
                      onChange={(e) => setSubCategoryForm(prev => ({ ...prev, subCategoryCode: e.target.value }))}
                      placeholder="مثال: MEALS_CONCORD"
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>

                  {/* الوصف */}
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                      الوصف (اختياري)
                    </label>
                    <textarea
                      value={subCategoryForm.description}
                      onChange={(e) => setSubCategoryForm(prev => ({ ...prev, description: e.target.value }))}
                      rows={3}
                      placeholder="وصف البند الفرعي..."
                      className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      }`}
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-3 mt-6">
                  <button
                    onClick={() => {
                      setShowAddSubCategoryModal(false);
                      setSubCategoryForm({ mainCategoryId: '', subCategoryName: '', subCategoryCode: '', description: '' });
                    }}
                    className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    إلغاء
                  </button>
                  <button
                    onClick={handleAddSubCategory}
                    disabled={loading}
                    className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
                  >
                    <FiSave className="w-4 h-4" />
                    حفظ البند الفرعي
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
