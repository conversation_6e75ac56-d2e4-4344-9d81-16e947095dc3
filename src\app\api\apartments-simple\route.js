import { NextResponse } from 'next/server';
import { getConnection } from '@/utils/db';

export async function GET(request) {
  try {

    const pool = await getConnection();
    
    // جلب الشقق من الجدول الموجود
    const result = await pool.request().query(`
      SELECT TOP 10
        ID,
        ApartmentCode,
        LandlordName,
        Address,
        StartDate,
        EndDate,
        RentAmount,
        InsuranceAmount,
        BacklogAmount
      FROM Apartments
      ORDER BY ID
    `);

    return NextResponse.json({
      success: true,
      message: `تم جلب ${result.recordset.length} شقة بنجاح`,
      data: result.recordset
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message,
      details: {
        name: error.name,
        code: error.code,
        state: error.state
      }
    }, { status: 500 });
  }
}
