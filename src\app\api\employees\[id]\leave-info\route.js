import { NextResponse } from 'next/server';
import sql from 'mssql';

// إعدادات قاعدة البيانات
const config = {
  user: process.env.DB_USER || 'sa',
  password: process.env.DB_PASSWORD || 'YourPassword123',
  server: process.env.DB_SERVER || 'localhost',
  database: process.env.DB_NAME || 'CompanyDB',
  options: {
    encrypt: false,
    trustServerCertificate: true,
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
};

export async function GET(request, { params }) {
  try {
    const pool = await sql.connect(config);
    const employeeId = params.id;

    // جلب معلومات الإجازة (بدون جدول LeaveRequests)
    let result;
    try {
      // محاولة جلب البيانات من جدول LeaveBalances مع حساب الإجازات المستخدمة من التمام اليومي
      const leaveInfoQuery = `
        SELECT
          ISNULL(lb.AnnualBalance, 15) as AnnualBalance,
          ISNULL(lb.CasualBalance, 6) as CasualBalance,
          (
            SELECT COUNT(*)
            FROM DailyAttendance da
            WHERE da.EmployeeCode = @employeeId
            AND da.Attendance LIKE N'%إجازة اعتيادية%'
            AND YEAR(da.AttendanceDate) = YEAR(GETDATE())
          ) as UsedRegular,
          (
            SELECT COUNT(*)
            FROM DailyAttendance da
            WHERE da.EmployeeCode = @employeeId
            AND da.Attendance LIKE N'%إجازة عارضة%'
            AND YEAR(da.AttendanceDate) = YEAR(GETDATE())
          ) as UsedCasual
        FROM LeaveBalances lb
        WHERE lb.EmployeeCode = @employeeId
      `;

      result = await pool.request()
        .input('employeeId', sql.NVarChar, employeeId)
        .query(leaveInfoQuery);

      // إذا لم يتم العثور على سجل في LeaveBalances، إنشاء قيم افتراضية
      if (result.recordset.length === 0) {
        const defaultQuery = `
          SELECT
            15 as AnnualBalance,
            6 as CasualBalance,
            (
              SELECT COUNT(*)
              FROM DailyAttendance da
              WHERE da.EmployeeCode = @employeeId
              AND da.Attendance LIKE N'%إجازة اعتيادية%'
              AND YEAR(da.AttendanceDate) = YEAR(GETDATE())
            ) as UsedRegular,
            (
              SELECT COUNT(*)
              FROM DailyAttendance da
              WHERE da.EmployeeCode = @employeeId
              AND da.Attendance LIKE N'%إجازة عارضة%'
              AND YEAR(da.AttendanceDate) = YEAR(GETDATE())
            ) as UsedCasual
        `;

        result = await pool.request()
          .input('employeeId', sql.NVarChar, employeeId)
          .query(defaultQuery);
      }
    } catch (error) {

      // في حالة فشل الاستعلام، إرجاع قيم افتراضية
      result = {
        recordset: [{
          AnnualBalance: 15,
          CasualBalance: 6,
          UsedRegular: 0,
          UsedCasual: 0
        }]
      };
    }

    if (result.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'لم يتم العثور على معلومات الإجازة للموظف'
      }, { status: 404 });
    }

    const leaveInfo = result.recordset[0];
    const remainingRegular = leaveInfo.AnnualBalance - leaveInfo.UsedRegular;
    const remainingCasual = leaveInfo.CasualBalance - leaveInfo.UsedCasual;

    return NextResponse.json({
      success: true,
      leaveInfo: {
        annual: {
          total: leaveInfo.AnnualBalance,
          used: leaveInfo.UsedRegular,
          remaining: remainingRegular
        },
        casual: {
          total: leaveInfo.CasualBalance,
          used: leaveInfo.UsedCasual,
          remaining: remainingCasual
        }
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب معلومات الإجازة: ' + error.message
    }, { status: 500 });
  }
}
