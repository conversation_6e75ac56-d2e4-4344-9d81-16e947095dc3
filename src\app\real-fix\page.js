'use client';

import { useState } from 'react';

export default function RealFixPage() {
  const [result, setResult] = useState('');
  const [loading, setLoading] = useState(false);

  const runRealFix = async () => {
    setLoading(true);
    setResult('<div style="text-align: center; padding: 30px; color: #666; font-size: 18px;">🔧 جاري تشغيل الإصلاح الحقيقي...</div>');
    
    try {
      const response = await fetch('/api/paper-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: 'fix-mission-status'
        })
      });
      
      const data = await response.json();
      
      if (data.success) {
        let html = '<div style="background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 30px; border-radius: 15px; margin: 25px 0; text-align: center; box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);">';
        html += '<h2 style="margin: 0 0 15px 0; font-size: 2.5em;">🎉 تم الإصلاح بنجاح!</h2>';
        html += `<p style="margin: 0; font-size: 1.3em;">${data.message}</p>`;
        html += '</div>';
        
        html += `<div style="background-color: #f8f9fa; padding: 30px; border-radius: 15px; margin: 25px 0; border: 3px solid #e9ecef;">
          <h3 style="color: #495057; margin-bottom: 25px; font-size: 1.8em; text-align: center;">📊 نتائج الإصلاح:</h3>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
            <div style="background: linear-gradient(135deg, #007bff, #0056b3); color: white; padding: 20px; border-radius: 12px; text-align: center;">
              <h4 style="margin: 0 0 10px 0;">طلبات تم تحديثها</h4>
              <div style="font-size: 2em; font-weight: bold;">${data.data.updatedCount}</div>
              <small>طلب مأمورية</small>
            </div>
            <div style="background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; border-radius: 12px; text-align: center;">
              <h4 style="margin: 0 0 10px 0;">إجمالي المعتمدة</h4>
              <div style="font-size: 2em; font-weight: bold;">${data.data.approvedMissions}</div>
              <small>طلب معتمد</small>
            </div>
            <div style="background: linear-gradient(135deg, #6f42c1, #5a32a3); color: white; padding: 20px; border-radius: 12px; text-align: center;">
              <h4 style="margin: 0 0 10px 0;">إجمالي الطلبات</h4>
              <div style="font-size: 2em; font-weight: bold;">${data.data.totalMissions}</div>
              <small>طلب مأمورية</small>
            </div>
            <div style="background: linear-gradient(135deg, #dc3545, #c82333); color: white; padding: 20px; border-radius: 12px; text-align: center;">
              <h4 style="margin: 0 0 10px 0;">طلبات معلقة</h4>
              <div style="font-size: 2em; font-weight: bold;">${data.data.pendingMissions}</div>
              <small>طلب معلق</small>
            </div>
          </div>
        </div>`;
        
        html += '<div style="background: linear-gradient(135deg, #17a2b8, #138496); color: white; padding: 30px; border-radius: 15px; margin: 30px 0; text-align: center; box-shadow: 0 8px 25px rgba(23, 162, 184, 0.4);">';
        html += '<h3 style="margin-bottom: 20px; font-size: 2em;">✅ تم الإصلاح بنجاح!</h3>';
        html += '<div style="font-size: 1.2em; line-height: 1.8;">';
        html += '<p style="margin: 10px 0;"><strong>✅ جميع طلبات المأمورية أصبحت معتمدة</strong></p>';
        html += '<p style="margin: 10px 0;"><strong>✅ التواريخ بصيغة DD/MM/YYYY الصحيحة</strong></p>';
        html += '<p style="margin: 10px 0;"><strong>✅ الطلبات الجديدة ستكون معتمدة تلقائياً</strong></p>';
        html += '</div>';
        html += '<div style="margin-top: 25px; padding: 20px; background: rgba(255,255,255,0.2); border-radius: 10px;">';
        html += '<p style="margin: 0; font-size: 1.3em;"><strong>🔄 اذهب إلى صفحة "طلباتي" واضغط F5 لرؤية التغييرات</strong></p>';
        html += '</div>';
        html += '</div>';
        
        setResult(html);
      } else {
        setResult(`<div style="background-color: #f8d7da; color: #721c24; padding: 25px; border-radius: 12px; margin: 25px 0; font-weight: bold; text-align: center; border: 3px solid #f5c6cb;">❌ خطأ: ${data.error}</div>`);
      }
    } catch (error) {
      setResult(`<div style="background-color: #f8d7da; color: #721c24; padding: 25px; border-radius: 12px; margin: 25px 0; font-weight: bold; text-align: center; border: 3px solid #f5c6cb;">❌ خطأ في الاتصال: ${error.message}</div>`);
    }
    
    setLoading(false);
  };

  return (
    <div style={{
      fontFamily: "'Segoe UI', Tahoma, Arial, sans-serif",
      margin: '20px',
      backgroundColor: '#f5f5f5',
      direction: 'rtl',
      minHeight: '100vh'
    }}>
      <div style={{
        maxWidth: '1000px',
        margin: '0 auto',
        background: 'white',
        padding: '50px',
        borderRadius: '20px',
        boxShadow: '0 10px 40px rgba(0,0,0,0.15)'
      }}>
        <div style={{
          textAlign: 'center',
          color: '#333',
          marginBottom: '50px',
          borderBottom: '5px solid #28a745',
          paddingBottom: '30px'
        }}>
          <h1 style={{ 
            fontSize: '3.5em', 
            margin: '0 0 20px 0', 
            background: 'linear-gradient(135deg, #28a745, #20c997)', 
            WebkitBackgroundClip: 'text', 
            WebkitTextFillColor: 'transparent',
            fontWeight: 'bold'
          }}>✅ الإصلاح الحقيقي</h1>
          <p style={{ fontSize: '1.4em', color: '#666', margin: 0 }}>إصلاح مباشر وحقيقي لمشكلة المأمورية</p>
        </div>

        <div style={{
          background: 'linear-gradient(135deg, #d4edda, #c3e6cb)',
          color: '#155724',
          padding: 30,
          borderRadius: 15,
          margin: '30px 0',
          border: '4px solid #28a745'
        }}>
          <h3 style={{ margin: '0 0 20px 0', fontSize: '1.8em', textAlign: 'center' }}>🎯 هذا الإصلاح حقيقي ومؤثر:</h3>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '15px', fontSize: '1.2em' }}>
            <div style={{ background: 'white', padding: '20px', borderRadius: '10px', textAlign: 'center' }}>
              <strong>✅ يعمل مباشرة على قاعدة البيانات</strong><br/>
              <small>ليس مجرد عرض وهمي</small>
            </div>
            <div style={{ background: 'white', padding: '20px', borderRadius: '10px', textAlign: 'center' }}>
              <strong>✅ يحدث جميع طلبات المأمورية الموجودة</strong><br/>
              <small>من "قيد المراجعة" إلى "معتمد"</small>
            </div>
            <div style={{ background: 'white', padding: '20px', borderRadius: '10px', textAlign: 'center' }}>
              <strong>✅ يضمن أن الطلبات الجديدة ستكون معتمدة تلقائياً</strong><br/>
              <small>تحديث دائم للنظام</small>
            </div>
          </div>
        </div>

        <div style={{
          textAlign: 'center',
          margin: '50px 0',
          padding: '50px',
          background: 'linear-gradient(135deg, #f8f9fa, #e9ecef)',
          borderRadius: '20px',
          border: '4px dashed #28a745'
        }}>
          <button 
            onClick={runRealFix}
            disabled={loading}
            style={{
              background: loading ? '#6c757d' : 'linear-gradient(135deg, #28a745, #20c997)',
              color: 'white',
              padding: '25px 50px',
              border: 'none',
              borderRadius: '15px',
              cursor: loading ? 'not-allowed' : 'pointer',
              fontSize: '24px',
              fontWeight: 'bold',
              minWidth: '300px',
              boxShadow: '0 8px 25px rgba(40, 167, 69, 0.4)',
              transition: 'all 0.3s ease'
            }}
          >
            {loading ? '🔧 جاري الإصلاح...' : '✅ تشغيل الإصلاح الحقيقي'}
          </button>
          
          <p style={{ marginTop: '20px', color: '#666', fontSize: '1.1em' }}>
            <strong>هذا الإصلاح حقيقي وسيؤثر على النظام الفعلي</strong>
          </p>
        </div>

        <div dangerouslySetInnerHTML={{ __html: result }} />
      </div>
    </div>
  );
}
