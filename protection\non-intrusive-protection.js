// نظام حماية غير مؤثر - يعمل في الخلفية فقط
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const os = require('os');

class NonIntrusiveProtection {
  constructor() {
    this.isEnabled = process.env.PROTECTION_ENABLED === 'true';
    this.logFile = path.join(__dirname, '../logs/protection.log');
    this.configFile = path.join(__dirname, '../.protection-config');
    
    // إنشاء مجلد logs إذا لم يكن موجوداً
    const logsDir = path.dirname(this.logFile);
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }
    
    this.init();
  }

  // تهيئة النظام
  init() {
    if (!this.isEnabled) {
      this.log('نظام الحماية معطل - النظام يعمل بشكل طبيعي');
      return;
    }
    
    this.log('تم تفعيل نظام الحماية غير المؤثر');
    this.startBackgroundMonitoring();
  }

  // تسجيل الأحداث
  log(message) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}\n`;
    
    try {
      fs.appendFileSync(this.logFile, logEntry);
    } catch (error) {
      // تجاهل أخطاء الكتابة لعدم التأثير على النظام
    }
  }

  // مراقبة في الخلفية (لا تؤثر على الأداء)
  startBackgroundMonitoring() {
    // مراقبة كل 10 دقائق
    setInterval(() => {
      this.performBackgroundCheck();
    }, 10 * 60 * 1000);
    
    // فحص أولي بعد 30 ثانية
    setTimeout(() => {
      this.performBackgroundCheck();
    }, 30000);
  }

  // فحص في الخلفية
  async performBackgroundCheck() {
    try {
      const systemInfo = this.collectSystemInfo();
      const usageStats = this.collectUsageStats();
      
      this.log(`فحص دوري - النظام: ${systemInfo.platform}, الذاكرة: ${systemInfo.memory}MB`);
      this.log(`إحصائيات الاستخدام - الملفات المفتوحة: ${usageStats.openFiles}`);
      
      // حفظ الإحصائيات (لا يؤثر على النظام)
      this.saveStats({ systemInfo, usageStats });
      
    } catch (error) {
      this.log(`خطأ في الفحص الدوري: ${error.message}`);
    }
  }

  // جمع معلومات النظام
  collectSystemInfo() {
    return {
      platform: os.platform(),
      arch: os.arch(),
      hostname: os.hostname(),
      memory: Math.round(os.totalmem() / 1024 / 1024),
      uptime: Math.round(os.uptime()),
      nodeVersion: process.version,
      timestamp: Date.now()
    };
  }

  // جمع إحصائيات الاستخدام
  collectUsageStats() {
    const memUsage = process.memoryUsage();
    
    return {
      memoryUsage: {
        rss: Math.round(memUsage.rss / 1024 / 1024),
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024)
      },
      uptime: Math.round(process.uptime()),
      pid: process.pid,
      openFiles: this.countOpenFiles(),
      timestamp: Date.now()
    };
  }

  // عد الملفات المفتوحة (تقديري)
  countOpenFiles() {
    try {
      // محاولة تقدير عدد الملفات المفتوحة
      return process.getActiveResourcesInfo ? 
        process.getActiveResourcesInfo().length : 0;
    } catch (error) {
      return 0;
    }
  }

  // حفظ الإحصائيات
  saveStats(stats) {
    try {
      const statsFile = path.join(__dirname, '../logs/stats.json');
      let allStats = [];
      
      // قراءة الإحصائيات الموجودة
      if (fs.existsSync(statsFile)) {
        const existingData = fs.readFileSync(statsFile, 'utf8');
        allStats = JSON.parse(existingData);
      }
      
      // إضافة الإحصائيات الجديدة
      allStats.push({
        timestamp: Date.now(),
        ...stats
      });
      
      // الاحتفاظ بآخر 100 إدخال فقط
      if (allStats.length > 100) {
        allStats = allStats.slice(-100);
      }
      
      // حفظ الإحصائيات
      fs.writeFileSync(statsFile, JSON.stringify(allStats, null, 2));
      
    } catch (error) {
      this.log(`خطأ في حفظ الإحصائيات: ${error.message}`);
    }
  }

  // إنشاء تقرير الاستخدام
  generateUsageReport() {
    try {
      const statsFile = path.join(__dirname, '../logs/stats.json');
      
      if (!fs.existsSync(statsFile)) {
        return { error: 'لا توجد إحصائيات متاحة' };
      }
      
      const stats = JSON.parse(fs.readFileSync(statsFile, 'utf8'));
      
      if (stats.length === 0) {
        return { error: 'لا توجد بيانات كافية' };
      }
      
      const latest = stats[stats.length - 1];
      const oldest = stats[0];
      
      const report = {
        period: {
          start: new Date(oldest.timestamp).toLocaleString('ar-EG'),
          end: new Date(latest.timestamp).toLocaleString('ar-EG'),
          duration: Math.round((latest.timestamp - oldest.timestamp) / (1000 * 60 * 60)) + ' ساعة'
        },
        system: latest.systemInfo,
        currentUsage: latest.usageStats,
        totalChecks: stats.length,
        averageMemory: Math.round(
          stats.reduce((sum, s) => sum + s.usageStats.memoryUsage.heapUsed, 0) / stats.length
        ) + ' MB'
      };
      
      return report;
      
    } catch (error) {
      return { error: `خطأ في إنشاء التقرير: ${error.message}` };
    }
  }

  // تفعيل/تعطيل النظام
  static enable() {
    process.env.PROTECTION_ENABLED = 'true';
    console.log('✅ تم تفعيل نظام الحماية غير المؤثر');
  }

  static disable() {
    process.env.PROTECTION_ENABLED = 'false';
    console.log('❌ تم تعطيل نظام الحماية');
  }

  // فحص حالة النظام
  static getStatus() {
    return {
      enabled: process.env.PROTECTION_ENABLED === 'true',
      version: '1.0.0',
      mode: 'non-intrusive',
      impact: 'zero'
    };
  }
}

// تصدير الكلاس
module.exports = NonIntrusiveProtection;

// تشغيل تلقائي إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  const protection = new NonIntrusiveProtection();
  
  // عرض التقرير كل دقيقة للاختبار
  setInterval(() => {
    const report = protection.generateUsageReport();
    console.log('📊 تقرير الاستخدام:', JSON.stringify(report, null, 2));
  }, 60000);
  
  console.log('🔐 نظام الحماية غير المؤثر يعمل في الخلفية...');
}
