const { getConnection } = require('../src/utils/db');

async function addMoreVersionRequests() {
  let pool;
  
  try {
    pool = await getConnection();
    
    console.log('📋 إضافة المزيد من طلبات الإصدار التجريبية...\n');
    
    // قائمة بالطلبات التجريبية
    const testRequests = [
      {
        costType: 'carscost',
        month: 12,
        year: 2024,
        totalAmount: 222477,
        itemsCount: 11,
        documentPath: 'carscost-12-2024.pdf'
      },
      {
        costType: 'housingcost',
        month: 11,
        year: 2024,
        totalAmount: 68000,
        itemsCount: 5,
        documentPath: 'housingcost-11-2024.pdf'
      },
      {
        costType: '3amala',
        month: 10,
        year: 2024,
        totalAmount: 293128,
        itemsCount: 61,
        documentPath: '3amala-10-2024.pdf'
      },
      {
        costType: 'carscost',
        month: 2,
        year: 2025,
        totalAmount: 230587,
        itemsCount: 11,
        documentPath: 'carscost-2-2025.pdf'
      },
      {
        costType: 'housingcost',
        month: 3,
        year: 2025,
        totalAmount: 73500,
        itemsCount: 6,
        documentPath: 'housingcost-3-2025.pdf'
      }
    ];
    
    let addedCount = 0;
    let updatedCount = 0;
    
    for (const request of testRequests) {
      // التحقق من وجود السجل
      const existingRecord = await pool.request()
        .input('costType', request.costType)
        .input('month', request.month)
        .input('year', request.year)
        .query(`
          SELECT ID FROM MonthlyCosts 
          WHERE CostType = @costType AND Month = @month AND Year = @year
        `);
      
      const monthName = getMonthName(request.month);
      const typeName = getTypeName(request.costType);
      
      if (existingRecord.recordset.length > 0) {
        // تحديث السجل الموجود
        await pool.request()
          .input('id', existingRecord.recordset[0].ID)
          .input('documentPath', request.documentPath)
          .input('notes', `تحديث تجريبي - ${new Date().toLocaleString('ar-EG')}`)
          .query(`
            UPDATE MonthlyCosts 
            SET DocumentPath = @documentPath,
                Notes = @notes,
                UpdatedAt = GETDATE()
            WHERE ID = @id
          `);
        
        console.log(`✅ تم تحديث: ${typeName} - ${monthName} ${request.year}`);
        updatedCount++;
      } else {
        // إضافة سجل جديد
        const averageCost = request.totalAmount / request.itemsCount;
        
        await pool.request()
          .input('costType', request.costType)
          .input('month', request.month)
          .input('year', request.year)
          .input('totalAmount', request.totalAmount)
          .input('itemsCount', request.itemsCount)
          .input('averageCost', averageCost)
          .input('documentPath', request.documentPath)
          .input('notes', `طلب إصدار تجريبي - ${new Date().toLocaleString('ar-EG')}`)
          .input('createdBy', 'نظام الاختبار')
          .query(`
            INSERT INTO MonthlyCosts (
              CostType, Month, Year, TotalAmount, ItemsCount, 
              AverageCostPerItem, DocumentPath, Notes, CreatedBy
            )
            VALUES (
              @costType, @month, @year, @totalAmount, @itemsCount,
              @averageCost, @documentPath, @notes, @createdBy
            )
          `);
        
        console.log(`➕ تم إضافة: ${typeName} - ${monthName} ${request.year}`);
        addedCount++;
      }
    }
    
    console.log(`\n📊 ملخص العملية:`);
    console.log(`   ➕ تم إضافة: ${addedCount} طلب جديد`);
    console.log(`   ✅ تم تحديث: ${updatedCount} طلب موجود`);
    
    // عرض جميع الطلبات مع المستندات
    console.log('\n📋 جميع طلبات الإصدار المتاحة:');
    const allRequests = await pool.request().query(`
      SELECT 
        CostType,
        Month,
        Year,
        TotalAmount,
        ItemsCount,
        DocumentPath,
        UpdatedAt,
        CreatedAt
      FROM MonthlyCosts 
      WHERE DocumentPath IS NOT NULL AND DocumentPath != ''
      ORDER BY Year DESC, Month DESC, UpdatedAt DESC
    `);
    
    allRequests.recordset.forEach((record, index) => {
      const monthName = getMonthName(record.Month);
      const typeName = getTypeName(record.CostType);
      const updateTime = record.UpdatedAt ? 
        record.UpdatedAt.toLocaleString('ar-EG') : 
        record.CreatedAt.toLocaleString('ar-EG');
      
      console.log(`  ${index + 1}. 📄 ${typeName} - ${monthName} ${record.Year}`);
      console.log(`     💰 ${record.TotalAmount.toLocaleString('ar-EG')} جنيه (${record.ItemsCount} عنصر)`);
      console.log(`     📅 ${updateTime}`);
    });
    
    console.log('\n🎯 تم الانتهاء! يمكنك الآن تحديث صفحة طلبات الإصدار لرؤية جميع التغييرات.');
    
  } catch (error) {
    console.error('❌ خطأ:', error);
  } finally {
    if (pool) {
      await pool.close();
    }
  }
}

function getMonthName(monthNumber) {
  const months = {
    1: 'يناير', 2: 'فبراير', 3: 'مارس', 4: 'أبريل',
    5: 'مايو', 6: 'يونيو', 7: 'يوليو', 8: 'أغسطس',
    9: 'سبتمبر', 10: 'أكتوبر', 11: 'نوفمبر', 12: 'ديسمبر'
  };
  return months[monthNumber] || 'غير محدد';
}

function getTypeName(costType) {
  const types = {
    'carscost': 'تكاليف السيارات',
    'housingcost': 'تكاليف الشقق',
    '3amala': 'تكاليف العمالة المؤقتة'
  };
  return types[costType] || costType;
}

addMoreVersionRequests();
