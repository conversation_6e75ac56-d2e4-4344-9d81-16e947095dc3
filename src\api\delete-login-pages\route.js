async function handler() {
  const session = getSession();
  if (!session?.user?.id) {
    return { error: 'Unauthorized' };
  }

  try {
    const duplicateModules = [
      'SimpleLogin',
      'UnifiedLogin',
      'LoginPage',
      'UnifiedLoginPage',
    ];

    const authFunctions = [
      'authenticateUser',
      'AuthenticateEmployee',
      'unified-auth-handler',
      'authenticate-employee-login',
      'authenticate-employee',
    ];

    const deletedPages = await sql`
      DELETE FROM modules 
      WHERE name = ANY(${duplicateModules})
      RETURNING id, name`;

    const deletedFunctions = await sql`
      DELETE FROM functions 
      WHERE name = ANY(${authFunctions})
      RETURNING id, name`;

    await sql`
      INSERT INTO activity_log 
      (user_id, action_type, entity_type, description)
      VALUES 
      (${session.user.id}, 'DELETE', 'LOGIN_PAGES', 'Cleaned up duplicate login pages and auth functions')`;

    return {
      success: true,
      deletedPages: deletedPages.length,
      deletedFunctions: deletedFunctions.length,
    };
  } catch (error) {
    return {
      error: 'Failed to cleanup login pages',
      details: error.message,
    };
  }
}
