/* تحسين عرض المستفيدين - ألوان واضحة */

.beneficiary-card {
  background: #ffffff !important;
  border: 2px solid #e5e7eb !important;
  border-radius: 12px !important;
  padding: 16px !important;
  margin: 8px 0 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.beneficiary-card:hover {
  border-color: #3b82f6 !important;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15) !important;
}

.employee-code {
  background: #1d4ed8 !important;
  color: #ffffff !important;
  padding: 6px 12px !important;
  border-radius: 20px !important;
  font-weight: bold !important;
  font-size: 14px !important;
}

.employee-name {
  color: #111827 !important;
  font-weight: bold !important;
  font-size: 16px !important;
  margin: 4px 0 !important;
}

.employee-job-title {
  color: #374151 !important;
  font-size: 14px !important;
  margin: 2px 0 !important;
}

.employee-department {
  color: #6b7280 !important;
  font-size: 13px !important;
  margin: 2px 0 !important;
}

/* تحسين الجدول */
.beneficiary-table-name {
  color: #1e40af !important;
  font-weight: bold !important;
  font-size: 18px !important;
}

/* تحسين الألوان في الوضع الداكن */
.dark .beneficiary-card {
  background: #1f2937 !important;
  border-color: #374151 !important;
}

.dark .employee-name {
  color: #f9fafb !important;
}

.dark .employee-job-title {
  color: #d1d5db !important;
}

.dark .employee-department {
  color: #9ca3af !important;
}

.dark .beneficiary-table-name {
  color: #60a5fa !important;
}
