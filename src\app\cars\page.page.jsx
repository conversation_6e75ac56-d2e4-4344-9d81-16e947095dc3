                </tbody>
                {statistics && (
                  <tfoot className="bg-gray-200 dark:bg-gray-800">
                    <tr className="font-bold text-lg text-gray-900 dark:text-gray-100">
                      <td className="px-6 py-3 text-right flex items-center gap-2" colSpan={5}>
                        <FiBarChart className="text-xl text-blue-600 dark:text-blue-400" />
                        الإجمالي
                      </td>
                      <td className="px-6 py-3 text-right">
                        <div className="flex items-center gap-1 justify-end">
                            <FiTruck className="text-blue-600 dark:text-blue-400" />
                            <span>{statistics.TotalActiveCars || 0} سيارة</span>
                        </div>
                      </td>
                      <td className="px-6 py-3 text-right">
                        <div className="flex items-center gap-1 justify-end">
                            <FiDollarSign className="text-green-600 dark:text-green-400" />
                            <span>{formatCurrency(statistics.TotalMonthlyRent)}</span>
                        </div>
                      </td>
                      <td className="px-6 py-3 text-right">
                        {/* Empty cell for actions column */}
                      </td>
                    </tr>
                  </tfoot>
                )}
              </table>
            </div> 