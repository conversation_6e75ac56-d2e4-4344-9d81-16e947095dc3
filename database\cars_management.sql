-- ===================================
-- جدول إدارة السيارات
-- ===================================

-- 1. جدول السيارات الأساسي
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Cars' AND xtype='U')
BEGIN
    CREATE TABLE Cars (
        ID INT IDENTITY(1,1) PRIMARY KEY,
        CarCode NVARCHAR(20) NOT NULL UNIQUE,
        ContractorName NVARCHAR(100) NOT NULL,
        CarNumber NVARCHAR(50) NOT NULL,
        CarType NVARCHAR(100) NOT NULL,
        CarModel NVARCHAR(100) NOT NULL,
        ManufactureYear INT,
        Route NVARCHAR(200) NOT NULL,
        RentAmount DECIMAL(10,2) NOT NULL,
        Notes NVARCHAR(MAX),
        IsActive BIT DEFAULT 1,
        CreatedAt DATETIME DEFAULT GETDATE(),
        UpdatedAt DATETIME DEFAULT GETDATE()
    )
    
    -- إنشاء الفهارس
    CREATE INDEX IX_Cars_Code ON Cars(CarCode)
    CREATE INDEX IX_Cars_Active ON Cars(IsActive)
    CREATE INDEX IX_Cars_CarNumber ON Cars(CarNumber)
    CREATE INDEX IX_Cars_Route ON Cars(Route)
END

-- 2. جدول المستفيدين من السيارات (ربط مع الموظفين)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='CarBeneficiaries' AND xtype='U')
BEGIN
    CREATE TABLE CarBeneficiaries (
        ID INT IDENTITY(1,1) PRIMARY KEY,
        CarCode NVARCHAR(20) NOT NULL,
        EmployeeCode NVARCHAR(20) NOT NULL,
        EmployeeName NVARCHAR(100) NOT NULL,
        JobTitle NVARCHAR(100) NOT NULL,
        Department NVARCHAR(100),
        StartDate DATE NOT NULL,
        EndDate DATE,
        IsActive BIT DEFAULT 1,
        CreatedAt DATETIME DEFAULT GETDATE(),
        UpdatedAt DATETIME DEFAULT GETDATE(),
        
        -- المفاتيح الخارجية
        FOREIGN KEY (CarCode) REFERENCES Cars(CarCode) ON DELETE CASCADE
    )
    
    -- إنشاء الفهارس
    CREATE INDEX IX_CarBeneficiaries_CarCode ON CarBeneficiaries(CarCode)
    CREATE INDEX IX_CarBeneficiaries_EmployeeCode ON CarBeneficiaries(EmployeeCode)
    CREATE INDEX IX_CarBeneficiaries_Active ON CarBeneficiaries(IsActive)
END

-- 3. إنشاء view شامل لعرض السيارات مع المستفيدين
IF NOT EXISTS (SELECT * FROM sys.views WHERE name = 'VW_CarsWithBeneficiaries')
BEGIN
    EXEC('
    CREATE VIEW VW_CarsWithBeneficiaries
    AS
    SELECT 
        c.ID as CarID,
        c.CarCode,
        c.ContractorName,
        c.CarNumber,
        c.CarType,
        c.CarModel,
        c.ManufactureYear,
        c.Route,
        c.RentAmount,
        c.Notes,
        c.IsActive as CarActive,
        
        -- بيانات المستفيدين
        cb.ID as BeneficiaryID,
        cb.EmployeeCode,
        cb.EmployeeName,
        cb.JobTitle,
        cb.Department,
        cb.StartDate as BeneficiaryStartDate,
        cb.EndDate as BeneficiaryEndDate,
        cb.IsActive as BeneficiaryActive,
        
        -- حسابات إضافية
        (SELECT COUNT(*) FROM CarBeneficiaries cb2 
         WHERE cb2.CarCode = c.CarCode AND cb2.IsActive = 1) as ActiveBeneficiariesCount
         
    FROM Cars c
    LEFT JOIN CarBeneficiaries cb ON c.CarCode = cb.CarCode
    WHERE c.IsActive = 1
    ')
END

-- 4. إنشاء stored procedure لإضافة سيارة جديدة مع المستفيدين
IF NOT EXISTS (SELECT * FROM sys.procedures WHERE name = 'SP_CreateCarWithBeneficiaries')
BEGIN
    EXEC('
    CREATE PROCEDURE SP_CreateCarWithBeneficiaries
        @CarCode NVARCHAR(20),
        @ContractorName NVARCHAR(100),
        @CarNumber NVARCHAR(50),
        @CarType NVARCHAR(100),
        @CarModel NVARCHAR(100),
        @ManufactureYear INT = NULL,
        @Route NVARCHAR(200),
        @RentAmount DECIMAL(10,2),
        @Notes NVARCHAR(MAX) = NULL
    AS
    BEGIN
        SET NOCOUNT ON
        
        DECLARE @CarID INT
        
        -- التحقق من عدم تكرار كود السيارة
        IF EXISTS (SELECT 1 FROM Cars WHERE CarCode = @CarCode)
        BEGIN
            RAISERROR(N''كود السيارة موجود مسبقاً'', 16, 1)
            RETURN
        END
        
        -- التحقق من عدم تكرار رقم السيارة
        IF EXISTS (SELECT 1 FROM Cars WHERE CarNumber = @CarNumber)
        BEGIN
            RAISERROR(N''رقم السيارة موجود مسبقاً'', 16, 1)
            RETURN
        END
        
        BEGIN TRANSACTION
        
        TRY
            -- إدراج السيارة
            INSERT INTO Cars (
                CarCode, ContractorName, CarNumber, CarType, CarModel,
                ManufactureYear, Route, RentAmount, Notes
            )
            VALUES (
                @CarCode, @ContractorName, @CarNumber, @CarType, @CarModel,
                @ManufactureYear, @Route, @RentAmount, @Notes
            )
            
            SET @CarID = SCOPE_IDENTITY()
            
            COMMIT TRANSACTION
            
            SELECT @CarID as NewCarID
            
        END TRY
        BEGIN CATCH
            ROLLBACK TRANSACTION
            THROW
        END CATCH
    END
    ')
END

-- 5. إنشاء stored procedure لإضافة مستفيد جديد للسيارة
IF NOT EXISTS (SELECT * FROM sys.procedures WHERE name = 'SP_AddBeneficiaryToCar')
BEGIN
    EXEC('
    CREATE PROCEDURE SP_AddBeneficiaryToCar
        @CarCode NVARCHAR(20),
        @EmployeeCode NVARCHAR(20),
        @StartDate DATE
    AS
    BEGIN
        SET NOCOUNT ON
        
        DECLARE @EmployeeName NVARCHAR(100)
        DECLARE @JobTitle NVARCHAR(100)
        DECLARE @Department NVARCHAR(100)
        
        -- جلب بيانات الموظف
        SELECT 
            @EmployeeName = EmployeeName,
            @JobTitle = JobTitle,
            @Department = Department
        FROM Employees
        WHERE EmployeeCode = @EmployeeCode
        
        IF @EmployeeName IS NULL
        BEGIN
            RAISERROR(N''كود الموظف غير موجود'', 16, 1)
            RETURN
        END
        
        -- التحقق من عدم وجود الموظف في نفس السيارة
        IF EXISTS (
            SELECT 1 FROM CarBeneficiaries 
            WHERE CarCode = @CarCode 
                AND EmployeeCode = @EmployeeCode 
                AND IsActive = 1
        )
        BEGIN
            RAISERROR(N''الموظف مسجل بالفعل في هذه السيارة'', 16, 1)
            RETURN
        END
        
        -- إدراج المستفيد
        INSERT INTO CarBeneficiaries (
            CarCode, EmployeeCode, EmployeeName, JobTitle, Department, StartDate
        )
        VALUES (
            @CarCode, @EmployeeCode, @EmployeeName, @JobTitle, @Department, @StartDate
        )
        
        SELECT SCOPE_IDENTITY() as NewBeneficiaryID
    END
    ')
END

-- 6. إنشاء stored procedure للحصول على إحصائيات السيارات
IF NOT EXISTS (SELECT * FROM sys.procedures WHERE name = 'SP_GetCarsStatistics')
BEGIN
    EXEC('
    CREATE PROCEDURE SP_GetCarsStatistics
    AS
    BEGIN
        SET NOCOUNT ON
        
        SELECT 
            -- إحصائيات عامة
            (SELECT COUNT(*) FROM Cars WHERE IsActive = 1) as TotalActiveCars,
            (SELECT COUNT(*) FROM Cars WHERE IsActive = 0) as TotalInactiveCars,
            (SELECT COUNT(*) FROM CarBeneficiaries WHERE IsActive = 1) as TotalActiveBeneficiaries,
            
            -- إحصائيات مالية
            (SELECT SUM(RentAmount) FROM Cars WHERE IsActive = 1) as TotalMonthlyRent,
            (SELECT AVG(RentAmount) FROM Cars WHERE IsActive = 1) as AverageRent,
            
            -- إحصائيات أخرى
            (SELECT COUNT(DISTINCT Route) FROM Cars WHERE IsActive = 1) as TotalRoutes,
            (SELECT COUNT(DISTINCT CarType) FROM Cars WHERE IsActive = 1) as TotalCarTypes,
            
            -- السيارات بدون مستفيدين
            (SELECT COUNT(*) FROM Cars c
             WHERE c.IsActive = 1 
                AND NOT EXISTS (
                    SELECT 1 FROM CarBeneficiaries cb 
                    WHERE cb.CarCode = c.CarCode AND cb.IsActive = 1
                )) as CarsWithoutBeneficiaries
    END
    ')
END

-- 7. إدراج بيانات تجريبية (اختيارية)
/*
INSERT INTO Cars (CarCode, ContractorName, CarNumber, CarType, CarModel, ManufactureYear, Route, RentAmount, Notes)
VALUES 
    (N'CAR-001', N'شركة النقل المتطور', N'أ ب ج 123', N'ميكروباص', N'هيونداي H1', 2020, N'القاهرة - الجيزة', 8000.00, N'سيارة نقل موظفين'),
    (N'CAR-002', N'مؤسسة المواصلات', N'د هـ و 456', N'أتوبيس', N'مرسيدس سبرينتر', 2019, N'المعادي - مدينة نصر', 12000.00, N'أتوبيس نقل جماعي'),
    (N'CAR-003', N'شركة الخدمات', N'ز ح ط 789', N'سيارة خاصة', N'تويوتا كامري', 2021, N'وسط البلد - الزمالك', 6000.00, N'سيارة إدارية')
*/

PRINT 'تم إنشاء جداول إدارة السيارات بنجاح'
