'use client';
import React, { useState } from 'react';

function MainComponent() {
  const [selectedLang, setSelectedLang] = useState('ar');
  const dir = selectedLang === 'ar' ? 'rtl' : 'ltr';

  const sections = [
    {
      title: { ar: 'الشقق المؤجرة', en: 'Rented Apartments' },
      icon: 'fa-building',
      links: [
        {
          text: { ar: 'إضافة شقة', en: 'Add Apartment' },
          href: '/add-apartment',
        },
        {
          text: { ar: 'عرض الشقق', en: 'View Apartments' },
          href: '/apartments',
        },
        {
          text: { ar: 'أرشيف الشقق', en: 'Apartments Archive' },
          href: '/apartments-archive',
        },
      ],
    },
    {
      title: { ar: 'السيارات المؤجرة', en: 'Rented Vehicles' },
      icon: 'fa-car',
      links: [
        { text: { ar: 'إضافة سيارة', en: 'Add Vehicle' }, href: '/add-car' },
        {
          text: { ar: 'عرض السيارات', en: 'View Vehicles' },
          href: '/vehicles',
        },
        {
          text: { ar: 'أرشيف السيارات', en: 'Vehicles Archive' },
          href: '/vehicles-archive',
        },
      ],
    },
    {
      title: { ar: 'العمالة المؤقتة', en: 'Temporary Workers' },
      icon: 'fa-users',
      links: [
        { text: { ar: 'إضافة عامل', en: 'Add Worker' }, href: '/add-worker' },
        { text: { ar: 'عرض العمال', en: 'View Workers' }, href: '/workers' },
        {
          text: { ar: 'أرشيف العمال', en: 'Workers Archive' },
          href: '/workers-archive',
        },
      ],
    },
  ];

  return (
    <div
      dir={dir}
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <a
            href="/"
            className="flex items-center text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
          >
            <i
              className={`fas fa-arrow-${
                selectedLang === 'ar' ? 'left' : 'right'
              } ml-2`}
            ></i>
            {selectedLang === 'ar' ? 'الرئيسية' : 'Home'}
          </a>
          <button
            onClick={() => setSelectedLang(selectedLang === 'ar' ? 'en' : 'ar')}
            className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
          >
            {selectedLang === 'ar' ? 'English' : 'العربية'}
          </button>
        </div>

        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          {selectedLang === 'ar' ? 'إدارة الأصول المؤجرة' : 'Asset Management'}
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {sections.map((section, index) => (
            <div
              key={index}
              className="bg-gray-50 dark:bg-gray-800 rounded-xl p-6 shadow-sm hover:shadow-md transition-shadow duration-300"
            >
              <div className="flex items-center mb-4">
                <i
                  className={`fas ${section.icon} text-3xl text-blue-600 dark:text-blue-400`}
                ></i>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mr-3 ml-3">
                  {section.title[selectedLang]}
                </h2>
              </div>

              <div className="space-y-3">
                {section.links.map((link, linkIndex) => (
                  <a
                    key={linkIndex}
                    href={link.href}
                    className="flex items-center text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                  >
                    <i className="fas fa-chevron-right text-sm ml-2"></i>
                    <span>{link.text[selectedLang]}</span>
                  </a>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default MainComponent;
