'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  FiFolder, 
  FiFile, 
  FiDownload, 
  FiEye,
  FiSearch,
  FiRefreshCw,
  FiArrowLeft
} from 'react-icons/fi';

export default function ArchivePage() {
  const router = useRouter();
  const [folders, setFolders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    if (!isLoggedIn) {
      router.push('/login');
      return;
    }

    fetchArchiveFolders();
  }, [router]);

  const fetchArchiveFolders = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/document-archive');
      const data = await response.json();
      
      if (data.success) {
        setFolders(data.folders);
      }
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  const filteredFolders = folders.filter(folder =>
    folder.FolderName.includes(searchTerm) ||
    folder.Description.includes(searchTerm)
  );

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* الهيدر */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center gap-4">
              <button
                onClick={() => router.back()}
                className="flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                <FiArrowLeft />
                العودة
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  أرشيف المستندات
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  إدارة وعرض مستندات الموظفين
                </p>
              </div>
            </div>
            
            <button
              onClick={fetchArchiveFolders}
              className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              <FiRefreshCw className={loading ? 'animate-spin' : ''} />
              تحديث
            </button>
          </div>
        </div>
      </div>

      {/* المحتوى الرئيسي */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* شريط البحث */}
        <div className="mb-6">
          <div className="relative">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="البحث في المجلدات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            />
          </div>
        </div>

        {/* عرض المجلدات */}
        {loading ? (
          <div className="text-center py-12">
            <FiRefreshCw className="animate-spin text-4xl text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">جاري تحميل المجلدات...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredFolders.map((folder) => (
              <div
                key={folder.ID}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => router.push(`/archive/${folder.FolderKey}`)}
              >
                <div className="flex items-center gap-4 mb-4">
                  <div 
                    className="text-3xl p-3 rounded-lg"
                    style={{ backgroundColor: folder.Color + '20', color: folder.Color }}
                  >
                    {folder.Icon}
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900 dark:text-white">
                      {folder.FolderName}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {folder.Description}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                  <span>مجلد الأرشيف</span>
                  <FiFolder className="text-lg" />
                </div>
              </div>
            ))}
          </div>
        )}

        {!loading && filteredFolders.length === 0 && (
          <div className="text-center py-12">
            <FiFolder className="text-6xl text-gray-300 dark:text-gray-600 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
              لا توجد مجلدات
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {searchTerm ? 'لم يتم العثور على مجلدات تطابق البحث' : 'لم يتم إنشاء أي مجلدات بعد'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
