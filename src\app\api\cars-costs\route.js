import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

// إضافة تكلفة سيارات جديدة
export async function POST(request) {
  try {
    const body = await request.json();
    const { month, year, carCount, rentalValue } = body;

    const pool = await getConnection();

    // التحقق من وجود سجل للشهر والسنة
    const checkQuery = `
      SELECT ID FROM carscost 
      WHERE [الشهر] = @Month AND [السنة] = @Year
    `;

    const checkRequest = pool.request();
    checkRequest.input('Month', sql.NVarChar, month);
    checkRequest.input('Year', sql.NVarChar, year);
    const existingRecord = await checkRequest.query(checkQuery);

    if (existingRecord.recordset.length > 0) {
      return NextResponse.json({
        success: false,
        message: `يوجد سجل بالفعل للشهر ${month} سنة ${year}`
      }, { status: 400 });
    }

    // إضافة السجل الجديد
    const insertQuery = `
      INSERT INTO carscost ([الشهر], [السنة], [العدد], [القيمة الإيجارية])
      VALUES (@Month, @Year, @CarCount, @RentalValue)
    `;

    const insertRequest = pool.request();
    insertRequest.input('Month', sql.NVarChar, month);
    insertRequest.input('Year', sql.NVarChar, year);
    insertRequest.input('CarCount', sql.NVarChar, carCount);
    insertRequest.input('RentalValue', sql.NVarChar, rentalValue);

    await insertRequest.query(insertQuery);

    return NextResponse.json({
      success: true,
      message: 'تم إضافة تكلفة السيارات بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'حدث خطأ في إضافة تكلفة السيارات',
      error: error.message
    }, { status: 500 });
  }
}

// تحديث تكلفة سيارات
export async function PUT(request) {
  try {
    const body = await request.json();
    const { id, month, year, carCount, rentalValue } = body;

    const pool = await getConnection();

    // التحقق من وجود سجل آخر بنفس الشهر والسنة (عدا السجل الحالي)
    const checkQuery = `
      SELECT ID FROM carscost 
      WHERE [الشهر] = @Month AND [السنة] = @Year AND ID != @ID
    `;

    const checkRequest = pool.request();
    checkRequest.input('Month', sql.NVarChar, month);
    checkRequest.input('Year', sql.NVarChar, year);
    checkRequest.input('ID', sql.Int, id);
    const existingRecord = await checkRequest.query(checkQuery);

    if (existingRecord.recordset.length > 0) {
      return NextResponse.json({
        success: false,
        message: `يوجد سجل آخر بالفعل للشهر ${month} سنة ${year}`
      }, { status: 400 });
    }

    // تحديث السجل
    const updateQuery = `
      UPDATE carscost 
      SET [الشهر] = @Month, [السنة] = @Year, [العدد] = @CarCount, [القيمة الإيجارية] = @RentalValue
      WHERE ID = @ID
    `;

    const updateRequest = pool.request();
    updateRequest.input('ID', sql.Int, id);
    updateRequest.input('Month', sql.NVarChar, month);
    updateRequest.input('Year', sql.NVarChar, year);
    updateRequest.input('CarCount', sql.NVarChar, carCount);
    updateRequest.input('RentalValue', sql.NVarChar, rentalValue);

    const result = await updateRequest.query(updateQuery);

    if (result.rowsAffected[0] > 0) {
      return NextResponse.json({
        success: true,
        message: 'تم تحديث تكلفة السيارات بنجاح'
      });
    } else {
      return NextResponse.json({
        success: false,
        message: 'لم يتم العثور على السجل'
      }, { status: 404 });
    }

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'حدث خطأ في تحديث تكلفة السيارات',
      error: error.message
    }, { status: 500 });
  }
}

// حذف تكلفة سيارات
export async function DELETE(request) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    const pool = await getConnection();

    const deleteQuery = 'DELETE FROM carscost WHERE ID = @ID';
    const deleteRequest = pool.request();
    deleteRequest.input('ID', sql.Int, parseInt(id));

    const result = await deleteRequest.query(deleteQuery);

    if (result.rowsAffected[0] > 0) {
      return NextResponse.json({
        success: true,
        message: 'تم حذف تكلفة السيارات بنجاح'
      });
    } else {
      return NextResponse.json({
        success: false,
        message: 'لم يتم العثور على السجل'
      }, { status: 404 });
    }

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'حدث خطأ في حذف تكلفة السيارات',
      error: error.message
    }, { status: 500 });
  }
}
