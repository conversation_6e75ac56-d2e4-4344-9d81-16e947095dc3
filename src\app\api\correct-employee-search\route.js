import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const searchType = searchParams.get('searchType') || 'individual';
    const searchValue = searchParams.get('searchValue') || searchParams.get('employeeId');

    if (!searchValue) {
      return NextResponse.json({
        success: false,
        message: 'يرجى إدخال قيمة البحث'
      });
    }

    const pool = await getConnection();

    // تحديد نوع البحث: رقم أم نص
    const isNumeric = /^\d+$/.test(searchValue);
    
    let whereClause;
    let paramValue;

    if (isNumeric) {
      whereClause = 'EmployeeCode = @searchValue';
      paramValue = parseInt(searchValue);
    } else {
      whereClause = 'EmployeeName LIKE @searchValue';
      paramValue = `%${searchValue}%`;
    }

    const request = pool.request();
    request.input('searchValue', isNumeric ? sql.Int : sql.NVarChar, paramValue);

    const result = await request.query(`
      SELECT TOP 10
        EmployeeCode,
        EmployeeName,
        JobTitle,
        Department,
        CurrentStatus,
        Mobile,
        email as Email,
        HireDate,
        BirthDate,
        NationalID,
        Governorate,
        MaritalStatus,
        Gender,
        SocialInsurance,
        MedicalInsurance,
        direct as DirectManager,
        area as Area,
        Education,
        University,
        Major,
        Grade,
        Batch,
        emrnum as EmergencyNumber,
        Kinship,
        spcialInsDate as SocialInsuranceDate,
        SocialInsureNum as SocialInsuranceNumber,
        MedicalInsuranceNum as MedicalInsuranceNumber,
        CASE
          WHEN EXISTS (
            SELECT 1 FROM ApartmentBeneficiaries ab
            WHERE ab.EmployeeCode = EmployeeCode
            AND ab.IsActive = 1
            AND (ab.EndDate IS NULL OR ab.EndDate >= GETDATE())
          ) THEN 1
          ELSE 0
        END as IsResidentEmployee
      FROM Employees
      WHERE ${whereClause}
      ORDER BY EmployeeName
    `);

    if (result.recordset.length > 0) {
      // إضافة بيانات الشقة والمواصلات لكل موظف
      const employeesWithDetails = await Promise.all(
        result.recordset.map(async (employee) => {
          // جلب بيانات الشقة
          try {
            const apartmentResult = await pool.request()
              .input('employeeCode', sql.NVarChar, employee.EmployeeCode.toString())
              .query(`
                SELECT TOP 1
                  a.ApartmentCode,
                  a.LandlordName,
                  a.Address,
                  a.RentAmount,
                  ab.StartDate as BeneficiaryStartDate,
                  ab.EndDate as BeneficiaryEndDate,
                  ab.IsActive as IsBeneficiaryActive
                FROM ApartmentBeneficiaries ab
                INNER JOIN Apartments a ON ab.ApartmentID = a.ID
                WHERE ab.EmployeeCode = @employeeCode
                  AND ab.IsActive = 1
                ORDER BY ab.StartDate DESC
              `);

            if (apartmentResult.recordset.length > 0) {
              employee.ApartmentInfo = apartmentResult.recordset[0];
            }
          } catch (apartmentError) {

          }

          // جلب بيانات المواصلات
          try {
            const transportResult = await pool.request()
              .input('employeeCode', sql.NVarChar, employee.EmployeeCode.toString())
              .query(`
                SELECT TOP 1
                  c.CarCode,
                  c.CarNumber,
                  c.Route,
                  c.ContractorName,
                  cb.StartDate as TransportStartDate,
                  cb.EndDate as TransportEndDate,
                  cb.IsActive as IsTransportActive
                FROM CarBeneficiaries cb
                INNER JOIN Cars c ON cb.CarID = c.ID
                WHERE cb.EmployeeCode = @employeeCode
                  AND cb.IsActive = 1
                ORDER BY cb.StartDate DESC
              `);

            if (transportResult.recordset.length > 0) {
              employee.TransportInfo = transportResult.recordset[0];
            }
          } catch (transportError) {

          }

          return {
            ...employee,
            // إضافة التسميات الموحدة للتوافق
            employeeCode: employee.EmployeeCode,
            employeeName: employee.EmployeeName,
            EmployeeID: employee.EmployeeCode,
            FullName: employee.EmployeeName,
            displayText: `${employee.EmployeeName} (${employee.EmployeeCode})`
          };
        })
      );

      if (searchType === 'individual') {
        return NextResponse.json({
          success: true,
          employee: employeesWithDetails[0]
        });
      } else {
        return NextResponse.json({
          success: true,
          employees: employeesWithDetails,
          count: employeesWithDetails.length
        });
      }
    }

    return NextResponse.json({
      success: false,
      message: 'لم يتم العثور على موظف'
    });

  } catch (error) {

    return NextResponse.json(
      { success: false, message: error.message },
      { status: 500 }
    );
  }
}

export async function POST(request) {
  try {
    const body = await request.json();
    const { searchType, searchValue, department, jobTitle, governorate } = body;

    // إعادة توجيه إلى GET للبحث الفردي
    if (searchType === 'individual' && searchValue) {
      const url = new URL(request.url);
      url.searchParams.set('searchType', 'individual');
      url.searchParams.set('searchValue', searchValue);
      
      const newRequest = new Request(url.toString(), { method: 'GET' });
      return GET(newRequest);
    }

    // البحث المجمع
    if (searchType === 'group') {
      const pool = await getConnection();
      const conditions = [];
      const inputs = new Map();

      if (department) {
        conditions.push('Department LIKE @department');
        inputs.set('department', `%${department}%`);
      }
      if (jobTitle) {
        conditions.push('JobTitle LIKE @jobTitle');
        inputs.set('jobTitle', `%${jobTitle}%`);
      }
      if (governorate) {
        conditions.push('Governorate LIKE @governorate');
        inputs.set('governorate', `%${governorate}%`);
      }

      const whereClause = conditions.length > 0 
        ? 'WHERE ' + conditions.join(' AND ')
        : '';

      const request = pool.request();
      inputs.forEach((value, key) => {
        request.input(key, sql.NVarChar, value);
      });

      const result = await request.query(`
        SELECT
          EmployeeCode,
          EmployeeName,
          JobTitle,
          Department,
          CurrentStatus,
          CompanyHousing,
          TransportMethod,
          Mobile,
          email as Email,
          HireDate,
          Governorate
        FROM Employees
        ${whereClause}
        ORDER BY EmployeeName
      `);

      const normalizedEmployees = result.recordset.map(employee => ({
        ...employee,
        employeeCode: employee.EmployeeCode,
        employeeName: employee.EmployeeName,
        EmployeeID: employee.EmployeeCode,
        FullName: employee.EmployeeName,
        displayText: `${employee.EmployeeName} (${employee.EmployeeCode})`
      }));

      return NextResponse.json({
        success: true,
        employees: normalizedEmployees,
        count: normalizedEmployees.length
      });
    }

    return NextResponse.json({
      success: false,
      message: 'نوع البحث غير صحيح'
    });

  } catch (error) {

    return NextResponse.json(
      { success: false, message: error.message },
      { status: 500 }
    );
  }
}
