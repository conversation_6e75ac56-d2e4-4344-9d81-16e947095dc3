'use client';

import { useState, useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { useLanguage } from '@/contexts/LanguageContext';
import MainLayout from '@/components/MainLayout';
import DateInput from '@/components/DateInput';
import { 
  Upload, 
  Download, 
  FileText, 
  Users, 
  Clock,
  CheckCircle,
  AlertCircle,
  Plus,
  Search,
  Filter,
  Calendar,
  Fingerprint,
  Database
} from 'lucide-react';

export default function AttendanceUpload() {
  const { isDarkMode } = useTheme();
  const { isRTL, isArabic } = useLanguage();
  
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [uploadType, setUploadType] = useState('manual'); // manual, biometric, bulk
  const [loading, setLoading] = useState(false);
  const [attendanceData, setAttendanceData] = useState([]);
  const [biometricData, setBiometricData] = useState([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // بيانات النموذج الجديد
  const [formData, setFormData] = useState({
    attendanceDate: new Date().toISOString().split('T')[0],
    employeeCode: '',
    employeeName: '',
    jobTitle: '',
    department: '',
    attendanceStatus: 'حضور',
    effectType: 'إداري',
    effectAmount: 0,
    checkInTime: '08:00',
    checkOutTime: '17:00',
    leaveType: '',
    leaveStartDate: '',
    leaveEndDate: '',
    leaveReason: '',
    notes: '',
    remarks: ''
  });

  // أنواع الحضور والمؤثرات
  const attendanceTypes = [
    { value: 'حضور', label: 'حضور', color: 'green', icon: CheckCircle },
    { value: 'غياب', label: 'غياب', color: 'red', icon: AlertCircle },
    { value: 'إجازة سنوية', label: 'إجازة سنوية', color: 'blue', icon: Calendar },
    { value: 'إجازة مرضية', label: 'إجازة مرضية', color: 'orange', icon: FileText },
    { value: 'إجازة عارضة', label: 'إجازة عارضة', color: 'purple', icon: Clock },
    { value: 'إجازة بدون راتب', label: 'إجازة بدون راتب', color: 'gray', icon: AlertCircle },
    { value: 'مأمورية', label: 'مأمورية', color: 'indigo', icon: Users },
    { value: 'تأخير', label: 'تأخير', color: 'yellow', icon: Clock },
    { value: 'وردية ليلية', label: 'وردية ليلية', color: 'dark', icon: Clock }
  ];

  const effectTypes = [
    { value: 'إداري', label: 'إداري' },
    { value: 'مالي', label: 'مالي' }
  ];

  useEffect(() => {
    loadAttendanceData();
  }, [selectedDate]);

  const loadAttendanceData = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/daily-attendance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'list',
          date: selectedDate,
          limit: 100
        })
      });

      const result = await response.json();
      if (result.success) {
        setAttendanceData(result.data || []);
      }
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  const handleAddRecord = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/daily-attendance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'add',
          ...formData,
          createdBy: 'النظام'
        })
      });

      const result = await response.json();
      if (result.success) {
        setShowAddModal(false);
        setFormData({
          attendanceDate: new Date().toISOString().split('T')[0],
          employeeCode: '',
          employeeName: '',
          jobTitle: '',
          department: '',
          attendanceStatus: 'حضور',
          effectType: 'إداري',
          effectAmount: 0,
          checkInTime: '08:00',
          checkOutTime: '17:00',
          leaveType: '',
          leaveStartDate: '',
          leaveEndDate: '',
          leaveReason: '',
          notes: '',
          remarks: ''
        });
        loadAttendanceData();
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {

      alert('خطأ في إضافة السجل');
    } finally {
      setLoading(false);
    }
  };

  const handleBiometricUpload = async (file) => {
    try {
      setLoading(true);
      // هنا يمكن إضافة منطق قراءة ملف البصمة
      // مثال: CSV أو Excel أو JSON
      const formData = new FormData();
      formData.append('file', file);
      
      // يمكن إضافة API endpoint لمعالجة ملف البصمة
      alert('سيتم إضافة معالجة ملف البصمة قريباً');
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  const processBiometricData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/daily-attendance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'process_biometric',
          processDate: selectedDate
        })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم معالجة بيانات البصمة بنجاح');
        loadAttendanceData();
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {

      alert('خطأ في معالجة البصمة');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    const type = attendanceTypes.find(t => t.value === status);
    return type ? type.color : 'gray';
  };

  const filteredData = attendanceData.filter(record =>
    record.EmployeeName?.includes(searchTerm) ||
    record.EmployeeCode?.includes(searchTerm) ||
    record.Department?.includes(searchTerm)
  );

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto">
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="flex items-center justify-between">
            <div>
              <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
                رفع التمام اليومي
              </h1>
              <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-600'} mb-2`}>
                رفع وإدارة بيانات التمام اليومي والمؤثرات (حضور، إجازات، تأخير، وردية ليلية، غياب)
              </p>
              <div className={`text-sm ${isDarkMode ? 'text-blue-400' : 'text-blue-600'} bg-blue-50 dark:bg-blue-900/20 px-3 py-2 rounded-lg inline-block`}>
                📅 التاريخ المحدد: {new Date(selectedDate).toLocaleDateString('ar-EG')}
              </div>
            </div>
            <div className="flex gap-3">
              <button
                onClick={() => setShowAddModal(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                إضافة سجل
              </button>
              <button
                onClick={processBiometricData}
                disabled={loading}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2 disabled:opacity-50"
              >
                <Fingerprint className="h-4 w-4" />
                معالجة البصمة
              </button>
            </div>
          </div>
        </div>

        {/* أدوات التحكم */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* اختيار التاريخ */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                التاريخ
              </label>
              <DateInput
                name="selectedDate"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                placeholder="DD/MM/YYYY"
                isArabic={isArabic}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                  isDarkMode 
                    ? 'bg-slate-800 border-slate-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>

            {/* البحث */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                البحث
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="ابحث بالاسم أو الكود أو القسم..."
                  className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                    isDarkMode 
                      ? 'bg-slate-800 border-slate-600 text-white placeholder-slate-400' 
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  }`}
                />
              </div>
            </div>

            {/* نوع الرفع */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                نوع الرفع
              </label>
              <select
                value={uploadType}
                onChange={(e) => setUploadType(e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                  isDarkMode 
                    ? 'bg-slate-800 border-slate-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="manual">إدخال يدوي</option>
                <option value="biometric">من جهاز البصمة</option>
                <option value="bulk">رفع مجمع (Excel)</option>
              </select>
            </div>

            {/* إحصائيات سريعة */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                الإحصائيات
              </label>
              <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                إجمالي السجلات: {filteredData.length}
              </div>
            </div>
          </div>
        </div>

        {/* جدول التمام اليومي */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border overflow-hidden`}>
          <div className="px-6 py-4 border-b border-gray-200 dark:border-slate-700">
            <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
              سجلات التمام اليومي - {new Date(selectedDate).toLocaleDateString('ar-EG')}
            </h3>
          </div>

          {loading ? (
            <div className="p-12 text-center">
              <div className="animate-spin text-4xl mb-4">⏳</div>
              <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>جاري تحميل البيانات...</p>
            </div>
          ) : filteredData.length === 0 ? (
            <div className="p-12 text-center">
              <FileText className={`h-16 w-16 mx-auto mb-4 ${isDarkMode ? 'text-slate-400' : 'text-gray-400'}`} />
              <h3 className={`text-xl font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-600'} mb-2`}>
                لا توجد سجلات
              </h3>
              <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>
                لم يتم العثور على سجلات تمام لهذا التاريخ
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-slate-700">
                <thead className={`${isDarkMode ? 'bg-slate-800' : 'bg-gray-50'}`}>
                  <tr>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      كود الموظف
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      الاسم
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      القسم
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      الحضور
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      الانصراف
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      الحالة
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      المؤثر
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      ملاحظات
                    </th>
                  </tr>
                </thead>
                <tbody className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} divide-y divide-gray-200 dark:divide-slate-700`}>
                  {filteredData.map((record) => (
                    <tr key={record.ID} className={`hover:${isDarkMode ? 'bg-slate-800' : 'bg-gray-50'}`}>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {record.EmployeeCode}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-900'}`}>
                        {record.EmployeeName}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-900'}`}>
                        {record.Department || '-'}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-900'}`}>
                        {record.CheckInTime || record.ActualCheckIn || '-'}
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-900'}`}>
                        {record.CheckOutTime || record.ActualCheckOut || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-${getStatusColor(record.AttendanceStatus)}-100 text-${getStatusColor(record.AttendanceStatus)}-800`}>
                          {record.AttendanceStatus}
                        </span>
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-900'}`}>
                        {record.EffectType || '-'}
                        {record.EffectAmount > 0 && (
                          <span className="text-green-600 mr-1">
                            (+{record.EffectAmount})
                          </span>
                        )}
                      </td>
                      <td className={`px-6 py-4 text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-900'} max-w-xs truncate`}>
                        {record.Notes || record.Remarks || '-'}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
