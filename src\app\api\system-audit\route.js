import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function POST(request) {
  try {

    const pool = await getConnection();
    const auditResults = {
      databaseConnection: null,
      tableStructures: {},
      fieldConsistency: {},
      apiTests: {},
      errors: [],
      recommendations: []
    };

    // 1. اختبار الاتصال بقاعدة البيانات

    try {
      const connectionTest = await pool.request().query('SELECT @@VERSION as version, DB_NAME() as database');
      auditResults.databaseConnection = {
        status: 'success',
        version: connectionTest.recordset[0].version,
        database: connectionTest.recordset[0].database,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      auditResults.databaseConnection = {
        status: 'failed',
        error: error.message
      };
      auditResults.errors.push('فشل الاتصال بقاعدة البيانات: ' + error.message);
    }

    // 2. فحص هياكل الجداول الأساسية

    const tables = [
      'Employees',
      'Apartments',
      'Cars',
      'ApartmentBeneficiaries',
      'CarBeneficiaries',
      'Department',
      'Governorates',
      'LeaveBalances',
      'LeaveRequests',
      'DailyAttendance',
      'ARCHIV',
      'UserActions',
      'SystemAlerts',
      'Notifications'
    ];
    
    for (const tableName of tables) {
      try {
        // فحص وجود الجدول
        const tableExists = await pool.request().query(`
          SELECT COUNT(*) as exists
          FROM INFORMATION_SCHEMA.TABLES 
          WHERE TABLE_NAME = '${tableName}'
        `);

        if (tableExists.recordset[0].exists > 0) {
          // جلب هيكل الجدول
          const columns = await pool.request().query(`
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = '${tableName}'
            ORDER BY ORDINAL_POSITION
          `);

          // عدد السجلات
          const count = await pool.request().query(`SELECT COUNT(*) as count FROM ${tableName}`);

          auditResults.tableStructures[tableName] = {
            exists: true,
            columns: columns.recordset,
            recordCount: count.recordset[0].count
          };
        } else {
          auditResults.tableStructures[tableName] = {
            exists: false
          };
          auditResults.errors.push(`الجدول ${tableName} غير موجود`);
        }
      } catch (error) {
        auditResults.tableStructures[tableName] = {
          exists: false,
          error: error.message
        };
        auditResults.errors.push(`خطأ في فحص الجدول ${tableName}: ${error.message}`);
      }
    }

    // 3. فحص تطابق أسماء الحقول

    // فحص جدول الموظفين
    if (auditResults.tableStructures.Employees?.exists) {
      const employeeColumns = auditResults.tableStructures.Employees.columns.map(col => col.COLUMN_NAME);
      
      auditResults.fieldConsistency.Employees = {
        hasEmployeeID: employeeColumns.includes('EmployeeID'),
        hasEmployeeCode: employeeColumns.includes('EmployeeCode'),
        hasFullName: employeeColumns.includes('FullName'),
        hasEmployeeName: employeeColumns.includes('EmployeeName'),
        recommendedMapping: {
          'EmployeeID': 'EmployeeCode (alias)',
          'FullName': 'EmployeeName (alias)'
        }
      };

      // اختبار استعلام مع الحقول الصحيحة
      try {
        const testQuery = await pool.request().query(`
          SELECT TOP 1 
            EmployeeID as EmployeeCode, 
            FullName as EmployeeName,
            JobTitle, Department, CurrentStatus
          FROM Employees
        `);
        auditResults.fieldConsistency.Employees.queryTest = 'success';
      } catch (error) {
        auditResults.fieldConsistency.Employees.queryTest = 'failed';
        auditResults.fieldConsistency.Employees.queryError = error.message;
      }
    }

    // فحص جدول الشقق
    if (auditResults.tableStructures.Apartments?.exists) {
      const apartmentColumns = auditResults.tableStructures.Apartments.columns.map(col => col.COLUMN_NAME);
      
      auditResults.fieldConsistency.Apartments = {
        hasApartmentCode: apartmentColumns.includes('ApartmentCode'),
        hasLandlordName: apartmentColumns.includes('LandlordName'),
        structure: 'correct'
      };
    }

    // 4. اختبار APIs الأساسية

    // اختبار API البحث عن الموظفين
    try {
      const employeeSearchTest = await pool.request()
        .input('SearchTerm', sql.NVarChar, '1')
        .query(`
          SELECT TOP 1
            EmployeeCode,
            EmployeeName,
            JobTitle, Department
          FROM Employees
          WHERE EmployeeCode LIKE @SearchTerm + '%'
        `);

      auditResults.apiTests.employeeSearch = {
        status: 'success',
        resultCount: employeeSearchTest.recordset.length
      };
    } catch (error) {
      auditResults.apiTests.employeeSearch = {
        status: 'failed',
        error: error.message
      };
    }

    // 5. توليد التوصيات

    if (auditResults.fieldConsistency.Employees?.hasEmployeeID && !auditResults.fieldConsistency.Employees?.hasEmployeeCode) {
      auditResults.recommendations.push('استخدام aliases في الاستعلامات: EmployeeID as EmployeeCode');
    }
    
    if (auditResults.fieldConsistency.Employees?.hasFullName && !auditResults.fieldConsistency.Employees?.hasEmployeeName) {
      auditResults.recommendations.push('استخدام aliases في الاستعلامات: FullName as EmployeeName');
    }

    if (auditResults.errors.length === 0) {
      auditResults.recommendations.push('النظام يعمل بشكل صحيح - لا توجد أخطاء كبيرة');
    }

    return NextResponse.json({
      success: true,
      message: 'تم تدقيق النظام بنجاح',
      audit: auditResults,
      summary: {
        totalErrors: auditResults.errors.length,
        tablesChecked: Object.keys(auditResults.tableStructures).length,
        recommendationsCount: auditResults.recommendations.length
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في تدقيق النظام: ' + error.message
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'System Audit API is ready',
    endpoints: {
      POST: 'Run comprehensive system audit'
    }
  });
}
