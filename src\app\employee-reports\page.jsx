'use client';
import React, { useState } from 'react';

function MainComponent() {
  const [selectedLang, setSelectedLang] = useState('ar');
  const [selectedReport, setSelectedReport] = useState('attendance');
  const [dateRange, setDateRange] = useState({ start: '', end: '' });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [reportData, setReportData] = useState([]);

  const dir = selectedLang === 'ar' ? 'rtl' : 'ltr';

  const reports = [
    {
      id: 'attendance',
      titleAr: 'تقارير الحضور',
      titleEn: 'Attendance Reports',
    },
    { id: 'leaves', titleAr: 'تقارير الإجازات', titleEn: 'Leave Reports' },
    { id: 'salary', titleAr: 'تقارير الرواتب', titleEn: 'Salary Reports' },
  ];

  const handleGenerateReport = async () => {
    setLoading(true);
    setError('');
    try {
      const response = await fetch('/api/reports', {
        method: 'POST',
        body: JSON.stringify({
          type: selectedReport,
          startDate: dateRange.start,
          endDate: dateRange.end,
        }),
      });

      if (!response.ok) {
        throw new Error(
          selectedLang === 'ar'
            ? 'حدث خطأ أثناء إنشاء التقرير'
            : 'Error generating report'
        );
      }

      const data = await response.json();
      setReportData(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async (format) => {
    try {
      const response = await fetch('/api/export-report', {
        method: 'POST',
        body: JSON.stringify({
          type: selectedReport,
          data: reportData,
          format,
        }),
      });

      if (!response.ok) {
        throw new Error(
          selectedLang === 'ar'
            ? 'حدث خطأ أثناء تصدير التقرير'
            : 'Error exporting report'
        );
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `report-${selectedReport}-${new Date().toISOString()}.${format}`;
      a.click();
    } catch (err) {
      setError(err.message);
    }
  };

  return (
    <div
      dir={dir}
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <a
            href="/"
            className="flex items-center text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
          >
            <i
              className={`fas fa-arrow-${
                selectedLang === 'ar' ? 'left' : 'right'
              } ml-2`}
            ></i>
            {selectedLang === 'ar' ? 'عودة' : 'Back'}
          </a>
          <button
            onClick={() => setSelectedLang(selectedLang === 'ar' ? 'en' : 'ar')}
            className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
          >
            {selectedLang === 'ar' ? 'English' : 'العربية'}
          </button>
        </div>

        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          {selectedLang === 'ar' ? 'تقارير الموظفين' : 'Employee Reports'}
        </h1>

        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                {selectedLang === 'ar' ? 'نوع التقرير' : 'Report Type'}
              </label>
              <select
                value={selectedReport}
                onChange={(e) => setSelectedReport(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                {reports.map((report) => (
                  <option key={report.id} value={report.id}>
                    {selectedLang === 'ar' ? report.titleAr : report.titleEn}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                {selectedLang === 'ar' ? 'من تاريخ' : 'Start Date'}
              </label>
              <input
                type="date"
                value={dateRange.start}
                onChange={(e) =>
                  setDateRange((prev) => ({ ...prev, start: e.target.value }))
                }
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>

            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                {selectedLang === 'ar' ? 'إلى تاريخ' : 'End Date'}
              </label>
              <input
                type="date"
                value={dateRange.end}
                onChange={(e) =>
                  setDateRange((prev) => ({ ...prev, end: e.target.value }))
                }
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>
          </div>

          <div className="mt-6 flex justify-end space-x-4 rtl:space-x-reverse">
            <button
              onClick={handleGenerateReport}
              disabled={loading}
              className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:bg-blue-300"
            >
              {loading
                ? selectedLang === 'ar'
                  ? 'جاري التحميل...'
                  : 'Loading...'
                : selectedLang === 'ar'
                  ? 'إنشاء التقرير'
                  : 'Generate Report'}
            </button>
          </div>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-100 text-red-700 rounded-md">
            {error}
          </div>
        )}

        {reportData.length > 0 && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
            <div className="flex justify-end p-4 border-b border-gray-200 dark:border-gray-700">
              <div className="space-x-2 rtl:space-x-reverse">
                <button
                  onClick={() => handleExport('pdf')}
                  className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-100"
                >
                  <i className="fas fa-file-pdf mr-2"></i>
                  PDF
                </button>
                <button
                  onClick={() => handleExport('xlsx')}
                  className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-100"
                >
                  <i className="fas fa-file-excel mr-2"></i>
                  Excel
                </button>
              </div>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {selectedLang === 'ar' ? 'اسم الموظف' : 'Employee Name'}
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {selectedLang === 'ar' ? 'القسم' : 'Department'}
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      {selectedLang === 'ar' ? 'التفاصيل' : 'Details'}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {reportData.map((row, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-gray-900 dark:text-gray-300">
                        {row.employeeName}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-gray-900 dark:text-gray-300">
                        {row.department}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-gray-900 dark:text-gray-300">
                        {row.details}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default MainComponent;
