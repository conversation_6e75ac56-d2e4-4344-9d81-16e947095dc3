'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { 
  FiFile, 
  FiDownload, 
  FiEye,
  FiSearch,
  FiRefreshCw,
  FiArrowLeft,
  FiUser,
  FiCalendar
} from 'react-icons/fi';

export default function FolderPage() {
  const router = useRouter();
  const params = useParams();
  const folderKey = params.folder;
  
  const [documents, setDocuments] = useState([]);
  const [folderInfo, setFolderInfo] = useState(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    if (!isLoggedIn) {
      router.push('/login');
      return;
    }

    if (folderKey) {
      fetchFolderContents();
    }
  }, [router, folderKey]);

  const fetchFolderContents = async () => {
    try {
      setLoading(true);
      
      // جلب معلومات المجلد
      const folderResponse = await fetch('/api/document-archive');
      const folderData = await folderResponse.json();
      
      if (folderData.success) {
        const folder = folderData.folders.find(f => f.FolderKey === folderKey);
        setFolderInfo(folder);
      }

      // جلب قائمة الموظفين النشطين لفحص وجود مستنداتهم
      const employeesResponse = await fetch('/api/employee-search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          searchType: 'group',
          department: '',
          jobTitle: '',
          governorate: ''
        })
      });

      const employeesData = await employeesResponse.json();
      if (employeesData.success) {
        // فحص وجود مستندات لكل موظف
        const documentsPromises = employeesData.employees.map(async (employee) => {
          const docResponse = await fetch('/api/document-archive', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              action: 'getEmployeeDocuments',
              employeeId: employee.EmployeeID
            })
          });

          const docData = await docResponse.json();
          if (docData.success) {
            const folderDoc = docData.documents.find(doc => doc.folderKey === folderKey);
            if (folderDoc && folderDoc.exists) {
              return {
                employeeId: employee.EmployeeID,
                employeeName: employee.FullName,
                department: employee.Department,
                jobTitle: employee.JobTitle,
                document: folderDoc
              };
            }
          }
          return null;
        });

        const results = await Promise.all(documentsPromises);
        setDocuments(results.filter(doc => doc !== null));
      }
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  const openDocument = (path) => {
    window.open(path, '_blank');
  };

  const filteredDocuments = documents.filter(doc =>
    doc.employeeName.includes(searchTerm) ||
    doc.employeeId.toString().includes(searchTerm) ||
    doc.department.includes(searchTerm)
  );

  if (!folderInfo && !loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            مجلد غير موجود
          </h1>
          <button
            onClick={() => router.push('/archive')}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
          >
            العودة للأرشيف
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* الهيدر */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center gap-4">
              <button
                onClick={() => router.push('/archive')}
                className="flex items-center gap-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                <FiArrowLeft />
                العودة للأرشيف
              </button>
              {folderInfo && (
                <div className="flex items-center gap-3">
                  <div 
                    className="text-2xl p-2 rounded-lg"
                    style={{ backgroundColor: folderInfo.Color + '20', color: folderInfo.Color }}
                  >
                    {folderInfo.Icon}
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                      {folderInfo.FolderName}
                    </h1>
                    <p className="text-gray-600 dark:text-gray-400">
                      {folderInfo.Description}
                    </p>
                  </div>
                </div>
              )}
            </div>
            
            <div className="flex items-center gap-3">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {documents.length} مستند
              </span>
              <button
                onClick={fetchFolderContents}
                className="flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                <FiRefreshCw className={loading ? 'animate-spin' : ''} />
                تحديث
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* المحتوى الرئيسي */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* شريط البحث */}
        <div className="mb-6">
          <div className="relative">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="البحث في المستندات..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            />
          </div>
        </div>

        {/* عرض المستندات */}
        {loading ? (
          <div className="text-center py-12">
            <FiRefreshCw className="animate-spin text-4xl text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600 dark:text-gray-400">جاري تحميل المستندات...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredDocuments.map((doc) => (
              <div
                key={doc.employeeId}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <FiUser className="text-gray-400" />
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">
                        {doc.employeeName}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        كود: {doc.employeeId}
                      </p>
                    </div>
                  </div>
                  <FiFile 
                    className="text-2xl"
                    style={{ color: folderInfo?.Color || '#6b7280' }}
                  />
                </div>

                <div className="space-y-2 mb-4">
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <span>القسم:</span>
                    <span>{doc.department}</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                    <span>المسمى:</span>
                    <span>{doc.jobTitle}</span>
                  </div>
                </div>

                <button
                  onClick={() => openDocument(doc.document.webPath)}
                  className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
                >
                  <FiEye />
                  عرض المستند
                </button>
              </div>
            ))}
          </div>
        )}

        {!loading && filteredDocuments.length === 0 && (
          <div className="text-center py-12">
            <FiFile className="text-6xl text-gray-300 dark:text-gray-600 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">
              لا توجد مستندات
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              {searchTerm ? 'لم يتم العثور على مستندات تطابق البحث' : 'لا توجد مستندات في هذا المجلد'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
