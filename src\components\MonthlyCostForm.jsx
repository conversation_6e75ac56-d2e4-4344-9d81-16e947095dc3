'use client';

import { useState } from 'react';

export default function MonthlyCostForm({ 
  costType, 
  onSave, 
  onCancel, 
  initialData = null 
}) {
  const [formData, setFormData] = useState({
    month: initialData?.month || new Date().getMonth() + 1,
    year: initialData?.year || new Date().getFullYear(),
    totalAmount: initialData?.totalAmount || '',
    itemsCount: initialData?.itemsCount || '',
    notes: initialData?.notes || '',
    isAttachment: initialData?.isAttachment || false,
    ...initialData
  });

  const [loading, setLoading] = useState(false);

  const months = [
    { value: 1, label: 'يناير' },
    { value: 2, label: 'فبراير' },
    { value: 3, label: 'مارس' },
    { value: 4, label: 'أبريل' },
    { value: 5, label: 'مايو' },
    { value: 6, label: 'يونيو' },
    { value: 7, label: 'يوليو' },
    { value: 8, label: 'أغسطس' },
    { value: 9, label: 'سبتمبر' },
    { value: 10, label: 'أكتوبر' },
    { value: 11, label: 'نوفمبر' },
    { value: 12, label: 'ديسمبر' }
  ];

  const costTypeLabels = {
    carscost: 'السيارات',
    housingcost: 'الشقق',
    '3amala': 'العمالة المؤقتة'
  };

  const itemLabels = {
    carscost: 'عدد السيارات',
    housingcost: 'عدد الشقق',
    '3amala': 'عدد العمال'
  };

  // حساب متوسط التكلفة للوحدة
  const calculateAverageCost = () => {
    const total = parseFloat(formData.totalAmount) || 0;
    const count = parseInt(formData.itemsCount) || 0;
    return count > 0 ? (total / count).toFixed(2) : 0;
  };

  // معالجة تغيير البيانات
  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // معالجة إرسال النموذج
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.totalAmount || !formData.itemsCount) {
      alert('يجب إدخال إجمالي التكلفة وعدد العناصر');
      return;
    }

    setLoading(true);
    try {
      const dataToSave = {
        costType,
        month: parseInt(formData.month),
        year: parseInt(formData.year),
        totalAmount: parseFloat(formData.totalAmount),
        itemsCount: parseInt(formData.itemsCount),
        notes: formData.notes,
        isAttachment: costType === 'housingcost' ? formData.isAttachment : false,
        details: {
          averageCostPerItem: calculateAverageCost(),
          createdBy: 'النظام',
          timestamp: new Date().toISOString()
        }
      };

      await onSave(dataToSave);
    } catch (error) {

      alert('حدث خطأ في حفظ البيانات');
    } finally {
      setLoading(false);
    }
  };

  // تنسيق العملة
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(amount || 0);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-bold text-gray-800 dark:text-gray-100">
          {initialData ? 'تعديل' : 'إضافة'} تكاليف {costTypeLabels[costType]} الشهرية
        </h3>
        <button
          onClick={onCancel}
          className="text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-gray-100"
        >
          ✕
        </button>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* الشهر والسنة */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
              الشهر *
            </label>
            <select
              value={formData.month}
              onChange={(e) => handleChange('month', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              required
            >
              {months.map(month => (
                <option key={month.value} value={month.value}>
                  {month.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
              السنة *
            </label>
            <select
              value={formData.year}
              onChange={(e) => handleChange('year', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              required
            >
              {[2023, 2024, 2025, 2026, 2027].map(year => (
                <option key={year} value={year}>{year}</option>
              ))}
            </select>
          </div>
        </div>

        {/* نوع طلب الإصدار - للشقق فقط */}
        {costType === 'housingcost' && (
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-4">
            <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-3">نوع طلب الإصدار:</h4>
            <div className="space-y-3">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="requestType"
                  checked={!formData.isAttachment}
                  onChange={() => handleChange('isAttachment', false)}
                  className="mr-2"
                />
                <div>
                  <span className="font-medium text-gray-800 dark:text-gray-100">طلب إصدار أساسي</span>
                  <p className="text-sm text-gray-600 dark:text-gray-300">طلب الإصدار العادي للشهر</p>
                </div>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="requestType"
                  checked={formData.isAttachment}
                  onChange={() => handleChange('isAttachment', true)}
                  className="mr-2"
                />
                <div>
                  <span className="font-medium text-gray-800 dark:text-gray-100">طلب إصدار ملحق</span>
                  <p className="text-sm text-gray-600 dark:text-gray-300">طلب إضافي للشهر (شقق طارئة)</p>
                </div>
              </label>
            </div>
            {formData.isAttachment && (
              <div className="mt-3 p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-700 rounded">
                <p className="text-sm text-orange-700 dark:text-orange-200">
                  <strong>ملاحظة:</strong> سيتم حفظ هذا الطلب كملحق للشهر المحدد وسيُضاف للتكاليف الأساسية.
                </p>
              </div>
            )}
          </div>
        )}

        {/* التكاليف والعدد */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
              إجمالي التكلفة (ج.م) *
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              value={formData.totalAmount}
              onChange={(e) => handleChange('totalAmount', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
              placeholder="مثال: 151000"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
              {itemLabels[costType]} *
            </label>
            <input
              type="number"
              min="1"
              value={formData.itemsCount}
              onChange={(e) => handleChange('itemsCount', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
              placeholder="مثال: 11"
              required
            />
          </div>
        </div>

        {/* الحسابات التلقائية */}
        {formData.totalAmount && formData.itemsCount && (
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-700">
            <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-3">الحسابات التلقائية:</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="flex justify-between">
                <span className="text-blue-600 dark:text-blue-300">إجمالي التكلفة:</span>
                <span className="font-medium text-blue-800 dark:text-blue-200">
                  {formatCurrency(formData.totalAmount)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-blue-600 dark:text-blue-300">{itemLabels[costType]}:</span>
                <span className="font-medium text-blue-800 dark:text-blue-200">
                  {formData.itemsCount}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-blue-600 dark:text-blue-300">متوسط التكلفة للوحدة:</span>
                <span className="font-medium text-green-600 dark:text-green-400">
                  {formatCurrency(calculateAverageCost())}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* ملاحظات */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
            ملاحظات
          </label>
          <textarea
            value={formData.notes}
            onChange={(e) => handleChange('notes', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
            rows="3"
            placeholder="ملاحظات إضافية حول التكاليف..."
          />
        </div>

        {/* أزرار الإجراءات */}
        <div className="flex gap-3 pt-4 border-t border-gray-200 dark:border-gray-600">
          <button
            type="submit"
            disabled={loading}
            className="bg-blue-600 text-white py-2 px-6 rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center gap-2"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                جاري الحفظ...
              </>
            ) : (
              <>
                💰 {initialData ? 'تحديث' : 'حفظ'} التكاليف
              </>
            )}
          </button>
          <button
            type="button"
            onClick={onCancel}
            className="bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-200 py-2 px-6 rounded-md hover:bg-gray-400 dark:hover:bg-gray-500"
          >
            إلغاء
          </button>
        </div>
      </form>
    </div>
  );
}
