'use client';

import { useState, useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import MainLayout from '@/components/MainLayout';
import {
  FiPlus, FiEdit, FiTrash2, FiCheck, FiX, FiClock,
  FiDollarSign, FiFileText, FiCalendar, FiFilter,
  FiDownload, FiRefreshCw, FiAlertCircle, FiCheckCircle,
  FiUpload
} from 'react-icons/fi';

export default function CustodyCostsPage() {
  const { isDarkMode } = useTheme();

  // إضافة CSS للتحكم في عدد الأسطر وتنسيق التاريخ
  const customStyles = `
    .line-clamp-1 {
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    .line-clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    /* تنسيق حقل التاريخ */
    input[type="date"] {
      position: relative;
    }
    input[type="date"]::-webkit-calendar-picker-indicator {
      cursor: pointer;
    }
  `;
  const [loading, setLoading] = useState(true);
  const [costs, setCosts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [permanentCustodies, setPermanentCustodies] = useState([]);

  // تتبع تغييرات العُهد المستديمة
  useEffect(() => {

  }, [permanentCustodies]);
  const [subCategories, setSubCategories] = useState([]);
  const [balances, setBalances] = useState({
    permanent: {
      TotalInitialAmount: 0,
      AvailableBalance: 0,
      TotalCurrentBalance: 0,
      PendingAmount: 0,
      TotalSettled: 0
    },
    temporary: {
      TotalRequested: 0,
      PendingCount: 0,
      SettledCount: 0
    },
    lastSettlementNumber: null
  });
  const [showForm, setShowForm] = useState(false);
  const [showCustodyManager, setShowCustodyManager] = useState(false);
  const [selectedCost, setSelectedCost] = useState(null);
  const [showAddCategoryModal, setShowAddCategoryModal] = useState(false);
  const [showAddSubCategoryModal, setShowAddSubCategoryModal] = useState(false);
  const [newCategoryData, setNewCategoryData] = useState({
    categoryName: '',
    categoryCode: '',
    description: '',
    custodyType: 'مستديمة'
  });
  const [newSubCategoryData, setNewSubCategoryData] = useState({
    parentCategoryId: '',
    categoryName: '',
    categoryCode: '',
    description: ''
  });
  const [custodyFormData, setCustodyFormData] = useState({
    custodyNumber: '',
    custodianName: '',
    custodianId: '',
    department: '',
    initialAmount: '',
    notes: ''
  });
  const [dateInput, setDateInput] = useState('');
  const [filters, setFilters] = useState({
    custodyType: '',
    status: '',
    dateFrom: '',
    dateTo: '',
    month: '',
    year: new Date().getFullYear().toString(),
    searchText: '',
    settlementNumber: '',
    mainCategory: '',
    subCategory: ''
  });

  // بيانات النموذج
  const [formData, setFormData] = useState({
    custodyType: 'مؤقتة',
    permanentCustodyNumber: '',
    settlementNumber: '', // رقم التسوية للعُهدة المستديمة
    purpose: '',
    mainCategoryId: '',
    subCategoryId: '',
    amount: '',
    receiptNumber: '',
    costDate: new Date().toISOString().split('T')[0], // تاريخ التكلفة (افتراضي اليوم)
    notes: ''
  });

  // جلب البيانات الأساسية
  // فلترة التكاليف
  const filteredCosts = costs.filter(cost => {
    // فلتر نوع العُهدة
    if (filters.custodyType && cost.CustodyType !== filters.custodyType) return false;

    // فلتر الحالة
    if (filters.status && cost.Status !== filters.status) return false;

    // فلتر البند الرئيسي
    if (filters.mainCategory && cost.MainCategoryID != filters.mainCategory) return false;

    // فلتر البند الفرعي
    if (filters.subCategory && cost.SubCategoryID != filters.subCategory) return false;

    // فلتر السنة
    if (filters.year && new Date(cost.CostDate).getFullYear().toString() !== filters.year) return false;

    // فلتر الشهر
    if (filters.month && (new Date(cost.CostDate).getMonth() + 1).toString() !== filters.month) return false;

    // فلتر البحث النصي
    if (filters.searchText) {
      const searchLower = filters.searchText.toLowerCase();
      return (
        cost.SettlementNumber?.toLowerCase().includes(searchLower) ||
        cost.Purpose?.toLowerCase().includes(searchLower) ||
        cost.Notes?.toLowerCase().includes(searchLower) ||
        cost.MainCategoryName?.toLowerCase().includes(searchLower) ||
        cost.SubCategoryName?.toLowerCase().includes(searchLower)
      );
    }

    // فلتر رقم التسوية
    if (filters.settlementNumber && !cost.SettlementNumber?.includes(filters.settlementNumber)) return false;

    return true;
  });

  useEffect(() => {
    fetchInitialData();
  }, []);

  const fetchInitialData = async () => {
    try {
      setLoading(true);

      // إعداد النظام أولاً
      await fetch('/api/project-custody-costs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'setup' })
      });

      // جلب البيانات
      const [costsRes, categoriesRes, custodiesRes, balancesRes] = await Promise.all([
        fetch('/api/project-custody-costs?action=getCosts'),
        fetch('/api/project-custody-costs?action=getCategories'),
        fetch('/api/project-custody-costs?action=getPermanentCustodies', {
          headers: { 'Accept-Charset': 'utf-8' }
        }),
        fetch('/api/project-custody-costs?action=getBalances')
      ]);

      const [costsData, categoriesData, custodiesData, balancesData] = await Promise.all([
        costsRes.json(),
        categoriesRes.json(),
        custodiesRes.json(),
        balancesRes.json()
      ]);

      if (costsData.success) {
        setCosts(costsData.costs || []);
      }
      if (categoriesData.success) {
        setCategories(categoriesData.data?.main || []);
        setSubCategories(categoriesData.data?.sub || []);
      }
      if (custodiesData.success) {

        if (custodiesData.custodies && custodiesData.custodies.length > 0) {
          custodiesData.custodies.forEach((custody, index) => {
            console.log(`${index + 1}. العُهدة ${custody.CustodyNumber}: ${custody.CustodianName} (ID: ${custody.ID})`);
          });
        }
        setPermanentCustodies(custodiesData.custodies || []);
      } else {

      }
      if (balancesData.success) setBalances(balancesData);

    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  // جلب التصنيفات حسب نوع العُهدة
  const fetchCategoriesByCustodyType = async (custodyType) => {
    try {
      const response = await fetch(`/api/project-custody-costs?action=getCategories&custodyType=${custodyType}`);
      const result = await response.json();

      if (result.success) {
        setCategories(result.data.main);
        setSubCategories(result.data.sub);
      }
    } catch (error) {

    }
  };

  // إنشاء تكلفة جديدة
  const handleCreateCost = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);

      const response = await fetch('/api/project-custody-costs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'createCost',
          data: {
            ...formData,
            amount: parseFloat(formData.amount),
            mainCategoryId: parseInt(formData.mainCategoryId),
            subCategoryId: formData.subCategoryId ? parseInt(formData.subCategoryId) : null
          }
        })
      });

      const result = await response.json();

      if (result.success) {
        alert('تم إنشاء التكلفة بنجاح');
        setShowForm(false);
        setFormData({
          custodyType: 'مستديمة',
          permanentCustodyNumber: '',
          settlementNumber: '',
          purpose: '',
          mainCategoryId: '',
          subCategoryId: '',
          amount: '',
          receiptNumber: '',
          costDate: new Date().toISOString().split('T')[0],
          notes: ''
        });
        fetchInitialData();
      } else {
        alert('خطأ: ' + result.error);
      }

    } catch (error) {

      alert('حدث خطأ في إنشاء التكلفة');
    } finally {
      setLoading(false);
    }
  };

  // تسوية التكلفة
  const handleSettleCost = async (costId) => {
    const settlementNumber = prompt('أدخل رقم التسوية:');
    if (!settlementNumber) return;

    try {
      setLoading(true);

      const response = await fetch('/api/project-custody-costs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'settleCost',
          costId: costId,
          settlementData: {
            settlementNumber: settlementNumber,
            settlementType: 'تم التسوية',
            notes: 'تسوية من الواجهة'
          }
        })
      });

      const result = await response.json();

      if (result.success) {
        alert('تم تسوية التكلفة بنجاح');
        fetchInitialData();
      } else {
        alert('خطأ: ' + result.error);
      }

    } catch (error) {

      alert('حدث خطأ في تسوية التكلفة');
    } finally {
      setLoading(false);
    }
  };

  // تغيير حالة التكلفة
  const handleChangeStatus = async (costId, newStatus) => {
    const notes = prompt(`أدخل ملاحظات تغيير الحالة إلى "${newStatus}":`);
    if (notes === null) return; // إلغاء

    try {
      setLoading(true);

      const response = await fetch('/api/project-custody-costs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'changeCostStatus',
          costId: costId,
          newStatus: newStatus,
          notes: notes,
          processedBy: 'المستخدم'
        })
      });

      const result = await response.json();

      if (result.success) {
        alert(`تم تغيير الحالة إلى "${newStatus}" بنجاح`);
        fetchInitialData();
      } else {
        alert('خطأ: ' + result.error);
      }

    } catch (error) {

      alert('حدث خطأ في تغيير حالة التكلفة');
    } finally {
      setLoading(false);
    }
  };

  // رفع ملف PDF للتسوية
  const handleUploadFile = async (costId) => {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.pdf';
    fileInput.onchange = async (e) => {
      const file = e.target && 'files' in e.target ? e.target.files[0] : null;
      if (!file) return;

      if (file.type !== 'application/pdf') {
        alert('يجب اختيار ملف PDF فقط');
        return;
      }

      try {
        setLoading(true);

        const response = await fetch('/api/project-custody-costs', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'uploadSettlementFile',
            costId: costId,
            fileData: {
              name: file.name,
              size: file.size,
              type: file.type
            },
            uploadedBy: 'المستخدم'
          })
        });

        const result = await response.json();

        if (result.success) {
          alert('تم رفع ملف التسوية بنجاح');
          fetchInitialData();
        } else {
          alert('خطأ: ' + result.error);
        }

      } catch (error) {

        alert('حدث خطأ في رفع الملف');
      } finally {
        setLoading(false);
      }
    };
    fileInput.click();
  };

  // حذف التسوية
  const handleDeleteCost = async (costId) => {
    const confirmDelete = confirm('هل أنت متأكد من حذف هذه التسوية؟ لا يمكن التراجع عن هذا الإجراء.');
    if (!confirmDelete) return;

    try {
      setLoading(true);

      const response = await fetch('/api/project-custody-costs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'deleteCost',
          costId: costId,
          deletedBy: 'المستخدم'
        })
      });

      const result = await response.json();

      if (result.success) {
        alert('تم حذف التسوية بنجاح');
        fetchInitialData();
      } else {
        alert('خطأ: ' + result.error);
      }

    } catch (error) {

      alert('حدث خطأ في حذف التسوية');
    } finally {
      setLoading(false);
    }
  };

  // إضافة عُهدة مستديمة جديدة
  const handleAddCustody = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);

      const response = await fetch('/api/project-custody-costs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'addPermanentCustody',
          data: {
            ...custodyFormData,
            initialAmount: parseFloat(custodyFormData.initialAmount)
          }
        })
      });

      const result = await response.json();

      if (result.success) {
        alert('تم إضافة العُهدة المستديمة بنجاح');
        setShowCustodyManager(false);
        setCustodyFormData({
          custodyNumber: '',
          custodianName: '',
          custodianId: '',
          department: '',
          initialAmount: '',
          notes: ''
        });
        fetchInitialData();
      } else {
        alert('خطأ: ' + result.error);
      }

    } catch (error) {

      alert('حدث خطأ في إضافة العُهدة');
    } finally {
      setLoading(false);
    }
  };

  // حذف عُهدة مستديمة
  const handleDeleteCustody = async (custodyId) => {
    const confirmDelete = confirm('هل أنت متأكد من حذف هذه العُهدة؟ سيتم حذف جميع التكاليف المرتبطة بها.');
    if (!confirmDelete) return;

    try {
      setLoading(true);

      const response = await fetch('/api/project-custody-costs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'deletePermanentCustody',
          custodyId: custodyId
        })
      });

      const result = await response.json();

      if (result.success) {
        alert('تم حذف العُهدة بنجاح');
        fetchInitialData();
      } else {
        alert('خطأ: ' + result.error);
      }

    } catch (error) {

      alert('حدث خطأ في حذف العُهدة');
    } finally {
      setLoading(false);
    }
  };

  // تحديث قيمة العُهدة المستديمة
  const handleUpdateCustodyAmount = async (custodyId) => {
    const newAmount = prompt('أدخل القيمة الجديدة للعُهدة المستديمة:', '240000');
    if (!newAmount || isNaN(parseFloat(newAmount))) return;

    try {
      setLoading(true);

      const response = await fetch('/api/project-custody-costs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'updatePermanentCustodyAmount',
          custodyId: custodyId,
          newAmount: parseFloat(newAmount),
          updatedBy: 'المستخدم'
        })
      });

      const result = await response.json();

      if (result.success) {
        alert(`تم تحديث قيمة العُهدة المستديمة من ${result.oldAmount} إلى ${result.newAmount}`);
        fetchInitialData();
      } else {
        alert('خطأ: ' + result.error);
      }

    } catch (error) {

      alert('حدث خطأ في تحديث قيمة العُهدة');
    } finally {
      setLoading(false);
    }
  };

  // تنسيق العملة
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(amount);
  };

  // حساب المجموع الإجمالي للتكاليف المعروضة (المفلترة)
  const calculateTotalAmount = () => {
    return filteredCosts.reduce((total, cost) => {
      return total + (parseFloat(cost.Amount) || 0);
    }, 0);
  };

  // حساب عدد التكاليف المعروضة (المفلترة)
  const getDisplayedCostsCount = () => {
    return filteredCosts.length;
  };

  // الحصول على وصف الفلتر المطبق
  const getFilterDescription = () => {
    const filters = [];

    // فلتر نوع العُهدة
    if (formData.custodyType && formData.custodyType !== 'الكل') {
      filters.push(`نوع العُهدة: ${formData.custodyType}`);
    }

    // فلتر البند الرئيسي
    const selectedMainCategory = categories.find(cat => cat.ID == formData.mainCategoryId);
    if (selectedMainCategory) {
      filters.push(`البند الرئيسي: ${selectedMainCategory.CategoryName}`);
    }

    // فلتر البند الفرعي
    const selectedSubCategory = categories
      .find(cat => cat.ID == formData.mainCategoryId)?.SubCategories
      ?.find(sub => sub.ID == formData.subCategoryId);
    if (selectedSubCategory) {
      filters.push(`البند الفرعي: ${selectedSubCategory.CategoryName}`);
    }

    return filters.length > 0 ? filters.join(' | ') : 'جميع التكاليف';
  };

  // حساب إحصائيات التكاليف حسب الحالة (للمفلترة)
  const getCostStatistics = () => {
    const stats = {
      total: filteredCosts.length,
      pending: filteredCosts.filter(cost => cost.Status === 'قيد المراجعة').length,
      settled: filteredCosts.filter(cost => cost.Status === 'تم التسوية').length,
      suspended: filteredCosts.filter(cost => cost.Status === 'معلّقة').length,
      rejected: filteredCosts.filter(cost => cost.Status === 'مرفوضة').length
    };

    return stats;
  };

  // تنسيق التاريخ بصيغة dd/mm/yyyy
  const formatDate = (dateString) => {
    if (!dateString) return '';
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '';

      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();

      return `${day}/${month}/${year}`;
    } catch (error) {
      return '';
    }
  };

  // تحويل التاريخ من yyyy-mm-dd إلى dd/mm/yyyy للعرض
  const formatDateForDisplay = (dateString) => {
    if (!dateString) return '';
    const [year, month, day] = dateString.split('-');
    return `${day}/${month}/${year}`;
  };

  // تحويل التاريخ من dd/mm/yyyy إلى yyyy-mm-dd للحفظ
  const formatDateForSave = (dateString) => {
    if (!dateString) return '';
    const [day, month, year] = dateString.split('/');
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  };

  // التحقق من صحة التاريخ بتنسيق dd/mm/yyyy
  const isValidDate = (dateString) => {
    if (!dateString) return false;
    const regex = /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/;
    const match = dateString.match(regex);
    if (!match) return false;

    const day = parseInt(match[1]);
    const month = parseInt(match[2]);
    const year = parseInt(match[3]);

    if (month < 1 || month > 12) return false;
    if (day < 1 || day > 31) return false;
    if (year < 1900 || year > 2100) return false;

    // التحقق من صحة التاريخ
    const date = new Date(year, month - 1, day);
    return date.getFullYear() === year &&
           date.getMonth() === month - 1 &&
           date.getDate() === day;
  };

  // معالجة تغيير التاريخ
  const handleDateChange = (value) => {
    setDateInput(value);

    if (isValidDate(value)) {
      const formattedDate = formatDateForSave(value);
      setFormData({...formData, costDate: formattedDate});
    }
  };

  // إضافة بند رئيسي جديد
  const handleAddMainCategory = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);

      const response = await fetch('/api/project-custody-costs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'addMainCategory',
          data: newCategoryData
        })
      });

      const result = await response.json();

      if (result.success) {
        alert('تم إضافة البند الرئيسي بنجاح');
        setShowAddCategoryModal(false);
        setNewCategoryData({
          categoryName: '',
          categoryCode: '',
          description: '',
          custodyType: 'مستديمة'
        });
        fetchInitialData(); // إعادة تحميل البيانات
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {

      alert('حدث خطأ في إضافة البند الرئيسي');
    } finally {
      setLoading(false);
    }
  };

  // إضافة بند فرعي جديد
  const handleAddSubCategory = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);

      const response = await fetch('/api/project-custody-costs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'addSubCategory',
          data: newSubCategoryData
        })
      });

      const result = await response.json();

      if (result.success) {
        alert('تم إضافة البند الفرعي بنجاح');
        setShowAddSubCategoryModal(false);
        setNewSubCategoryData({
          parentCategoryId: '',
          categoryName: '',
          categoryCode: '',
          description: ''
        });
        fetchInitialData(); // إعادة تحميل البيانات
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {

      alert('حدث خطأ في إضافة البند الفرعي');
    } finally {
      setLoading(false);
    }
  };

  // الحصول على لون الحالة
  const getStatusColor = (status) => {
    switch (status) {
      case 'قيد المراجعة': return 'text-yellow-600 bg-yellow-100';
      case 'معلّقة': return 'text-orange-600 bg-orange-100';
      case 'مرفوضة': return 'text-red-600 bg-red-100';
      case 'تم التسوية': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // الحصول على أيقونة الحالة
  const getStatusIcon = (status) => {
    switch (status) {
      case 'قيد المراجعة': return <FiClock className="w-4 h-4" />;
      case 'معلّقة': return <FiAlertCircle className="w-4 h-4" />;
      case 'مرفوضة': return <FiX className="w-4 h-4" />;
      case 'تم التسوية': return <FiCheckCircle className="w-4 h-4" />;
      default: return <FiFileText className="w-4 h-4" />;
    }
  };

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
            <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>جاري التحميل...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <style jsx>{customStyles}</style>
      <div className="max-w-full mx-auto p-6">
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-slate-800' : 'bg-white'} rounded-xl p-6 shadow-lg mb-6`}>
          <div className="flex items-center justify-between">
            <div>
              <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
                🧠 إدارة التكاليف والعُهد الذكية
              </h1>
              <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                نظام مدمج لإدارة العُهد المستديمة والمؤقتة مع التسوية الذكية
              </p>
            </div>
            <div className="flex gap-3">
              <button
                onClick={() => {
                  setShowForm(true);
                  // تهيئة التاريخ الحالي
                  const today = new Date();
                  const todayFormatted = `${today.getDate().toString().padStart(2, '0')}/${(today.getMonth() + 1).toString().padStart(2, '0')}/${today.getFullYear()}`;
                  setDateInput(todayFormatted);
                  setFormData({...formData, costDate: today.toISOString().split('T')[0]});
                  // جلب التصنيفات للعُهدة المستديمة (افتراضي)
                  fetchCategoriesByCustodyType('مستديمة');
                }}
                className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg flex items-center gap-2 transition-colors"
              >
                <FiPlus className="w-5 h-5" />
                إضافة تكلفة جديدة
              </button>

              <button
                onClick={() => setShowCustodyManager(true)}
                className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg flex items-center gap-2 transition-colors"
              >
                <FiEdit className="w-5 h-5" />
                إدارة العُهد
              </button>
            </div>
          </div>
        </div>

        {/* ملخص الأرصدة */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          {/* العُهد المستديمة */}
          <div className={`${isDarkMode ? 'bg-slate-800' : 'bg-white'} rounded-xl p-6 shadow-lg`}>
            <div className="flex items-center justify-between mb-4">
              <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                العُهد المستديمة
              </h3>
              <div className="p-2 rounded-lg bg-blue-500/20">
                <FiDollarSign className="w-6 h-6 text-blue-500" />
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>إجمالي العُهدة:</span>
                <span className="font-medium text-blue-600">
                  {formatCurrency(balances?.permanent?.TotalInitialAmount || 240000)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>الرصيد المتاح:</span>
                <span className="font-medium text-green-600">
                  {formatCurrency(balances?.permanent?.AvailableBalance || balances?.permanent?.TotalCurrentBalance || 240000)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>قيد المراجعة:</span>
                <span className="font-medium text-yellow-600">
                  {formatCurrency(balances?.permanent?.PendingAmount || 0)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>المُسوّى:</span>
                <span className="font-medium text-red-600">
                  {formatCurrency(balances?.permanent?.TotalSettled || 0)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>آخر تسوية:</span>
                <span className="font-medium text-purple-600 text-xs">
                  {balances?.lastSettlementNumber || 'لا توجد تسويات'}
                </span>
              </div>
            </div>
          </div>

          {/* العُهد المؤقتة */}
          <div className={`${isDarkMode ? 'bg-slate-800' : 'bg-white'} rounded-xl p-6 shadow-lg`}>
            <div className="flex items-center justify-between mb-4">
              <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                العُهد المؤقتة
              </h3>
              <div className="p-2 rounded-lg bg-orange-500/20">
                <FiClock className="w-6 h-6 text-orange-500" />
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>إجمالي المطلوب:</span>
                <span className="font-medium text-orange-600">
                  {formatCurrency(balances?.temporary?.TotalRequested || 0)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>قيد المراجعة:</span>
                <span className="font-medium">{balances?.temporary?.PendingCount || 0}</span>
              </div>
              <div className="flex justify-between">
                <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>مُسوّاة:</span>
                <span className="font-medium text-green-600">{balances?.temporary?.SettledCount || 0}</span>
              </div>
            </div>
          </div>

          {/* إحصائيات التكاليف */}
          <div className={`${isDarkMode ? 'bg-slate-800' : 'bg-white'} rounded-xl p-6 shadow-lg`}>
            <div className="flex items-center justify-between mb-4">
              <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                إحصائيات التكاليف
              </h3>
              <div className="p-2 rounded-lg bg-purple-500/20">
                <FiFileText className="w-6 h-6 text-purple-500" />
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>إجمالي التكاليف:</span>
                <span className="font-medium">
                  {costs.length}
                </span>
              </div>
              <div className="flex justify-between">
                <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>قيد المراجعة:</span>
                <span className="font-medium text-yellow-600">
                  {costs.filter(c => c.Status === 'قيد المراجعة').length}
                </span>
              </div>
              <div className="flex justify-between">
                <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>مُسوّاة:</span>
                <span className="font-medium text-green-600">
                  {costs.filter(c => c.Status === 'تم التسوية').length}
                </span>
              </div>
            </div>
          </div>

          {/* إجراءات سريعة */}
          <div className={`${isDarkMode ? 'bg-slate-800' : 'bg-white'} rounded-xl p-6 shadow-lg`}>
            <div className="flex items-center justify-between mb-4">
              <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                إجراءات سريعة
              </h3>
              <div className="p-2 rounded-lg bg-green-500/20">
                <FiRefreshCw className="w-6 h-6 text-green-500" />
              </div>
            </div>
            <div className="space-y-3">
              <button
                onClick={fetchInitialData}
                className="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg text-sm transition-colors"
              >
                تحديث البيانات
              </button>
              <button
                onClick={() => setShowForm(true)}
                className="w-full bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg text-sm transition-colors"
              >
                إضافة تكلفة
              </button>
              <button
                className="w-full bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 rounded-lg text-sm transition-colors"
              >
                تصدير تقرير
              </button>
            </div>
          </div>
        </div>

        {/* نموذج إضافة تكلفة */}
        {showForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className={`${isDarkMode ? 'bg-slate-800' : 'bg-white'} rounded-xl p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto`}>
              <div className="flex items-center justify-between mb-6">
                <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  إضافة تكلفة جديدة
                </h2>
                <button
                  onClick={() => setShowForm(false)}
                  className="text-gray-500 hover:text-gray-700 p-2"
                >
                  <FiX className="w-6 h-6" />
                </button>
              </div>

              <form onSubmit={handleCreateCost} className="space-y-6">
                {/* نوع العُهدة */}
                <div>
                  <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                    نوع العُهدة *
                  </label>
                  <div className="flex gap-4">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="custodyType"
                        value="مستديمة"
                        checked={formData.custodyType === 'مستديمة'}
                        onChange={(e) => {
                          setFormData({...formData, custodyType: e.target.value, mainCategoryId: '', subCategoryId: ''});
                          fetchCategoriesByCustodyType(e.target.value);
                        }}
                        className="mr-2"
                      />
                      <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>مستديمة ✅</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="custodyType"
                        value="مؤقتة"
                        checked={formData.custodyType === 'مؤقتة'}
                        onChange={(e) => {
                          setFormData({...formData, custodyType: e.target.value, mainCategoryId: '', subCategoryId: ''});
                          fetchCategoriesByCustodyType(e.target.value);
                        }}
                        className="mr-2"
                      />
                      <span className={isDarkMode ? 'text-gray-300' : 'text-gray-700'}>مؤقتة ⏳</span>
                    </label>
                  </div>
                </div>

                {/* رقم العُهدة المستديمة */}
                {formData.custodyType === 'مستديمة' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                        رقم العُهدة المستديمة *
                      </label>
                      <select
                        value={formData.permanentCustodyNumber}
                        onChange={(e) => setFormData({...formData, permanentCustodyNumber: e.target.value})}
                        className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                        required={formData.custodyType === 'مستديمة'}
                      >
                        <option value="">اختر العُهدة المستديمة</option>
                        {(() => {

                          return permanentCustodies.map(custody => (
                            <option key={custody.ID} value={custody.CustodyNumber}>
                              {custody.CustodyNumber} - {custody.CustodianName} (الرصيد: {formatCurrency(custody.CurrentBalance)})
                            </option>
                          ));
                        })()}
                      </select>
                      {permanentCustodies.length === 0 && (
                        <div className="text-xs text-orange-500 mt-1">
                          ⚠️ لا توجد عُهد مستديمة متاحة. جاري إنشاء العُهدة الافتراضية...
                        </div>
                      )}
                      <div className="text-xs text-gray-500 mt-1">
                        💡 العُهد المتاحة: {permanentCustodies.length} |
                        {permanentCustodies.length > 0 && ` آخر عُهدة: ${permanentCustodies[0]?.CustodyNumber}`}
                      </div>
                    </div>

                    <div>
                      <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                        رقم التسوية *
                      </label>
                      <input
                        type="text"
                        value={formData.settlementNumber}
                        onChange={(e) => setFormData({...formData, settlementNumber: e.target.value})}
                        className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                        placeholder="رقم التسوية"
                        required={formData.custodyType === 'مستديمة'}
                      />
                    </div>
                  </div>
                )}

                {/* الغرض/الوصف */}
                <div>
                  <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                    الغرض / الوصف *
                  </label>
                  <textarea
                    value={formData.purpose}
                    onChange={(e) => setFormData({...formData, purpose: e.target.value})}
                    className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                    rows={3}
                    placeholder="وصف تفصيلي للغرض من المصروف"
                    required
                  />
                </div>

                {/* التصنيف */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                      البند الرئيسي *
                    </label>
                    <select
                      value={formData.mainCategoryId}
                      onChange={(e) => {
                        setFormData({
                          ...formData,
                          mainCategoryId: e.target.value,
                          subCategoryId: '' // إعادة تعيين البند الفرعي
                        });
                      }}
                      className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                      required
                    >
                      <option value="">اختر البند الرئيسي</option>
                      {categories.map(category => (
                        <option key={category.ID} value={category.ID}>
                          {category.CategoryName}
                          {/* عرض عدد الفروع للعُهدة المستديمة فقط */}
                          {formData.custodyType === 'مستديمة' && category.CategoryCode === 'MEALS' && ' (5 فروع)'}
                          {formData.custodyType === 'مستديمة' && category.CategoryCode === 'BEVERAGES_CLEANING' && ' (4 فروع)'}
                          {formData.custodyType === 'مستديمة' && category.CategoryCode === 'TRANSPORTATION' && ' (4 فروع)'}
                          {formData.custodyType === 'مستديمة' && category.CategoryCode === 'PURCHASES' && ' (3 فروع)'}
                          {formData.custodyType === 'مستديمة' && category.CategoryCode === 'APARTMENTS' && ' (3 فروع)'}
                          {formData.custodyType === 'مستديمة' && category.CategoryCode === 'CARS' && ' (7 فروع)'}
                          {formData.custodyType === 'مستديمة' && category.CategoryCode === 'SERVICES' && ' (7 فروع)'}
                          {/* للعُهدة المؤقتة - بدون فروع */}
                          {formData.custodyType === 'مؤقتة' && category.CategoryCode === 'TEMP_OTHER' && ' (يرجى تحديد التفاصيل في الملاحظات)'}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* البند الفرعي - فقط للعُهدة المستديمة */}
                  {formData.custodyType === 'مستديمة' && (
                    <div>
                      <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                        البند الفرعي
                      </label>
                      <select
                        value={formData.subCategoryId}
                        onChange={(e) => setFormData({...formData, subCategoryId: e.target.value})}
                        className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                        disabled={!formData.mainCategoryId}
                      >
                        <option value="">اختر البند الفرعي (اختياري)</option>
                        {formData.mainCategoryId && categories
                          .find(cat => cat.ID == formData.mainCategoryId)?.SubCategories
                          ?.map(subCat => (
                            <option key={subCat.ID} value={subCat.ID}>
                              {subCat.CategoryName}
                            </option>
                          ))}
                      </select>
                    </div>
                  )}

                  {/* ملاحظة للعُهدة المؤقتة */}
                  {formData.custodyType === 'مؤقتة' && (
                    <div className={`p-3 rounded-lg ${isDarkMode ? 'bg-slate-700' : 'bg-blue-50'} border ${isDarkMode ? 'border-slate-600' : 'border-blue-200'}`}>
                      <p className={`text-sm ${isDarkMode ? 'text-blue-300' : 'text-blue-700'}`}>
                        💡 العُهدة المؤقتة لا تحتوي على بنود فرعية. إذا اخترت "أخرى" يرجى تحديد التفاصيل في الملاحظات.
                      </p>
                    </div>
                  )}
                </div>

                {/* المبلغ ورقم الإيصال والتاريخ */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                      المبلغ (جنيه مصري) *
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.amount}
                      onChange={(e) => setFormData({...formData, amount: e.target.value})}
                      className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                      placeholder="0.00"
                      required
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                      رقم الإيصال
                    </label>
                    <input
                      type="text"
                      value={formData.receiptNumber}
                      onChange={(e) => setFormData({...formData, receiptNumber: e.target.value})}
                      className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                      placeholder="رقم الإيصال أو الفاتورة"
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                      تاريخ التكلفة * (يوم/شهر/سنة)
                    </label>
                    <div className="relative">
                      <input
                        type="text"
                        value={dateInput}
                        onChange={(e) => handleDateChange(e.target.value)}
                        placeholder="24/06/2025"
                        className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'} ${
                          dateInput && !isValidDate(dateInput) ? 'border-red-500' : ''
                        }`}
                        required
                        maxLength={10}
                        onKeyDown={(e) => {
                          // السماح بالأرقام والشرطة المائلة فقط
                          if (!/[\d\/]/.test(e.key) && e.key !== 'Backspace' && e.key !== 'Delete' && e.key !== 'ArrowLeft' && e.key !== 'ArrowRight') {
                            e.preventDefault();
                          }
                        }}
                        onInput={(e) => {
                          // تنسيق تلقائي للتاريخ
                          const target = e.target;
                          if (target && 'value' in target && typeof target.value === 'string') {
                            let value = target.value.replace(/[^\d]/g, '');
                            if (value.length >= 2) {
                              value = value.substring(0, 2) + '/' + value.substring(2);
                            }
                            if (value.length >= 5) {
                              value = value.substring(0, 5) + '/' + value.substring(5, 9);
                            }
                            if (value !== target.value) {
                              target.value = value;
                              handleDateChange(value);
                            }
                          }
                        }}
                      />
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                        <span className="text-gray-400">📅</span>
                      </div>
                    </div>
                    <div className="text-xs mt-1">
                      {dateInput && !isValidDate(dateInput) ? (
                        <span className="text-red-500">
                          ❌ تنسيق التاريخ غير صحيح. استخدم: يوم/شهر/سنة (مثال: 24/06/2025)
                        </span>
                      ) : dateInput && isValidDate(dateInput) ? (
                        <span className="text-green-600">
                          ✅ التاريخ صحيح: {dateInput}
                        </span>
                      ) : (
                        <span className="text-gray-500">
                          💡 أدخل التاريخ بتنسيق: يوم/شهر/سنة (مثال: 24/06/2025)
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {/* ملاحظات */}
                <div>
                  <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                    ملاحظات إضافية
                  </label>
                  <textarea
                    value={formData.notes}
                    onChange={(e) => setFormData({...formData, notes: e.target.value})}
                    className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                    rows={2}
                    placeholder="أي ملاحظات إضافية"
                  />
                </div>

                {/* أزرار الإجراءات */}
                <div className="flex gap-4 pt-4">
                  <button
                    type="submit"
                    disabled={loading}
                    className="flex-1 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white py-3 px-6 rounded-lg font-medium transition-colors"
                  >
                    {loading ? 'جاري الحفظ...' : 'حفظ التكلفة'}
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowForm(false)}
                    className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    إلغاء
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* نافذة إدارة العُهد المستديمة */}
        {showCustodyManager && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className={`${isDarkMode ? 'bg-slate-800' : 'bg-white'} rounded-xl p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto`}>
              <div className="flex items-center justify-between mb-6">
                <h2 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  🏦 إدارة العُهد المستديمة
                </h2>
                <button
                  onClick={() => setShowCustodyManager(false)}
                  className="text-gray-500 hover:text-gray-700 p-2"
                >
                  <FiX className="w-6 h-6" />
                </button>
              </div>

              {/* نموذج إضافة عُهدة جديدة */}
              <div className={`${isDarkMode ? 'bg-slate-700' : 'bg-gray-50'} rounded-lg p-6 mb-6`}>
                <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-4`}>
                  ➕ إضافة عُهدة مستديمة جديدة
                </h3>

                <form onSubmit={handleAddCustody} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                        رقم العُهدة *
                      </label>
                      <input
                        type="text"
                        value={custodyFormData.custodyNumber}
                        onChange={(e) => setCustodyFormData({...custodyFormData, custodyNumber: e.target.value})}
                        className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-600 border-slate-500 text-white' : 'bg-white border-gray-300'}`}
                        placeholder="مثال: 1451"
                        required
                      />
                    </div>

                    <div>
                      <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                        اسم المسؤول *
                      </label>
                      <input
                        type="text"
                        value={custodyFormData.custodianName}
                        onChange={(e) => setCustodyFormData({...custodyFormData, custodianName: e.target.value})}
                        className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-600 border-slate-500 text-white' : 'bg-white border-gray-300'}`}
                        placeholder="اسم المسؤول عن العُهدة"
                        required
                      />
                    </div>

                    <div>
                      <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                        رقم الموظف
                      </label>
                      <input
                        type="text"
                        value={custodyFormData.custodianId}
                        onChange={(e) => setCustodyFormData({...custodyFormData, custodianId: e.target.value})}
                        className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-600 border-slate-500 text-white' : 'bg-white border-gray-300'}`}
                        placeholder="رقم الموظف"
                      />
                    </div>

                    <div>
                      <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                        القسم *
                      </label>
                      <input
                        type="text"
                        value={custodyFormData.department}
                        onChange={(e) => setCustodyFormData({...custodyFormData, department: e.target.value})}
                        className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-600 border-slate-500 text-white' : 'bg-white border-gray-300'}`}
                        placeholder="القسم أو الإدارة"
                        required
                      />
                    </div>

                    <div>
                      <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                        المبلغ الأولي (جنيه) *
                      </label>
                      <input
                        type="number"
                        step="0.01"
                        min="0"
                        value={custodyFormData.initialAmount}
                        onChange={(e) => setCustodyFormData({...custodyFormData, initialAmount: e.target.value})}
                        className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-600 border-slate-500 text-white' : 'bg-white border-gray-300'}`}
                        placeholder="240000"
                        required
                      />
                    </div>

                    <div>
                      <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                        ملاحظات
                      </label>
                      <input
                        type="text"
                        value={custodyFormData.notes}
                        onChange={(e) => setCustodyFormData({...custodyFormData, notes: e.target.value})}
                        className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-600 border-slate-500 text-white' : 'bg-white border-gray-300'}`}
                        placeholder="ملاحظات إضافية"
                      />
                    </div>
                  </div>

                  <button
                    type="submit"
                    disabled={loading}
                    className="bg-green-500 hover:bg-green-600 disabled:bg-green-300 text-white py-3 px-6 rounded-lg font-medium transition-colors"
                  >
                    {loading ? 'جاري الإضافة...' : 'إضافة العُهدة'}
                  </button>
                </form>
              </div>

              {/* قائمة العُهد الحالية */}
              <div>
                <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-4`}>
                  📋 العُهد المستديمة الحالية ({permanentCustodies.length})
                </h3>

                <div className="space-y-4">
                  {permanentCustodies.map(custody => (
                    <div key={custody.ID} className={`${isDarkMode ? 'bg-slate-700' : 'bg-gray-50'} rounded-lg p-4 border ${isDarkMode ? 'border-slate-600' : 'border-gray-200'}`}>
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                              <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>رقم العُهدة:</span>
                              <div className={`font-semibold ${isDarkMode ? 'text-blue-300' : 'text-blue-600'}`}>
                                {custody.CustodyNumber}
                              </div>
                            </div>
                            <div>
                              <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>المسؤول:</span>
                              <div className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                                {custody.CustodianName}
                              </div>
                            </div>
                            <div>
                              <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>القسم:</span>
                              <div className={`font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                                {custody.Department}
                              </div>
                            </div>
                            <div>
                              <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>المبلغ الأولي:</span>
                              <div className={`font-semibold ${isDarkMode ? 'text-green-400' : 'text-green-600'}`}>
                                {formatCurrency(custody.InitialAmount)}
                              </div>
                            </div>
                            <div>
                              <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>الرصيد الحالي:</span>
                              <div className={`font-semibold ${isDarkMode ? 'text-green-400' : 'text-green-600'}`}>
                                {formatCurrency(custody.CurrentBalance)}
                              </div>
                            </div>
                            <div>
                              <span className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>الحالة:</span>
                              <div className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${
                                custody.Status === 'نشطة'
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-red-100 text-red-800'
                              }`}>
                                {custody.Status}
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-2 ml-4">
                          <button
                            onClick={() => handleUpdateCustodyAmount(custody.ID)}
                            className="text-blue-600 hover:text-blue-800 p-2 rounded-lg hover:bg-blue-50 transition-colors"
                            title="تعديل المبلغ"
                          >
                            <FiEdit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteCustody(custody.ID)}
                            className="text-red-600 hover:text-red-800 p-2 rounded-lg hover:bg-red-50 transition-colors"
                            title="حذف العُهدة"
                          >
                            <FiTrash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}

                  {permanentCustodies.length === 0 && (
                    <div className="text-center py-8">
                      <div className={`text-gray-500 ${isDarkMode ? 'text-gray-400' : ''}`}>
                        <FiFileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
                        <p>لا توجد عُهد مستديمة مسجلة</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* جدول التكاليف */}
        <div className={`${isDarkMode ? 'bg-slate-800' : 'bg-white'} rounded-xl shadow-lg overflow-hidden`}>
          <div className="p-6 border-b border-gray-200 dark:border-slate-700">
            <div className="flex items-center justify-between">
              <h2 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                سجل التكاليف والعُهد
              </h2>
              <div className="flex flex-wrap items-center gap-4">
                {/* فلاتر سريعة */}
                <select
                  value={filters.status}
                  onChange={(e) => setFilters({...filters, status: e.target.value})}
                  className={`px-3 py-2 border rounded-lg text-sm ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                >
                  <option value="">جميع الحالات</option>
                  <option value="قيد المراجعة">قيد المراجعة</option>
                  <option value="معلّقة">معلّقة</option>
                  <option value="مرفوضة">مرفوضة</option>
                  <option value="تم التسوية">تم التسوية</option>
                </select>

                <select
                  value={filters.custodyType}
                  onChange={(e) => setFilters({...filters, custodyType: e.target.value})}
                  className={`px-3 py-2 border rounded-lg text-sm ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                >
                  <option value="">جميع الأنواع</option>
                  <option value="مستديمة">مستديمة</option>
                  <option value="مؤقتة">مؤقتة</option>
                </select>

                {/* فلتر الشهر */}
                <select
                  value={filters.month}
                  onChange={(e) => setFilters({...filters, month: e.target.value})}
                  className={`px-3 py-2 border rounded-lg text-sm ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                >
                  <option value="">جميع الشهور</option>
                  <option value="1">يناير</option>
                  <option value="2">فبراير</option>
                  <option value="3">مارس</option>
                  <option value="4">أبريل</option>
                  <option value="5">مايو</option>
                  <option value="6">يونيو</option>
                  <option value="7">يوليو</option>
                  <option value="8">أغسطس</option>
                  <option value="9">سبتمبر</option>
                  <option value="10">أكتوبر</option>
                  <option value="11">نوفمبر</option>
                  <option value="12">ديسمبر</option>
                </select>

                {/* فلتر السنة */}
                <select
                  value={filters.year}
                  onChange={(e) => setFilters({...filters, year: e.target.value})}
                  className={`px-3 py-2 border rounded-lg text-sm ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                >
                  <option value="">جميع السنوات</option>
                  <option value="2024">2024</option>
                  <option value="2025">2025</option>
                  <option value="2026">2026</option>
                </select>

                {/* فلتر البند الرئيسي */}
                <select
                  value={filters.mainCategory}
                  onChange={(e) => setFilters({...filters, mainCategory: e.target.value, subCategory: ''})}
                  className={`px-3 py-2 border rounded-lg text-sm ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                >
                  <option value="">جميع البنود الرئيسية</option>
                  {categories.map(category => (
                    <option key={category.ID} value={category.ID}>
                      {category.CategoryName}
                    </option>
                  ))}
                </select>

                {/* فلتر البند الفرعي */}
                <select
                  value={filters.subCategory}
                  onChange={(e) => setFilters({...filters, subCategory: e.target.value})}
                  disabled={!filters.mainCategory}
                  className={`px-3 py-2 border rounded-lg text-sm ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'} ${!filters.mainCategory ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <option value="">
                    {!filters.mainCategory ? 'اختر البند الرئيسي أولاً' : 'جميع البنود الفرعية'}
                  </option>
                  {filters.mainCategory && categories
                    .find(cat => cat.ID == filters.mainCategory)?.SubCategories
                    ?.map(subCat => (
                      <option key={subCat.ID} value={subCat.ID}>
                        {subCat.CategoryName}
                      </option>
                    ))}
                </select>

                {/* بحث نصي */}
                <input
                  type="text"
                  placeholder="بحث برقم التسوية أو الوصف..."
                  value={filters.searchText}
                  onChange={(e) => setFilters({...filters, searchText: e.target.value})}
                  className={`px-3 py-2 border rounded-lg text-sm ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                />

                {/* أزرار إضافة البنود */}
                <button
                  onClick={() => setShowAddCategoryModal(true)}
                  className="px-3 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg text-sm flex items-center gap-1 transition-colors"
                  title="إضافة بند رئيسي جديد"
                >
                  <FiPlus className="w-4 h-4" />
                  بند رئيسي
                </button>

                <button
                  onClick={() => setShowAddSubCategoryModal(true)}
                  className="px-3 py-2 bg-indigo-500 hover:bg-indigo-600 text-white rounded-lg text-sm flex items-center gap-1 transition-colors"
                  title="إضافة بند فرعي جديد"
                >
                  <FiPlus className="w-4 h-4" />
                  بند فرعي
                </button>

                <button
                  onClick={fetchInitialData}
                  className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm flex items-center gap-2"
                >
                  <FiRefreshCw className="w-4 h-4" />
                  تحديث
                </button>

                <button
                  onClick={() => setShowCustodyManager(true)}
                  className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm flex items-center gap-2"
                >
                  <FiEdit className="w-4 h-4" />
                  إدارة العُهد
                </button>
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full min-w-[1200px]">
              <thead className={`${isDarkMode ? 'bg-slate-700' : 'bg-gray-50'}`}>
                <tr>
                  <th className={`px-3 py-4 text-center text-xs font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider`} style={{width: '60px'}}>
                    م
                  </th>
                  <th className={`px-4 py-4 text-right text-xs font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider`} style={{width: '150px'}}>
                    رقم التسوية
                  </th>
                  <th className={`px-4 py-4 text-right text-xs font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider`} style={{width: '140px'}}>
                    قيمة التسوية
                  </th>
                  <th className={`px-4 py-4 text-right text-xs font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider`} style={{width: '180px'}}>
                    البند الرئيسي
                  </th>
                  <th className={`px-4 py-4 text-right text-xs font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider`} style={{width: '180px'}}>
                    البند الفرعي
                  </th>
                  <th className={`px-4 py-4 text-right text-xs font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider`} style={{minWidth: '300px'}}>
                    ملاحظات
                  </th>
                  <th className={`px-4 py-4 text-right text-xs font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider`} style={{width: '120px'}}>
                    الحالة
                  </th>
                  <th className={`px-4 py-4 text-right text-xs font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider`} style={{width: '140px'}}>
                    التاريخ
                  </th>
                  <th className={`px-4 py-4 text-center text-xs font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-500'} uppercase tracking-wider`} style={{width: '160px'}}>
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className={`${isDarkMode ? 'bg-slate-800' : 'bg-white'} divide-y ${isDarkMode ? 'divide-slate-700' : 'divide-gray-200'}`}>
                {filteredCosts.length === 0 ? (
                  <tr>
                    <td colSpan={8} className="px-6 py-12 text-center">
                      <div className="text-gray-500">
                        <FiFileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
                        <p>لا توجد تكاليف مسجلة</p>
                        <button
                          onClick={() => setShowForm(true)}
                          className="mt-4 text-blue-500 hover:text-blue-600 font-medium"
                        >
                          إضافة أول تكلفة
                        </button>
                      </div>
                    </td>
                  </tr>
                ) : (
                  filteredCosts.map((cost, index) => (
                    <tr key={cost.ID} className={`hover:${isDarkMode ? 'bg-slate-700' : 'bg-gray-50'} transition-colors border-b ${isDarkMode ? 'border-slate-600' : 'border-gray-200'}`}>
                      {/* م */}
                      <td className="px-3 py-4 text-center" style={{width: '60px'}}>
                        <div className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                          {index + 1}
                        </div>
                      </td>

                      {/* رقم التسوية */}
                      <td className="px-4 py-4" style={{width: '150px'}}>
                        <div className="space-y-2">
                          {cost.SettlementNumber ? (
                            <div className={`text-sm font-medium ${isDarkMode ? 'text-blue-300' : 'text-blue-600'} break-words`} title={cost.SettlementNumber}>
                              📄 {cost.SettlementNumber}
                            </div>
                          ) : cost.CustodyType === 'مؤقتة' && cost.RequestNumber ? (
                            <div className={`text-sm font-medium ${isDarkMode ? 'text-orange-300' : 'text-orange-600'} break-words`} title={cost.RequestNumber}>
                              📋 {cost.RequestNumber}
                            </div>
                          ) : (
                            <div className="text-xs text-gray-400">-</div>
                          )}
                          <div className={`text-xs px-2 py-1 rounded-full inline-block ${
                            cost.CustodyType === 'مستديمة'
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-orange-100 text-orange-800'
                          }`}>
                            {cost.CustodyType}
                          </div>
                        </div>
                      </td>

                      {/* قيمة التسوية */}
                      <td className="px-4 py-4" style={{width: '140px'}}>
                        <div className="space-y-1">
                          <div className={`text-lg font-bold ${isDarkMode ? 'text-green-400' : 'text-green-600'}`}>
                            {formatCurrency(cost.Amount)}
                          </div>
                          <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                            جنيه مصري
                          </div>
                        </div>
                      </td>

                      {/* البند الرئيسي */}
                      <td className="px-4 py-4" style={{width: '180px'}}>
                        <div className="space-y-1">
                          <div className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'} break-words`} title={cost.MainCategoryName}>
                            🏷️ {cost.MainCategoryName}
                          </div>
                        </div>
                      </td>

                      {/* البند الفرعي */}
                      <td className="px-4 py-4" style={{width: '180px'}}>
                        <div className="space-y-1">
                          <div className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} break-words`} title={cost.SubCategoryName || 'لا يوجد'}>
                            {cost.SubCategoryName ? `↳ ${cost.SubCategoryName}` : '-'}
                          </div>
                          {cost.ReceiptNumber && (
                            <div className="text-xs text-gray-500 break-words" title={`إيصال: ${cost.ReceiptNumber}`}>
                              🧾 {cost.ReceiptNumber}
                            </div>
                          )}
                        </div>
                      </td>

                      {/* ملاحظات */}
                      <td className="px-4 py-4" style={{minWidth: '300px'}}>
                        <div className="space-y-2">
                          <div className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} break-words`} title={cost.CostDescription}>
                            📝 {cost.CostDescription}
                          </div>
                          {cost.Notes && (
                            <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'} italic break-words`} title={cost.Notes}>
                              💬 {cost.Notes}
                            </div>
                          )}
                          {cost.CustodyType === 'مستديمة' && cost.CustodyNumber && (
                            <div className="text-xs text-blue-600 break-words" title={`عُهدة: ${cost.CustodyNumber} - ${cost.CustodianName}`}>
                              👤 عُهدة: {cost.CustodyNumber} - {cost.CustodianName}
                            </div>
                          )}
                        </div>
                      </td>

                      {/* الحالة */}
                      <td className="px-4 py-4" style={{width: '120px'}}>
                        <div className="space-y-1">
                          <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(cost.Status)}`}>
                            {getStatusIcon(cost.Status)}
                            <span>{cost.Status}</span>
                          </div>
                        </div>
                      </td>

                      {/* التاريخ */}
                      <td className="px-4 py-4" style={{width: '140px'}}>
                        <div className="space-y-1">
                          <div className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                            📅 {formatDate(cost.CostDate)}
                          </div>
                          {cost.DaysOld > 0 && (
                            <div className={`text-xs ${
                              cost.DaysOld > 14 ? 'text-red-500' :
                              cost.DaysOld > 7 ? 'text-orange-500' : 'text-gray-500'
                            }`}>
                              ⏰ منذ {cost.DaysOld} يوم
                            </div>
                          )}
                        </div>
                      </td>

                      {/* الإجراءات */}
                      <td className="px-4 py-4" style={{width: '160px'}}>
                        <div className="flex items-center gap-1 justify-center flex-wrap">
                          {/* أزرار تغيير الحالة */}
                          {cost.Status === 'قيد المراجعة' && (
                            <>
                              <button
                                onClick={() => handleChangeStatus(cost.ID, 'تم التسوية')}
                                className="text-green-600 hover:text-green-800 p-1 rounded"
                                title="تم التسوية"
                              >
                                <FiCheck className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleChangeStatus(cost.ID, 'معلّقة')}
                                className="text-orange-600 hover:text-orange-800 p-1 rounded"
                                title="تعليق"
                              >
                                <FiClock className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleChangeStatus(cost.ID, 'مرفوضة')}
                                className="text-red-600 hover:text-red-800 p-1 rounded"
                                title="رفض"
                              >
                                <FiX className="w-4 h-4" />
                              </button>
                            </>
                          )}

                          {cost.Status === 'تم التسوية' && (
                            <button
                              onClick={() => handleChangeStatus(cost.ID, 'قيد المراجعة')}
                              className="text-blue-600 hover:text-blue-800 p-1 rounded"
                              title="إرجاع للمراجعة"
                            >
                              <FiRefreshCw className="w-4 h-4" />
                            </button>
                          )}

                          {cost.Status === 'معلّقة' && (
                            <>
                              <button
                                onClick={() => handleChangeStatus(cost.ID, 'قيد المراجعة')}
                                className="text-blue-600 hover:text-blue-800 p-1 rounded"
                                title="إرجاع للمراجعة"
                              >
                                <FiRefreshCw className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleChangeStatus(cost.ID, 'تم التسوية')}
                                className="text-green-600 hover:text-green-800 p-1 rounded"
                                title="تم التسوية"
                              >
                                <FiCheck className="w-4 h-4" />
                              </button>
                            </>
                          )}

                          {cost.Status === 'مرفوضة' && (
                            <button
                              onClick={() => handleChangeStatus(cost.ID, 'قيد المراجعة')}
                              className="text-blue-600 hover:text-blue-800 p-1 rounded"
                              title="إرجاع للمراجعة"
                            >
                              <FiRefreshCw className="w-4 h-4" />
                            </button>
                          )}

                          {/* رفع ملف PDF */}
                          {(cost.CustodyType === 'مستديمة' && cost.SettlementNumber) || cost.CustodyType === 'مؤقتة' ? (
                            <button
                              onClick={() => handleUploadFile(cost.ID)}
                              className="text-purple-600 hover:text-purple-800 p-1 rounded"
                              title="رفع ملف PDF"
                            >
                              <FiUpload className="w-4 h-4" />
                            </button>
                          ) : null}

                          {/* حذف التسوية */}
                          <button
                            onClick={() => handleDeleteCost(cost.ID)}
                            className="text-red-600 hover:text-red-800 p-1 rounded"
                            title="حذف التسوية"
                          >
                            <FiTrash2 className="w-4 h-4" />
                          </button>

                          {/* عرض التفاصيل */}
                          <button
                            onClick={() => setSelectedCost(cost)}
                            className="text-gray-600 hover:text-gray-800 p-1 rounded"
                            title="عرض التفاصيل"
                          >
                            <FiEdit className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}

                {/* صف المجموع الإجمالي */}
                {filteredCosts.length > 0 && (
                  <tr className={`border-t-2 ${isDarkMode ? 'border-slate-600 bg-slate-700' : 'border-gray-300 bg-gray-50'} font-bold`}>
                    {/* م */}
                    <td className="px-4 py-4 text-center">
                      <div className={`text-sm font-bold ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`}>
                        📊
                      </div>
                    </td>

                    {/* رقم التسوية */}
                    <td className="px-4 py-4">
                      <div className={`text-sm font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        المجموع الإجمالي
                      </div>
                      <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        {getDisplayedCostsCount()} تكلفة
                      </div>
                    </td>

                    {/* قيمة التسوية */}
                    <td className="px-4 py-4">
                      <div className={`text-lg font-bold ${isDarkMode ? 'text-green-400' : 'text-green-600'}`}>
                        {formatCurrency(calculateTotalAmount())}
                      </div>
                      <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        إجمالي المبالغ
                      </div>
                    </td>

                    {/* البند الرئيسي */}
                    <td className="px-4 py-4">
                      <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        جميع البنود
                      </div>
                    </td>

                    {/* البند الفرعي */}
                    <td className="px-4 py-4">
                      <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        -
                      </div>
                    </td>

                    {/* ملاحظات */}
                    <td className="px-4 py-4">
                      <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        {getFilterDescription()}
                      </div>
                      <div className={`text-xs ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                        إجمالي التكاليف المعروضة
                      </div>
                    </td>

                    {/* الحالة */}
                    <td className="px-4 py-4">
                      <div className="space-y-1">
                        <div className={`text-xs px-2 py-1 rounded-full inline-block ${isDarkMode ? 'bg-blue-600 text-white' : 'bg-blue-100 text-blue-800'}`}>
                          ملخص الحالات
                        </div>
                        <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          {(() => {
                            const stats = getCostStatistics();
                            return `✅ ${stats.settled} | ⏳ ${stats.pending} | ⏸️ ${stats.suspended} | ❌ ${stats.rejected}`;
                          })()}
                        </div>
                      </div>
                    </td>

                    {/* التاريخ */}
                    <td className="px-4 py-4">
                      <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        📅 {new Date().toLocaleDateString('ar-EG')}
                      </div>
                    </td>

                    {/* الإجراءات */}
                    <td className="px-4 py-4">
                      <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        -
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* نافذة إضافة بند رئيسي */}
      {showAddCategoryModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`${isDarkMode ? 'bg-slate-800' : 'bg-white'} rounded-lg p-6 w-full max-w-md mx-4`}>
            <h3 className={`text-lg font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              إضافة بند رئيسي جديد
            </h3>

            <form onSubmit={handleAddMainCategory} className="space-y-4">
              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  اسم البند
                </label>
                <input
                  type="text"
                  value={newCategoryData.categoryName}
                  onChange={(e) => setNewCategoryData({...newCategoryData, categoryName: e.target.value})}
                  className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                  required
                />
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  كود البند
                </label>
                <input
                  type="text"
                  value={newCategoryData.categoryCode}
                  onChange={(e) => setNewCategoryData({...newCategoryData, categoryCode: e.target.value})}
                  className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                  required
                />
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  نوع العُهدة
                </label>
                <select
                  value={newCategoryData.custodyType}
                  onChange={(e) => setNewCategoryData({...newCategoryData, custodyType: e.target.value})}
                  className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                >
                  <option value="مستديمة">مستديمة</option>
                  <option value="مؤقتة">مؤقتة</option>
                </select>
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  الوصف
                </label>
                <textarea
                  value={newCategoryData.description}
                  onChange={(e) => setNewCategoryData({...newCategoryData, description: e.target.value})}
                  className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                  rows={3}
                />
              </div>

              <div className="flex gap-3 pt-4">
                <button
                  type="submit"
                  disabled={loading}
                  className="flex-1 bg-purple-500 hover:bg-purple-600 disabled:bg-purple-300 text-white py-2 px-4 rounded-lg transition-colors"
                >
                  {loading ? 'جاري الإضافة...' : 'إضافة البند'}
                </button>
                <button
                  type="button"
                  onClick={() => setShowAddCategoryModal(false)}
                  className="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg transition-colors"
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* نافذة إضافة بند فرعي */}
      {showAddSubCategoryModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`${isDarkMode ? 'bg-slate-800' : 'bg-white'} rounded-lg p-6 w-full max-w-md mx-4`}>
            <h3 className={`text-lg font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              إضافة بند فرعي جديد
            </h3>

            <form onSubmit={handleAddSubCategory} className="space-y-4">
              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  البند الرئيسي
                </label>
                <select
                  value={newSubCategoryData.parentCategoryId}
                  onChange={(e) => setNewSubCategoryData({...newSubCategoryData, parentCategoryId: e.target.value})}
                  className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                  required
                >
                  <option value="">اختر البند الرئيسي</option>
                  {categories.map(category => (
                    <option key={category.ID} value={category.ID}>
                      {category.CategoryName}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  اسم البند الفرعي
                </label>
                <input
                  type="text"
                  value={newSubCategoryData.categoryName}
                  onChange={(e) => setNewSubCategoryData({...newSubCategoryData, categoryName: e.target.value})}
                  className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                  required
                />
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  كود البند الفرعي
                </label>
                <input
                  type="text"
                  value={newSubCategoryData.categoryCode}
                  onChange={(e) => setNewSubCategoryData({...newSubCategoryData, categoryCode: e.target.value})}
                  className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                  required
                />
              </div>

              <div>
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  الوصف
                </label>
                <textarea
                  value={newSubCategoryData.description}
                  onChange={(e) => setNewSubCategoryData({...newSubCategoryData, description: e.target.value})}
                  className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                  rows={3}
                />
              </div>

              <div className="flex gap-3 pt-4">
                <button
                  type="submit"
                  disabled={loading}
                  className="flex-1 bg-indigo-500 hover:bg-indigo-600 disabled:bg-indigo-300 text-white py-2 px-4 rounded-lg transition-colors"
                >
                  {loading ? 'جاري الإضافة...' : 'إضافة البند الفرعي'}
                </button>
                <button
                  type="button"
                  onClick={() => setShowAddSubCategoryModal(false)}
                  className="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-2 px-4 rounded-lg transition-colors"
                >
                  إلغاء
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </MainLayout>
  );
}
