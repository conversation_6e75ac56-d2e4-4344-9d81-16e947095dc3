'use client';
import React, { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTheme } from '@/contexts/ThemeContext';
import { Search, RefreshCw, Check, X, Trash2, Calendar, Clock, User } from 'lucide-react';

export default function PendingRequestsPage() {
  const { isArabic } = useLanguage();
  const { isDarkMode } = useTheme();
  
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [actionType, setActionType] = useState(''); // 'approve', 'reject', 'delete'
  const [notes, setNotes] = useState('');

  // جلب الطلبات قيد المراجعة
  const fetchPendingRequests = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/paper-requests?action=list&status=قيد المراجعة');
      const result = await response.json();
      
      if (result.success) {
        setRequests(result.requests || []);
      } else {

      }
    } catch (error) {

    }
    setLoading(false);
  };

  useEffect(() => {
    fetchPendingRequests();
  }, []);

  // فلترة الطلبات حسب البحث
  const filteredRequests = requests.filter(request => 
    request.EmployeeName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    request.EmployeeCode?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    request.LeaveType?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // فتح نافذة الإجراء
  const openActionModal = (request, action) => {
    setSelectedRequest(request);
    setActionType(action);
    setNotes('');
    setShowModal(true);
  };

  // تنفيذ الإجراء
  const executeAction = async () => {
    if (!selectedRequest) return;

    try {
      // الحصول على كود المستخدم من localStorage
      const userInfo = localStorage.getItem('userInfo');
      const userCode = userInfo ? JSON.parse(userInfo).code || '1450' : '1450';

      let endpoint = '/api/paper-requests';
      let body = {};

      if (actionType === 'delete') {
        body = {
          action: 'delete',
          requestId: selectedRequest.ID
        };
      } else {
        body = {
          action: 'update-status',
          requestId: selectedRequest.ID,
          status: actionType === 'approve' ? 'معتمدة' : 'مرفوضة',
          notes: notes
        };
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-code': userCode // إرسال كود المستخدم
        },
        body: JSON.stringify(body)
      });

      const result = await response.json();
      
      if (result.success) {
        alert(result.message);
        fetchPendingRequests(); // إعادة تحميل الطلبات
        setShowModal(false);
      } else {
        alert(result.error || 'خطأ في تنفيذ الإجراء');
      }
    } catch (error) {

      alert('خطأ في تنفيذ الإجراء');
    }
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto">
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="flex items-center justify-between">
            <div>
              <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
                طلبات قيد المراجعة
              </h1>
              <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                مراجعة واعتماد أو رفض طلبات الإجازات المقدمة
              </p>
            </div>
            <div className="flex gap-3">
              <button
                onClick={fetchPendingRequests}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
              >
                <RefreshCw className="w-4 h-4" />
                تحديث
              </button>
            </div>
          </div>
        </div>

        {/* شريط البحث */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-4 mb-6`}>
          <div className="flex items-center gap-4">
            <div className="flex-1 relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="البحث بالاسم أو الكود أو نوع الإجازة..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`w-full pr-10 pl-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  isDarkMode 
                    ? 'bg-slate-800 border-slate-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <Clock className="w-8 h-8 text-orange-500 mr-3" />
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>قيد المراجعة</p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {filteredRequests.length}
                </p>
              </div>
            </div>
          </div>
          
          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <Calendar className="w-8 h-8 text-blue-500 mr-3" />
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>إجمالي الأيام</p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {filteredRequests.reduce((sum, req) => sum + (req.DaysCount || 0), 0)}
                </p>
              </div>
            </div>
          </div>
          
          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <User className="w-8 h-8 text-green-500 mr-3" />
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>موظفين مختلفين</p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {new Set(filteredRequests.map(req => req.EmployeeCode)).size}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* جدول الطلبات */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border overflow-hidden`}>
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <RefreshCw className="w-6 h-6 animate-spin text-blue-500" />
              <span className="mr-2">جاري التحميل...</span>
            </div>
          ) : filteredRequests.length === 0 ? (
            <div className="text-center py-12">
              <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className={`text-lg ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                لا توجد طلبات قيد المراجعة
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className={`${isDarkMode ? 'bg-slate-800' : 'bg-gray-50'}`}>
                  <tr>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      الموظف
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      نوع الإجازة
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      التواريخ
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      عدد الأيام
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      تاريخ التقديم
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} divide-y ${isDarkMode ? 'divide-slate-700' : 'divide-gray-200'}`}>
                  {filteredRequests.map((request) => (
                    <tr key={request.ID} className={`hover:${isDarkMode ? 'bg-slate-800' : 'bg-gray-50'} transition-colors`}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                            {request.EmployeeName}
                          </div>
                          <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>
                            {request.EmployeeCode}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          request.LeaveType === 'اعتيادية' 
                            ? 'bg-blue-100 text-blue-800' 
                            : request.LeaveType === 'مرضية'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {request.LeaveType}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <div className={isDarkMode ? 'text-slate-300' : 'text-gray-900'}>
                          {request.StartDate && new Date(request.StartDate).toLocaleDateString('ar-EG')}
                          {request.EndDate && (
                            <> - {new Date(request.EndDate).toLocaleDateString('ar-EG')}</>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          {request.DaysCount} يوم
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <div className={isDarkMode ? 'text-slate-300' : 'text-gray-900'}>
                          {request.RequestDate && new Date(request.RequestDate).toLocaleDateString('ar-EG')}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => openActionModal(request, 'approve')}
                            className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs flex items-center gap-1"
                            title="اعتماد"
                          >
                            <Check className="w-3 h-3" />
                            اعتماد
                          </button>
                          <button
                            onClick={() => openActionModal(request, 'reject')}
                            className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs flex items-center gap-1"
                            title="رفض"
                          >
                            <X className="w-3 h-3" />
                            رفض
                          </button>
                          <button
                            onClick={() => openActionModal(request, 'delete')}
                            className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-xs flex items-center gap-1"
                            title="حذف"
                          >
                            <Trash2 className="w-3 h-3" />
                            حذف
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* نافذة تأكيد الإجراء */}
        {showModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg p-6 w-full max-w-md mx-4`}>
              <h3 className={`text-lg font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {actionType === 'approve' ? 'اعتماد الطلب' : 
                 actionType === 'reject' ? 'رفض الطلب' : 'حذف الطلب'}
              </h3>
              
              {selectedRequest && (
                <div className={`mb-4 p-3 rounded ${isDarkMode ? 'bg-slate-800' : 'bg-gray-100'}`}>
                  <p className={`text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                    <strong>الموظف:</strong> {selectedRequest.EmployeeName} ({selectedRequest.EmployeeCode})
                  </p>
                  <p className={`text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                    <strong>نوع الإجازة:</strong> {selectedRequest.LeaveType}
                  </p>
                  <p className={`text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                    <strong>المدة:</strong> {selectedRequest.DaysCount} يوم
                  </p>
                </div>
              )}

              {actionType !== 'delete' && (
                <div className="mb-4">
                  <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-slate-300' : 'text-gray-700'}`}>
                    ملاحظات (اختيارية)
                  </label>
                  <textarea
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    rows={3}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      isDarkMode
                        ? 'bg-slate-800 border-slate-600 text-white'
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                    placeholder="أدخل أي ملاحظات..."
                  />
                </div>
              )}

              <div className="flex justify-end gap-3">
                <button
                  onClick={() => setShowModal(false)}
                  className={`px-4 py-2 rounded-lg ${
                    isDarkMode 
                      ? 'bg-slate-600 hover:bg-slate-700 text-white' 
                      : 'bg-gray-300 hover:bg-gray-400 text-gray-700'
                  }`}
                >
                  إلغاء
                </button>
                <button
                  onClick={executeAction}
                  className={`px-4 py-2 rounded-lg text-white ${
                    actionType === 'approve' 
                      ? 'bg-green-600 hover:bg-green-700' 
                      : actionType === 'reject'
                      ? 'bg-red-600 hover:bg-red-700'
                      : 'bg-gray-600 hover:bg-gray-700'
                  }`}
                >
                  {actionType === 'approve' ? 'اعتماد' : 
                   actionType === 'reject' ? 'رفض' : 'حذف'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
