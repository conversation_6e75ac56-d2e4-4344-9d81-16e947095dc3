'use client';

import MainLayout from '@/components/MainLayout';
import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function VersionRequestsPage() {
  const searchParams = useSearchParams();
  const typeFilter = searchParams.get('type');

  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedType, setSelectedType] = useState(typeFilter || '');
  const [selectedMonth, setSelectedMonth] = useState('');
  const [lastUpdate, setLastUpdate] = useState(null);
  const [updateNotification, setUpdateNotification] = useState('');
  const [mounted, setMounted] = useState(false);
  const [selectedYear, setSelectedYear] = useState('');
  const [error, setError] = useState('');

  // أنواع التكاليف
  const costTypes = [
    { value: '', label: 'جميع الأنواع' },
    { value: 'cars', label: 'تكاليف السيارات' },
    { value: 'apartments', label: 'تكاليف الشقق' },
    { value: 'apartments-annex', label: 'ملاحق تكاليف الشقق' },
    { value: 'temp-workers', label: 'تكاليف العمالة المؤقتة' }
  ];

  // الشهور
  const months = [
    { value: '', label: 'جميع الشهور' },
    { value: '1', label: 'يناير' },
    { value: '2', label: 'فبراير' },
    { value: '3', label: 'مارس' },
    { value: '4', label: 'أبريل' },
    { value: '5', label: 'مايو' },
    { value: '6', label: 'يونيو' },
    { value: '7', label: 'يوليو' },
    { value: '8', label: 'أغسطس' },
    { value: '9', label: 'سبتمبر' },
    { value: '10', label: 'أكتوبر' },
    { value: '11', label: 'نوفمبر' },
    { value: '12', label: 'ديسمبر' }
  ];

  // بيانات تجريبية لطلبات الإصدار
  const mockRequests = [
    {
      id: 1,
      type: 'cars',
      typeName: 'تكاليف السيارات',
      month: 1,
      year: 2024,
      fileName: '1-2024.pdf',
      uploadDate: '2024-01-31',
      fileSize: '2.5 MB',
      status: 'مرفوع'
    },
    {
      id: 2,
      type: 'apartments',
      typeName: 'تكاليف الشقق',
      month: 1,
      year: 2024,
      fileName: '1-2024.pdf',
      uploadDate: '2024-01-31',
      fileSize: '1.8 MB',
      status: 'مرفوع'
    },
    {
      id: 3,
      type: 'temp-workers',
      typeName: 'تكاليف العمالة المؤقتة',
      month: 1,
      year: 2024,
      fileName: '1-2024.pdf',
      uploadDate: '2024-01-31',
      fileSize: '3.2 MB',
      status: 'مرفوع'
    },
    {
      id: 4,
      type: 'cars',
      typeName: 'تكاليف السيارات',
      month: 2,
      year: 2024,
      fileName: '2-2024.pdf',
      uploadDate: '2024-02-29',
      fileSize: '2.1 MB',
      status: 'مرفوع'
    },
    {
      id: 5,
      type: 'apartments',
      typeName: 'تكاليف الشقق',
      month: 2,
      year: 2024,
      fileName: '2-2024.pdf',
      uploadDate: '2024-02-29',
      fileSize: '1.9 MB',
      status: 'مرفوع'
    }
  ];

  useEffect(() => {
    setMounted(true);
    setLastUpdate(new Date());
    fetchVersionRequests();

    // تحديث تلقائي كل دقيقتين لتقليل الأخطاء
    const interval = setInterval(() => {
      fetchVersionRequests(false); // بدون loading indicator للتحديث التلقائي
    }, 120000); // 2 دقيقة

    return () => clearInterval(interval);
  }, []);

  // جلب طلبات الإصدار من API
  const fetchVersionRequests = async (showLoadingIndicator = true) => {
    if (showLoadingIndicator) {
      setLoading(true);
    }

    // إعادة تعيين رسالة الخطأ
    setError('');

    try {
      // إضافة timestamp لتجنب cache
      const timestamp = new Date().getTime();
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 15000); // timeout بعد 15 ثانية

      const response = await fetch(`/api/version-requests?_t=${timestamp}`, {
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        const previousCount = requests.length;
        const newCount = result.data.length;

        console.log('📡 البيانات المستلمة من API:', {
          total: newCount,
          cars: result.data.filter(r => r.type === 'cars').length,
          apartments: result.data.filter(r => r.type === 'apartments').length,
          tempWorkers: result.data.filter(r => r.type === 'temp-workers').length,
          firstFew: result.data.slice(0, 5).map(r => r.fileName)
        });

        setRequests(result.data);
        setLastUpdate(new Date());

        // إشعار التحديث
        if (newCount !== previousCount) {
          setUpdateNotification(`تم العثور على ${newCount - previousCount} طلب جديد!`);
          setTimeout(() => setUpdateNotification(''), 5000);
        } else if (showLoadingIndicator) {
          setUpdateNotification('تم تحديث البيانات بنجاح');
          setTimeout(() => setUpdateNotification(''), 3000);
        }

        console.log('✅ تم تحديث طلبات الإصدار:', result.data.length, 'طلب');
      } else {
        console.error('❌ خطأ في جلب طلبات الإصدار:', result.error);
        // استخدام البيانات التجريبية في حالة الخطأ
        setRequests(mockRequests);
      }
    } catch (error) {
      console.error('❌ خطأ في الاتصال:', error);

      if (error.name === 'AbortError') {
        setError('انتهت مهلة الاتصال - يرجى المحاولة مرة أخرى');
        console.log('⏰ انتهت مهلة الاتصال');
      } else if (error.message.includes('Failed to fetch')) {
        setError('فشل في الاتصال بالخادم - تحقق من الاتصال');
        console.log('🌐 مشكلة في الشبكة');
      } else {
        setError(`خطأ في جلب البيانات: ${error.message}`);
        console.log('❌ خطأ عام:', error.message);
      }

      // عدم استخدام البيانات التجريبية إذا كان لدينا بيانات حقيقية
      if (requests.length === 0) {
        console.log('📋 استخدام البيانات التجريبية كبديل');
        setRequests(mockRequests);
      } else {
        console.log('📋 الاحتفاظ بالبيانات الحالية:', requests.length, 'طلب');
      }
    } finally {
      if (showLoadingIndicator) {
        setLoading(false);
      }
    }
  };

  // فلترة الطلبات
  const filteredRequests = requests.filter(request => {
    if (selectedType && request.type !== selectedType) return false;
    if (selectedMonth && request.month.toString() !== selectedMonth) return false;
    if (selectedYear && request.year.toString() !== selectedYear) return false;
    return true;
  });

  // logging للتحقق من الفلاتر
  useEffect(() => {
    if (mounted && requests.length > 0) {
      console.log('🔍 حالة الفلاتر:', {
        selectedType,
        selectedMonth,
        selectedYear,
        totalRequests: requests.length,
        filteredRequests: filteredRequests.length,
        filters: {
          type: selectedType || 'الكل',
          month: selectedMonth || 'الكل',
          year: selectedYear || 'الكل'
        }
      });
    }
  }, [mounted, requests.length, filteredRequests.length, selectedType, selectedMonth, selectedYear]);

  // عرض الملف
  const viewFile = (fileName, type) => {
    // استخدام النوع كما هو (لا نحتاج تحويل بعد الآن)
    const fileUrl = `/api/view-file?file=${encodeURIComponent(fileName)}&type=${type}`;
    window.open(fileUrl, '_blank');
  };

  // تحميل الملف
  const downloadFile = (fileName, type) => {
    // استخدام النوع كما هو (لا نحتاج تحويل بعد الآن)
    const fileUrl = `/api/view-file?file=${encodeURIComponent(fileName)}&type=${type}`;
    const link = document.createElement('a');
    link.href = fileUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // حذف الملف
  const deleteFile = async (id, fileName, type) => {
    if (confirm(`هل أنت متأكد من حذف الملف: ${fileName}؟`)) {
      try {
        const response = await fetch(`/api/version-requests?fileName=${fileName}&type=${type}`, {
          method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
          // إزالة الملف من القائمة
          setRequests(prev => prev.filter(req => req.id !== id));
          alert('تم حذف الملف بنجاح');
        } else {
          alert('خطأ في حذف الملف: ' + result.error);
        }
      } catch (error) {

        alert('خطأ في الاتصال بالخادم');
      }
    }
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto">
        {/* رأس الصفحة */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="text-3xl text-teal-600">📋</div>
              <div>
                <h1 className="text-3xl font-bold text-gray-800">
                  طلبات الإصدار المسجلة
                  <span className="text-lg text-blue-600 font-normal">
                    ({mounted ? filteredRequests.length : 0}
                    {mounted && requests.length !== filteredRequests.length &&
                      ` من أصل ${requests.length}`
                    })
                  </span>
                </h1>
                <p className="text-gray-600">
                  عرض وإدارة جميع طلبات الإصدار المرفوعة
                  {mounted && requests.length !== filteredRequests.length && (
                    <span className="text-orange-600 font-medium"> - مطبق فلاتر</span>
                  )}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <div className="text-sm text-gray-500">
                آخر تحديث: {mounted && lastUpdate ? lastUpdate.toLocaleTimeString('ar-EG') : '--:--:--'}
              </div>
              <button
                onClick={() => fetchVersionRequests(true)}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    جاري التحديث...
                  </>
                ) : (
                  <>
                    🔄 تحديث البيانات
                  </>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* إشعار التحديث */}
        {updateNotification && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6 flex items-center gap-2">
            <span className="text-green-600">✅</span>
            {updateNotification}
          </div>
        )}

        {/* رسالة الخطأ */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6 flex items-center gap-2">
            <span className="text-red-600">❌</span>
            {error}
            <button
              onClick={() => setError('')}
              className="ml-auto text-red-600 hover:text-red-800"
            >
              ✕
            </button>
          </div>
        )}

        {/* فلاتر البحث */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">فلاتر البحث</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">نوع التكلفة</label>
              <select
                value={selectedType}
                onChange={(e) => setSelectedType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-teal-500 focus:border-teal-500"
              >
                {costTypes.map(type => (
                  <option key={type.value} value={type.value}>{type.label}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">الشهر</label>
              <select
                value={selectedMonth}
                onChange={(e) => setSelectedMonth(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-teal-500 focus:border-teal-500"
              >
                {months.map(month => (
                  <option key={month.value} value={month.value}>{month.label}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">السنة</label>
              <select
                value={selectedYear}
                onChange={(e) => setSelectedYear(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-teal-500 focus:border-teal-500"
              >
                <option value="">جميع السنوات</option>
                {[2023, 2024, 2025, 2026].map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
            </div>

            <div className="flex items-end gap-2">
              <button
                onClick={() => {
                  setSelectedType('');
                  setSelectedMonth('');
                  setSelectedYear('');
                  console.log('🔄 تم إعادة تعيين جميع الفلاتر');
                }}
                className="flex-1 bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors"
              >
                مسح الفلاتر
              </button>
              <button
                onClick={() => {
                  fetchVersionRequests(true);
                  console.log('🔄 تحديث يدوي للبيانات');
                }}
                disabled={loading}
                className="flex-1 bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 disabled:bg-blue-300 transition-colors"
              >
                {loading ? 'جاري التحديث...' : 'تحديث البيانات'}
              </button>
            </div>
          </div>
        </div>

        {/* الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
          <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-teal-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي الطلبات</p>
                <p className="text-3xl font-bold text-teal-600">{mounted ? filteredRequests.length : 0}</p>
              </div>
              <div className="text-2xl text-teal-600">📋</div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-orange-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">تكاليف السيارات</p>
                <p className="text-2xl font-bold text-orange-600">
                  {mounted ? filteredRequests.filter(r => r.type === 'cars').length : 0}
                </p>
              </div>
              <div className="text-2xl text-orange-600">🚗</div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-purple-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">تكاليف الشقق</p>
                <p className="text-2xl font-bold text-purple-600">
                  {mounted ? filteredRequests.filter(r => r.type === 'apartments').length : 0}
                </p>
              </div>
              <div className="text-2xl text-purple-600">🏢</div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-indigo-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">ملاحق الشقق</p>
                <p className="text-2xl font-bold text-indigo-600">
                  {mounted ? filteredRequests.filter(r => r.type === 'apartments-annex').length : 0}
                </p>
              </div>
              <div className="text-2xl text-indigo-600">📎</div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-red-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">تكاليف العمالة</p>
                <p className="text-2xl font-bold text-red-600">
                  {mounted ? filteredRequests.filter(r => r.type === 'temp-workers').length : 0}
                </p>
              </div>
              <div className="text-2xl text-red-600">👷</div>
            </div>
          </div>
        </div>

        {/* جدول طلبات الإصدار */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-800">طلبات الإصدار</h3>
          </div>

          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">جاري تحميل البيانات...</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      نوع التكلفة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الشهر/السنة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      اسم الملف
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      حجم الملف
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      تاريخ الرفع
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الحالة
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {!mounted ? (
                    <tr>
                      <td colSpan="7" className="px-6 py-12 text-center text-gray-500">
                        جاري تحميل البيانات...
                      </td>
                    </tr>
                  ) : filteredRequests.length === 0 ? (
                    <tr>
                      <td colSpan="7" className="px-6 py-12 text-center text-gray-500">
                        لا توجد طلبات إصدار مطابقة للفلاتر المحددة
                      </td>
                    </tr>
                  ) : (
                    filteredRequests.map((request) => (
                      <tr key={request.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            request.type === 'cars' ? 'bg-orange-100 text-orange-800' :
                            request.type === 'apartments' ? 'bg-purple-100 text-purple-800' :
                            request.type === 'apartments-annex' ? 'bg-indigo-100 text-indigo-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {request.typeName}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {months.find(m => m.value === request.month.toString())?.label} {request.year}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {request.fileName}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {request.fileSize}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(request.uploadDate).toLocaleDateString('ar-EG')}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                            {request.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex gap-2">
                            <button
                              onClick={() => downloadFile(request.fileName, request.type)}
                              className="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700 flex items-center gap-1"
                              title="تحميل الملف"
                            >
                              📥 تحميل
                            </button>
                            <button
                              onClick={() => viewFile(request.fileName, request.type)}
                              className="bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700 flex items-center gap-1"
                              title="عرض الملف"
                            >
                              👁️ عرض
                            </button>
                            <button
                              onClick={() => deleteFile(request.id, request.fileName, request.type)}
                              className="bg-red-600 text-white px-3 py-1 rounded text-xs hover:bg-red-700 flex items-center gap-1"
                              title="حذف الملف"
                            >
                              🗑️ حذف
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
