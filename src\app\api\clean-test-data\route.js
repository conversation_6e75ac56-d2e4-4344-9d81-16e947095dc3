import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function POST(request) {
  let pool;
  
  try {
    const { action } = await request.json();
    
    if (action !== 'cleanTestData') {
      return NextResponse.json({
        success: false,
        error: 'إجراء غير صحيح'
      }, { status: 400 });
    }

    pool = await getConnection();
    
    // حذف البيانات التجريبية فقط
    const deleteResult = await pool.request().query(`
      DELETE FROM MonthlyCosts 
      WHERE CreatedBy = 'سكريبت البيانات التجريبية'
    `);
    
    const deletedCount = deleteResult.rowsAffected[0] || 0;
    
    return NextResponse.json({
      success: true,
      message: `تم حذف ${deletedCount} سجل من البيانات التجريبية بنجاح`,
      deletedCount: deletedCount
    });
    
  } catch (error) {
    console.error('خطأ في تنظيف البيانات:', error);
    return NextResponse.json({
      success: false,
      error: 'فشل في تنظيف البيانات: ' + error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      await pool.close();
    }
  }
}
