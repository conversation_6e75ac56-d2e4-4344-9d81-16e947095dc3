// نظام حماية للنسخة المباعة فقط - لا يؤثر على النسخة الأصلية
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class ProductionOnlyProtection {
  constructor() {
    // التحقق من أن هذه نسخة إنتاج (مباعة)
    this.isProduction = this.checkIfProduction();
    this.licenseFile = path.join(process.cwd(), '.license');
    this.configFile = path.join(process.cwd(), '.production-config');
    
    if (this.isProduction) {
      this.initProductionProtection();
    }
  }

  // التحقق من أن هذه نسخة إنتاج
  checkIfProduction() {
    // البحث عن علامات النسخة المباعة
    const productionMarkers = [
      '.license',           // ملف الترخيص
      '.production-config', // ملف تكوين الإنتاج
      'customer-info.json'  // معلومات العميل
    ];
    
    return productionMarkers.some(marker => 
      fs.existsSync(path.join(process.cwd(), marker))
    );
  }

  // تهيئة حماية الإنتاج
  initProductionProtection() {
    console.log('🔐 تم اكتشاف نسخة إنتاج - تفعيل الحماية...');
    
    try {
      // التحقق من الترخيص
      const licenseValid = this.validateLicense();
      
      if (!licenseValid.valid) {
        this.handleInvalidLicense(licenseValid.error);
        return;
      }
      
      console.log(`✅ ترخيص صالح للعميل: ${licenseValid.data.customerName}`);
      
      // بدء المراقبة
      this.startProductionMonitoring(licenseValid.data);
      
    } catch (error) {
      console.error('❌ خطأ في تهيئة الحماية:', error.message);
      this.handleProtectionError(error);
    }
  }

  // التحقق من صحة الترخيص
  validateLicense() {
    try {
      if (!fs.existsSync(this.licenseFile)) {
        return { valid: false, error: 'ملف الترخيص غير موجود' };
      }

      const licenseContent = fs.readFileSync(this.licenseFile, 'utf8');
      const { license, signature } = JSON.parse(licenseContent);
      
      // فك تشفير الترخيص
      const licenseString = Buffer.from(license, 'base64').toString('utf8');
      const licenseData = JSON.parse(licenseString);
      
      // التحقق من التوقيع
      const expectedSignature = this.signLicense(licenseString);
      if (signature !== expectedSignature) {
        return { valid: false, error: 'توقيع الترخيص غير صالح' };
      }
      
      // التحقق من تاريخ الانتهاء
      if (licenseData.expiryDate) {
        const expiryDate = new Date(licenseData.expiryDate);
        if (new Date() > expiryDate) {
          return { valid: false, error: 'انتهت صلاحية الترخيص' };
        }
      }
      
      return { valid: true, data: licenseData };
      
    } catch (error) {
      return { valid: false, error: `خطأ في قراءة الترخيص: ${error.message}` };
    }
  }

  // توقيع الترخيص
  signLicense(licenseString) {
    const secretKey = 'protection_key_2025'; // نفس المفتاح المستخدم في الإنشاء
    const hmac = crypto.createHmac('sha256', secretKey);
    hmac.update(licenseString);
    return hmac.digest('hex');
  }

  // التعامل مع ترخيص غير صالح
  handleInvalidLicense(error) {
    console.error('🚫 ترخيص غير صالح:', error);
    
    // إنشاء ملف تحذير
    const warningFile = path.join(process.cwd(), 'LICENSE-WARNING.txt');
    const warningContent = `
⚠️  تحذير: مشكلة في الترخيص

السبب: ${error}
التاريخ: ${new Date().toLocaleString('ar-EG')}

للحصول على المساعدة:
📧 البريد الإلكتروني: <EMAIL>
📞 الهاتف: +20 123 456 7890

هذا النظام محمي بحقوق الطبع والنشر.
الاستخدام غير المصرح به مخالف للقانون.
`;
    
    fs.writeFileSync(warningFile, warningContent);
    
    // تسجيل المحاولة
    this.logUnauthorizedAccess(error);
    
    // إيقاف النظام بعد 30 ثانية (إعطاء وقت لقراءة الرسالة)
    setTimeout(() => {
      console.log('🛑 إيقاف النظام بسبب مشكلة في الترخيص');
      process.exit(1);
    }, 30000);
  }

  // بدء مراقبة الإنتاج
  startProductionMonitoring(licenseData) {
    // مراقبة كل ساعة
    setInterval(() => {
      this.performProductionCheck(licenseData);
    }, 60 * 60 * 1000);
    
    // فحص أولي بعد 5 دقائق
    setTimeout(() => {
      this.performProductionCheck(licenseData);
    }, 5 * 60 * 1000);
  }

  // فحص دوري للإنتاج
  performProductionCheck(licenseData) {
    try {
      // إعادة التحقق من الترخيص
      const validation = this.validateLicense();
      
      if (!validation.valid) {
        this.handleInvalidLicense(validation.error);
        return;
      }
      
      // تسجيل الاستخدام
      this.logUsage(licenseData);
      
      // فحص التلاعب
      this.checkForTampering();
      
    } catch (error) {
      console.error('خطأ في الفحص الدوري:', error.message);
    }
  }

  // تسجيل الاستخدام
  logUsage(licenseData) {
    const usageLog = {
      timestamp: new Date().toISOString(),
      customerId: licenseData.customerId,
      customerName: licenseData.customerName,
      systemInfo: {
        platform: process.platform,
        nodeVersion: process.version,
        memory: Math.round(process.memoryUsage().heapUsed / 1024 / 1024) + 'MB'
      }
    };
    
    const logFile = path.join(process.cwd(), 'logs', 'usage.log');
    const logDir = path.dirname(logFile);
    
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    
    fs.appendFileSync(logFile, JSON.stringify(usageLog) + '\n');
  }

  // فحص التلاعب
  checkForTampering() {
    const criticalFiles = [
      '.license',
      'customer-info.json',
      '.production-config'
    ];
    
    for (const file of criticalFiles) {
      const filePath = path.join(process.cwd(), file);
      
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath);
        const lastModified = stats.mtime.getTime();
        const now = Date.now();
        
        // إذا تم تعديل الملف في آخر دقيقة
        if (now - lastModified < 60000) {
          this.logSuspiciousActivity(`تم تعديل ملف حساس: ${file}`);
        }
      }
    }
  }

  // تسجيل النشاط المشبوه
  logSuspiciousActivity(activity) {
    const suspiciousLog = {
      timestamp: new Date().toISOString(),
      activity: activity,
      systemInfo: {
        platform: process.platform,
        pid: process.pid
      }
    };
    
    const logFile = path.join(process.cwd(), 'logs', 'suspicious.log');
    const logDir = path.dirname(logFile);
    
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    
    fs.appendFileSync(logFile, JSON.stringify(suspiciousLog) + '\n');
    console.warn('⚠️  نشاط مشبوه:', activity);
  }

  // تسجيل الوصول غير المصرح
  logUnauthorizedAccess(error) {
    const accessLog = {
      timestamp: new Date().toISOString(),
      error: error,
      attempt: 'unauthorized_access',
      systemInfo: {
        platform: process.platform,
        nodeVersion: process.version
      }
    };
    
    const logFile = path.join(process.cwd(), 'logs', 'unauthorized.log');
    const logDir = path.dirname(logFile);
    
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    
    fs.appendFileSync(logFile, JSON.stringify(accessLog) + '\n');
  }

  // التعامل مع خطأ الحماية
  handleProtectionError(error) {
    console.error('خطأ في نظام الحماية:', error.message);
    
    // في حالة الخطأ، السماح بالعمل لتجنب كسر النظام
    console.log('⚠️  تم تعطيل الحماية مؤقتاً بسبب خطأ');
  }

  // الحصول على معلومات الترخيص
  getLicenseInfo() {
    if (!this.isProduction) {
      return { type: 'development', protection: 'disabled' };
    }
    
    try {
      const validation = this.validateLicense();
      
      if (validation.valid) {
        return {
          type: 'production',
          protection: 'active',
          customer: validation.data.customerName,
          customerId: validation.data.customerId,
          expiryDate: validation.data.expiryDate
        };
      } else {
        return {
          type: 'production',
          protection: 'invalid',
          error: validation.error
        };
      }
    } catch (error) {
      return {
        type: 'production',
        protection: 'error',
        error: error.message
      };
    }
  }
}

// تصدير الكلاس
module.exports = ProductionOnlyProtection;

// تشغيل تلقائي
if (require.main === module) {
  const protection = new ProductionOnlyProtection();
  console.log('معلومات الترخيص:', protection.getLicenseInfo());
}
