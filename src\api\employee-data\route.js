async function handler({ action, data, employeeId }) {
  try {
    switch (action) {
      case 'create': {
        const columns = Object.keys(data);
        const values = Object.values(data);
        const placeholders = columns.map((_, i) => `$${i + 1}`).join(', ');

        const query = `
          INSERT INTO Employees (${columns.join(', ')})
          VALUES (${placeholders})
          RETURNING *
        `;

        const result = await sql(query, values);
        return { success: true, data: result[0] };
      }

      case 'update': {
        const setClauses = [];
        const queryParams = [];

        Object.entries(data).forEach(([key, value], index) => {
          setClauses.push(`${key} = $${index + 1}`);
          queryParams.push(value);
        });

        queryParams.push(employeeId);

        const query = `
          UPDATE Employees
          SET ${setClauses.join(', ')}
          WHERE EmployeeCode = $${queryParams.length}
          RETURNING *
        `;

        const result = await sql(query, queryParams);
        return { success: true, data: result[0] };
      }

      case 'get': {
        const result = await sql(
          'SELECT * FROM Employees WHERE EmployeeCode = $1',
          [employeeId]
        );
        return { success: true, data: result[0] };
      }

      case 'list': {
        const whereConditions = [];
        const queryParams = [];
        let paramCount = 1;

        const selectFields = `
          employee_id,
          code,
          employee_name,
          TO_CHAR(birth_date, 'DD/MM/YYYY') as birth_date,
          education_level,
          department,
          job_title,
          area,
          bus_line,
          status,
          phone_number,
          national_id
        `;

        if (data?.search) {
          whereConditions.push(`
            (code ILIKE $${paramCount} 
            OR employee_name ILIKE $${paramCount} 
            OR phone_number ILIKE $${paramCount}
            OR national_id ILIKE $${paramCount})
          `);
          queryParams.push(`%${data.search}%`);
          paramCount++;
        }

        if (data?.filters) {
          const filterFields = [
            'department',
            'area',
            'status',
            'education_level',
            'military_service',
            'marital_status',
          ];
          filterFields.forEach((field) => {
            if (data.filters[field]) {
              whereConditions.push(`${field} = $${paramCount}`);
              queryParams.push(data.filters[field]);
              paramCount++;
            }
          });
        }

        const whereClause =
          whereConditions.length > 0
            ? 'WHERE ' + whereConditions.join(' AND ')
            : '';

        const query = `
          SELECT ${selectFields}
          FROM monthly_attendance
          ${whereClause}
          ORDER BY code`;

        const employees = await sql(query, queryParams);
        return { success: true, data: employees };
      }

      case 'delete': {
        const result = await sql(
          'DELETE FROM monthly_attendance WHERE employee_id = $1 RETURNING *',
          [employeeId]
        );
        return { success: true, data: result[0] };
      }

      default:
        throw new Error('Invalid action');
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
}
