'use client';

import React, { useState, useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { useLanguage } from '@/contexts/LanguageContext';
import MainLayout from '@/components/MainLayout';
import { 
  Clock, 
  Download, 
  Filter, 
  Search, 
  Calendar, 
  FileText, 
  BarChart3, 
  Eye, 
  Printer,
  RefreshCw,
  UserCheck,
  UserX,
  AlertCircle,
  TrendingUp,
  Users,
  CheckCircle,
  XCircle,
  Pause
} from 'lucide-react';

export default function AttendanceReportsPage() {
  const { isDarkMode } = useTheme();
  const { isRTL, isArabic } = useLanguage();
  
  const [loading, setLoading] = useState(false);
  const [selectedReport, setSelectedReport] = useState('daily');
  const [filters, setFilters] = useState({
    department: '',
    employee: '',
    dateFrom: '',
    dateTo: '',
    attendanceType: 'all',
    searchTerm: ''
  });
  const [reportData, setReportData] = useState([]);
  const [reportStats, setReportStats] = useState({});

  // أنواع تقارير الحضور
  const attendanceReports = [
    {
      id: 'daily',
      title: 'تقرير الحضور اليومي',
      description: 'تقرير تفصيلي للحضور والغياب اليومي',
      icon: Clock,
      color: 'blue'
    },
    {
      id: 'monthly',
      title: 'تقرير الحضور الشهري',
      description: 'ملخص الحضور والغياب الشهري',
      icon: Calendar,
      color: 'green'
    },
    {
      id: 'monthly-effects',
      title: 'تقرير المؤثرات الشهرية',
      description: 'تقرير المؤثرات على الراتب الشهري',
      icon: BarChart3,
      color: 'purple'
    },
    {
      id: 'delays',
      title: 'تقرير التأخير والانصراف',
      description: 'تقرير حالات التأخير والانصراف المبكر',
      icon: AlertCircle,
      color: 'orange'
    },
    {
      id: 'absences',
      title: 'تقرير الغياب',
      description: 'تقرير تفصيلي لحالات الغياب',
      icon: UserX,
      color: 'red'
    },
    {
      id: 'statistics',
      title: 'إحصائيات الحضور',
      description: 'إحصائيات شاملة للحضور والغياب',
      icon: TrendingUp,
      color: 'indigo'
    }
  ];

  // رموز الحضور
  const attendanceCodes = {
    'W': { ar: 'عمل', color: 'green', icon: CheckCircle },
    'Ab': { ar: 'غياب', color: 'red', icon: XCircle },
    'S': { ar: 'مرضى', color: 'yellow', icon: Pause },
    'R': { ar: 'راحة', color: 'blue', icon: Pause },
    'NH': { ar: 'إجازة رسمية', color: 'purple', icon: Pause },
    'CR': { ar: 'بدل راحة', color: 'indigo', icon: Pause },
    'M': { ar: 'مأمورية', color: 'orange', icon: CheckCircle },
    'AL': { ar: 'إجازة اعتيادية', color: 'teal', icon: Pause },
    'CL': { ar: 'إجازة عارضة', color: 'pink', icon: Pause },
    'UL': { ar: 'إجازة بدون أجر', color: 'gray', icon: Pause },
    'ML': { ar: 'إجازة وضع', color: 'rose', icon: Pause }
  };

  // تحميل بيانات التقرير
  useEffect(() => {
    loadReportData();
  }, [selectedReport, filters]);

  const loadReportData = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/attendance-reports', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reportType: selectedReport,
          filters: filters
        })
      });

      if (response.ok) {
        const data = await response.json();
        setReportData(data.data || []);
        setReportStats(data.stats || {});
      } else {

        // بيانات تجريبية
        setReportData(generateMockAttendanceData());
        setReportStats(generateMockAttendanceStats());
      }
    } catch (error) {

      // بيانات تجريبية
      setReportData(generateMockAttendanceData());
      setReportStats(generateMockAttendanceStats());
    } finally {
      setLoading(false);
    }
  };

  // إنشاء بيانات تجريبية للحضور
  const generateMockAttendanceData = () => {
    const mockData = [];
    const employees = ['أحمد محمد', 'فاطمة علي', 'محمد حسن', 'نور الدين', 'سارة أحمد'];
    const departments = ['الإدارة العامة', 'الموارد البشرية', 'المالية', 'التقنية'];
    const codes = Object.keys(attendanceCodes);
    
    for (let i = 1; i <= 30; i++) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      
      employees.forEach((emp, empIndex) => {
        const code = codes[Math.floor(Math.random() * codes.length)];
        mockData.push({
          id: i * 10 + empIndex,
          employeeCode: `EMP${String(empIndex + 1).padStart(3, '0')}`,
          employeeName: emp,
          department: departments[empIndex % departments.length],
          date: date.toISOString().split('T')[0],
          attendanceCode: code,
          attendanceStatus: attendanceCodes[code].ar,
          checkIn: code === 'W' ? '08:30' : null,
          checkOut: code === 'W' ? '17:00' : null,
          workingHours: code === 'W' ? 8.5 : 0,
          overtime: Math.random() > 0.8 ? Math.floor(Math.random() * 3) : 0,
          notes: Math.random() > 0.7 ? 'ملاحظة تجريبية' : ''
        });
      });
    }
    
    return mockData;
  };

  // إنشاء إحصائيات تجريبية للحضور
  const generateMockAttendanceStats = () => {
    return {
      totalRecords: 150,
      workDays: 98,
      absentDays: 15,
      sickDays: 12,
      restDays: 20,
      leaveDays: 5,
      attendanceRate: 85.2,
      averageWorkingHours: 8.3,
      totalOvertime: 45,
      punctualityRate: 92.5
    };
  };

  // تصدير التقرير
  const exportReport = (format) => {
    alert(`جاري تصدير التقرير بصيغة ${format}...`);
  };

  // طباعة التقرير
  const printReport = () => {
    window.print();
  };

  // إعادة تعيين الفلاتر
  const resetFilters = () => {
    setFilters({
      department: '',
      employee: '',
      dateFrom: '',
      dateTo: '',
      attendanceType: 'all',
      searchTerm: ''
    });
  };

  const selectedReportInfo = attendanceReports.find(r => r.id === selectedReport);

  return (
    <MainLayout>
      <div className={`p-6 ${isDarkMode ? 'bg-[#0f172a] text-white' : 'bg-gray-50 text-gray-900'}`}>
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm p-6 mb-6`}>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <Clock className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-2xl font-bold">تقارير الحضور والغياب</h1>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  تقارير شاملة ومفصلة عن حضور وغياب الموظفين
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <button
                onClick={() => exportReport('Excel')}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <Download className="h-4 w-4" />
                Excel
              </button>
              
              <button
                onClick={() => exportReport('PDF')}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <FileText className="h-4 w-4" />
                PDF
              </button>
              
              <button
                onClick={printReport}
                className={`px-4 py-2 rounded-lg flex items-center gap-2 transition-colors ${
                  isDarkMode 
                    ? 'bg-gray-700 hover:bg-gray-600 text-white' 
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                }`}
              >
                <Printer className="h-4 w-4" />
                طباعة
              </button>
            </div>
          </div>
        </div>

        {/* اختيار نوع التقرير */}
        <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm p-6 mb-6`}>
          <h3 className="text-lg font-semibold mb-4">اختر نوع التقرير</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {attendanceReports.map((report) => {
              const IconComponent = report.icon;
              return (
                <button
                  key={report.id}
                  onClick={() => setSelectedReport(report.id)}
                  className={`p-4 rounded-lg border-2 transition-all ${
                    selectedReport === report.id
                      ? `border-${report.color}-500 bg-${report.color}-50 dark:bg-${report.color}-900/20`
                      : `border-gray-200 dark:border-gray-700 hover:border-${report.color}-300`
                  }`}
                >
                  <div className="flex flex-col items-center text-center gap-2">
                    <IconComponent className={`h-8 w-8 text-${report.color}-600`} />
                    <div>
                      <h4 className="font-medium text-sm">{report.title}</h4>
                      <p className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                        {report.description}
                      </p>
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        </div>

        {/* الفلاتر */}
        <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm p-6 mb-6`}>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">فلاتر التقرير</h3>
            <button
              onClick={resetFilters}
              className={`px-3 py-1 rounded text-sm transition-colors ${
                isDarkMode 
                  ? 'bg-gray-700 hover:bg-gray-600 text-white' 
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
              }`}
            >
              إعادة تعيين
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {/* البحث */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                البحث
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  value={filters.searchTerm}
                  onChange={(e) => setFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
                  placeholder="ابحث بالاسم أو الكود..."
                  className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                    isDarkMode 
                      ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400' 
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  }`}
                />
              </div>
            </div>

            {/* القسم */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                القسم
              </label>
              <select
                value={filters.department}
                onChange={(e) => setFilters(prev => ({ ...prev, department: e.target.value }))}
                className={`w-full p-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                  isDarkMode 
                    ? 'bg-gray-800 border-gray-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="">جميع الأقسام</option>
                <option value="الإدارة العامة">الإدارة العامة</option>
                <option value="الموارد البشرية">الموارد البشرية</option>
                <option value="المالية">المالية</option>
                <option value="التقنية">التقنية</option>
              </select>
            </div>

            {/* نوع الحضور */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                نوع الحضور
              </label>
              <select
                value={filters.attendanceType}
                onChange={(e) => setFilters(prev => ({ ...prev, attendanceType: e.target.value }))}
                className={`w-full p-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                  isDarkMode 
                    ? 'bg-gray-800 border-gray-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="all">جميع الأنواع</option>
                <option value="W">عمل</option>
                <option value="Ab">غياب</option>
                <option value="S">مرضى</option>
                <option value="R">راحة</option>
                <option value="AL">إجازة</option>
              </select>
            </div>

            {/* الموظف */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                الموظف
              </label>
              <select
                value={filters.employee}
                onChange={(e) => setFilters(prev => ({ ...prev, employee: e.target.value }))}
                className={`w-full p-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                  isDarkMode 
                    ? 'bg-gray-800 border-gray-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="">جميع الموظفين</option>
                <option value="EMP001">أحمد محمد</option>
                <option value="EMP002">فاطمة علي</option>
                <option value="EMP003">محمد حسن</option>
              </select>
            </div>

            {/* من تاريخ */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                من تاريخ
              </label>
              <input
                type="date"
                value={filters.dateFrom}
                onChange={(e) => setFilters(prev => ({ ...prev, dateFrom: e.target.value }))}
                className={`w-full p-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                  isDarkMode 
                    ? 'bg-gray-800 border-gray-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>

            {/* إلى تاريخ */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                إلى تاريخ
              </label>
              <input
                type="date"
                value={filters.dateTo}
                onChange={(e) => setFilters(prev => ({ ...prev, dateTo: e.target.value }))}
                className={`w-full p-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                  isDarkMode 
                    ? 'bg-gray-800 border-gray-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
