# دليل المساهمة

نرحب بمساهماتكم في تطوير نظام إدارة الموارد البشرية! يرجى قراءة هذا الدليل قبل المساهمة.

## 🚀 كيفية المساهمة

### 1. إعداد البيئة المحلية

```bash
# استنساخ المشروع
git clone [repository-url]
cd project-directory

# تثبيت التبعيات
npm install

# إعداد قاعدة البيانات
# تأكد من تشغيل SQL Server وإنشاء قاعدة البيانات

# تشغيل المشروع
npm run dev
```

### 2. معايير الكود

#### JavaScript/TypeScript
- استخدم ES6+ features
- اتبع معايير Prettier للتنسيق
- استخدم ESLint للتحقق من جودة الكود
- اكتب تعليقات واضحة باللغة العربية للوظائف المهمة

#### React Components
- استخدم Functional Components مع Hooks
- اتبع نمط تسمية PascalCase للمكونات
- ضع المكونات المشتركة في مجلد `src/components`
- استخدم TypeScript للمكونات الجديدة

#### CSS/Styling
- استخدم Tailwind CSS للتنسيق
- تجنب CSS مخصص إلا عند الضرورة
- اتبع نمط التصميم المتجاوب
- تأكد من دعم اللغة العربية (RTL)

### 3. هيكل المشروع

```
src/
├── app/                 # صفحات التطبيق (App Router)
│   ├── api/            # API endpoints
│   ├── dashboard/      # لوحة التحكم
│   ├── employees/      # إدارة الموظفين
│   └── ...
├── components/         # المكونات المشتركة
├── lib/               # مكتبات مساعدة
├── utils/             # دوال مساعدة
└── types/             # تعريفات TypeScript
```

### 4. قواعد Git

#### Commit Messages
استخدم الصيغة التالية للرسائل:

```
نوع: وصف مختصر

وصف مفصل (اختياري)

Closes #123
```

أنواع الـ commits:
- `feat`: ميزة جديدة
- `fix`: إصلاح خطأ
- `docs`: تحديث التوثيق
- `style`: تغييرات التنسيق
- `refactor`: إعادة هيكلة الكود
- `test`: إضافة أو تحديث الاختبارات
- `chore`: مهام صيانة

#### Branch Naming
- `feature/اسم-الميزة` للميزات الجديدة
- `fix/وصف-الإصلاح` للإصلاحات
- `docs/تحديث-التوثيق` للتوثيق
- `refactor/اسم-التحسين` لإعادة الهيكلة

### 5. عملية المراجعة

1. **Fork** المشروع
2. إنشاء **branch** جديد من `main`
3. تطبيق التغييرات مع اتباع المعايير
4. كتابة أو تحديث الاختبارات
5. تشغيل الاختبارات والتأكد من نجاحها
6. **Commit** التغييرات مع رسائل واضحة
7. **Push** إلى الـ branch
8. إنشاء **Pull Request**

### 6. متطلبات Pull Request

- [ ] وصف واضح للتغييرات
- [ ] اتباع معايير الكود
- [ ] تشغيل `npm run lint` بدون أخطاء
- [ ] تشغيل `npm run format` لتنسيق الكود
- [ ] اختبار التغييرات محلياً
- [ ] تحديث التوثيق إذا لزم الأمر
- [ ] إضافة screenshots للتغييرات في UI

### 7. الإبلاغ عن المشاكل

عند الإبلاغ عن مشكلة، يرجى تضمين:

- وصف واضح للمشكلة
- خطوات إعادة إنتاج المشكلة
- السلوك المتوقع مقابل السلوك الفعلي
- لقطات شاشة إذا كانت مفيدة
- معلومات البيئة (نظام التشغيل، المتصفح، إلخ)

### 8. طلب ميزات جديدة

- تأكد من أن الميزة غير موجودة
- اشرح الحاجة للميزة
- قدم أمثلة على الاستخدام
- اقترح تصميم أو تنفيذ إذا أمكن

### 9. الاختبارات

- اكتب اختبارات للميزات الجديدة
- تأكد من تمرير جميع الاختبارات الموجودة
- استخدم Jest و React Testing Library

### 10. الأمان

- لا تضع معلومات حساسة في الكود
- استخدم متغيرات البيئة للإعدادات
- اتبع أفضل ممارسات الأمان

## 📞 التواصل

للأسئلة أو المساعدة:
- إنشاء Issue في المشروع
- مراجعة التوثيق الموجود
- التواصل مع فريق التطوير

## 📄 الترخيص

بمساهمتك في هذا المشروع، فإنك توافق على أن مساهماتك ستكون مرخصة تحت نفس ترخيص المشروع.

---

شكراً لمساهمتك في تطوير النظام! 🚀
