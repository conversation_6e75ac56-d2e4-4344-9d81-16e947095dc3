const sql = require('mssql');

// إعدادات قاعدة البيانات
const dbConfig = {
  user: 'SA',
  password: 'admin@123',
  server: 'localhost\\DBOJESTA',
  database: 'EMP',
  options: {
    encrypt: false,
    trustServerCertificate: true,
    enableArithAbort: true,
    instanceName: 'DBOJESTA'
  }
};

async function addTechnicalDepartments() {
  try {
    console.log('🔗 الاتصال بقاعدة البيانات...');
    const pool = await sql.connect(dbConfig);

    // البحث عن المكتب الفني
    const techOfficeResult = await pool.request()
      .query("SELECT ID FROM OrganizationalUnits WHERE UnitName = N'المكتب الفني' AND IsActive = 1");

    if (techOfficeResult.recordset.length === 0) {
      console.log('❌ لم يتم العثور على المكتب الفني');
      return;
    }

    const techOfficeId = techOfficeResult.recordset[0].ID;
    console.log(`✅ تم العثور على المكتب الفني - ID: ${techOfficeId}`);

    // حذف الأقسام الفرعية القديمة
    await pool.request()
      .input('parentId', techOfficeId)
      .query('UPDATE OrganizationalUnits SET IsActive = 0 WHERE ParentUnitID = @parentId');

    console.log('🗑️ تم حذف الأقسام الفرعية القديمة');

    // الأقسام الجديدة للمكتب الفني
    const newDepartments = [
      {
        name: 'قسم التخطيط والمتابعة',
        code: 'TECH-PLAN',
        managerCode: '1417',
        managerName: 'أحمد محمد علي',
        description: 'قسم التخطيط ومتابعة المشاريع والجدولة الزمنية'
      },
      {
        name: 'قسم الرسومات التنفيذية',
        code: 'TECH-DRAW',
        managerCode: '1418',
        managerName: 'سارة إبراهيم حسن',
        description: 'قسم إعداد الرسومات التنفيذية والمخططات الهندسية'
      },
      {
        name: 'قسم الدراسات الفنية',
        code: 'TECH-STUDIES',
        managerCode: '1419',
        managerName: 'محمود عبد الرحمن',
        description: 'قسم الدراسات الفنية وتحليل المشاريع'
      },
      {
        name: 'قسم المواصفات والمقايسات',
        code: 'TECH-SPEC',
        managerCode: '1420',
        managerName: 'فاطمة أحمد محمد',
        description: 'قسم إعداد المواصفات الفنية والمقايسات'
      }
    ];

    console.log('📝 إضافة الأقسام الجديدة...');

    for (const dept of newDepartments) {
      // إضافة القسم
      const deptResult = await pool.request()
        .input('unitName', dept.name)
        .input('unitCode', dept.code)
        .input('parentUnitId', techOfficeId)
        .input('managerEmployeeCode', dept.managerCode)
        .input('managerName', dept.managerName)
        .input('description', dept.description)
        .query(`
          INSERT INTO OrganizationalUnits (
            UnitName, UnitCode, ParentUnitID, ManagerEmployeeCode,
            ManagerName, UnitLevel, UnitType, Description
          )
          OUTPUT INSERTED.ID
          VALUES (
            @unitName, @unitCode, @parentUnitId, @managerEmployeeCode,
            @managerName, 3, N'قسم', @description
          )
        `);

      const deptId = deptResult.recordset[0].ID;

      // إضافة المدير إلى جدول ربط الموظفين
      await pool.request()
        .input('employeeCode', dept.managerCode)
        .input('unitId', deptId)
        .input('position', 'رئيس القسم')
        .query(`
          INSERT INTO EmployeeUnits (EmployeeCode, UnitID, Position, IsDirectManager)
          VALUES (@employeeCode, @unitId, @position, 1)
        `);

      console.log(`✅ تم إضافة: ${dept.name} - رئيس: ${dept.managerName}`);
    }

    // إضافة بعض الموظفين التجريبيين للأقسام
    console.log('👥 إضافة موظفين تجريبيين...');

    const sampleEmployees = [
      // قسم التخطيط والمتابعة
      { code: '1421', name: 'علي حسن محمد', unitName: 'قسم التخطيط والمتابعة', position: 'مهندس تخطيط' },
      { code: '1422', name: 'نورا أحمد علي', unitName: 'قسم التخطيط والمتابعة', position: 'محلل مشاريع' },

      // قسم الرسومات التنفيذية
      { code: '1423', name: 'محمد سعد إبراهيم', unitName: 'قسم الرسومات التنفيذية', position: 'مهندس رسم' },
      { code: '1424', name: 'أميرة محمود حسن', unitName: 'قسم الرسومات التنفيذية', position: 'مصمم CAD' },

      // قسم الدراسات الفنية
      { code: '1425', name: 'خالد عبد الله أحمد', unitName: 'قسم الدراسات الفنية', position: 'مهندس دراسات' },

      // قسم المواصفات والمقايسات
      { code: '1426', name: 'رانيا محمد سعد', unitName: 'قسم المواصفات والمقايسات', position: 'مهندس مقايسات' }
    ];

    for (const emp of sampleEmployees) {
      // البحث عن القسم
      const unitResult = await pool.request()
        .input('unitName', emp.unitName)
        .query('SELECT ID FROM OrganizationalUnits WHERE UnitName = @unitName AND IsActive = 1');

      if (unitResult.recordset.length > 0) {
        const unitId = unitResult.recordset[0].ID;

        // إضافة الموظف إلى جدول الموظفين إذا لم يكن موجوداً
        await pool.request()
          .input('employeeCode', emp.code)
          .input('employeeName', emp.name)
          .query(`
            IF NOT EXISTS (SELECT * FROM Employees WHERE EmployeeCode = @employeeCode)
            BEGIN
              INSERT INTO Employees (EmployeeCode, EmployeeName, JobTitle)
              VALUES (@employeeCode, @employeeName, N'${emp.position}')
            END
          `);

        // إضافة الموظف إلى الوحدة التنظيمية
        await pool.request()
          .input('employeeCode', emp.code)
          .input('unitId', unitId)
          .input('position', emp.position)
          .query(`
            IF NOT EXISTS (SELECT * FROM EmployeeUnits WHERE EmployeeCode = @employeeCode AND UnitID = @unitId)
            BEGIN
              INSERT INTO EmployeeUnits (EmployeeCode, UnitID, Position, IsDirectManager)
              VALUES (@employeeCode, @unitId, @position, 0)
            END
          `);

        console.log(`  👤 تم إضافة: ${emp.name} إلى ${emp.unitName}`);
      }
    }

    // عرض النتيجة النهائية
    const finalResult = await pool.request().query(`
      SELECT
        ou.ID,
        ou.UnitName,
        ou.UnitCode,
        ou.ManagerEmployeeCode,
        ou.ManagerName,
        ou.UnitLevel,
        ou.UnitType,
        parent.UnitName as ParentUnitName,
        COUNT(eu.EmployeeCode) as EmployeeCount
      FROM OrganizationalUnits ou
      LEFT JOIN OrganizationalUnits parent ON ou.ParentUnitID = parent.ID
      LEFT JOIN EmployeeUnits eu ON ou.ID = eu.UnitID AND eu.IsActive = 1
      WHERE ou.IsActive = 1
      GROUP BY ou.ID, ou.UnitName, ou.UnitCode, ou.ManagerEmployeeCode,
               ou.ManagerName, ou.UnitLevel, ou.UnitType, parent.UnitName
      ORDER BY ou.UnitLevel, ou.UnitName
    `);

    console.log('\n📊 الهيكل الوظيفي المحدث:');
    finalResult.recordset.forEach((unit, index) => {
      const indent = '  '.repeat(unit.UnitLevel - 1);
      console.log(`${index + 1}. ${indent}${unit.UnitName} (${unit.UnitType})`);
      console.log(`${indent}   مدير: ${unit.ManagerName} - كود: ${unit.ManagerEmployeeCode}`);
      console.log(`${indent}   عدد الموظفين: ${unit.EmployeeCount}`);
      if (unit.ParentUnitName) {
        console.log(`${indent}   تابع لـ: ${unit.ParentUnitName}`);
      }
      console.log('');
    });

    await pool.close();
    console.log('\n✅ تم تحديث المكتب الفني بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في تحديث المكتب الفني:', error.message);
  }
}

addTechnicalDepartments();
