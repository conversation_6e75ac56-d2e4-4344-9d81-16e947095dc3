import { NextResponse } from 'next/server';
import { getConnection } from '@/utils/db';

export async function GET(request) {
  try {

    const pool = await getConnection();
    
    // جلب الشقق مع المستفيدين
    const result = await pool.request().query(`
      SELECT 
        a.ID,
        a.ApartmentCode,
        a.LandlordName,
        a.Address,
        a.StartDate,
        a.EndDate,
        a.RentAmount,
        a.InsuranceAmount,
        a.CommissionAmount,
        a.BacklogAmount,
        a.Notes,
        a.IsActive,
        a.CreatedAt,
        a.UpdatedAt
      FROM Apartments a
      WHERE a.IsActive = 1
      ORDER BY a.ApartmentCode
    `);

    // جلب المستفيدين لكل شقة
    const apartmentsWithBeneficiaries = await Promise.all(
      result.recordset.map(async (apartment) => {
        try {
          const beneficiariesResult = await pool.request()
            .input('apartmentId', apartment.ID)
            .query(`
              SELECT 
                ab.ID as BeneficiaryID,
                ab.EmployeeCode,
                ab.EmployeeName,
                ab.JobTitle,
                ab.Department,
                ab.StartDate as BeneficiaryStartDate,
                ab.EndDate as BeneficiaryEndDate,
                ab.IsActive as IsBeneficiaryActive,
                ab.Notes as BeneficiaryNotes
              FROM ApartmentBeneficiaries ab
              WHERE ab.ApartmentID = @apartmentId AND ab.IsActive = 1
              ORDER BY ab.StartDate DESC
            `);

          return {
            ...apartment,
            beneficiaries: beneficiariesResult.recordset || [],
            BeneficiariesCount: beneficiariesResult.recordset?.length || 0
          };
        } catch (error) {

          return {
            ...apartment,
            beneficiaries: [],
            BeneficiariesCount: 0
          };
        }
      })
    );

    return NextResponse.json({
      success: true,
      data: apartmentsWithBeneficiaries,
      count: apartmentsWithBeneficiaries.length,
      message: `تم جلب ${apartmentsWithBeneficiaries.length} شقة بنجاح`
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message,
      details: {
        name: error.name,
        code: error.code,
        state: error.state
      }
    }, { status: 500 });
  }
}

export async function POST(request) {
  try {
    const body = await request.json();
    const { action } = body;

    if (action === 'getAll') {
      // إعادة توجيه إلى GET
      return GET(request);
    }

    return NextResponse.json({
      success: false,
      error: 'Action غير مدعوم'
    }, { status: 400 });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
