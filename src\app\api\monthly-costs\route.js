import { getConnection, sql } from '@/utils/db';
import fs from 'fs';
import { NextResponse } from 'next/server';

// GET method للحصول على بيانات الرسوم البيانية
export async function GET(request) {
  let pool = null;
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'getChartsData';

    pool = await getConnection();

    switch (action) {
      case 'getChartsData':
        return await getChartsData(pool);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }
  } catch (error) {
    console.error('خطأ في GET monthly-costs:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      try {
        await pool.close();
      } catch (closeError) {
        console.error('خطأ في إغلاق الاتصال:', closeError);
      }
    }
  }
}

// دالة مساعدة لتحويل رقم الشهر إلى اسم
function getMonthName(monthNumber) {
  const months = {
    1: 'يناير', 2: 'فبراير', 3: 'مارس', 4: 'ابريل',
    5: 'مايو', 6: 'يونيو', 7: 'يوليو', 8: 'اغسطس',
    9: 'سبتمبر', 10: 'اكتوبر', 11: 'نوفمبر', 12: 'ديسمبر'
  };
  return months[monthNumber] || 'غير محدد';
}

export async function POST(request) {
  let pool = null;
  try {
    // التحقق من صحة الطلب
    if (!request.body) {
      return NextResponse.json({
        success: false,
        error: 'لا يوجد محتوى في الطلب'
      }, { status: 400 });
    }

    const body = await request.json();
    const { action } = body;

    if (!action) {
      return NextResponse.json({
        success: false,
        error: 'الإجراء مطلوب'
      }, { status: 400 });
    }

    pool = await getConnection();

    switch (action) {
      case 'setup':
        return await setupMonthlyCostTables(pool);
      case 'createMonthlyCosts':
        return await createMonthlyCosts(pool, body);
      case 'getMonthlyCosts':
        return await getMonthlyCosts(pool, body);
      case 'updateMonthlyCosts':
        return await updateMonthlyCosts(pool, body);
      case 'deleteMonthlyCosts':
        return await deleteMonthlyCosts(pool, body);
      case 'getVersionRequests':
        return await getVersionRequests(pool, body);
      case 'uploadVersionDocument':
        return await uploadVersionDocument(pool, body);
      case 'getStatistics':
        return await getStatistics(pool, body);
      case 'getCostStats':
        return await getCostStats(pool, body);
      case 'getChartsData':
        return await getChartsData(pool);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }
  } catch (error) {
    console.error('خطأ في API monthly-costs:', error);

    // التحقق من نوع الخطأ
    if (error.name === 'SyntaxError' && error.message.includes('JSON')) {
      return NextResponse.json({
        success: false,
        error: 'تنسيق JSON غير صحيح'
      }, { status: 400 });
    }

    if (error.code === 'ECONNRESET' || error.code === 'ECONNABORTED') {
      return NextResponse.json({
        success: false,
        error: 'انقطع الاتصال'
      }, { status: 408 });
    }

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  } finally {
    // إغلاق الاتصال بقاعدة البيانات
    if (pool) {
      try {
        await pool.close();
      } catch (closeError) {
        console.error('خطأ في إغلاق الاتصال:', closeError);
      }
    }
  }
}

// إعداد جداول التكاليف الشهرية
async function setupMonthlyCostTables(pool) {
  try {

    // 1. جدول التكاليف الشهرية
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MonthlyCosts' AND xtype='U')
      BEGIN
        CREATE TABLE MonthlyCosts (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          CostType NVARCHAR(50) NOT NULL, -- carscost, housingcost, 3amala
          Month INT NOT NULL,
          Year INT NOT NULL,
          TotalAmount DECIMAL(15,2) NOT NULL DEFAULT 0,
          ItemsCount INT DEFAULT 0, -- عدد السيارات/الشقق/العمال
          AverageCostPerItem DECIMAL(10,2) DEFAULT 0, -- متوسط التكلفة للوحدة
          Details NVARCHAR(MAX), -- JSON للتفاصيل
          HasDocument BIT DEFAULT 0,
          DocumentPath NVARCHAR(500),
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),
          CreatedBy NVARCHAR(100),
          Notes NVARCHAR(MAX), -- ملاحظات إضافية

          UNIQUE(CostType, Month, Year)
        )

        CREATE INDEX IX_MonthlyCosts_Type ON MonthlyCosts(CostType)
        CREATE INDEX IX_MonthlyCosts_Date ON MonthlyCosts(Year, Month)
        CREATE INDEX IX_MonthlyCosts_Document ON MonthlyCosts(HasDocument)
      END
    `);

    // 2. جدول تفاصيل التكاليف
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MonthlyCostDetails' AND xtype='U')
      BEGIN
        CREATE TABLE MonthlyCostDetails (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          MonthlyCostID INT NOT NULL,
          ItemCode NVARCHAR(50), -- كود السيارة/الشقة/العامل
          ItemName NVARCHAR(200), -- اسم العنصر
          Description NVARCHAR(500), -- وصف التكلفة
          Amount DECIMAL(10,2) NOT NULL,
          Notes NVARCHAR(MAX),
          CreatedAt DATETIME DEFAULT GETDATE(),

          FOREIGN KEY (MonthlyCostID) REFERENCES MonthlyCosts(ID) ON DELETE CASCADE
        )

        CREATE INDEX IX_MonthlyCostDetails_Monthly ON MonthlyCostDetails(MonthlyCostID)
        CREATE INDEX IX_MonthlyCostDetails_Item ON MonthlyCostDetails(ItemCode)
      END
    `);

    // 3. التأكد من وجود مجلدات الأرشيف
    const archivePaths = [
      'E:\\webapp\\createxyz-project\\archiv\\carscost',
      'E:\\webapp\\createxyz-project\\archiv\\housingcost',
      'E:\\webapp\\createxyz-project\\archiv\\3amala'
    ];

    for (const archivePath of archivePaths) {
      try {
        if (!fs.existsSync(archivePath)) {
          fs.mkdirSync(archivePath, { recursive: true });

        }
      } catch (error) {

      }
    }

    return NextResponse.json({
      success: true,
      message: 'تم إعداد نظام التكاليف الشهرية بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في إعداد جداول التكاليف الشهرية: ' + error.message
    }, { status: 500 });
  }
}

// إنشاء تكاليف شهرية جديدة
async function createMonthlyCosts(pool, data) {
  try {
    const { costType, month, year, totalAmount, itemsCount, details, items, notes, isAttachment } = data;

    // التحقق من صحة البيانات
    if (!costType || !month || !year || !totalAmount || !itemsCount) {
      return NextResponse.json({
        success: false,
        error: 'جميع الحقول مطلوبة'
      }, { status: 400 });
    }

    // التحقق من عدم وجود سجل مكرر
    const existing = await pool.request()
      .input('costType', sql.NVarChar, costType)
      .input('month', sql.Int, month)
      .input('year', sql.Int, year)
      .query(`
        SELECT ID FROM MonthlyCosts
        WHERE CostType = @costType AND Month = @month AND Year = @year
      `);

    if (existing.recordset.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'توجد تكاليف مسجلة بالفعل لهذا الشهر والنوع'
      }, { status: 400 });
    }

    // حساب متوسط التكلفة
    const averageCostPerItem = itemsCount > 0 ? (totalAmount / itemsCount) : 0;

    // إدراج البيانات في جدول MonthlyCosts الموحد
    const result = await pool.request()
      .input('costType', sql.NVarChar, costType)
      .input('month', sql.Int, month)
      .input('year', sql.Int, year)
      .input('totalAmount', sql.Decimal(15, 2), totalAmount)
      .input('itemsCount', sql.Int, itemsCount)
      .input('averageCostPerItem', sql.Decimal(10, 2), averageCostPerItem)
      .input('notes', sql.NVarChar, notes || null)
      .input('details', sql.NVarChar, JSON.stringify({
        isAttachment: costType === 'housingcost' ? (isAttachment || false) : false,
        createdBy: 'النظام',
        timestamp: new Date().toISOString()
      }))
      .query(`
        INSERT INTO MonthlyCosts (
          CostType, Month, Year, TotalAmount, ItemsCount,
          AverageCostPerItem, Notes, Details, CreatedBy
        )
        OUTPUT INSERTED.ID
        VALUES (
          @costType, @month, @year, @totalAmount, @itemsCount,
          @averageCostPerItem, @notes, @details, 'النظام'
        )
      `);

    const recordId = result.recordset[0].ID;

    // رسالة النجاح
    const attachmentText = (costType === 'housingcost' && isAttachment) ? ' (ملحق)' : '';
    const message = `تم إنشاء تكاليف ${costType === 'carscost' ? 'السيارات' : costType === 'housingcost' ? 'الشقق' : 'العمالة المؤقتة'}${attachmentText} بنجاح`;

    return NextResponse.json({
      success: true,
      message: message,
      data: {
        recordId,
        isAttachment: isAttachment || false,
        monthName: getMonthName(month),
        year
      }
    });

  } catch (error) {
    console.error('خطأ في إنشاء التكاليف الشهرية:', error);
    console.error('Stack trace:', error.stack);
    return NextResponse.json({
      success: false,
      error: 'فشل في إنشاء التكاليف الشهرية: ' + error.message
    }, { status: 500 });
  }
}

// جلب التكاليف الشهرية
async function getMonthlyCosts(pool, data) {
  try {
    const { costType, month, year, includeDetails = true } = data;

    let whereClause = 'WHERE 1=1';
    const inputs = [];

    if (costType) {
      whereClause += ' AND mc.CostType = @costType';
      inputs.push({ name: 'costType', type: sql.NVarChar, value: costType });
    }

    if (month) {
      whereClause += ' AND mc.Month = @month';
      inputs.push({ name: 'month', type: sql.Int, value: month });
    }

    if (year) {
      whereClause += ' AND mc.Year = @year';
      inputs.push({ name: 'year', type: sql.Int, value: year });
    }

    const request = pool.request();
    inputs.forEach(input => {
      request.input(input.name, input.type, input.value);
    });

    const query = `
      SELECT
        mc.ID,
        mc.CostType,
        mc.Month,
        mc.Year,
        mc.TotalAmount,
        mc.ItemsCount,
        mc.AverageCostPerItem,
        mc.Details,
        mc.HasDocument,
        mc.DocumentPath,
        mc.Notes,
        mc.CreatedAt,
        mc.UpdatedAt
      FROM MonthlyCosts mc
      ${whereClause}
      ORDER BY mc.Year DESC, mc.Month DESC
    `;

    const result = await request.query(query);

    // جلب التفاصيل إذا كان مطلوباً
    if (includeDetails) {
      for (let cost of result.recordset) {
        const detailsResult = await pool.request()
          .input('monthlyCostId', sql.Int, cost.ID)
          .query(`
            SELECT
              ID, ItemCode, ItemName, Description, Amount, Notes, CreatedAt
            FROM MonthlyCostDetails
            WHERE MonthlyCostID = @monthlyCostId
            ORDER BY CreatedAt
          `);

        cost.items = detailsResult.recordset;
        cost.details = cost.Details ? JSON.parse(cost.Details) : {};
      }
    }

    return NextResponse.json({
      success: true,
      data: result.recordset
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب التكاليف الشهرية: ' + error.message
    }, { status: 500 });
  }
}

// تحديث التكاليف الشهرية
async function updateMonthlyCosts(pool, data) {
  try {
    const { id, totalAmount, itemsCount, notes, details } = data;

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف التكلفة مطلوب'
      }, { status: 400 });
    }

    // حساب متوسط التكلفة
    const averageCostPerItem = itemsCount > 0 ? (totalAmount / itemsCount) : 0;

    // تحديث السجل في جدول MonthlyCosts
    const updateResult = await pool.request()
      .input('id', sql.Int, id)
      .input('totalAmount', sql.Decimal(15, 2), totalAmount)
      .input('itemsCount', sql.Int, itemsCount)
      .input('averageCostPerItem', sql.Decimal(10, 2), averageCostPerItem)
      .input('notes', sql.NVarChar, notes || null)
      .input('details', sql.NVarChar, JSON.stringify(details || {}))
      .query(`
        UPDATE MonthlyCosts
        SET
          TotalAmount = @totalAmount,
          ItemsCount = @itemsCount,
          AverageCostPerItem = @averageCostPerItem,
          Notes = @notes,
          Details = @details,
          UpdatedAt = GETDATE()
        WHERE ID = @id
      `);

    if (updateResult.rowsAffected[0] === 0) {
      return NextResponse.json({
        success: false,
        error: 'لم يتم العثور على السجل المطلوب تحديثه'
      }, { status: 404 });
    }

    // جلب السجل المحدث
    const updatedRecord = await pool.request()
      .input('id', sql.Int, id)
      .query(`
        SELECT
          ID, CostType, Month, Year, TotalAmount, ItemsCount,
          AverageCostPerItem, Details, Notes, CreatedAt, UpdatedAt
        FROM MonthlyCosts
        WHERE ID = @id
      `);

    return NextResponse.json({
      success: true,
      message: 'تم تحديث التكاليف بنجاح',
      data: updatedRecord.recordset[0]
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث التكاليف: ' + error.message
    }, { status: 500 });
  }
}

// حذف التكاليف الشهرية
async function deleteMonthlyCosts(pool, data) {
  try {
    const { id } = data;

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف التكلفة مطلوب'
      }, { status: 400 });
    }

    // حذف السجل من جدول MonthlyCosts
    const deleteResult = await pool.request()
      .input('id', sql.Int, id)
      .query(`
        DELETE FROM MonthlyCosts
        WHERE ID = @id
      `);

    if (deleteResult.rowsAffected[0] === 0) {
      return NextResponse.json({
        success: false,
        error: 'لم يتم العثور على السجل المطلوب حذفه'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف التكاليف بنجاح'
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'فشل في حذف التكاليف: ' + error.message
    }, { status: 500 });
  }
}

// جلب طلبات الإصدار (الملفات المحفوظة)
async function getVersionRequests(pool, data) {
  try {
    const { costType } = data;

    // جلب جميع الملفات المحفوظة لنوع التكلفة
    const result = await pool.request()
      .input('costType', sql.NVarChar, costType)
      .query(`
        SELECT
          ID,
          Month,
          Year,
          TotalAmount,
          ItemsCount,
          HasDocument,
          DocumentPath,
          CreatedAt
        FROM MonthlyCosts
        WHERE CostType = @costType AND HasDocument = 1
        ORDER BY Year DESC, Month DESC
      `);

    // تنسيق البيانات لعرض طلبات الإصدار
    const versionRequests = result.recordset.map(record => ({
      id: record.ID,
      versionName: `${record.Month}-${record.Year}`,
      month: record.Month,
      year: record.Year,
      totalAmount: record.TotalAmount,
      itemsCount: record.ItemsCount,
      documentPath: record.DocumentPath,
      createdAt: record.CreatedAt
    }));

    return NextResponse.json({
      success: true,
      data: versionRequests
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب طلبات الإصدار: ' + error.message
    }, { status: 500 });
  }
}

// رفع ملف طلب الإصدار
async function uploadVersionDocument(pool, data) {
  try {
    const { costType, month, year, fileBuffer, fileName } = data;

    // تحديد مسار الحفظ
    const archivePaths = {
      'carscost': 'E:\\webapp\\createxyz-project\\archiv\\carscost\\',
      'housingcost': 'E:\\webapp\\createxyz-project\\archiv\\housingcost\\',
      '3amala': 'E:\\webapp\\createxyz-project\\archiv\\3amala\\'
    };

    const documentName = `${month}-${year}.pdf`;
    const documentPath = archivePaths[costType] + documentName;

    // حفظ الملف في المجلد المحدد
    try {
      fs.writeFileSync(documentPath, fileBuffer);

    } catch (fileError) {

      return NextResponse.json({
        success: false,
        error: 'فشل في حفظ الملف: ' + fileError.message
      }, { status: 500 });
    }

    // تحديث قاعدة البيانات
    await pool.request()
      .input('costType', sql.NVarChar, costType)
      .input('month', sql.Int, month)
      .input('year', sql.Int, year)
      .input('documentPath', sql.NVarChar, documentPath)
      .query(`
        UPDATE MonthlyCosts
        SET HasDocument = 1, DocumentPath = @documentPath, UpdatedAt = GETDATE()
        WHERE CostType = @costType AND Month = @month AND Year = @year
      `);

    return NextResponse.json({
      success: true,
      message: 'تم رفع وحفظ ملف طلب الإصدار بنجاح',
      data: { documentPath, documentName }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في رفع ملف طلب الإصدار: ' + error.message
    }, { status: 500 });
  }
}

// جلب إحصائيات التكاليف من الجداول الفعلية
async function getCostStats(pool, data) {
  try {
    const { month, year } = data;

    const stats = {
      cars: { total: 0, monthly: 0, count: 0 },
      apartments: { total: 0, monthly: 0, count: 0 },
      tempWorkers: { total: 0, monthly: 0, count: 0 }
    };

    // 1. جلب تكاليف السيارات من جدول carscost
    try {
      const carsResult = await pool.request()
        .input('month', sql.Int, month)
        .input('year', sql.Int, year)
        .query(`
          SELECT
            SUM(
              CASE
                WHEN ISNUMERIC([العدد]) = 1
                THEN CAST([العدد] AS INT)
                ELSE 0
              END
            ) as ItemCount,
            SUM(
              CASE
                WHEN ISNUMERIC([القيمة الإيجارية]) = 1
                THEN CAST([القيمة الإيجارية] AS DECIMAL(15,2))
                ELSE CAST(REPLACE(REPLACE([القيمة الإيجارية], ',', ''), ' ', '') AS DECIMAL(15,2))
              END
            ) as MonthlyTotal
          FROM carscost
          WHERE [الشهر] = (
            CASE @month
              WHEN 1 THEN 'يناير' WHEN 2 THEN 'فبراير' WHEN 3 THEN 'مارس'
              WHEN 4 THEN 'ابريل' WHEN 5 THEN 'مايو' WHEN 6 THEN 'يونيو'
              WHEN 7 THEN 'يوليو' WHEN 8 THEN 'اغسطس' WHEN 9 THEN 'سبتمبر'
              WHEN 10 THEN 'اكتوبر' WHEN 11 THEN 'نوفمبر' WHEN 12 THEN 'ديسمبر'
            END
          ) AND [السنة] = CAST(@year AS NVARCHAR)
        `);

      if (carsResult.recordset.length > 0) {
        stats.cars.monthly = parseFloat(carsResult.recordset[0].MonthlyTotal) || 0;
        stats.cars.count = parseInt(carsResult.recordset[0].ItemCount) || 0;
      }

      // جلب إجمالي تكاليف السيارات للسنة
      const carsYearResult = await pool.request()
        .input('year', sql.Int, year)
        .query(`
          SELECT
            SUM(
              CASE
                WHEN ISNUMERIC([القيمة الإيجارية]) = 1
                THEN CAST([القيمة الإيجارية] AS DECIMAL(15,2))
                ELSE CAST(REPLACE(REPLACE([القيمة الإيجارية], ',', ''), ' ', '') AS DECIMAL(15,2))
              END
            ) as YearlyTotal
          FROM carscost
          WHERE [السنة] = CAST(@year AS NVARCHAR)
        `);

      if (carsYearResult.recordset.length > 0 && carsYearResult.recordset[0].YearlyTotal) {
        stats.cars.total = parseFloat(carsYearResult.recordset[0].YearlyTotal) || 0;
      }

    } catch (error) {

    }

    // 2. جلب تكاليف الشقق من جدول APARTMENTCOST
    try {
      const apartmentsResult = await pool.request()
        .input('month', sql.Int, month)
        .input('year', sql.Int, year)
        .query(`
          SELECT
            SUM([العدد]) as ItemCount,
            SUM([القيمة الإيجارية]) as MonthlyTotal
          FROM APARTMENTCOST
          WHERE [الشهر] = (
            CASE @month
              WHEN 1 THEN 'يناير' WHEN 2 THEN 'فبراير' WHEN 3 THEN 'مارس'
              WHEN 4 THEN 'ابريل' WHEN 5 THEN 'مايو' WHEN 6 THEN 'يونيو'
              WHEN 7 THEN 'يوليو' WHEN 8 THEN 'اغسطس' WHEN 9 THEN 'سبتمبر'
              WHEN 10 THEN 'اكتوبر' WHEN 11 THEN 'نوفمبر' WHEN 12 THEN 'ديسمبر'
            END
          ) AND [السنة] = @year
        `);

      if (apartmentsResult.recordset.length > 0) {
        stats.apartments.monthly = parseFloat(apartmentsResult.recordset[0].MonthlyTotal) || 0;
        stats.apartments.count = parseInt(apartmentsResult.recordset[0].ItemCount) || 0;
      }

      // جلب إجمالي تكاليف الشقق للسنة
      const apartmentsYearResult = await pool.request()
        .input('year', sql.Int, year)
        .query(`
          SELECT
            SUM([القيمة الإيجارية]) as YearlyTotal
          FROM APARTMENTCOST
          WHERE [السنة] = @year
        `);

      if (apartmentsYearResult.recordset.length > 0 && apartmentsYearResult.recordset[0].YearlyTotal) {
        stats.apartments.total = parseFloat(apartmentsYearResult.recordset[0].YearlyTotal) || 0;
      }

    } catch (error) {

    }

    // 3. جلب تكاليف العمالة المؤقتة
    try {
      // أولاً: محاولة جلب البيانات من جدول TEMPWORKERSCOST إذا كان موجوداً
      try {
        const tempWorkersResult = await pool.request()
          .input('month', sql.Int, month)
          .input('year', sql.Int, year)
          .query(`
            SELECT
              SUM([العدد]) as WorkerCount,
              SUM([القيمة الإجمالية]) as MonthlyTotal
            FROM TEMPWORKERSCOST
            WHERE [الشهر] = (
              CASE @month
                WHEN 1 THEN 'يناير' WHEN 2 THEN 'فبراير' WHEN 3 THEN 'مارس'
                WHEN 4 THEN 'ابريل' WHEN 5 THEN 'مايو' WHEN 6 THEN 'يونيو'
                WHEN 7 THEN 'يوليو' WHEN 8 THEN 'اغسطس' WHEN 9 THEN 'سبتمبر'
                WHEN 10 THEN 'اكتوبر' WHEN 11 THEN 'نوفمبر' WHEN 12 THEN 'ديسمبر'
              END
            ) AND [السنة] = @year
          `);

        if (tempWorkersResult.recordset.length > 0) {
          stats.tempWorkers.monthly = parseFloat(tempWorkersResult.recordset[0].MonthlyTotal) || 0;
          stats.tempWorkers.count = parseInt(tempWorkersResult.recordset[0].WorkerCount) || 0;
        }

        // جلب إجمالي تكاليف العمالة المؤقتة للسنة
        const tempWorkersYearResult = await pool.request()
          .input('year', sql.Int, year)
          .query(`
            SELECT
              SUM([القيمة الإجمالية]) as YearlyTotal
            FROM TEMPWORKERSCOST
            WHERE [السنة] = @year
          `);

        if (tempWorkersYearResult.recordset.length > 0 && tempWorkersYearResult.recordset[0].YearlyTotal) {
          stats.tempWorkers.total = parseFloat(tempWorkersYearResult.recordset[0].YearlyTotal) || 0;
        }

      } catch (tempCostError) {

        // ثانياً: حساب التكاليف من جدول TempWorkers
        const tempWorkersFromMainTable = await pool.request()
          .input('month', sql.Int, month)
          .input('year', sql.Int, year)
          .query(`
            SELECT
              COUNT(*) as WorkerCount,
              SUM(DailyRate * 30) as EstimatedMonthlyTotal
            FROM TempWorkers
            WHERE IsActive = 1
              AND MONTH(FirstWorkDay) <= @month
              AND YEAR(FirstWorkDay) <= @year
          `);

        if (tempWorkersFromMainTable.recordset.length > 0) {
          stats.tempWorkers.monthly = parseFloat(tempWorkersFromMainTable.recordset[0].EstimatedMonthlyTotal) || 0;
        }

        // حساب إجمالي السنة
        const tempWorkersYearFromMainTable = await pool.request()
          .input('year', sql.Int, year)
          .query(`
            SELECT
              SUM(DailyRate * 30 * 12) as EstimatedYearlyTotal
            FROM TempWorkers
            WHERE IsActive = 1
              AND YEAR(FirstWorkDay) <= @year
          `);

        if (tempWorkersYearFromMainTable.recordset.length > 0) {
          stats.tempWorkers.total = parseFloat(tempWorkersYearFromMainTable.recordset[0].EstimatedYearlyTotal) || 0;
        }

      }
    } catch (error) {

    }

    return NextResponse.json({
      success: true,
      data: stats
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب إحصائيات التكاليف: ' + error.message
    }, { status: 500 });
  }
}

// دالة getStatistics (للتوافق مع الكود الموجود)
async function getStatistics(pool, data) {
  // استخدام نفس دالة getCostStats
  return await getCostStats(pool, data);
}

// جلب بيانات الرسوم البيانية من الجداول الحقيقية
async function getChartsData(pool) {
  try {
    console.log('🔄 جلب بيانات الرسوم البيانية من الجداول الحقيقية...');

    const chartsData = {
      cars: [],
      apartments: [],
      tempWorkers: []
    };

    // 1. جلب بيانات السيارات من الجدول الأصلي مع إضافة البيانات الحقيقية
    try {
      // أولاً: جلب البيانات من الجدول الأصلي
      const carsOriginalResult = await pool.request().query(`
        SELECT
          [الشهر] as month,
          [السنة] as year,
          SUM(
            CASE
              WHEN ISNUMERIC([العدد]) = 1
              THEN CAST([العدد] AS INT)
              ELSE 0
            END
          ) as count,
          SUM(
            CASE
              WHEN ISNUMERIC([القيمة الإيجارية]) = 1
              THEN CAST([القيمة الإيجارية] AS DECIMAL(15,2))
              ELSE CAST(REPLACE(REPLACE([القيمة الإيجارية], ',', ''), ' ', '') AS DECIMAL(15,2))
            END
          ) as cost
        FROM carscost
        GROUP BY [الشهر], [السنة]
        ORDER BY [السنة],
          CASE [الشهر]
            WHEN 'يناير' THEN 1 WHEN 'فبراير' THEN 2 WHEN 'مارس' THEN 3
            WHEN 'ابريل' THEN 4 WHEN 'أبريل' THEN 4 WHEN 'مايو' THEN 5 WHEN 'يونيو' THEN 6
            WHEN 'يوليو' THEN 7 WHEN 'اغسطس' THEN 8 WHEN 'أغسطس' THEN 8 WHEN 'سبتمبر' THEN 9
            WHEN 'اكتوبر' THEN 10 WHEN 'أكتوبر' THEN 10 WHEN 'نوفمبر' THEN 11 WHEN 'ديسمبر' THEN 12
          END
      `);

      chartsData.cars = carsOriginalResult.recordset.map(row => ({
        month: row.month,
        year: parseInt(row.year),
        count: row.count || 0,
        cost: parseFloat(row.cost) || 0
      }));

      console.log('✅ بيانات السيارات:', chartsData.cars);
    } catch (error) {
      console.error('❌ خطأ في جلب بيانات السيارات:', error);
    }

    // 2. جلب بيانات الشقق من الجدول الأصلي
    try {
      const apartmentsResult = await pool.request().query(`
        SELECT
          [الشهر] as month,
          [السنة] as year,
          SUM([العدد]) as count,
          SUM([القيمة الإيجارية]) as cost
        FROM APARTMENTCOST
        GROUP BY [الشهر], [السنة]
        ORDER BY [السنة],
          CASE [الشهر]
            WHEN 'يناير' THEN 1 WHEN 'فبراير' THEN 2 WHEN 'مارس' THEN 3
            WHEN 'ابريل' THEN 4 WHEN 'أبريل' THEN 4 WHEN 'مايو' THEN 5 WHEN 'يونيو' THEN 6
            WHEN 'يوليو' THEN 7 WHEN 'اغسطس' THEN 8 WHEN 'أغسطس' THEN 8 WHEN 'سبتمبر' THEN 9
            WHEN 'اكتوبر' THEN 10 WHEN 'أكتوبر' THEN 10 WHEN 'نوفمبر' THEN 11 WHEN 'ديسمبر' THEN 12
          END
      `);

      chartsData.apartments = apartmentsResult.recordset.map(row => ({
        month: row.month,
        year: parseInt(row.year),
        count: row.count || 0,
        cost: parseFloat(row.cost) || 0
      }));

      console.log('✅ بيانات الشقق:', chartsData.apartments);
    } catch (error) {
      console.error('❌ خطأ في جلب بيانات الشقق:', error);
    }

    // 3. جلب بيانات العمالة المؤقتة من الجدول الأصلي
    try {
      const tempWorkersResult = await pool.request().query(`
        SELECT
          [الشهر] as month,
          [السنة] as year,
          SUM([العدد]) as count,
          SUM([القيمة الإجمالية]) as cost
        FROM TEMPWORKERSCOST
        GROUP BY [الشهر], [السنة]
        ORDER BY [السنة],
          CASE [الشهر]
            WHEN 'يناير' THEN 1 WHEN 'فبراير' THEN 2 WHEN 'مارس' THEN 3
            WHEN 'ابريل' THEN 4 WHEN 'أبريل' THEN 4 WHEN 'مايو' THEN 5 WHEN 'يونيو' THEN 6
            WHEN 'يوليو' THEN 7 WHEN 'اغسطس' THEN 8 WHEN 'أغسطس' THEN 8 WHEN 'سبتمبر' THEN 9
            WHEN 'اكتوبر' THEN 10 WHEN 'أكتوبر' THEN 10 WHEN 'نوفمبر' THEN 11 WHEN 'ديسمبر' THEN 12
          END
      `);

      chartsData.tempWorkers = tempWorkersResult.recordset.map(row => ({
        month: row.month,
        year: parseInt(row.year),
        count: row.count || 0,
        cost: parseFloat(row.cost) || 0
      }));

      console.log('✅ بيانات العمالة المؤقتة:', chartsData.tempWorkers);
    } catch (error) {
      console.error('❌ خطأ في جلب بيانات العمالة المؤقتة:', error);
    }

    return NextResponse.json({
      success: true,
      data: chartsData
    });

  } catch (error) {
    console.error('❌ خطأ في جلب بيانات الرسوم البيانية:', error);
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب بيانات الرسوم البيانية: ' + error.message
    }, { status: 500 });
  }
}
