import { NextResponse } from 'next/server';
import sql from 'mssql';

const dbConfig = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  server: process.env.DB_SERVER,
  database: process.env.DB_NAME,
  options: {
    encrypt: true,
    trustServerCertificate: true,
  },
};

export async function GET(request) {
  const { searchParams } = new URL(request.url);
  const employeeId = searchParams.get('employeeId');
  const leaveType = searchParams.get('leaveType');

  if (!employeeId || !leaveType) {
    return NextResponse.json({
      success: false,
      error: 'كود الموظف ونوع الإجازة مطلوبان'
    }, { status: 400 });
  }

  try {
    const pool = await sql.connect(dbConfig);

    // تحديد نوع الإجازة بالعربية
    let leaveTypeArabic = '';
    switch (leaveType) {
      case 'annual':
        leaveTypeArabic = 'اعتيادية';
        break;
      case 'emergency':
        leaveTypeArabic = 'عارضة';
        break;
      case 'sick':
        leaveTypeArabic = 'مرضية';
        break;
      case 'compensatory':
      case 'badal':
        leaveTypeArabic = 'بدل';
        break;
      case 'unpaid':
        leaveTypeArabic = 'بدون أجر';
        break;
      case 'other':
        leaveTypeArabic = 'أخرى';
        break;
      default:
        leaveTypeArabic = leaveType;
    }

    // أولاً: فحص بنية الجدول لمعرفة أسماء الأعمدة الفعلية
    const columnCheckResult = await pool.request().query(`
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'LeaveBalances'
      AND COLUMN_NAME IN ('AnnualBalance', 'RegularBalance', 'CasualBalance')
      ORDER BY COLUMN_NAME
    `);

    // تحديد أسماء الأعمدة الصحيحة
    const hasAnnualBalance = columnCheckResult.recordset.some(col => col.COLUMN_NAME === 'AnnualBalance');
    const hasRegularBalance = columnCheckResult.recordset.some(col => col.COLUMN_NAME === 'RegularBalance');
    const hasCasualBalance = columnCheckResult.recordset.some(col => col.COLUMN_NAME === 'CasualBalance');

    // التحقق من وجود الموظف في جدول LeaveBalances
    const checkEmployeeResult = await pool.request()
      .input('employeeId', sql.NVarChar, employeeId)
      .query(`
        SELECT COUNT(*) as EmployeeExists
        FROM LeaveBalances
        WHERE EmployeeCode = @employeeId
      `);

    // إذا لم يكن الموظف موجود، أنشئ سجل له
    if (checkEmployeeResult.recordset[0].EmployeeExists === 0) {

      // جلب بيانات الموظف من جدول Employees
      const employeeInfoResult = await pool.request()
        .input('employeeId', sql.NVarChar, employeeId)
        .query(`
          SELECT TOP 1 EmployeeName, JobTitle, Department
          FROM Employees
          WHERE EmployeeCode = @employeeId
        `);

      if (employeeInfoResult.recordset.length > 0) {
        const empInfo = employeeInfoResult.recordset[0];

        // فحص وجود عمود JobTitle
        const jobTitleCheck = await pool.request().query(`
          SELECT COLUMN_NAME
          FROM INFORMATION_SCHEMA.COLUMNS
          WHERE TABLE_NAME = 'LeaveBalances' AND COLUMN_NAME = 'JobTitle'
        `);

        const hasJobTitle = jobTitleCheck.recordset.length > 0;

        // إنشاء سجل رصيد جديد بناءً على بنية الجدول
        if (hasAnnualBalance) {
          if (hasJobTitle) {
            await pool.request()
              .input('employeeCode', sql.NVarChar, employeeId)
              .input('employeeName', sql.NVarChar, empInfo.EmployeeName || 'غير محدد')
              .input('jobTitle', sql.NVarChar, empInfo.JobTitle || 'غير محدد')
              .input('department', sql.NVarChar, empInfo.Department || 'غير محدد')
              .query(`
                INSERT INTO LeaveBalances (EmployeeCode, EmployeeName, JobTitle, Department, AnnualBalance, CasualBalance)
                VALUES (@employeeCode, @employeeName, @jobTitle, @department, 15, 6)
              `);
          } else {
            await pool.request()
              .input('employeeCode', sql.NVarChar, employeeId)
              .input('employeeName', sql.NVarChar, empInfo.EmployeeName || 'غير محدد')
              .input('department', sql.NVarChar, empInfo.Department || 'غير محدد')
              .query(`
                INSERT INTO LeaveBalances (EmployeeCode, EmployeeName, Department, AnnualBalance, CasualBalance)
                VALUES (@employeeCode, @employeeName, @department, 15, 6)
              `);
          }
        } else {
          if (hasJobTitle) {
            await pool.request()
              .input('employeeCode', sql.NVarChar, employeeId)
              .input('employeeName', sql.NVarChar, empInfo.EmployeeName || 'غير محدد')
              .input('jobTitle', sql.NVarChar, empInfo.JobTitle || 'غير محدد')
              .input('department', sql.NVarChar, empInfo.Department || 'غير محدد')
              .query(`
                INSERT INTO LeaveBalances (EmployeeCode, EmployeeName, JobTitle, Department, RegularBalance, CasualBalance, UsedRegular, UsedCasual)
                VALUES (@employeeCode, @employeeName, @jobTitle, @department, 15, 6, 0, 0)
              `);
          } else {
            await pool.request()
              .input('employeeCode', sql.NVarChar, employeeId)
              .input('employeeName', sql.NVarChar, empInfo.EmployeeName || 'غير محدد')
              .input('department', sql.NVarChar, empInfo.Department || 'غير محدد')
              .query(`
                INSERT INTO LeaveBalances (EmployeeCode, EmployeeName, Department, RegularBalance, CasualBalance, UsedRegular, UsedCasual)
                VALUES (@employeeCode, @employeeName, @department, 15, 6, 0, 0)
              `);
          }
        }

      }
    }

    // بناء الاستعلام بناءً على الأعمدة الموجودة
    let balanceQuery = '';
    if (hasAnnualBalance) {
      // فحص وجود أعمدة Used
      const usedColumnsCheck = await pool.request().query(`
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'LeaveBalances'
        AND COLUMN_NAME IN ('UsedAnnual', 'UsedCasual')
        ORDER BY COLUMN_NAME
      `);

      const hasUsedColumns = usedColumnsCheck.recordset.length === 2;

      if (hasUsedColumns) {
        // استخدام الأعمدة الجديدة مع Used
        balanceQuery = `
          SELECT
            ISNULL(AnnualBalance, 15) as AnnualBalance,
            ISNULL(CasualBalance, 6) as CasualBalance,
            ISNULL(UsedAnnual, 0) as UsedRegular,
            ISNULL(UsedCasual, 0) as UsedCasual,
            ISNULL(AnnualBalance, 15) as RemainingRegular,
            ISNULL(CasualBalance, 6) as RemainingCasual
          FROM LeaveBalances
          WHERE EmployeeCode = @employeeId
        `;
      } else {
        // استخدام الأعمدة الأساسية فقط
        balanceQuery = `
          SELECT
            ISNULL(AnnualBalance, 15) as AnnualBalance,
            ISNULL(CasualBalance, 6) as CasualBalance,
            0 as UsedRegular,
            0 as UsedCasual,
            ISNULL(AnnualBalance, 15) as RemainingRegular,
            ISNULL(CasualBalance, 6) as RemainingCasual
          FROM LeaveBalances
          WHERE EmployeeCode = @employeeId
        `;
      }
    } else if (hasRegularBalance) {
      // استخدام AnnualBalance و CasualBalance (للتوافق مع الأنظمة القديمة)
      balanceQuery = `
        SELECT
          ISNULL(RegularBalance, 15) as AnnualBalance,
          ISNULL(CasualBalance, 6) as CasualBalance,
          ISNULL(UsedRegular, 0) as UsedRegular,
          ISNULL(UsedCasual, 0) as UsedCasual,
          (ISNULL(RegularBalance, 15) - ISNULL(UsedRegular, 0)) as RemainingRegular,
          (ISNULL(CasualBalance, 6) - ISNULL(UsedCasual, 0)) as RemainingCasual
        FROM LeaveBalances
        WHERE EmployeeCode = @employeeId
      `;
    } else {
      // جدول غير موجود أو بنية مختلفة

      throw new Error('بنية جدول LeaveBalances غير متوافقة');
    }

    // جلب رصيد الإجازات من جدول LeaveBalances
    const balanceResult = await pool.request()
      .input('employeeId', sql.NVarChar, employeeId)
      .query(balanceQuery);

    let balanceText = '';
    let remainingDays = 0;
    let baseBalance = 0;
    let usedDays = 0;

    if (balanceResult.recordset.length > 0) {
      const balance = balanceResult.recordset[0];

      switch (leaveTypeArabic) {
        case 'اعتيادية':
          if (hasAnnualBalance) {
            // استخدام AnnualBalance مباشرة
            remainingDays = balance.AnnualBalance; // استخدام AnnualBalance الصحيح
            baseBalance = balance.AnnualBalance;
            usedDays = 0; // لا يوجد عمود UsedRegular
            balanceText = `${remainingDays} يوم متبقي`;
          } else {
            // استخدام AnnualBalance مع UsedRegular
            remainingDays = balance.RemainingRegular;
            baseBalance = balance.AnnualBalance;
            usedDays = balance.UsedRegular;
            balanceText = `${remainingDays} يوم من أصل ${baseBalance}`;
          }
          break;
        case 'عارضة':
          if (hasAnnualBalance) {
            // استخدام CasualBalance مباشرة
            remainingDays = balance.CasualBalance;
            baseBalance = balance.CasualBalance;
            usedDays = 0;
            balanceText = `${remainingDays} يوم متبقي`;
          } else {
            // استخدام CasualBalance مع UsedCasual
            remainingDays = balance.RemainingCasual;
            baseBalance = balance.CasualBalance;
            usedDays = balance.UsedCasual;
            balanceText = `${remainingDays} يوم من أصل ${baseBalance}`;
          }
          break;
        case 'مرضية':
          balanceText = 'حسب التقرير الطبي (90 يوم سنوياً)';
          remainingDays = 90;
          baseBalance = 90;
          break;
        case 'بدل':
          balanceText = 'غير محدود (حسب أيام العمل الإضافية)';
          remainingDays = 'غير محدود';
          baseBalance = 'غير محدود';
          break;
        case 'بدون أجر':
          balanceText = 'غير محدود';
          remainingDays = 'غير محدود';
          baseBalance = 'غير محدود';
          break;
        default:
          balanceText = 'نوع إجازة غير محدد';
          remainingDays = 0;
          baseBalance = 0;
      }
    } else {
      // في حالة عدم وجود رصيد في الجدول، استخدم القيم الافتراضية
      switch (leaveTypeArabic) {
        case 'اعتيادية':
          balanceText = '15 يوم من أصل 15 (افتراضي)';
          remainingDays = 15;
          baseBalance = 15;
          break;
        case 'عارضة':
          balanceText = '6 أيام من أصل 6 (افتراضي)';
          remainingDays = 6;
          baseBalance = 6;
          break;
        case 'مرضية':
          balanceText = 'حسب التقرير الطبي (90 يوم سنوياً)';
          remainingDays = 90;
          baseBalance = 90;
          break;
        case 'بدل':
          balanceText = 'غير محدود (حسب أيام العمل الإضافية)';
          remainingDays = 'غير محدود';
          baseBalance = 'غير محدود';
          break;
        case 'بدون أجر':
          balanceText = 'غير محدود';
          remainingDays = 'غير محدود';
          baseBalance = 'غير محدود';
          break;
        default:
          balanceText = 'نوع إجازة غير محدد';
          remainingDays = 0;
          baseBalance = 0;
      }
    }

    return NextResponse.json({
      success: true,
      balance: balanceText,
      remainingDays,
      baseBalance,
      usedDays,
      leaveType: leaveTypeArabic,
      originalLeaveType: leaveType,
      hasBalanceRecord: balanceResult.recordset.length > 0
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في حساب الرصيد',
      details: error.message
    }, { status: 500 });
  }
}

// تحديث رصيد الإجازات
export async function POST(request) {
  try {
    const body = await request.json();
    const { action, employeeCode, annualBalance, casualBalance } = body;

    if (action !== 'update') {
      return NextResponse.json({
        success: false,
        error: 'Invalid action'
      }, { status: 400 });
    }

    if (!employeeCode || annualBalance === undefined || casualBalance === undefined) {
      return NextResponse.json({
        success: false,
        error: 'جميع البيانات مطلوبة'
      }, { status: 400 });
    }

    const pool = await sql.connect(dbConfig);

    // فحص بنية الجدول لمعرفة أسماء الأعمدة
    const columnCheckResult = await pool.request().query(`
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'LeaveBalances'
      AND COLUMN_NAME IN ('AnnualBalance', 'RegularBalance', 'CasualBalance')
      ORDER BY COLUMN_NAME
    `);

    const hasAnnualBalance = columnCheckResult.recordset.some(col => col.COLUMN_NAME === 'AnnualBalance');

    // التحقق من وجود الموظف في جدول LeaveBalances
    const checkEmployeeResult = await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .query(`
        SELECT COUNT(*) as EmployeeExists
        FROM LeaveBalances
        WHERE EmployeeCode = @employeeCode
      `);

    if (checkEmployeeResult.recordset[0].EmployeeExists === 0) {
      // إنشاء سجل جديد
      const employeeInfoResult = await pool.request()
        .input('employeeCode', sql.NVarChar, employeeCode)
        .query(`
          SELECT TOP 1 EmployeeName, JobTitle, Department
          FROM Employees
          WHERE EmployeeCode = @employeeCode
        `);

      if (employeeInfoResult.recordset.length > 0) {
        const empInfo = employeeInfoResult.recordset[0];

        if (hasAnnualBalance) {
          await pool.request()
            .input('employeeCode', sql.NVarChar, employeeCode)
            .input('employeeName', sql.NVarChar, empInfo.EmployeeName || 'غير محدد')
            .input('jobTitle', sql.NVarChar, empInfo.JobTitle || 'غير محدد')
            .input('department', sql.NVarChar, empInfo.Department || 'غير محدد')
            .input('annualBalance', sql.Int, annualBalance)
            .input('casualBalance', sql.Int, casualBalance)
            .query(`
              INSERT INTO LeaveBalances (EmployeeCode, EmployeeName, JobTitle, Department, AnnualBalance, CasualBalance)
              VALUES (@employeeCode, @employeeName, @jobTitle, @department, @annualBalance, @casualBalance)
            `);
        } else {
          await pool.request()
            .input('employeeCode', sql.NVarChar, employeeCode)
            .input('employeeName', sql.NVarChar, empInfo.EmployeeName || 'غير محدد')
            .input('jobTitle', sql.NVarChar, empInfo.JobTitle || 'غير محدد')
            .input('department', sql.NVarChar, empInfo.Department || 'غير محدد')
            .input('annualBalance', sql.Int, annualBalance)
            .input('casualBalance', sql.Int, casualBalance)
            .query(`
              INSERT INTO LeaveBalances (EmployeeCode, EmployeeName, JobTitle, Department, RegularBalance, CasualBalance)
              VALUES (@employeeCode, @employeeName, @jobTitle, @department, @annualBalance, @casualBalance)
            `);
        }
      }
    } else {
      // تحديث السجل الموجود
      if (hasAnnualBalance) {
        await pool.request()
          .input('employeeCode', sql.NVarChar, employeeCode)
          .input('annualBalance', sql.Int, annualBalance)
          .input('casualBalance', sql.Int, casualBalance)
          .query(`
            UPDATE LeaveBalances
            SET AnnualBalance = @annualBalance, CasualBalance = @casualBalance
            WHERE EmployeeCode = @employeeCode
          `);
      } else {
        await pool.request()
          .input('employeeCode', sql.NVarChar, employeeCode)
          .input('annualBalance', sql.Int, annualBalance)
          .input('casualBalance', sql.Int, casualBalance)
          .query(`
            UPDATE LeaveBalances
            SET RegularBalance = @annualBalance, CasualBalance = @casualBalance
            WHERE EmployeeCode = @employeeCode
          `);
      }
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث رصيد الإجازات بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في تحديث الرصيد'
    }, { status: 500 });
  }
}
