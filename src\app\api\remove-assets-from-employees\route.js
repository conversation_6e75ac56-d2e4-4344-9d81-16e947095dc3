import { NextResponse } from 'next/server';
import sql from 'mssql';

// إعدادات قاعدة البيانات
const config = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  server: process.env.DB_SERVER,
  database: process.env.DB_NAME,
  options: {
    encrypt: true,
    trustServerCertificate: true,
  },
};

export async function POST(request) {
  let pool;
  
  try {

    // الاتصال بقاعدة البيانات
    pool = await sql.connect(config);

    const results = [];
    const fieldsToRemove = [
      // حقول السكن والشقق
      'CompanyHousing',
      'codeHousing', 
      'HousingCode',
      'IsResidentEmployee',
      
      // حقول المواصلات والسيارات
      'TransportMethod',
      'VehicleCode'
    ];

    // 1. فحص الحقول الموجودة

    const existingFields = await pool.request().query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'Employees'
      AND COLUMN_NAME IN (${fieldsToRemove.map(field => `'${field}'`).join(', ')})
      ORDER BY ORDINAL_POSITION
    `);

    if (existingFields.recordset.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'لا توجد حقول تحتاج إلى حذف',
        details: {
          fieldsToRemove: fieldsToRemove,
          existingFields: [],
          removedFields: []
        }
      });
    }

    // 2. عرض البيانات قبل الحذف (للمراجعة)

    const sampleDataQuery = `
      SELECT TOP 10 
        EmployeeCode,
        EmployeeName,
        ${existingFields.recordset.map(field => field.COLUMN_NAME).join(', ')}
      FROM Employees
      WHERE ${existingFields.recordset.map(field => `${field.COLUMN_NAME} IS NOT NULL`).join(' OR ')}
      ORDER BY EmployeeCode
    `;
    
    const sampleData = await pool.request().query(sampleDataQuery);
    
    results.push({
      step: 'عينة البيانات قبل الحذف',
      count: sampleData.recordset.length,
      data: sampleData.recordset
    });

    // 3. إحصائيات البيانات المتأثرة

    const statsQueries = [];
    for (const field of existingFields.recordset) {
      const fieldName = field.COLUMN_NAME;
      const countQuery = `SELECT COUNT(*) as count FROM Employees WHERE ${fieldName} IS NOT NULL AND ${fieldName} != ''`;
      const countResult = await pool.request().query(countQuery);
      
      statsQueries.push({
        field: fieldName,
        recordsWithData: countResult.recordset[0].count
      });
    }

    results.push({
      step: 'إحصائيات البيانات المتأثرة',
      stats: statsQueries
    });

    // 4. حذف القيود المرتبطة بالحقول

    const removedConstraints = [];
    for (const field of existingFields.recordset) {
      const fieldName = field.COLUMN_NAME;
      
      // البحث عن القيود المرتبطة بالحقل
      const constraintsQuery = `
        SELECT 
          dc.name as constraint_name,
          dc.definition
        FROM sys.default_constraints dc
        INNER JOIN sys.columns c ON dc.parent_column_id = c.column_id
        WHERE dc.parent_object_id = OBJECT_ID('Employees') 
        AND c.name = '${fieldName}'
      `;
      
      const constraints = await pool.request().query(constraintsQuery);
      
      for (const constraint of constraints.recordset) {
        try {
          await pool.request().query(`ALTER TABLE Employees DROP CONSTRAINT ${constraint.constraint_name}`);
          removedConstraints.push({
            field: fieldName,
            constraint: constraint.constraint_name,
            definition: constraint.definition
          });

        } catch (constraintError) {

        }
      }
    }

    results.push({
      step: 'إزالة القيود',
      removedConstraints: removedConstraints
    });

    // 5. حذف الحقول

    const removedFields = [];
    for (const field of existingFields.recordset) {
      const fieldName = field.COLUMN_NAME;
      
      try {
        await pool.request().query(`ALTER TABLE Employees DROP COLUMN ${fieldName}`);
        removedFields.push({
          field: fieldName,
          dataType: field.DATA_TYPE,
          wasNullable: field.IS_NULLABLE,
          defaultValue: field.COLUMN_DEFAULT
        });

      } catch (fieldError) {

        results.push({
          step: 'خطأ في حذف الحقل',
          field: fieldName,
          error: fieldError.message
        });
      }
    }

    results.push({
      step: 'حذف الحقول',
      removedFields: removedFields
    });

    // 6. التحقق من النتيجة النهائية

    const finalStructure = await pool.request().query(`
      SELECT 
        COLUMN_NAME as 'اسم الحقل',
        DATA_TYPE as 'نوع البيانات',
        IS_NULLABLE as 'يقبل NULL',
        COLUMN_DEFAULT as 'القيمة الافتراضية'
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'Employees'
      ORDER BY ORDINAL_POSITION
    `);

    results.push({
      step: 'بنية الجدول النهائية',
      totalFields: finalStructure.recordset.length,
      structure: finalStructure.recordset
    });

    // 7. التحقق من عدم وجود الحقول المحذوفة
    const remainingAssetFields = await pool.request().query(`
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'Employees'
      AND COLUMN_NAME IN (${fieldsToRemove.map(field => `'${field}'`).join(', ')})
    `);

    const summary = {
      totalFieldsToRemove: fieldsToRemove.length,
      fieldsFound: existingFields.recordset.length,
      fieldsRemoved: removedFields.length,
      constraintsRemoved: removedConstraints.length,
      remainingAssetFields: remainingAssetFields.recordset.length,
      success: remainingAssetFields.recordset.length === 0
    };

    return NextResponse.json({
      success: summary.success,
      message: summary.success 
        ? 'تم حذف جميع حقول الشقق والسيارات من جدول الموظفين بنجاح'
        : 'تم حذف بعض الحقول ولكن هناك حقول متبقية',
      summary: summary,
      results: results,
      details: {
        fieldsToRemove: fieldsToRemove,
        removedFields: removedFields.map(f => f.field),
        remainingFields: remainingAssetFields.recordset.map(f => f.COLUMN_NAME),
        recommendations: [
          'تأكد من أن جداول المستفيدين تحتوي على جميع البيانات المطلوبة',
          'راجع الاستعلامات التي تستخدم الحقول المحذوفة وحدثها',
          'اختبر جميع الوظائف المتعلقة بالشقق والسيارات',
          'تأكد من أن واجهات المستخدم تعتمد على جداول المستفيدين'
        ]
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'فشل في حذف حقول الشقق والسيارات',
      error: error.message
    }, { status: 500 });
    
  } finally {
    if (pool) {
      await pool.close();

    }
  }
}

// GET - فحص الحقول بدون حذف
export async function GET(request) {
  let pool;
  
  try {

    pool = await sql.connect(config);
    
    const fieldsToCheck = [
      'CompanyHousing', 'codeHousing', 'HousingCode', 'IsResidentEmployee',
      'TransportMethod', 'VehicleCode'
    ];

    // فحص الحقول الموجودة
    const existingFields = await pool.request().query(`
      SELECT 
        COLUMN_NAME,
        DATA_TYPE,
        IS_NULLABLE,
        COLUMN_DEFAULT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'Employees'
      AND COLUMN_NAME IN (${fieldsToCheck.map(field => `'${field}'`).join(', ')})
      ORDER BY ORDINAL_POSITION
    `);

    // إحصائيات البيانات
    const dataStats = [];
    for (const field of existingFields.recordset) {
      const fieldName = field.COLUMN_NAME;
      const countQuery = `SELECT COUNT(*) as total, COUNT(${fieldName}) as withData FROM Employees`;
      const countResult = await pool.request().query(countQuery);
      
      dataStats.push({
        field: fieldName,
        dataType: field.DATA_TYPE,
        totalRecords: countResult.recordset[0].total,
        recordsWithData: countResult.recordset[0].withData,
        percentageWithData: ((countResult.recordset[0].withData / countResult.recordset[0].total) * 100).toFixed(2)
      });
    }

    return NextResponse.json({
      success: true,
      fieldsToRemove: fieldsToCheck,
      existingFields: existingFields.recordset,
      dataStatistics: dataStats,
      summary: {
        totalFieldsToCheck: fieldsToCheck.length,
        existingFieldsCount: existingFields.recordset.length,
        fieldsToRemove: existingFields.recordset.map(f => f.COLUMN_NAME)
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'فشل في فحص الحقول',
      error: error.message
    }, { status: 500 });
    
  } finally {
    if (pool) {
      await pool.close();
    }
  }
}
