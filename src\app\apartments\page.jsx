'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import DateInput from '@/components/DateInput';
import {
  FiHome,
  FiPlus,
  FiEdit,
  FiTrash2,
  FiUsers,
  FiCalendar,
  FiDollarSign,
  FiMapPin,
  FiSearch,
  FiFilter,
  FiRefreshCw,
  FiEye,
  FiUserPlus,
  FiUserMinus,
  FiActivity,
  FiShield
} from 'react-icons/fi';

export default function ApartmentsPage() {
  const [apartments, setApartments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [showBeneficiaryModal, setShowBeneficiaryModal] = useState(false);
  const [showBeneficiariesListModal, setShowBeneficiariesListModal] = useState(false);
  const [editingApartment, setEditingApartment] = useState(null);
  const [selectedApartment, setSelectedApartment] = useState(null);
  const [statistics, setStatistics] = useState(null);
  const [editingInlineId, setEditingInlineId] = useState(null);
  const [inlineEditData, setInlineEditData] = useState({});

  const [formData, setFormData] = useState({
    apartmentCode: '',
    landlordName: '',
    address: '',
    startDate: '',
    endDate: '',
    rentAmount: '',
    insuranceAmount: '',
    commissionAmount: '',
    backlogAmount: '',
    notes: '',
    beneficiaries: []
  });

  const [beneficiaryForm, setBeneficiaryForm] = useState({
    employeeCode: '',
    startDate: ''
  });

  const [employeeSearchResults, setEmployeeSearchResults] = useState([]);
  const [showEmployeeSearch, setShowEmployeeSearch] = useState(false);
  const [employeeSearchTerm, setEmployeeSearchTerm] = useState('');

  useEffect(() => {
    loadApartments();
    loadStatistics();
  }, []);

  // جلب الشقق
  const loadApartments = async () => {
    try {
      setLoading(true);

      // بناء URL مع معاملات البحث
      const params = new URLSearchParams({
        action: 'getAll',
        includeInactive: 'false'
      });

      if (searchTerm && searchTerm.trim()) {
        params.append('search', searchTerm.trim());
      }

      const response = await fetch(`/api/apartments?${params.toString()}`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });

      const result = await response.json();
      if (result.success) {
        setApartments(result.data || []);

      } else {

        setApartments([]);
      }
    } catch (error) {

      setApartments([]);
    } finally {
      setLoading(false);
    }
  };

  // جلب الإحصائيات
  const loadStatistics = async () => {
    try {
      const response = await fetch('/api/apartments?action=getStatistics', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });

      const result = await response.json();
      if (result.success) {
        setStatistics(result.data);

      } else {

      }
    } catch (error) {

    }
  };

  // إعداد النظام
  const setupSystem = async () => {
    try {
      const response = await fetch('/api/apartments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'setup' })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم إعداد نظام الشقق بنجاح');
        loadApartments();
        loadStatistics();
      } else {
        alert('خطأ في إعداد النظام: ' + result.error);
      }
    } catch (error) {
      alert('خطأ في إعداد النظام: ' + error.message);
    }
  };

  // حفظ الشقة
  const saveApartment = async (e) => {
    e.preventDefault();

    try {
      const action = editingApartment ? 'update' : 'create';
      const payload = {
        action,
        ...formData,
        ...(editingApartment && { apartmentId: editingApartment.ID })
      };

      const response = await fetch('/api/apartments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      const result = await response.json();
      if (result.success) {
        alert(editingApartment ? 'تم تحديث الشقة بنجاح' : 'تم إنشاء الشقة بنجاح');
        setShowModal(false);
        resetForm();
        loadApartments();
        loadStatistics();
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {
      alert('خطأ في حفظ الشقة: ' + error.message);
    }
  };

  // حذف الشقة
  const deleteApartment = async (apartmentId) => {
    if (!confirm('هل أنت متأكد من حذف هذه الشقة؟')) return;

    try {
      const response = await fetch('/api/apartments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'delete',
          apartmentId
        })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم حذف الشقة بنجاح');
        loadApartments();
        loadStatistics();
      } else {
        alert('خطأ في حذف الشقة: ' + result.error);
      }
    } catch (error) {
      alert('خطأ في حذف الشقة: ' + error.message);
    }
  };

  // إضافة مستفيد
  const addBeneficiary = async (e) => {
    e.preventDefault();

    // التحقق من صحة البيانات قبل الإرسال
    if (!beneficiaryForm.employeeCode || beneficiaryForm.employeeCode.trim() === '') {
      alert('يرجى اختيار موظف من قائمة البحث');

      return;
    }

    if (!beneficiaryForm.startDate) {
      alert('يرجى تحديد تاريخ بداية الاستفادة');

      return;
    }

    if (!selectedApartment || !selectedApartment.ID) {
      alert('خطأ: لم يتم تحديد الشقة بشكل صحيح');

      return;
    }

    try {
      const requestData = {
        action: 'addBeneficiary',
        apartmentId: selectedApartment.ID,
        employeeCode: beneficiaryForm.employeeCode.trim(),
        startDate: beneficiaryForm.startDate
      };

      const response = await fetch('/api/apartments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData)
      });

      const result = await response.json();

      if (result.success) {
        alert('تم إضافة المستفيد بنجاح');
        setShowBeneficiaryModal(false);
        resetBeneficiaryForm();

        // تحديث البيانات فوراً
        await loadApartments();
        await loadStatistics();

        // إعادة تحميل الصفحة للتأكد من ظهور البيانات
        setTimeout(() => {
          window.location.reload();
        }, 1000);

      } else {
        // عرض رسالة خطأ مفصلة
        const errorMessage = result.error || 'حدث خطأ غير معروف';
        alert(`فشل في إضافة المستفيد:\n${errorMessage}`);

      }
    } catch (error) {

      alert('خطأ في الاتصال بالخادم. يرجى المحاولة مرة أخرى.');
    }
  };

  // إزالة مستفيد
  const removeBeneficiary = async (beneficiaryId) => {
    if (!confirm('هل أنت متأكد من إزالة هذا المستفيد؟')) return;

    try {
      const response = await fetch('/api/apartments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'removeBeneficiary',
          beneficiaryId
        })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم إزالة المستفيد بنجاح');
        loadApartments();
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {
      alert('خطأ في إزالة المستفيد: ' + error.message);
    }
  };

  // البحث عن الموظفين
  const searchEmployees = async (searchTerm) => {
    if (!searchTerm || searchTerm.length < 2) {
      setEmployeeSearchResults([]);
      return;
    }

    try {
      const response = await fetch('/api/employee-live-search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          searchTerm: searchTerm,
          limit: 10
        })
      });

      const result = await response.json();
      if (result.success) {
        setEmployeeSearchResults(result.data || []);
      } else {

        setEmployeeSearchResults([]);
      }
    } catch (error) {

      setEmployeeSearchResults([]);
    }
  };

  // اختيار موظف من نتائج البحث
  const selectEmployee = (employee) => {
    setBeneficiaryForm(prev => ({
      ...prev,
      employeeCode: employee.employeeCode || employee.EmployeeCode || employee.EmployeeID || employee.ID
    }));
    setEmployeeSearchTerm(employee.employeeName || employee.EmployeeName || employee.fullName || employee.FullName || employee.Name || '');
    setShowEmployeeSearch(false);
    setEmployeeSearchResults([]);
  };

  // إعادة تعيين نموذج المستفيد
  const resetBeneficiaryForm = () => {
    setBeneficiaryForm({ employeeCode: '', startDate: '' });
    setEmployeeSearchTerm('');
    setEmployeeSearchResults([]);
    setShowEmployeeSearch(false);
  };

  // إعادة تعيين النموذج
  const resetForm = () => {
    setFormData({
      apartmentCode: '',
      landlordName: '',
      address: '',
      startDate: '',
      endDate: '',
      rentAmount: '',
      insuranceAmount: '',
      commissionAmount: '',
      backlogAmount: '',
      notes: '',
      beneficiaries: []
    });
    setEditingApartment(null);
  };

  // فتح نموذج التعديل
  const openEditModal = (apartment) => {
    setFormData({
      apartmentCode: apartment.ApartmentCode,
      landlordName: apartment.LandlordName,
      address: apartment.Address,
      startDate: apartment.StartDate ? apartment.StartDate.split('T')[0] : '',
      endDate: apartment.EndDate ? apartment.EndDate.split('T')[0] : '',
      rentAmount: apartment.RentAmount,
      insuranceAmount: apartment.InsuranceAmount,
      commissionAmount: apartment.CommissionAmount,
      backlogAmount: apartment.BacklogAmount,
      notes: apartment.Notes || '',
      beneficiaries: apartment.beneficiaries || []
    });
    setEditingApartment(apartment);
    setShowModal(true);
  };

  // تنسيق العملة
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(amount || 0);
  };

  // تنسيق التاريخ
  const formatDate = (dateString) => {
    if (!dateString) return 'غير محدد';
    return new Date(dateString).toLocaleDateString('ar-EG');
  };

  // تحويل التاريخ من DD/MM/YYYY إلى YYYY-MM-DD للحسابات
  const convertDateForCalculation = (dateStr) => {
    if (!dateStr || dateStr === 'undefined' || dateStr === 'null') return '';
    if (typeof dateStr === 'string' && dateStr.includes('/')) {
      const parts = dateStr.split('/');
      if (parts.length === 3) {
        const [day, month, year] = parts;
        if (day && month && year) {
          return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        }
      }
    }
    return dateStr;
  };

  // تحويل التاريخ من YYYY-MM-DD إلى DD/MM/YYYY للعرض
  const convertDateForDisplay = (dateStr) => {
    if (!dateStr || dateStr === 'undefined' || dateStr === 'null') return '';
    if (typeof dateStr === 'string' && dateStr.includes('-') && dateStr.length === 10) {
      const parts = dateStr.split('-');
      if (parts.length === 3) {
        const [year, month, day] = parts;
        if (year && month && day) {
          return `${day}/${month}/${year}`;
        }
      }
    }
    return dateStr;
  };

  // بدء التعديل المباشر
  const startInlineEdit = (apartment) => {
    setEditingInlineId(apartment.ID);
    setInlineEditData({
      apartmentCode: apartment.ApartmentCode,
      landlordName: apartment.LandlordName,
      address: apartment.Address,
      startDate: apartment.StartDate ? apartment.StartDate.split('T')[0] : '',
      endDate: apartment.EndDate ? apartment.EndDate.split('T')[0] : '',
      rentAmount: apartment.RentAmount,
      insuranceAmount: apartment.InsuranceAmount,
      commissionAmount: apartment.CommissionAmount,
      backlogAmount: apartment.BacklogAmount,
      notes: apartment.Notes || ''
    });
  };

  // إلغاء التعديل المباشر
  const cancelInlineEdit = () => {
    setEditingInlineId(null);
    setInlineEditData({});
  };

  // حفظ التعديل المباشر
  const saveInlineEdit = async () => {
    try {
      const response = await fetch('/api/apartments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'update',
          apartmentId: editingInlineId,
          ...inlineEditData
        })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم تحديث الشقة بنجاح');
        setEditingInlineId(null);
        setInlineEditData({});
        loadApartments();
        loadStatistics();
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {
      alert('خطأ في حفظ التعديل: ' + error.message);
    }
  };

  // معالجة تغيير التاريخ
  const handleDateChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleBeneficiaryDateChange = (e) => {
    const { name, value } = e.target;
    setBeneficiaryForm(prev => ({ ...prev, [name]: value }));
  };

  const handleInlineEditChange = (field, value) => {
    setInlineEditData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <MainLayout>
      <div className="w-full px-4">
        {/* رأس الصفحة */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FiHome className="text-3xl text-purple-600" />
              <div>
                <h1 className="text-3xl font-bold text-gray-800">إدارة الشقق</h1>
                <p className="text-gray-600">إدارة شقق الشركة والمستفيدين</p>
              </div>
            </div>
            <div className="flex gap-3">
              <button
                onClick={setupSystem}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
              >
                <FiRefreshCw />
                إعداد النظام
              </button>
              <button
                onClick={() => {
                  resetForm();
                  setShowModal(true);
                }}
                className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center gap-2"
              >
                <FiPlus />
                إضافة شقة جديدة
              </button>
            </div>
          </div>
        </div>

        {/* الإحصائيات */}
        {statistics && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-purple-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي الشقق النشطة</p>
                  <p className="text-3xl font-bold text-purple-600">{statistics.TotalActiveApartments || 0}</p>
                </div>
                <FiHome className="text-2xl text-purple-600" />
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-green-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي المستفيدين</p>
                  <p className="text-3xl font-bold text-green-600">{statistics.TotalActiveBeneficiaries || 0}</p>
                </div>
                <FiUsers className="text-2xl text-green-600" />
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-blue-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي الإيجار الشهري</p>
                  <p className="text-2xl font-bold text-blue-600">{formatCurrency(statistics.TotalMonthlyRent)}</p>
                </div>
                <FiDollarSign className="text-2xl text-blue-600" />
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-orange-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">شقق تنتهي قريباً</p>
                  <p className="text-3xl font-bold text-orange-600">{statistics.ApartmentsExpiringSoon || 0}</p>
                </div>
                <FiCalendar className="text-2xl text-orange-600" />
              </div>
            </div>
          </div>
        )}

        {/* شريط البحث والفلترة */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <div className="relative">
                <FiSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="البحث في الشقق (كود، مؤجر، عنوان)..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-purple-500 focus:border-purple-500"
                />
              </div>
            </div>
            <button
              onClick={loadApartments}
              className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center gap-2"
            >
              <FiSearch />
              بحث
            </button>
            <button
              onClick={() => {
                setSearchTerm('');
                loadApartments();
              }}
              className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center gap-2"
            >
              <FiRefreshCw />
              إعادة تعيين
            </button>
          </div>
        </div>

        {/* جدول الشقق */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-800">قائمة الشقق</h3>
          </div>

          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto"></div>
              <p className="text-gray-600 mt-4">جاري تحميل الشقق...</p>
            </div>
          ) : apartments.length === 0 ? (
            <div className="text-center py-12">
              <FiHome className="text-6xl text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-medium text-gray-600 mb-2">لا توجد شقق</h3>
              <p className="text-gray-500 mb-4">لم يتم العثور على شقق مطابقة للبحث</p>
              <button
                onClick={() => {
                  resetForm();
                  setShowModal(true);
                }}
                className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700"
              >
                إضافة أول شقة
              </button>
            </div>
          ) : (
            <div className="w-full overflow-x-auto">
              <table className="w-full divide-y divide-gray-200 table-auto table-no-truncate">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px]">
                      كود الشقة
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[150px]">
                      اسم المؤجر
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[200px]">
                      العنوان
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[300px]">
                      المستفيدين
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                      الإيجار الشهري
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                      تاريخ البداية
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                      تاريخ النهاية
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[80px]">
                      الحالة
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px]">
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {apartments.map((apartment) => (
                    <tr key={apartment.ID} className="hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {apartment.ApartmentCode}
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-900">
                        {editingInlineId === apartment.ID ? (
                          <input
                            type="text"
                            value={inlineEditData.landlordName || ''}
                            onChange={(e) => handleInlineEditChange('landlordName', e.target.value)}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                          />
                        ) : (
                          <div className="break-words">
                            <span title={apartment.LandlordName} className="font-medium text-blue-700">
                              {apartment.LandlordName || 'غير محدد'}
                            </span>
                          </div>
                        )}
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-900">
                        <div className="break-words" title={apartment.Address}>
                          {apartment.Address}
                        </div>
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-900">
                        <div className="space-y-1">
                          {/* عرض المستفيدين النشطين */}
                          {apartment.beneficiaries && apartment.beneficiaries.filter(b => b.IsActive).length > 0 ? (
                            <div className="flex flex-wrap gap-1">
                              {apartment.beneficiaries.filter(b => b.IsActive).map((beneficiary, index) => (
                                <div key={beneficiary.ID} className="flex items-center gap-1 text-xs bg-gray-100 px-2 py-1 rounded-full border border-gray-300 shadow-sm">
                                  <span className="bg-blue-600 text-white px-1.5 py-0.5 rounded-full font-bold text-xs">
                                    {beneficiary.EmployeeCode}
                                  </span>
                                  <span className="font-bold text-gray-800 text-xs break-words" title={beneficiary.EmployeeName || beneficiary.FullName || 'اسم غير محدد'}>
                                    {beneficiary.EmployeeName || beneficiary.FullName || 'اسم غير محدد'}
                                  </span>
                                  <button
                                    onClick={() => removeBeneficiary(beneficiary.ID)}
                                    className="text-red-600 hover:text-red-800 p-0.5 rounded-full hover:bg-red-100"
                                    title="إزالة المستفيد"
                                  >
                                    <FiUserMinus className="w-3 h-3" />
                                  </button>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <span className="text-gray-400 text-xs">لا يوجد مستفيدين</span>
                          )}

                          {/* أزرار الإجراءات */}
                          <div className="flex gap-1 mt-1">
                            <button
                              onClick={() => {
                                setSelectedApartment(apartment);
                                resetBeneficiaryForm();
                                setShowBeneficiaryModal(true);
                              }}
                              className="text-blue-600 hover:text-blue-900 text-xs"
                              title="إضافة مستفيد"
                            >
                              <FiUserPlus />
                            </button>
                            {apartment.beneficiaries && apartment.beneficiaries.length > 0 && (
                              <button
                                onClick={() => {
                                  setSelectedApartment(apartment);
                                  setShowBeneficiariesListModal(true);
                                }}
                                className="text-green-600 hover:text-green-900 text-xs"
                                title="عرض جميع المستفيدين"
                              >
                                <FiEye />
                              </button>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        {editingInlineId === apartment.ID ? (
                          <input
                            type="number"
                            step="0.01"
                            min="0"
                            value={inlineEditData.rentAmount || ''}
                            onChange={(e) => handleInlineEditChange('rentAmount', e.target.value)}
                            className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                          />
                        ) : (
                          <div className="break-words" title={formatCurrency(apartment.RentAmount)}>
                            {formatCurrency(apartment.RentAmount)}
                          </div>
                        )}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div className="break-words">
                          {formatDate(apartment.StartDate)}
                        </div>
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-900">
                        <div className="break-words">
                          {formatDate(apartment.EndDate)}
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          apartment.ContractStatus === 'ساري'
                            ? 'bg-green-100 text-green-800'
                            : apartment.ContractStatus === 'منتهي'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-blue-100 text-blue-800'
                        }`}>
                          {apartment.ContractStatus}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex gap-2">
                          {editingInlineId === apartment.ID ? (
                            <>
                              <button
                                onClick={saveInlineEdit}
                                className="text-green-600 hover:text-green-900"
                                title="حفظ التعديل"
                              >
                                ✓
                              </button>
                              <button
                                onClick={cancelInlineEdit}
                                className="text-gray-600 hover:text-gray-900"
                                title="إلغاء التعديل"
                              >
                                ✕
                              </button>
                            </>
                          ) : (
                            <>
                              <button
                                onClick={() => startInlineEdit(apartment)}
                                className="text-blue-600 hover:text-blue-900"
                                title="تعديل مباشر"
                              >
                                <FiEdit />
                              </button>
                              <button
                                onClick={() => openEditModal(apartment)}
                                className="text-purple-600 hover:text-purple-900"
                                title="تعديل في نافذة"
                              >
                                <FiEye />
                              </button>
                              <button
                                onClick={() => deleteApartment(apartment.ID)}
                                className="text-red-600 hover:text-red-900"
                                title="حذف"
                              >
                                <FiTrash2 />
                              </button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>

                {/* صف الإجماليات - يظهر فقط عندما توجد شقق */}
                {apartments.length > 0 && (
                <tfoot className="bg-gradient-to-r from-purple-100 to-blue-100 dark:from-gray-800 dark:to-gray-700 border-t-4 border-purple-500 dark:border-purple-400 shadow-lg">
                  <tr className="font-bold text-gray-800 dark:text-gray-200 hover:bg-gradient-to-r hover:from-purple-200 hover:to-blue-200 dark:hover:from-gray-700 dark:hover:to-gray-600 transition-all duration-300">
                    <td className="px-4 py-5 text-base font-bold text-purple-800 dark:text-purple-300 bg-purple-200 dark:bg-gray-700 rounded-r-lg">
                      <div className="flex items-center gap-2">
                        <FiActivity className="text-lg animate-pulse" />
                        <span className="text-lg">الإجمالي</span>
                      </div>
                    </td>
                    <td className="px-4 py-5 text-sm font-bold text-blue-700 dark:text-blue-300 bg-blue-100 dark:bg-gray-600">
                      <div className="flex items-center gap-2 justify-center">
                        <FiHome className="text-lg text-blue-600 dark:text-blue-400" />
                        <span className="text-base">{apartments.filter(apt => apt.IsActive).length}</span>
                        <span className="text-xs text-blue-600 dark:text-blue-400">شقة نشطة</span>
                      </div>
                    </td>
                    <td className="px-4 py-5 text-sm text-gray-500 dark:text-gray-400 text-center bg-gray-100 dark:bg-gray-600">—</td>
                    <td className="px-4 py-5 text-sm text-gray-500 dark:text-gray-400 text-center bg-gray-100 dark:bg-gray-600">—</td>
                    <td className="px-4 py-5 text-sm font-bold text-green-700 dark:text-green-300 bg-green-100 dark:bg-gray-600">
                      <div className="flex items-center gap-2 justify-center">
                        <FiDollarSign className="text-lg text-green-600 dark:text-green-400" />
                        <span className="text-base">
                          {formatCurrency(
                            apartments
                              .filter(apt => apt.IsActive)
                              .reduce((total, apt) => total + (apt.RentAmount || 0), 0)
                          )}
                        </span>
                      </div>
                    </td>
                    <td className="px-4 py-5 text-sm font-bold text-orange-700 dark:text-orange-300 bg-orange-100 dark:bg-gray-600">
                      <div className="flex items-center gap-2 justify-center">
                        <FiUsers className="text-lg text-orange-600 dark:text-orange-400" />
                        <span className="text-base">
                          {apartments
                            .filter(apt => apt.IsActive)
                            .reduce((total, apt) => total + (apt.ActiveBeneficiariesCount || 0), 0)}
                        </span>
                        <span className="text-xs text-orange-600 dark:text-orange-400">مستفيد</span>
                      </div>
                    </td>
                    <td className="px-4 py-5 text-sm text-gray-500 dark:text-gray-400 text-center bg-gray-100 dark:bg-gray-600">—</td>
                    <td className="px-4 py-5 text-sm text-gray-500 dark:text-gray-400 text-center bg-gray-100 dark:bg-gray-600">—</td>
                    <td className="px-4 py-5 text-sm text-gray-500 dark:text-gray-400 text-center bg-gray-100 dark:bg-gray-600 rounded-l-lg">—</td>
                  </tr>
                </tfoot>
                )}
              </table>
            </div>
          )}
        </div>

        {/* مودال إضافة/تعديل الشقة */}
        {showModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between p-6 border-b">
                <h3 className="text-xl font-bold text-gray-800">
                  {editingApartment ? 'تعديل الشقة' : 'إضافة شقة جديدة'}
                </h3>
                <button
                  onClick={() => setShowModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <form onSubmit={saveApartment} className="p-6 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      كود الشقة *
                    </label>
                    <input
                      type="text"
                      value={formData.apartmentCode}
                      onChange={(e) => setFormData(prev => ({ ...prev, apartmentCode: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                      placeholder="مثال: APT-001"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      اسم المؤجر *
                    </label>
                    <input
                      type="text"
                      value={formData.landlordName}
                      onChange={(e) => setFormData(prev => ({ ...prev, landlordName: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                      placeholder="اسم مالك الشقة"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    العنوان *
                  </label>
                  <textarea
                    value={formData.address}
                    onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                    rows="3"
                    placeholder="العنوان الكامل للشقة"
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <DateInput
                      name="startDate"
                      value={convertDateForDisplay(formData.startDate)}
                      onChange={(e) => {
                        const value = e.target.value;
                        const convertedDate = convertDateForCalculation(value);
                        setFormData(prev => ({ ...prev, startDate: convertedDate }));
                      }}
                      label="تاريخ بداية الإيجار *"
                      placeholder="DD/MM/YYYY"
                      isArabic={true}
                      required={true}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                    />
                  </div>

                  <div>
                    <DateInput
                      name="endDate"
                      value={convertDateForDisplay(formData.endDate)}
                      onChange={(e) => {
                        const value = e.target.value;
                        const convertedDate = convertDateForCalculation(value);
                        setFormData(prev => ({ ...prev, endDate: convertedDate }));
                      }}
                      label="تاريخ نهاية الإيجار"
                      placeholder="DD/MM/YYYY"
                      isArabic={true}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      قيمة الإيجار (ج.م) *
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.rentAmount}
                      onChange={(e) => setFormData(prev => ({ ...prev, rentAmount: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                      placeholder="0.00"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      قيمة التأمين (ج.م)
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.insuranceAmount}
                      onChange={(e) => setFormData(prev => ({ ...prev, insuranceAmount: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      قيمة العمولة (ج.م)
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.commissionAmount}
                      onChange={(e) => setFormData(prev => ({ ...prev, commissionAmount: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                      placeholder="0.00"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      قيمة الأثر الراجعي (ج.م)
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.backlogAmount}
                      onChange={(e) => setFormData(prev => ({ ...prev, backlogAmount: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                      placeholder="0.00"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    ملاحظات
                  </label>
                  <textarea
                    value={formData.notes}
                    onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                    rows="3"
                    placeholder="ملاحظات إضافية..."
                  />
                </div>

                <div className="flex gap-3 pt-4 border-t">
                  <button
                    type="submit"
                    className="bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 flex items-center gap-2"
                  >
                    <FiHome />
                    {editingApartment ? 'تحديث الشقة' : 'إضافة الشقة'}
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowModal(false)}
                    className="bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400"
                  >
                    إلغاء
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* مودال إضافة مستفيد */}
        {showBeneficiaryModal && selectedApartment && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
              <div className="flex items-center justify-between p-6 border-b">
                <h3 className="text-xl font-bold text-gray-800">
                  إضافة مستفيد - {selectedApartment.ApartmentCode}
                </h3>
                <button
                  onClick={() => {
                    setShowBeneficiaryModal(false);
                    resetBeneficiaryForm();
                  }}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <form onSubmit={addBeneficiary} className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    البحث عن الموظف *
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      value={employeeSearchTerm}
                      onChange={(e) => {
                        setEmployeeSearchTerm(e.target.value);
                        searchEmployees(e.target.value);
                        setShowEmployeeSearch(true);
                      }}
                      onFocus={() => setShowEmployeeSearch(true)}
                      onBlur={() => {
                        // تأخير إخفاء النتائج للسماح بالنقر على النتائج
                        setTimeout(() => setShowEmployeeSearch(false), 200);
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                      placeholder="ابحث بالاسم أو كود الموظف..."
                      required
                    />

                    {/* نتائج البحث */}
                    {showEmployeeSearch && employeeSearchResults.length > 0 && (
                      <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                        {employeeSearchResults.map((employee, index) => (
                          <div
                            key={index}
                            onClick={() => selectEmployee(employee)}
                            className="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-600 cursor-pointer border-b border-gray-100 dark:border-gray-600 last:border-b-0 transition-colors"
                          >
                            <div className="font-medium text-gray-900">
                              {employee.employeeName || employee.EmployeeName || employee.fullName || employee.FullName || employee.Name}
                            </div>
                            <div className="text-sm text-gray-600">
                              كود: {employee.employeeCode || employee.EmployeeCode || employee.EmployeeID || employee.ID} |
                              {employee.jobTitle || employee.JobTitle || 'غير محدد'}
                            </div>
                            {(employee.department || employee.Department) && (
                              <div className="text-xs text-gray-500">
                                {employee.department || employee.Department}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* عرض كود الموظف المختار */}
                  {beneficiaryForm.employeeCode && (
                    <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded-md">
                      <span className="text-sm text-green-800">
                        كود الموظف المختار: <strong>{beneficiaryForm.employeeCode}</strong>
                      </span>
                    </div>
                  )}
                </div>

                <div>
                  <DateInput
                    id="beneficiary-start-date"
                    name="startDate"
                    value={beneficiaryForm.startDate}
                    onChange={(e) => {
                      const value = e.target.value;

                      setBeneficiaryForm(prev => ({ ...prev, startDate: value }));
                    }}
                    label="تاريخ بداية الاستفادة *"
                    placeholder="DD/MM/YYYY"
                    required={true}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-purple-500 focus:border-purple-500"
                  />
                </div>

                <div className="flex gap-3 pt-4 border-t">
                  <button
                    type="submit"
                    className="bg-purple-600 text-white py-2 px-4 rounded-md hover:bg-purple-700 flex items-center gap-2"
                  >
                    <FiUserPlus />
                    إضافة المستفيد
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setShowBeneficiaryModal(false);
                      resetBeneficiaryForm();
                    }}
                    className="bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400"
                  >
                    إلغاء
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* مودال عرض جميع المستفيدين */}
        {showBeneficiariesListModal && selectedApartment && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between p-6 border-b">
                <h3 className="text-xl font-bold text-gray-800">
                  مستفيدي الشقة - {selectedApartment.ApartmentCode}
                </h3>
                <button
                  onClick={() => setShowBeneficiariesListModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <div className="p-6">
                {/* معلومات الشقة */}
                <div className="bg-gray-50 p-4 rounded-lg mb-6">
                  <h4 className="font-semibold text-gray-800 mb-2">معلومات الشقة:</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div><strong>المؤجر:</strong> {selectedApartment.LandlordName}</div>
                    <div><strong>العنوان:</strong> {selectedApartment.Address}</div>
                    <div><strong>الإيجار الشهري:</strong> {formatCurrency(selectedApartment.RentAmount)}</div>
                    <div><strong>تاريخ البداية:</strong> {formatDate(selectedApartment.StartDate)}</div>
                  </div>
                </div>

                {/* قائمة المستفيدين */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="font-semibold text-gray-800">قائمة المستفيدين:</h4>
                    <button
                      onClick={() => {
                        setShowBeneficiariesListModal(false);
                        resetBeneficiaryForm();
                        setShowBeneficiaryModal(true);
                      }}
                      className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center gap-2"
                    >
                      <FiUserPlus />
                      إضافة مستفيد جديد
                    </button>
                  </div>

                  {selectedApartment.beneficiaries && selectedApartment.beneficiaries.length > 0 ? (
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                          <tr>
                            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                              كود الموظف
                            </th>
                            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                              اسم الموظف
                            </th>
                            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                              المسمى الوظيفي
                            </th>
                            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                              القسم
                            </th>
                            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                              تاريخ البداية
                            </th>
                            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                              تاريخ النهاية
                            </th>
                            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                              الحالة
                            </th>
                            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                              الإجراءات
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {selectedApartment.beneficiaries.map((beneficiary) => (
                            <tr key={beneficiary.ID} className={`hover:bg-gray-50 ${!beneficiary.IsActive ? 'opacity-50' : ''}`}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                                  {beneficiary.EmployeeCode}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <div className="font-bold text-lg text-blue-900">
                                  {beneficiary.EmployeeName || beneficiary.FullName || 'اسم غير محدد'}
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {beneficiary.JobTitle}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {beneficiary.Department || 'غير محدد'}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {formatDate(beneficiary.StartDate)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {formatDate(beneficiary.EndDate)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                  beneficiary.IsActive
                                    ? 'bg-green-100 text-green-800'
                                    : 'bg-red-100 text-red-800'
                                }`}>
                                  {beneficiary.IsActive ? 'نشط' : 'غير نشط'}
                                </span>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div className="flex gap-2">
                                  {beneficiary.IsActive && (
                                    <button
                                      onClick={() => removeBeneficiary(beneficiary.ID)}
                                      className="text-red-600 hover:text-red-900"
                                      title="إزالة المستفيد"
                                    >
                                      <FiUserMinus />
                                    </button>
                                  )}
                                </div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <FiUsers className="text-6xl text-gray-300 mx-auto mb-4" />
                      <h3 className="text-xl font-medium text-gray-600 mb-2">لا يوجد مستفيدين</h3>
                      <p className="text-gray-500 mb-4">لم يتم إضافة أي مستفيدين لهذه الشقة بعد</p>
                      <button
                        onClick={() => {
                          setShowBeneficiariesListModal(false);
                          resetBeneficiaryForm();
                          setShowBeneficiaryModal(true);
                        }}
                        className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700"
                      >
                        إضافة أول مستفيد
                      </button>
                    </div>
                  )}
                </div>

                <div className="flex justify-end pt-4 border-t mt-6">
                  <button
                    onClick={() => setShowBeneficiariesListModal(false)}
                    className="bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400"
                  >
                    إغلاق
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
