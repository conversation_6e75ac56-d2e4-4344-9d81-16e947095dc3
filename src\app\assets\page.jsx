'use client';
import React from 'react';

function MainComponent() {
  const [assets, setAssets] = useState({
    apartments: [],
    cars: [],
    workers: [],
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchAssets() {
      setLoading(true);
      try {
        const [apartments, cars, workers] = await Promise.all([
          fetch('/api/data-service', {
            method: 'POST',
            body: JSON.stringify({ table: 'apartments', action: 'list' }),
          }).then((res) => res.json()),
          fetch('/api/data-service', {
            method: 'POST',
            body: JSON.stringify({ table: 'cars', action: 'list' }),
          }).then((res) => res.json()),
          fetch('/api/data-service', {
            method: 'POST',
            body: JSON.stringify({ table: 'temp_workers', action: 'list' }),
          }).then((res) => res.json()),
        ]);

        setAssets({
          apartments: apartments.data || [],
          cars: cars.data || [],
          workers: workers.data || [],
        });
      } catch (error) {

      }
      setLoading(false);
    }

    fetchAssets();
  }, []);

  return (
    <div dir="rtl" className="min-h-screen bg-white dark:bg-gray-900">
      <MainComponent currentPath="/assets" />

      <div className="max-w-7xl mx-auto p-4 md:p-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          إدارة الأصول
        </h1>

        {loading ? (
          <div className="text-center text-gray-600 dark:text-gray-400 text-xl">
            جاري التحميل...
          </div>
        ) : (
          <div className="space-y-8">
            <section className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                  الشقق المؤجرة
                </h2>
                <a
                  href="/add-apartment"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  إضافة شقة
                </a>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {assets.apartments.map((apt) => (
                  <div
                    key={apt.id}
                    className="bg-white dark:bg-gray-700 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow"
                  >
                    <h3 className="text-lg font-semibold mb-2">
                      {apt.apartment_code}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      الموقع: {apt.location}
                    </p>
                    <p className="text-gray-600 dark:text-gray-300">
                      الإيجار الشهري: {apt.monthly_rent}
                    </p>
                    <p className="text-gray-600 dark:text-gray-300">
                      الحالة: {apt.status}
                    </p>
                  </div>
                ))}
              </div>
            </section>

            <section className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                  السيارات
                </h2>
                <a
                  href="/add-car"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  إضافة سيارة
                </a>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {assets.cars.map((car) => (
                  <div
                    key={car.id}
                    className="bg-white dark:bg-gray-700 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow"
                  >
                    <h3 className="text-lg font-semibold mb-2">
                      {car.car_code}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      الموديل: {car.model}
                    </p>
                    <p className="text-gray-600 dark:text-gray-300">
                      رقم اللوحة: {car.plate_number}
                    </p>
                    <p className="text-gray-600 dark:text-gray-300">
                      الحالة: {car.status}
                    </p>
                  </div>
                ))}
              </div>
            </section>

            <section className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                  العمال المؤقتين
                </h2>
                <a
                  href="/add-worker"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  إضافة عامل
                </a>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {assets.workers.map((worker) => (
                  <div
                    key={worker.id}
                    className="bg-white dark:bg-gray-700 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow"
                  >
                    <h3 className="text-lg font-semibold mb-2">
                      {worker.name}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      الوظيفة: {worker.job_title}
                    </p>
                    <p className="text-gray-600 dark:text-gray-300">
                      المعدل اليومي: {worker.daily_rate}
                    </p>
                    <p className="text-gray-600 dark:text-gray-300">
                      الحالة: {worker.status}
                    </p>
                  </div>
                ))}
              </div>
            </section>
          </div>
        )}
      </div>
    </div>
  );
}

export default MainComponent;
