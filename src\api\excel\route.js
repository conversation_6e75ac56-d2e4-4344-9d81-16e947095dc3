async function handler({
  action,
  data,
  template,
  validationRules,
  dataType,
  lang = 'ar',
  reportType,
  filters,
}) {
  const errorMessages = {
    required: { ar: 'حقل مطلوب', en: 'Field is required' },
    number: { ar: 'يجب أن يكون رقم', en: 'Must be a number' },
    date: { ar: 'يجب أن يكون تاريخ صحيح', en: 'Must be a valid date' },
    nationalId: { ar: 'رقم قومي غير صالح', en: 'Invalid national ID' },
    phone: { ar: 'رقم هاتف غير صالح', en: 'Invalid phone number' },
    invalid: { ar: 'إجراء غير صالح', en: 'Invalid action' },
    noData: { ar: 'لا توجد بيانات للاستيراد', en: 'No data to import' },
    validation: {
      ar: 'البيانات وقواعد التحقق مطلوبة',
      en: 'Data and validation rules are required',
    },
    export: {
      ar: 'البيانات والقالب مطلوبة للتصدير',
      en: 'Data and template are required for export',
    },
    fileFormat: { ar: 'تنسيق الملف غير صالح', en: 'Invalid file format' },
    missingFields: { ar: 'حقول مفقودة في الملف', en: 'Missing fields in file' },
    dataFormat: { ar: 'تنسيق البيانات غير صالح', en: 'Invalid data format' },
  };

  if (!action) {
    return { success: false, error: errorMessages.invalid[lang] };
  }

  try {
    switch (action) {
      case 'export': {
        let queryStr = 'SELECT * FROM Employees WHERE 1=1';
        const queryParams = [];
        let paramCount = 1;

        if (filters?.department) {
          queryStr += ` AND department = $${paramCount}`;
          queryParams.push(filters.department);
          paramCount++;
        }

        if (filters?.area) {
          queryStr += ` AND area = $${paramCount}`;
          queryParams.push(filters.area);
          paramCount++;
        }

        queryStr += ' ORDER BY employee_name';

        const employees = await sql(queryStr, queryParams);

        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(employees);
        XLSX.utils.book_append_sheet(wb, ws, 'Employees');

        const buffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });

        return {
          success: true,
          buffer: buffer.toString('base64'),
          contentType:
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          filename: `employees-export-${new Date()
            .toISOString()
            .slice(0, 7)}.xlsx`,
        };
      }

      case 'getTemplate':
      case 'downloadTemplate': {
        const wb = XLSX.utils.book_new();

        if (dataType === 'monthlyAttendance') {
          const mainHeaders = [
            [
              'Employee ID\nرقم الموظف',
              'Employee Name\nاسم الموظف',
              'Department\nالقسم',
              'Job Title\nالمسمى الوظيفي',
              'Month\nالشهر',
              'Working Days\nأيام العمل',
              'Present Days\nأيام الحضور',
              'Absent Days\nأيام الغياب',
              'Official Missions\nمهمات رسمية',
              'Sick Leave\nإجازة مرضية',
              'Annual Leave\nإجازة سنوية',
              'Unpaid Leave\nإجازة بدون راتب',
              'Late (Minutes)\nتأخير (دقائق)',
              'Overtime (Hours)\nوقت إضافي (ساعات)',
            ],
          ];

          const mainWS = XLSX.utils.aoa_to_sheet(mainHeaders);
          XLSX.utils.book_append_sheet(
            wb,
            mainWS,
            'Monthly Attendance - الحضور الشهري'
          );

          const dailyHeaders = [
            [
              'Employee ID\nرقم الموظف',
              'Date\nالتاريخ',
              'Status\nالحالة',
              'Check In\nوقت الدخول',
              'Check Out\nوقت الخروج',
              'Late Minutes\nدقائق التأخير',
              'Overtime Hours\nساعات إضافية',
              'Notes\nملاحظات',
            ],
          ];

          const dailyWS = XLSX.utils.aoa_to_sheet(dailyHeaders);
          XLSX.utils.book_append_sheet(
            wb,
            dailyWS,
            'Daily Details - التفاصيل اليومية'
          );

          const statusCodes = [
            ['Status Code\nرمز الحالة', 'Description\nالوصف'],
            ['P', 'Present - حاضر'],
            ['A', 'Absent - غائب'],
            ['M', 'Official Mission - مهمة رسمية'],
            ['S', 'Sick Leave - إجازة مرضية'],
            ['V', 'Annual Leave - إجازة سنوية'],
            ['U', 'Unpaid Leave - إجازة بدون راتب'],
            ['H', 'Holiday - عطلة رسمية'],
            ['W', 'Weekend - عطلة أسبوعية'],
          ];

          const instructionsWS = XLSX.utils.aoa_to_sheet(statusCodes);
          XLSX.utils.book_append_sheet(
            wb,
            instructionsWS,
            'Instructions - التعليمات'
          );
        } else {
          const headers =
            lang === 'ar'
              ? [
                  [
                    'الكود',
                    'الأسم',
                    'تاريخ_الميلاد',
                    'تاريخ_التعيين',
                    'المسمى_الوظيفى',
                    'القسم',
                    'الشهادة',
                    'الجامعة',
                    'القسم_الدراسي',
                    'التقدير',
                    'سنة_التخرج',
                    'رقم_التليفون',
                    'رقم_البطاقة',
                    'تاريخ_التواجد_بالمشروع',
                    'بدل_النقل',
                    'خط_السيارة',
                    'مكان_السكن',
                    'التامين_الاجتماعي',
                    'السكن',
                    'خط_سير_العربية',
                    'المنطقة',
                    'المحافظة',
                    'الحالة',
                    'الخدمة_العسكرية',
                  ],
                ]
              : [
                  [
                    'Code',
                    'Name',
                    'Birth Date',
                    'Hire Date',
                    'Job Title',
                    'Department',
                    'Degree',
                    'University',
                    'Study Department',
                    'Grade',
                    'Graduation Year',
                    'Phone',
                    'ID Number',
                    'Project Join Date',
                    'Transportation Allowance',
                    'Bus Line',
                    'Residence Location',
                    'Social Insurance',
                    'Housing',
                    'Car Route',
                    'Area',
                    'Governorate',
                    'Status',
                    'Military Service',
                  ],
                ];

          const ws = XLSX.utils.aoa_to_sheet(headers);
          XLSX.utils.book_append_sheet(
            wb,
            ws,
            lang === 'ar' ? 'النموذج العربي' : 'English Template'
          );
        }

        const buffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' });

        return {
          success: true,
          buffer: buffer.toString('base64'),
          contentType:
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          filename: `${dataType || 'employee'}-template-${new Date()
            .toISOString()
            .slice(0, 7)}.xlsx`,
        };
      }

      default:
        throw new Error(errorMessages.invalid[lang]);
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
}
