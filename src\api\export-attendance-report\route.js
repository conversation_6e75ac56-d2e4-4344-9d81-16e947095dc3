async function handler({ month, year }) {
  if (!month || !year) {
    return { error: 'يجب تحديد الشهر والسنة' };
  }

  const startDate = `${year}-${month.padStart(2, '0')}-01`;
  const endDate = `${year}-${month.padStart(2, '0')}-${new Date(
    year,
    month,
    0
  ).getDate()}`;

  try {
    const attendanceData = await sql`
      SELECT 
        emp.EmployeeID,
        emp.FullName,
        dept.Name as DepartmentName,
        job.Title as JobTitle,
        att.AttendanceDate,
        att.CheckInTime,
        att.CheckOutTime,
        att.Status,
        att.LateMinutes,
        att.EarlyLeaveMinutes,
        att.OvertimeMinutes
      FROM Employees emp
      LEFT JOIN Attendance att ON emp.EmployeeID = att.EmployeeID
      LEFT JOIN Departments dept ON emp.DepartmentID = dept.DepartmentID
      LEFT JOIN JobTitles job ON emp.JobTitleID = job.JobTitleID
      WHERE att.AttendanceDate BETWEEN ${startDate} AND ${endDate}
      ORDER BY emp.EmployeeID, att.AttendanceDate
    `;

    if (!attendanceData.length) {
      return { error: 'لا توجد بيانات للفترة المحددة' };
    }

    const reportData = attendanceData.map((row) => ({
      'الرقم الوظيفي': row.EmployeeID,
      'اسم الموظف': row.FullName,
      القسم: row.DepartmentName,
      'المسمى الوظيفي': row.JobTitle,
      التاريخ: row.AttendanceDate,
      'وقت الحضور': row.CheckInTime,
      'وقت الانصراف': row.CheckOutTime,
      الحالة: row.Status,
      'دقائق التأخير': row.LateMinutes,
      'دقائق الخروج المبكر': row.EarlyLeaveMinutes,
      'ساعات العمل الإضافية': row.OvertimeMinutes,
    }));

    return {
      success: true,
      data: reportData,
      filename: `تقرير_الحضور_${year}_${month}.xlsx`,
      headers: [
        'الرقم الوظيفي',
        'اسم الموظف',
        'القسم',
        'المسمى الوظيفي',
        'التاريخ',
        'وقت الحضور',
        'وقت الانصراف',
        'الحالة',
        'دقائق التأخير',
        'دقائق الخروج المبكر',
        'ساعات العمل الإضافية',
      ],
    };
  } catch (error) {
    return { error: 'حدث خطأ أثناء استخراج البيانات' };
  }
}
