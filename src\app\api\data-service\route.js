import { NextResponse } from 'next/server';
import { getConnection, executeQuery, sql } from '@/lib/db';

export async function POST(request) {
  let pool = null;
  try {
    const body = await request.json();
    const { table, action, data, query = {} } = body;

    if (!table || !action) {
      return NextResponse.json({
        success: false,
        error: 'Table and action are required'
      }, { status: 400 });
    }
    pool = await getConnection();

    let result;
    switch (action) {
      case 'create':
        result = await handleCreate(pool, table, data);
        break;
      case 'read':
        result = await handleRead(pool, table, query);
        break;
      case 'update':
        result = await handleUpdate(pool, table, data, query);
        break;
      case 'delete':
        result = await handleDelete(pool, table, query);
        break;
      default:
        throw new Error('Invalid action');
    }

    return NextResponse.json({ success: true, data: result });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message || 'حدث خطأ في معالجة الطلب'
    }, { status: 500 });
  }
}

async function handleCreate(pool, table, data) {
  const columns = Object.keys(data);
  const values = Object.values(data);
  const paramPlaceholders = values.map((_, i) => `@p${i}`).join(', ');
  
  const params = {};
  values.forEach((value, index) => {
    params[`p${index}`] = value;
  });

  const query = `
    INSERT INTO ${table} (${columns.join(', ')})
    VALUES (${paramPlaceholders})
    OUTPUT INSERTED.*;
  `;

  const result = await executeQuery(query, params);
  return result[0];
}

async function handleRead(pool, table, query = {}) {
  const whereConditions = [];
  const params = {};
  
  Object.entries(query).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      whereConditions.push(`${key} = @${key}`);
      params[key] = value;
    }
  });

  const whereClause = whereConditions.length
    ? `WHERE ${whereConditions.join(' AND ')}`
    : '';

  return await executeQuery(`
    SELECT * FROM ${table}
    ${whereClause}
  `, params);
}

async function handleUpdate(pool, table, data, query) {
  const setColumns = Object.keys(data)
    .map(key => `${key} = @${key}`)
    .join(', ');

  const whereConditions = Object.keys(query)
    .map(key => `${key} = @where_${key}`)
    .join(' AND ');

  const params = {
    ...data,
    ...Object.entries(query).reduce((acc, [key, value]) => {
      acc[`where_${key}`] = value;
      return acc;
    }, {})
  };

  const result = await executeQuery(`
    UPDATE ${table}
    SET ${setColumns}
    WHERE ${whereConditions}
    OUTPUT INSERTED.*;
  `, params);

  return result[0];
}

async function handleDelete(pool, table, query) {
  const whereConditions = Object.keys(query)
    .map(key => `${key} = @${key}`)
    .join(' AND ');

  const result = await executeQuery(`
    DELETE FROM ${table}
    OUTPUT DELETED.*
    WHERE ${whereConditions}
  `, query);

  return result[0];
}
