'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

export default function SimpleLayout({ children }) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarHovered, setSidebarHovered] = useState(false);
  const [expandedMenus, setExpandedMenus] = useState({});
  const router = useRouter();

  const handleLogout = () => {
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('userInfo');
    document.cookie = 'isLoggedIn=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    document.cookie = 'userInfo=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT';
    router.push('/login');
  };

  const toggleMenu = (menuId) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menuId]: !prev[menuId]
    }));
  };

  const menuItems = [
    {
      id: 'home',
      title: 'الرئيسية',
      icon: '🏠',
      link: '/dashboard'
    },
    {
      id: 'employees',
      title: 'الموظفين',
      icon: '👥',
      color: 'blue',
      subItems: [
        { title: 'داش بورد الموظفين', link: '/employees/dashboard', icon: '📊' },
        { title: 'إضافة موظف', link: '/employees/add', icon: '➕' },
        { title: 'بحث عن موظف', link: '/employees/search', icon: '🔍' },
        { title: 'أرشيف المستندات', link: '/employees/archive', icon: '📁' }
      ]
    },
    {
      id: 'attendance',
      title: 'التمام اليومى والإجازات',
      icon: '⏰',
      color: 'green',
      subItems: [
        { title: 'التمام اليومى', link: '/attendance/daily', icon: '🕐' },
        { title: 'رفع التمام اليومي', link: '/attendance/upload', icon: '📤' },
        { title: 'كشف التمام الشهري', link: '/attendance/monthly-report', icon: '📊' },
        { title: 'كشف المؤثرات الشهرية', link: '/attendance/monthly', icon: '📈' },
        { title: 'كشف الرواتب الشهرية', link: '/payroll/monthly', icon: '💰' },
        { title: 'إجازة جديدة', link: '/leaves/new', icon: '📅' },
        { title: 'رصيد الإجازات', link: '/leaves/balance', icon: '📋' }
      ]
    },
    {
      id: 'cars',
      title: 'السيارات',
      icon: '🚗',
      color: 'orange',
      subItems: [
        { title: 'عرض بيانات السيارات', link: '/cars/data', icon: '📋' },
        { title: 'أرشيف مستندات السيارات', link: '/cars/archive', icon: '📁' },
        { title: 'مذكرات', link: '/cars/memos', icon: '📝' }
      ]
    },
    {
      id: 'apartments',
      title: 'الشقق',
      icon: '🏢',
      color: 'purple',
      subItems: [
        { title: 'عرض بيانات الشقق', link: '/apartments/data', icon: '📋' },
        { title: 'أرشيف مستندات الشقق', link: '/apartments/archive', icon: '📁' },
        { title: 'مذكرات', link: '/apartments/memos', icon: '📝' }
      ]
    },
    {
      id: 'costs',
      title: 'التكاليف',
      icon: '💸',
      color: 'red',
      subItems: [
        { title: 'داش بورد التكاليف', link: '/costs/dashboard', icon: '📊' },
        { title: 'تكاليف السيارات', link: '/costs/cars', icon: '🚗' },
        { title: 'تكاليف الشقق', link: '/costs/apartments', icon: '🏢' },
        { title: 'تكاليف العمالة المؤقتة', link: '/costs/temp-workers', icon: '👷' }
      ]
    }
  ];

  const isExpanded = sidebarOpen || sidebarHovered;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex" dir="rtl">
      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 right-0 z-50 bg-gradient-to-b from-slate-800 to-slate-900 shadow-2xl transition-all duration-300 ease-in-out ${
          isExpanded ? 'w-80' : 'w-16'
        } lg:static lg:inset-0 border-l border-slate-700`}
        onMouseEnter={() => setSidebarHovered(true)}
        onMouseLeave={() => setSidebarHovered(false)}
      >

        {/* Sidebar Header */}
        <div className="flex items-center justify-between h-16 px-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center backdrop-blur-sm border border-white border-opacity-30">
              <span className="text-xl">🏢</span>
            </div>
            {isExpanded && (
              <div>
                <h1 className="text-lg font-bold">نظام إدارة الشركة</h1>
                <p className="text-xs text-blue-100 opacity-90">إدارة شاملة ومتطورة</p>
              </div>
            )}
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden w-8 h-8 rounded-lg bg-white bg-opacity-20 flex items-center justify-center hover:bg-opacity-30 transition-all"
          >
            ✕
          </button>
        </div>

        {/* Navigation Menu */}
        <nav className="flex-1 px-3 py-6 space-y-2 overflow-y-auto h-full">
          {menuItems.map((item) => (
            <div key={item.id}>
              {item.subItems ? (
                <div>
                  <button
                    onClick={() => toggleMenu(item.id)}
                    className={`w-full flex items-center justify-between px-4 py-3 text-slate-300 rounded-xl transition-all duration-200 group ${
                      item.color === 'blue' ? 'hover:bg-blue-600/20 hover:text-blue-300 hover:shadow-lg hover:shadow-blue-500/20' :
                      item.color === 'green' ? 'hover:bg-green-600/20 hover:text-green-300 hover:shadow-lg hover:shadow-green-500/20' :
                      item.color === 'orange' ? 'hover:bg-orange-600/20 hover:text-orange-300 hover:shadow-lg hover:shadow-orange-500/20' :
                      item.color === 'purple' ? 'hover:bg-purple-600/20 hover:text-purple-300 hover:shadow-lg hover:shadow-purple-500/20' :
                      item.color === 'red' ? 'hover:bg-red-600/20 hover:text-red-300 hover:shadow-lg hover:shadow-red-500/20' :
                      'hover:bg-slate-700/50 hover:text-white'
                    } ${expandedMenus[item.id] ? 'bg-slate-700/30 text-white' : ''}`}
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded-lg bg-slate-700/50 flex items-center justify-center group-hover:scale-110 transition-transform">
                        <span className="text-lg">{item.icon}</span>
                      </div>
                      {isExpanded && (
                        <span className="font-medium text-sm">{item.title}</span>
                      )}
                    </div>
                    {isExpanded && (
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center transition-transform ${
                        expandedMenus[item.id] ? 'rotate-180' : ''
                      }`}>
                        <span className="text-xs">▼</span>
                      </div>
                    )}
                  </button>

                  {expandedMenus[item.id] && isExpanded && (
                    <div className="mt-2 mr-8 space-y-1 border-r-2 border-slate-600/30 pr-4">
                      {item.subItems.map((subItem, index) => (
                        <a
                          key={index}
                          href={subItem.link}
                          className="w-full flex items-center gap-3 px-3 py-2 text-sm text-slate-400 rounded-lg hover:bg-slate-700/30 hover:text-white transition-all duration-200 group"
                        >
                          <div className="w-6 h-6 rounded-md bg-slate-600/30 flex items-center justify-center group-hover:bg-slate-600/50 transition-colors">
                            <span className="text-xs">{subItem.icon}</span>
                          </div>
                          <span>{subItem.title}</span>
                        </a>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <a
                  href={item.link}
                  className="w-full flex items-center gap-3 px-4 py-3 text-slate-300 rounded-xl hover:bg-slate-700/50 hover:text-white transition-all duration-200 group"
                >
                  <div className="w-8 h-8 rounded-lg bg-slate-700/50 flex items-center justify-center group-hover:scale-110 transition-transform">
                    <span className="text-lg">{item.icon}</span>
                  </div>
                  {isExpanded && (
                    <span className="font-medium text-sm">{item.title}</span>
                  )}
                </a>
              )}
            </div>
          ))}
        </nav>

        {/* Logout Button */}
        <div className="p-3 border-t border-slate-700/50">
          <button
            onClick={handleLogout}
            className="w-full flex items-center gap-3 px-4 py-3 text-red-300 rounded-xl hover:bg-red-600/20 hover:text-red-200 transition-all duration-200 group hover:shadow-lg hover:shadow-red-500/20"
          >
            <div className="w-8 h-8 rounded-lg bg-red-600/20 flex items-center justify-center group-hover:scale-110 transition-transform">
              <span className="text-lg">🚪</span>
            </div>
            {isExpanded && (
              <span className="font-medium text-sm">تسجيل الخروج</span>
            )}
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className={`flex-1 transition-all duration-300 ${
        isExpanded ? 'lg:mr-80' : 'lg:mr-16'
      }`}>
        {/* Mobile Header */}
        <div className="lg:hidden bg-white/80 backdrop-blur-md shadow-sm border-b border-gray-200/50">
          <div className="flex items-center justify-between h-16 px-4">
            <button
              onClick={() => setSidebarOpen(true)}
              className="w-10 h-10 rounded-xl bg-gray-100 hover:bg-gray-200 flex items-center justify-center text-gray-600 hover:text-gray-900 transition-all"
            >
              ☰
            </button>
            <h1 className="text-lg font-semibold text-gray-900">نظام إدارة الشركة</h1>
            <div className="w-10"></div>
          </div>
        </div>

        {/* Page Content */}
        <main className="p-6">
          {children}
        </main>
      </div>

      {/* Mobile Overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
}
