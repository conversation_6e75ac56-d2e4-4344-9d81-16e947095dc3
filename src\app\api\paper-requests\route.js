import { NextResponse } from 'next/server';
import sql from 'mssql';

// دالة تحديد الحالة الأولية للطلب
function getInitialStatus(requestType, leaveType) {

  // جميع الطلبات تبدأ بـ "قيد المراجعة" للسماح بالتعديل والحذف
  // يمكن اعتماد المأموريات والورديات لاحقاً بسهولة

  return 'قيد المراجعة';

  // الإذن معتمد تلقائياً
  if (requestType === 'permission') {

    return 'معتمد';
  }

  // باقي الطلبات تحتاج مراجعة

  return 'قيد المراجعة';
}

// دالة تنسيق التاريخ بصيغة dd/mm/yyyy
function formatDateToDDMMYYYY(dateInput) {
  if (!dateInput) return '';
  const date = new Date(dateInput);
  if (isNaN(date.getTime())) return '';

  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  return `${day}/${month}/${year}`;
}

// دالة تحويل كود المستخدم إلى اسم المسؤول
function getManagerNameFromCode(userCode) {
  const managerMap = {
    '1450': 'سامي منير',
    '5567': 'إسلام فايز',
    '7890': 'حسام أحمد' // يمكن إضافة المزيد حسب الحاجة
  };

  return managerMap[userCode] || 'مسؤول النظام';
}

// دالة للحصول على كود المستخدم الحالي من الجلسة
function getCurrentUserCode(request) {
  try {
    // محاولة الحصول على كود المستخدم من headers أو cookies
    const userCode = request.headers.get('x-user-code') ||
                    request.headers.get('user-code') ||
                    '1450'; // افتراضي سامي منير
    return userCode;
  } catch (error) {

    return '1450'; // افتراضي سامي منير
  }
}

// إعداد قاعدة البيانات
const dbConfig = {
  user: process.env.DB_USER || 'SA',
  password: process.env.DB_PASSWORD || 'admin@123',
  server: process.env.DB_SERVER || 'localhost\\DBOJESTA',
  database: process.env.DB_NAME || 'EMP',
  options: {
    encrypt: false,
    trustServerCertificate: true,
    enableArithAbort: true,
    instanceName: 'DBOJESTA'
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
  requestTimeout: 30000,
  connectionTimeout: 30000,
};

export async function POST(request) {
  let pool;

  try {
    const body = await request.json();
    const { action } = body;

    // الحصول على كود المستخدم الحالي
    const currentUserCode = getCurrentUserCode(request);
    const currentManagerName = getManagerNameFromCode(currentUserCode);

    pool = await sql.connect(dbConfig);

    switch (action) {
      case 'create':
        return await createPaperRequest(pool, body, currentManagerName);
      case 'list':
        return await getPaperRequests(pool, body);
      case 'update-status':
        return await updateRequestStatus(pool, body, currentManagerName);
      case 'delete':
        return await deleteRequest(pool, body);
      case 'print':
        return await getPrintData(pool, body);
      case 'getActionLog':
        return await getRequestActionLog(pool, body);
      case 'cleanup-duplicates':
        return await cleanupDuplicateRequests(pool, body);
      case 'fix-mission-status':
        return await fixMissionStatus(pool);
      case 'fix-all-missions':
        return await fixAllMissions(pool);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      try {
        await pool.close();
      } catch (closeError) {

      }
    }
  }
}

async function createPaperRequest(pool, data, currentManagerName) {
  try {
    // التحقق من الطلبات المكررة أولاً
    const duplicateCheck = await checkDuplicateRequest(pool, data);
    if (duplicateCheck.isDuplicate) {
      return NextResponse.json({
        success: false,
        error: duplicateCheck.message
      }, { status: 409 });
    }

    // التحقق من تضارب الطلبات
    if (data.startDate && data.endDate) {
      const conflictCheck = await checkRequestConflicts(
        pool,
        data.employeeCode,
        data.startDate,
        data.endDate,
        data.requestType
      );

      if (conflictCheck.hasConflict) {
        return NextResponse.json({
          success: false,
          error: conflictCheck.message,
          conflicts: conflictCheck.conflicts
        }, { status: 409 }); // 409 Conflict
      }
    }

    // تحديد الحالة الأولية بناءً على نوع الطلب
    const initialStatus = getInitialStatus(data.requestType, data.leaveType);

    // إنشاء جدول موحد لجميع الطلبات الورقية وإضافة الأعمدة المفقودة
    const checkTableQuery = `
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PaperRequests' AND xtype='U')
      BEGIN
        CREATE TABLE PaperRequests (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          RequestType NVARCHAR(50) NOT NULL, -- leave, mission, permission, night_shift
          EmployeeCode NVARCHAR(20) NOT NULL,
          EmployeeName NVARCHAR(100) NOT NULL,
          Department NVARCHAR(100),
          JobTitle NVARCHAR(100),

          -- بيانات الإجازة
          LeaveType NVARCHAR(50), -- اعتيادية، مرضية، عارضة، بدل
          StartDate DATE,
          EndDate DATE,
          DaysCount INT,
          LeaveReason NVARCHAR(MAX),
          LastLeaveDate DATE,
          RemainingBalance INT,

          -- بيانات المأمورية
          MissionDestination NVARCHAR(200),
          MissionPurpose NVARCHAR(MAX),
          MissionDuration NVARCHAR(100),
          TransportMethod NVARCHAR(100),

          -- بيانات الإذن
          PermissionStartTime NVARCHAR(20),
          PermissionEndTime NVARCHAR(20),
          PermissionDuration NVARCHAR(50),
          PermissionReason NVARCHAR(MAX),

          -- بيانات الوردية الليلية
          NightShiftDate DATE,
          NightShiftReason NVARCHAR(MAX),

          -- حالة الطلب
          Status NVARCHAR(20) DEFAULT N'قيد المراجعة', -- قيد المراجعة، معتمدة، مرفوضة
          RequestDate DATETIME DEFAULT GETDATE(),
          ApprovalDate DATETIME NULL,
          RejectionDate DATETIME NULL,
          ApprovalNotes NVARCHAR(MAX) NULL,
          ApprovedBy NVARCHAR(100) NULL,

          -- بيانات إضافية
          EmergencyContact NVARCHAR(100),
          EmergencyPhone NVARCHAR(20),
          ReplacementEmployee NVARCHAR(100),
          Notes NVARCHAR(MAX)
        )
      END

      -- إضافة الأعمدة المفقودة إذا كان الجدول موجوداً بالفعل
      IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('PaperRequests') AND name = 'LastLeaveDate')
      BEGIN
        ALTER TABLE PaperRequests ADD LastLeaveDate DATE
      END

      IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('PaperRequests') AND name = 'RemainingBalance')
      BEGIN
        ALTER TABLE PaperRequests ADD RemainingBalance INT
      END
    `;

    await pool.request().query(checkTableQuery);

    // لا نحدث المأموريات تلقائ漪 - نتركها "قيد المراجعة" للسماح بالتعديل والحذف

    // إنشاء جدول سجل الإجراءات للطلبات الورقية
    const actionLogTableQuery = `
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PaperRequestActions' AND xtype='U')
      BEGIN
        CREATE TABLE PaperRequestActions (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          RequestID INT NOT NULL,
          ActionType NVARCHAR(50) NOT NULL, -- submitted, approved, rejected, deleted, reverted
          ActionBy NVARCHAR(100) NOT NULL,
          ActionDate DATETIME DEFAULT GETDATE(),
          Notes NVARCHAR(MAX),
          PreviousStatus NVARCHAR(50),
          NewStatus NVARCHAR(50),
          FOREIGN KEY (RequestID) REFERENCES PaperRequests(ID) ON DELETE CASCADE
        )
      END
    `;

    await pool.request().query(actionLogTableQuery);

    // تحويل التاريخ من dd/mm/yyyy إلى yyyy-mm-dd
    const convertDate = (dateStr) => {
      if (!dateStr || dateStr === '' || dateStr === 'لا توجد إجازات سابقة' || dateStr === 'غير متاح') {
        return null;
      }

      try {
        if (dateStr.includes('/')) {
          const [day, month, year] = dateStr.split('/');
          if (day && month && year && day.length <= 2 && month.length <= 2 && year.length === 4) {
            const date = new Date(year, month - 1, day);
            if (!isNaN(date.getTime())) {
              return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
            }
          }
        }

        // محاولة تحويل التاريخ مباشرة
        const date = new Date(dateStr);
        if (!isNaN(date.getTime())) {
          return dateStr;
        }

        return null;
      } catch (error) {

        return null;
      }
    };

    // إدراج طلب جديد مع الحصول على ID في نفس العملية
    const insertQuery = `
      INSERT INTO PaperRequests (
        RequestType, EmployeeCode, EmployeeName, Department, JobTitle,
        LeaveType, StartDate, EndDate, DaysCount, LeaveReason,
        LastLeaveDate, RemainingBalance,
        MissionDestination, MissionPurpose, MissionDuration, TransportMethod,
        PermissionStartTime, PermissionEndTime, PermissionDuration, PermissionReason,
        NightShiftDate, NightShiftReason,
        Status, RequestDate, EmergencyContact, EmergencyPhone,
        ReplacementEmployee, Notes
      ) VALUES (
        @requestType, @employeeCode, @employeeName, @department, @jobTitle,
        @leaveType, @startDate, @endDate, @daysCount, @leaveReason,
        @lastLeaveDate, @remainingBalance,
        @missionDestination, @missionPurpose, @missionDuration, @transportMethod,
        @permissionStartTime, @permissionEndTime, @permissionDuration, @permissionReason,
        @nightShiftDate, @nightShiftReason,
        @status, @requestDate, @emergencyContact, @emergencyPhone,
        @replacementEmployee, @notes
      );
      SELECT SCOPE_IDENTITY() as RequestID;
    `;

    const insertedResult = await pool.request()
      .input('requestType', sql.NVarChar, data.requestType || 'leave')
      .input('employeeCode', sql.NVarChar, data.employeeId)
      .input('employeeName', sql.NVarChar, data.employeeName)
      .input('department', sql.NVarChar, data.department || '')
      .input('jobTitle', sql.NVarChar, data.jobTitle || '')

      // بيانات الإجازة
      .input('leaveType', sql.NVarChar, data.leaveType || null)
      .input('startDate', sql.Date, convertDate(data.startDate))
      .input('endDate', sql.Date, convertDate(data.endDate))
      .input('daysCount', sql.Int, parseInt(data.totalDays) || null)
      .input('leaveReason', sql.NVarChar, data.reason || null)
      .input('lastLeaveDate', sql.Date, convertDate(data.lastLeaveDate))
      .input('remainingBalance', sql.Int, data.remainingBalance && !isNaN(parseInt(data.remainingBalance)) ? parseInt(data.remainingBalance) : null)

      // بيانات المأمورية
      .input('missionDestination', sql.NVarChar, data.destination || null)
      .input('missionPurpose', sql.NVarChar, data.purpose || null)
      .input('missionDuration', sql.NVarChar, data.duration || null)
      .input('transportMethod', sql.NVarChar, data.transportMethod || null)

      // بيانات الإذن
      .input('permissionStartTime', sql.NVarChar, data.startTime || null)
      .input('permissionEndTime', sql.NVarChar, data.endTime || null)
      .input('permissionDuration', sql.NVarChar, data.permissionDuration || null)
      .input('permissionReason', sql.NVarChar, data.permissionReason || null)

      // بيانات الوردية الليلية
      .input('nightShiftDate', sql.Date, convertDate(data.nightShiftDate))
      .input('nightShiftReason', sql.NVarChar, data.nightShiftReason || null)

      // حالة الطلب
      .input('status', sql.NVarChar, initialStatus)
      .input('requestDate', sql.DateTime, new Date())
      .input('emergencyContact', sql.NVarChar, data.emergencyContact || null)
      .input('emergencyPhone', sql.NVarChar, data.emergencyPhone || null)
      .input('replacementEmployee', sql.NVarChar, data.replacementEmployee || null)
      .input('notes', sql.NVarChar, data.notes || null)
      .query(insertQuery);

    const requestId = insertedResult.recordset[0].RequestID;

    // تسجيل إجراء تقديم الطلب في سجل الإجراءات
    await logRequestAction(pool, requestId, 'submitted', currentManagerName, 'تم تقديم الطلب', null, 'قيد المراجعة');

    // إذا كان الطلب معتمد تلقائ漪، نحدث التمام اليومي
    if (initialStatus === 'معتمدة') {
      await updateAttendanceFromApprovedRequest(pool, {
        requestId: requestId,
        requestType: data.requestType,
        employeeCode: data.employeeId,
        startDate: data.startDate,
        endDate: data.endDate,
        leaveType: data.leaveType,
        destination: data.missionDestination
      });
    }

    // إنشاء إشعار في نظام الإشعارات الذكية المنفصل
    if (data.requestType === 'leave') {
      const startDate = data.startDate ? formatDateToDDMMYYYY(data.startDate) : 'غير محدد';

      await createSmartNotification(pool, {
        type: 'LEAVE_REQUEST_SUBMITTED',
        title: `طلب إجازة جديد - ${data.employeeName}`,
        message: `تم تقديم إجازة ${getLeaveTypeArabic(data.leaveType)} لـ (${data.employeeName}) (${data.totalDays || 0} أيام) تبدأ من (${startDate}) بواسطة (${currentManagerName})`,
        employeeId: data.employeeId,
        employeeName: data.employeeName,
        systemUserId: currentManagerName,
        leaveStartDate: data.startDate,
        priority: 'high', // أولوية عالية للطلبات الجديدة
        relatedData: JSON.stringify({
          requestId: requestId,
          leaveType: data.leaveType,
          startDate: data.startDate,
          totalDays: data.totalDays,
          systemUser: currentManagerName,
          submissionTime: new Date().toISOString()
        })
      });
    }

    return NextResponse.json({
      success: true,
      message: 'تم تقديم الطلب بنجاح',
      requestId: requestId
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في إنشاء الطلب: ' + error.message
    }, { status: 500 });
  }
}

async function getPaperRequests(pool, data) {
  try {
    let query = `
      SELECT 
        ID, RequestType, EmployeeCode, EmployeeName, Department, JobTitle,
        LeaveType, StartDate, EndDate, DaysCount, LeaveReason,
        MissionDestination, MissionPurpose, MissionDuration,
        PermissionStartTime, PermissionEndTime, PermissionDuration, PermissionReason,
        NightShiftDate, NightShiftReason,
        Status, RequestDate, ApprovalDate, RejectionDate, ApprovalNotes,
        EmergencyContact, EmergencyPhone, ReplacementEmployee, Notes
      FROM PaperRequests
    `;

    const conditions = [];
    const request = pool.request();

    // فلاتر البحث
    if (data.employeeCode) {
      conditions.push('EmployeeCode = @employeeCode');
      request.input('employeeCode', sql.NVarChar, data.employeeCode);
    }

    if (data.requestType && data.requestType !== 'all') {
      conditions.push('RequestType = @requestType');
      request.input('requestType', sql.NVarChar, data.requestType);
    }

    if (data.status && data.status !== 'all') {
      conditions.push('Status = @status');
      request.input('status', sql.NVarChar, data.status);
    }

    if (data.startDate && data.endDate) {
      // فلترة بناءً على تواريخ الإجازة نفسها وليس تاريخ تقديم الطلب
      conditions.push('(StartDate <= @endDate AND EndDate >= @startDate)');
      request.input('startDate', sql.Date, data.startDate);
      request.input('endDate', sql.Date, data.endDate);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY RequestDate DESC';

    const result = await request.query(query);

    // تنسيق التواريخ بصيغة DD/MM/YYYY
    const formattedRequests = result.recordset.map(req => ({
      ...req,
      RequestDate: req.RequestDate ? formatDateToDDMMYYYY(req.RequestDate) : null,
      StartDate: req.StartDate ? formatDateToDDMMYYYY(req.StartDate) : null,
      EndDate: req.EndDate ? formatDateToDDMMYYYY(req.EndDate) : null,
      ApprovalDate: req.ApprovalDate ? formatDateToDDMMYYYY(req.ApprovalDate) : null,
      RejectionDate: req.RejectionDate ? formatDateToDDMMYYYY(req.RejectionDate) : null,
      PermissionDate: req.PermissionDate ? formatDateToDDMMYYYY(req.PermissionDate) : null,
      NightShiftDate: req.NightShiftDate ? formatDateToDDMMYYYY(req.NightShiftDate) : null
    }));

    return NextResponse.json({
      success: true,
      requests: formattedRequests
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب الطلبات: ' + error.message
    }, { status: 500 });
  }
}

async function updateRequestStatus(pool, data, currentManagerName) {
  try {
    const { requestId, status, notes, approvedBy } = data;

    // جلب الحالة السابقة
    const currentData = await pool.request()
      .input('requestId', sql.Int, requestId)
      .query('SELECT Status FROM PaperRequests WHERE ID = @requestId');

    const previousStatus = currentData.recordset[0]?.Status;

    let updateQuery;
    if (status === 'معتمدة') {
      updateQuery = `
        UPDATE PaperRequests
        SET Status = @status, ApprovalDate = GETDATE(), ApprovalNotes = @notes, ApprovedBy = @approvedBy
        WHERE ID = @requestId
      `;
    } else if (status === 'مرفوضة') {
      updateQuery = `
        UPDATE PaperRequests
        SET Status = @status, RejectionDate = GETDATE(), ApprovalNotes = @notes, ApprovedBy = @approvedBy
        WHERE ID = @requestId
      `;
    } else if (status === 'قيد المراجعة') {
      // حالة الرجوع عن الاعتماد
      updateQuery = `
        UPDATE PaperRequests
        SET Status = @status, ApprovalDate = NULL, RejectionDate = NULL, ApprovalNotes = @notes, ApprovedBy = @approvedBy
        WHERE ID = @requestId
      `;
    }

    await pool.request()
      .input('requestId', sql.Int, requestId)
      .input('status', sql.NVarChar, status)
      .input('notes', sql.NVarChar, notes || '')
      .input('approvedBy', sql.NVarChar, approvedBy || '')
      .query(updateQuery);

    // تحديث رصيد الإجازات عند الموافقة أو الرجوع عن الموافقة
    await updateLeaveBalanceAfterStatusChange(pool, requestId, status, previousStatus);

    // تسجيل الإجراء في سجل الإجراءات
    let actionType = '';
    if (status === 'معتمدة') actionType = 'approved';
    else if (status === 'مرفوضة') actionType = 'rejected';
    else if (status === 'قيد المراجعة' && previousStatus === 'معتمدة') actionType = 'reverted';

    await logRequestAction(pool, requestId, actionType, currentManagerName, notes, previousStatus, status);

    // تحديث التمام اليومي حسب الحالة الجديدة
    await updateAttendanceAfterStatusChange(pool, requestId, status, previousStatus);

    // إنشاء إشعار ذكي لتحديث الحالة
    const requestData = await pool.request()
      .input('requestId', sql.Int, requestId)
      .query('SELECT * FROM PaperRequests WHERE ID = @requestId');

    if (requestData.recordset.length > 0) {
      const request = requestData.recordset[0];
      const startDate = request.StartDate ? formatDateToDDMMYYYY(request.StartDate) : 'غير محدد';
      const actionText = status === 'معتمدة' ? 'اعتماد' : status === 'مرفوضة' ? 'رفض' : 'تحديث';

      let notificationMessage = '';
      const arabicLeaveType = getLeaveTypeArabic(request.LeaveType);
      if (status === 'معتمدة') {
        notificationMessage = `تم اعتماد إجازة ${arabicLeaveType} لـ (${request.EmployeeName}) (${request.DaysCount || 0} أيام) تبدأ من (${startDate}) بواسطة (${currentManagerName})`;
      } else if (status === 'مرفوضة') {
        notificationMessage = `تم رفض إجازة ${arabicLeaveType} لـ (${request.EmployeeName}) (${request.DaysCount || 0} أيام) تبدأ من (${startDate}) بواسطة (${currentManagerName})`;
      } else {
        notificationMessage = `تم تحديث إجازة ${arabicLeaveType} لـ (${request.EmployeeName}) (${request.DaysCount || 0} أيام) تبدأ من (${startDate}) بواسطة (${currentManagerName})`;
      }

      if (notes) {
        notificationMessage += ` - ملاحظات: ${notes}`;
      }

      await createSmartNotification(pool, {
        type: status === 'معتمدة' ? 'LEAVE_REQUEST_APPROVED' :
              status === 'مرفوضة' ? 'LEAVE_REQUEST_REJECTED' : 'LEAVE_REQUEST_UPDATED',
        title: `تم ${actionText} طلب الإجازة - ${request.EmployeeName}`,
        message: notificationMessage,
        employeeId: request.EmployeeCode,
        employeeName: request.EmployeeName,
        systemUserId: currentManagerName,
        leaveStartDate: request.StartDate,
        priority: status === 'معتمدة' ? 'medium' : 'high',
        relatedData: JSON.stringify({
          requestId: requestId,
          status: status,
          notes: notes,
          approvedBy: currentManagerName,
          leaveType: request.LeaveType,
          startDate: request.StartDate,
          totalDays: request.DaysCount,
          actionTime: new Date().toISOString()
        })
      });
    }

    return NextResponse.json({
      success: true,
      message: `تم ${status === 'معتمدة' ? 'اعتماد' : status === 'مرفوضة' ? 'رفض' : 'تحديث'} الطلب بنجاح`
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في تحديث حالة الطلب: ' + error.message
    }, { status: 500 });
  }
}

// دالة تسجيل الإجراءات في سجل الطلبات الورقية
async function logRequestAction(pool, requestId, actionType, actionBy, notes, previousStatus, newStatus) {
  try {
    await pool.request()
      .input('requestId', sql.Int, requestId)
      .input('actionType', sql.NVarChar, actionType)
      .input('actionBy', sql.NVarChar, actionBy || 'النظام')
      .input('notes', sql.NVarChar, notes || '')
      .input('previousStatus', sql.NVarChar, previousStatus)
      .input('newStatus', sql.NVarChar, newStatus)
      .query(`
        INSERT INTO PaperRequestActions (
          RequestID, ActionType, ActionBy, Notes, PreviousStatus, NewStatus
        )
        VALUES (
          @requestId, @actionType, @actionBy, @notes, @previousStatus, @newStatus
        )
      `);
  } catch (error) {

  }
}

// جلب سجل الإجراءات لطلب معين
async function getRequestActionLog(pool, data) {
  try {
    const { requestId } = data;

    const query = `
      SELECT
        pra.ID,
        pra.ActionType,
        pra.ActionBy,
        pra.ActionDate,
        pra.Notes,
        pra.PreviousStatus,
        pra.NewStatus,
        pr.EmployeeName,
        pr.EmployeeCode,
        pr.RequestType,
        pr.LeaveType
      FROM PaperRequestActions pra
      INNER JOIN PaperRequests pr ON pra.RequestID = pr.ID
      WHERE pra.RequestID = @requestId
      ORDER BY pra.ActionDate DESC
    `;

    const result = await pool.request()
      .input('requestId', sql.Int, requestId)
      .query(query);

    return NextResponse.json({
      success: true,
      actionLog: result.recordset
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب سجل الإجراءات: ' + error.message
    }, { status: 500 });
  }
}

// دالة لتحويل أنواع الإجازات من الإنجليزية إلى العربية
function getLeaveTypeArabic(leaveType) {
  if (!leaveType) return 'إعتيادية';

  const leaveTypeMap = {
    'annual': 'إعتيادية',
    'emergency': 'عارضة',
    'sick': 'مرضية',
    'unpaid': 'بدون أجر',
    'badal': 'بدل',
    'maternity': 'أمومة',
    'paternity': 'أبوة',
    'hajj': 'حج',
    'marriage': 'زواج',
    'death': 'وفاة',
    'exam': 'امتحانات',
    'other': 'أخرى',
    // المسميات العربية (في حالة كانت مرسلة بالعربية أصلاً)
    'إعتيادية': 'إعتيادية',
    'اعتيادية': 'إعتيادية',
    'عارضة': 'عارضة',
    'مرضية': 'مرضية',
    'بدون أجر': 'بدون أجر',
    'بدل': 'بدل',
    'أمومة': 'أمومة',
    'أبوة': 'أبوة',
    'حج': 'حج',
    'زواج': 'زواج',
    'وفاة': 'وفاة',
    'امتحانات': 'امتحانات',
    'أخرى': 'أخرى'
  };

  return leaveTypeMap[leaveType] || leaveType;
}

// إنشاء إشعار ذكي في النظام المنفصل
async function createSmartNotification(pool, data) {
  try {
    // إنشاء جدول الإشعارات الذكية إذا لم يكن موجوداً
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SmartNotifications' AND xtype='U')
      BEGIN
        CREATE TABLE SmartNotifications (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          NotificationType NVARCHAR(100) NOT NULL,
          Title NVARCHAR(255) NOT NULL,
          Message NVARCHAR(1000) NOT NULL,
          EmployeeID NVARCHAR(50),
          EmployeeName NVARCHAR(255),
          SystemUserID NVARCHAR(50),
          LeaveStartDate DATE,
          RelatedData NVARCHAR(MAX),
          Priority NVARCHAR(20) DEFAULT 'medium',
          Status NVARCHAR(20) DEFAULT 'pending',
          CreatedAt DATETIME DEFAULT GETDATE(),
          ActionTaken BIT DEFAULT 0,
          ActionDate DATETIME,
          ActionBy NVARCHAR(100),
          IsActive BIT DEFAULT 1
        )
      END
    `);

    // إضافة الإشعار الذكي
    await pool.request()
      .input('notificationType', sql.NVarChar, data.type)
      .input('title', sql.NVarChar, data.title)
      .input('message', sql.NVarChar, data.message)
      .input('employeeId', sql.NVarChar, data.employeeId)
      .input('employeeName', sql.NVarChar, data.employeeName)
      .input('systemUserId', sql.NVarChar, data.systemUserId)
      .input('leaveStartDate', sql.Date, data.leaveStartDate ? new Date(data.leaveStartDate) : null)
      .input('relatedData', sql.NVarChar, data.relatedData)
      .input('priority', sql.NVarChar, data.priority)
      .query(`
        INSERT INTO SmartNotifications (
          NotificationType, Title, Message, EmployeeID, EmployeeName,
          SystemUserID, LeaveStartDate, RelatedData, Priority
        )
        VALUES (
          @notificationType, @title, @message, @employeeId, @employeeName,
          @systemUserId, @leaveStartDate, @relatedData, @priority
        )
      `);

  } catch (error) {

  }
}

// دالة التحقق من الطلبات المكررة
async function checkDuplicateRequest(pool, data) {
  try {

    // تحويل التواريخ إلى تنسيق صحيح
    const startDate = new Date(data.startDate);
    const endDate = new Date(data.endDate);

    const duplicateQuery = `
      SELECT COUNT(*) as count
      FROM PaperRequests
      WHERE EmployeeCode = @employeeCode
        AND RequestType = @requestType
        AND CONVERT(DATE, StartDate) = CONVERT(DATE, @startDate)
        AND CONVERT(DATE, EndDate) = CONVERT(DATE, @endDate)
        AND Status IN ('قيد المراجعة', 'معتمدة')
    `;

    const result = await pool.request()
      .input('employeeCode', sql.NVarChar, data.employeeCode)
      .input('requestType', sql.NVarChar, data.requestType)
      .input('startDate', sql.DateTime, startDate)
      .input('endDate', sql.DateTime, endDate)
      .query(duplicateQuery);

    const count = result.recordset[0].count;

    if (count > 0) {
      return {
        isDuplicate: true,
        message: `يوجد طلب مماثل مسجل بالفعل لنفس الموظف في نفس الفترة الزمنية (${count} طلب)`
      };
    }

    return {
      isDuplicate: false,
      message: 'لا يوجد طلب مكرر'
    };

  } catch (error) {

    return {
      isDuplicate: false,
      message: 'خطأ في فحص الطلبات المكررة'
    };
  }
}

// دالة التحقق من تضارب الطلبات
async function checkRequestConflicts(pool, employeeCode, startDate, endDate, requestType, excludeRequestId = null) {
  try {

    // تحويل التواريخ إلى صيغة قاعدة البيانات
    const start = new Date(startDate);
    const end = new Date(endDate);

    // البحث عن الطلبات المتضاربة
    const conflictQuery = `
      SELECT
        ID, RequestType, StartDate, EndDate, Status, EmployeeName,
        CASE
          WHEN RequestType = 'leave' THEN 'إجازة'
          WHEN RequestType = 'mission' THEN 'مأمورية'
          WHEN RequestType = 'permission' THEN 'إذن'
          WHEN RequestType = 'night-shift' THEN 'وردية ليلية'
          ELSE RequestType
        END as RequestTypeArabic
      FROM PaperRequests
      WHERE EmployeeCode = @employeeCode
        AND Status IN ('قيد المراجعة', 'معتمدة')
        AND (
          (StartDate <= @endDate AND EndDate >= @startDate)
        )
        ${excludeRequestId ? 'AND ID != @excludeRequestId' : ''}
      ORDER BY StartDate
    `;

    const request = pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .input('startDate', sql.DateTime, start)
      .input('endDate', sql.DateTime, end);

    if (excludeRequestId) {
      request.input('excludeRequestId', sql.Int, excludeRequestId);
    }

    const result = await request.query(conflictQuery);

    if (result.recordset.length > 0) {
      const conflicts = result.recordset.map(conflict => {
        const conflictStart = formatDateToDDMMYYYY(conflict.StartDate);
        const conflictEnd = formatDateToDDMMYYYY(conflict.EndDate);
        return {
          id: conflict.ID,
          type: conflict.RequestTypeArabic,
          startDate: conflictStart,
          endDate: conflictEnd,
          status: conflict.Status,
          employeeName: conflict.EmployeeName
        };
      });

      return {
        hasConflict: true,
        conflicts: conflicts,
        message: `يوجد تضارب مع ${conflicts[0].type} مسجل لنفس الموظف في الفترة من ${conflicts[0].startDate} إلى ${conflicts[0].endDate} (${conflicts[0].status})`
      };
    }

    return {
      hasConflict: false,
      conflicts: [],
      message: 'لا يوجد تضارب'
    };

  } catch (error) {

    return {
      hasConflict: false,
      conflicts: [],
      message: 'خطأ في فحص التضارب'
    };
  }
}

// دالة تحديث رصيد الإجازات عند تغيير حالة الطلب
async function updateLeaveBalanceAfterStatusChange(pool, requestId, newStatus, previousStatus) {
  try {

    // جلب بيانات الطلب
    const requestData = await pool.request()
      .input('requestId', sql.Int, requestId)
      .query(`
        SELECT EmployeeCode, LeaveType, DaysCount, RequestType
        FROM PaperRequests
        WHERE ID = @requestId
      `);

    if (requestData.recordset.length === 0) {

      return;
    }

    const request = requestData.recordset[0];

    // التأكد من أن الطلب هو طلب إجازة
    if (request.RequestType !== 'leave') {

      return;
    }

    const { EmployeeCode, LeaveType, DaysCount } = request;

    // فحص بنية جدول LeaveBalances لمعرفة أسماء الأعمدة
    const columnCheckResult = await pool.request().query(`
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'LeaveBalances'
      AND COLUMN_NAME IN ('AnnualBalance', 'RegularBalance', 'CasualBalance')
      ORDER BY COLUMN_NAME
    `);

    const hasAnnualBalance = columnCheckResult.recordset.some(col => col.COLUMN_NAME === 'AnnualBalance');
    const hasRegularBalance = columnCheckResult.recordset.some(col => col.COLUMN_NAME === 'RegularBalance');

    // التحقق من وجود الموظف في جدول LeaveBalances
    const employeeBalanceCheck = await pool.request()
      .input('employeeCode', sql.NVarChar, EmployeeCode)
      .query(`
        SELECT COUNT(*) as EmployeeExists
        FROM LeaveBalances
        WHERE EmployeeCode = @employeeCode
      `);

    // إنشاء سجل رصيد جديد إذا لم يكن موجود
    if (employeeBalanceCheck.recordset[0].EmployeeExists === 0) {

      // جلب بيانات الموظف
      const employeeInfo = await pool.request()
        .input('employeeCode', sql.NVarChar, EmployeeCode)
        .query(`
          SELECT TOP 1 EmployeeName, JobTitle, Department
          FROM Employees
          WHERE EmployeeCode = @employeeCode
        `);

      if (employeeInfo.recordset.length > 0) {
        const emp = employeeInfo.recordset[0];

        if (hasAnnualBalance) {
          await pool.request()
            .input('employeeCode', sql.NVarChar, EmployeeCode)
            .input('employeeName', sql.NVarChar, emp.EmployeeName || 'غير محدد')
            .input('jobTitle', sql.NVarChar, emp.JobTitle || 'غير محدد')
            .input('department', sql.NVarChar, emp.Department || 'غير محدد')
            .query(`
              INSERT INTO LeaveBalances (EmployeeCode, EmployeeName, JobTitle, Department, AnnualBalance, CasualBalance)
              VALUES (@employeeCode, @employeeName, @jobTitle, @department, 15, 6)
            `);
        } else {
          await pool.request()
            .input('employeeCode', sql.NVarChar, EmployeeCode)
            .input('employeeName', sql.NVarChar, emp.EmployeeName || 'غير محدد')
            .input('jobTitle', sql.NVarChar, emp.JobTitle || 'غير محدد')
            .input('department', sql.NVarChar, emp.Department || 'غير محدد')
            .query(`
              INSERT INTO LeaveBalances (EmployeeCode, EmployeeName, JobTitle, Department, RegularBalance, CasualBalance)
              VALUES (@employeeCode, @employeeName, @jobTitle, @department, 15, 6)
            `);
        }
      }
    }

    // تحديد نوع الإجازة والعمود المناسب
    let balanceColumn = '';

    if (LeaveType === 'اعتيادية' || LeaveType === 'إعتيادية' || LeaveType === 'annual') {
      balanceColumn = hasAnnualBalance ? 'AnnualBalance' : 'RegularBalance';

    } else if (LeaveType === 'عارضة' || LeaveType === 'casual') {
      balanceColumn = 'CasualBalance';

    } else {

      return;
    }

    // تحديد العملية المطلوبة
    let operation = '';
    if (newStatus === 'معتمدة' && previousStatus !== 'معتمدة') {
      // الموافقة على الطلب - خصم من الرصيد
      operation = 'subtract';
    } else if (previousStatus === 'معتمدة' && newStatus !== 'معتمدة') {
      // الرجوع عن الموافقة - إضافة للرصيد
      operation = 'add';
    } else {

      return;
    }

    // جلب الرصيد الحالي قبل التحديث
    const currentBalanceQuery = `SELECT ${balanceColumn} as CurrentBalance FROM LeaveBalances WHERE EmployeeCode = @employeeCode`;
    const currentBalanceResult = await pool.request()
      .input('employeeCode', sql.NVarChar, request.EmployeeCode)
      .query(currentBalanceQuery);

    const currentBalance = currentBalanceResult.recordset[0]?.CurrentBalance || 0;

    // تنفيذ التحديث
    const updateQuery = operation === 'subtract'
      ? `UPDATE LeaveBalances SET ${balanceColumn} = ${balanceColumn} - @daysCount WHERE EmployeeCode = @employeeCode`
      : `UPDATE LeaveBalances SET ${balanceColumn} = ${balanceColumn} + @daysCount WHERE EmployeeCode = @employeeCode`;

    const updateResult = await pool.request()
      .input('employeeCode', sql.NVarChar, request.EmployeeCode)
      .input('daysCount', sql.Int, request.DaysCount)
      .query(updateQuery);

    // جلب الرصيد الجديد بعد التحديث
    const newBalanceResult = await pool.request()
      .input('employeeCode', sql.NVarChar, request.EmployeeCode)
      .query(currentBalanceQuery);

    const newBalance = newBalanceResult.recordset[0]?.CurrentBalance || 0;

    console.log(`✅ تم تحديث رصيد الإجازات للموظف ${request.EmployeeCode}: ${operation === 'subtract' ? 'خصم' : 'إضافة'} ${request.DaysCount} أيام من ${balanceColumn} (${currentBalance} → ${newBalance})`);

    // إذا كانت إجازة بدون أجر معتمدة، سجلها في جدول منفصل
    if (operation === 'subtract' && (request.LeaveType === 'unpaid' || request.LeaveType === 'بدون أجر') && request.Status === 'معتمدة') {
      await recordUnpaidLeave(request);
    }

  } catch (error) {

    // لا نوقف العملية في حالة فشل تحديث الرصيد
  }
}

// دالة تنظيف الطلبات المكررة
async function cleanupDuplicateRequests(pool, data) {
  try {

    // البحث عن الطلبات المكررة
    const duplicatesQuery = `
      SELECT
        EmployeeCode, RequestType,
        CONVERT(VARCHAR, StartDate, 23) as StartDate,
        CONVERT(VARCHAR, EndDate, 23) as EndDate,
        Status,
        COUNT(*) as DuplicateCount,
        MIN(ID) as KeepID
      FROM PaperRequests
      GROUP BY EmployeeCode, RequestType,
               CONVERT(VARCHAR, StartDate, 23),
               CONVERT(VARCHAR, EndDate, 23),
               Status
      HAVING COUNT(*) > 1
      ORDER BY EmployeeCode, StartDate
    `;

    const duplicatesResult = await pool.request().query(duplicatesQuery);
    const duplicates = duplicatesResult.recordset;

    if (duplicates.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'لا توجد طلبات مكررة',
        cleaned: 0
      });
    }

    let totalCleaned = 0;

    // حذف الطلبات المكررة (الاحتفاظ بالأقدم)
    for (const duplicate of duplicates) {
      // البحث عن جميع الطلبات المطابقة للحصول على IDs
      const findDuplicatesQuery = `
        SELECT ID FROM PaperRequests
        WHERE EmployeeCode = @employeeCode
          AND RequestType = @requestType
          AND CONVERT(VARCHAR, StartDate, 23) = @startDate
          AND CONVERT(VARCHAR, EndDate, 23) = @endDate
          AND Status = @status
          AND ID != @keepID
        ORDER BY ID
      `;

      const findResult = await pool.request()
        .input('employeeCode', sql.NVarChar, duplicate.EmployeeCode)
        .input('requestType', sql.NVarChar, duplicate.RequestType)
        .input('startDate', sql.VarChar, duplicate.StartDate)
        .input('endDate', sql.VarChar, duplicate.EndDate)
        .input('status', sql.NVarChar, duplicate.Status)
        .input('keepID', sql.Int, duplicate.KeepID)
        .query(findDuplicatesQuery);

      const idsToDelete = findResult.recordset.map(row => row.ID);

      if (idsToDelete.length > 0) {
        // حذف الطلبات المكررة واحد تلو الآخر
        for (const idToDelete of idsToDelete) {
          const deleteQuery = `DELETE FROM PaperRequests WHERE ID = @id`;
          await pool.request()
            .input('id', sql.Int, idToDelete)
            .query(deleteQuery);

          totalCleaned++;
        }

      }
    }

    return NextResponse.json({
      success: true,
      message: `تم تنظيف ${totalCleaned} طلب مكرر بنجاح`,
      cleaned: totalCleaned,
      duplicatesFound: duplicates.length
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في تنظيف الطلبات المكررة: ' + error.message
    }, { status: 500 });
  }
}

// دالة إصلاح حالة طلبات المأمورية
async function fixMissionStatus(pool) {
  try {

    // تحديث جميع طلبات المأمورية لتصبح معتمدة
    const updateResult = await pool.request().query(`
      UPDATE PaperRequests
      SET Status = N'معتمدة',
          ApprovalDate = GETDATE(),
          ApprovedBy = N'النظام - إصلاح تلقائي'
      WHERE (
        RequestType = 'mission'
        OR LeaveType LIKE N'%مأمورية%'
        OR LeaveType = N'مأمورية'
      )
      AND Status = N'قيد المراجعة';

      SELECT @@ROWCOUNT as UpdatedCount;
    `);

    const updatedCount = updateResult.recordset[0].UpdatedCount;

    // جلب إحصائيات نهائية
    const statsResult = await pool.request().query(`
      SELECT
        COUNT(*) as TotalMissions,
        SUM(CASE WHEN Status = N'معتمد' THEN 1 ELSE 0 END) as ApprovedMissions,
        SUM(CASE WHEN Status = N'قيد المراجعة' THEN 1 ELSE 0 END) as PendingMissions
      FROM PaperRequests
      WHERE RequestType = 'mission'
         OR LeaveType LIKE N'%مأمورية%'
         OR LeaveType = N'مأمورية'
    `);

    const stats = statsResult.recordset[0];

    return NextResponse.json({
      success: true,
      message: `تم إصلاح ${updatedCount} طلب مأمورية بنجاح`,
      data: {
        updatedCount,
        totalMissions: stats.TotalMissions,
        approvedMissions: stats.ApprovedMissions,
        pendingMissions: stats.PendingMissions
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في إصلاح حالة المأمورية: ' + error.message
    }, { status: 500 });
  }
}

// دالة إصلاح جميع المأموريات والطلبات الأخرى لتكون قيد المراجعة
async function fixAllMissions(pool) {
  try {

    // إصلاح جميع المأموريات والطلبات الأخرى (غير الإجازات) لتكون قيد المراجعة
    const updateQuery = `
      UPDATE PaperRequests
      SET Status = N'قيد المراجعة',
          ApprovalDate = NULL,
          ApprovedBy = NULL
      WHERE RequestType IN ('mission', 'permission', 'night_shift')
        AND Status = N'معتمد'
    `;

    const result = await pool.request().query(updateQuery);

    return NextResponse.json({
      success: true,
      message: `تم إصلاح ${result.rowsAffected[0]} طلب (مأمورية، إذن، وردية ليلية) ليصبح قيد المراجعة`,
      updatedCount: result.rowsAffected[0]
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في إصلاح الطلبات'
    }, { status: 500 });
  }
}

export async function GET(request) {
  const { searchParams } = new URL(request.url);
  const action = searchParams.get('action') || 'list';

  // معالجة خاصة لجلب تاريخ آخر إجازة معتمدة
  if (action === 'getLastApprovedLeave') {
    const employeeId = searchParams.get('employeeId');

    if (!employeeId) {
      return NextResponse.json({
        success: false,
        error: 'كود الموظف مطلوب'
      }, { status: 400 });
    }

    try {
      const pool = await sql.connect(dbConfig);

      // البحث عن آخر إجازة معتمدة للموظف
      const result = await pool.request()
        .input('employeeId', sql.NVarChar, employeeId)
        .query(`
          SELECT TOP 1 EndDate
          FROM PaperRequests
          WHERE EmployeeCode = @employeeId
            AND RequestType = 'leave'
            AND Status = 'معتمدة'
          ORDER BY EndDate DESC
        `);

      if (result.recordset.length > 0) {
        return NextResponse.json({
          success: true,
          lastLeave: result.recordset[0]
        });
      } else {
        return NextResponse.json({
          success: true,
          lastLeave: null
        });
      }
    } catch (error) {

      return NextResponse.json({
        success: false,
        error: 'خطأ في جلب البيانات'
      }, { status: 500 });
    }
  }

  const data = {
    action,
    employeeCode: searchParams.get('employeeCode'),
    requestType: searchParams.get('requestType'),
    status: searchParams.get('status'),
    startDate: searchParams.get('startDate'),
    endDate: searchParams.get('endDate'),
    requestId: searchParams.get('requestId')
  };

  return POST({ json: () => Promise.resolve(data) });
}

async function getPrintData(pool, data) {
  try {
    const { requestId } = data;

    const query = `
      SELECT * FROM PaperRequests WHERE ID = @requestId
    `;

    const result = await pool.request()
      .input('requestId', sql.Int, requestId)
      .query(query);

    if (result.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'لم يتم العثور على الطلب'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: result.recordset[0]
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب بيانات الطباعة: ' + error.message
    }, { status: 500 });
  }
}

// دالة حذف الطلب
async function deleteRequest(pool, data) {
  try {
    const { requestId } = data;

    if (!requestId) {
      return NextResponse.json({
        success: false,
        error: 'معرف الطلب مطلوب'
      }, { status: 400 });
    }

    const request = pool.request();
    request.input('requestId', sql.Int, requestId);

    // التحقق من وجود الطلب أولاً وجلب جميع البيانات المطلوبة
    const checkQuery = `
      SELECT ID, Status, RequestType, EmployeeCode, EmployeeName,
             StartDate, EndDate, LeaveType, MissionDestination
      FROM PaperRequests
      WHERE ID = @requestId
    `;

    const checkResult = await request.query(checkQuery);

    if (checkResult.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الطلب غير موجود'
      }, { status: 404 });
    }

    const requestData = checkResult.recordset[0];

    // للطلبات المعتمدة، نحذف من التمام اليومي أولاً
    if (requestData.Status === 'معتمدة') {

      // حذف من التمام اليومي باستخدام معرف الطلب
      await removeAttendanceFromRequest(pool, {
        requestId: requestData.ID,
        employeeCode: requestData.EmployeeCode,
        startDate: requestData.StartDate,
        endDate: requestData.EndDate
      });

      // إذا كانت إجازة معتمدة، نعيد رصيد الإجازة
      if (requestData.RequestType === 'leave') {
        await restoreLeaveBalance(pool, {
          employeeCode: requestData.EmployeeCode,
          leaveType: requestData.LeaveType,
          daysCount: calculateDaysBetween(requestData.StartDate, requestData.EndDate)
        });

      }
    }

    // حذف الطلب
    const deleteQuery = `
      DELETE FROM PaperRequests
      WHERE ID = @requestId
    `;

    await request.query(deleteQuery);

    return NextResponse.json({
      success: true,
      message: 'تم حذف الطلب بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في حذف الطلب'
    }, { status: 500 });
  }
}

// دالة تنظيف الطلبات المكررة
export async function PATCH(request) {
  try {
    const { action } = await request.json();

    if (action === 'cleanup-duplicates') {
      const pool = await connectToDatabase();

      // البحث عن الطلبات المكررة وحذف النسخ الإضافية
      const duplicatesQuery = `
        WITH DuplicateRequests AS (
          SELECT
            ID,
            EmployeeCode,
            RequestType,
            StartDate,
            EndDate,
            Status,
            RequestDate,
            ROW_NUMBER() OVER (
              PARTITION BY EmployeeCode, RequestType,
                          CONVERT(DATE, StartDate), CONVERT(DATE, EndDate), Status
              ORDER BY RequestDate DESC, ID DESC
            ) as rn
          FROM PaperRequests
          WHERE Status IN ('قيد المراجعة', 'معتمدة')
        )
        DELETE FROM PaperRequests
        WHERE ID IN (
          SELECT ID FROM DuplicateRequests WHERE rn > 1
        )
      `;

      const result = await pool.request().query(duplicatesQuery);

      return NextResponse.json({
        success: true,
        deletedCount: result.rowsAffected[0] || 0,
        message: `تم حذف ${result.rowsAffected[0] || 0} طلب مكرر`
      });
    }

    return NextResponse.json({
      success: false,
      error: 'إجراء غير صحيح'
    }, { status: 400 });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في تنظيف الطلبات المكررة'
    }, { status: 500 });
  }
}

// دالة تسجيل الإجازة بدون أجر في جدول منفصل
async function recordUnpaidLeave(request) {
  try {

    const pool = await getConnection();

    // التحقق من وجود الجدول وإنشاؤه إذا لم يكن موجود
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='UnpaidLeaves' AND xtype='U')
      BEGIN
        CREATE TABLE UnpaidLeaves (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          EmployeeCode NVARCHAR(20) NOT NULL,
          EmployeeName NVARCHAR(100) NOT NULL,
          LeaveDate DATE NOT NULL,
          DaysCount INT NOT NULL DEFAULT 1,
          StartDate DATE NOT NULL,
          EndDate DATE NOT NULL,
          Reason NVARCHAR(500),
          Status NVARCHAR(20) DEFAULT N'معتمدة',
          RequestDate DATETIME DEFAULT GETDATE(),
          ApprovalDate DATETIME NULL,
          ApprovedBy NVARCHAR(100) NULL,
          Notes NVARCHAR(500),
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE()
        )
      END
    `);

    // التحقق من عدم وجود السجل مسبقاً (تجنب التكرار)
    const existingRecord = await pool.request()
      .input('employeeCode', sql.NVarChar, request.EmployeeCode)
      .input('startDate', sql.Date, request.StartDate)
      .input('endDate', sql.Date, request.EndDate)
      .query(`
        SELECT COUNT(*) as count
        FROM UnpaidLeaves
        WHERE EmployeeCode = @employeeCode
          AND StartDate = @startDate
          AND EndDate = @endDate
      `);

    if (existingRecord.recordset[0].count > 0) {

      return;
    }

    // إدراج السجل الجديد
    await pool.request()
      .input('employeeCode', sql.NVarChar, request.EmployeeCode)
      .input('employeeName', sql.NVarChar, request.EmployeeName)
      .input('leaveDate', sql.Date, request.StartDate)
      .input('daysCount', sql.Int, request.DaysCount)
      .input('startDate', sql.Date, request.StartDate)
      .input('endDate', sql.Date, request.EndDate)
      .input('reason', sql.NVarChar, request.Reason || '')
      .input('approvalDate', sql.DateTime, new Date())
      .input('approvedBy', sql.NVarChar, 'النظام')
      .query(`
        INSERT INTO UnpaidLeaves (
          EmployeeCode, EmployeeName, LeaveDate, DaysCount,
          StartDate, EndDate, Reason, Status, ApprovalDate, ApprovedBy
        )
        VALUES (
          @employeeCode, @employeeName, @leaveDate, @daysCount,
          @startDate, @endDate, @reason, N'معتمدة', @approvalDate, @approvedBy
        )
      `);

  } catch (error) {

  }
}

// دالة تحديث التمام اليومي عند تغيير حالة الطلب
async function updateAttendanceAfterStatusChange(pool, requestId, newStatus, previousStatus) {
  try {

    // جلب بيانات الطلب
    const requestData = await pool.request()
      .input('requestId', sql.Int, requestId)
      .query(`
        SELECT
          ID, RequestType, EmployeeCode, EmployeeName, Department, JobTitle,
          LeaveType, StartDate, EndDate, DaysCount,
          MissionDestination as Destination
        FROM PaperRequests
        WHERE ID = @requestId
      `);

    if (requestData.recordset.length === 0) {

      return;
    }

    const request = requestData.recordset[0];

    // إذا تم اعتماد الطلب، نضيف للتمام
    if (newStatus === 'معتمدة' && previousStatus !== 'معتمدة') {
      await updateAttendanceFromApprovedRequest(pool, {
        requestId: request.ID,
        requestType: request.RequestType,
        employeeCode: request.EmployeeCode,
        startDate: request.StartDate,
        endDate: request.EndDate,
        leaveType: request.LeaveType,
        destination: request.Destination
      });
    }
    // إذا تم إلغاء الاعتماد، نحذف من التمام
    else if (previousStatus === 'معتمدة' && newStatus !== 'معتمدة') {
      await removeAttendanceFromRequest(pool, {
        requestId: request.ID,
        employeeCode: request.EmployeeCode,
        startDate: request.StartDate,
        endDate: request.EndDate
      });
    }

  } catch (error) {

  }
}

// دالة تحديث التمام من طلب معتمد
async function updateAttendanceFromApprovedRequest(pool, data) {
  try {
    const { requestId, requestType, employeeCode, startDate, endDate, leaveType, destination } = data;

    // تحديد نوع التمام والملاحظات
    let attendanceType = '';
    let notes = '';

    if (requestType === 'leave') {
      attendanceType = getLeaveAttendanceType(leaveType);
      notes = `تنتهي في ${endDate}`;
    } else if (requestType === 'mission') {
      attendanceType = 'مأمورية';
      notes = `مأمورية إلى ${destination} - تنتهي في ${endDate}`;
    } else {
      attendanceType = requestType;
      notes = `${requestType} - تنتهي في ${endDate}`;
    }

    // إنشاء قائمة التواريخ
    const dates = generateDateRange(startDate, endDate);

    for (const date of dates) {
      try {
        // التحقق من وجود سجل تمام لهذا اليوم
        const existingAttendance = await pool.request()
          .input('employeeCode', sql.NVarChar, employeeCode)
          .input('date', sql.Date, date)
          .query(`
            SELECT ID FROM DailyAttendance
            WHERE EmployeeCode = @employeeCode AND AttendanceDate = @date
          `);

        if (existingAttendance.recordset.length > 0) {
          // تحديث السجل الموجود
          await pool.request()
            .input('employeeCode', sql.NVarChar, employeeCode)
            .input('date', sql.Date, date)
            .input('attendance', sql.NVarChar, attendanceType)
            .input('notes', sql.NVarChar, notes)
            .input('requestId', sql.Int, requestId)
            .query(`
              UPDATE DailyAttendance
              SET
                Attendance = @attendance,
                Notes = @notes,
                IsFromRequest = 1,
                RequestID = @requestId,
                UpdatedAt = GETDATE()
              WHERE EmployeeCode = @employeeCode AND AttendanceDate = @date
            `);
        } else {
          // إنشاء سجل جديد
          await pool.request()
            .input('employeeCode', sql.NVarChar, employeeCode)
            .input('date', sql.Date, date)
            .input('attendance', sql.NVarChar, attendanceType)
            .input('notes', sql.NVarChar, notes)
            .input('requestId', sql.Int, requestId)
            .query(`
              INSERT INTO DailyAttendance (
                AttendanceDate, EmployeeCode, EmployeeName, Department, JobTitle,
                Attendance, attendanceStatus, Notes, IsFromRequest, RequestID, CreatedAt
              ) VALUES (
                @date, @employeeCode,
                (SELECT TOP 1 EmployeeName FROM Employees WHERE EmployeeCode = @employeeCode),
                (SELECT TOP 1 Department FROM Employees WHERE EmployeeCode = @employeeCode),
                (SELECT TOP 1 JobTitle FROM Employees WHERE EmployeeCode = @employeeCode),
                @attendance, @attendance, @notes, 1, @requestId, GETDATE()
              )
            `);
        }

      } catch (dayError) {

      }
    }

  } catch (error) {

  }
}

// دالة حذف التمام من طلب ملغي
async function removeAttendanceFromRequest(pool, data) {
  try {
    const { requestId, employeeCode, startDate, endDate } = data;

    // حذف سجلات التمام المرتبطة بهذا الطلب
    const deleteResult = await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .input('startDate', sql.Date, startDate)
      .input('endDate', sql.Date, endDate)
      .input('requestId', sql.Int, requestId)
      .query(`
        DELETE FROM DailyAttendance
        WHERE EmployeeCode = @employeeCode
          AND AttendanceDate BETWEEN @startDate AND @endDate
          AND IsFromRequest = 1
          AND RequestID = @requestId
      `);

    const deletedRows = deleteResult.rowsAffected[0];

  } catch (error) {

  }
}

// دوال مساعدة للتمام

// تحديد نوع التمام للإجازة
function getLeaveAttendanceType(leaveType) {
  const leaveTypes = {
    'إعتيادية': 'إجازة إعتيادية',
    'اعتيادية': 'إجازة إعتيادية',
    'عارضة': 'إجازة عارضة',
    'مرضية': 'إجازة مرضية',
    'أمومة': 'إجازة أمومة',
    'أبوة': 'إجازة أبوة',
    'بدون أجر': 'إجازة بدون أجر',
    'بدل': 'إجازة بدل'
  };

  return leaveTypes[leaveType] || `إجازة ${leaveType}`;
}

// إنشاء قائمة التواريخ
function generateDateRange(startDate, endDate) {
  const dates = [];
  const start = new Date(startDate);
  const end = new Date(endDate);

  for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
    dates.push(date.toISOString().split('T')[0]);
  }

  return dates;
}

// حساب عدد الأيام بين تاريخين
function calculateDaysBetween(startDate, endDate) {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const diffTime = Math.abs(end - start);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 لتضمين اليوم الأول
  return diffDays;
}

// دالة استرداد رصيد الإجازة عند حذف طلب معتمد
async function restoreLeaveBalance(pool, data) {
  try {
    const { employeeCode, leaveType, daysCount } = data;

    // تحديد نوع الرصيد المطلوب استرداده
    let balanceColumn = '';
    switch (leaveType) {
      case 'إعتيادية':
      case 'اعتيادية':
        balanceColumn = 'AnnualBalance';
        break;
      case 'عارضة':
        balanceColumn = 'EmergencyBalance';
        break;
      case 'مرضية':
        balanceColumn = 'SickBalance';
        break;
      case 'بدل':
        balanceColumn = 'CompensatoryBalance';
        break;
      default:

        return;
    }

    // التحقق من وجود جدول أرصدة الإجازات
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LeaveBalances' AND xtype='U')
      BEGIN
        CREATE TABLE LeaveBalances (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          EmployeeCode NVARCHAR(20) NOT NULL UNIQUE,
          EmployeeName NVARCHAR(100),
          AnnualBalance INT DEFAULT 30,
          EmergencyBalance INT DEFAULT 7,
          SickBalance INT DEFAULT 30,
          CompensatoryBalance INT DEFAULT 0,
          LastUpdated DATETIME DEFAULT GETDATE()
        )
      END
    `);

    // استرداد الرصيد
    const updateQuery = `
      IF EXISTS (SELECT 1 FROM LeaveBalances WHERE EmployeeCode = @employeeCode)
      BEGIN
        UPDATE LeaveBalances
        SET ${balanceColumn} = ${balanceColumn} + @daysCount,
            LastUpdated = GETDATE()
        WHERE EmployeeCode = @employeeCode
      END
      ELSE
      BEGIN
        INSERT INTO LeaveBalances (EmployeeCode, EmployeeName, ${balanceColumn})
        SELECT @employeeCode, EmployeeName, @daysCount
        FROM Employees
        WHERE EmployeeCode = @employeeCode
      END
    `;

    await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .input('daysCount', sql.Int, daysCount)
      .query(updateQuery);

  } catch (error) {

  }
}