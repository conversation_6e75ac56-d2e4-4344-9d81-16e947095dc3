// تصريحات TypeScript العامة للمشروع

// تصريحات للأيقونات - حل شامل
declare module 'react-icons/fi' {
  import { ComponentType, SVGProps } from 'react';

  // تصريح عام لجميع الأيقونات
  interface IconBaseProps extends SVGProps<SVGSVGElement> {
    className?: string;
    size?: string | number;
    color?: string;
    style?: React.CSSProperties;
    [key: string]: any;
  }

  // تصريح لجميع الأيقونات المستخدمة
  export const FiUsers: ComponentType<IconBaseProps>;
  export const FiHome: ComponentType<IconBaseProps>;
  export const FiTruck: ComponentType<IconBaseProps>;
  export const FiDollarSign: ComponentType<IconBaseProps>;
  export const FiTrendingUp: ComponentType<IconBaseProps>;
  export const FiBarChart: ComponentType<IconBaseProps>;
  export const FiPieChart: ComponentType<IconBaseProps>;
  export const FiActivity: ComponentType<IconBaseProps>;
  export const FiCalendar: ComponentType<IconBaseProps>;
  export const FiLogOut: ComponentType<IconBaseProps>;
  export const FiSettings: ComponentType<IconBaseProps>;
  export const FiUser: ComponentType<IconBaseProps>;
  export const FiRefreshCw: ComponentType<IconBaseProps>;
  export const FiSun: ComponentType<IconBaseProps>;
  export const FiMoon: ComponentType<IconBaseProps>;
  export const FiArrowRight: ComponentType<IconBaseProps>;
  export const FiPlus: ComponentType<IconBaseProps>;
  export const FiUserX: ComponentType<IconBaseProps>;
  export const FiArrowLeft: ComponentType<IconBaseProps>;
  export const FiMapPin: ComponentType<IconBaseProps>;
  export const FiShield: ComponentType<IconBaseProps>;
  export const FiBell: ComponentType<IconBaseProps>;
  export const FiCheckCircle: ComponentType<IconBaseProps>;
  export const FiXCircle: ComponentType<IconBaseProps>;
  export const FiAlertCircle: ComponentType<IconBaseProps>;
  export const FiHeart: ComponentType<IconBaseProps>;
  export const FiGlobe: ComponentType<IconBaseProps>;
}

// تصريحات لـ React JSX Runtime
declare module 'react/jsx-runtime' {
  export * from 'react/jsx-runtime';
}

// تصريحات للمتغيرات البيئية
declare namespace NodeJS {
  interface ProcessEnv {
    NODE_ENV: 'development' | 'production' | 'test';
    NEXT_PUBLIC_API_URL?: string;
    DATABASE_URL?: string;
    [key: string]: string | undefined;
  }
}

// تصريحات للنوافذ العامة
declare global {
  interface Window {
    [key: string]: any;
  }
}

export {};
