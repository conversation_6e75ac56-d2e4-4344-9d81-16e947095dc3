#!/usr/bin/env node
// أداة إنشاء التراخيص

const fs = require('fs');
const path = require('path');
const readline = require('readline');
const LicenseSystem = require('./license-system');
const ProtectedBuilder = require('./build-protected');

class LicenseGenerator {
  constructor() {
    this.licenseSystem = new LicenseSystem();
    this.protectedBuilder = new ProtectedBuilder();
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  // سؤال المستخدم
  async askQuestion(question) {
    return new Promise((resolve) => {
      this.rl.question(question, (answer) => {
        resolve(answer.trim());
      });
    });
  }

  // جمع معلومات العميل
  async collectCustomerInfo() {
    console.log('\n🔐 مولد التراخيص - نظام إدارة التكاليف');
    console.log('==========================================\n');

    const customerInfo = {};

    customerInfo.id = await this.askQuestion('🆔 رقم العميل (أو اتركه فارغاً للتوليد التلقائي): ');
    if (!customerInfo.id) {
      customerInfo.id = 'CUST-' + Date.now().toString(36).toUpperCase();
    }

    customerInfo.name = await this.askQuestion('👤 اسم العميل: ');
    customerInfo.email = await this.askQuestion('📧 البريد الإلكتروني: ');
    customerInfo.company = await this.askQuestion('🏢 اسم الشركة (اختياري): ');

    // نوع الترخيص
    console.log('\n📋 أنواع التراخيص المتاحة:');
    console.log('1. ترخيص دائم');
    console.log('2. ترخيص لمدة سنة');
    console.log('3. ترخيص لمدة 6 أشهر');
    console.log('4. ترخيص تجريبي (30 يوم)');
    
    const licenseType = await this.askQuestion('اختر نوع الترخيص (1-4): ');
    
    switch (licenseType) {
      case '1':
        customerInfo.expiryDate = null; // دائم
        break;
      case '2':
        customerInfo.expiryDate = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString();
        break;
      case '3':
        customerInfo.expiryDate = new Date(Date.now() + 180 * 24 * 60 * 60 * 1000).toISOString();
        break;
      case '4':
        customerInfo.expiryDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();
        break;
      default:
        customerInfo.expiryDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString();
    }

    // الميزات المتاحة
    console.log('\n🎯 الميزات المتاحة:');
    console.log('1. جميع الميزات');
    console.log('2. إدارة التكاليف فقط');
    console.log('3. طلبات الإصدار فقط');
    console.log('4. ميزات مخصصة');
    
    const featuresChoice = await this.askQuestion('اختر الميزات (1-4): ');
    
    switch (featuresChoice) {
      case '1':
        customerInfo.features = ['all'];
        break;
      case '2':
        customerInfo.features = ['costs-management'];
        break;
      case '3':
        customerInfo.features = ['version-requests'];
        break;
      case '4':
        const customFeatures = await this.askQuestion('أدخل الميزات مفصولة بفاصلة: ');
        customerInfo.features = customFeatures.split(',').map(f => f.trim());
        break;
      default:
        customerInfo.features = ['all'];
    }

    customerInfo.version = await this.askQuestion('إصدار النظام (افتراضي: 1.0.0): ') || '1.0.0';

    return customerInfo;
  }

  // إنشاء ترخيص فقط
  async generateLicenseOnly() {
    try {
      const customerInfo = await this.collectCustomerInfo();
      
      console.log('\n🔄 جاري إنشاء الترخيص...');
      
      const license = this.licenseSystem.generateLicense(customerInfo);
      
      // حفظ الترخيص
      const licensesDir = path.join(__dirname, '../licenses');
      if (!fs.existsSync(licensesDir)) {
        fs.mkdirSync(licensesDir, { recursive: true });
      }
      
      const licenseFileName = `license-${customerInfo.id}-${Date.now()}.json`;
      const licensePath = path.join(licensesDir, licenseFileName);
      
      const licenseData = {
        customerInfo,
        license,
        generatedAt: new Date().toISOString(),
        hardwareId: this.licenseSystem.hardwareId
      };
      
      fs.writeFileSync(licensePath, JSON.stringify(licenseData, null, 2));
      
      console.log('\n✅ تم إنشاء الترخيص بنجاح!');
      console.log(`📁 مسار الملف: ${licensePath}`);
      console.log(`🆔 رقم العميل: ${customerInfo.id}`);
      console.log(`👤 اسم العميل: ${customerInfo.name}`);
      
      if (customerInfo.expiryDate) {
        console.log(`⏰ تاريخ الانتهاء: ${new Date(customerInfo.expiryDate).toLocaleDateString('ar-EG')}`);
      } else {
        console.log('⏰ ترخيص دائم');
      }
      
      return licensePath;
      
    } catch (error) {
      console.error('❌ خطأ في إنشاء الترخيص:', error.message);
      throw error;
    }
  }

  // إنشاء نسخة محمية كاملة
  async generateProtectedVersion() {
    try {
      const customerInfo = await this.collectCustomerInfo();
      
      const buildProtected = await this.askQuestion('\n🔨 هل تريد بناء نسخة محمية كاملة؟ (y/n): ');
      
      if (buildProtected.toLowerCase() === 'y' || buildProtected.toLowerCase() === 'yes') {
        console.log('\n🔄 جاري إنشاء النسخة المحمية...');
        const packagePath = await this.protectedBuilder.buildProtectedVersion(customerInfo);
        
        console.log('\n✅ تم إنشاء النسخة المحمية بنجاح!');
        console.log(`📦 مسار الحزمة: ${packagePath}`);
        
        return packagePath;
      } else {
        return await this.generateLicenseOnly();
      }
      
    } catch (error) {
      console.error('❌ خطأ في إنشاء النسخة المحمية:', error.message);
      throw error;
    }
  }

  // عرض القائمة الرئيسية
  async showMainMenu() {
    console.log('\n🔐 مولد التراخيص - نظام إدارة التكاليف');
    console.log('==========================================');
    console.log('1. إنشاء ترخيص فقط');
    console.log('2. إنشاء نسخة محمية كاملة');
    console.log('3. عرض التراخيص الموجودة');
    console.log('4. التحقق من ترخيص');
    console.log('5. خروج');
    
    const choice = await this.askQuestion('\nاختر العملية (1-5): ');
    
    switch (choice) {
      case '1':
        await this.generateLicenseOnly();
        break;
      case '2':
        await this.generateProtectedVersion();
        break;
      case '3':
        await this.listExistingLicenses();
        break;
      case '4':
        await this.validateExistingLicense();
        break;
      case '5':
        console.log('👋 وداعاً!');
        this.rl.close();
        return;
      default:
        console.log('❌ اختيار غير صالح');
    }
    
    // العودة للقائمة الرئيسية
    await this.showMainMenu();
  }

  // عرض التراخيص الموجودة
  async listExistingLicenses() {
    const licensesDir = path.join(__dirname, '../licenses');
    
    if (!fs.existsSync(licensesDir)) {
      console.log('📭 لا توجد تراخيص');
      return;
    }
    
    const files = fs.readdirSync(licensesDir).filter(f => f.endsWith('.json'));
    
    if (files.length === 0) {
      console.log('📭 لا توجد تراخيص');
      return;
    }
    
    console.log('\n📋 التراخيص الموجودة:');
    console.log('==================');
    
    files.forEach((file, index) => {
      try {
        const licenseData = JSON.parse(fs.readFileSync(path.join(licensesDir, file), 'utf8'));
        const customer = licenseData.customerInfo;
        
        console.log(`${index + 1}. ${customer.name} (${customer.id})`);
        console.log(`   📧 ${customer.email}`);
        console.log(`   📅 ${new Date(licenseData.generatedAt).toLocaleDateString('ar-EG')}`);
        
        if (customer.expiryDate) {
          const isExpired = new Date() > new Date(customer.expiryDate);
          console.log(`   ⏰ ${new Date(customer.expiryDate).toLocaleDateString('ar-EG')} ${isExpired ? '(منتهي)' : '(نشط)'}`);
        } else {
          console.log('   ⏰ دائم');
        }
        console.log('');
      } catch (error) {
        console.log(`❌ خطأ في قراءة ${file}`);
      }
    });
  }

  // التحقق من ترخيص موجود
  async validateExistingLicense() {
    const validation = this.licenseSystem.validateLicense();
    
    console.log('\n🔍 نتيجة التحقق من الترخيص:');
    console.log('============================');
    
    if (validation.valid) {
      console.log('✅ الترخيص صالح');
      console.log(`👤 العميل: ${validation.data.customerName}`);
      console.log(`📧 البريد: ${validation.data.email}`);
      console.log(`🆔 رقم العميل: ${validation.data.customerId}`);
      
      if (validation.data.expiryDate) {
        console.log(`⏰ ينتهي في: ${new Date(validation.data.expiryDate).toLocaleDateString('ar-EG')}`);
      } else {
        console.log('⏰ ترخيص دائم');
      }
      
      console.log(`🎯 الميزات: ${validation.data.features.join(', ')}`);
    } else {
      console.log('❌ الترخيص غير صالح');
      console.log(`🔍 السبب: ${validation.error}`);
    }
  }

  // تشغيل التطبيق
  async run() {
    try {
      await this.showMainMenu();
    } catch (error) {
      console.error('❌ خطأ في التطبيق:', error.message);
    } finally {
      this.rl.close();
    }
  }
}

// تشغيل التطبيق إذا تم استدعاؤه مباشرة
if (require.main === module) {
  const generator = new LicenseGenerator();
  generator.run().catch(console.error);
}

module.exports = LicenseGenerator;
