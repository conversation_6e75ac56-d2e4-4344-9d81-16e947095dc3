import { NextResponse } from 'next/server';
import { getConnection } from '@/lib/db';
import sql from 'mssql';

// GET - جلب البيانات الحقيقية للتكاليف
export async function GET(request) {
  let pool;
  
  try {
    pool = await getConnection();
    console.log('🔄 جلب البيانات الحقيقية للتكاليف...');

    // 1. جلب بيانات السيارات الحقيقية
    const carsResult = await pool.request().query(`
      SELECT
        COUNT(*) as totalCars,
        SUM(RentAmount) as monthlyTotal,
        SUM(RentAmount * 12) as annualTotal,
        AVG(RentAmount) as averageRent
      FROM Cars
      WHERE IsActive = 1
    `);

    // 2. جلب بيانات الشقق الحقيقية
    const apartmentsResult = await pool.request().query(`
      SELECT
        COUNT(*) as totalApartments,
        SUM(RentAmount) as monthlyTotal,
        SUM(RentAmount * 12) as annualTotal,
        AVG(RentAmount) as averageRent
      FROM Apartments
      WHERE IsActive = 1
    `);

    // 3. جلب بيانات العمالة المؤقتة الحقيقية
    const tempWorkersResult = await pool.request().query(`
      SELECT
        COUNT(*) as totalWorkers,
        SUM(MonthlySalary) as monthlyTotal,
        SUM(MonthlySalary * 12) as annualTotal,
        AVG(MonthlySalary) as averageSalary
      FROM TempWorkers
      WHERE IsActive = 1
    `);

    // 4. جلب بيانات المستفيدين
    const beneficiariesResult = await pool.request().query(`
      SELECT
        (SELECT COUNT(DISTINCT EmployeeCode) FROM CarBeneficiaries WHERE IsActive = 1) as carBeneficiaries,
        (SELECT COUNT(DISTINCT EmployeeCode) FROM ApartmentBeneficiaries WHERE IsActive = 1) as apartmentBeneficiaries
    `);

    const carsData = carsResult.recordset[0] || {};
    const apartmentsData = apartmentsResult.recordset[0] || {};
    const tempWorkersData = tempWorkersResult.recordset[0] || {};
    const beneficiariesData = beneficiariesResult.recordset[0] || {};

    // تنسيق البيانات
    const realData = {
      cars: {
        count: parseInt(carsData.totalCars) || 0,
        monthlyTotal: parseFloat(carsData.monthlyTotal) || 0,
        annualTotal: parseFloat(carsData.annualTotal) || 0,
        averageRent: parseFloat(carsData.averageRent) || 0,
        beneficiaries: parseInt(beneficiariesData.carBeneficiaries) || 0
      },
      apartments: {
        count: parseInt(apartmentsData.totalApartments) || 0,
        monthlyTotal: parseFloat(apartmentsData.monthlyTotal) || 0,
        annualTotal: parseFloat(apartmentsData.annualTotal) || 0,
        averageRent: parseFloat(apartmentsData.averageRent) || 0,
        beneficiaries: parseInt(beneficiariesData.apartmentBeneficiaries) || 0
      },
      tempWorkers: {
        count: parseInt(tempWorkersData.totalWorkers) || 0,
        monthlyTotal: parseFloat(tempWorkersData.monthlyTotal) || 0,
        annualTotal: parseFloat(tempWorkersData.annualTotal) || 0,
        averageSalary: parseFloat(tempWorkersData.averageSalary) || 0
      }
    };

    // حساب الإجماليات
    const totals = {
      monthlyTotal: realData.cars.monthlyTotal + realData.apartments.monthlyTotal + realData.tempWorkers.monthlyTotal,
      annualTotal: realData.cars.annualTotal + realData.apartments.annualTotal + realData.tempWorkers.annualTotal,
      totalItems: realData.cars.count + realData.apartments.count + realData.tempWorkers.count,
      totalBeneficiaries: realData.cars.beneficiaries + realData.apartments.beneficiaries + realData.tempWorkers.count
    };

    // بيانات للرسوم البيانية
    const chartData = [
      {
        name: 'تكاليف السيارات',
        value: realData.cars.monthlyTotal,
        count: realData.cars.count,
        color: '#3B82F6'
      },
      {
        name: 'تكاليف الشقق',
        value: realData.apartments.monthlyTotal,
        count: realData.apartments.count,
        color: '#10B981'
      },
      {
        name: 'تكاليف العمالة المؤقتة',
        value: realData.tempWorkers.monthlyTotal,
        count: realData.tempWorkers.count,
        color: '#F59E0B'
      }
    ];

    console.log('✅ البيانات الحقيقية:');
    console.log(`- السيارات: ${realData.cars.count} سيارة، ${realData.cars.monthlyTotal.toLocaleString('ar-EG')} ج.م شهرياً`);
    console.log(`- الشقق: ${realData.apartments.count} شقة، ${realData.apartments.monthlyTotal.toLocaleString('ar-EG')} ج.م شهرياً`);
    console.log(`- العمالة المؤقتة: ${realData.tempWorkers.count} عامل، ${realData.tempWorkers.monthlyTotal.toLocaleString('ar-EG')} ج.م شهرياً`);
    console.log(`- الإجمالي الشهري: ${totals.monthlyTotal.toLocaleString('ar-EG')} ج.م`);

    return NextResponse.json({
      success: true,
      data: realData,
      totals: totals,
      chartData: chartData,
      lastUpdated: new Date().toISOString(),
      message: 'تم جلب البيانات الحقيقية بنجاح'
    });

  } catch (error) {
    console.error('❌ خطأ في جلب البيانات الحقيقية:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب البيانات الحقيقية: ' + error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      await pool.close();
    }
  }
}

// POST - تحديث البيانات
export async function POST(request) {
  let pool;
  
  try {
    const body = await request.json();
    const { action } = body;

    pool = await getConnection();

    switch (action) {
      case 'refreshData':
        // إعادة حساب البيانات
        return await GET(request);
      
      case 'validateData':
        // التحقق من صحة البيانات
        const validationResult = await validateCostsData(pool);
        return NextResponse.json({
          success: true,
          validation: validationResult
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }

  } catch (error) {
    console.error('❌ خطأ في POST real-costs-data:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      await pool.close();
    }
  }
}

// دالة التحقق من صحة البيانات
async function validateCostsData(pool) {
  try {
    const validation = {
      cars: { valid: false, issues: [] },
      apartments: { valid: false, issues: [] },
      tempWorkers: { valid: false, issues: [] }
    };

    // التحقق من بيانات السيارات
    const carsCheck = await pool.request().query(`
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN RentAmount IS NULL OR RentAmount <= 0 THEN 1 END) as invalidRents,
        COUNT(CASE WHEN IsActive IS NULL THEN 1 END) as missingStatus
      FROM Cars
    `);

    const carsData = carsCheck.recordset[0];
    validation.cars.valid = carsData.invalidRents === 0 && carsData.missingStatus === 0;
    if (carsData.invalidRents > 0) validation.cars.issues.push(`${carsData.invalidRents} سيارة بقيم إيجار غير صحيحة`);
    if (carsData.missingStatus > 0) validation.cars.issues.push(`${carsData.missingStatus} سيارة بحالة غير محددة`);

    // التحقق من بيانات الشقق
    const apartmentsCheck = await pool.request().query(`
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN RentAmount IS NULL OR RentAmount <= 0 THEN 1 END) as invalidRents,
        COUNT(CASE WHEN IsActive IS NULL THEN 1 END) as missingStatus
      FROM Apartments
    `);

    const apartmentsData = apartmentsCheck.recordset[0];
    validation.apartments.valid = apartmentsData.invalidRents === 0 && apartmentsData.missingStatus === 0;
    if (apartmentsData.invalidRents > 0) validation.apartments.issues.push(`${apartmentsData.invalidRents} شقة بقيم إيجار غير صحيحة`);
    if (apartmentsData.missingStatus > 0) validation.apartments.issues.push(`${apartmentsData.missingStatus} شقة بحالة غير محددة`);

    // التحقق من بيانات العمالة المؤقتة
    const tempWorkersCheck = await pool.request().query(`
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN MonthlySalary IS NULL OR MonthlySalary <= 0 THEN 1 END) as invalidSalaries,
        COUNT(CASE WHEN IsActive IS NULL THEN 1 END) as missingStatus
      FROM TempWorkers
    `);

    const tempWorkersData = tempWorkersCheck.recordset[0];
    validation.tempWorkers.valid = tempWorkersData.invalidSalaries === 0 && tempWorkersData.missingStatus === 0;
    if (tempWorkersData.invalidSalaries > 0) validation.tempWorkers.issues.push(`${tempWorkersData.invalidSalaries} عامل براتب غير صحيح`);
    if (tempWorkersData.missingStatus > 0) validation.tempWorkers.issues.push(`${tempWorkersData.missingStatus} عامل بحالة غير محددة`);

    return validation;

  } catch (error) {
    console.error('❌ خطأ في التحقق من البيانات:', error);
    return { error: error.message };
  }
}
