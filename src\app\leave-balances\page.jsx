'use client';

import { useState, useEffect } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import MainLayout from '@/components/MainLayout';
import { FiSearch, FiEdit, FiSave, FiX, FiUsers } from 'react-icons/fi';

export default function LeaveBalancesPage() {
  const { isArabic } = useLanguage();
  const [employees, setEmployees] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [editingEmployee, setEditingEmployee] = useState(null);
  const [editValues, setEditValues] = useState({});

  // جلب بيانات الموظفين مع رصيد الإجازات
  const fetchEmployeesWithBalances = async () => {
    try {
      setLoading(true);
      
      const queryParams = new URLSearchParams({
        action: 'list-with-balances',
        searchTerm
      });

      const response = await fetch(`/api/employees?${queryParams}`);
      const result = await response.json();

      if (result.success) {
        setEmployees(result.employees || []);
      } else {

      }
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  // بدء تعديل رصيد موظف
  const startEditing = (employee) => {
    setEditingEmployee(employee.EmployeeCode);
    setEditValues({
      annualBalance: employee.AnnualBalance || 15,
      casualBalance: employee.CasualBalance || 6
    });
  };

  // إلغاء التعديل
  const cancelEditing = () => {
    setEditingEmployee(null);
    setEditValues({});
  };

  // حفظ التعديل
  const saveBalance = async (employeeCode) => {
    try {
      const response = await fetch('/api/leave-balance', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'update',
          employeeCode,
          annualBalance: editValues.annualBalance,
          casualBalance: editValues.casualBalance
        })
      });

      const result = await response.json();
      if (result.success) {
        // تحديث البيانات محلياً
        setEmployees(prev => prev.map(emp => 
          emp.EmployeeCode === employeeCode 
            ? { 
                ...emp, 
                AnnualBalance: editValues.annualBalance,
                CasualBalance: editValues.casualBalance
              }
            : emp
        ));
        
        setEditingEmployee(null);
        setEditValues({});
        alert(isArabic ? 'تم تحديث الرصيد بنجاح' : 'Balance updated successfully');
      } else {
        alert(result.error || (isArabic ? 'خطأ في تحديث الرصيد' : 'Error updating balance'));
      }
    } catch (error) {

      alert(isArabic ? 'خطأ في الاتصال بالخادم' : 'Server connection error');
    }
  };

  // فلترة الموظفين حسب البحث
  const filteredEmployees = employees.filter(emp =>
    !searchTerm ||
    String(emp.EmployeeCode || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
    String(emp.EmployeeName || '').toLowerCase().includes(searchTerm.toLowerCase())
  );

  useEffect(() => {
    fetchEmployeesWithBalances();
  }, []);

  // البحث عند تغيير النص
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      fetchEmployeesWithBalances();
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto">
          {/* العنوان */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-200 mb-2">
                  {isArabic ? 'رصيد الإجازات' : 'Leave Balances'}
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  {isArabic ? 'عرض وتعديل رصيد إجازات الموظفين' : 'View and edit employee leave balances'}
                </p>
              </div>
              <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <FiUsers className="text-2xl text-white" />
              </div>
            </div>
          </div>

          {/* البحث */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
            <div className="flex items-center gap-4">
              <div className="flex-1 relative">
                <FiSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder={isArabic ? 'البحث بالكود أو الاسم...' : 'Search by code or name...'}
                  className="w-full pl-4 pr-10 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-gray-200"
                  autoComplete="off"
                  autoCorrect="off"
                  autoCapitalize="off"
                  spellCheck="false"
                />
              </div>
              <button
                onClick={fetchEmployeesWithBalances}
                disabled={loading}
                className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 flex items-center gap-2"
              >
                <FiSearch />
                {isArabic ? 'بحث' : 'Search'}
              </button>
            </div>
          </div>

          {/* جدول رصيد الإجازات */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
                {isArabic ? 'رصيد الإجازات' : 'Leave Balances'} 
                <span className="text-sm text-gray-500 ml-2">({filteredEmployees.length})</span>
              </h2>
            </div>

            {loading ? (
              <div className="p-8 text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
                <p className="mt-2 text-gray-600 dark:text-gray-400">
                  {isArabic ? 'جاري التحميل...' : 'Loading...'}
                </p>
              </div>
            ) : filteredEmployees.length === 0 ? (
              <div className="p-8 text-center">
                <FiUsers className="mx-auto h-12 w-12 text-gray-400" />
                <p className="mt-2 text-gray-600 dark:text-gray-400">
                  {isArabic ? 'لا توجد بيانات موظفين' : 'No employee data found'}
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'الموظف' : 'Employee'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'الوظيفة' : 'Job Title'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'الرصيد الاعتيادي' : 'Annual Balance'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'الرصيد العارض' : 'Casual Balance'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'الإجراءات' : 'Actions'}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {filteredEmployees.map((employee) => (
                      <tr key={employee.EmployeeCode} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                            {employee.EmployeeName}
                          </div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {employee.EmployeeCode}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {employee.JobTitle || 'غير محدد'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {editingEmployee === employee.EmployeeCode ? (
                            <input
                              type="number"
                              value={editValues.annualBalance}
                              onChange={(e) => setEditValues(prev => ({ ...prev, annualBalance: parseInt(e.target.value) || 0 }))}
                              className="w-20 px-2 py-1 border border-gray-300 rounded text-center"
                              min="0"
                              max="365"
                            />
                          ) : (
                            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {employee.AnnualBalance || 15} يوم
                            </span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {editingEmployee === employee.EmployeeCode ? (
                            <input
                              type="number"
                              value={editValues.casualBalance}
                              onChange={(e) => setEditValues(prev => ({ ...prev, casualBalance: parseInt(e.target.value) || 0 }))}
                              className="w-20 px-2 py-1 border border-gray-300 rounded text-center"
                              min="0"
                              max="30"
                            />
                          ) : (
                            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {employee.CasualBalance || 6} يوم
                            </span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          {editingEmployee === employee.EmployeeCode ? (
                            <div className="flex items-center gap-2">
                              <button
                                onClick={() => saveBalance(employee.EmployeeCode)}
                                className="text-green-600 hover:text-green-900 flex items-center gap-1"
                              >
                                <FiSave />
                                {isArabic ? 'حفظ' : 'Save'}
                              </button>
                              <button
                                onClick={cancelEditing}
                                className="text-red-600 hover:text-red-900 flex items-center gap-1"
                              >
                                <FiX />
                                {isArabic ? 'إلغاء' : 'Cancel'}
                              </button>
                            </div>
                          ) : (
                            <button
                              onClick={() => startEditing(employee)}
                              className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 flex items-center gap-1"
                            >
                              <FiEdit />
                              {isArabic ? 'تعديل' : 'Edit'}
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
