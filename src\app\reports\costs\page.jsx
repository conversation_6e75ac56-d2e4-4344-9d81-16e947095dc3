'use client';

import React, { useState, useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { useLanguage } from '@/contexts/LanguageContext';
import MainLayout from '@/components/MainLayout';
import { 
  DollarSign, 
  Download, 
  Filter, 
  Search, 
  FileText, 
  BarChart3, 
  Eye, 
  Printer,
  RefreshCw,
  Car,
  Building,
  Users,
  TrendingUp,
  Calendar,
  PieChart
} from 'lucide-react';

export default function CostReportsPage() {
  const { isDarkMode } = useTheme();
  const { isRTL, isArabic } = useLanguage();
  
  const [loading, setLoading] = useState(false);
  const [selectedReport, setSelectedReport] = useState('cars');
  const [dateRange, setDateRange] = useState({
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  });
  const [filters, setFilters] = useState({
    category: '',
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear()
  });

  // أنواع تقارير التكاليف
  const costReports = [
    {
      id: 'cars',
      title: 'تقرير تكاليف السيارات',
      description: 'تكاليف إيجار وصيانة السيارات',
      icon: Car,
      color: 'blue'
    },
    {
      id: 'apartments',
      title: 'تقرير تكاليف الشقق',
      description: 'تكاليف إيجار وصيانة الشقق',
      icon: Building,
      color: 'green'
    },
    {
      id: 'temp-workers',
      title: 'تقرير تكاليف العمالة المؤقتة',
      description: 'تكاليف العمالة المؤقتة والمقاولين',
      icon: Users,
      color: 'orange'
    },
    {
      id: 'total',
      title: 'تقرير إجمالي التكاليف',
      description: 'إجمالي جميع التكاليف والمصروفات',
      icon: PieChart,
      color: 'purple'
    }
  ];

  // بيانات وهمية للتقارير
  const [reportData, setReportData] = useState({
    cars: [
      { id: 1, carModel: 'تويوتا كامري 2023', plateNumber: 'أ ب ج 123', monthlyRent: 2500, maintenance: 300, fuel: 800, insurance: 200, total: 3800, month: 'يناير 2025' },
      { id: 2, carModel: 'هونداي النترا 2022', plateNumber: 'د هـ و 456', monthlyRent: 2200, maintenance: 150, fuel: 750, insurance: 180, total: 3280, month: 'يناير 2025' },
      { id: 3, carModel: 'نيسان صني 2023', plateNumber: 'ز ح ط 789', monthlyRent: 2000, maintenance: 200, fuel: 700, insurance: 160, total: 3060, month: 'يناير 2025' }
    ],
    apartments: [
      { id: 1, apartmentLocation: 'شقة الرياض - حي النرجس', monthlyRent: 4500, utilities: 300, maintenance: 200, cleaning: 150, total: 5150, month: 'يناير 2025' },
      { id: 2, apartmentLocation: 'شقة جدة - حي الصفا', monthlyRent: 3800, utilities: 250, maintenance: 100, cleaning: 120, total: 4270, month: 'يناير 2025' },
      { id: 3, apartmentLocation: 'شقة الدمام - حي الفيصلية', monthlyRent: 3200, utilities: 200, maintenance: 150, cleaning: 100, total: 3650, month: 'يناير 2025' }
    ],
    tempWorkers: [
      { id: 1, workerName: 'أحمد محمد', workerType: 'عامل نظافة', dailyRate: 150, workingDays: 26, totalAmount: 3900, month: 'يناير 2025' },
      { id: 2, workerName: 'محمد علي', workerType: 'حارس أمن', dailyRate: 200, workingDays: 30, totalAmount: 6000, month: 'يناير 2025' },
      { id: 3, workerName: 'سعد أحمد', workerType: 'سائق', dailyRate: 180, workingDays: 25, totalAmount: 4500, month: 'يناير 2025' }
    ],
    summary: {
      totalCars: 45800,
      totalApartments: 38200,
      totalTempWorkers: 42500,
      grandTotal: 126500,
      monthlyAverage: 10541,
      yearlyProjection: 1518000
    }
  });

  // تحميل البيانات
  const loadReportData = async () => {
    setLoading(true);
    try {
      // محاكاة تحميل البيانات
      setTimeout(() => {
        setLoading(false);
      }, 1000);
    } catch (error) {

      setLoading(false);
    }
  };

  useEffect(() => {
    loadReportData();
  }, [selectedReport, dateRange, filters]);

  // تصدير التقرير
  const exportReport = (format) => {

  };

  // طباعة التقرير
  const printReport = () => {
    window.print();
  };

  // تنسيق العملة
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  // رندر محتوى التقرير حسب النوع
  const renderReportContent = () => {
    switch (selectedReport) {
      case 'cars':
        return (
          <div className="overflow-x-auto">
            <table className={`min-w-full ${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg`}>
              <thead className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <tr>
                  <th className="px-4 py-3 text-right text-sm font-medium">موديل السيارة</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">رقم اللوحة</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">الإيجار الشهري</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">الصيانة</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">الوقود</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">التأمين</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">الإجمالي</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">الشهر</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {reportData.cars.map((car) => (
                  <tr key={car.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-4 py-3 text-sm font-medium">{car.carModel}</td>
                    <td className="px-4 py-3 text-sm">{car.plateNumber}</td>
                    <td className="px-4 py-3 text-sm">{formatCurrency(car.monthlyRent)}</td>
                    <td className="px-4 py-3 text-sm">{formatCurrency(car.maintenance)}</td>
                    <td className="px-4 py-3 text-sm">{formatCurrency(car.fuel)}</td>
                    <td className="px-4 py-3 text-sm">{formatCurrency(car.insurance)}</td>
                    <td className="px-4 py-3 text-sm font-bold text-blue-600">{formatCurrency(car.total)}</td>
                    <td className="px-4 py-3 text-sm">{car.month}</td>
                  </tr>
                ))}
              </tbody>
              <tfoot className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <tr>
                  <td colSpan="6" className="px-4 py-3 text-sm font-bold text-right">الإجمالي:</td>
                  <td className="px-4 py-3 text-sm font-bold text-blue-600">
                    {formatCurrency(reportData.cars.reduce((sum, car) => sum + car.total, 0))}
                  </td>
                  <td></td>
                </tr>
              </tfoot>
            </table>
          </div>
        );

      case 'apartments':
        return (
          <div className="overflow-x-auto">
            <table className={`min-w-full ${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg`}>
              <thead className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <tr>
                  <th className="px-4 py-3 text-right text-sm font-medium">موقع الشقة</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">الإيجار الشهري</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">المرافق</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">الصيانة</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">التنظيف</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">الإجمالي</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">الشهر</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {reportData.apartments.map((apartment) => (
                  <tr key={apartment.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-4 py-3 text-sm font-medium">{apartment.apartmentLocation}</td>
                    <td className="px-4 py-3 text-sm">{formatCurrency(apartment.monthlyRent)}</td>
                    <td className="px-4 py-3 text-sm">{formatCurrency(apartment.utilities)}</td>
                    <td className="px-4 py-3 text-sm">{formatCurrency(apartment.maintenance)}</td>
                    <td className="px-4 py-3 text-sm">{formatCurrency(apartment.cleaning)}</td>
                    <td className="px-4 py-3 text-sm font-bold text-green-600">{formatCurrency(apartment.total)}</td>
                    <td className="px-4 py-3 text-sm">{apartment.month}</td>
                  </tr>
                ))}
              </tbody>
              <tfoot className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <tr>
                  <td colSpan="5" className="px-4 py-3 text-sm font-bold text-right">الإجمالي:</td>
                  <td className="px-4 py-3 text-sm font-bold text-green-600">
                    {formatCurrency(reportData.apartments.reduce((sum, apt) => sum + apt.total, 0))}
                  </td>
                  <td></td>
                </tr>
              </tfoot>
            </table>
          </div>
        );

      case 'temp-workers':
        return (
          <div className="overflow-x-auto">
            <table className={`min-w-full ${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg`}>
              <thead className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <tr>
                  <th className="px-4 py-3 text-right text-sm font-medium">اسم العامل</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">نوع العمل</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">الأجر اليومي</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">أيام العمل</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">إجمالي المبلغ</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">الشهر</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {reportData.tempWorkers.map((worker) => (
                  <tr key={worker.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-4 py-3 text-sm font-medium">{worker.workerName}</td>
                    <td className="px-4 py-3 text-sm">{worker.workerType}</td>
                    <td className="px-4 py-3 text-sm">{formatCurrency(worker.dailyRate)}</td>
                    <td className="px-4 py-3 text-sm">{worker.workingDays}</td>
                    <td className="px-4 py-3 text-sm font-bold text-orange-600">{formatCurrency(worker.totalAmount)}</td>
                    <td className="px-4 py-3 text-sm">{worker.month}</td>
                  </tr>
                ))}
              </tbody>
              <tfoot className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <tr>
                  <td colSpan="4" className="px-4 py-3 text-sm font-bold text-right">الإجمالي:</td>
                  <td className="px-4 py-3 text-sm font-bold text-orange-600">
                    {formatCurrency(reportData.tempWorkers.reduce((sum, worker) => sum + worker.totalAmount, 0))}
                  </td>
                  <td></td>
                </tr>
              </tfoot>
            </table>
          </div>
        );

      case 'total':
        return (
          <div className="space-y-6">
            {/* إحصائيات سريعة */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className={`${isDarkMode ? 'bg-gray-700' : 'bg-blue-50'} p-6 rounded-lg`}>
                <div className="flex items-center">
                  <Car className="h-8 w-8 text-blue-500" />
                  <div className="mr-4">
                    <p className="text-sm text-gray-600 dark:text-gray-400">تكاليف السيارات</p>
                    <p className="text-2xl font-bold text-blue-600">{formatCurrency(reportData.summary.totalCars)}</p>
                  </div>
                </div>
              </div>
              
              <div className={`${isDarkMode ? 'bg-gray-700' : 'bg-green-50'} p-6 rounded-lg`}>
                <div className="flex items-center">
                  <Building className="h-8 w-8 text-green-500" />
                  <div className="mr-4">
                    <p className="text-sm text-gray-600 dark:text-gray-400">تكاليف الشقق</p>
                    <p className="text-2xl font-bold text-green-600">{formatCurrency(reportData.summary.totalApartments)}</p>
                  </div>
                </div>
              </div>
              
              <div className={`${isDarkMode ? 'bg-gray-700' : 'bg-orange-50'} p-6 rounded-lg`}>
                <div className="flex items-center">
                  <Users className="h-8 w-8 text-orange-500" />
                  <div className="mr-4">
                    <p className="text-sm text-gray-600 dark:text-gray-400">تكاليف العمالة المؤقتة</p>
                    <p className="text-2xl font-bold text-orange-600">{formatCurrency(reportData.summary.totalTempWorkers)}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* الإجمالي العام */}
            <div className={`${isDarkMode ? 'bg-gray-700' : 'bg-purple-50'} p-8 rounded-lg border-2 border-purple-200`}>
              <div className="text-center">
                <h3 className="text-lg font-semibold text-purple-800 dark:text-purple-300 mb-2">
                  إجمالي التكاليف الشهرية
                </h3>
                <p className="text-4xl font-bold text-purple-600 mb-4">
                  {formatCurrency(reportData.summary.grandTotal)}
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">المتوسط الشهري: </span>
                    <span className="font-semibold">{formatCurrency(reportData.summary.monthlyAverage)}</span>
                  </div>
                  <div>
                    <span className="text-gray-600 dark:text-gray-400">التوقع السنوي: </span>
                    <span className="font-semibold">{formatCurrency(reportData.summary.yearlyProjection)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return <div>التقرير غير متاح</div>;
    }
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto p-6">
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-sm p-6 mb-6`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <DollarSign className="h-8 w-8 text-red-600" />
              <div>
                <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  تقارير التكاليف
                </h1>
                <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  تقارير شاملة عن جميع التكاليف والمصروفات
                </p>
              </div>
            </div>
            
            <div className="flex gap-2">
              <button
                onClick={() => exportReport('excel')}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                تصدير Excel
              </button>
              <button
                onClick={() => exportReport('pdf')}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                تصدير PDF
              </button>
              <button
                onClick={printReport}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
              >
                <Printer className="h-4 w-4" />
                طباعة
              </button>
            </div>
          </div>
        </div>

        {/* أنواع التقارير */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          {costReports.map((report) => (
            <button
              key={report.id}
              onClick={() => setSelectedReport(report.id)}
              className={`p-4 rounded-lg border-2 transition-all ${
                selectedReport === report.id
                  ? `border-${report.color}-500 bg-${report.color}-50 dark:bg-${report.color}-900/20`
                  : `border-gray-200 dark:border-gray-700 ${isDarkMode ? 'bg-gray-800' : 'bg-white'} hover:border-${report.color}-300`
              }`}
            >
              <report.icon className={`h-8 w-8 text-${report.color}-500 mx-auto mb-2`} />
              <h3 className={`font-medium text-sm ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {report.title}
              </h3>
              <p className={`text-xs mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {report.description}
              </p>
            </button>
          ))}
        </div>

        {/* الفلاتر */}
        <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-sm p-6 mb-6`}>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">الشهر</label>
              <select
                value={filters.month}
                onChange={(e) => setFilters({...filters, month: e.target.value})}
                className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-300'}`}
              >
                <option value="1">يناير</option>
                <option value="2">فبراير</option>
                <option value="3">مارس</option>
                <option value="4">أبريل</option>
                <option value="5">مايو</option>
                <option value="6">يونيو</option>
                <option value="7">يوليو</option>
                <option value="8">أغسطس</option>
                <option value="9">سبتمبر</option>
                <option value="10">أكتوبر</option>
                <option value="11">نوفمبر</option>
                <option value="12">ديسمبر</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">السنة</label>
              <select
                value={filters.year}
                onChange={(e) => setFilters({...filters, year: e.target.value})}
                className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-300'}`}
              >
                <option value="2023">2023</option>
                <option value="2024">2024</option>
                <option value="2025">2025</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">فئة التكلفة</label>
              <select
                value={filters.category}
                onChange={(e) => setFilters({...filters, category: e.target.value})}
                className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-white border-gray-300'}`}
              >
                <option value="">جميع الفئات</option>
                <option value="rent">إيجار</option>
                <option value="maintenance">صيانة</option>
                <option value="fuel">وقود</option>
                <option value="insurance">تأمين</option>
              </select>
            </div>
            <div className="flex items-end">
              <button
                onClick={loadReportData}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg flex items-center justify-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                تحديث التقرير
              </button>
            </div>
          </div>
        </div>

        {/* محتوى التقرير */}
        <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-sm p-6`}>
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
              <span className="mr-3">جاري تحميل التقرير...</span>
            </div>
          ) : (
            renderReportContent()
          )}
        </div>
      </div>
    </MainLayout>
  );
}
