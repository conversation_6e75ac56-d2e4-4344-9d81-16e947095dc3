"use client";
import React from "react";

function MainComponent() {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState(null);
  const [error, setError] = useState(null);

  const handleCleanup = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch("/api/cleanup-duplicates", {
        method: "POST",
      });

      if (!response.ok) {
        throw new Error(`فشلت عملية التنظيف: ${response.status}`);
      }

      const data = await response.json();
      setResults(data);
    } catch (err) {
      setError(err.message);

    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      dir="rtl"
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <a
            href="/"
            className="flex items-center text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
          >
            <i className="fas fa-arrow-right ml-2"></i>
            الرئيسية
          </a>
        </div>

        <div className="bg-gray-50 dark:bg-gray-800 rounded-xl p-6 shadow-sm">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
            تنظيف النظام
          </h1>

          <div className="mb-8">
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              سيقوم النظام بتنظيف العناصر المكررة مثل صفحات تسجيل الدخول ولوحات
              التحكم والوظائف المرتبطة بها.
            </p>
            <button
              onClick={handleCleanup}
              disabled={loading}
              className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:bg-blue-300"
            >
              {loading ? "جاري التنظيف..." : "بدء عملية التنظيف"}
            </button>
          </div>

          {error && (
            <div className="p-4 bg-red-100 text-red-700 rounded-md mb-6">
              {error}
            </div>
          )}

          {results && (
            <div className="space-y-6">
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                نتائج عملية التنظيف
              </h2>

              {results.success ? (
                <div className="space-y-4">
                  <div className="bg-white dark:bg-gray-700 p-4 rounded-lg">
                    <h3 className="font-bold text-gray-900 dark:text-white mb-2">
                      الصفحات المحذوفة
                    </h3>
                    <div className="space-y-2">
                      <p className="text-gray-600 dark:text-gray-300">
                        إجمالي الصفحات المحذوفة: {results.details.pages.total}
                      </p>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <h4 className="font-semibold">
                            تصنيف الصفحات المحذوفة:
                          </h4>
                          <ul className="list-disc list-inside text-gray-600 dark:text-gray-300">
                            <li>
                              صفحات المصادقة:{" "}
                              {results.details.pages.byCategory.auth}
                            </li>
                            <li>
                              لوحات التحكم:{" "}
                              {results.details.pages.byCategory.dashboard}
                            </li>
                            <li>
                              إدارة الموظفين:{" "}
                              {results.details.pages.byCategory.employee}
                            </li>
                            <li>
                              صفحات أخرى:{" "}
                              {results.details.pages.byCategory.other}
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white dark:bg-gray-700 p-4 rounded-lg">
                    <h3 className="font-bold text-gray-900 dark:text-white mb-2">
                      الوظائف المحذوفة
                    </h3>
                    <div className="space-y-2">
                      <p className="text-gray-600 dark:text-gray-300">
                        إجمالي الوظائف المحذوفة:{" "}
                        {results.details.functions.total}
                      </p>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <h4 className="font-semibold">
                            تصنيف الوظائف المحذوفة:
                          </h4>
                          <ul className="list-disc list-inside text-gray-600 dark:text-gray-300">
                            <li>
                              وظائف المصادقة:{" "}
                              {results.details.functions.byCategory.auth}
                            </li>
                            <li>
                              وظائف لوحة التحكم:{" "}
                              {results.details.functions.byCategory.dashboard}
                            </li>
                            <li>
                              وظائف إدارة الموظفين:{" "}
                              {results.details.functions.byCategory.employee}
                            </li>
                            <li>
                              وظائف أخرى:{" "}
                              {results.details.functions.byCategory.other}
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-green-100 dark:bg-green-900 p-4 rounded-lg">
                    <p className="text-green-700 dark:text-green-300">
                      {results.message}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="bg-yellow-100 dark:bg-yellow-900 p-4 rounded-lg">
                  <p className="text-yellow-700 dark:text-yellow-300">
                    لم يتم العثور على عناصر مكررة للحذف
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default MainComponent;