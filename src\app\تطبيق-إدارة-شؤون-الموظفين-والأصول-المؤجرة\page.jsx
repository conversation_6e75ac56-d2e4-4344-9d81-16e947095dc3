'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useTheme } from '@/contexts/ThemeContext';

function MainComponent() {
  const { isDarkMode, toggleTheme } = useTheme();

  const companyInfo = {
    name: {
      ar: 'كونكورد للتطوير المستدام',
      en: 'Concord Building Sustainable Development',
    },
    logo: 'https://ucarecdn.com/124e469f-6ca3-4e59-80f5-cf63c604c07f/-/format/auto/',
  };
  const theme = {
    primary: '#003366',
    secondary: '#FFA500',
    background: '#FFFFFF',
    text: '#333333',
  };
  const [selectedLang, setSelectedLang] = useState('ar');
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [currentView, setCurrentView] = useState('dashboard');
  const [isLoading, setIsLoading] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  // استخدام ThemeProvider بدلاً من state منفصل
  // const [darkMode, setDarkMode] = useState(false);
  const [stats, setStats] = useState({
    employees: { total: 150, new: 5, departed: 2, percentChange: 3.5 },
    apartments: { total: 45, occupied: 38, available: 7, percentChange: 2.1 },
    cars: { total: 25, inUse: 22, available: 3, percentChange: 5.2 },
    tempWorkers: { total: 75, active: 68, inactive: 7, percentChange: 1.8 },
  });
  const dir = selectedLang === 'ar' ? 'rtl' : 'ltr';
  const menuItems = [
    {
      id: 'employees',
      label: { ar: 'الموظفين', en: 'Employees' },
      icon: 'fa-users',
      subItems: [
        {
          id: 'add-employee',
          label: { ar: 'إضافة موظف', en: 'Add Employee' },
          route: '/add-employee',
        },
        {
          id: 'employee-data',
          label: { ar: 'بيانات الموظفين', en: 'Employee Data' },
          route: '/employee-data',
        },
        {
          id: 'employee-archive',
          label: { ar: 'أرشيف الموظفين', en: 'Employee Archive' },
          route: '/employee-archive',
        },
        {
          id: 'employee-search',
          label: { ar: 'استعلام عن موظف', en: 'Employee Search' },
          route: '/employee-search',
        },
        {
          id: 'employee-reports',
          label: { ar: 'تقارير', en: 'Reports' },
          route: '/employee-reports',
        },
      ],
    },
    {
      id: 'attendance',
      label: { ar: 'التمام اليومى والمؤثرات الشهرية', en: 'Daily Attendance & Monthly Effects' },
      icon: 'fa-clock',
      subItems: [
        {
          id: 'daily-attendance',
          label: { ar: 'التمام اليومى', en: 'Daily Attendance' },
          route: '/attendance/daily',
        },
        {
          id: 'upload-attendance',
          label: { ar: 'رفع التمام اليومي', en: 'Upload Daily Attendance' },
          route: '/attendance/upload',
        },
        {
          id: 'monthly-effects',
          label: { ar: 'كشف المؤثرات الشهرية', en: 'Monthly Effects Report' },
          route: '/attendance/monthly',
        },
        {
          id: 'monthly-payroll',
          label: { ar: 'كشف الرواتب الشهرية', en: 'Monthly Payroll Report' },
          route: '/payroll/monthly',
        },
        {
          id: 'attendance-report',
          label: { ar: 'تقرير الحضور', en: 'Attendance Report' },
          route: '/attendance/report',
        },
      ],
    },
    {
      id: 'leaves',
      label: { ar: 'الإجازات', en: 'Leaves' },
      icon: 'fa-calendar-alt',
      subItems: [
        {
          id: 'new-leave',
          label: { ar: 'تسجيل إجازة جديدة', en: 'New Leave Request' },
          route: '/leave-request',
        },
        {
          id: 'pending-leaves',
          label: { ar: 'إجازات قيد المراجعة', en: 'Pending Leaves' },
          route: '/pending-leaves',
        },
        {
          id: 'approved-leaves',
          label: { ar: 'إجازات معتمدة', en: 'Approved Leaves' },
          route: '/approved-leaves',
        },
        {
          id: 'leave-balance',
          label: { ar: 'رصيد الإجازات', en: 'Leave Balance' },
          route: '/leave-balance',
        },
      ],
    },
    {
      id: 'assets',
      label: { ar: 'الأصول المؤجرة', en: 'Rented Assets' },
      icon: 'fa-building',
      subItems: [
        {
          id: 'apartments',
          label: { ar: 'الشقق المؤجرة', en: 'Rented Apartments' },
          route: '/apartments',
        },
        {
          id: 'cars',
          label: { ar: 'السيارات المؤجرة', en: 'Rented Cars' },
          route: '/cars',
        },
        {
          id: 'temp-workers',
          label: { ar: 'العمالة المؤقتة', en: 'Temporary Workers' },
          route: '/temp-workers',
        },
      ],
    },
  ];
  const ActionButtons = ({ onExport, onPrint }) => {
    return (
      <div className="flex gap-2 mb-4">
        <button
          onClick={onExport}
          className="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600 flex items-center"
        >
          <i className="fas fa-file-excel mr-2"></i>
          {selectedLang === 'ar' ? 'تصدير إلى Excel' : 'Export to Excel'}
        </button>

        <button
          onClick={onPrint}
          className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 flex items-center"
        >
          <i className="fas fa-print mr-2"></i>
          {selectedLang === 'ar' ? 'طباعة' : 'Print'}
        </button>
      </div>
    );
  };

  useEffect(() => {
    const savedLang = localStorage.getItem('preferredLanguage');
    if (savedLang) {
      setSelectedLang(savedLang);
    }
    // تم حذف كود darkMode لأنه يتم إدارته بواسطة ThemeProvider
  }, []);

  const [showReconnectButton, setShowReconnectButton] = useState(false);

  useEffect(() => {
    function updateOnlineStatus() {
      const online = navigator.onLine;
      setIsOnline(online);
      if (!online) {
        setShowReconnectButton(true);
      }
    }
    const intervalId = setInterval(() => {
      fetch('/api/ping')
        .then(() => {
          setIsOnline(true);
          setShowReconnectButton(false);
        })
        .catch(() => {
          setIsOnline(false);
          setShowReconnectButton(true);
        });
    }, 30000);

    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);

    return () => {
      clearInterval(intervalId);
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
    };
  }, []);

  const handleLanguageChange = useCallback(() => {
    const newLang = selectedLang === 'ar' ? 'en' : 'ar';
    setSelectedLang(newLang);
    localStorage.setItem('preferredLanguage', newLang);
  }, [selectedLang]);

  const refreshData = useCallback(async () => {
    if (!isOnline) {
      toast.error(
        selectedLang === 'ar'
          ? 'لا يمكن تحديث البيانات - لا يوجد اتصال بالإنترنت'
          : 'Cannot refresh data - No internet connection'
      );
      return;
    }

    try {
      setIsLoading(true);
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setStats((prevStats) => ({
        ...prevStats,
        employees: {
          ...prevStats.employees,
          total: prevStats.employees.total + 1,
        },
      }));
    } catch (error) {
      toast.error(
        selectedLang === 'ar'
          ? 'حدث خطأ أثناء تحديث البيانات'
          : 'Error refreshing data'
      );
    } finally {
      setIsLoading(false);
    }
  }, [selectedLang, isOnline]);

  const [openSection, setOpenSection] = useState(null);
  const toggleSection = useCallback(
    (section) => {
      setOpenSection(openSection === section ? null : section);
    },
    [openSection]
  );

  const handleMenuItemClick = useCallback((view) => {
    setCurrentView(view);
    if (window.innerWidth < 1024) {
      setIsSidebarOpen(false);
    }
  }, []);

  const handleViewChange = useCallback((view) => {
    setCurrentView(view);
    if (window.innerWidth < 1024) {
      setIsSidebarOpen(false);
    }
  }, []);

  const handleRetry = () => {
    window.location.reload();
  };

  return (
    <div
      className={`min-h-screen ${
        dir === 'rtl' ? 'font-cairo' : 'font-roboto'
      } ${isDarkMode ? 'dark bg-gray-900' : 'bg-gray-100'}`}
      dir={dir}
    >
      {!isOnline && (
        <div className="fixed top-0 left-0 right-0 bg-red-500 text-white p-2 text-center z-50">
          <i className="fas fa-exclamation-triangle ml-2"></i>
          {selectedLang === 'ar'
            ? 'تم فقدان الاتصال بالإنترنت'
            : 'Internet connection lost'}
        </div>
      )}

      {showReconnectButton && isOnline && (
        <div className="fixed bottom-4 right-4">
          <button
            onClick={handleRetry}
            className="bg-green-500 text-white py-2 px-4 rounded-full shadow-lg hover:bg-green-600 transition-colors flex items-center"
          >
            <i className="fas fa-sync-alt ml-2"></i>
            {selectedLang === 'ar'
              ? 'تم استعادة الاتصال - انقر للتحديث'
              : 'Connection Restored - Click to Refresh'}
          </button>
        </div>
      )}

      <div className="flex h-screen overflow-hidden bg-gray-100 dark:bg-gray-900">
        <div
          className={`fixed inset-y-0 ${
            dir === 'rtl' ? 'right-0' : 'left-0'
          } z-50 flex flex-col transition-transform duration-300 ${
            isSidebarOpen
              ? 'translate-x-0'
              : dir === 'rtl'
                ? 'translate-x-full'
                : '-translate-x-full'
          } w-64 bg-[#003366] dark:bg-gray-800 border-${
            dir === 'rtl' ? 'l' : 'r'
          } border-gray-200 dark:border-gray-700`}
        >
          <div className="flex items-center justify-center h-16 px-4 bg-[#003366] dark:bg-gray-900">
            <img
              src={companyInfo.logo}
              alt="Concord Logo"
              className="h-12 w-auto"
            />
            <h1
              className={`${
                dir === 'rtl' ? 'mr-3' : 'ml-3'
              } text-lg font-bold text-white whitespace-nowrap`}
            >
              {companyInfo.name[selectedLang]}
            </h1>
          </div>
          {menuItems.map((item) => (
            <div key={item.id}>
              <button
                onClick={() => toggleSection(item.id)}
                className={`w-full flex items-center p-2 ${
                  currentView === item.id
                    ? 'bg-[#FFA500] dark:bg-gray-700 text-white'
                    : 'text-white dark:text-gray-300'
                }`}
              >
                <i
                  className={`fas ${item.icon} ${
                    dir === 'rtl' ? 'ml-3' : 'mr-3'
                  }`}
                ></i>
                <span>{item.label[selectedLang]}</span>
                {item.subItems && (
                  <i
                    className={`fas fa-chevron-${
                      openSection === item.id
                        ? 'down'
                        : dir === 'rtl'
                          ? 'left'
                          : 'right'
                    } ml-auto`}
                  ></i>
                )}
              </button>
              {item.subItems && openSection === item.id && (
                <div className={`${dir === 'rtl' ? 'mr-6' : 'ml-6'} py-2`}>
                  {item.subItems.map((subItem) =>
                    subItem.subItems ? (
                      <div key={subItem.id}>
                        <button
                          onClick={() => toggleSection(subItem.id)}
                          className="w-full flex items-center p-2 text-sm text-white"
                        >
                          <span>{subItem.label[selectedLang]}</span>
                          <i
                            className={`fas fa-chevron-${
                              openSection === subItem.id
                                ? 'down'
                                : dir === 'rtl'
                                  ? 'left'
                                  : 'right'
                            } ml-auto`}
                          />
                        </button>
                        {openSection === subItem.id && (
                          <div
                            className={`${
                              dir === 'rtl' ? 'mr-4' : 'ml-4'
                            } py-1`}
                          >
                            {subItem.subItems.map((nestedItem) => (
                              <a
                                key={nestedItem.id}
                                href={nestedItem.route}
                                className={`block p-2 text-sm ${
                                  currentView === nestedItem.id
                                    ? 'text-[#FFA500]'
                                    : 'text-white hover:text-[#FFA500]'
                                }`}
                              >
                                {nestedItem.label[selectedLang]}
                              </a>
                            ))}
                          </div>
                        )}
                      </div>
                    ) : (
                      <a
                        key={subItem.id}
                        href={subItem.route}
                        className={`block p-2 text-sm ${
                          currentView === subItem.id
                            ? 'text-[#FFA500]'
                            : 'text-white hover:text-[#FFA500]'
                        }`}
                      >
                        {subItem.label[selectedLang]}
                      </a>
                    )
                  )}
                </div>
              )}
            </div>
          ))}
          <div className="mt-auto p-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <span className="text-sm text-white dark:text-gray-400">
                {selectedLang === 'ar' ? 'الوضع الليلي' : 'Dark Mode'}
              </span>
              <button
                onClick={toggleTheme}
                className={`relative inline-flex items-center h-6 rounded-full w-11 transition-colors ${
                  isDarkMode ? 'bg-[#FFA500]' : 'bg-gray-300'
                }`}
              >
                <span
                  className={`inline-block w-4 h-4 transform transition-transform bg-white rounded-full ${
                    isDarkMode
                      ? dir === 'rtl'
                        ? '-translate-x-6'
                        : 'translate-x-6'
                      : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
            <button
              onClick={handleLanguageChange}
              className="w-full px-4 py-2 text-sm font-medium text-white dark:text-gray-200 bg-[#FFA500] dark:bg-gray-700 rounded-md"
            >
              {selectedLang === 'ar' ? 'English' : 'عربي'}
            </button>
          </div>
        </div>
        <div className="flex-1 overflow-hidden">
          <header className="bg-[#003366] shadow-sm">
            <div className="flex justify-between items-center px-4 py-3">
              <div className="flex items-center gap-4">
                <button onClick={() => setIsSidebarOpen(!isSidebarOpen)}>
                  <i className="fas fa-bars text-white text-xl" />
                </button>
                <h1 className="text-xl font-bold text-white">
                  {selectedLang === 'ar'
                    ? 'نظام إدارة شؤون الموظفين والأصول المؤجرة'
                    : 'Employee and Asset Management System'}
                </h1>
              </div>
              <button
                onClick={handleLanguageChange}
                className="px-4 py-2 text-sm text-white"
              >
                {selectedLang === 'ar' ? 'English' : 'عربي'}
              </button>
            </div>
          </header>

          <main className="p-6 overflow-y-auto h-[calc(100vh-64px)]">
            {currentView === 'dashboard' && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                  <h2 className="text-xl font-bold mb-4 text-[#003366] dark:text-white">
                    {selectedLang === 'ar'
                      ? 'إدارة الموظفين'
                      : 'Employee Management'}
                  </h2>
                  <div className="space-y-3">
                    {menuItems[0].subItems.map((item) => (
                      <a
                        key={item.id}
                        href={item.route}
                        className="block w-full text-center py-2 px-4 bg-gray-50 dark:bg-gray-700 hover:bg-[#FFA500] hover:text-white rounded transition-colors"
                      >
                        {item.label[selectedLang]}
                      </a>
                    ))}
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                  <h2 className="text-xl font-bold mb-4 text-[#003366] dark:text-white">
                    {selectedLang === 'ar'
                      ? 'الحضور والإجازات'
                      : 'Attendance & Leaves'}
                  </h2>
                  <div className="space-y-3">
                    {[...menuItems[1].subItems, ...menuItems[2].subItems]
                      .slice(0, 3)
                      .map((item) => (
                        <a
                          key={item.id}
                          href={item.route}
                          className="block w-full text-center py-2 px-4 bg-gray-50 dark:bg-gray-700 hover:bg-[#FFA500] hover:text-white rounded transition-colors"
                        >
                          {item.label[selectedLang]}
                        </a>
                      ))}
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                  <h2 className="text-xl font-bold mb-4 text-[#003366] dark:text-white">
                    {selectedLang === 'ar'
                      ? 'إدارة الأصول'
                      : 'Asset Management'}
                  </h2>
                  <div className="space-y-3">
                    {menuItems[3].subItems.map((item) => (
                      <a
                        key={item.id}
                        href={item.route}
                        className="block w-full text-center py-2 px-4 bg-gray-50 dark:bg-gray-700 hover:bg-[#FFA500] hover:text-white rounded transition-colors"
                      >
                        {item.label[selectedLang]}
                      </a>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </main>
        </div>
      </div>
    </div>
  );
}

export default MainComponent;
