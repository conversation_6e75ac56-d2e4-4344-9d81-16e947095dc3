/* تنسيق خانات إدخال التاريخ */

.date-input-arabic {
  direction: ltr;
  text-align: center;
  font-size: 1.1rem;
  font-weight: 500;
}

.date-input-arabic::-webkit-calendar-picker-indicator {
  opacity: 0;
  position: absolute;
  right: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  z-index: 10;
}

.date-input-arabic:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* تحسين عرض التاريخ في المتصفحات المختلفة */
.date-input-arabic::-webkit-datetime-edit {
  padding: 0;
  color: inherit;
}

.date-input-arabic::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

.date-input-arabic::-webkit-datetime-edit-text {
  color: inherit;
  padding: 0 2px;
}

.date-input-arabic::-webkit-datetime-edit-month-field,
.date-input-arabic::-webkit-datetime-edit-day-field,
.date-input-arabic::-webkit-datetime-edit-year-field {
  color: inherit;
  background: transparent;
  border: none;
  padding: 2px;
  border-radius: 4px;
}

.date-input-arabic::-webkit-datetime-edit-month-field:focus,
.date-input-arabic::-webkit-datetime-edit-day-field:focus,
.date-input-arabic::-webkit-datetime-edit-year-field:focus {
  background: rgba(59, 130, 246, 0.1);
  outline: none;
}

/* تنسيق للوضع المظلم */
.dark .date-input-arabic::-webkit-datetime-edit-month-field:focus,
.dark .date-input-arabic::-webkit-datetime-edit-day-field:focus,
.dark .date-input-arabic::-webkit-datetime-edit-year-field:focus {
  background: rgba(59, 130, 246, 0.2);
}

/* إخفاء الأيقونة الافتراضية في Firefox */
.date-input-arabic::-moz-calendar-picker-indicator {
  opacity: 0;
  position: absolute;
  right: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

/* تحسين التباعد والمحاذاة */
.date-input-container {
  position: relative;
  display: inline-block;
  width: 100%;
}

.date-input-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
}

.dark .date-input-label {
  color: #f9fafb;
}

/* تحسين عرض placeholder */
.date-input-arabic:invalid {
  color: #9ca3af;
}

.dark .date-input-arabic:invalid {
  color: #6b7280;
}

.date-input-arabic:valid {
  color: inherit;
}

/* تحسين الحدود والتركيز */
.date-input-arabic {
  border: 2px solid #e5e7eb;
  transition: all 0.2s ease-in-out;
}

.date-input-arabic:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.dark .date-input-arabic {
  border-color: #4b5563;
  background-color: #374151;
  color: #f9fafb;
}

.dark .date-input-arabic:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}
