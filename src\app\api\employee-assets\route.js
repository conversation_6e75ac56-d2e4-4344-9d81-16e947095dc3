import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const employeeCode = searchParams.get('employeeCode');
    
    if (!employeeCode) {
      return NextResponse.json({
        success: false,
        error: 'كود الموظف مطلوب'
      }, { status: 400 });
    }

    const pool = await getConnection();
    const assets = {
      cars: [],
      apartments: [],
      insurance: {}
    };

    // 1. جلب السيارات المرتبطة بالموظف

    try {
      const carsResult = await pool.request()
        .input('employeeCode', sql.NVarChar, employeeCode)
        .query(`
          SELECT 
            c.CarCode,
            c.ContractorName,
            c.CarNumber,
            c.CarType,
            c.Car<PERSON>odel,
            c.<PERSON>ufact<PERSON>Year,
            c.Route,
            c.RentAmount,
            c.Notes as CarNotes,
            cb.StartDate as BeneficiaryStartDate,
            cb.EndDate as BeneficiaryEndDate,
            cb.IsActive as IsBeneficiaryActive,
            cb.CreatedAt as BeneficiaryCreatedAt
          FROM CarBeneficiaries cb
          INNER JOIN Cars c ON cb.Carcode = c.CarCode
          WHERE cb.EmployeeCode = @employeeCode
            AND cb.IsActive = 1
            AND c.IsActive = 1
          ORDER BY cb.StartDate DESC
        `);

      assets.cars = carsResult.recordset.map(car => ({
        carCode: car.CarCode,
        contractorName: car.ContractorName,
        carNumber: car.CarNumber,
        carType: car.CarType,
        carModel: car.CarModel,
        manufactureYear: car.ManufactureYear,
        route: car.Route,
        rentAmount: car.RentAmount,
        notes: car.CarNotes,
        startDate: car.BeneficiaryStartDate,
        endDate: car.BeneficiaryEndDate,
        isActive: car.IsBeneficiaryActive,
        createdAt: car.BeneficiaryCreatedAt
      }));

    } catch (error) {

      assets.cars = [];
    }

    // 2. جلب الشقق المرتبطة بالموظف

    try {
      const apartmentsResult = await pool.request()
        .input('employeeCode', sql.NVarChar, employeeCode)
        .query(`
          SELECT 
            a.ApartmentCode,
            a.LandlordName,
            a.Address,
            a.StartDate as ApartmentStartDate,
            a.EndDate as ApartmentEndDate,
            a.RentAmount,
            a.InsuranceAmount,
            a.CommissionAmount,
            a.BacklogAmount,
            a.Notes as ApartmentNotes,
            ab.StartDate as BeneficiaryStartDate,
            ab.EndDate as BeneficiaryEndDate,
            ab.IsActive as IsBeneficiaryActive,
            ab.CreatedAt as BeneficiaryCreatedAt
          FROM ApartmentBeneficiaries ab
          INNER JOIN Apartments a ON ab.ApartmentCode = a.ApartmentCode
          WHERE ab.EmployeeCode = @employeeCode
            AND ab.IsActive = 1
            AND a.IsActive = 1
          ORDER BY ab.StartDate DESC
        `);

      assets.apartments = apartmentsResult.recordset.map(apt => ({
        apartmentCode: apt.ApartmentCode,
        landlordName: apt.LandlordName,
        address: apt.Address,
        apartmentStartDate: apt.ApartmentStartDate,
        apartmentEndDate: apt.ApartmentEndDate,
        rentAmount: apt.RentAmount,
        insuranceAmount: apt.InsuranceAmount,
        commissionAmount: apt.CommissionAmount,
        backlogAmount: apt.BacklogAmount,
        notes: apt.ApartmentNotes,
        startDate: apt.BeneficiaryStartDate,
        endDate: apt.BeneficiaryEndDate,
        isActive: apt.IsBeneficiaryActive,
        createdAt: apt.BeneficiaryCreatedAt
      }));

    } catch (error) {

      assets.apartments = [];
    }

    // 3. جلب بيانات التأمين الصحيحة من جدول الموظفين

    try {
      const insuranceResult = await pool.request()
        .input('employeeCode', sql.NVarChar, employeeCode)
        .query(`
          SELECT 
            SocialInsurance,
            SocialInsureNum as SocialInsuranceNumber,
            spcialInsDate as SocialInsuranceDate,
            MedicalInsurance,
            MedicalInsuranceNum as MedicalInsuranceNumber
          FROM Employees
          WHERE EmployeeCode = @employeeCode
             OR CAST(EmployeeCode AS NVARCHAR) = @employeeCode
        `);

      if (insuranceResult.recordset.length > 0) {
        const insurance = insuranceResult.recordset[0];
        assets.insurance = {
          socialInsurance: insurance.SocialInsurance,
          socialInsuranceNumber: insurance.SocialInsuranceNumber,
          socialInsuranceDate: insurance.SocialInsuranceDate,
          medicalInsurance: insurance.MedicalInsurance,
          medicalInsuranceNumber: insurance.MedicalInsuranceNumber
        };
      }

    } catch (error) {

      assets.insurance = {};
    }

    // 4. إحصائيات الأصول
    const summary = {
      totalCars: assets.cars.length,
      totalApartments: assets.apartments.length,
      activeCars: assets.cars.filter(car => car.isActive).length,
      activeApartments: assets.apartments.filter(apt => apt.isActive).length,
      hasInsurance: !!(assets.insurance.socialInsurance || assets.insurance.medicalInsurance)
    };

    return NextResponse.json({
      success: true,
      data: assets,
      summary: summary,
      message: `تم جلب أصول الموظف ${employeeCode} بنجاح`
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب أصول الموظف',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}

export async function POST(request) {
  try {
    const body = await request.json();
    const { action, employeeCode } = body;

    if (!employeeCode) {
      return NextResponse.json({
        success: false,
        error: 'كود الموظف مطلوب'
      }, { status: 400 });
    }

    const pool = await getConnection();

    if (action === 'sync-employee-data') {

      // تحديث بيانات السكن في ملف الموظف
      const apartmentUpdate = await pool.request()
        .input('employeeCode', sql.NVarChar, employeeCode)
        .query(`
          UPDATE e
          SET 
            CompanyHousing = CASE 
              WHEN EXISTS (
                SELECT 1 FROM ApartmentBeneficiaries ab
                INNER JOIN Apartments a ON ab.ApartmentCode = a.ApartmentCode
                WHERE ab.EmployeeCode = e.EmployeeCode AND ab.IsActive = 1
              ) THEN 'نعم'
              ELSE 'لا'
            END,
            codeHousing = (
              SELECT TOP 1 a.ApartmentCode
              FROM ApartmentBeneficiaries ab
              INNER JOIN Apartments a ON ab.ApartmentCode = a.ApartmentCode
              WHERE ab.EmployeeCode = e.EmployeeCode AND ab.IsActive = 1
              ORDER BY ab.StartDate DESC
            )
          FROM Employees e
          WHERE e.EmployeeCode = @employeeCode
             OR CAST(e.EmployeeCode AS NVARCHAR) = @employeeCode
        `);

      // تحديث بيانات المواصلات في ملف الموظف
      const transportUpdate = await pool.request()
        .input('employeeCode', sql.NVarChar, employeeCode)
        .query(`
          UPDATE e
          SET 
            TransportMethod = (
              SELECT TOP 1 CONCAT(c.CarCode, ' - ', c.Route)
              FROM CarBeneficiaries cb
              INNER JOIN Cars c ON cb.Carcode = c.CarCode
              WHERE cb.EmployeeCode = e.EmployeeCode AND cb.IsActive = 1
              ORDER BY cb.StartDate DESC
            )
          FROM Employees e
          WHERE e.EmployeeCode = @employeeCode
             OR CAST(e.EmployeeCode AS NVARCHAR) = @employeeCode
        `);

      return NextResponse.json({
        success: true,
        message: 'تم تحديث بيانات الموظف بنجاح',
        details: {
          apartmentRowsAffected: apartmentUpdate.rowsAffected[0],
          transportRowsAffected: transportUpdate.rowsAffected[0]
        }
      });
    }

    return NextResponse.json({
      success: false,
      error: 'إجراء غير مدعوم'
    }, { status: 400 });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في معالجة الطلب',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}
