'use client';

import { useState, useEffect } from 'react';
import {
  FiBell,
  FiX,
  FiCheck,
  FiClock,
  FiUser,
  FiActivity,
  FiAlertCircle,
  FiCheckCircle,
  FiInfo
} from 'react-icons/fi';

export default function NotificationCenter({ userCode, className = '' }) {
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadNotifications();

    // تحديث الإشعارات كل 30 ثانية
    const interval = setInterval(loadNotifications, 30000);

    // استماع لأحداث تحديث الإشعارات
    const handleNotificationUpdate = (event) => {
      loadNotifications();
    };

    window.addEventListener('notificationUpdate', handleNotificationUpdate);

    return () => {
      clearInterval(interval);
      window.removeEventListener('notificationUpdate', handleNotificationUpdate);
    };
  }, []);

  // جلب الإشعارات من النظام الجديد
  const loadNotifications = async () => {
    try {
      setLoading(true);

      const response = await fetch('/api/smart-notifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'list',
          limit: 20,
          status: 'all'
        })
      });

      const result = await response.json();

      if (result.success) {
        const notifications = result.notifications || [];
        const pendingCount = notifications.filter(n => n.Status === 'pending').length;

        setNotifications(notifications);
        setUnreadCount(pendingCount);
      } else {
        setNotifications([]);
        setUnreadCount(0);
      }
    } catch (error) {
      setNotifications([]);
      setUnreadCount(0);
    } finally {
      setLoading(false);
    }
  };

  // تحديد الإشعار كمقروء
  const markAsRead = async (notificationId) => {
    try {
      const response = await fetch('/api/smart-notifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'markAsRead',
          notificationId: notificationId
        })
      });

      if (response.ok) {
        setNotifications(prev =>
          prev.map(notif =>
            notif.ID === notificationId
              ? { ...notif, Status: 'read' }
              : notif
          )
        );
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (error) {

    }
  };

  // تحديد جميع الإشعارات كمقروءة
  const markAllAsRead = async () => {
    const unreadNotifications = notifications.filter(n => !n.IsRead);

    for (const notification of unreadNotifications) {
      await markAsRead(notification.ID);
    }
  };

  // تنسيق التاريخ
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));

    if (diffInMinutes < 1) return 'الآن';
    if (diffInMinutes < 60) return `منذ ${diffInMinutes} دقيقة`;
    if (diffInMinutes < 1440) return `منذ ${Math.floor(diffInMinutes / 60)} ساعة`;
    return `منذ ${Math.floor(diffInMinutes / 1440)} يوم`;
  };

  // أيقونة نوع الإشعار
  const getNotificationIcon = (type, priority) => {
    const iconClass = `w-4 h-4 ${
      priority === 'high' ? 'text-red-500' :
      priority === 'medium' ? 'text-yellow-500' :
      'text-blue-500'
    }`;

    switch (type) {
      case 'LOGIN':
      case 'LOGOUT':
        return <FiUser className={iconClass} />;
      case 'CREATE_EMPLOYEE':
      case 'UPDATE_EMPLOYEE':
      case 'DELETE_EMPLOYEE':
        return <FiUser className={iconClass} />;
      case 'CREATE_APARTMENT':
      case 'UPDATE_APARTMENT':
      case 'DELETE_APARTMENT':
      case 'ADD_BENEFICIARY':
      case 'REMOVE_BENEFICIARY':
        return <FiActivity className={iconClass} />;
      case 'UPLOAD_DOCUMENT':
      case 'DELETE_DOCUMENT':
        return <FiInfo className={iconClass} />;
      default:
        return <FiBell className={iconClass} />;
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* زر الإشعارات */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white transition-colors duration-200"
      >
        <FiBell className="w-6 h-6" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>

      {/* قائمة الإشعارات */}
      {isOpen && (
        <>
          {/* خلفية شفافة للإغلاق */}
          <div
            className="fixed inset-0 z-40"
            onClick={() => setIsOpen(false)}
          />

          {/* نافذة الإشعارات */}
          <div className="absolute left-0 mt-2 w-96 bg-white dark:bg-gray-800 rounded-lg shadow-2xl border-2 border-gray-300 dark:border-gray-600 z-50 max-h-96 overflow-hidden">
            {/* رأس النافذة */}
            <div className="flex items-center justify-between p-4 border-b-2 border-gray-300 dark:border-gray-600 bg-gradient-to-r from-blue-50 to-gray-50 dark:from-gray-800 dark:to-gray-700">
              <h3 className="text-xl font-black text-black dark:text-white">
                الإشعارات
              </h3>
              <div className="flex items-center gap-2">
                {unreadCount > 0 && (
                  <button
                    onClick={markAllAsRead}
                    className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                  >
                    تحديد الكل كمقروء
                  </button>
                )}
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                >
                  <FiX className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* محتوى الإشعارات */}
            <div className="max-h-80 overflow-y-auto">
              {loading ? (
                <div className="flex items-center justify-center p-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              ) : notifications.length === 0 ? (
                <div className="text-center p-8">
                  <FiBell className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400">لا توجد إشعارات</p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200 dark:divide-gray-700">
                  {notifications.map((notification) => (
                    <div
                      key={notification.ID}
                      className={`p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200 ${
                        notification.Status === 'pending' ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                      }`}
                    >
                      <div className="flex items-start gap-3">
                        {/* أيقونة الإشعار */}
                        <div className="flex-shrink-0 mt-1">
                          {getNotificationIcon(notification.NotificationType, notification.Priority)}
                        </div>

                        {/* محتوى الإشعار */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                              {notification.Title}
                            </h4>
                            {notification.Status === 'pending' && (
                              <button
                                onClick={() => markAsRead(notification.ID)}
                                className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 ml-2"
                                title="تحديد كمقروء"
                              >
                                <FiCheck className="w-4 h-4" />
                              </button>
                            )}
                          </div>

                          <p className="text-sm text-gray-600 dark:text-gray-300 mt-1 line-clamp-2">
                            {notification.Message}
                          </p>

                          <div className="flex items-center justify-between mt-2">
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {formatDate(notification.CreatedAt)}
                            </span>

                            {notification.ActionNameAr && (
                              <span className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-1 rounded-full">
                                {notification.ActionNameAr}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* تذييل النافذة */}
            {notifications.length > 0 && (
              <div className="p-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                <button
                  onClick={() => {
                    setIsOpen(false);
                    // يمكن إضافة رابط لصفحة الإشعارات الكاملة
                  }}
                  className="w-full text-center text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  عرض جميع الإشعارات
                </button>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
}
