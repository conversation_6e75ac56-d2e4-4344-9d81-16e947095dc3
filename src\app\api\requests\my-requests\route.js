import { NextResponse } from 'next/server';
import sql from 'mssql';

// إعدادات قاعدة البيانات
const config = {
  server: process.env.DB_SERVER || 'localhost',
  database: process.env.DB_NAME || 'EmployeeDB',
  user: process.env.DB_USER || 'sa',
  password: process.env.DB_PASSWORD || 'YourPassword123',
  options: {
    encrypt: false,
    trustServerCertificate: true,
    enableArithAbort: true,
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
};

export async function GET(request) {
  let pool;
  
  try {

    // الحصول على معرف المستخدم من الـ headers أو query parameters
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || 'current_user'; // يمكن تحسين هذا لاحقاً

    // الاتصال بقاعدة البيانات
    pool = await sql.connect(config);

    // التحقق من وجود بيانات تجريبية، وإنشاؤها إذا لم توجد
    const existingRequests = await pool.request().query(`
      SELECT COUNT(*) as count FROM LeaveRequests
    `);

    if (existingRequests.recordset[0].count === 0) {

      const sampleRequests = [
        {
          employeeName: 'أحمد محمد علي',
          employeeId: 'EMP001',
          department: 'الكهرباء',
          jobTitle: 'مهندس كهرباء',
          leaveType: 'annual',
          startDate: '2024-01-15',
          endDate: '2024-01-20',
          totalDays: 6,
          reason: 'إجازة سنوية للراحة',
          status: 'approved'
        },
        {
          employeeName: 'أحمد محمد علي',
          employeeId: 'EMP001',
          department: 'الكهرباء',
          jobTitle: 'مهندس كهرباء',
          leaveType: 'sick',
          startDate: '2024-02-10',
          endDate: '2024-02-12',
          totalDays: 3,
          reason: 'إجازة مرضية',
          status: 'pending'
        },
        {
          employeeName: 'فاطمة أحمد',
          employeeId: 'EMP002',
          department: 'الإدارة',
          jobTitle: 'محاسبة',
          leaveType: 'emergency',
          startDate: '2024-01-25',
          endDate: '2024-01-26',
          totalDays: 2,
          reason: 'ظروف طارئة عائلية',
          status: 'approved'
        },
        {
          employeeName: 'محمد علي',
          employeeId: 'EMP003',
          department: 'التنفيذ',
          jobTitle: 'مشرف تنفيذ',
          leaveType: 'annual',
          startDate: '2024-03-01',
          endDate: '2024-03-07',
          totalDays: 7,
          reason: 'إجازة سنوية',
          status: 'rejected'
        }
      ];

      for (const req of sampleRequests) {
        await pool.request()
          .input('EmployeeName', sql.NVarChar, req.employeeName)
          .input('EmployeeID', sql.NVarChar, req.employeeId)
          .input('Department', sql.NVarChar, req.department)
          .input('JobTitle', sql.NVarChar, req.jobTitle)
          .input('LeaveType', sql.NVarChar, req.leaveType)
          .input('StartDate', sql.Date, new Date(req.startDate))
          .input('EndDate', sql.Date, new Date(req.endDate))
          .input('TotalDays', sql.Int, req.totalDays)
          .input('Reason', sql.NVarChar, req.reason)
          .input('Status', sql.NVarChar, req.status)
          .query(`
            INSERT INTO LeaveRequests (
              EmployeeName, EmployeeID, Department, JobTitle, LeaveType,
              StartDate, EndDate, TotalDays, Reason, Status,
              SubmittedAt, ReviewedAt
            ) VALUES (
              @EmployeeName, @EmployeeID, @Department, @JobTitle, @LeaveType,
              @StartDate, @EndDate, @TotalDays, @Reason, @Status,
              DATEADD(day, -${Math.floor(Math.random() * 30)}, GETDATE()),
              ${req.status !== 'pending' ? 'DATEADD(day, -' + Math.floor(Math.random() * 20) + ', GETDATE())' : 'NULL'}
            )
          `);
      }
    }

    // جلب طلبات المستخدم من جدول PaperRequests
    const result = await pool.request().query(`
      SELECT
        ID as id,
        EmployeeName as employeeName,
        EmployeeCode as employeeId,
        Department as department,
        JobTitle as jobTitle,
        RequestType as requestType,
        LeaveType as type,
        CASE
          WHEN RequestType = 'leave' AND LeaveType = 'اعتيادية' THEN 'طلب إجازة اعتيادية'
          WHEN RequestType = 'leave' AND LeaveType = 'مرضية' THEN 'طلب إجازة مرضية'
          WHEN RequestType = 'leave' AND LeaveType = 'عارضة' THEN 'طلب إجازة عارضة'
          WHEN RequestType = 'leave' AND LeaveType = 'أمومة' THEN 'طلب إجازة أمومة'
          WHEN RequestType = 'leave' AND LeaveType = 'أبوة' THEN 'طلب إجازة أبوة'
          WHEN RequestType = 'leave' AND LeaveType = 'بدون راتب' THEN 'طلب إجازة بدون راتب'
          WHEN RequestType = 'mission' THEN 'طلب مأمورية'
          WHEN RequestType = 'permission' THEN 'طلب إذن'
          WHEN RequestType = 'night_shift' THEN 'طلب وردية ليلية'
          ELSE 'طلب'
        END as title,
        StartDate as startDate,
        EndDate as endDate,
        DaysCount as totalDays,
        CASE
          WHEN StartDate IS NOT NULL AND EndDate IS NOT NULL THEN
            CONCAT(
              FORMAT(StartDate, 'dd/MM/yyyy'),
              ' - ',
              FORMAT(EndDate, 'dd/MM/yyyy'),
              ' (', DaysCount, ' أيام)'
            )
          ELSE 'غير محدد'
        END as period,
        LeaveReason as reason,
        Status as status,
        RequestDate as submittedAt,
        ApprovalDate as reviewedAt,
        ApprovedBy as reviewedBy,
        ApprovalNotes as reviewComments
      FROM PaperRequests
      ORDER BY RequestDate DESC
    `);

    return NextResponse.json({
      success: true,
      requests: result.recordset
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'خطأ في جلب طلبات المستخدم',
      error: error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      await pool.close();

    }
  }
}
