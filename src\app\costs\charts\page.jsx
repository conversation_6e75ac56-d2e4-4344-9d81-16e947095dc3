'use client';

import Professional<PERSON>hart from '@/components/ProfessionalChart';
import {
    CategoryScale,
    Chart as ChartJ<PERSON>,
    Filler,
    Legend,
    LinearScale,
    LineElement,
    PointElement,
    Title,
    Tooltip
} from 'chart.js';
import { useEffect, useState } from 'react';
import { FiBarChart, FiDollarSign, FiPrinter, FiRefreshCw } from 'react-icons/fi';

ChartJS.register(
  CategoryScale,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

// دالة مساعدة لتحويل رقم الشهر إلى اسم
const getMonthName = (monthNumber) => {
  const months = {
    1: 'يناير', 2: 'فبراير', 3: 'مارس', 4: 'ابريل',
    5: 'مايو', 6: 'يونيو', 7: 'يوليو', 8: 'اغسطس',
    9: 'سبتمبر', 10: 'اكتوبر', 11: 'نوفمبر', 12: 'ديسمبر'
  };
  return months[monthNumber] || 'غير محدد';
};

// دالة تنسيق الأرقام
const formatNumber = (num) => {
  return num.toLocaleString('ar-EG');
};

// دالة لجلب بيانات الرسوم البيانية من قاعدة البيانات
const fetchChartsData = async () => {
  try {
    const response = await fetch('/api/monthly-costs', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'getChartsData' })
    });

    const result = await response.json();
    if (result.success) {
      return result.data;
    } else {
      console.error('فشل في جلب البيانات:', result.error);
      return null;
    }
  } catch (error) {
    console.error('خطأ في جلب البيانات:', error);
    return null;
  }
};

// مكون MainLayout مبسط
function SimpleLayout({ children }) {
  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900">
      <div className="p-6">
        {children}
      </div>
    </div>
  );
}

export default function CostsChartsPage() {
  const [loading, setLoading] = useState(true);
  const [chartsData, setChartsData] = useState(null);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear().toString());

  // جلب البيانات عند تحميل الصفحة
  useEffect(() => {
    loadChartsData();
  }, []);

  const loadChartsData = async () => {
    setLoading(true);
    try {
      const data = await fetchChartsData();
      if (data) {
        setChartsData(data);
        // تحديد السنوات المتاحة
        const years = new Set();
        Object.values(data).forEach(categoryData => {
          if (categoryData && categoryData.length > 0) {
            categoryData.forEach(item => {
              if (item.year) years.add(item.year);
            });
          }
        });
        const availableYears = Array.from(years).sort((a, b) => b - a);
        if (availableYears.length > 0) {
          setSelectedYear(availableYears[0].toString());
        }
      }
    } catch (error) {
      console.error('خطأ في تحميل البيانات:', error);
    } finally {
      setLoading(false);
    }
  };

  // دالة لتصفية البيانات حسب السنة
  const filterDataByYear = (data, year) => {
    if (!data || !Array.isArray(data)) return [];
    return data.filter(item => item.year === parseInt(year));
  };

  // تصفية البيانات حسب السنة المختارة
  const filteredCarsData = chartsData ? filterDataByYear(chartsData.cars, selectedYear) : [];
  const filteredApartmentsData = chartsData ? filterDataByYear(chartsData.apartments, selectedYear) : [];
  const filteredTempWorkersData = chartsData ? filterDataByYear(chartsData.tempWorkers, selectedYear) : [];

  // الحصول على السنوات المتاحة
  const getAvailableYears = () => {
    if (!chartsData) return [];
    const years = new Set();
    Object.values(chartsData).forEach(categoryData => {
      if (categoryData && Array.isArray(categoryData)) {
        categoryData.forEach(item => {
          if (item.year) years.add(item.year);
        });
      }
    });
    return Array.from(years).sort((a, b) => b - a);
  };

  const availableYears = getAvailableYears();

  // حساب تكاليف السنة المحددة
  const calculateYearlyCosts = () => {
    if (!chartsData) return { yearlyCategoryCosts: { cars: 0, apartments: 0, tempWorkers: 0 }, totalYearCost: 0 };

    const year = parseInt(selectedYear);
    const yearlyCategoryCosts = {
      cars: filteredCarsData.reduce((sum, item) => sum + item.cost, 0),
      apartments: filteredApartmentsData.reduce((sum, item) => sum + item.cost, 0),
      tempWorkers: filteredTempWorkersData.reduce((sum, item) => sum + item.cost, 0)
    };

    const totalYearCost = yearlyCategoryCosts.cars + yearlyCategoryCosts.apartments + yearlyCategoryCosts.tempWorkers;

    return { yearlyCategoryCosts, totalYearCost };
  };

  const { yearlyCategoryCosts, totalYearCost } = calculateYearlyCosts();

  const getPercentage = (cost) => {
    return totalYearCost > 0 ? ((cost / totalYearCost) * 100).toFixed(1) : 0;
  };

  // حساب إجمالي التكاليف لجميع السنوات
  const calculateOverallTotalCosts = () => {
    if (!chartsData) return { overallCategoryCosts: { cars: 0, apartments: 0, tempWorkers: 0 }, overallTotalCost: 0 };

    const overallCategoryCosts = {
      cars: chartsData.cars.reduce((sum, item) => sum + item.cost, 0),
      apartments: chartsData.apartments.reduce((sum, item) => sum + item.cost, 0),
      tempWorkers: chartsData.tempWorkers.reduce((sum, item) => sum + item.cost, 0)
    };

    const overallTotalCost = overallCategoryCosts.cars + overallCategoryCosts.apartments + overallCategoryCosts.tempWorkers;
    return { overallCategoryCosts, overallTotalCost };
  };

  const { overallCategoryCosts, overallTotalCost } = calculateOverallTotalCosts();

  const getOverallPercentage = (cost) => {
    return overallTotalCost > 0 ? ((cost / overallTotalCost) * 100).toFixed(1) : 0;
  };

  // طباعة الرسوم البيانية فقط
  const handlePrint = () => {
    // إنشاء نافذة جديدة للطباعة
    const printWindow = window.open('', '_blank');

    // إنشاء محتوى HTML للطباعة
    const printContent = `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>الرسوم البيانية للتكاليف - مشروع أوجستا</title>
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: white;
            color: #333;
            line-height: 1.6;
          }

          .print-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #2563EB;
            padding-bottom: 20px;
          }

          .print-header h1 {
            font-size: 28px;
            color: #1f2937;
            margin-bottom: 10px;
          }

          .print-header h2 {
            font-size: 18px;
            color: #6b7280;
            margin-bottom: 5px;
          }

          .print-header p {
            font-size: 14px;
            color: #9ca3af;
          }

          .chart-page {
            page-break-before: always;
            margin-bottom: 40px;
            padding: 20px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
          }

          .chart-page:first-child {
            page-break-before: auto;
          }

          .chart-title {
            font-size: 20px;
            font-weight: bold;
            color: #1f2937;
            text-align: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e5e7eb;
          }

          .chart-stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 20px;
          }

          .stat-box {
            text-align: center;
            padding: 15px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            background: #f9fafb;
          }

          .stat-label {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 5px;
          }

          .stat-value {
            font-size: 16px;
            font-weight: bold;
            color: #1f2937;
          }

          .chart-container {
            height: 400px;
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          @page {
            size: A4 landscape;
            margin: 1.5cm;
          }

          @media print {
            .chart-page {
              page-break-inside: avoid;
            }
          }
        </style>
      </head>
      <body>
        <div class="print-header">
          <h1>الرسوم البيانية للتكاليف</h1>
          <h2>مشروع أوجستا</h2>
          <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-EG')}</p>
        </div>

        ${generateChartPages()}
      </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();

    // انتظار تحميل المحتوى ثم الطباعة
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 1000);
  };

  // دالة لإنشاء صفحات الرسوم البيانية
  const generateChartPages = () => {
    const charts = [
      {
        title: 'تكاليف السيارات المؤجرة بمشروع أوجستا',
        data: filteredCarsData,
        type: 'cars'
      },
      {
        title: 'تكاليف الشقق المؤجرة بمشروع أوجستا',
        data: filteredApartmentsData,
        type: 'apartments'
      },
      {
        title: 'تكاليف العمال المؤقتين بمشروع أوجستا',
        data: filteredTempWorkersData,
        type: 'tempWorkers'
      }
    ];

    return charts.map(chart => {
      const costs = chart.data.map(item => item.cost);
      const counts = chart.data.map(item => item.count);

      return `
        <div class="chart-page">
          <div class="chart-title">${chart.title}</div>

          <div class="chart-stats">
            <div class="stat-box">
              <div class="stat-label">إجمالي التكلفة</div>
              <div class="stat-value">${formatNumber(costs.reduce((sum, cost) => sum + cost, 0))} ج.م</div>
            </div>
            <div class="stat-box">
              <div class="stat-label">متوسط العدد</div>
              <div class="stat-value">${costs.length > 0 ? Math.round(counts.reduce((sum, count) => sum + count, 0) / counts.length) : 0}</div>
            </div>
            <div class="stat-box">
              <div class="stat-label">أعلى تكلفة</div>
              <div class="stat-value">${costs.length > 0 ? formatNumber(Math.max(...costs)) : 0} ج.م</div>
            </div>
            <div class="stat-box">
              <div class="stat-label">متوسط التكلفة الشهرية</div>
              <div class="stat-value">${costs.length > 0 ? formatNumber(costs.reduce((sum, cost) => sum + cost, 0) / costs.length) : 0} ج.م</div>
            </div>
          </div>

          <div class="chart-container">
            <p style="color: #6b7280; font-size: 16px;">
              📊 الرسم البياني لـ ${chart.title}<br>
              📈 ${chart.data.length} شهر من البيانات<br>
              💰 إجمالي: ${formatNumber(costs.reduce((sum, cost) => sum + cost, 0))} ج.م
            </p>
          </div>
        </div>
      `;
    }).join('');
  };

  return (
    <SimpleLayout>

      <div className="max-w-7xl mx-auto">
        {/* Header مع أدوات التحكم */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FiBarChart className="text-3xl text-blue-600" />
              <div>
                <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100">الرسوم البيانية للتكاليف</h1>
                <p className="text-gray-600 dark:text-gray-300">تحليل بصري شامل لتطور التكاليف والأعداد عبر الزمن</p>
              </div>
            </div>

            {/* أدوات التحكم */}
            <div className="flex items-center gap-4 print:hidden">
              {/* اختيار السنة */}
              <div className="flex items-center gap-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-200">السنة:</label>
                <select
                  value={selectedYear}
                  onChange={(e) => setSelectedYear(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                >
                  {availableYears.map(year => (
                    <option key={year} value={year}>{year}</option>
                  ))}
                </select>
              </div>

              {/* زر تحديث البيانات */}
              <button
                onClick={loadChartsData}
                disabled={loading}
                className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors disabled:bg-green-400"
              >
                <FiRefreshCw className={`text-lg ${loading ? 'animate-spin' : ''}`} />
                <span>{loading ? 'جاري التحديث...' : 'تحديث البيانات'}</span>
              </button>

              {/* زر الطباعة */}
              <button
                onClick={handlePrint}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                <FiPrinter className="text-lg" />
                <span>طباعة</span>
              </button>
            </div>
          </div>
        </div>

        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600 mt-4">جاري تحميل الرسوم البيانية...</p>
          </div>
        ) : (
          <div className="space-y-8">


            {/* رسم تكاليف السيارات */}
            <ProfessionalChart
              title="تكاليف سيارات المؤجرة بمشروع أوجستا"
              data={chartsData?.cars || []}
              type="cars"
              selectedYear={selectedYear}
            />

            {/* رسم تكاليف الشقق */}
            <ProfessionalChart
              title="تكاليف الشقق المؤجرة بمشروع أوجستا"
              data={chartsData?.apartments || []}
              type="apartments"
              selectedYear={selectedYear}
            />

            {/* رسم تكاليف العمالة المؤقتة */}
            <ProfessionalChart
              title="تكاليف العمال المؤقتين بمشروع أوجستا"
              data={chartsData?.tempWorkers || []}
              type="tempWorkers"
              selectedYear={selectedYear}
            />

            {/* قسم جديد: إحصائيات السنة المحددة */}
            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-8 mb-4">إجمالي التكاليف للسنة <span className="text-blue-600">({selectedYear})</span></h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* تكلفة السيارات للسنة المحددة */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 relative overflow-hidden text-right">
                <FiDollarSign className="absolute top-4 left-4 text-4xl text-blue-600 opacity-20" />
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">تكلفة السيارات</h3>
                <p className="text-4xl font-bold text-blue-600 mb-1">
                  {yearlyCategoryCosts.cars.toLocaleString('ar-EG')} ج.م
                </p>
                <p className="text-base text-gray-600 dark:text-gray-300">
                  {getPercentage(yearlyCategoryCosts.cars)}% من إجمالي التكاليف
                </p>
              </div>
              {/* تكلفة الشقق للسنة المحددة */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 relative overflow-hidden text-right">
                <FiDollarSign className="absolute top-4 left-4 text-4xl text-green-600 opacity-20" />
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">تكلفة الشقق</h3>
                <p className="text-4xl font-bold text-green-600 mb-1">
                  {yearlyCategoryCosts.apartments.toLocaleString('ar-EG')} ج.م
                </p>
                <p className="text-base text-gray-600 dark:text-gray-300">
                  {getPercentage(yearlyCategoryCosts.apartments)}% من إجمالي التكاليف
                </p>
              </div>
              {/* تكلفة العمالة للسنة المحددة */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 relative overflow-hidden text-right">
                <FiDollarSign className="absolute top-4 left-4 text-4xl text-purple-600 opacity-20" />
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">تكلفة العمالة</h3>
                <p className="text-4xl font-bold text-purple-600 mb-1">
                  {yearlyCategoryCosts.tempWorkers.toLocaleString('ar-EG')} ج.م
                </p>
                <p className="text-base text-gray-600 dark:text-gray-300">
                  {getPercentage(yearlyCategoryCosts.tempWorkers)}% من إجمالي التكاليف
                </p>
              </div>
            </div>

            {/* قسم إحصائيات إجمالي التكاليف لجميع السنوات */}
            <h2 className="text-2xl font-bold text-gray-800 dark:text-gray-100 mt-8 mb-4">إجمالي التكاليف لجميع السنوات</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* إجمالي تكاليف السيارات لجميع السنوات */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 relative overflow-hidden text-right">
                <FiDollarSign className="absolute top-4 left-4 text-4xl text-blue-600 opacity-20" />
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">إجمالي تكاليف السيارات</h3>
                <p className="text-4xl font-bold text-blue-600 mb-1">
                  {overallCategoryCosts.cars.toLocaleString('ar-EG')} ج.م
                </p>
                <p className="text-base text-gray-600 dark:text-gray-300">
                  {getOverallPercentage(overallCategoryCosts.cars)}% من إجمالي التكاليف الكلية
                </p>
              </div>

              {/* إجمالي تكاليف الشقق لجميع السنوات */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 relative overflow-hidden text-right">
                <FiDollarSign className="absolute top-4 left-4 text-4xl text-green-600 opacity-20" />
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">إجمالي تكاليف الشقق</h3>
                <p className="text-4xl font-bold text-green-600 mb-1">
                  {overallCategoryCosts.apartments.toLocaleString('ar-EG')} ج.م
                </p>
                <p className="text-base text-gray-600 dark:text-gray-300">
                  {getOverallPercentage(overallCategoryCosts.apartments)}% من إجمالي التكاليف الكلية
                </p>
              </div>

              {/* إجمالي تكاليف العمالة لجميع السنوات */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 relative overflow-hidden text-right">
                <FiDollarSign className="absolute top-4 left-4 text-4xl text-purple-600 opacity-20" />
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-100 mb-2">إجمالي تكاليف العمالة</h3>
                <p className="text-4xl font-bold text-purple-600 mb-1">
                  {overallCategoryCosts.tempWorkers.toLocaleString('ar-EG')} ج.م
                </p>
                <p className="text-base text-gray-600 dark:text-gray-300">
                  {getOverallPercentage(overallCategoryCosts.tempWorkers)}% من إجمالي التكاليف الكلية
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </SimpleLayout>
  );
}
