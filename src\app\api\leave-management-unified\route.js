import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function POST(request) {
  try {
    const body = await request.json();
    const { action } = body;

    const pool = await getConnection();

    switch (action) {
      case 'create-request':
        return await createLeaveRequest(pool, body);
      case 'list-requests':
        return await getLeaveRequests(pool, body);
      case 'list-submitted-requests':
        return await getSubmittedRequests(pool, body);
      case 'list-approved-procedures':
        return await getApprovedProcedures(pool, body);
      case 'approve-request':
        return await approveLeaveRequest(pool, body);
      case 'reject-request':
        return await rejectLeaveRequest(pool, body);
      case 'reverse-approval':
        return await reverseApproval(pool, body);
      case 'get-balance':
        return await getLeaveBalance(pool, body);
      case 'get-employee':
        return await getEmployeeData(pool, body);
      case 'check-conflicts':
        return await checkLeaveConflicts(pool, body);
      case 'get-approved-leaves':
        return await getApprovedLeaves(pool, body);
      case 'update-attendance-from-requests':
        return await updateAttendanceFromRequests(pool, body);
      case 'get-request-types':
        return await getRequestTypes(pool, body);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في إدارة الإجازات: ' + error.message
    }, { status: 500 });
  }
}

// إنشاء طلب إجازة جديد
async function createLeaveRequest(pool, data) {
  try {
    const {
      employeeCode,
      employeeName,
      jobTitle,
      department,
      requestType = 'leave',
      leaveType,
      startDate,
      endDate,
      daysCount,
      reason
    } = data;

    // التحقق من البيانات المطلوبة
    if (!employeeCode || !employeeName || !leaveType) {
      return NextResponse.json({
        success: false,
        error: 'كود الموظف واسم الموظف ونوع الإجازة مطلوبة'
      }, { status: 400 });
    }

    // جلب معلومات نوع الطلب
    const requestTypeInfo = await pool.request()
      .input('leaveType', sql.NVarChar, leaveType)
      .query(`
        SELECT RequiresApproval, AffectsBalance, MaxDaysAllowed
        FROM RequestTypes
        WHERE TypeCode = @leaveType OR TypeNameArabic = @leaveType
      `);

    const typeInfo = requestTypeInfo.recordset[0] || {
      RequiresApproval: true,
      AffectsBalance: true,
      MaxDaysAllowed: null
    };

    // التحقق من الحد الأقصى للأيام
    if (typeInfo.MaxDaysAllowed && daysCount > typeInfo.MaxDaysAllowed) {
      return NextResponse.json({
        success: false,
        error: `الحد الأقصى لهذا النوع من الإجازات هو ${typeInfo.MaxDaysAllowed} أيام`
      }, { status: 400 });
    }

    // التحقق من وجود تضارب (للإجازات التي لها تواريخ)
    if (startDate && endDate) {
      const conflictCheck = await checkLeaveConflicts(pool, {
        employeeCode,
        startDate,
        endDate,
        excludeId: null
      });

      if (conflictCheck.hasConflict) {
        return NextResponse.json({
          success: false,
          error: 'يوجد تضارب مع طلب آخر',
          conflicts: conflictCheck.conflicts
        }, { status: 409 });
      }
    }

    // تحديد الحالة الأولية
    const initialStatus = typeInfo.RequiresApproval ? 'قيد المراجعة' : 'معتمد';
    const isAutoApproved = !typeInfo.RequiresApproval;

    // إدراج طلب الإجازة في PaperRequests
    const result = await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .input('employeeName', sql.NVarChar, employeeName)
      .input('jobTitle', sql.NVarChar, jobTitle || '')
      .input('department', sql.NVarChar, department || '')
      .input('requestType', sql.NVarChar, requestType)
      .input('leaveType', sql.NVarChar, leaveType)
      .input('startDate', sql.Date, startDate ? new Date(startDate) : null)
      .input('endDate', sql.Date, endDate ? new Date(endDate) : null)
      .input('daysCount', sql.Int, daysCount || 0)
      .input('reason', sql.NVarChar, reason || '')
      .input('status', sql.NVarChar, initialStatus)
      .input('isAutoApproved', sql.Bit, isAutoApproved)
      .query(`
        INSERT INTO PaperRequests
        (EmployeeCode, EmployeeName, JobTitle, Department, RequestType, LeaveType,
         StartDate, EndDate, DaysCount, LeaveReason, Status, IsAutoApproved,
         ApprovalDate, ApprovedBy)
        VALUES (@employeeCode, @employeeName, @jobTitle, @department, @requestType, @leaveType,
                @startDate, @endDate, @daysCount, @reason, @status, @isAutoApproved,
                ${isAutoApproved ? 'GETDATE()' : 'NULL'}, ${isAutoApproved ? "'النظام'" : 'NULL'});
        SELECT SCOPE_IDENTITY() as RequestID;
      `);

    const requestId = result.recordset[0].RequestID;

    // إذا كان الطلب معتمد تلقائياً، قم بتحديث الحضور والرصيد
    if (isAutoApproved) {
      await processApprovedRequest(pool, {
        requestId,
        employeeCode,
        leaveType,
        startDate,
        endDate,
        daysCount,
        affectsBalance: typeInfo.AffectsBalance
      });
    }

    // إرسال إشعار
    await sendLeaveNotification(pool, {
      requestId,
      employeeCode,
      employeeName,
      leaveType,
      startDate,
      daysCount,
      status: initialStatus
    });

    return NextResponse.json({
      success: true,
      requestId: requestId,
      status: initialStatus,
      isAutoApproved: isAutoApproved,
      message: isAutoApproved ? 'تم اعتماد الطلب تلقائياً' : 'تم إرسال طلب الإجازة بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في إنشاء طلب الإجازة: ' + error.message
    }, { status: 500 });
  }
}

// جلب طلبات الإجازات (عام)
async function getLeaveRequests(pool, data) {
  try {
    const { employeeCode, status, leaveType, dateFrom, dateTo } = data;

    let query = `
      SELECT
        pr.ID,
        pr.EmployeeCode,
        pr.EmployeeName,
        pr.JobTitle,
        pr.Department,
        pr.RequestType,
        pr.LeaveType,
        pr.StartDate,
        pr.EndDate,
        pr.DaysCount,
        pr.LeaveReason as Reason,
        pr.Status,
        pr.RequestDate,
        pr.ApprovalDate,
        pr.RejectionDate,
        pr.ApprovedBy,
        pr.RejectedBy,
        pr.ApprovalNotes,
        pr.RejectionReason,
        pr.IsAutoApproved,
        lb.RemainingAnnual,
        lb.RemainingCasual
      FROM PaperRequests pr
      LEFT JOIN LeaveBalances lb ON pr.EmployeeCode = lb.EmployeeCode AND lb.Year = YEAR(GETDATE())
      WHERE 1=1
    `;

    const request = pool.request();
    const conditions = [];

    if (employeeCode) {
      conditions.push('pr.EmployeeCode = @employeeCode');
      request.input('employeeCode', sql.NVarChar, employeeCode);
    }

    if (status) {
      conditions.push('pr.Status = @status');
      request.input('status', sql.NVarChar, status);
    }

    if (leaveType) {
      conditions.push('pr.LeaveType = @leaveType');
      request.input('leaveType', sql.NVarChar, leaveType);
    }

    if (dateFrom) {
      conditions.push('pr.StartDate >= @dateFrom');
      request.input('dateFrom', sql.Date, dateFrom);
    }

    if (dateTo) {
      conditions.push('pr.EndDate <= @dateTo');
      request.input('dateTo', sql.Date, dateTo);
    }

    if (conditions.length > 0) {
      query += ' AND ' + conditions.join(' AND ');
    }

    query += ' ORDER BY pr.RequestDate DESC';

    const result = await request.query(query);

    return NextResponse.json({
      success: true,
      data: result.recordset,
      totalCount: result.recordset.length
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب طلبات الإجازات: ' + error.message
    }, { status: 500 });
  }
}

// جلب الطلبات المقدمة (قيد المراجعة)
async function getSubmittedRequests(pool, data) {
  try {
    const { employeeCode, limit = 100 } = data;

    let query = `
      SELECT
        pr.ID,
        pr.EmployeeCode,
        pr.EmployeeName,
        pr.JobTitle,
        pr.Department,
        pr.RequestType,
        pr.LeaveType,
        pr.StartDate,
        pr.EndDate,
        pr.DaysCount,
        pr.LeaveReason as Reason,
        pr.Status,
        pr.RequestDate,
        rt.TypeNameArabic as LeaveTypeArabic,
        rt.RequiresApproval,
        lb.RemainingAnnual,
        lb.RemainingCasual,
        CASE
          WHEN pr.LeaveType = 'annual' OR pr.LeaveType = N'إجازة إعتيادية' THEN lb.RemainingAnnual
          WHEN pr.LeaveType = 'casual' OR pr.LeaveType = N'إجازة عارضة' THEN lb.RemainingCasual
          WHEN pr.LeaveType = 'unpaid' OR pr.LeaveType = N'إجازة بدون أجر' THEN 999
          ELSE NULL
        END as RemainingBalance
      FROM PaperRequests pr
      LEFT JOIN RequestTypes rt ON (pr.LeaveType = rt.TypeCode OR pr.LeaveType = rt.TypeNameArabic)
      LEFT JOIN LeaveBalances lb ON pr.EmployeeCode = lb.EmployeeCode
        AND lb.Year = YEAR(GETDATE())
      WHERE pr.Status = N'قيد المراجعة'
    `;

    const request = pool.request();

    if (employeeCode) {
      query += ` AND pr.EmployeeCode = @employeeCode`;
      request.input('employeeCode', sql.NVarChar, employeeCode);
    }

    query += ` ORDER BY pr.RequestDate DESC`;

    if (limit) {
      query = `SELECT TOP ${limit} * FROM (${query}) AS LimitedResults`;
    }

    const result = await request.query(query);

    return NextResponse.json({
      success: true,
      data: result.recordset,
      count: result.recordset.length,
      message: 'تم جلب الطلبات المقدمة بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب الطلبات المقدمة: ' + error.message
    }, { status: 500 });
  }
}

// جلب الإجراءات المعتمدة
async function getApprovedProcedures(pool, data) {
  try {
    const { employeeCode, limit = 100 } = data;

    let query = `
      SELECT
        pr.ID,
        pr.EmployeeCode,
        pr.EmployeeName,
        pr.JobTitle,
        pr.Department,
        pr.RequestType,
        pr.LeaveType,
        pr.StartDate,
        pr.EndDate,
        pr.DaysCount,
        pr.LeaveReason as Reason,
        pr.Status,
        pr.RequestDate,
        pr.ApprovalDate,
        pr.ApprovedBy,
        pr.IsAutoApproved,
        rt.TypeNameArabic as LeaveTypeArabic,
        rt.RequiresApproval,
        lb.RemainingAnnual,
        lb.RemainingCasual
      FROM PaperRequests pr
      LEFT JOIN RequestTypes rt ON (pr.LeaveType = rt.TypeCode OR pr.LeaveType = rt.TypeNameArabic)
      LEFT JOIN LeaveBalances lb ON pr.EmployeeCode = lb.EmployeeCode
        AND lb.Year = YEAR(GETDATE())
      WHERE pr.Status = N'معتمد'
    `;

    const request = pool.request();

    if (employeeCode) {
      query += ` AND pr.EmployeeCode = @employeeCode`;
      request.input('employeeCode', sql.NVarChar, employeeCode);
    }

    query += ` ORDER BY pr.ApprovalDate DESC`;

    if (limit) {
      query = `SELECT TOP ${limit} * FROM (${query}) AS LimitedResults`;
    }

    const result = await request.query(query);

    return NextResponse.json({
      success: true,
      data: result.recordset,
      count: result.recordset.length,
      message: 'تم جلب الإجراءات المعتمدة بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب الإجراءات المعتمدة: ' + error.message
    }, { status: 500 });
  }
}

// اعتماد طلب إجازة
async function approveLeaveRequest(pool, data) {
  try {
    const { requestId, approvedBy, notes } = data;

    if (!requestId) {
      return NextResponse.json({
        success: false,
        error: 'معرف الطلب مطلوب'
      }, { status: 400 });
    }

    // جلب تفاصيل الطلب
    const requestDetails = await pool.request()
      .input('requestId', sql.Int, requestId)
      .query(`
        SELECT EmployeeCode, LeaveType, DaysCount, Status
        FROM LeaveRequests 
        WHERE ID = @requestId
      `);

    if (requestDetails.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الطلب غير موجود'
      }, { status: 404 });
    }

    const request = requestDetails.recordset[0];

    if (request.Status === 'معتمد') {
      return NextResponse.json({
        success: false,
        error: 'الطلب معتمد مسبقاً'
      }, { status: 400 });
    }

    // تحديث حالة الطلب
    await pool.request()
      .input('requestId', sql.Int, requestId)
      .query(`
        UPDATE LeaveRequests
        SET
          Status = N'معتمد'
        WHERE ID = @requestId
      `);

    // خصم من رصيد الإجازات (إذا لم تكن بدون أجر)
    if (request.LeaveType !== 'بدون أجر') {
      await deductFromLeaveBalance(pool, {
        employeeCode: request.EmployeeCode,
        leaveType: request.LeaveType,
        daysCount: request.DaysCount
      });
    }

    return NextResponse.json({
      success: true,
      message: 'تم اعتماد طلب الإجازة بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في اعتماد طلب الإجازة: ' + error.message
    }, { status: 500 });
  }
}

// رفض طلب إجازة
async function rejectLeaveRequest(pool, data) {
  try {
    const { requestId, rejectedBy, rejectionReason } = data;

    if (!requestId || !rejectionReason) {
      return NextResponse.json({
        success: false,
        error: 'معرف الطلب وسبب الرفض مطلوبان'
      }, { status: 400 });
    }

    await pool.request()
      .input('requestId', sql.Int, requestId)
      .query(`
        UPDATE LeaveRequests
        SET
          Status = N'مرفوض'
        WHERE ID = @requestId
      `);

    return NextResponse.json({
      success: true,
      message: 'تم رفض طلب الإجازة'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في رفض طلب الإجازة: ' + error.message
    }, { status: 500 });
  }
}

// الرجوع عن اعتماد طلب إجازة
async function reverseApproval(pool, data) {
  try {
    const { requestId, reversedBy, reason } = data;

    if (!requestId) {
      return NextResponse.json({
        success: false,
        error: 'معرف الطلب مطلوب'
      }, { status: 400 });
    }

    // جلب تفاصيل الطلب
    const requestDetails = await pool.request()
      .input('requestId', sql.Int, requestId)
      .query(`
        SELECT EmployeeCode, LeaveType, DaysCount, Status
        FROM LeaveRequests
        WHERE ID = @requestId
      `);

    if (requestDetails.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الطلب غير موجود'
      }, { status: 404 });
    }

    const request = requestDetails.recordset[0];

    if (request.Status !== 'معتمد') {
      return NextResponse.json({
        success: false,
        error: 'يمكن الرجوع فقط عن الطلبات المعتمدة'
      }, { status: 400 });
    }

    // تحديث حالة الطلب
    await pool.request()
      .input('requestId', sql.Int, requestId)
      .input('reversedBy', sql.NVarChar, reversedBy || 'النظام')
      .input('reason', sql.NVarChar, reason || 'رجوع عن الاعتماد')
      .query(`
        UPDATE LeaveRequests
        SET
          Status = N'قيد المراجعة',
          ApprovalDate = NULL,
          ApprovedBy = NULL,
          Notes = CONCAT(ISNULL(Notes, ''), ' - تم الرجوع عن الاعتماد بواسطة: ', @reversedBy, ' - السبب: ', @reason),
          UpdatedAt = GETDATE()
        WHERE ID = @requestId
      `);

    // إرجاع الرصيد (إذا لم تكن بدون أجر)
    if (request.LeaveType !== 'بدون أجر') {
      await restoreLeaveBalance(pool, {
        employeeCode: request.EmployeeCode,
        leaveType: request.LeaveType,
        daysCount: request.DaysCount
      });
    }

    return NextResponse.json({
      success: true,
      message: 'تم الرجوع عن اعتماد طلب الإجازة بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الرجوع عن الاعتماد: ' + error.message
    }, { status: 500 });
  }
}

// جلب رصيد الإجازات
async function getLeaveBalance(pool, data) {
  try {
    const { employeeCode } = data;

    if (!employeeCode) {
      return NextResponse.json({
        success: false,
        error: 'كود الموظف مطلوب'
      }, { status: 400 });
    }

    const result = await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .query(`
        SELECT
          lb.*,
          e.EmployeeName,
          e.JobTitle,
          e.Department
        FROM LeaveBalances lb
        INNER JOIN Employees e ON lb.EmployeeCode = e.EmployeeCode
        WHERE lb.EmployeeCode = @employeeCode
        AND lb.Year = YEAR(GETDATE())
      `);

    if (result.recordset.length === 0) {
      // إنشاء رصيد جديد للموظف
      await pool.request()
        .input('employeeCode', sql.NVarChar, employeeCode)
        .query(`
          INSERT INTO LeaveBalances (EmployeeCode, EmployeeName, JobTitle, Department, AnnualBalance, CasualBalance, Year)
          SELECT
            e.EmployeeCode,
            e.EmployeeName,
            e.JobTitle,
            e.Department,
            15 as AnnualBalance,
            6 as CasualBalance,
            YEAR(GETDATE()) as Year
          FROM Employees e
          WHERE e.EmployeeCode = @employeeCode
        `);

      // جلب الرصيد المنشأ حديثاً
      const newResult = await pool.request()
        .input('employeeCode', sql.NVarChar, employeeCode)
        .query(`
          SELECT
            lb.*,
            e.EmployeeName,
            e.JobTitle,
            e.Department
          FROM LeaveBalances lb
          INNER JOIN Employees e ON lb.EmployeeCode = e.EmployeeCode
          WHERE lb.EmployeeCode = @employeeCode
          AND lb.Year = YEAR(GETDATE())
        `);

      return NextResponse.json({
        success: true,
        data: newResult.recordset[0]
      });
    }

    return NextResponse.json({
      success: true,
      data: result.recordset[0]
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب رصيد الإجازات: ' + error.message
    }, { status: 500 });
  }
}

// جلب بيانات الموظف
async function getEmployeeData(pool, data) {
  try {
    const { employeeCode } = data;

    if (!employeeCode) {
      return NextResponse.json({
        success: false,
        error: 'كود الموظف مطلوب'
      }, { status: 400 });
    }

    const result = await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .query(`
        SELECT
          EmployeeCode,
          EmployeeName,
          JobTitle,
          Department,
          CurrentStatus
        FROM Employees
        WHERE EmployeeCode = @employeeCode
      `);

    if (result.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'كود الموظف غير موجود'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: result.recordset[0]
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب بيانات الموظف: ' + error.message
    }, { status: 500 });
  }
}

// فحص تضارب الإجازات
async function checkLeaveConflicts(pool, data) {
  try {
    const { employeeCode, startDate, endDate, excludeId } = data;

    let query = `
      SELECT
        ID,
        LeaveType,
        StartDate,
        EndDate,
        Status
      FROM LeaveRequests
      WHERE EmployeeCode = @employeeCode
      AND Status IN (N'قيد المراجعة', N'معتمد')
      AND (
        (StartDate <= @endDate AND EndDate >= @startDate)
      )
    `;

    const request = pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .input('startDate', sql.Date, startDate)
      .input('endDate', sql.Date, endDate);

    if (excludeId) {
      query += ' AND ID != @excludeId';
      request.input('excludeId', sql.Int, excludeId);
    }

    const result = await request.query(query);

    const hasConflict = result.recordset.length > 0;

    return {
      success: true,
      hasConflict: hasConflict,
      conflicts: result.recordset
    };

  } catch (error) {

    return {
      success: false,
      error: 'خطأ في فحص تضارب الإجازات: ' + error.message
    };
  }
}

// جلب الإجازات المعتمدة
async function getApprovedLeaves(pool, data) {
  try {
    const { employeeCode, dateFrom, dateTo } = data;

    let query = `
      SELECT
        lr.ID,
        lr.EmployeeCode,
        lr.EmployeeName,
        ISNULL(lr.JobTitle, '') as JobTitle,
        ISNULL(lr.Department, '') as Department,
        lr.LeaveType,
        lr.StartDate,
        lr.EndDate,
        lr.DaysCount,
        lr.Reason
      FROM LeaveRequests lr
      WHERE lr.Status = N'معتمد'
    `;

    const request = pool.request();
    const conditions = [];

    if (employeeCode) {
      conditions.push('lr.EmployeeCode = @employeeCode');
      request.input('employeeCode', sql.NVarChar, employeeCode);
    }

    if (dateFrom) {
      conditions.push('lr.StartDate >= @dateFrom');
      request.input('dateFrom', sql.Date, dateFrom);
    }

    if (dateTo) {
      conditions.push('lr.EndDate <= @dateTo');
      request.input('dateTo', sql.Date, dateTo);
    }

    if (conditions.length > 0) {
      query += ' AND ' + conditions.join(' AND ');
    }

    query += ' ORDER BY lr.StartDate DESC';

    const result = await request.query(query);

    return NextResponse.json({
      success: true,
      data: result.recordset,
      totalCount: result.recordset.length
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب الإجازات المعتمدة: ' + error.message
    }, { status: 500 });
  }
}

// خصم من رصيد الإجازات
async function deductFromLeaveBalance(pool, data) {
  try {
    const { employeeCode, leaveType, daysCount } = data;

    let updateField = '';
    if (leaveType === 'إعتيادية' || leaveType === 'اعتيادية' || leaveType === 'annual') {
      updateField = 'UsedAnnual = UsedAnnual + @daysCount';
    } else if (leaveType === 'عارضة' || leaveType === 'casual') {
      updateField = 'UsedCasual = UsedCasual + @daysCount';
    } else {
      // للإجازات الأخرى (مرضية، أمومة، إلخ) لا نخصم من الرصيد
      return;
    }

    await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .input('daysCount', sql.Int, daysCount)
      .query(`
        UPDATE LeaveBalances
        SET ${updateField}, UpdatedAt = GETDATE()
        WHERE EmployeeCode = @employeeCode
        AND Year = YEAR(GETDATE())
      `);

  } catch (error) {

    throw error;
  }
}

// إرجاع رصيد الإجازات
async function restoreLeaveBalance(pool, data) {
  try {
    const { employeeCode, leaveType, daysCount } = data;

    let updateField = '';
    if (leaveType === 'إعتيادية' || leaveType === 'اعتيادية' || leaveType === 'annual') {
      updateField = 'UsedAnnual = CASE WHEN UsedAnnual >= @daysCount THEN UsedAnnual - @daysCount ELSE 0 END';
    } else if (leaveType === 'عارضة' || leaveType === 'casual') {
      updateField = 'UsedCasual = CASE WHEN UsedCasual >= @daysCount THEN UsedCasual - @daysCount ELSE 0 END';
    } else {
      // للإجازات الأخرى لا نسترد الرصيد
      return;
    }

    await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .input('daysCount', sql.Int, daysCount)
      .query(`
        UPDATE LeaveBalances
        SET ${updateField}
        WHERE EmployeeCode = @employeeCode
        AND Year = YEAR(GETDATE())
      `);

  } catch (error) {

    throw error;
  }
}

// إرسال إشعار الإجازة
async function sendLeaveNotification(pool, data) {
  try {
    const { requestId, employeeCode, employeeName, leaveType, startDate, daysCount } = data;

    // تحديد نوع الإجازة بالعربية
    let leaveTypeArabic = leaveType;
    switch (leaveType) {
      case 'annual':
      case 'اعتيادية':
        leaveTypeArabic = 'إعتيادية';
        break;
      case 'casual':
      case 'عارضة':
        leaveTypeArabic = 'عارضة';
        break;
      case 'sick':
      case 'مرضية':
        leaveTypeArabic = 'مرضية';
        break;
      case 'unpaid':
      case 'بدون أجر':
        leaveTypeArabic = 'بدون أجر';
        break;
    }

    // تحديد مسئول النظام بناءً على كود المستخدم
    const userInfo = { code: '1450' }; // افتراضي
    let systemAdmin = 'سامي منير';
    if (userInfo.code === '5567') {
      systemAdmin = 'إسلام فايز';
    }

    const notificationMessage = `تم تقديم إجازة ${leaveTypeArabic} ل(${employeeName}) (${daysCount} أيام) تبدأ من (${startDate}) بواسطة (${systemAdmin})`;

    // يمكن إضافة منطق إرسال الإشعار هنا (إيميل، SMS، إلخ)

  } catch (error) {

    // لا نرمي خطأ هنا لأن الإشعار ليس أساسياً
  }
}
