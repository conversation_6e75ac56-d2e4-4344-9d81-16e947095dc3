'use client';
import React from 'react';

function MainComponent() {
  const [selectedLang, setSelectedLang] = useState('ar');
  const [employees, setEmployees] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { data: user, loading: authLoading } = useUser();
  const router = useRouter();

  const dir = selectedLang === 'ar' ? 'rtl' : 'ltr';

  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/account/signin?callbackUrl=/employee-archive');
      return;
    }

    const fetchEmployees = async () => {
      try {
        const response = await fetch('/api/employee-data-handler', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            action: 'list',
            filters: {
              isArchived: true,
            },
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to fetch employees');
        }

        const data = await response.json();
        setEmployees(data.employees || []);
      } catch (err) {

        setError(
          selectedLang === 'ar'
            ? 'حدث خطأ في تحميل بيانات الموظفين'
            : 'Error loading employee data'
        );
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchEmployees();
    }
  }, [user, authLoading]);

  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <i className="fas fa-spinner fa-spin text-3xl text-blue-600"></i>
          <p className="mt-2 text-gray-600">
            {selectedLang === 'ar' ? 'جاري التحميل...' : 'Loading...'}
          </p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Router will handle redirect
  }

  return (
    <div
      dir={dir}
      className="min-h-screen bg-gray-100 dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center">
            <i className="fas fa-archive mx-2"></i>
            {selectedLang === 'ar' ? 'أرشيف الموظفين' : 'Employee Archive'}
          </h1>
          <button
            onClick={() => setSelectedLang(selectedLang === 'ar' ? 'en' : 'ar')}
            className="px-4 py-2 text-sm bg-white dark:bg-gray-800 text-gray-700 dark:text-white border border-gray-200 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            {selectedLang === 'ar' ? 'English' : 'العربية'}
          </button>
        </div>

        {error && (
          <div className="mb-4 p-4 bg-red-50 border-l-4 border-red-500 text-red-700">
            <div className="flex items-center">
              <i className="fas fa-exclamation-circle mx-2"></i>
              {error}
            </div>
          </div>
        )}

        {loading ? (
          <div className="text-center py-8">
            <i className="fas fa-spinner fa-spin text-3xl text-blue-600"></i>
            <p className="mt-2 text-gray-600">
              {selectedLang === 'ar'
                ? 'جاري تحميل البيانات...'
                : 'Loading data...'}
            </p>
          </div>
        ) : employees.length === 0 ? (
          <div className="text-center py-8 bg-white dark:bg-gray-800 rounded-lg shadow">
            <i className="fas fa-folder-open text-4xl text-gray-400"></i>
            <p className="mt-2 text-gray-600 dark:text-gray-400">
              {selectedLang === 'ar'
                ? 'لا يوجد موظفين في الأرشيف'
                : 'No archived employees found'}
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {employees.map((employee) => (
              <div
                key={employee.id}
                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow"
              >
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                    <i className="fas fa-user text-blue-600 dark:text-blue-300"></i>
                  </div>
                  <div className={`${selectedLang === 'ar' ? 'mr-4' : 'ml-4'}`}>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {employee.employee_name}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {employee.employee_id}
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                    <i className="fas fa-briefcase w-5"></i>
                    <span>{employee.job_title}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                    <i className="fas fa-building w-5"></i>
                    <span>{employee.department}</span>
                  </div>
                  <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                    <i className="fas fa-calendar w-5"></i>
                    <span>
                      {new Date(employee.end_date).toLocaleDateString(
                        selectedLang === 'ar' ? 'ar-SA' : 'en-US'
                      )}
                    </span>
                  </div>
                </div>

                <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                  <button
                    onClick={() =>
                      router.push(`/employees/${employee.employee_id}`)
                    }
                    className="w-full text-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    {selectedLang === 'ar' ? 'عرض التفاصيل' : 'View Details'}
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

export default MainComponent;
