'use client';

import React, { useState } from 'react';

function MainComponent() {
  const [selectedLang, setSelectedLang] = useState('ar');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState(null);
  const [error, setError] = useState(null);
  const dir = selectedLang === 'ar' ? 'rtl' : 'ltr';

  const handleRefresh = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/enhanced-file-handler-1', {
        method: 'POST',
        body: JSON.stringify({
          action: 'download',
          fileType: 'monthly_attendance',
          format: 'csv',
        }),
      });
      if (!response.ok) {
        throw new Error('Failed to refresh data');
      }
      setMessage(
        selectedLang === 'ar'
          ? 'تم تحديث البيانات بنجاح'
          : 'Data refreshed successfully'
      );
    } catch (err) {
      setError(
        selectedLang === 'ar' ? 'فشل تحديث البيانات' : 'Failed to refresh data'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleBackup = async () => {
    try {
      const response = await fetch('/api/enhanced-file-handler-1', {
        method: 'POST',
        body: JSON.stringify({
          action: 'download',
          fileType: 'monthly_attendance',
          format: 'csv',
        }),
      });
      const result = await response.json();
      if (result.success) {
        setMessage(
          selectedLang === 'ar'
            ? 'تم إنشاء نسخة احتياطية بنجاح'
            : 'Backup created successfully'
        );
      } else {
        throw new Error('Failed to create backup');
      }
    } catch (err) {
      setError(
        selectedLang === 'ar'
          ? 'فشل إنشاء النسخة الاحتياطية'
          : 'Failed to create backup'
      );
    }
  };

  const handleExport = async () => {
    try {
      const response = await fetch('/api/enhanced-file-handler-1', {
        method: 'POST',
        body: JSON.stringify({
          action: 'export',
          fileType: 'monthly_attendance',
          format: 'xlsx',
        }),
      });
      if (!response.ok) {
        throw new Error('Failed to export data');
      }
      const result = await response.json();
      const a = document.createElement('a');
      a.href = `data:${result.contentType};base64,${btoa(
        JSON.stringify(result.data)
      )}`;
      a.download = result.filename;
      a.click();
    } catch (err) {
      setError(
        selectedLang === 'ar' ? 'فشل تصدير البيانات' : 'Failed to export data'
      );
    }
  };

  const handleUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      const reader = new FileReader();
      reader.onload = async (e) => {
        const content = e.target.result;
        const response = await fetch('/api/enhanced-file-handler-1', {
          method: 'POST',
          body: JSON.stringify({
            action: 'upload',
            fileType: 'monthly_attendance',
            fileContent: content,
          }),
        });
        if (!response.ok) {
          throw new Error('Failed to upload file');
        }
        setMessage(
          selectedLang === 'ar'
            ? 'تم رفع الملف بنجاح'
            : 'File uploaded successfully'
        );
      };
      reader.readAsText(file);
    } catch (err) {
      setError(
        selectedLang === 'ar' ? 'فشل رفع الملف' : 'Failed to upload file'
      );
    }
  };

  return (
    <div
      dir={dir}
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="flex justify-between items-center mb-6">
        <></>
        <button
          onClick={() => setSelectedLang(selectedLang === 'ar' ? 'en' : 'ar')}
          className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
        >
          {selectedLang === 'ar' ? 'English' : 'العربية'}
        </button>
      </div>

      <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
        {selectedLang === 'ar' ? 'إدارة الموظفين' : 'Employee Management'}
      </h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <a
          href="/add-employee"
          className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-lg transition-shadow"
        >
          <div className="text-xl font-semibold mb-4">
            {selectedLang === 'ar' ? 'إضافة موظف' : 'Add Employee'}
          </div>
          <p className="text-gray-600 dark:text-gray-300">
            {selectedLang === 'ar'
              ? 'إضافة موظف جديد وإدخال بياناته'
              : 'Add a new employee and enter their data'}
          </p>
        </a>

        <a
          href="/employee-search"
          className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-lg transition-shadow"
        >
          <div className="text-xl font-semibold mb-4">
            {selectedLang === 'ar' ? 'بحث عن موظف' : 'Search Employee'}
          </div>
          <p className="text-gray-600 dark:text-gray-300">
            {selectedLang === 'ar'
              ? 'البحث عن موظف وعرض بياناته'
              : 'Search for an employee and view their data'}
          </p>
        </a>

        <a
          href="/employee-reports"
          className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-lg transition-shadow"
        >
          <div className="text-xl font-semibold mb-4">
            {selectedLang === 'ar' ? 'تقارير الموظفين' : 'Employee Reports'}
          </div>
          <p className="text-gray-600 dark:text-gray-300">
            {selectedLang === 'ar'
              ? 'عرض وتحميل تقارير الموظفين'
              : 'View and download employee reports'}
          </p>
        </a>

        <a
          href="/employee-archive"
          className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-lg transition-shadow"
        >
          <div className="text-xl font-semibold mb-4">
            {selectedLang === 'ar' ? 'أرشيف الموظفين' : 'Employee Archive'}
          </div>
          <p className="text-gray-600 dark:text-gray-300">
            {selectedLang === 'ar'
              ? 'الوصول إلى سجلات الموظفين السابقين'
              : 'Access former employee records'}
          </p>
        </a>

        <a
          href="/employee-data"
          className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow hover:shadow-lg transition-shadow"
        >
          <div className="text-xl font-semibold mb-4">
            {selectedLang === 'ar' ? 'بيانات الموظفين' : 'Employee Data'}
          </div>
          <p className="text-gray-600 dark:text-gray-300">
            {selectedLang === 'ar'
              ? 'إدارة وتحديث بيانات الموظفين'
              : 'Manage and update employee data'}
          </p>
        </a>
      </div>

      {message && (
        <div className="mt-4 p-4 bg-green-50 text-green-700 rounded-md">
          {message}
        </div>
      )}

      {error && (
        <div className="mt-4 p-4 bg-red-50 text-red-700 rounded-md">
          {error}
        </div>
      )}
    </div>
  );
}

export default MainComponent;
