import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function POST(request) {
  try {
    const pool = await getConnection();

    // فحص الجداول الأساسية (بناءً على الجداول الموجودة فعلياً)
    const requiredTables = [
      'AlertTypes',
      'ApartmentBeneficiaries',
      'Apartments',
      'ARCHIV',
      'Archive',
      'BiometricData',
      'CarBeneficiaries',
      'Cars',
      'carscost',
      'Costs',
      'DailyAttendance',
      'Department',
      'Employees',
      'EmployeeResignations',
      'EmployeeTransfers',
      'Governorates',
      'LeaveBalances',
      'LeaveRequests',
      'Login',
      'MonthlyAttendanceSheet',
      'MonthlyCostDetails',
      'MonthlyCosts',
      'Notifications',
      'ResignationArchive',
      'SimpleLeaves',
      'SystemAlerts',
      'TempWorkers',
      'TransferArchive',
      'UndirectedLeaves',
      'UserActions',
      'UserSessions'
    ];

    const tableStatus = {};
    const tableDetails = {};

    for (const tableName of requiredTables) {
      try {
        // فحص وجود الجدول
        const tableCheck = await pool.request().query(`
          SELECT COUNT(*) as tableExists
          FROM INFORMATION_SCHEMA.TABLES 
          WHERE TABLE_NAME = '${tableName}'
        `);

        const exists = tableCheck.recordset[0].tableExists > 0;
        tableStatus[tableName] = { exists };

        if (exists) {
          // جلب عدد السجلات
          const countResult = await pool.request().query(`
            SELECT COUNT(*) as recordCount FROM [${tableName}]
          `);
          
          // جلب هيكل الجدول
          const structureResult = await pool.request().query(`
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, CHARACTER_MAXIMUM_LENGTH
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = '${tableName}'
            ORDER BY ORDINAL_POSITION
          `);

          tableDetails[tableName] = {
            recordCount: countResult.recordset[0].recordCount,
            structure: structureResult.recordset,
            status: 'healthy'
          };
        } else {
          tableDetails[tableName] = {
            recordCount: 0,
            structure: [],
            status: 'missing'
          };
        }
      } catch (error) {

        tableStatus[tableName] = { exists: false, error: error.message };
        tableDetails[tableName] = {
          recordCount: 0,
          structure: [],
          status: 'error',
          error: error.message
        };
      }
    }

    // فحص الاتصالات والأداء
    const performanceTests = [];

    // اختبار سرعة الاستعلام
    const startTime = Date.now();
    try {
      await pool.request().query('SELECT 1 as test');
      const queryTime = Date.now() - startTime;
      performanceTests.push({
        test: 'query_speed',
        result: 'success',
        time: queryTime,
        status: queryTime < 1000 ? 'excellent' : queryTime < 3000 ? 'good' : 'slow'
      });
    } catch (error) {
      performanceTests.push({
        test: 'query_speed',
        result: 'failed',
        error: error.message,
        status: 'error'
      });
    }

    // فحص مساحة قاعدة البيانات
    try {
      const spaceResult = await pool.request().query(`
        SELECT 
          DB_NAME() as database_name,
          SUM(size * 8.0 / 1024) as size_mb
        FROM sys.database_files
      `);
      
      performanceTests.push({
        test: 'database_size',
        result: 'success',
        size_mb: spaceResult.recordset[0].size_mb,
        status: 'healthy'
      });
    } catch (error) {
      performanceTests.push({
        test: 'database_size',
        result: 'failed',
        error: error.message,
        status: 'error'
      });
    }

    // إحصائيات عامة
    const totalTables = requiredTables.length;
    const existingTables = Object.values(tableStatus).filter(t => t.exists).length;
    const missingTables = totalTables - existingTables;
    const totalRecords = Object.values(tableDetails).reduce((sum, table) => sum + (table.recordCount || 0), 0);

    const systemHealth = {
      overall_status: missingTables === 0 ? 'healthy' : missingTables < 5 ? 'warning' : 'critical',
      tables: {
        total: totalTables,
        existing: existingTables,
        missing: missingTables,
        percentage: Math.round((existingTables / totalTables) * 100)
      },
      data: {
        total_records: totalRecords,
        tables_with_data: Object.values(tableDetails).filter(t => t.recordCount > 0).length
      },
      performance: performanceTests,
      table_status: tableStatus,
      table_details: tableDetails,
      last_check: new Date().toISOString()
    };

    // تقييم الصحة العامة
    let healthScore = 0;
    healthScore += (existingTables / totalTables) * 60; // 60% للجداول الموجودة
    healthScore += (totalRecords > 0 ? 20 : 0); // 20% للبيانات الموجودة
    healthScore += (performanceTests.filter(t => t.result === 'success').length / performanceTests.length) * 20; // 20% للأداء

    systemHealth.health_score = Math.round(healthScore);
    systemHealth.health_grade = healthScore >= 90 ? 'A' : healthScore >= 80 ? 'B' : healthScore >= 70 ? 'C' : healthScore >= 60 ? 'D' : 'F';

    return NextResponse.json({
      success: true,
      data: systemHealth
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في فحص صحة النظام: ' + error.message
    }, { status: 500 });
  }
}

// فحص سريع للنظام
export async function GET() {
  try {
    const pool = await getConnection();

    // فحص سريع للجداول الأساسية
    const quickChecks = [
      { table: 'employee_data', description: 'بيانات الموظفين' },
      { table: 'Departments', description: 'الأقسام' },
      { table: 'UserActions', description: 'سجل الأنشطة' }
    ];

    const results = [];

    for (const check of quickChecks) {
      try {
        const tableCheck = await pool.request().query(`
          SELECT COUNT(*) as tableExists
          FROM INFORMATION_SCHEMA.TABLES 
          WHERE TABLE_NAME = '${check.table}'
        `);

        const exists = tableCheck.recordset[0].tableExists > 0;
        let recordCount = 0;

        if (exists) {
          const countResult = await pool.request().query(`
            SELECT COUNT(*) as recordCount FROM [${check.table}]
          `);
          recordCount = countResult.recordset[0].recordCount;
        }

        results.push({
          table: check.table,
          description: check.description,
          exists: exists,
          recordCount: recordCount,
          status: exists ? (recordCount > 0 ? 'healthy' : 'empty') : 'missing'
        });

      } catch (error) {
        results.push({
          table: check.table,
          description: check.description,
          exists: false,
          recordCount: 0,
          status: 'error',
          error: error.message
        });
      }
    }

    const healthyTables = results.filter(r => r.status === 'healthy').length;
    const totalTables = results.length;
    const overallHealth = (healthyTables / totalTables) * 100;

    return NextResponse.json({
      success: true,
      data: {
        overall_health: Math.round(overallHealth),
        status: overallHealth >= 80 ? 'healthy' : overallHealth >= 60 ? 'warning' : 'critical',
        checks: results,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في الفحص السريع: ' + error.message
    }, { status: 500 });
  }
}
