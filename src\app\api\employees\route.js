import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function GET(req) {
  try {
    const { searchParams } = new URL(req.url);
    const action = searchParams.get('action');
    const searchTerm = searchParams.get('searchTerm') || '';

    if (action === 'list-with-balances') {
      return await getEmployeesWithBalances(searchTerm);
    }

    return NextResponse.json({
      success: false,
      error: 'Invalid action'
    }, { status: 400 });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'Server error'
    }, { status: 500 });
  }
}

// جلب الموظفين مع رصيد الإجازات
async function getEmployeesWithBalances(searchTerm) {
  try {

    const pool = await getConnection();

    let whereClause = '';
    const request = pool.request();

    if (searchTerm) {
      whereClause = `WHERE (e.EmployeeCode LIKE @searchTerm OR e.EmployeeName LIKE @searchTerm)`;
      request.input('searchTerm', sql.NVarChar, `%${searchTerm}%`);

    }

    const query = `
      SELECT
        e.EmployeeCode,
        e.EmployeeName,
        e.JobTitle,
        e.Department,
        ISNULL(lb.AnnualBalance, 15) as AnnualBalance,
        ISNULL(lb.CasualBalance, 6) as CasualBalance
      FROM Employees e
      LEFT JOIN LeaveBalances lb ON e.EmployeeCode = lb.EmployeeCode
      ${whereClause}
      ORDER BY CAST(e.EmployeeCode AS INT) ASC
    `;

    const result = await request.query(query);

    return NextResponse.json({
      success: true,
      employees: result.recordset
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب بيانات الموظفين'
    }, { status: 500 });
  }
}

// دالة لتوحيد بيانات الموظف
function normalizeEmployeeData(employee) {
  return {
    ...employee,
    // التسميات الموحدة الجديدة
    employeeCode: employee.EmployeeCode,
    employeeName: employee.EmployeeName,

    // الحفاظ على التسميات القديمة للتوافق
    EmployeeCode: employee.EmployeeCode,
    EmployeeName: employee.EmployeeName,

    // إضافة displayText للواجهات
    displayText: `${employee.EmployeeName} (${employee.EmployeeCode})`
  };
}

export async function POST(req) {
  let pool = null;
  let dbRequest = null;
  
  try {

    const body = await req.json();
    const { searchType, searchValue } = body;

    // التحقق من القيم المدخلة
    if (!searchValue || typeof searchValue !== 'string' || searchValue.trim() === '') {
      return NextResponse.json({ 
        success: false, 
        error: 'يرجى إدخال قيمة صحيحة للبحث' 
      });
    }

    // تنظيف وتجهيز قيمة البحث
    const cleanedSearchValue = searchValue.trim();

    // إنشاء اتصال بقاعدة البيانات
    pool = await getConnection();

    // إنشاء طلب قاعدة البيانات
    dbRequest = pool.request();
    
    let query;
    if (searchType === 'name') {
      query = `
        SELECT 
          EmployeeCode,
          EmployeeName,
          DepartmentID,
          JobTitleID,
          IsActive,
          Mobile,
          Email
        FROM Employees WITH(NOLOCK)
        WHERE EmployeeName LIKE @searchValue
          AND IsActive = 1
      `;
      dbRequest.input('searchValue', sql.NVarChar, `%${cleanedSearchValue}%`);
    } else if (searchType === 'id') {
      query = `
        SELECT 
          EmployeeCode,
          EmployeeName,
          DepartmentID,
          JobTitleID,
          IsActive,
          Mobile,
          Email
        FROM Employees WITH(NOLOCK)
        WHERE EmployeeCode = @searchValue
          AND IsActive = 1
      `;
      dbRequest.input('searchValue', sql.NVarChar, cleanedSearchValue);
    } else {
      return NextResponse.json({ 
        success: false, 
        error: 'نوع البحث غير صالح' 
      });
    }

    const result = await dbRequest.query(query);

    if (result.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: searchType === 'id' ? 'رقم الموظف غير صحيح' : 'لم يتم العثور على موظف بهذا الاسم'
      });
    }

    // التحقق من أن الموظف نشط
    const employee = result.recordset[0];
    if (!employee.IsActive) {
      return NextResponse.json({
        success: false,
        error: 'هذا الحساب غير نشط. يرجى مراجعة الإدارة'
      });
    }

    const normalizedEmployee = normalizeEmployeeData(employee);
    return NextResponse.json({
      success: true,
      data: normalizedEmployee
    });

  } catch (error) {

    let errorMessage = 'حدث خطأ أثناء البحث عن الموظف. يرجى المحاولة مرة أخرى';
    if (error.code === 'ETIMEDOUT') {
      errorMessage = 'انتهت مهلة الاتصال بقاعدة البيانات. يرجى المحاولة مرة أخرى';
    } else if (error.code === 'ECONNREFUSED') {
      errorMessage = 'تعذر الاتصال بقاعدة البيانات. يرجى التحقق من اتصال الشبكة';
    }
    
    return NextResponse.json({ 
      success: false, 
      error: errorMessage
    }, { status: 500 });
  } finally {
    // Connection pool is managed globally, no need to close here

  }
}
