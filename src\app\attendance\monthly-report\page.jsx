'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import { useTheme } from '@/contexts/ThemeContext';
import { Calendar, Users, Download, Printer, Edit3, Save, RefreshCw, BarChart3, FileText, Upload } from 'lucide-react';

export default function MonthlyAttendanceReport() {
  const { isDarkMode } = useTheme();
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [employees, setEmployees] = useState([]);
  const [attendanceData, setAttendanceData] = useState({});
  const [loading, setLoading] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [statistics, setStatistics] = useState({});
  const [searchTerm, setSearchTerm] = useState('');

  // رموز الحضور مع الألوان المحسنة
  const attendanceCodes = {
    'W': {
      ar: 'حضور',
      en: 'Work',
      color: 'bg-white text-black border border-gray-400',
      darkColor: 'bg-white text-black border border-gray-400',
      description: 'حضور'
    },
    'Ab': {
      ar: 'غياب',
      en: 'Absent',
      color: 'bg-red-500 text-white',
      darkColor: 'bg-red-600 text-white',
      description: 'غياب'
    },
    'S': {
      ar: 'إجازة مرضية',
      en: 'Sick Leave',
      color: 'bg-green-500 text-white',
      darkColor: 'bg-green-600 text-white',
      description: 'إجازة مرضية'
    },
    'R': {
      ar: 'راحة',
      en: 'Rest',
      color: 'bg-yellow-300 text-black',
      darkColor: 'bg-yellow-400 text-black',
      description: 'راحة'
    },
    'NH': {
      ar: 'إجازة رسمية',
      en: 'National Holiday',
      color: 'bg-purple-600 text-white',
      darkColor: 'bg-purple-600 text-white',
      description: 'إجازة رسمية'
    },
    'CR': {
      ar: 'إجازة بدل',
      en: 'Compensatory Rest',
      color: 'bg-purple-300 text-black',
      darkColor: 'bg-purple-400 text-white',
      description: 'إجازة بدل'
    },
    'M': {
      ar: 'مأمورية',
      en: 'Mission',
      color: 'bg-yellow-400 text-black',
      darkColor: 'bg-yellow-500 text-black',
      description: 'مأمورية'
    },
    'AL': {
      ar: 'إجازة اعتيادية',
      en: 'Annual Leave',
      color: 'bg-blue-500 text-white',
      darkColor: 'bg-blue-600 text-white',
      description: 'إجازة اعتيادية'
    },
    'CL': {
      ar: 'إجازة عارضة',
      en: 'Casual Leave',
      color: 'bg-pink-500 text-white',
      darkColor: 'bg-pink-600 text-white',
      description: 'إجازة عارضة'
    },
    'UL': {
      ar: 'إجازة بدون أجر',
      en: 'Unpaid Leave',
      color: 'bg-gray-500 text-white',
      darkColor: 'bg-gray-600 text-white',
      description: 'إجازة بدون أجر'
    },
    'ML': {
      ar: 'إجازة أمومة',
      en: 'Maternity Leave',
      color: 'bg-cyan-500 text-white',
      darkColor: 'bg-cyan-600 text-white',
      description: 'إجازة أمومة'
    }
  };

  const monthNames = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ];

  const dayNames = ['أحد', 'اثنين', 'ثلاثاء', 'أربعاء', 'خميس', 'جمعة', 'سبت'];

  // حساب عدد الأيام في الفترة (من 11 إلى 10)
  const daysInPeriod = (() => {
    if (selectedMonth === 1) {
      // يناير: من 11 ديسمبر إلى 10 يناير
      const daysInDecember = 31 - 10; // 21 يوم
      const daysInJanuary = 10; // 10 أيام
      return daysInDecember + daysInJanuary; // 31 يوم
    } else {
      // باقي الشهور: من 11 الشهر السابق إلى 10 الشهر الحالي
      const daysInPrevMonth = new Date(selectedYear, selectedMonth - 1, 0).getDate();
      const daysFromPrevMonth = daysInPrevMonth - 10; // من 11 إلى آخر الشهر
      const daysFromCurrentMonth = 10; // من 1 إلى 10
      return daysFromPrevMonth + daysFromCurrentMonth;
    }
  })();

  // نص فترة التقرير
  const getPeriodText = (month, year) => {
    if (month === 1) {
      return `من 11 / 12 / ${year - 1}م حتى 10 / 1 / ${year}م`;
    } else {
      return `من 11 / ${month - 1} / ${year}م حتى 10 / ${month} / ${year}م`;
    }
  };

  // حساب اسم اليوم
  const getDayName = (dayIndex, month, year) => {
    let actualDate;
    if (month === 1) {
      if (dayIndex < 21) {
        actualDate = new Date(year - 1, 11, 11 + dayIndex); // ديسمبر السنة السابقة
      } else {
        actualDate = new Date(year, 0, dayIndex - 20); // يناير
      }
    } else {
      const daysInPrevMonth = new Date(year, month - 1, 0).getDate();
      if (dayIndex < (daysInPrevMonth - 10)) {
        actualDate = new Date(year, month - 2, 11 + dayIndex); // الشهر السابق
      } else {
        actualDate = new Date(year, month - 1, dayIndex - (daysInPrevMonth - 11)); // الشهر الحالي
      }
    }
    return dayNames[actualDate.getDay()];
  };

  // جلب بيانات الموظفين والحضور
  const loadData = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/monthly-attendance-sheet', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'get-data',
          month: selectedMonth,
          year: selectedYear
        })
      });

      const result = await response.json();
      if (result.success) {
        setEmployees(result.employees || []);
        setAttendanceData(result.attendanceData || {});
        setStatistics(result.statistics || {});
      } else {

      }
    } catch (error) {

    }
    setLoading(false);
  };

  // تحديث بيانات الحضور
  const updateAttendance = async (employeeCode, day, code) => {
    try {
      const response = await fetch('/api/monthly-attendance-sheet', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'update-attendance',
          employeeCode,
          day,
          code,
          month: selectedMonth,
          year: selectedYear
        })
      });

      const result = await response.json();
      if (result.success) {
        setAttendanceData(prev => ({
          ...prev,
          [employeeCode]: {
            ...prev[employeeCode],
            [day]: code
          }
        }));
      }
    } catch (error) {

    }
  };

  // حفظ البيانات
  const saveData = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/monthly-attendance-sheet', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'save-data',
          attendanceData,
          month: selectedMonth,
          year: selectedYear
        })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم حفظ البيانات بنجاح');
        setEditMode(false);
        await loadData();
      } else {
        alert('خطأ في حفظ البيانات: ' + result.error);
      }
    } catch (error) {

      alert('حدث خطأ أثناء حفظ البيانات');
    }
    setLoading(false);
  };

  // إعادة حساب الإحصائيات
  const recalculateStats = async () => {
    if (!confirm(`هل تريد إعادة حساب إحصائيات التمام لشهر ${monthNames[selectedMonth - 1]} ${selectedYear}؟`)) {
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/monthly-attendance-sheet', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'recalculate-stats',
          month: selectedMonth,
          year: selectedYear
        })
      });

      const result = await response.json();
      if (result.success) {
        alert(`تم إعادة حساب الإحصائيات لـ ${result.employeesCount} موظف`);
        await loadData();
      } else {
        alert('خطأ في إعادة الحساب: ' + result.error);
      }
    } catch (error) {

      alert('حدث خطأ أثناء إعادة الحساب');
    } finally {
      setLoading(false);
    }
  };

  // تصدير إلى Excel
  const exportToExcel = async () => {
    try {
      const response = await fetch('/api/monthly-attendance-sheet', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'export-excel',
          month: selectedMonth,
          year: selectedYear,
          employees,
          attendanceData
        })
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `كشف_التمام_الشهري_${monthNames[selectedMonth - 1]}_${selectedYear}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {

      alert('حدث خطأ أثناء تصدير الملف');
    }
  };

  // طباعة التقرير بتنسيق مخصص
  const printReport = () => {
    const printWindow = window.open('', '_blank');
    const printContent = generatePrintContent();

    printWindow.document.write(printContent);
    printWindow.document.close();

    printWindow.onload = () => {
      setTimeout(() => {
        printWindow.print();
      }, 500);
    };
  };

  // إنشاء محتوى الطباعة
  const generatePrintContent = () => {
    const periodText = getPeriodText(selectedMonth, selectedYear);

    // فلترة الموظفين حسب البحث
    const currentFilteredEmployees = employees.filter(employee =>
      employee.EmployeeName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.EmployeeCode?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.JobTitle?.toLowerCase().includes(searchTerm.toLowerCase())
    );

    return `
      <!DOCTYPE html>
      <html dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>كشف التمام الشهري - ${monthNames[selectedMonth - 1]} ${selectedYear}</title>
        <style>
          @page {
            size: A4 landscape;
            margin: 8mm;
          }
          body {
            font-family: 'Arial', sans-serif;
            font-size: 7px;
            line-height: 1.1;
            color: #000;
            margin: 0;
            padding: 10px;
            direction: rtl;
            background: white;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
          }
          .header {
            text-align: center;
            border: 2px solid #000;
            padding: 10px;
            margin: 0 auto 15px auto;
            max-width: 100%;
          }
          .company-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
          }
          .report-title {
            font-size: 14px;
            font-weight: bold;
            margin: 5px 0;
          }
          .period {
            font-size: 12px;
            margin: 5px 0;
          }
          table {
            width: 90%;
            max-width: 100%;
            border-collapse: collapse;
            margin: 0 auto 20px auto;
            border: 2px solid #000;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
          }
          th, td {
            border: 1px solid #000;
            padding: 2px 3px;
            text-align: center;
            font-size: 7px;
            line-height: 1.1;
            vertical-align: middle;
          }
          th {
            background-color: #f0f0f0;
            font-weight: bold;
          }
          .employee-info {
            text-align: right;
            font-size: 7px;
            line-height: 1.2;
            padding: 1px 2px;
          }
          .day-header {
            writing-mode: vertical-rl;
            text-orientation: mixed;
            width: 15px;
            font-size: 6px;
            padding: 1px;
          }
          .attendance-cell {
            width: 15px;
            height: 18px;
            font-size: 6px;
            font-weight: bold;
            padding: 1px;
          }
          .stats-cell {
            font-size: 6px;
            padding: 1px;
            width: 15px;
          }
          .signatures {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
            text-align: center;
          }
          .signature-box {
            width: 30%;
            border-top: 1px solid #000;
            padding-top: 10px;
            font-size: 10px;
          }
          .W { background-color: #ffffff; color: #000; }
          .Ab { background-color: #ef4444; color: #fff; }
          .S { background-color: #22c55e; color: #fff; }
          .R { background-color: #fbbf24; color: #000; }
          .NH { background-color: #8b5cf6; color: #fff; }
          .M { background-color: #f59e0b; color: #000; }
          .AL { background-color: #3b82f6; color: #fff; }
          .CL { background-color: #ec4899; color: #fff; }
          .UL { background-color: #6b7280; color: #fff; }
          .ML { background-color: #06b6d4; color: #fff; }
          .CR { background-color: #a855f7; color: #fff; }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="company-name">حضور العمالة المتعاقدة بمشروع أوجيستا</div>
          <div class="report-title">كشف التمام الشهري</div>
          <div class="period">${periodText}</div>
        </div>

        <table>
          <thead>
            <tr>
              <th rowspan="2" style="width: 20px;">م</th>
              <th rowspan="2" style="width: 40px;">كود الموظف</th>
              <th rowspan="2" style="width: 80px;">اسم الموظف</th>
              <th rowspan="2" style="width: 60px;">المسمى الوظيفي</th>
              <th colspan="${daysInPeriod}">الأيام</th>
              <th colspan="11">الإحصائيات</th>
            </tr>
            <tr>
              ${Array.from({ length: daysInPeriod }, (_, i) => {
                let dayNumber;
                if (selectedMonth === 1) {
                  if (i < 21) {
                    dayNumber = 11 + i;
                  } else {
                    dayNumber = i - 20;
                  }
                } else {
                  const daysInPrevMonth = new Date(selectedYear, selectedMonth - 1, 0).getDate();
                  if (i < (daysInPrevMonth - 10)) {
                    dayNumber = 11 + i;
                  } else {
                    dayNumber = i - (daysInPrevMonth - 11);
                  }
                }
                return `<th class="day-header">${dayNumber}</th>`;
              }).join('')}
              <th style="background-color: #ffffff; color: #000; border: 1px solid #000; font-size: 7px; width: 15px;">W</th>
              <th style="background-color: #ef4444; color: #fff; font-size: 7px; width: 15px;">Ab</th>
              <th style="background-color: #22c55e; color: #fff; font-size: 7px; width: 15px;">S</th>
              <th style="background-color: #fbbf24; color: #000; font-size: 7px; width: 15px;">R</th>
              <th style="background-color: #8b5cf6; color: #fff; font-size: 7px; width: 15px;">NH</th>
              <th style="background-color: #a855f7; color: #fff; font-size: 7px; width: 15px;">CR</th>
              <th style="background-color: #f59e0b; color: #000; font-size: 7px; width: 15px;">M</th>
              <th style="background-color: #3b82f6; color: #fff; font-size: 7px; width: 15px;">AL</th>
              <th style="background-color: #ec4899; color: #fff; font-size: 7px; width: 15px;">CL</th>
              <th style="background-color: #6b7280; color: #fff; font-size: 7px; width: 15px;">UL</th>
              <th style="background-color: #06b6d4; color: #fff; font-size: 7px; width: 15px;">ML</th>
            </tr>
          </thead>
          <tbody>
            ${currentFilteredEmployees.map((employee, index) => {
              const employeeCode = employee.EmployeeCode || employee.EmployeeID;
              const employeeAttendance = attendanceData[employeeCode] || {};

              // حساب الإحصائيات لهذا الموظف
              const stats = {
                W: 0, Ab: 0, S: 0, R: 0, NH: 0, CR: 0, M: 0, AL: 0, CL: 0, UL: 0, ML: 0
              };

              Object.values(employeeAttendance).forEach(code => {
                if (stats[code] !== undefined) {
                  stats[code]++;
                }
              });

              return `
                <tr>
                  <td>${index + 1}</td>
                  <td>${employeeCode}</td>
                  <td class="employee-info">${employee.EmployeeName || employee.FullName}</td>
                  <td class="employee-info">${employee.JobTitle}</td>
                  ${Array.from({ length: daysInPeriod }, (_, dayIndex) => {
                    const day = dayIndex + 1;
                    const code = employeeAttendance[day] || '';
                    return `<td class="attendance-cell ${code}">${code || '-'}</td>`;
                  }).join('')}
                  <td class="stats-cell W" style="background-color: #ffffff; color: #000; font-weight: bold; border: 1px solid #000;">${stats.W || 0}</td>
                  <td class="stats-cell Ab" style="background-color: #ef4444; color: #fff; font-weight: bold;">${stats.Ab || 0}</td>
                  <td class="stats-cell S" style="background-color: #22c55e; color: #fff; font-weight: bold;">${stats.S || 0}</td>
                  <td class="stats-cell R" style="background-color: #fbbf24; color: #000; font-weight: bold;">${stats.R || 0}</td>
                  <td class="stats-cell NH" style="background-color: #8b5cf6; color: #fff; font-weight: bold;">${stats.NH || 0}</td>
                  <td class="stats-cell CR" style="background-color: #a855f7; color: #fff; font-weight: bold;">${stats.CR || 0}</td>
                  <td class="stats-cell M" style="background-color: #f59e0b; color: #000; font-weight: bold;">${stats.M || 0}</td>
                  <td class="stats-cell AL" style="background-color: #3b82f6; color: #fff; font-weight: bold;">${stats.AL || 0}</td>
                  <td class="stats-cell CL" style="background-color: #ec4899; color: #fff; font-weight: bold;">${stats.CL || 0}</td>
                  <td class="stats-cell UL" style="background-color: #6b7280; color: #fff; font-weight: bold;">${stats.UL || 0}</td>
                  <td class="stats-cell ML" style="background-color: #06b6d4; color: #fff; font-weight: bold;">${stats.ML || 0}</td>
                </tr>
              `;
            }).join('')}

          </tbody>
        </table>

        <div class="signatures">
          <div class="signature-box">
            <div>الشئون الإدارية</div>
            <div style="margin-top: 20px;">........................</div>
          </div>
          <div class="signature-box">
            <div>مدير المنطقة</div>
            <div style="margin-top: 20px;">........................</div>
          </div>
          <div class="signature-box">
            <div>رئيس القطاع</div>
            <div style="margin-top: 20px;">........................</div>
          </div>
        </div>
      </body>
      </html>
    `;
  };

  useEffect(() => {
    loadData();
  }, [selectedMonth, selectedYear]);

  // فلترة الموظفين حسب البحث
  const filteredEmployees = employees.filter(employee =>
    employee.EmployeeName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.EmployeeCode?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    employee.JobTitle?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <MainLayout>
      <div className="max-w-full mx-auto p-6">
        {/* Header */}
        <div className={`${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-800'} rounded-lg shadow-xl p-6 mb-6`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className={`p-3 rounded-lg ${isDarkMode ? 'bg-blue-900' : 'bg-blue-100'}`}>
                <Calendar className="h-8 w-8 text-blue-600" />
              </div>
              <div>
                <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
                  كشف التمام الشهري
                </h1>
                <p className={`${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  تقرير شامل للحضور والغياب الشهري مع إمكانية التحرير والتصدير
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-3">
              {editMode ? (
                <button
                  onClick={saveData}
                  disabled={loading}
                  className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg flex items-center gap-2 text-white transition-colors"
                >
                  <Save className="h-4 w-4" />
                  حفظ التغييرات
                </button>
              ) : (
                <button
                  onClick={() => setEditMode(true)}
                  className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg flex items-center gap-2 text-white transition-colors"
                >
                  <Edit3 className="h-4 w-4" />
                  تحرير
                </button>
              )}

              {editMode && (
                <button
                  onClick={() => setEditMode(false)}
                  className="bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded-lg flex items-center gap-2 text-white transition-colors"
                >
                  إلغاء
                </button>
              )}
              
              <button
                onClick={recalculateStats}
                disabled={loading}
                className="bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg flex items-center gap-2 text-white transition-colors"
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                إعادة حساب
              </button>
              
              <button
                onClick={exportToExcel}
                className="bg-emerald-600 hover:bg-emerald-700 px-4 py-2 rounded-lg flex items-center gap-2 text-white transition-colors"
              >
                <Download className="h-4 w-4" />
                تصدير Excel
              </button>
              
              <button
                onClick={printReport}
                className="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-lg flex items-center gap-2 text-white transition-colors"
              >
                <Printer className="h-4 w-4" />
                طباعة
              </button>
            </div>
          </div>
        </div>

        {/* فلاتر وإعدادات */}
        <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-xl p-6 mb-6`}>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {/* نوع الرسم */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                نوع الرسم
              </label>
              <select
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="table">جدول</option>
                <option value="chart">رسم بياني</option>
              </select>
            </div>

            {/* إلى تاريخ */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                إلى تاريخ
              </label>
              <input
                type="date"
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>

            {/* من تاريخ */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                من تاريخ
              </label>
              <input
                type="date"
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>

            {/* البند الفرعي */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                البند الفرعي
              </label>
              <select
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="">جميع البنود الفرعية</option>
              </select>
            </div>

            {/* البند الرئيسي */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                البند الرئيسي
              </label>
              <select
                value={selectedMonth}
                onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                {monthNames.map((month, index) => (
                  <option key={index + 1} value={index + 1}>{month}</option>
                ))}
              </select>
            </div>
          </div>

          {/* الصف الثاني للفلاتر */}
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mt-4">
            {/* انتقالات */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                انتقالات
              </label>
              <select
                value={selectedYear}
                onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - 2 + i).map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
            </div>

            {/* البحث في الحقول */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                البحث في الحقول
              </label>
              <input
                type="text"
                placeholder="بحث بالاسم أو الكود..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                    : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                }`}
              />
            </div>

            {/* أزرار العمليات */}
            <div className="flex gap-2">
              <button
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors"
              >
                إضافة
              </button>
            </div>

            {/* فترة التقرير */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                فترة التقرير
              </label>
              <div className={`px-3 py-2 rounded-lg text-sm font-medium ${
                isDarkMode ? 'bg-blue-900 text-blue-300' : 'bg-blue-100 text-blue-800'
              }`}>
                {getPeriodText(selectedMonth, selectedYear)}
              </div>
            </div>

            {/* تحديث */}
            <div>
              <button
                onClick={loadData}
                disabled={loading}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors disabled:opacity-50"
              >
                تحديث
              </button>
            </div>
          </div>
        </div>

        {/* إحصائيات عامة */}
        {statistics && Object.keys(statistics).length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-xl p-4`}>
              <div className="flex items-center gap-3">
                <Users className="text-blue-500 text-2xl" />
                <div>
                  <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    إجمالي الموظفين
                  </p>
                  <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {filteredEmployees.length}
                  </p>
                </div>
              </div>
            </div>

            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-xl p-4`}>
              <div className="flex items-center gap-3">
                <BarChart3 className="text-green-500 text-2xl" />
                <div>
                  <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    أيام الفترة
                  </p>
                  <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {daysInPeriod}
                  </p>
                </div>
              </div>
            </div>

            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-xl p-4`}>
              <div className="flex items-center gap-3">
                <Calendar className="text-orange-500 text-2xl" />
                <div>
                  <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    الشهر المحدد
                  </p>
                  <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {monthNames[selectedMonth - 1]}
                  </p>
                </div>
              </div>
            </div>

            <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-xl p-4`}>
              <div className="flex items-center gap-3">
                <FileText className="text-purple-500 text-2xl" />
                <div>
                  <p className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                    السنة
                  </p>
                  <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    {selectedYear}
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* مفتاح الرموز */}
        <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-xl p-6 mb-6`}>
          <h3 className={`text-lg font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            مفتاح رموز الحضور
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
            {Object.entries(attendanceCodes).map(([code, info]) => (
              <div key={code} className="flex items-center gap-2">
                <span className={`px-2 py-1 rounded text-xs font-bold ${
                  isDarkMode ? info.darkColor : info.color
                }`}>
                  {code}
                </span>
                <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  {info.ar}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* جدول كشف التمام الشهري */}
        <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-xl overflow-hidden`}>
          {loading ? (
            <div className="p-8 text-center">
              <div className="flex items-center justify-center gap-2">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>جاري التحميل...</span>
              </div>
            </div>
          ) : filteredEmployees.length === 0 ? (
            <div className="p-8 text-center">
              <Users className={`mx-auto h-12 w-12 mb-4 ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`} />
              <p className={isDarkMode ? 'text-gray-400' : 'text-gray-500'}>
                لا توجد بيانات موظفين للشهر المحدد
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'} border-b-2 ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                  <tr>
                    <th className={`px-2 py-3 text-center font-bold text-xs ${isDarkMode ? 'text-gray-200' : 'text-gray-700'} border-r ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                      م
                    </th>
                    <th className={`px-3 py-3 text-center font-bold text-xs ${isDarkMode ? 'text-gray-200' : 'text-gray-700'} border-r ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                      كود الموظف
                    </th>
                    <th className={`px-4 py-3 text-center font-bold text-xs ${isDarkMode ? 'text-gray-200' : 'text-gray-700'} border-r ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                      اسم الموظف
                    </th>
                    <th className={`px-3 py-3 text-center font-bold text-xs ${isDarkMode ? 'text-gray-200' : 'text-gray-700'} border-r ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                      المسمى الوظيفي
                    </th>

                    {/* أعمدة الأيام */}
                    {Array.from({ length: daysInPeriod }, (_, i) => {
                      // حساب رقم اليوم الصحيح (11-10)
                      let dayNumber;
                      let isCurrentMonth = true;

                      if (selectedMonth === 1) {
                        // يناير: من 11 ديسمبر إلى 10 يناير
                        if (i < 21) {
                          dayNumber = 11 + i; // 11-31 ديسمبر
                          isCurrentMonth = false;
                        } else {
                          dayNumber = i - 20; // 1-10 يناير
                        }
                      } else {
                        // باقي الشهور: من 11 الشهر السابق إلى 10 الشهر الحالي
                        const daysInPrevMonth = new Date(selectedYear, selectedMonth - 1, 0).getDate();
                        if (i < (daysInPrevMonth - 10)) {
                          dayNumber = 11 + i; // من 11 للشهر السابق
                          isCurrentMonth = false;
                        } else {
                          dayNumber = i - (daysInPrevMonth - 11); // للشهر الحالي
                        }
                      }

                      const dayName = getDayName(i, selectedMonth, selectedYear);
                      const isFriday = dayName === 'جمعة';

                      return (
                        <th key={i + 1} className={`px-1 py-2 text-center font-bold text-xs border-r min-w-[35px] max-w-[35px] ${
                          isFriday
                            ? (isDarkMode ? 'text-red-300 bg-red-900 border-gray-600' : 'text-red-700 bg-red-50 border-gray-200')
                            : isCurrentMonth
                              ? (isDarkMode ? 'text-blue-300 bg-blue-900 border-gray-600' : 'text-blue-700 bg-blue-50 border-gray-200')
                              : (isDarkMode ? 'text-gray-400 bg-gray-700 border-gray-600' : 'text-gray-500 bg-gray-100 border-gray-200')
                        }`}>
                          <div className="text-xs font-bold">{dayNumber}</div>
                          <div className="text-xs font-normal mt-0.5" style={{ fontSize: '9px' }}>{dayName}</div>
                        </th>
                      );
                    })}

                  </tr>
                </thead>
                <tbody className={`${isDarkMode ? 'bg-gray-800 divide-gray-700' : 'bg-white divide-gray-200'} divide-y`}>
                  {filteredEmployees.map((employee, index) => {
                    const employeeCode = employee.EmployeeCode || employee.EmployeeID;
                    const employeeAttendance = attendanceData[employeeCode] || {};

                    return (
                      <tr key={employeeCode} className={`border-b ${isDarkMode ? 'border-gray-700 hover:bg-gray-700' : 'border-gray-200 hover:bg-gray-50'} transition-colors`}>
                        <td className={`px-2 py-2 text-center font-bold text-sm ${isDarkMode ? 'text-blue-400' : 'text-blue-600'} border-r ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                          {index + 1}
                        </td>
                        <td className={`px-3 py-2 text-center font-bold text-sm ${isDarkMode ? 'text-blue-400' : 'text-blue-600'} border-r ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                          {employeeCode}
                        </td>
                        <td className={`px-4 py-2 text-right font-medium text-sm ${isDarkMode ? 'text-gray-200' : 'text-gray-900'} border-r ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                          {employee.EmployeeName || employee.FullName}
                        </td>
                        <td className={`px-3 py-2 text-right text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} border-r ${isDarkMode ? 'border-gray-600' : 'border-gray-200'}`}>
                          {employee.JobTitle}
                        </td>

                        {/* خلايا الحضور اليومي */}
                        {Array.from({ length: daysInPeriod }, (_, dayIndex) => {
                          const day = dayIndex + 1;
                          const code = employeeAttendance[day] || '';
                          const codeInfo = attendanceCodes[code];

                          return (
                            <td key={day} className={`px-1 py-1 text-center border-r ${isDarkMode ? 'border-gray-600' : 'border-gray-200'} min-w-[35px] max-w-[35px]`}>
                              {editMode ? (
                                <select
                                  value={code}
                                  onChange={(e) => updateAttendance(employeeCode, day, e.target.value)}
                                  className={`w-full text-xs p-0.5 border rounded ${
                                    isDarkMode
                                      ? 'bg-gray-700 border-gray-600 text-white'
                                      : 'bg-white border-gray-300 text-gray-900'
                                  }`}
                                  style={{ fontSize: '10px' }}
                                >
                                  <option value="">-</option>
                                  {Object.keys(attendanceCodes).map(c => (
                                    <option key={c} value={c}>{c}</option>
                                  ))}
                                </select>
                              ) : (
                                <div className="flex justify-center items-center h-6">
                                  {code ? (
                                    <span className={`inline-block px-1 py-0.5 rounded text-xs font-bold min-w-[20px] text-center ${
                                      codeInfo
                                        ? (isDarkMode ? codeInfo.darkColor : codeInfo.color)
                                        : (isDarkMode ? 'bg-gray-700 text-gray-400' : 'bg-gray-100 text-gray-500')
                                    }`} title={codeInfo?.description || 'غير محدد'}>
                                      {code}
                                    </span>
                                  ) : (
                                    <span className={`text-xs ${isDarkMode ? 'text-gray-500' : 'text-gray-400'}`}>-</span>
                                  )}
                                </div>
                              )}
                            </td>
                          );
                        })}

                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
