async function handler({ action, data, fileName, fileContent, fileId }) {
  try {
    switch (action) {
      case 'upload':
        if (!fileContent || !fileName) {
          throw new Error('المحتوى واسم الملف مطلوبان');
        }

        const { url, error } = await upload({
          base64: fileContent,
        });

        if (error) {
          throw new Error(error);
        }

        return {
          success: true,
          url,
          fileName,
          timestamp: new Date().toISOString(),
        };

      case 'view':
        if (!fileId) {
          throw new Error('معرف الملف مطلوب');
        }

        return {
          success: true,
          url: data.url,
          fileName: data.fileName,
        };

      case 'archive':
        if (!data || !data.files) {
          throw new Error('بيانات الأرشفة مطلوبة');
        }

        return {
          success: true,
          archived: true,
          timestamp: new Date().toISOString(),
          files: data.files,
        };

      case 'retrieve':
        if (!fileId) {
          throw new Error('معرف الملف مطلوب');
        }

        return {
          success: true,
          url: data.url,
          fileName: data.fileName,
          metadata: data.metadata,
        };

      default:
        throw new Error('إجراء غير صالح');
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
}
