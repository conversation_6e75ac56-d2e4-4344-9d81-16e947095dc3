/* Local Font Configuration - System Fonts with Arabic Support */

/* Define font stacks that work offline */
:root {
  /* Primary font stack - Modern system fonts */
  --font-sans: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', 
               Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 
               'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 
               'Noto Color Emoji';
  
  /* Arabic-friendly font stack */
  --font-arabic: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', 'Lucida Grande', 
                 sans-serif;
  
  /* Monospace font stack */
  --font-mono: ui-monospace, SFMono-Regular, 'Monaco', 'Consolas', 
               'Liberation Mono', 'Courier New', monospace;
}

/* Base font application */
html {
  font-family: var(--font-sans);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-family: var(--font-sans);
  font-feature-settings: "kern", "liga", "clig", "calt";
}

/* Arabic text optimization */
[lang="ar"], .arabic-text {
  font-family: var(--font-arabic);
  direction: rtl;
  text-align: right;
}

/* Monospace text */
code, pre, .font-mono {
  font-family: var(--font-mono);
}

/* Font weight utilities */
.font-thin { font-weight: 100; }
.font-extralight { font-weight: 200; }
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

/* Responsive font sizes */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-5xl { font-size: 3rem; line-height: 1; }
.text-6xl { font-size: 3.75rem; line-height: 1; }

/* Enhanced readability for Arabic text */
.arabic-enhanced {
  font-feature-settings: "kern", "liga";
  letter-spacing: -0.01em;
  word-spacing: 0.05em;
  line-height: 1.7;
}

/* Fallback for missing font icons */
.icon-fallback {
  display: inline-block;
  width: 1em;
  height: 1em;
  background-color: currentColor;
  mask-size: contain;
  mask-repeat: no-repeat;
  mask-position: center;
}

/* Performance optimizations */
.font-display-swap {
  font-display: swap;
}

/* Prevent font loading issues */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Loading state for fonts */
.font-loading {
  visibility: hidden;
}

.font-loaded {
  visibility: visible;
}

/* System font optimization for different OS */
@supports (font: -apple-system-body) {
  html {
    font: -apple-system-body;
  }
}

/* Windows-specific font improvements */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
  body {
    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
  }
}

/* macOS-specific font improvements */
@supports (-webkit-appearance: none) {
  body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
}
