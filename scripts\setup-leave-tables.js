/**
 * سكريبت إعداد جداول الإجازات
 * يقوم بإنشاء الجداول وإدراج البيانات الأولية
 */

const { getConnection } = require('../src/utils/db');
const fs = require('fs');
const path = require('path');

async function setupLeaveTables() {
  let pool = null;
  
  try {

    // الاتصال بقاعدة البيانات
    pool = await getConnection();

    // قراءة ملف SQL
    const sqlFilePath = path.join(__dirname, '../database/leave_management_enhanced.sql');
    const sqlScript = fs.readFileSync(sqlFilePath, 'utf8');

    // تقسيم السكريبت إلى أوامر منفصلة
    const sqlCommands = sqlScript
      .split('GO')
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0);

    // تنفيذ كل أمر على حدة
    for (let i = 0; i < sqlCommands.length; i++) {
      const command = sqlCommands[i];
      if (command.trim()) {
        try {

          await pool.request().query(command);
        } catch (error) {

          // متابعة التنفيذ حتى لو فشل أمر واحد
        }
      }
    }

    // التحقق من إنشاء الجداول

    const tablesCheck = await pool.request().query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_NAME IN ('LeaveBalances', 'LeaveRequests')
    `);

    console.log('📊 الجداول الموجودة:', tablesCheck.recordset.map(t => t.TABLE_NAME));

    // عد الموظفين في جدول رصيد الإجازات
    const balanceCount = await pool.request().query(`
      SELECT COUNT(*) as Count FROM LeaveBalances
    `);

    // عد طلبات الإجازات
    const requestsCount = await pool.request().query(`
      SELECT COUNT(*) as Count FROM LeaveRequests
    `);

    return {
      success: true,
      message: 'تم إعداد جداول الإجازات بنجاح',
      stats: {
        employeesWithBalance: balanceCount.recordset[0].Count,
        totalRequests: requestsCount.recordset[0].Count
      }
    };

  } catch (error) {

    return {
      success: false,
      error: error.message
    };
  } finally {
    if (pool) {
      try {
        await pool.close();

      } catch (error) {

      }
    }
  }
}

// تشغيل السكريبت إذا تم استدعاؤه مباشرة
if (require.main === module) {
  setupLeaveTables()
    .then(result => {
      if (result.success) {

      } else {

        process.exit(1);
      }
    })
    .catch(error => {

      process.exit(1);
    });
}

module.exports = { setupLeaveTables };
