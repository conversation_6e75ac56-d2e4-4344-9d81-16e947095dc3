import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function POST(request) {
  try {

    const pool = await getConnection();
    const results = [];

    // 1. التحقق من وجود الجداول

    const tablesCheck = await pool.request().query(`
      SELECT 
        CASE WHEN EXISTS (SELECT * FROM sysobjects WHERE name='Cars' AND xtype='U') THEN 1 ELSE 0 END as CarsExists,
        CASE WHEN EXISTS (SELECT * FROM sysobjects WHERE name='CarBeneficiaries' AND xtype='U') THEN 1 ELSE 0 END as CarBeneficiariesExists,
        CASE WHEN EXISTS (SELECT * FROM sysobjects WHERE name='Apartments' AND xtype='U') THEN 1 ELSE 0 END as ApartmentsExists,
        CASE WHEN EXISTS (SELECT * FROM sysobjects WHERE name='ApartmentBeneficiaries' AND xtype='U') THEN 1 ELSE 0 END as ApartmentBeneficiariesExists
    `);

    const tables = tablesCheck.recordset[0];
    results.push({
      step: 'فحص الجداول',
      cars: tables.CarsExists ? 'موجود' : 'غير موجود',
      carBeneficiaries: tables.CarBeneficiariesExists ? 'موجود' : 'غير موجود',
      apartments: tables.ApartmentsExists ? 'موجود' : 'غير موجود',
      apartmentBeneficiaries: tables.ApartmentBeneficiariesExists ? 'موجود' : 'غير موجود'
    });

    // 2. إضافة بيانات اختبار للسيارات
    if (tables.CarsExists && tables.CarBeneficiariesExists) {

      // إضافة سيارة اختبار
      try {
        await pool.request().query(`
          IF NOT EXISTS (SELECT * FROM Cars WHERE CarCode = 'TEST001')
          BEGIN
            INSERT INTO Cars (CarCode, ContractorName, CarNumber, CarType, CarModel, ManufactureYear, Route, RentAmount, Notes)
            VALUES ('TEST001', 'شركة النقل التجريبية', 'أ ب ج 123', 'ميني باص', 'تويوتا هايس', 2020, 'القاهرة - الجيزة - أوجيستا', 3000, 'سيارة اختبار')
          END
        `);

        // ربط السيارة بموظف اختبار
        await pool.request().query(`
          IF NOT EXISTS (SELECT * FROM CarBeneficiaries WHERE CarCode = 'TEST001' AND EmployeeCode = '1450')
          BEGIN
            INSERT INTO CarBeneficiaries (CarCode, EmployeeCode, EmployeeName, JobTitle, Department, StartDate)
            VALUES ('TEST001', '1450', 'سامي منير كوكب', 'مهندس', 'الهندسة', GETDATE())
          END
        `);

        results.push({
          step: 'إضافة بيانات السيارات',
          status: 'تم بنجاح',
          details: 'سيارة TEST001 مربوطة بالموظف 1450'
        });
      } catch (error) {
        results.push({
          step: 'إضافة بيانات السيارات',
          status: 'فشل',
          error: error.message
        });
      }
    }

    // 3. إضافة بيانات اختبار للشقق
    if (tables.ApartmentsExists && tables.ApartmentBeneficiariesExists) {

      try {
        // إضافة شقة اختبار
        await pool.request().query(`
          IF NOT EXISTS (SELECT * FROM Apartments WHERE ApartmentCode = 'APT001')
          BEGIN
            INSERT INTO Apartments (ApartmentCode, LandlordName, Address, StartDate, RentAmount, InsuranceAmount, Notes)
            VALUES ('APT001', 'أحمد محمد المالك', 'شارع النيل، المعادي، القاهرة', GETDATE(), 2500, 500, 'شقة اختبار')
          END
        `);

        // ربط الشقة بموظف اختبار مباشرة باستخدام ApartmentCode
        await pool.request().query(`
          IF NOT EXISTS (SELECT * FROM ApartmentBeneficiaries WHERE ApartmentCode = 'APT001' AND EmployeeCode = '1472')
          BEGIN
            INSERT INTO ApartmentBeneficiaries (ApartmentCode, EmployeeCode, EmployeeName, JobTitle, Department, StartDate)
            VALUES ('APT001', '1472', 'نبرة حسن أمين', 'محاسب', 'المحاسبة', GETDATE())
          END
        `);

        results.push({
          step: 'إضافة بيانات الشقق',
          status: 'تم بنجاح',
          details: 'شقة APT001 مربوطة بالموظف 1472'
        });
      } catch (error) {
        results.push({
          step: 'إضافة بيانات الشقق',
          status: 'فشل',
          error: error.message
        });
      }
    }

    // 4. تحديث بيانات التأمين للموظفين

    try {
      await pool.request().query(`
        UPDATE Employees 
        SET 
          SocialInsurance = 'مؤمن',
          SocialInsureNum = '123456789',
          spcialInsDate = GETDATE(),
          MedicalInsurance = 'مؤمن',
          MedicalInsuranceNum = 'MED123456'
        WHERE EmployeeCode IN ('1450', '1472', '1426', '5707')
      `);

      results.push({
        step: 'تحديث بيانات التأمين',
        status: 'تم بنجاح',
        details: 'تم تحديث التأمين لـ 4 موظفين'
      });
    } catch (error) {
      results.push({
        step: 'تحديث بيانات التأمين',
        status: 'فشل',
        error: error.message
      });
    }

    // 5. مزامنة بيانات الموظفين

    try {
      // تحديث بيانات السكن
      await pool.request().query(`
        UPDATE e
        SET 
          CompanyHousing = CASE
            WHEN EXISTS (
              SELECT 1 FROM ApartmentBeneficiaries ab
              INNER JOIN Apartments a ON ab.ApartmentCode = a.ApartmentCode
              WHERE ab.EmployeeCode = e.EmployeeCode AND ab.IsActive = 1
            ) THEN 'نعم'
            ELSE 'لا'
          END,
          codeHousing = (
            SELECT TOP 1 a.ApartmentCode
            FROM ApartmentBeneficiaries ab
            INNER JOIN Apartments a ON ab.ApartmentCode = a.ApartmentCode
            WHERE ab.EmployeeCode = e.EmployeeCode AND ab.IsActive = 1
            ORDER BY ab.StartDate DESC
          )
        FROM Employees e
        WHERE e.EmployeeCode IN ('1450', '1472', '1426', '5707')
      `);

      // تحديث بيانات المواصلات
      await pool.request().query(`
        UPDATE e
        SET 
          TransportMethod = (
            SELECT TOP 1 CONCAT(c.CarCode, ' - ', c.Route)
            FROM CarBeneficiaries cb
            INNER JOIN Cars c ON cb.CarCode = c.CarCode
            WHERE cb.EmployeeCode = e.EmployeeCode AND cb.IsActive = 1
            ORDER BY cb.StartDate DESC
          )
        FROM Employees e
        WHERE e.EmployeeCode IN ('1450', '1472', '1426', '5707')
      `);

      results.push({
        step: 'مزامنة بيانات الموظفين',
        status: 'تم بنجاح',
        details: 'تم تحديث بيانات السكن والمواصلات'
      });
    } catch (error) {
      results.push({
        step: 'مزامنة بيانات الموظفين',
        status: 'فشل',
        error: error.message
      });
    }

    // 6. فحص النتائج النهائية

    const finalCheck = await pool.request().query(`
      SELECT 
        (SELECT COUNT(*) FROM Cars WHERE IsActive = 1) as TotalCars,
        (SELECT COUNT(*) FROM CarBeneficiaries WHERE IsActive = 1) as TotalCarBeneficiaries,
        (SELECT COUNT(*) FROM Apartments WHERE IsActive = 1) as TotalApartments,
        (SELECT COUNT(*) FROM ApartmentBeneficiaries WHERE IsActive = 1) as TotalApartmentBeneficiaries,
        (SELECT COUNT(*) FROM Employees WHERE SocialInsurance = 'مؤمن') as EmployeesWithInsurance
    `);

    const stats = finalCheck.recordset[0];
    results.push({
      step: 'الإحصائيات النهائية',
      totalCars: stats.TotalCars,
      totalCarBeneficiaries: stats.TotalCarBeneficiaries,
      totalApartments: stats.TotalApartments,
      totalApartmentBeneficiaries: stats.TotalApartmentBeneficiaries,
      employeesWithInsurance: stats.EmployeesWithInsurance
    });

    return NextResponse.json({
      success: true,
      message: 'تم إعداد بيانات الاختبار بنجاح',
      results: results,
      summary: {
        totalSteps: results.length,
        successfulSteps: results.filter(r => r.status === 'تم بنجاح').length,
        failedSteps: results.filter(r => r.status === 'فشل').length
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في إعداد بيانات الاختبار',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}

export async function GET(request) {
  return NextResponse.json({
    message: 'استخدم POST لإعداد بيانات الاختبار',
    endpoints: {
      setup: 'POST /api/setup-test-data',
      test: 'GET /api/employee-assets?employeeCode=1450'
    }
  });
}
