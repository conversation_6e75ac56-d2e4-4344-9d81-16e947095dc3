'use client';

import { useState, useEffect } from 'react';
import { FiTrash2, FiCheck, FiX, FiRefreshCw, FiDatabase, FiAlertTriangle } from 'react-icons/fi';

export default function RemoveAssetsFieldsPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [fieldData, setFieldData] = useState(null);
  const [results, setResults] = useState(null);
  const [error, setError] = useState('');
  const [showConfirmation, setShowConfirmation] = useState(false);

  // فحص الحقول عند تحميل الصفحة
  useEffect(() => {
    checkFields();
  }, []);

  const checkFields = async () => {
    setIsChecking(true);
    setError('');

    try {
      const response = await fetch('/api/remove-assets-from-employees', {
        method: 'GET',
      });

      const data = await response.json();

      if (response.ok) {
        setFieldData(data);
      } else {
        setError(data.message || 'فشل في فحص الحقول');
      }
    } catch (error) {

      setError('خطأ في الاتصال بالخادم');
    } finally {
      setIsChecking(false);
    }
  };

  const removeFields = async () => {
    setIsLoading(true);
    setError('');
    setResults(null);

    try {
      const response = await fetch('/api/remove-assets-from-employees', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok) {
        setResults(data);
        setShowConfirmation(false);
        // إعادة فحص الحقول بعد الحذف
        await checkFields();
      } else {
        setError(data.message || 'فشل في حذف الحقول');
      }
    } catch (error) {

      setError('خطأ في الاتصال بالخادم');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
      <div className="max-w-6xl mx-auto">
        {/* العنوان الرئيسي */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center gap-3 mb-4">
            <FiDatabase className="text-2xl text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              حذف حقول الشقق والسيارات من جدول الموظفين
            </h1>
          </div>
          <p className="text-gray-600 dark:text-gray-300">
            هذه الصفحة تتيح لك حذف الحقول المتعلقة بالشقق والسيارات من جدول الموظفين والاعتماد على جداول المستفيدين فقط
          </p>
        </div>

        {/* فوائد هذا التغيير */}
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6 mb-6">
          <h3 className="text-lg font-bold text-green-800 dark:text-green-400 mb-4">
            🎯 فوائد هذا التصميم:
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-green-700 dark:text-green-300">
            <div>
              <h4 className="font-medium mb-2">🏗️ فصل البيانات:</h4>
              <ul className="text-sm space-y-1">
                <li>• بيانات أساسية في جدول الموظفين</li>
                <li>• بيانات الاستفادة في جداول منفصلة</li>
                <li>• لا تداخل بين البيانات</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">🔄 مرونة في الإدارة:</h4>
              <ul className="text-sm space-y-1">
                <li>• إضافة/إزالة المستفيدين بسهولة</li>
                <li>• تاريخ الاستفادة محفوظ</li>
                <li>• عدة موظفين لنفس الأصل</li>
              </ul>
            </div>
          </div>
        </div>

        {/* أزرار التحكم */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
          <div className="flex gap-4">
            <button
              onClick={checkFields}
              disabled={isChecking}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-bold py-2 px-4 rounded-lg transition-colors flex items-center gap-2"
            >
              {isChecking ? (
                <>
                  <FiRefreshCw className="animate-spin" />
                  جاري الفحص...
                </>
              ) : (
                <>
                  <FiRefreshCw />
                  فحص الحقول
                </>
              )}
            </button>

            {fieldData && fieldData.existingFields.length > 0 && !showConfirmation && (
              <button
                onClick={() => setShowConfirmation(true)}
                className="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg transition-colors flex items-center gap-2"
              >
                <FiTrash2 />
                حذف الحقول
              </button>
            )}
          </div>
        </div>

        {/* رسالة الخطأ */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2">
              <FiX className="text-red-600" />
              <span className="text-red-800 dark:text-red-400 font-medium">{error}</span>
            </div>
          </div>
        )}

        {/* نتائج الفحص */}
        {fieldData && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
            <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">
              📊 نتائج فحص الحقول
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{fieldData.summary.totalFieldsToCheck}</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">حقول مطلوب فحصها</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{fieldData.summary.existingFieldsCount}</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">حقول موجودة</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{fieldData.summary.fieldsToRemove.length}</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">حقول تحتاج حذف</div>
              </div>
            </div>

            {fieldData.existingFields.length > 0 ? (
              <div>
                <h4 className="font-bold text-gray-800 dark:text-gray-200 mb-3">الحقول الموجودة:</h4>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">اسم الحقل</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">نوع البيانات</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">يقبل NULL</th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase">القيمة الافتراضية</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {fieldData.existingFields.map((field, index) => (
                        <tr key={index}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                            {field.COLUMN_NAME}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                            {field.DATA_TYPE}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                            {field.IS_NULLABLE}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                            {field.COLUMN_DEFAULT || 'لا يوجد'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {fieldData.dataStatistics && (
                  <div className="mt-6">
                    <h4 className="font-bold text-gray-800 dark:text-gray-200 mb-3">إحصائيات البيانات:</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {fieldData.dataStatistics.map((stat, index) => (
                        <div key={index} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                          <div className="font-medium text-gray-900 dark:text-white">{stat.field}</div>
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            {stat.recordsWithData} من {stat.totalRecords} سجل ({stat.percentageWithData}%)
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <FiCheck className="text-4xl text-green-600 mx-auto mb-4" />
                <h4 className="text-lg font-bold text-green-800 dark:text-green-400">
                  ممتاز! لا توجد حقول تحتاج إلى حذف
                </h4>
                <p className="text-green-700 dark:text-green-300">
                  جدول الموظفين نظيف ولا يحتوي على حقول الشقق والسيارات
                </p>
              </div>
            )}
          </div>
        )}

        {/* نافذة التأكيد */}
        {showConfirmation && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
            <div className="text-center">
              <FiAlertTriangle className="text-4xl text-red-600 mx-auto mb-4" />
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
                هل أنت متأكد من حذف هذه الحقول؟
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                سيتم حذف جميع الحقول المتعلقة بالشقق والسيارات من جدول الموظفين نهائياً
              </p>
              
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
                <h4 className="font-bold text-yellow-800 dark:text-yellow-400 mb-2">الحقول التي سيتم حذفها:</h4>
                <div className="text-sm text-yellow-700 dark:text-yellow-300">
                  {fieldData?.summary.fieldsToRemove.join(', ')}
                </div>
              </div>

              <div className="flex gap-4 justify-center">
                <button
                  onClick={removeFields}
                  disabled={isLoading}
                  className="bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white font-bold py-2 px-6 rounded-lg transition-colors flex items-center gap-2"
                >
                  {isLoading ? (
                    <>
                      <FiRefreshCw className="animate-spin" />
                      جاري الحذف...
                    </>
                  ) : (
                    <>
                      <FiCheck />
                      نعم، احذف الحقول
                    </>
                  )}
                </button>
                <button
                  onClick={() => setShowConfirmation(false)}
                  disabled={isLoading}
                  className="bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white font-bold py-2 px-6 rounded-lg transition-colors flex items-center gap-2"
                >
                  <FiX />
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        )}

        {/* نتائج الحذف */}
        {results && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-6">
            <div className="flex items-center gap-3 mb-4">
              <FiCheck className="text-2xl text-green-600" />
              <h3 className="text-xl font-bold text-green-800 dark:text-green-400">
                {results.success ? 'تم الحذف بنجاح!' : 'تم الحذف جزئياً'}
              </h3>
            </div>
            
            <div className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{results.summary.fieldsFound}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">حقول موجودة</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{results.summary.fieldsRemoved}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">حقول تم حذفها</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{results.summary.constraintsRemoved}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">قيود تم حذفها</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{results.summary.remainingAssetFields}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">حقول متبقية</div>
                </div>
              </div>

              {results.details.recommendations && (
                <div>
                  <h4 className="font-bold text-gray-800 dark:text-gray-200 mb-2">التوصيات:</h4>
                  <ul className="space-y-1">
                    {results.details.recommendations.map((recommendation, index) => (
                      <li key={index} className="text-green-700 dark:text-green-300 flex items-start gap-2">
                        <span className="text-green-600 mt-1">•</span>
                        {recommendation}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
