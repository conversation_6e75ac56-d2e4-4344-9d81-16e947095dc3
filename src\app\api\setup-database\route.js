import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function POST(request) {
  try {
    const body = await request.json();
    const { action } = body;

    const pool = await getConnection();

    switch (action) {
      case 'setupAll':
        return await setupCompleteDatabase(pool);
      case 'cleanTestData':
        return await cleanTestData(pool);
      case 'createMissingTables':
        return await createMissingTables(pool);
      case 'setupRealData':
        return await setupRealData(pool);
      case 'checkDatabase':
        return await checkDatabaseStatus(pool);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  }
}

// إعداد قاعدة البيانات الكاملة
async function setupCompleteDatabase(pool) {
  try {

    const steps = [];

    // 1. إنشاء الجداول المفقودة
    steps.push(await createMissingTables(pool));

    // 2. تنظيف البيانات التجريبية
    steps.push(await cleanTestData(pool));

    // 3. إعداد البيانات الحقيقية
    steps.push(await setupRealData(pool));

    // 4. إعداد أنظمة الإشعارات والتنبيهات
    steps.push(await setupNotificationSystems(pool));

    return NextResponse.json({
      success: true,
      message: 'تم إعداد قاعدة البيانات بنجاح',
      steps: steps
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في إعداد قاعدة البيانات: ' + error.message
    }, { status: 500 });
  }
}

// إنشاء الجداول المفقودة
async function createMissingTables(pool) {
  try {

    // 1. جدول المحافظات
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Governorates' AND xtype='U')
      BEGIN
        CREATE TABLE Governorates (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          NameAr NVARCHAR(100) NOT NULL UNIQUE,
          NameEn NVARCHAR(100),
          IsActive BIT DEFAULT 1,
          CreatedAt DATETIME DEFAULT GETDATE()
        )
        
        -- إدراج المحافظات المصرية
        INSERT INTO Governorates (NameAr, NameEn) VALUES
        (N'القاهرة', 'Cairo'),
        (N'الإسكندرية', 'Alexandria'),
        (N'بورسعيد', 'Port Said'),
        (N'السويس', 'Suez'),
        (N'دمياط', 'Damietta'),
        (N'الدقهلية', 'Dakahlia'),
        (N'الشرقية', 'Sharqia'),
        (N'القليوبية', 'Qalyubia'),
        (N'كفر الشيخ', 'Kafr El Sheikh'),
        (N'الغربية', 'Gharbia'),
        (N'المنوفية', 'Monufia'),
        (N'البحيرة', 'Beheira'),
        (N'الفيوم', 'Fayoum'),
        (N'بني سويف', 'Beni Suef'),
        (N'المنيا', 'Minya'),
        (N'أسيوط', 'Asyut'),
        (N'سوهاج', 'Sohag'),
        (N'قنا', 'Qena'),
        (N'الأقصر', 'Luxor'),
        (N'أسوان', 'Aswan'),
        (N'شمال سيناء', 'North Sinai'),
        (N'جنوب سيناء', 'South Sinai'),
        (N'البحر الأحمر', 'Red Sea'),
        (N'الوادي الجديد', 'New Valley'),
        (N'مطروح', 'Matrouh'),
        (N'حلوان', 'Helwan'),
        (N'6 أكتوبر', '6th of October')
      END
    `);

    // 2. جدول الأقسام
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Departments' AND xtype='U')
      BEGIN
        CREATE TABLE Departments (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          DepartmentCode NVARCHAR(20) NOT NULL UNIQUE,
          DepartmentName NVARCHAR(100) NOT NULL,
          ManagerCode NVARCHAR(50),
          ParentDepartmentID INT,
          IsActive BIT DEFAULT 1,
          CreatedAt DATETIME DEFAULT GETDATE(),
          
          FOREIGN KEY (ParentDepartmentID) REFERENCES Departments(ID)
        )
        
        -- إدراج الأقسام الأساسية
        INSERT INTO Departments (DepartmentCode, DepartmentName) VALUES
        ('HR', N'الموارد البشرية'),
        ('FIN', N'الشؤون المالية'),
        ('IT', N'تكنولوجيا المعلومات'),
        ('OPS', N'العمليات'),
        ('ADM', N'الإدارة العامة'),
        ('SEC', N'الأمن والسلامة'),
        ('MAINT', N'الصيانة'),
        ('TRANS', N'النقل والمواصلات')
      END
    `);

    // 3. جدول المسميات الوظيفية
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='JobTitles' AND xtype='U')
      BEGIN
        CREATE TABLE JobTitles (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          JobCode NVARCHAR(20) NOT NULL UNIQUE,
          JobTitle NVARCHAR(100) NOT NULL,
          DepartmentCode NVARCHAR(20),
          JobLevel INT DEFAULT 1,
          MinSalary DECIMAL(10,2),
          MaxSalary DECIMAL(10,2),
          IsActive BIT DEFAULT 1,
          CreatedAt DATETIME DEFAULT GETDATE(),
          
          FOREIGN KEY (DepartmentCode) REFERENCES Departments(DepartmentCode)
        )
        
        -- إدراج المسميات الوظيفية الأساسية
        INSERT INTO JobTitles (JobCode, JobTitle, DepartmentCode, JobLevel) VALUES
        ('MGR001', N'مدير عام', 'ADM', 5),
        ('MGR002', N'مدير إدارة', 'ADM', 4),
        ('SUP001', N'مشرف', 'OPS', 3),
        ('EMP001', N'موظف إداري', 'ADM', 2),
        ('EMP002', N'موظف فني', 'OPS', 2),
        ('EMP003', N'موظف خدمات', 'OPS', 1),
        ('HR001', N'أخصائي موارد بشرية', 'HR', 3),
        ('FIN001', N'محاسب', 'FIN', 3),
        ('IT001', N'مطور برمجيات', 'IT', 3),
        ('SEC001', N'حارس أمن', 'SEC', 1),
        ('MAINT001', N'فني صيانة', 'MAINT', 2),
        ('TRANS001', N'سائق', 'TRANS', 1)
      END
    `);

    // 4. جدول أرشيف المستندات
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ARCHIV' AND xtype='U')
      BEGIN
        CREATE TABLE ARCHIV (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          itemType NVARCHAR(50) NOT NULL UNIQUE,
          folderPath NVARCHAR(500) NOT NULL,
          description NVARCHAR(200),
          isActive BIT DEFAULT 1,
          createdAt DATETIME DEFAULT GETDATE()
        )
        
        -- إدراج مسارات الأرشيف
        INSERT INTO ARCHIV (itemType, folderPath, description) VALUES
        ('photo', 'E:\\webapp\\createxyz-project\\archiv\\photo', N'الصور الشخصية'),
        ('NationalIDs', 'E:\\webapp\\createxyz-project\\archiv\\NationalIDs', N'بطاقات الرقم القومي'),
        ('StatusReports', 'E:\\webapp\\createxyz-project\\archiv\\StatusReports', N'بيانات الحالة الاجتماعية'),
        ('UnionCards', 'E:\\webapp\\createxyz-project\\archiv\\UnionCards', N'كارينة النقابة'),
        ('WorkReceipts', 'E:\\webapp\\createxyz-project\\archiv\\WorkReceipts', N'إستلام العمل'),
        ('Bh', 'E:\\webapp\\createxyz-project\\archiv\\Bh', N'بدل إعاشة'),
        ('Bt', 'E:\\webapp\\createxyz-project\\archiv\\Bt', N'بدل إنتقال'),
        ('pic', 'E:\\webapp\\createxyz-project\\archiv\\pic', N'صورة شخصية jpg'),
        ('carscost', 'E:\\webapp\\createxyz-project\\archiv\\carscost', N'تكاليف السيارات'),
        ('housingcost', 'E:\\webapp\\createxyz-project\\archiv\\housingcost', N'تكاليف السكن'),
        ('3amala', 'E:\\webapp\\createxyz-project\\archiv\\3amala', N'تكاليف العمالة المؤقتة'),
        ('namazg', 'E:\\webapp\\createxyz-project\\archiv\\namazg', N'نماذج النظام')
      END
    `);

    // 5. جدول تسجيل الدخول
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='login' AND xtype='U')
      BEGIN
        CREATE TABLE login (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          username NVARCHAR(50) NOT NULL UNIQUE,
          password NVARCHAR(255) NOT NULL,
          employeeCode NVARCHAR(50),
          fullName NVARCHAR(100),
          role NVARCHAR(50) DEFAULT 'user',
          isActive BIT DEFAULT 1,
          lastLogin DATETIME,
          createdAt DATETIME DEFAULT GETDATE(),
          updatedAt DATETIME DEFAULT GETDATE()
        )
        
        -- إدراج مستخدم افتراضي
        INSERT INTO login (username, password, fullName, role) VALUES
        ('admin', 'admin123', N'مدير النظام', 'admin')
      END
    `);

    return { step: 'createMissingTables', success: true, message: 'تم إنشاء الجداول المفقودة' };

  } catch (error) {

    return { step: 'createMissingTables', success: false, error: error.message };
  }
}

// تنظيف البيانات التجريبية
async function cleanTestData(pool) {
  try {

    // حذف البيانات التجريبية من جدول العمالة المؤقتة
    await pool.request().query(`
      DELETE FROM TempWorkers 
      WHERE WorkerCode IN ('TW001', 'TW002', 'TW003', 'TW004', 'TW005', 'TW006', 'TW007', 'TW008', 'TW009', 'TW010')
    `);

    // حذف البيانات التجريبية من جدول الشقق
    await pool.request().query(`
      DELETE FROM ApartmentBeneficiaries WHERE ApartmentID IN (
        SELECT ID FROM Apartments WHERE ApartmentCode IN ('APT-001', 'APT-002', 'APT-003', 'APT-004', 'APT-005')
      )
    `);
    
    await pool.request().query(`
      DELETE FROM Apartments 
      WHERE ApartmentCode IN ('APT-001', 'APT-002', 'APT-003', 'APT-004', 'APT-005')
    `);

    // حذف البيانات التجريبية من جدول السيارات
    await pool.request().query(`
      DELETE FROM CarBeneficiaries WHERE CarID IN (
        SELECT ID FROM Cars WHERE CarCode IN ('CAR-001', 'CAR-002', 'CAR-003')
      )
    `);
    
    await pool.request().query(`
      DELETE FROM Cars 
      WHERE CarCode IN ('CAR-001', 'CAR-002', 'CAR-003')
    `);

    // حذف البيانات التجريبية من جدول الحضور الشهري
    await pool.request().query(`
      DELETE FROM MonthlyAttendanceSheet 
      WHERE EmployeeCode IN ('EMP001', 'EMP002', 'EMP003') 
        AND AttendanceDate >= '2024-01-01' 
        AND AttendanceDate <= '2024-12-31'
    `);

    return { step: 'cleanTestData', success: true, message: 'تم تنظيف البيانات التجريبية' };

  } catch (error) {

    return { step: 'cleanTestData', success: false, error: error.message };
  }
}

// إعداد البيانات الحقيقية
async function setupRealData(pool) {
  try {

    // 1. إعداد إعدادات النظام
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SystemSettings' AND xtype='U')
      BEGIN
        CREATE TABLE SystemSettings (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          SettingKey NVARCHAR(100) NOT NULL UNIQUE,
          SettingValue NVARCHAR(500),
          SettingType NVARCHAR(50) DEFAULT 'string',
          Description NVARCHAR(500),
          Category NVARCHAR(100),
          IsActive BIT DEFAULT 1,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE()
        )

        -- إدراج الإعدادات الأساسية
        INSERT INTO SystemSettings (SettingKey, SettingValue, SettingType, Description, Category) VALUES
        ('COMPANY_NAME', N'شركة XYZ', 'string', N'اسم الشركة', 'General'),
        ('COMPANY_ADDRESS', N'القاهرة، مصر', 'string', N'عنوان الشركة', 'General'),
        ('WORK_START_TIME', '08:00', 'time', N'وقت بداية العمل', 'Attendance'),
        ('WORK_END_TIME', '17:00', 'time', N'وقت نهاية العمل', 'Attendance'),
        ('WEEKEND_DAYS', 'Friday,Saturday', 'string', N'أيام نهاية الأسبوع', 'Attendance'),
        ('ANNUAL_LEAVE_DAYS', '21', 'number', N'عدد أيام الإجازة السنوية', 'Leave'),
        ('CASUAL_LEAVE_DAYS', '6', 'number', N'عدد أيام الإجازة العارضة', 'Leave'),
        ('SICK_LEAVE_DAYS', '30', 'number', N'عدد أيام الإجازة المرضية', 'Leave'),
        ('CURRENCY', 'EGP', 'string', N'العملة المستخدمة', 'Financial'),
        ('BACKUP_INTERVAL_DAYS', '7', 'number', N'فترة النسخ الاحتياطي بالأيام', 'System')
      END
    `);

    // 2. إعداد جدول التكاليف
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Costs' AND xtype='U')
      BEGIN
        CREATE TABLE Costs (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          CostType NVARCHAR(50) NOT NULL, -- cars, apartments, temp_workers
          Month INT NOT NULL,
          Year INT NOT NULL,
          ItemCount INT DEFAULT 0,
          TotalAmount DECIMAL(12,2) NOT NULL,
          VersionRequestPath NVARCHAR(500),
          Notes NVARCHAR(MAX),
          CreatedBy NVARCHAR(100),
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),

          UNIQUE(CostType, Month, Year)
        )

        CREATE INDEX IX_Costs_Type ON Costs(CostType)
        CREATE INDEX IX_Costs_Date ON Costs(Year, Month)
      END
    `);

    // 3. إعداد جدول الحضور اليومي
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='daily_attendance' AND xtype='U')
      BEGIN
        CREATE TABLE daily_attendance (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          date DATE NOT NULL,
          employeeCode NVARCHAR(50) NOT NULL,
          employeeName NVARCHAR(100) NOT NULL,
          jobTitle NVARCHAR(100),
          movementType NVARCHAR(100),
          notes NVARCHAR(MAX),
          createdAt DATETIME DEFAULT GETDATE(),

          INDEX IX_daily_attendance_date (date),
          INDEX IX_daily_attendance_employee (employeeCode),
          INDEX IX_daily_attendance_date_employee (date, employeeCode)
        )
      END
    `);

    // 4. إعداد جدول رصيد الإجازات
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='leave_balances' AND xtype='U')
      BEGIN
        CREATE TABLE leave_balances (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          employee_code NVARCHAR(50) NOT NULL,
          employee_name NVARCHAR(100) NOT NULL,
          job_title NVARCHAR(100),
          year INT NOT NULL DEFAULT YEAR(GETDATE()),
          regular_balance INT DEFAULT 21,
          regular_used INT DEFAULT 0,
          casual_balance INT DEFAULT 6,
          casual_used INT DEFAULT 0,
          sick_balance INT DEFAULT 30,
          sick_used INT DEFAULT 0,
          compensation_balance INT DEFAULT 0,
          compensation_used INT DEFAULT 0,
          created_at DATETIME DEFAULT GETDATE(),
          updated_at DATETIME DEFAULT GETDATE(),

          UNIQUE(employee_code, year),
          INDEX IX_leave_balances_employee (employee_code),
          INDEX IX_leave_balances_year (year)
        )
      END
    `);

    // 5. إعداد جدول طلبات الإجازات
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='leave_requests' AND xtype='U')
      BEGIN
        CREATE TABLE leave_requests (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          employee_code NVARCHAR(50) NOT NULL,
          employee_name NVARCHAR(100) NOT NULL,
          leave_type NVARCHAR(50) NOT NULL, -- regular, casual, sick, compensation
          start_date DATE NOT NULL,
          end_date DATE NOT NULL,
          days_count INT NOT NULL,
          reason NVARCHAR(500),
          status NVARCHAR(20) DEFAULT 'pending', -- pending, approved, rejected
          approved_by NVARCHAR(100),
          approved_at DATETIME,
          rejection_reason NVARCHAR(500),
          created_at DATETIME DEFAULT GETDATE(),
          updated_at DATETIME DEFAULT GETDATE(),

          INDEX IX_leave_requests_employee (employee_code),
          INDEX IX_leave_requests_status (status),
          INDEX IX_leave_requests_dates (start_date, end_date),
          INDEX IX_leave_requests_type (leave_type)
        )
      END
    `);

    return { step: 'setupRealData', success: true, message: 'تم إعداد البيانات الحقيقية' };

  } catch (error) {

    return { step: 'setupRealData', success: false, error: error.message };
  }
}

// إعداد أنظمة الإشعارات والتنبيهات
async function setupNotificationSystems(pool) {
  try {

    // إعداد نظام الإشعارات
    const notificationResponse = await fetch('/api/notifications', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'setup' })
    });

    // إعداد نظام التنبيهات
    const alertResponse = await fetch('/api/alerts', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ action: 'setup' })
    });

    return {
      step: 'setupNotificationSystems',
      success: true,
      message: 'تم إعداد أنظمة الإشعارات والتنبيهات'
    };

  } catch (error) {

    return { step: 'setupNotificationSystems', success: false, error: error.message };
  }
}

// فحص حالة قاعدة البيانات
async function checkDatabaseStatus(pool) {
  try {

    const tables = [
      'employee_data', 'Governorates', 'Departments', 'JobTitles', 'ARCHIV', 'login',
      'Apartments', 'ApartmentBeneficiaries', 'Cars', 'CarBeneficiaries',
      'TempWorkers', 'Costs', 'daily_attendance', 'leave_balances', 'leave_requests',
      'SystemSettings', 'UserActions', 'Notifications', 'SystemAlerts', 'AlertTypes'
    ];

    const tableStatus = {};
    let totalRecords = 0;

    for (const table of tables) {
      try {
        const result = await pool.request().query(`
          SELECT COUNT(*) as count FROM ${table}
        `);
        const count = result.recordset[0].count;
        tableStatus[table] = { exists: true, records: count };
        totalRecords += count;
      } catch (error) {
        tableStatus[table] = { exists: false, error: error.message };
      }
    }

    // فحص البيانات التجريبية المتبقية
    const testDataCheck = {};

    try {
      const tempWorkersTest = await pool.request().query(`
        SELECT COUNT(*) as count FROM TempWorkers
        WHERE WorkerCode LIKE 'TW%' AND WorkerName LIKE '%تجريبي%' OR WorkerName LIKE 'أحمد محمد علي'
      `);
      testDataCheck.tempWorkers = tempWorkersTest.recordset[0].count;
    } catch (error) {
      testDataCheck.tempWorkers = 'error';
    }

    try {
      const apartmentsTest = await pool.request().query(`
        SELECT COUNT(*) as count FROM Apartments
        WHERE ApartmentCode LIKE 'APT-%'
      `);
      testDataCheck.apartments = apartmentsTest.recordset[0].count;
    } catch (error) {
      testDataCheck.apartments = 'error';
    }

    return NextResponse.json({
      success: true,
      data: {
        tableStatus,
        totalRecords,
        testDataRemaining: testDataCheck,
        databaseReady: Object.values(tableStatus).filter(t => t.exists).length >= 15
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في فحص قاعدة البيانات: ' + error.message
    }, { status: 500 });
  }
}
