async function handler({ employeeCode }) {
  if (!employeeCode) {
    return {
      success: false,
      message: 'رمز الموظف مطلوب',
    };
  }

  try {
    const employee = await sql`
      SELECT 
        emp.EmployeeID,
        emp.FullName,
        emp.DepartmentID,
        emp.JobTitleID
      FROM Employees emp
      WHERE emp.EmployeeID = ${employeeCode}
      AND emp.IsActive = true
      LIMIT 1
    `;

    if (!employee || employee.length === 0) {
      return {
        success: false,
        message: 'رمز الموظف غير صحيح',
      };
    }

    const session = await sql`
      INSERT INTO auth_sessions 
        ("userId", expires, "sessionToken")
      VALUES 
        (
          ${employee[0].EmployeeID}, 
          ${new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)}, 
          ${Math.random().toString(36).substring(2)}
        )
      RETURNING *
    `;

    return {
      success: true,
      employee: employee[0],
      session: session[0]
    };
  } catch (error) {
    return {
      success: false,
      message: error.message
    };
  }
}
