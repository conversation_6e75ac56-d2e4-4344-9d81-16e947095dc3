'use client';

import { useState, useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { useLanguage } from '@/contexts/LanguageContext';
import MainLayout from '@/components/MainLayout';
import DateInput from '@/components/DateInput';
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown, 
  Users, 
  FileText, 
  Download,
  Calculator,
  Eye,
  Edit,
  Printer,
  Search,
  Filter
} from 'lucide-react';

export default function MonthlyPayroll() {
  const { isDarkMode } = useTheme();
  const { isRTL, isArabic } = useLanguage();
  
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [loading, setLoading] = useState(false);
  const [payrollData, setPayrollData] = useState([]);
  const [summary, setSummary] = useState({
    totalEmployees: 0,
    totalBasicSalary: 0,
    totalAllowances: 0,
    totalDeductions: 0,
    totalNetSalary: 0,
    totalTax: 0,
    totalInsurance: 0
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [filters, setFilters] = useState({
    department: '',
    employeeCode: '',
    salaryRange: ''
  });
  const [showCalculateModal, setShowCalculateModal] = useState(false);

  // تحويل التاريخ من DD/MM/YYYY إلى YYYY-MM-DD للحسابات
  const convertDateForCalculation = (dateStr) => {
    if (!dateStr || dateStr === 'undefined' || dateStr === 'null') return '';
    if (typeof dateStr === 'string' && dateStr.includes('/')) {
      const parts = dateStr.split('/');
      if (parts.length === 3) {
        const [day, month, year] = parts;
        if (day && month && year) {
          return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        }
      }
    }
    return dateStr;
  };

  // تحويل التاريخ من YYYY-MM-DD إلى DD/MM/YYYY للعرض
  const convertDateForDisplay = (dateStr) => {
    if (!dateStr || dateStr === 'undefined' || dateStr === 'null') return '';
    if (typeof dateStr === 'string' && dateStr.includes('-') && dateStr.length === 10) {
      const parts = dateStr.split('-');
      if (parts.length === 3) {
        const [year, month, day] = parts;
        if (year && month && day) {
          return `${day}/${month}/${year}`;
        }
      }
    }
    return dateStr;
  };

  useEffect(() => {
    loadMonthlyPayroll();
  }, [selectedMonth, selectedYear]);

  const loadMonthlyPayroll = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/monthly-payroll', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'list',
          month: selectedMonth,
          year: selectedYear,
          ...filters
        })
      });

      const result = await response.json();
      
      if (result.success) {
        setPayrollData(result.data || []);
        setSummary(result.summary || {});
      } else {

      }
    } catch (error) {

    }
    setLoading(false);
  };

  // حساب الرواتب للشهر
  const calculatePayroll = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/monthly-payroll', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'calculate',
          month: selectedMonth,
          year: selectedYear
        })
      });

      const result = await response.json();
      
      if (result.success) {
        await loadMonthlyPayroll(); // إعادة تحميل البيانات
        alert('تم حساب الرواتب بنجاح');
      } else {
        alert('خطأ في حساب الرواتب: ' + result.error);
      }
    } catch (error) {

      alert('خطأ في حساب الرواتب');
    }
    setLoading(false);
  };

  // تنسيق العملة
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(amount || 0);
  };

  // فلترة البيانات
  const filteredPayrollData = payrollData.filter(employee => {
    const matchesSearch = !searchTerm || 
      employee.EmployeeName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      employee.EmployeeCode?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesDepartment = !filters.department || employee.Department === filters.department;
    const matchesEmployee = !filters.employeeCode || employee.EmployeeCode === filters.employeeCode;

    return matchesSearch && matchesDepartment && matchesEmployee;
  });

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto">
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="flex items-center justify-between">
            <div>
              <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
                كشف الرواتب الشهرية
              </h1>
              <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                إدارة ومتابعة كشف الرواتب الشهرية للموظفين
              </p>
            </div>
            <div className="flex gap-3">
              <button
                onClick={calculatePayroll}
                disabled={loading}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
              >
                <Calculator className="w-4 h-4" />
                {loading ? 'جاري الحساب...' : 'حساب الرواتب'}
              </button>
              <button
                onClick={loadMonthlyPayroll}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
              >
                <Download className="w-4 h-4" />
                {loading ? 'جاري التحميل...' : 'تصدير Excel'}
              </button>
              <button
                onClick={() => window.print()}
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <Printer className="w-4 h-4" />
                طباعة
              </button>
            </div>
          </div>
        </div>

        {/* فلاتر الشهر والسنة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                الشهر
              </label>
              <select
                value={selectedMonth}
                onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                  isDarkMode 
                    ? 'bg-slate-800 border-slate-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                {[
                  { value: 1, label: 'يناير' },
                  { value: 2, label: 'فبراير' },
                  { value: 3, label: 'مارس' },
                  { value: 4, label: 'أبريل' },
                  { value: 5, label: 'مايو' },
                  { value: 6, label: 'يونيو' },
                  { value: 7, label: 'يوليو' },
                  { value: 8, label: 'أغسطس' },
                  { value: 9, label: 'سبتمبر' },
                  { value: 10, label: 'أكتوبر' },
                  { value: 11, label: 'نوفمبر' },
                  { value: 12, label: 'ديسمبر' }
                ].map(month => (
                  <option key={month.value} value={month.value}>
                    {month.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                السنة
              </label>
              <select
                value={selectedYear}
                onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                  isDarkMode 
                    ? 'bg-slate-800 border-slate-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                {Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - 2 + i).map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
            </div>

            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                القسم
              </label>
              <select
                value={filters.department}
                onChange={(e) => setFilters(prev => ({ ...prev, department: e.target.value }))}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                  isDarkMode 
                    ? 'bg-slate-800 border-slate-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              >
                <option value="">جميع الأقسام</option>
                <option value="الإدارة">الإدارة</option>
                <option value="المحاسبة">المحاسبة</option>
                <option value="الموارد البشرية">الموارد البشرية</option>
                <option value="تقنية المعلومات">تقنية المعلومات</option>
                <option value="المبيعات">المبيعات</option>
              </select>
            </div>

            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-700'} mb-2`}>
                البحث
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="البحث بالاسم أو الكود..."
                  className={`w-full pl-10 pr-3 py-2 border rounded-lg focus:ring-blue-500 focus:border-blue-500 ${
                    isDarkMode 
                      ? 'bg-slate-800 border-slate-600 text-white placeholder-slate-400' 
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  }`}
                />
              </div>
            </div>
          </div>
        </div>

        {/* جدول كشف الرواتب */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border`}>
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className={`text-lg font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              كشف الرواتب - {selectedMonth}/{selectedYear}
            </h3>
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className={`mr-3 ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                جاري تحميل البيانات...
              </span>
            </div>
          ) : filteredPayrollData.length === 0 ? (
            <div className="text-center py-12">
              <FileText className={`mx-auto h-12 w-12 ${isDarkMode ? 'text-slate-400' : 'text-gray-400'}`} />
              <h3 className={`mt-2 text-sm font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-900'}`}>
                لا توجد بيانات رواتب
              </h3>
              <p className={`mt-1 text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>
                لم يتم حساب الرواتب للشهر المحدد بعد
              </p>
              <button
                onClick={calculatePayroll}
                className="mt-4 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 mx-auto transition-colors"
              >
                <Calculator className="w-4 h-4" />
                حساب الرواتب الآن
              </button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className={isDarkMode ? 'bg-slate-800' : 'bg-gray-50'}>
                  <tr>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      الموظف
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      الراتب الأساسي
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      البدلات
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      المؤثرات
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      الخصومات
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      الراتب الإجمالي
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      الراتب الصافي
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} divide-y ${isDarkMode ? 'divide-slate-700' : 'divide-gray-200'}`}>
                  {filteredPayrollData.map((employee) => (
                    <tr key={employee.ID} className={isDarkMode ? 'hover:bg-slate-800' : 'hover:bg-gray-50'}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div>
                            <div className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                              {employee.EmployeeName}
                            </div>
                            <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>
                              {employee.EmployeeCode} - {employee.Department}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          {formatCurrency(employee.BasicSalary)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm font-medium text-blue-600`}>
                          {formatCurrency(employee.TotalAllowances)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm font-medium ${
                          employee.TotalEffects >= 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {employee.TotalEffects >= 0 ? '+' : ''}{formatCurrency(employee.TotalEffects)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm font-medium text-red-600`}>
                          {formatCurrency(employee.TotalDeductions)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          {formatCurrency(employee.GrossSalary)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className={`text-lg font-bold text-green-600`}>
                          {formatCurrency(employee.NetSalary)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => handleViewDetails(employee)}
                            className="text-blue-600 hover:text-blue-900 transition-colors"
                            title="عرض التفاصيل"
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleEditPayroll(employee)}
                            className="text-green-600 hover:text-green-900 transition-colors"
                            title="تعديل"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handlePrintPayslip(employee)}
                            className="text-purple-600 hover:text-purple-900 transition-colors"
                            title="طباعة قسيمة الراتب"
                          >
                            <Printer className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );

  // دوال المعالجة
  function handleViewDetails(employee) {
    // عرض تفاصيل الراتب

  }

  function handleEditPayroll(employee) {
    // تعديل بيانات الراتب

  }

  function handlePrintPayslip(employee) {
    // طباعة قسيمة الراتب

  }
}
