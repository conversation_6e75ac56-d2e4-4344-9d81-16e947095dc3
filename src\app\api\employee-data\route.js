import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

// دالة لمعالجة التواريخ وتجنب 1970-01-01
function formatDate(dateValue) {
  if (!dateValue) return null;
  const date = new Date(dateValue);
  // التحقق من صحة التاريخ وأنه ليس 1970-01-01
  if (isNaN(date.getTime()) || date.getFullYear() < 1900) {
    return null;
  }
  return dateValue;
}

// دالة لتوحيد بيانات الموظف
function normalizeEmployeeData(employee) {
  return {
    ...employee,
    // التسميات الموحدة الجديدة
    employeeCode: employee.EmployeeCode,
    employeeName: employee.EmployeeName,

    // التسميات الإضافية
    EmployeeCode: employee.EmployeeCode,
    EmployeeName: employee.EmployeeName,

    // معالجة التواريخ
    HireDate: formatDate(employee.HireDate),
    BirthDate: formatDate(employee.BirthDate),
    JoinDate: formatDate(employee.JoinDate),

    // إضافة displayText للواجهات
    displayText: `${employee.EmployeeName} (${employee.EmployeeCode})`
  };
}

async function validateEmployeeData(data) {
  const requiredFields = [
    { form: 'employeeCode', db: 'كود الموظف' },
    { form: 'name', db: 'الاسم' },
    { form: 'jobTitle', db: 'المسمى الوظيفي' },
    { form: 'department', db: 'القسم' },
    { form: 'directManager', db: 'المدير المباشر' },
    { form: 'nationalId', db: 'الرقم القومي' },
    { form: 'birthDate', db: 'تاريخ الميلاد' },
    { form: 'joinDate', db: 'تاريخ التواجد' },
    { form: 'governorate', db: 'المحافظة' },
    { form: 'gender', db: 'النوع' },
    { form: 'employeeStatus', db: 'حالة الموظف' },
    { form: 'militaryService', db: 'الخدمة العسكرية' },
    { form: 'isResident', db: 'مغترب' },
    { form: 'housing', db: 'السكن' },
    { form: 'transportation', db: 'المواصلات' }
    // إزالة housingCode من الحقول المطلوبة لأنه قد يكون فارغ
  ];

  const missingFields = [];
  for (const field of requiredFields) {
    // Special handling for boolean fields
    if (field.form === 'isResident') {
      if (typeof data[field.form] !== 'boolean') {
        missingFields.push(field.db);
      }
    }
    // Handle empty strings, null, or undefined
    else if (!data[field.form] && data[field.form] !== 0) {
      missingFields.push(field.db);
    }
  }

  if (missingFields.length > 0) {
    throw new Error('يجب ملء الحقول التالية: ' + missingFields.join('، '));
  }

  // Additional validation for specific fields
  if (data.nationalId && data.nationalId.length !== 14) {
    throw new Error('الرقم القومي يجب أن يكون 14 رقم');
  }

  // Validate dates
  const dateFields = ['birthDate', 'joinDate'];
  for (const field of dateFields) {
    if (data[field] && !isValidDate(data[field])) {
      throw new Error(`تاريخ غير صحيح: ${field === 'birthDate' ? 'تاريخ الميلاد' : 'تاريخ التواجد'}`);
    }
  }

  return true;
}

function isValidDate(dateString) {
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date);
}

export async function POST(req) {
  try {
    const data = await req.json();
    console.log('Received employee data:', JSON.stringify(data, null, 2));

    // Handle list action
    if (data.action === 'list') {
      try {
        const pool = await getConnection();
        const result = await pool.request().query(`
          SELECT
            Num,
            EmployeeCode,
            EmployeeName,
            JobTitle,
            Department,
            CurrentStatus,
            HireDate,
            BirthDate,
            JoinDate,
            NationalID,
            Governorate,
            Gender,
            Mobile,
            email,
            Education,
            University,
            Major,
            Grade,
            Batch
          FROM Employees
        `);

        const normalizedEmployees = (result.recordset || []).map(normalizeEmployeeData);
        return NextResponse.json({
          success: true,
          data: normalizedEmployees
        });
      } catch (err) {
        return NextResponse.json({
          success: false,
          message: 'خطأ في جلب بيانات الموظفين',
          error: err.message
        }, { status: 500 });
      }
    }

    // Handle create action
    try {
      await validateEmployeeData(data);
    } catch (validationError) {
      return NextResponse.json({
        success: false,
        message: validationError.message
      }, { status: 400 });
    }

    // Map form fields to database fields
    const dbData = {
      EmployeeCode: data.employeeCode,
      EmployeeName: data.name,
      JobTitle: data.jobTitle,
      Department: data.department,
      Direct: data.directManager,
      HireDate: data.hireDate,
      BirthDate: data.birthDate,
      JoinDate: data.joinDate,
      NationalID: data.nationalId,
      Governorate: data.governorate,
      Gender: data.gender,
      CurrentStatus: data.employeeStatus,
      MaritalStatus: data.maritalStatus, // تصحيح: كان militaryService
      MilitaryService: data.militaryService, // إضافة الخدمة العسكرية
      IsResidentEmployee: data.isResident ? 1 : 0,
      CompanyHousing: data.housing,
      codeHousing: data.housingCode,
      TransportMethod: data.transportation,
      VehicleCode: data.vehicleCode || '',
      Area: data.area || '',
      Mobile: data.mobile || '',
      Email: data.email || '',
      Education: data.education || '', // تصحيح: كان qualification
      University: data.university || '',
      Major: data.specialization || '',
      Grade: data.grade || '',
      Batch: data.graduationYear || '',
      emrnum: data.emergencyNumber || '',
      Kinship: data.kinshipDegree || '',
      Address: data.address || '',
      HasSubsistenceAllowance: data.hasSubsistenceAllowance ? 1 : 0,
      HasTransportationAllowance: data.hasTransportationAllowance ? 1 : 0
    };

    // إدخال بيانات الموظف
    const pool = await getConnection();
    const employeeQuery = `
      INSERT INTO Employees (
        Num, EmployeeCode, EmployeeName, JobTitle, Department, direct,
        HireDate, BirthDate, JoinDate, NationalID,
        Governorate, Gender, CurrentStatus, MaritalStatus, Mserv,
        IsResidentEmployee, CompanyHousing, codeHousing,
        TransportMethod, VehicleCode, area, Mobile, email,
        Education, University, Major, Grade,
        Batch, emrnum, Kinship, Address
      ) VALUES (
        (SELECT ISNULL(MAX(Num), 0) + 1 FROM Employees),
        @EmployeeCode, @EmployeeName, @JobTitle, @Department, @Direct,
        @HireDate, @BirthDate, @JoinDate, @NationalID,
        @Governorate, @Gender, @CurrentStatus, @MaritalStatus, @MilitaryService,
        @IsResidentEmployee, @CompanyHousing, @codeHousing,
        @TransportMethod, @VehicleCode, @area, @Mobile, @email,
        @Education, @University, @Major, @Grade,
        @Batch, @emrnum, @Kinship, @Address
      )
    `;

    const request = pool.request();

    // إضافة البارامترات مع تحديد نوع البيانات
    request.input('EmployeeCode', sql.VarChar, dbData.EmployeeCode);
    request.input('EmployeeName', sql.NVarChar, dbData.EmployeeName);
    request.input('JobTitle', sql.NVarChar, dbData.JobTitle);
    request.input('Department', sql.NVarChar, dbData.Department);
    request.input('Direct', sql.NVarChar, dbData.Direct);
    request.input('HireDate', sql.Date, dbData.HireDate ? new Date(dbData.HireDate) : null);
    request.input('BirthDate', sql.Date, dbData.BirthDate ? new Date(dbData.BirthDate) : null);
    request.input('JoinDate', sql.Date, dbData.JoinDate ? new Date(dbData.JoinDate) : null);
    request.input('NationalID', sql.VarChar, dbData.NationalID);
    request.input('Governorate', sql.NVarChar, dbData.Governorate);
    request.input('Gender', sql.NVarChar, dbData.Gender);
    request.input('CurrentStatus', sql.NVarChar, dbData.CurrentStatus);
    request.input('MaritalStatus', sql.NVarChar, dbData.MaritalStatus);
    request.input('MilitaryService', sql.NVarChar, dbData.MilitaryService);
    request.input('IsResidentEmployee', sql.Bit, dbData.IsResidentEmployee);
    request.input('CompanyHousing', sql.NVarChar, dbData.CompanyHousing);
    request.input('codeHousing', sql.VarChar, dbData.codeHousing);
    request.input('TransportMethod', sql.NVarChar, dbData.TransportMethod);
    request.input('VehicleCode', sql.VarChar, dbData.VehicleCode);
    request.input('area', sql.NVarChar, dbData.Area);
    request.input('Mobile', sql.VarChar, dbData.Mobile);
    request.input('email', sql.VarChar, dbData.Email);
    request.input('Education', sql.NVarChar, dbData.Education);
    request.input('University', sql.NVarChar, dbData.University);
    request.input('Major', sql.NVarChar, dbData.Major);
    request.input('Grade', sql.NVarChar, dbData.Grade);
    request.input('Batch', sql.VarChar, dbData.Batch);
    request.input('emrnum', sql.VarChar, dbData.emrnum);
    request.input('Kinship', sql.NVarChar, dbData.Kinship);
    request.input('Address', sql.NVarChar, dbData.Address);

    // تنفيذ الاستعلام
    await request.query(employeeQuery);

    return NextResponse.json({ 
      success: true, 
      message: 'تم حفظ بيانات الموظف بنجاح' 
    });

  } catch (error) {
    let errorMessage = 'حدث خطأ أثناء حفظ بيانات الموظف';
    
    // Add specific error messages for common database errors
    if (error.code === 'ELOGIN') {
      errorMessage = 'خطأ في تسجيل الدخول لقاعدة البيانات';
    } else if (error.number === 207) {
      errorMessage = 'خطأ في اسماء الأعمدة في قاعدة البيانات';
    } else if (error.number === 2627) {
      errorMessage = 'رقم الموظف مسجل مسبقاً';
    }
    
    return NextResponse.json(
      { 
        success: false, 
        message: errorMessage,
        error: error.message,
        details: {
          code: error.code,
          number: error.number,
          state: error.state
        }
      },
      { status: 500 }
    );
  }
}

export async function PUT(request) {
  try {
    const data = await request.json();
    const { employeeCode, employeeId, ...updateData } = data;
    const empCode = employeeCode || employeeId;

    if (!empCode) {
      return NextResponse.json({
        success: false,
        message: 'معرف الموظف مطلوب'
      }, { status: 400 });
    }
    const pool = await getConnection();
    // بناء استعلام التحديث ديناميكياً
    const updateFields = [];
    const request = pool.request();

    // إضافة الحقول القابلة للتحديث
    const updatableFields = {
      'EmployeeName': 'EmployeeName',
      'JobTitle': 'JobTitle',
      'Department': 'Department',
      'DirectManager': 'direct',
      'HireDate': 'HireDate',
      'BirthDate': 'BirthDate',
      'JoinDate': 'JoinDate',
      'NationalID': 'NationalID',
      'Governorate': 'Governorate',
      'MaritalStatus': 'MaritalStatus',
      'Gender': 'Gender',
      'CurrentStatus': 'CurrentStatus',
      'MilitaryService': 'Mserv',
      'IsResidentEmployee': 'IsResidentEmployee',
      'CompanyHousing': 'CompanyHousing',
      'codeHousing': 'codeHousing',
      'TransportMethod': 'TransportMethod',
      'Area': 'area',
      'Mobile': 'Mobile',
      'Email': 'email',
      'Education': 'Education',
      'University': 'University',
      'Major': 'Major',
      'Grade': 'Grade',
      'Batch': 'Batch',
      'EmergencyNumber': 'emrnum',
      'Kinship': 'Kinship'
    };

    Object.entries(updatableFields).forEach(([frontendField, dbField]) => {
      if (updateData[frontendField] !== undefined) {
        updateFields.push(`${dbField} = @${frontendField}`);

        // تحديد نوع البيانات المناسب
        if (frontendField.includes('Date')) {
          request.input(frontendField, sql.Date, updateData[frontendField] ? new Date(updateData[frontendField]) : null);
        } else if (frontendField === 'IsResidentEmployee') {
          request.input(frontendField, sql.Bit, updateData[frontendField] ? 1 : 0);
        } else if (frontendField === 'EmployeeCode') {
          request.input(frontendField, sql.VarChar, updateData[frontendField]);
        } else {
          request.input(frontendField, sql.NVarChar, updateData[frontendField] || null);
        }
      }
    });

    if (updateFields.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'لا توجد حقول للتحديث'
      }, { status: 400 });
    }

    request.input('EmployeeCode', sql.VarChar, empCode);

    const updateQuery = `
      UPDATE Employees
      SET ${updateFields.join(', ')}
      WHERE EmployeeCode = @EmployeeCode
    `;
    const result = await request.query(updateQuery);
    if (result.rowsAffected[0] === 0) {
      return NextResponse.json({
        success: false,
        message: 'لم يتم العثور على الموظف'
      }, { status: 404 });
    }
    return NextResponse.json({
      success: true,
      message: 'تم تحديث بيانات الموظف بنجاح'
    });

  } catch (error) {
    // رسائل خطأ مخصصة
    let errorMessage = 'حدث خطأ أثناء تحديث بيانات الموظف';

    if (error.code === 'ECONNREFUSED') {
      errorMessage = 'خطأ في الاتصال بقاعدة البيانات';
    } else if (error.code === 'ELOGIN') {
      errorMessage = 'خطأ في تسجيل الدخول لقاعدة البيانات';
    } else if (error.number === 207) {
      errorMessage = 'خطأ في أسماء الأعمدة في قاعدة البيانات';
    } else if (error.number === 2627) {
      errorMessage = 'قيمة مكررة في قاعدة البيانات';
    }

    return NextResponse.json({
      success: false,
      message: errorMessage,
      error: error.message,
      details: process.env.NODE_ENV === 'development' ? {
        code: error.code,
        state: error.state,
        number: error.number
      } : undefined
    }, { status: 500 });
  } finally {
  }
}

export async function DELETE(request) {
  try {
    const { employeeCode, employeeId } = await request.json();
    const empCode = employeeCode || employeeId;

    const pool = await getConnection();
    if (!pool) {
      throw new Error('Failed to establish database connection');
    }

    const result = await pool.request()
      .input('EmployeeCode', sql.VarChar, empCode)
      .query('DELETE FROM Employees WHERE EmployeeCode = @EmployeeCode');

    if (result.rowsAffected[0] === 0) {
      return NextResponse.json({
        success: false,
        message: 'لم يتم العثور على الموظف'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      message: 'تم حذف الموظف بنجاح'
    });

  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        message: 'حدث خطأ أثناء حذف الموظف',
        error: error.message
      },
      { status: 500 }
    );
  }
}
