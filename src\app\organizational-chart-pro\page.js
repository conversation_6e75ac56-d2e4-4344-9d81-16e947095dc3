'use client';

import { useState, useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import MainLayout from '@/components/MainLayout';
import { Tree, TreeNode } from 'react-organizational-chart';
import styled from 'styled-components';
import {
  FiUsers,
  FiUser,
  FiSearch,
  FiFilter,
  FiDownload,
  FiZoomIn,
  FiZoomOut,
  FiMaximize,
  FiMail,
  FiPhone,
  FiMapPin,
  FiCalendar,
  FiAward,
  FiRefreshCw
} from 'react-icons/fi';

// تصميم مخصص للعقد
const StyledNode = styled.div`
  padding: 16px;
  border-radius: 12px;
  display: inline-block;
  border: 2px solid ${props => props.borderColor || '#e5e7eb'};
  background: ${props => props.isDarkMode ? '#1f2937' : '#ffffff'};
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  min-width: 280px;
  max-width: 320px;
  cursor: pointer;
  position: relative;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: ${props => props.hoverColor || '#3b82f6'};
  }

  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, ${props => props.gradientColors || '#3b82f6, #8b5cf6'});
    border-radius: 14px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover::before {
    opacity: 0.1;
  }
`;

const EmployeeAvatar = styled.div`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: ${props => props.bgColor || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 24px;
  margin: 0 auto 12px;
  border: 3px solid ${props => props.borderColor || '#ffffff'};
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
`;

const EmployeeInfo = styled.div`
  text-align: center;
  color: ${props => props.isDarkMode ? '#ffffff' : '#1f2937'};
`;

const EmployeeName = styled.h3`
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 700;
  color: ${props => props.isDarkMode ? '#ffffff' : '#1f2937'};
`;

const EmployeeTitle = styled.p`
  margin: 0 0 8px 0;
  font-size: 14px;
  color: ${props => props.isDarkMode ? '#9ca3af' : '#6b7280'};
  font-weight: 500;
`;

const EmployeeCode = styled.span`
  background: ${props => props.isDarkMode ? '#374151' : '#f3f4f6'};
  color: ${props => props.isDarkMode ? '#9ca3af' : '#6b7280'};
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
`;

const EmployeeStats = styled.div`
  display: flex;
  justify-content: space-around;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid ${props => props.isDarkMode ? '#374151' : '#e5e7eb'};
`;

const StatItem = styled.div`
  text-align: center;
  font-size: 12px;
  color: ${props => props.isDarkMode ? '#9ca3af' : '#6b7280'};
`;

export default function OrganizationalChartPro() {
  const { isDarkMode } = useTheme();

  const [employees, setEmployees] = useState([]);
  const [organizationTree, setOrganizationTree] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState('');
  const [departments, setDepartments] = useState([]);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [showEmployeeModal, setShowEmployeeModal] = useState(false);

  // ألوان مختلفة للمستويات الإدارية
  const levelColors = {
    1: { bg: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', border: '#667eea', hover: '#5a67d8' },
    2: { bg: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', border: '#f093fb', hover: '#e53e3e' },
    3: { bg: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', border: '#4facfe', hover: '#3182ce' },
    4: { bg: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', border: '#43e97b', hover: '#38a169' },
    5: { bg: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', border: '#fa709a', hover: '#d69e2e' }
  };

  useEffect(() => {
    fetchEmployees();
  }, []);

  // جلب بيانات الموظفين
  const fetchEmployees = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/organizational-chart-enhanced');
      const data = await response.json();

      if (data.success) {
        setEmployees(data.employees);
        setDepartments(data.departments);
        buildOrganizationTree(data.employees);
      } else {
        setError(data.error || 'فشل في جلب البيانات');
      }
    } catch (error) {

      setError('حدث خطأ في الاتصال بالخادم');
    } finally {
      setLoading(false);
    }
  };

  // بناء شجرة التنظيم
  const buildOrganizationTree = (employeesData) => {
    // العثور على المدير العام (الذي ليس له مدير مباشر)
    const topManager = employeesData.find(emp =>
      !emp.DirectManager1 || emp.DirectManager1 === '' || emp.DirectManager1 === null
    );

    if (!topManager) {
      setError('لم يتم العثور على المدير العام');
      return;
    }

    const tree = buildEmployeeNode(topManager, employeesData, 1);
    setOrganizationTree(tree);
  };

  // بناء عقدة موظف مع أطفاله
  const buildEmployeeNode = (employee, allEmployees, level) => {
    // العثور على المرؤوسين المباشرين
    const directReports = allEmployees.filter(emp =>
      emp.DirectManager1 === employee.EmployeeCode ||
      emp.DirectManager2 === employee.EmployeeCode ||
      emp.DirectManager3 === employee.EmployeeCode ||
      emp.DirectManager4 === employee.EmployeeCode
    );

    const children = directReports.map(report =>
      buildEmployeeNode(report, allEmployees, level + 1)
    );

    return {
      ...employee,
      level,
      children
    };
  };

  // مكون عقدة الموظف
  const EmployeeNode = ({ employee, onClick }) => {
    const colors = levelColors[employee.level] || levelColors[5];
    const initials = employee.EmployeeName
      ? employee.EmployeeName.split(' ').map(name => name[0]).join('').substring(0, 2)
      : 'غ م';

    return (
      <StyledNode
        isDarkMode={isDarkMode}
        borderColor={colors.border}
        hoverColor={colors.hover}
        gradientColors={`${colors.border}, ${colors.hover}`}
        onClick={() => onClick(employee)}
      >
        <EmployeeAvatar
          bgColor={colors.bg}
          borderColor={colors.border}
        >
          {initials}
        </EmployeeAvatar>

        <EmployeeInfo isDarkMode={isDarkMode}>
          <EmployeeName isDarkMode={isDarkMode}>
            {employee.EmployeeName || 'غير محدد'}
          </EmployeeName>

          <EmployeeTitle isDarkMode={isDarkMode}>
            {employee.JobTitle || 'غير محدد'}
          </EmployeeTitle>

          <EmployeeCode isDarkMode={isDarkMode}>
            كود: {employee.EmployeeCode}
          </EmployeeCode>

          <EmployeeStats isDarkMode={isDarkMode}>
            <StatItem isDarkMode={isDarkMode}>
              <FiUsers className="mx-auto mb-1" />
              <div>{employee.children?.length || 0}</div>
              <div>مرؤوس</div>
            </StatItem>

            <StatItem isDarkMode={isDarkMode}>
              <FiAward className="mx-auto mb-1" />
              <div>المستوى {employee.level}</div>
            </StatItem>
          </EmployeeStats>
        </EmployeeInfo>
      </StyledNode>
    );
  };

  // رندر شجرة التنظيم
  const renderTree = (node) => {
    if (!node) return null;

    return (
      <TreeNode
        key={node.EmployeeCode}
        label={<EmployeeNode employee={node} onClick={setSelectedEmployee} />}
      >
        {node.children && node.children.map(child => renderTree(child))}
      </TreeNode>
    );
  };

  // فلترة الموظفين
  const filteredEmployees = employees.filter(emp => {
    const matchesSearch = !searchTerm ||
      emp.EmployeeName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      emp.EmployeeCode?.includes(searchTerm);

    const matchesDepartment = !selectedDepartment ||
      emp.Department === selectedDepartment;

    return matchesSearch && matchesDepartment;
  });

  return (
    <MainLayout>
      <div className="max-w-full mx-auto p-6">
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FiUsers className="text-3xl text-blue-600" />
              <div>
                <h1 className={`text-3xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  الهيكل التنظيمي الاحترافي
                </h1>
                <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                  عرض تفاعلي للهيكل الإداري للشركة مع تصميم احترافي
                </p>
              </div>
            </div>

            <div className="flex gap-3">
              <button
                onClick={fetchEmployees}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
              >
                <FiRefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                تحديث البيانات
              </button>

              <button
                onClick={() => setZoomLevel(prev => Math.min(prev + 0.1, 2))}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <FiZoomIn className="w-4 h-4" />
                تكبير
              </button>

              <button
                onClick={() => setZoomLevel(prev => Math.max(prev - 0.1, 0.5))}
                className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <FiZoomOut className="w-4 h-4" />
                تصغير
              </button>
            </div>
          </div>
        </div>

        {/* أدوات التحكم والفلاتر */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* البحث */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                البحث في الموظفين
              </label>
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="اسم الموظف أو الكود..."
                  className={`w-full pl-10 pr-4 py-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
                />
              </div>
            </div>

            {/* فلتر القسم */}
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}>
                القسم
              </label>
              <select
                value={selectedDepartment}
                onChange={(e) => setSelectedDepartment(e.target.value)}
                className={`w-full p-3 border rounded-lg ${isDarkMode ? 'bg-slate-700 border-slate-600 text-white' : 'bg-white border-gray-300'}`}
              >
                <option value="">جميع الأقسام</option>
                {departments.map(dept => (
                  <option key={dept} value={dept}>{dept}</option>
                ))}
              </select>
            </div>

            {/* إحصائيات سريعة */}
            <div className="flex items-center justify-center">
              <div className="text-center">
                <div className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {filteredEmployees.length}
                </div>
                <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                  إجمالي الموظفين
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* الهيكل التنظيمي */}
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <FiRefreshCw className="w-8 h-8 animate-spin text-blue-500 mr-3" />
            <span className={`text-lg ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>جاري تحميل الهيكل التنظيمي...</span>
          </div>
        ) : error ? (
          <div className={`${isDarkMode ? 'bg-red-900 border-red-700' : 'bg-red-50 border-red-200'} border rounded-lg p-6 text-center`}>
            <div className={`text-lg font-semibold ${isDarkMode ? 'text-red-300' : 'text-red-800'} mb-2`}>
              خطأ في تحميل البيانات
            </div>
            <div className={`${isDarkMode ? 'text-red-400' : 'text-red-600'}`}>
              {error}
            </div>
            <button
              onClick={fetchEmployees}
              className="mt-4 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              إعادة المحاولة
            </button>
          </div>
        ) : organizationTree ? (
          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div
              className="overflow-auto"
              style={{
                transform: `scale(${zoomLevel})`,
                transformOrigin: 'top center',
                minHeight: '600px'
              }}
            >
              <Tree
                lineWidth="2px"
                lineColor={isDarkMode ? '#4b5563' : '#d1d5db'}
                lineBorderRadius="10px"
                label={<EmployeeNode employee={organizationTree} onClick={setSelectedEmployee} />}
              >
                {organizationTree.children && organizationTree.children.map(child => renderTree(child))}
              </Tree>
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <FiUsers className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <div className={`text-lg ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              لا توجد بيانات هيكل تنظيمي
            </div>
          </div>
        )}

        {/* مودال تفاصيل الموظف */}
        {selectedEmployee && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-xl border max-w-md w-full max-h-[90vh] overflow-y-auto`}>
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                    تفاصيل الموظف
                  </h3>
                  <button
                    onClick={() => setSelectedEmployee(null)}
                    className={`text-gray-400 hover:text-gray-600 text-2xl`}
                  >
                    ×
                  </button>
                </div>

                <div className="text-center mb-6">
                  <EmployeeAvatar
                    bgColor={levelColors[selectedEmployee.level]?.bg || levelColors[5].bg}
                    borderColor={levelColors[selectedEmployee.level]?.border || levelColors[5].border}
                  >
                    {selectedEmployee.EmployeeName
                      ? selectedEmployee.EmployeeName.split(' ').map(name => name[0]).join('').substring(0, 2)
                      : 'غ م'}
                  </EmployeeAvatar>

                  <h4 className={`text-lg font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-1`}>
                    {selectedEmployee.EmployeeName || 'غير محدد'}
                  </h4>

                  <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-600'} mb-2`}>
                    {selectedEmployee.JobTitle || 'غير محدد'}
                  </p>

                  <EmployeeCode isDarkMode={isDarkMode}>
                    كود: {selectedEmployee.EmployeeCode}
                  </EmployeeCode>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <FiMapPin className="text-blue-500" />
                    <div>
                      <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>القسم</div>
                      <div className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {selectedEmployee.Department || 'غير محدد'}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <FiAward className="text-green-500" />
                    <div>
                      <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>المستوى الإداري</div>
                      <div className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        المستوى {selectedEmployee.level}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <FiUsers className="text-purple-500" />
                    <div>
                      <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>عدد المرؤوسين</div>
                      <div className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                        {selectedEmployee.children?.length || 0} موظف
                      </div>
                    </div>
                  </div>

                  {selectedEmployee.DirectManager1 && (
                    <div className="flex items-center gap-3">
                      <FiUser className="text-orange-500" />
                      <div>
                        <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>المدير المباشر</div>
                        <div className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          {selectedEmployee.DirectManager1}
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                  <button
                    onClick={() => setSelectedEmployee(null)}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors"
                  >
                    إغلاق
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
