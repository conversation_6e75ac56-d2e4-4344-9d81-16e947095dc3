'use client';
import React from 'react';

function MainComponent() {
  const [selectedLang, setSelectedLang] = useState('ar');
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState('all');
  const [approvedLeaves, setApprovedLeaves] = useState([
    {
      id: 1,
      employeeId: 'EMP001',
      employeeName: 'أحمد محمد',
      leaveType: 'annual',
      startDate: '2025-01-15',
      endDate: '2025-01-20',
      approvalDate: '2025-01-10',
      approvedBy: 'محمد علي',
      status: 'approved',
    },
    {
      id: 2,
      employeeId: 'EMP002',
      employeeName: 'سارة أحمد',
      leaveType: 'sick',
      startDate: '2025-01-18',
      endDate: '2025-01-19',
      approvalDate: '2025-01-17',
      approvedBy: 'محمد علي',
      status: 'approved',
    },
  ]);

  const dir = selectedLang === 'ar' ? 'rtl' : 'ltr';

  const exportToExcel = async () => {
    try {
      const response = await fetch('/api/excel', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'export',
          data: approvedLeaves,
          template: {
            employeeId: selectedLang === 'ar' ? 'رقم الموظف' : 'Employee ID',
            employeeName:
              selectedLang === 'ar' ? 'اسم الموظف' : 'Employee Name',
            leaveType: selectedLang === 'ar' ? 'نوع الإجازة' : 'Leave Type',
            startDate: selectedLang === 'ar' ? 'تاريخ البداية' : 'Start Date',
            endDate: selectedLang === 'ar' ? 'تاريخ النهاية' : 'End Date',
            approvalDate:
              selectedLang === 'ar' ? 'تاريخ الموافقة' : 'Approval Date',
            approvedBy:
              selectedLang === 'ar' ? 'تمت الموافقة من قبل' : 'Approved By',
          },
        }),
      });

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error);
      }
    } catch (error) {

    }
  };

  const filteredLeaves = approvedLeaves.filter((leave) => {
    const matchesSearch =
      leave.employeeId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      leave.employeeName.toLowerCase().includes(searchTerm.toLowerCase());

    if (dateFilter === 'all') return matchesSearch;

    const today = new Date();
    const startDate = new Date(leave.startDate);

    switch (dateFilter) {
      case 'week':
        return (
          matchesSearch &&
          startDate <= new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000) &&
          startDate >= today
        );
      case 'month':
        return (
          matchesSearch &&
          startDate.getMonth() === today.getMonth() &&
          startDate.getFullYear() === today.getFullYear()
        );
      default:
        return matchesSearch;
    }
  });

  return (
    <div
      dir={dir}
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            {selectedLang === 'ar' ? 'الإجازات المعتمدة' : 'Approved Leaves'}
          </h1>
          <button
            onClick={() => setSelectedLang(selectedLang === 'ar' ? 'en' : 'ar')}
            className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
          >
            {selectedLang === 'ar' ? 'English' : 'العربية'}
          </button>
        </div>

        <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder={
                selectedLang === 'ar'
                  ? 'بحث برقم أو اسم الموظف'
                  : 'Search by ID or Name'
              }
              className="p-2 border border-gray-300 rounded-md"
              name="search"
            />
            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              className="p-2 border border-gray-300 rounded-md"
              name="dateFilter"
            >
              <option value="all">
                {selectedLang === 'ar' ? 'كل الإجازات' : 'All Leaves'}
              </option>
              <option value="week">
                {selectedLang === 'ar' ? 'الأسبوع القادم' : 'Next Week'}
              </option>
              <option value="month">
                {selectedLang === 'ar' ? 'الشهر الحالي' : 'Current Month'}
              </option>
            </select>
            <button
              onClick={exportToExcel}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
              {selectedLang === 'ar' ? 'تصدير إلى Excel' : 'Export to Excel'}
            </button>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-100 dark:bg-gray-700">
              <tr>
                <th className="px-4 py-2 text-right">
                  {selectedLang === 'ar' ? 'رقم الموظف' : 'Employee ID'}
                </th>
                <th className="px-4 py-2 text-right">
                  {selectedLang === 'ar' ? 'اسم الموظف' : 'Employee Name'}
                </th>
                <th className="px-4 py-2 text-right">
                  {selectedLang === 'ar' ? 'نوع الإجازة' : 'Leave Type'}
                </th>
                <th className="px-4 py-2 text-right">
                  {selectedLang === 'ar' ? 'تاريخ البداية' : 'Start Date'}
                </th>
                <th className="px-4 py-2 text-right">
                  {selectedLang === 'ar' ? 'تاريخ النهاية' : 'End Date'}
                </th>
                <th className="px-4 py-2 text-right">
                  {selectedLang === 'ar' ? 'تاريخ الموافقة' : 'Approval Date'}
                </th>
                <th className="px-4 py-2 text-right">
                  {selectedLang === 'ar'
                    ? 'تمت الموافقة من قبل'
                    : 'Approved By'}
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredLeaves.map((leave) => (
                <tr key={leave.id} className="border-b">
                  <td className="px-4 py-2">{leave.employeeId}</td>
                  <td className="px-4 py-2">{leave.employeeName}</td>
                  <td className="px-4 py-2">
                    {selectedLang === 'ar'
                      ? leave.leaveType === 'annual'
                        ? 'سنوية'
                        : 'مرضية'
                      : leave.leaveType === 'annual'
                        ? 'Annual'
                        : 'Sick'}
                  </td>
                  <td className="px-4 py-2">
                    {new Date(leave.startDate).toLocaleDateString()}
                  </td>
                  <td className="px-4 py-2">
                    {new Date(leave.endDate).toLocaleDateString()}
                  </td>
                  <td className="px-4 py-2">
                    {new Date(leave.approvalDate).toLocaleDateString()}
                  </td>
                  <td className="px-4 py-2">{leave.approvedBy}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default MainComponent;
