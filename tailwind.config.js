/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: 'class',
  "content": [
    "./src/**/*.{js,jsx,ts,tsx}"
  ],
  "theme": {
    "extend": {
      "textColor": {
        "foreground": "var(--foreground-color, #000000)"
      },
      "backgroundColor": {
        "background": "var(--background-color, #ffffff)"
      },
      "borderColor": {
        "border": "var(--border-color, #e5e7eb)"
      },
      "fontFamily": {
        "sans": [
          "system-ui",
          "-apple-system",
          "BlinkMacSystemFont",
          "Segoe UI",
          "Roboto",
          "Helvetica Neue",
          "Arial",
          "Noto Sans",
          "sans-serif",
          "Apple Color Emoji",
          "Segoe UI Emoji",
          "Segoe UI Symbol",
          "Noto Color Emoji"
        ],
        "mono": [
          "ui-monospace",
          "SFMono-Regular",
          "Monaco",
          "Consolas",
          "Liberation Mono",
          "Courier New",
          "monospace"
        ],
        "display": [
          "system-ui",
          "-apple-system",
          "BlinkMacSystemFont",
          "Segoe UI",
          "Roboto",
          "Helvetica Neue",
          "Arial",
          "sans-serif"
        ],
        "body": [
          "system-ui",
          "-apple-system",
          "BlinkMacSystemFont",
          "Segoe UI",
          "Roboto",
          "Helvetica Neue",
          "Arial",
          "sans-serif"
        ]
      }
    }
  },
  "plugins": [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography')
  ]
}