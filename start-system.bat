@echo off
title تشغيل نظام إدارة التكاليف
color 0A

echo ========================================
echo    تشغيل نظام إدارة التكاليف
echo ========================================
echo.

:: التحقق من وجود Node.js
echo [1/4] التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير مثبت على النظام
    echo يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)
echo ✅ Node.js متاح

:: التحقق من وجود npm
echo [2/4] التحقق من npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: npm غير متاح
    pause
    exit /b 1
)
echo ✅ npm متاح

:: الانتقال إلى مجلد المشروع
echo [3/4] الانتقال إلى مجلد المشروع...
cd /d "E:\web\project"
if %errorlevel% neq 0 (
    echo ❌ خطأ: لا يمكن الوصول إلى مجلد المشروع
    echo تأكد من وجود المجلد: E:\web\project
    pause
    exit /b 1
)
echo ✅ تم الانتقال إلى مجلد المشروع

:: تثبيت التبعيات إذا لم تكن موجودة
echo [4/4] التحقق من التبعيات...
if not exist "node_modules" (
    echo 📦 تثبيت التبعيات...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ خطأ في تثبيت التبعيات
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت التبعيات بنجاح
) else (
    echo ✅ التبعيات موجودة
)

echo.
echo ========================================
echo 🚀 بدء تشغيل الخادم...
echo ========================================
echo.
echo 📍 الخادم سيعمل على: http://localhost:3001
echo 🔄 لإيقاف الخادم اضغط: Ctrl + C
echo 🌐 لفتح النظام في المتصفح اضغط: Ctrl + Click على الرابط
echo.

:: تشغيل الخادم
npm run dev

:: في حالة إغلاق الخادم
echo.
echo ========================================
echo تم إيقاف الخادم
echo ========================================
pause
