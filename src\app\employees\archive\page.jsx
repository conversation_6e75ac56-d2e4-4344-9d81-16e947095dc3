'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import {
  FiSearch,
  FiUser,
  FiFileText,
  FiImage,
  FiDownload,
  FiEye,
  FiFilter,
  FiRefreshCw,
  FiFolder,
  FiGrid,
  FiList
} from 'react-icons/fi';

export default function EmployeeArchive() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [employees, setEmployees] = useState([]);
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [documentsLoading, setDocumentsLoading] = useState(false);
  const [viewMode, setViewMode] = useState('grid'); // grid or list
  const [filterType, setFilterType] = useState('all');

  // Document type mappings with colors and icons
  const documentTypes = {
    'NationalIDs': {
      name: 'بطاقة الرقم القومى',
      icon: 'fa-id-card',
      color: '#2563eb',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    'WorkReceipts': {
      name: 'إستلام العمل',
      icon: 'fa-handshake',
      color: '#ea580c',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200'
    },
    'StatusReports': {
      name: 'بيان حالة إجتماعية',
      icon: 'fa-file-alt',
      color: '#7c3aed',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200'
    },
    'UnionCards': {
      name: 'كارينة النقابة',
      icon: 'fa-id-badge',
      color: '#0891b2',
      bgColor: 'bg-cyan-50',
      borderColor: 'border-cyan-200'
    },
    'photo': {
      name: 'صورة شخصية PDF',
      icon: 'fa-file-pdf',
      color: '#dc2626',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200'
    },
    'pic': {
      name: 'صورة شخصية JPG',
      icon: 'fa-image',
      color: '#059669',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    },
    'Bh': {
      name: 'بدل إعاشة',
      icon: 'fa-utensils',
      color: '#65a30d',
      bgColor: 'bg-lime-50',
      borderColor: 'border-lime-200'
    },
    'Bt': {
      name: 'بدل إنتقال',
      icon: 'fa-bus',
      color: '#be185d',
      bgColor: 'bg-pink-50',
      borderColor: 'border-pink-200'
    }
  };

  // Search for employees
  const searchEmployees = async (term) => {
    if (!term || term.length < 2) {
      setEmployees([]);
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/employee-live-search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ searchTerm: term })
      });

      const data = await response.json();
      if (data.success) {
        setEmployees(data.employees || []);
      }
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  // Load employee documents
  const loadEmployeeDocuments = async (employeeId) => {
    setDocumentsLoading(true);
    try {
      const response = await fetch(`/api/employee-archive?action=getDocuments&employeeId=${employeeId}`);
      const data = await response.json();

      if (data.success) {
        setDocuments(data.documents || []);
      } else {
        setDocuments([]);
      }
    } catch (error) {

      setDocuments([]);
    } finally {
      setDocumentsLoading(false);
    }
  };

  // Handle employee selection
  const selectEmployee = (employee) => {
    setSelectedEmployee(employee);
    loadEmployeeDocuments(employee.EmployeeID);
  };

  // Filter documents by type
  const filteredDocuments = documents.filter(doc => {
    if (filterType === 'all') return true;
    return doc.Item === filterType;
  });

  // Handle document view
  const viewDocument = (doc) => {
    let webPath = doc.Path.replace(/\\/g, '/');
    const archivIndex = webPath.indexOf('archiv/');
    if (archivIndex !== -1) {
      webPath = webPath.substring(archivIndex);
    }
    window.open(`/${webPath}`, '_blank');
  };

  // Handle document download
  const downloadDocument = (doc) => {
    let webPath = doc.Path.replace(/\\/g, '/');
    const archivIndex = webPath.indexOf('archiv/');
    if (archivIndex !== -1) {
      webPath = webPath.substring(archivIndex);
    }

    const link = document.createElement('a');
    link.href = `/${webPath}`;
    link.download = `${selectedEmployee.Name}_${documentTypes[doc.Item]?.name || doc.Item}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchEmployees(searchTerm);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-800 mb-2 flex items-center gap-3">
                <FiFolder className="text-blue-600" />
                أرشيف مستندات الموظفين
              </h1>
              <p className="text-gray-600">
                البحث عن مستندات الموظفين وعرضها وتحميلها
              </p>
            </div>
            <div className="flex gap-3">
              <button
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 flex items-center gap-2"
              >
                {viewMode === 'grid' ? <FiList /> : <FiGrid />}
                {viewMode === 'grid' ? 'عرض قائمة' : 'عرض شبكة'}
              </button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Employee Search Panel */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                <FiSearch className="text-blue-600" />
                البحث عن موظف
              </h2>

              {/* Search Input */}
              <div className="relative mb-4">
                <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="ابحث بكود الموظف أو الاسم..."
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Search Results */}
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {loading ? (
                  <div className="text-center py-4">
                    <FiRefreshCw className="animate-spin text-2xl text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">جاري البحث...</p>
                  </div>
                ) : employees.length > 0 ? (
                  employees.map((employee) => (
                    <div
                      key={employee.EmployeeID}
                      onClick={() => selectEmployee(employee)}
                      className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                        selectedEmployee?.EmployeeID === employee.EmployeeID
                          ? 'bg-blue-50 border-blue-300'
                          : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <FiUser className="text-gray-600" />
                        <div>
                          <div className="font-medium text-gray-800">{employee.Name}</div>
                          <div className="text-sm text-gray-500">كود: {employee.EmployeeID}</div>
                          <div className="text-xs text-gray-400">{employee.Department}</div>
                        </div>
                      </div>
                    </div>
                  ))
                ) : searchTerm.length >= 2 ? (
                  <div className="text-center py-4 text-gray-500">
                    لا توجد نتائج للبحث
                  </div>
                ) : (
                  <div className="text-center py-4 text-gray-400">
                    ابدأ بكتابة اسم الموظف أو كوده
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Documents Panel */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm p-6">
              {selectedEmployee ? (
                <>
                  {/* Employee Info Header */}
                  <div className="flex items-center justify-between mb-6 pb-4 border-b">
                    <div>
                      <h2 className="text-xl font-bold text-gray-800">
                        مستندات الموظف: {selectedEmployee.Name}
                      </h2>
                      <p className="text-gray-600">
                        كود الموظف: {selectedEmployee.EmployeeID} | القسم: {selectedEmployee.Department}
                      </p>
                    </div>

                    {/* Document Type Filter */}
                    <div className="flex items-center gap-3">
                      <FiFilter className="text-gray-600" />
                      <select
                        value={filterType}
                        onChange={(e) => setFilterType(e.target.value)}
                        className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="all">جميع المستندات</option>
                        {Object.entries(documentTypes).map(([key, type]) => (
                          <option key={key} value={key}>{type.name}</option>
                        ))}
                      </select>
                    </div>
                  </div>

                  {/* Documents Display */}
                  {documentsLoading ? (
                    <div className="text-center py-12">
                      <FiRefreshCw className="animate-spin text-4xl text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600">جاري تحميل المستندات...</p>
                    </div>
                  ) : filteredDocuments.length > 0 ? (
                    <div className={`${viewMode === 'grid'
                      ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4'
                      : 'space-y-3'
                    }`}>
                      {filteredDocuments.map((doc, index) => {
                        const docType = documentTypes[doc.Item] || {
                          name: doc.Item,
                          icon: 'fa-file',
                          color: '#6b7280',
                          bgColor: 'bg-gray-50',
                          borderColor: 'border-gray-200'
                        };

                        return viewMode === 'grid' ? (
                          // Grid View
                          <div
                            key={index}
                            className={`${docType.bgColor} ${docType.borderColor} border rounded-lg p-4 hover:shadow-md transition-all`}
                          >
                            <div className="flex items-center gap-3 mb-4">
                              <i
                                className={`fas ${docType.icon} text-2xl`}
                                style={{ color: docType.color }}
                              />
                              <div>
                                <h4 className="font-semibold text-gray-800">{docType.name}</h4>
                                <p className="text-xs text-gray-500">نوع الملف: {doc.Item}</p>
                              </div>
                            </div>

                            <div className="flex gap-2">
                              <button
                                onClick={() => viewDocument(doc)}
                                className="flex-1 bg-blue-600 text-white py-2 px-3 rounded hover:bg-blue-700 transition-colors text-sm flex items-center justify-center gap-1"
                              >
                                <FiEye />
                                عرض
                              </button>
                              <button
                                onClick={() => downloadDocument(doc)}
                                className="flex-1 bg-green-600 text-white py-2 px-3 rounded hover:bg-green-700 transition-colors text-sm flex items-center justify-center gap-1"
                              >
                                <FiDownload />
                                تحميل
                              </button>
                            </div>
                          </div>
                        ) : (
                          // List View
                          <div
                            key={index}
                            className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border hover:shadow-sm transition-all"
                          >
                            <div className="flex items-center gap-4">
                              <i
                                className={`fas ${docType.icon} text-xl`}
                                style={{ color: docType.color }}
                              />
                              <div>
                                <h4 className="font-medium text-gray-800">{docType.name}</h4>
                                <p className="text-sm text-gray-500">نوع: {doc.Item}</p>
                              </div>
                            </div>

                            <div className="flex gap-2">
                              <button
                                onClick={() => viewDocument(doc)}
                                className="bg-blue-600 text-white py-1 px-3 rounded hover:bg-blue-700 transition-colors text-sm flex items-center gap-1"
                              >
                                <FiEye />
                                عرض
                              </button>
                              <button
                                onClick={() => downloadDocument(doc)}
                                className="bg-green-600 text-white py-1 px-3 rounded hover:bg-green-700 transition-colors text-sm flex items-center gap-1"
                              >
                                <FiDownload />
                                تحميل
                              </button>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <FiFileText className="text-6xl text-gray-300 mx-auto mb-4" />
                      <h3 className="text-xl font-medium text-gray-600 mb-2">لا توجد مستندات</h3>
                      <p className="text-gray-500">
                        {filterType === 'all'
                          ? 'لا توجد مستندات مؤرشفة لهذا الموظف'
                          : `لا توجد مستندات من نوع "${documentTypes[filterType]?.name}"`
                        }
                      </p>
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center py-12">
                  <FiUser className="text-6xl text-gray-300 mx-auto mb-4" />
                  <h3 className="text-xl font-medium text-gray-600 mb-2">اختر موظفاً</h3>
                  <p className="text-gray-500">
                    ابحث عن موظف من القائمة الجانبية لعرض مستنداته
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
