async function handler({ xmlContent }) {
  const session = getSession();
  if (!session?.user) {
    return { error: "غير مصرح" };
  }

  try {
    // Parse XML content
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlContent, "text/xml");
    const employees = xmlDoc.getElementsByTagName("employee");

    const results = {
      success: [],
      errors: [],
      duplicates: [],
    };

    // Process each employee
    for (const employee of employees) {
      try {
        const employeeData = {
          employee_code: employee.getAttribute("id"),
          name: employee.getElementsByTagName("name")[0]?.textContent,
          national_id:
            employee.getElementsByTagName("national_id")[0]?.textContent,
          birth_date:
            employee.getElementsByTagName("birth_date")[0]?.textContent,
          hire_date: employee.getElementsByTagName("hire_date")[0]?.textContent,
          department:
            employee.getElementsByTagName("department")[0]?.textContent,
          position: employee.getElementsByTagName("job_title")[0]?.textContent,
          education_info: JSON.stringify({
            level:
              employee.getElementsByTagName("education_level")[0]?.textContent,
            university:
              employee.getElementsByTagName("university")[0]?.textContent,
            graduationYear:
              employee.getElementsByTagName("graduation_year")[0]?.textContent,
          }),
          contact_info: JSON.stringify({
            phone:
              employee.getElementsByTagName("phone_number")[0]?.textContent,
          }),
        };

        // Check for required fields
        if (!employeeData.employee_code || !employeeData.name) {
          results.errors.push({
            code: employeeData.employee_code,
            error: "بيانات إلزامية مفقودة",
          });
          continue;
        }

        // Check for existing employee
        const existing = await sql`
          SELECT id FROM employees 
          WHERE employee_code = ${employeeData.employee_code} 
          OR national_id = ${employeeData.national_id}`;

        if (existing.length > 0) {
          results.duplicates.push({
            code: employeeData.employee_code,
            message: "الموظف موجود مسبقاً",
          });
          continue;
        }

        // Insert new employee
        const result = await sql`
          INSERT INTO employees (
            employee_code, name, national_id, birth_date, hire_date,
            department, position, education_info, contact_info,
            status, created_at, updated_at
          ) VALUES (
            ${employeeData.employee_code},
            ${employeeData.name},
            ${employeeData.national_id},
            ${employeeData.birth_date},
            ${employeeData.hire_date},
            ${employeeData.department},
            ${employeeData.position},
            ${employeeData.education_info},
            ${employeeData.contact_info},
            'active',
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
          ) RETURNING id`;

        results.success.push({
          code: employeeData.employee_code,
          id: result[0].id,
        });
      } catch (error) {

        results.errors.push({
          code: employee.getAttribute("id"),
          error: "خطأ في معالجة بيانات الموظف",
        });
      }
    }

    return {
      success: true,
      results: {
        total: employees.length,
        imported: results.success.length,
        duplicates: results.duplicates.length,
        errors: results.errors.length,
        details: results,
      },
    };
  } catch (error) {

    return {
      error: "فشل في معالجة ملف XML",
      details: error.message,
    };
  }
}
export async function POST(request) {
  return handler(await request.json());
}