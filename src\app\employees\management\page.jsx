'use client';
import React from 'react';

function MainComponent() {
  const [selectedLang, setSelectedLang] = useState('ar');
  const [employees, setEmployees] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchParams, setSearchParams] = useState({
    code: '',
    name: '',
    department: '',
    governorate: '',
    nationalId: '',
    phone: '',
  });
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [showDocuments, setShowDocuments] = useState(false);
  const [documents, setDocuments] = useState([]);

  const dir = selectedLang === 'ar' ? 'rtl' : 'ltr';

  useEffect(() => {
    fetchEmployees();
  }, []);

  const fetchEmployees = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/data-service', {
        method: 'POST',
        body: JSON.stringify({
          table: 'employees',
          action: 'list',
        }),
      });
      if (!response.ok) {
        throw new Error('Failed to fetch employees');
      }
      const result = await response.json();
      setEmployees(result.data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    const filteredEmployees = employees.filter((emp) => {
      return Object.entries(searchParams).every(([key, value]) => {
        if (!value) return true;
        return emp[key]?.toLowerCase().includes(value.toLowerCase());
      });
    });
    setEmployees(filteredEmployees);
  };

  const handleStatusUpdate = async (employeeId, status) => {
    try {
      const response = await fetch('/api/data-service', {
        method: 'POST',
        body: JSON.stringify({
          table: 'employees',
          action: 'update',
          id: employeeId,
          data: { status },
        }),
      });
      if (!response.ok) {
        throw new Error('Failed to update status');
      }
      fetchEmployees();
    } catch (err) {
      setError(err.message);
    }
  };

  const fetchDocuments = async (employeeId) => {
    try {
      const response = await fetch('/api/data-service', {
        method: 'POST',
        body: JSON.stringify({
          table: 'employee_documents',
          action: 'list',
          filter: { employeeId },
        }),
      });
      if (!response.ok) {
        throw new Error('Failed to fetch documents');
      }
      const result = await response.json();
      setDocuments(result.data);
    } catch (err) {
      setError(err.message);
    }
  };

  return (
    <div
      dir={dir}
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          {selectedLang === 'ar' ? 'إدارة الموظفين' : 'Employee Management'}
        </h1>
        <button
          onClick={() => setSelectedLang(selectedLang === 'ar' ? 'en' : 'ar')}
          className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
        >
          {selectedLang === 'ar' ? 'English' : 'العربية'}
        </button>
      </div>

      <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <input
            type="text"
            value={searchParams.code}
            onChange={(e) =>
              setSearchParams({ ...searchParams, code: e.target.value })
            }
            placeholder={selectedLang === 'ar' ? 'رقم الموظف' : 'Employee Code'}
            className="p-2 border border-gray-300 rounded-md"
            name="code"
          />
          <input
            type="text"
            value={searchParams.name}
            onChange={(e) =>
              setSearchParams({ ...searchParams, name: e.target.value })
            }
            placeholder={selectedLang === 'ar' ? 'اسم الموظف' : 'Employee Name'}
            className="p-2 border border-gray-300 rounded-md"
            name="name"
          />
          <input
            type="text"
            value={searchParams.department}
            onChange={(e) =>
              setSearchParams({ ...searchParams, department: e.target.value })
            }
            placeholder={selectedLang === 'ar' ? 'القسم' : 'Department'}
            className="p-2 border border-gray-300 rounded-md"
            name="department"
          />
          <input
            type="text"
            value={searchParams.governorate}
            onChange={(e) =>
              setSearchParams({ ...searchParams, governorate: e.target.value })
            }
            placeholder={selectedLang === 'ar' ? 'المحافظة' : 'Governorate'}
            className="p-2 border border-gray-300 rounded-md"
            name="governorate"
          />
          <input
            type="text"
            value={searchParams.nationalId}
            onChange={(e) =>
              setSearchParams({ ...searchParams, nationalId: e.target.value })
            }
            placeholder={selectedLang === 'ar' ? 'الرقم القومي' : 'National ID'}
            className="p-2 border border-gray-300 rounded-md"
            name="nationalId"
          />
          <input
            type="text"
            value={searchParams.phone}
            onChange={(e) =>
              setSearchParams({ ...searchParams, phone: e.target.value })
            }
            placeholder={selectedLang === 'ar' ? 'رقم الهاتف' : 'Phone Number'}
            className="p-2 border border-gray-300 rounded-md"
            name="phone"
          />
        </div>
        <button
          onClick={handleSearch}
          className="mt-4 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          {selectedLang === 'ar' ? 'بحث' : 'Search'}
        </button>
      </div>

      {loading ? (
        <div className="text-center py-4">
          {selectedLang === 'ar' ? 'جاري التحميل...' : 'Loading...'}
        </div>
      ) : error ? (
        <div className="text-red-600 text-center py-4">{error}</div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {employees.map((employee) => (
            <div
              key={employee.id}
              className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow"
            >
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="text-xl font-semibold">{employee.name}</h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    {employee.code}
                  </p>
                </div>
                <select
                  value={employee.status}
                  onChange={(e) =>
                    handleStatusUpdate(employee.id, e.target.value)
                  }
                  className="p-1 border border-gray-300 rounded-md"
                >
                  <option value="active">
                    {selectedLang === 'ar' ? 'نشط' : 'Active'}
                  </option>
                  <option value="inactive">
                    {selectedLang === 'ar' ? 'غير نشط' : 'Inactive'}
                  </option>
                  <option value="onLeave">
                    {selectedLang === 'ar' ? 'في إجازة' : 'On Leave'}
                  </option>
                </select>
              </div>

              <div className="space-y-2">
                <p>
                  <i className="fas fa-building"></i> {employee.department}
                </p>
                <p>
                  <i className="fas fa-map-marker-alt"></i>{' '}
                  {employee.governorate}
                </p>
                <p>
                  <i className="fas fa-id-card"></i> {employee.nationalId}
                </p>
                <p>
                  <i className="fas fa-phone"></i> {employee.phone}
                </p>
              </div>

              <div className="mt-4 flex gap-2">
                <button
                  onClick={() => {
                    setSelectedEmployee(employee);
                    fetchDocuments(employee.id);
                    setShowDocuments(true);
                  }}
                  className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-md"
                >
                  {selectedLang === 'ar' ? 'المستندات' : 'Documents'}
                </button>
                <button
                  onClick={() => setSelectedEmployee(employee)}
                  className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-md"
                >
                  {selectedLang === 'ar' ? 'البدلات' : 'Allowances'}
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {showDocuments && selectedEmployee && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg w-full max-w-2xl">
            <h2 className="text-2xl font-bold mb-4">
              {selectedLang === 'ar' ? 'مستندات الموظف' : 'Employee Documents'}{' '}
              - {selectedEmployee.name}
            </h2>
            <div className="space-y-4">
              {documents.map((doc) => (
                <div
                  key={doc.id}
                  className="flex justify-between items-center p-2 border-b"
                >
                  <span>{doc.name}</span>
                  <div className="flex gap-2">
                    <button className="text-blue-600 hover:text-blue-800">
                      <i className="fas fa-download"></i>
                    </button>
                    <button className="text-red-600 hover:text-red-800">
                      <i className="fas fa-trash"></i>
                    </button>
                  </div>
                </div>
              ))}
            </div>
            <button
              onClick={() => setShowDocuments(false)}
              className="mt-4 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-md"
            >
              {selectedLang === 'ar' ? 'إغلاق' : 'Close'}
            </button>
          </div>
        </div>
      )}

      {error && (
        <div className="fixed bottom-4 right-4 bg-red-100 text-red-700 p-4 rounded-md shadow">
          {error}
        </div>
      )}
    </div>
  );
}

export default MainComponent;
