import { NextResponse } from 'next/server';
import sql from 'mssql';

// إعداد الاتصال بقاعدة البيانات
const config = {
  user: process.env.DB_USER || 'sa',
  password: process.env.DB_PASSWORD || 'MyStrongPassword123',
  server: process.env.DB_SERVER || 'localhost',
  database: process.env.DB_NAME || 'EmployeeManagement',
  options: {
    encrypt: false,
    trustServerCertificate: true,
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
};

// إنشاء جداول المؤثرات الشهرية
async function createMonthlyEffectsTables(pool) {
  try {
    // 1. جدول المؤثرات الشهرية
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MonthlyEffects' AND xtype='U')
      BEGIN
        CREATE TABLE MonthlyEffects (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          EmployeeCode NVARCHAR(20) NOT NULL,
          EmployeeName NVARCHAR(100) NOT NULL,
          Department NVARCHAR(100),
          JobTitle NVARCHAR(100),
          EffectType NVARCHAR(50) NOT NULL, -- missions, unpaid_leave, sick_leave, penalties, holiday_work, rest_allowance
          EffectDate DATE NOT NULL,
          Month INT NOT NULL,
          Year INT NOT NULL,
          Amount DECIMAL(10,2) NOT NULL DEFAULT 0,
          Days INT DEFAULT 0, -- عدد الأيام للإجازات والمأموريات
          Hours DECIMAL(5,2) DEFAULT 0, -- عدد الساعات للعمل الإضافي
          Description NVARCHAR(500),
          Notes NVARCHAR(MAX),
          IsActive BIT DEFAULT 1,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),
          CreatedBy NVARCHAR(100),
          
          INDEX idx_employee_month (EmployeeCode, Month, Year),
          INDEX idx_effect_type (EffectType),
          INDEX idx_effect_date (EffectDate)
        )
      END
    `);

    // 2. جدول ملخص المؤثرات الشهرية
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MonthlyEffectsSummary' AND xtype='U')
      BEGIN
        CREATE TABLE MonthlyEffectsSummary (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          Month INT NOT NULL,
          Year INT NOT NULL,
          TotalEmployees INT DEFAULT 0,
          TotalMissions DECIMAL(15,2) DEFAULT 0,
          TotalUnpaidLeave DECIMAL(15,2) DEFAULT 0,
          TotalSickLeave DECIMAL(15,2) DEFAULT 0,
          TotalPenalties DECIMAL(15,2) DEFAULT 0,
          TotalHolidayWork DECIMAL(15,2) DEFAULT 0,
          TotalRestAllowance DECIMAL(15,2) DEFAULT 0,
          TotalPositive DECIMAL(15,2) DEFAULT 0,
          TotalNegative DECIMAL(15,2) DEFAULT 0,
          NetEffect DECIMAL(15,2) DEFAULT 0,
          IsCalculated BIT DEFAULT 0,
          CalculatedAt DATETIME,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),
          
          UNIQUE(Month, Year)
        )
      END
    `);

    return true;

  } catch (error) {

    throw error;
  }
}

// معالج الطلبات
export async function POST(request) {
  let pool;
  
  try {
    const data = await request.json();
    const { action } = data;

    // الاتصال بقاعدة البيانات
    pool = await sql.connect(config);

    // إنشاء الجداول إذا لم تكن موجودة
    await createMonthlyEffectsTables(pool);

    switch (action) {
      case 'list':
        return await getMonthlyEffects(pool, data);
      case 'add':
        return await addMonthlyEffect(pool, data);
      case 'update':
        return await updateMonthlyEffect(pool, data);
      case 'delete':
        return await deleteMonthlyEffect(pool, data);
      case 'calculate':
        return await calculateMonthlySummary(pool, data);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      try {
        await pool.close();
      } catch (error) {

      }
    }
  }
}

// جلب المؤثرات الشهرية
async function getMonthlyEffects(pool, data) {
  try {
    const { month, year, effectType, department, employeeCode } = data;

    let whereClause = 'WHERE 1=1';
    const request = pool.request();

    if (month) {
      whereClause += ' AND Month = @month';
      request.input('month', sql.Int, month);
    }

    if (year) {
      whereClause += ' AND Year = @year';
      request.input('year', sql.Int, year);
    }

    if (effectType) {
      whereClause += ' AND EffectType = @effectType';
      request.input('effectType', sql.NVarChar, effectType);
    }

    if (department) {
      whereClause += ' AND Department = @department';
      request.input('department', sql.NVarChar, department);
    }

    if (employeeCode) {
      whereClause += ' AND EmployeeCode = @employeeCode';
      request.input('employeeCode', sql.NVarChar, employeeCode);
    }

    // جلب المؤثرات
    const effectsQuery = `
      SELECT
        ID,
        EmployeeCode,
        EmployeeName,
        Department,
        JobTitle,
        EffectType,
        EffectDate,
        Month,
        Year,
        Amount,
        Days,
        Hours,
        Description,
        Notes,
        CreatedAt
      FROM MonthlyEffects
      ${whereClause}
      ORDER BY EffectDate DESC, EmployeeName
    `;

    const effectsResult = await request.query(effectsQuery);

    // جلب الملخص
    const summaryResult = await pool.request()
      .input('month', sql.Int, month || new Date().getMonth() + 1)
      .input('year', sql.Int, year || new Date().getFullYear())
      .query(`
        SELECT
          COUNT(DISTINCT EmployeeCode) as TotalEmployees,
          ISNULL(SUM(CASE WHEN EffectType = 'missions' THEN Amount ELSE 0 END), 0) as TotalMissions,
          ISNULL(SUM(CASE WHEN EffectType = 'unpaid_leave' THEN Amount ELSE 0 END), 0) as TotalUnpaidLeave,
          ISNULL(SUM(CASE WHEN EffectType = 'sick_leave' THEN Amount ELSE 0 END), 0) as TotalSickLeave,
          ISNULL(SUM(CASE WHEN EffectType = 'penalties' THEN Amount ELSE 0 END), 0) as TotalPenalties,
          ISNULL(SUM(CASE WHEN EffectType = 'holiday_work' THEN Amount ELSE 0 END), 0) as TotalHolidayWork,
          ISNULL(SUM(CASE WHEN EffectType = 'rest_allowance' THEN Amount ELSE 0 END), 0) as TotalRestAllowance,
          ISNULL(SUM(CASE WHEN EffectType IN ('missions', 'holiday_work', 'rest_allowance') THEN Amount ELSE 0 END), 0) as TotalPositive,
          ISNULL(SUM(CASE WHEN EffectType IN ('unpaid_leave', 'sick_leave', 'penalties') THEN Amount ELSE 0 END), 0) as TotalNegative
        FROM MonthlyEffects
        WHERE Month = @month AND Year = @year AND IsActive = 1
      `);

    const summary = summaryResult.recordset[0] || {};
    summary.netEffect = (summary.TotalPositive || 0) - (summary.TotalNegative || 0);

    return NextResponse.json({
      success: true,
      data: effectsResult.recordset,
      summary: summary
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب المؤثرات الشهرية: ' + error.message
    }, { status: 500 });
  }
}

// إضافة مؤثر شهري
async function addMonthlyEffect(pool, data) {
  try {
    const {
      employeeCode,
      employeeName,
      department,
      jobTitle,
      effectType,
      effectDate,
      amount,
      days,
      hours,
      description,
      notes
    } = data;

    // استخراج الشهر والسنة من التاريخ
    const date = new Date(effectDate);
    const month = date.getMonth() + 1;
    const year = date.getFullYear();

    const result = await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .input('employeeName', sql.NVarChar, employeeName)
      .input('department', sql.NVarChar, department)
      .input('jobTitle', sql.NVarChar, jobTitle)
      .input('effectType', sql.NVarChar, effectType)
      .input('effectDate', sql.Date, effectDate)
      .input('month', sql.Int, month)
      .input('year', sql.Int, year)
      .input('amount', sql.Decimal(10, 2), amount || 0)
      .input('days', sql.Int, days || 0)
      .input('hours', sql.Decimal(5, 2), hours || 0)
      .input('description', sql.NVarChar, description)
      .input('notes', sql.NVarChar, notes)
      .query(`
        INSERT INTO MonthlyEffects (
          EmployeeCode, EmployeeName, Department, JobTitle, EffectType,
          EffectDate, Month, Year, Amount, Days, Hours, Description, Notes
        )
        VALUES (
          @employeeCode, @employeeName, @department, @jobTitle, @effectType,
          @effectDate, @month, @year, @amount, @days, @hours, @description, @notes
        )
      `);

    return NextResponse.json({
      success: true,
      message: 'تم إضافة المؤثر بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في إضافة المؤثر: ' + error.message
    }, { status: 500 });
  }
}

// تحديث مؤثر شهري
async function updateMonthlyEffect(pool, data) {
  try {
    const {
      id,
      employeeCode,
      employeeName,
      department,
      jobTitle,
      effectType,
      effectDate,
      amount,
      days,
      hours,
      description,
      notes
    } = data;

    // استخراج الشهر والسنة من التاريخ
    const date = new Date(effectDate);
    const month = date.getMonth() + 1;
    const year = date.getFullYear();

    const result = await pool.request()
      .input('id', sql.Int, id)
      .input('employeeCode', sql.NVarChar, employeeCode)
      .input('employeeName', sql.NVarChar, employeeName)
      .input('department', sql.NVarChar, department)
      .input('jobTitle', sql.NVarChar, jobTitle)
      .input('effectType', sql.NVarChar, effectType)
      .input('effectDate', sql.Date, effectDate)
      .input('month', sql.Int, month)
      .input('year', sql.Int, year)
      .input('amount', sql.Decimal(10, 2), amount || 0)
      .input('days', sql.Int, days || 0)
      .input('hours', sql.Decimal(5, 2), hours || 0)
      .input('description', sql.NVarChar, description)
      .input('notes', sql.NVarChar, notes)
      .query(`
        UPDATE MonthlyEffects SET
          EmployeeCode = @employeeCode,
          EmployeeName = @employeeName,
          Department = @department,
          JobTitle = @jobTitle,
          EffectType = @effectType,
          EffectDate = @effectDate,
          Month = @month,
          Year = @year,
          Amount = @amount,
          Days = @days,
          Hours = @hours,
          Description = @description,
          Notes = @notes,
          UpdatedAt = GETDATE()
        WHERE ID = @id
      `);

    return NextResponse.json({
      success: true,
      message: 'تم تحديث المؤثر بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث المؤثر: ' + error.message
    }, { status: 500 });
  }
}

// حذف مؤثر شهري
async function deleteMonthlyEffect(pool, data) {
  try {
    const { id } = data;

    const result = await pool.request()
      .input('id', sql.Int, id)
      .query(`
        UPDATE MonthlyEffects SET
          IsActive = 0,
          UpdatedAt = GETDATE()
        WHERE ID = @id
      `);

    return NextResponse.json({
      success: true,
      message: 'تم حذف المؤثر بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في حذف المؤثر: ' + error.message
    }, { status: 500 });
  }
}
