import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const includeDetails = searchParams.get('details') === 'true';

    const pool = await getConnection();

    // جلب السيارات النشطة فقط
    const query = `
      SELECT 
        c.CarCode,
        c.CarNumber,
        c.CarType,
        c.ModelYear,
        c.Route,
        c.ContractorName,
        c.RentalValue,
        c.StartDate,
        c.EndDate,
        c.Status,
        CASE 
          WHEN c.EndDate IS NULL OR c.EndDate >= GETDATE() THEN 1
          ELSE 0
        END as IsActive
      FROM Cars c
      WHERE (c.EndDate IS NULL OR c.EndDate >= GETDATE())
        AND c.Status = 'active'
      ORDER BY c.CarCode
    `;

    const result = await pool.request().query(query);
    let activeCars = result.recordset;

    // إضافة تفاصيل المستفيدين إذا كان مطلوباً
    if (includeDetails) {
      for (let car of activeCars) {
        // جلب المستفيدين من السيارة
        const beneficiariesQuery = `
          SELECT 
            cb.EmployeeCode,
            e.EmployeeName,
            e.JobTitle,
            cb.StartDate as BeneficiaryStartDate,
            cb.EndDate as BeneficiaryEndDate
          FROM CarBeneficiaries cb
          LEFT JOIN Employees e ON cb.EmployeeCode = e.EmployeeCode
          WHERE cb.CarCode = @carCode
            AND (cb.EndDate IS NULL OR cb.EndDate >= GETDATE())
          ORDER BY cb.StartDate DESC
        `;

        const beneficiariesResult = await pool.request()
          .input('carCode', sql.NVarChar, car.CarCode)
          .query(beneficiariesQuery);

        car.beneficiaries = beneficiariesResult.recordset;

        // جلب إحصائيات التكاليف للسيارة
        const costsQuery = `
          SELECT 
            COUNT(*) as TotalCosts,
            SUM(Amount) as TotalAmount,
            MAX(CreatedAt) as LastCostDate
          FROM CarCosts 
          WHERE CarCode = @carCode
        `;

        const costsResult = await pool.request()
          .input('carCode', sql.NVarChar, car.CarCode)
          .query(costsQuery);

        car.costsSummary = costsResult.recordset[0] || {
          TotalCosts: 0,
          TotalAmount: 0,
          LastCostDate: null
        };
      }
    }

    return NextResponse.json({
      success: true,
      data: activeCars,
      count: activeCars.length
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب السيارات النشطة: ' + error.message
    }, { status: 500 });
  }
}

export async function POST(request) {
  try {
    const body = await request.json();
    const { action, carCode } = body;

    const pool = await getConnection();

    switch (action) {
      case 'validateCar':
        return await validateActiveCar(pool, carCode);
      case 'getCarDetails':
        return await getCarDetails(pool, carCode);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  }
}

// التحقق من أن السيارة نشطة
async function validateActiveCar(pool, carCode) {
  try {
    const result = await pool.request()
      .input('carCode', sql.NVarChar, carCode)
      .query(`
        SELECT 
          CarCode,
          CarNumber,
          CarType,
          Status,
          EndDate,
          CASE 
            WHEN EndDate IS NULL OR EndDate >= GETDATE() THEN 1
            ELSE 0
          END as IsActive
        FROM Cars
        WHERE CarCode = @carCode
      `);

    if (result.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'السيارة غير موجودة',
        isActive: false
      });
    }

    const car = result.recordset[0];
    
    if (!car.IsActive || car.Status !== 'active') {
      return NextResponse.json({
        success: false,
        error: 'السيارة غير نشطة أو منتهية الصلاحية',
        isActive: false,
        car: car
      });
    }

    return NextResponse.json({
      success: true,
      message: 'السيارة نشطة وصالحة',
      isActive: true,
      car: car
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في التحقق من السيارة: ' + error.message
    }, { status: 500 });
  }
}

// جلب تفاصيل السيارة
async function getCarDetails(pool, carCode) {
  try {
    // جلب بيانات السيارة
    const carResult = await pool.request()
      .input('carCode', sql.NVarChar, carCode)
      .query(`
        SELECT 
          CarCode,
          CarNumber,
          CarType,
          ModelYear,
          Route,
          ContractorName,
          RentalValue,
          StartDate,
          EndDate,
          Status,
          Notes
        FROM Cars
        WHERE CarCode = @carCode
      `);

    if (carResult.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'السيارة غير موجودة'
      });
    }

    const car = carResult.recordset[0];

    // جلب المستفيدين
    const beneficiariesResult = await pool.request()
      .input('carCode', sql.NVarChar, carCode)
      .query(`
        SELECT 
          cb.EmployeeCode,
          e.EmployeeName,
          e.JobTitle,
          e.Department,
          cb.StartDate,
          cb.EndDate
        FROM CarBeneficiaries cb
        LEFT JOIN Employees e ON cb.EmployeeCode = e.EmployeeCode
        WHERE cb.CarCode = @carCode
          AND (cb.EndDate IS NULL OR cb.EndDate >= GETDATE())
        ORDER BY cb.StartDate DESC
      `);

    car.beneficiaries = beneficiariesResult.recordset;

    // جلب آخر التكاليف
    const recentCostsResult = await pool.request()
      .input('carCode', sql.NVarChar, carCode)
      .query(`
        SELECT TOP 5
          CostType,
          Description,
          Amount,
          Date,
          CreatedAt
        FROM CarCosts
        WHERE CarCode = @carCode
        ORDER BY CreatedAt DESC
      `);

    car.recentCosts = recentCostsResult.recordset;

    // حساب إجمالي التكاليف
    const totalCostsResult = await pool.request()
      .input('carCode', sql.NVarChar, carCode)
      .query(`
        SELECT 
          COUNT(*) as TotalCosts,
          SUM(Amount) as TotalAmount,
          AVG(Amount) as AvgAmount
        FROM CarCosts
        WHERE CarCode = @carCode
      `);

    car.costsSummary = totalCostsResult.recordset[0];

    return NextResponse.json({
      success: true,
      data: car
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب تفاصيل السيارة: ' + error.message
    }, { status: 500 });
  }
}
