'use client';

import React, { useState, useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { useLanguage } from '@/contexts/LanguageContext';
import MainLayout from '@/components/MainLayout';
import {
  Building,
  Download,
  Filter,
  Search,
  FileText,
  BarChart3,
  Eye,
  Printer,
  RefreshCw,
  Car,
  Home,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  Wrench,
  TrendingUp
} from 'lucide-react';

export default function AssetReportsPage() {
  const { isDarkMode } = useTheme();
  const { isRTL, isArabic } = useLanguage();

  const [loading, setLoading] = useState(false);
  const [selectedReport, setSelectedReport] = useState('cars');
  const [filters, setFilters] = useState({
    assetType: '',
    status: '',
    location: '',
    contractStatus: ''
  });

  // أنواع تقارير الأصول
  const assetReports = [
    {
      id: 'cars',
      title: 'تقرير السيارات المؤجرة',
      description: 'جميع السيارات المؤجرة وحالتها',
      icon: Car,
      color: 'blue'
    },
    {
      id: 'apartments',
      title: 'تقرير الشقق المؤجرة',
      description: 'جميع الشقق المؤجرة وحالتها',
      icon: Building,
      color: 'green'
    },
    {
      id: 'expired-contracts',
      title: 'تقرير العقود المنتهية',
      description: 'العقود المنتهية الصلاحية',
      icon: AlertTriangle,
      color: 'red'
    },
    {
      id: 'renewed-contracts',
      title: 'تقرير العقود المجددة',
      description: 'العقود التي تم تجديدها',
      icon: CheckCircle,
      color: 'green'
    },
    {
      id: 'maintenance',
      title: 'تقرير الصيانة',
      description: 'أعمال الصيانة والإصلاحات',
      icon: Wrench,
      color: 'orange'
    },
    {
      id: 'statistics',
      title: 'إحصائيات الأصول',
      description: 'إحصائيات شاملة عن الأصول',
      icon: TrendingUp,
      color: 'purple'
    }
  ];

  // بيانات وهمية للتقارير
  const [reportData, setReportData] = useState({
    cars: [
      { id: 1, model: 'تويوتا كامري 2023', plateNumber: 'أ ب ج 123', assignedTo: 'أحمد محمد', contractStart: '2024-01-01', contractEnd: '2024-12-31', monthlyRent: 2500, status: 'نشط', location: 'الرياض' },
      { id: 2, model: 'هونداي النترا 2022', plateNumber: 'د هـ و 456', assignedTo: 'فاطمة علي', contractStart: '2024-03-01', contractEnd: '2025-02-28', monthlyRent: 2200, status: 'نشط', location: 'جدة' },
      { id: 3, model: 'نيسان صني 2023', plateNumber: 'ز ح ط 789', assignedTo: 'محمد حسن', contractStart: '2023-12-01', contractEnd: '2024-11-30', monthlyRent: 2000, status: 'منتهي', location: 'الدمام' }
    ],
    apartments: [
      { id: 1, location: 'الرياض - حي النرجس', assignedTo: 'سعد أحمد', contractStart: '2024-01-01', contractEnd: '2024-12-31', monthlyRent: 4500, status: 'نشط', bedrooms: 3, area: 150 },
      { id: 2, location: 'جدة - حي الصفا', assignedTo: 'نورا محمد', contractStart: '2024-02-01', contractEnd: '2025-01-31', monthlyRent: 3800, status: 'نشط', bedrooms: 2, area: 120 },
      { id: 3, location: 'الدمام - حي الفيصلية', assignedTo: 'خالد علي', contractStart: '2023-11-01', contractEnd: '2024-10-31', monthlyRent: 3200, status: 'منتهي', bedrooms: 2, area: 100 }
    ],
    statistics: {
      totalCars: 15,
      activeCars: 12,
      expiredCars: 3,
      totalApartments: 8,
      activeApartments: 6,
      expiredApartments: 2,
      totalMaintenanceCost: 25000,
      averageMaintenanceCost: 1250,
      contractsExpiringThisMonth: 2
    }
  });

  // تحميل البيانات
  const loadReportData = async () => {
    setLoading(true);
    try {
      setTimeout(() => {
        setLoading(false);
      }, 1000);
    } catch (error) {

      setLoading(false);
    }
  };

  useEffect(() => {
    loadReportData();
  }, [selectedReport, filters]);

  // تصدير التقرير
  const exportReport = (format) => {

  };

  // طباعة التقرير
  const printReport = () => {
    window.print();
  };

  // تنسيق العملة
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount);
  };

  // رندر محتوى التقرير حسب النوع
  const renderReportContent = () => {
    switch (selectedReport) {
      case 'cars':
        return (
          <div className="overflow-x-auto">
            <table className={`min-w-full ${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg`}>
              <thead className={`${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <tr>
                  <th className="px-4 py-3 text-right text-sm font-medium">موديل السيارة</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">رقم اللوحة</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">مخصصة لـ</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">بداية العقد</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">نهاية العقد</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">الإيجار الشهري</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">الحالة</th>
                  <th className="px-4 py-3 text-right text-sm font-medium">الموقع</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {reportData.cars.map((car) => (
                  <tr key={car.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-4 py-3 text-sm font-medium">{car.model}</td>
                    <td className="px-4 py-3 text-sm">{car.plateNumber}</td>
                    <td className="px-4 py-3 text-sm">{car.assignedTo}</td>
                    <td className="px-4 py-3 text-sm">{car.contractStart}</td>
                    <td className="px-4 py-3 text-sm">{car.contractEnd}</td>
                    <td className="px-4 py-3 text-sm">{formatCurrency(car.monthlyRent)}</td>
                    <td className="px-4 py-3 text-sm">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        car.status === 'نشط' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {car.status}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-sm">{car.location}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        );

      case 'statistics':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className={`${isDarkMode ? 'bg-gray-700' : 'bg-blue-50'} p-6 rounded-lg`}>
              <div className="flex items-center">
                <Car className="h-8 w-8 text-blue-500" />
                <div className="mr-4">
                  <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي السيارات</p>
                  <p className="text-2xl font-bold text-blue-600">{reportData.statistics.totalCars}</p>
                  <p className="text-xs text-gray-500">نشط: {reportData.statistics.activeCars}</p>
                </div>
              </div>
            </div>

            <div className={`${isDarkMode ? 'bg-gray-700' : 'bg-green-50'} p-6 rounded-lg`}>
              <div className="flex items-center">
                <Building className="h-8 w-8 text-green-500" />
                <div className="mr-4">
                  <p className="text-sm text-gray-600 dark:text-gray-400">إجمالي الشقق</p>
                  <p className="text-2xl font-bold text-green-600">{reportData.statistics.totalApartments}</p>
                  <p className="text-xs text-gray-500">نشط: {reportData.statistics.activeApartments}</p>
                </div>
              </div>
            </div>

            <div className={`${isDarkMode ? 'bg-gray-700' : 'bg-orange-50'} p-6 rounded-lg`}>
              <div className="flex items-center">
                <Wrench className="h-8 w-8 text-orange-500" />
                <div className="mr-4">
                  <p className="text-sm text-gray-600 dark:text-gray-400">تكاليف الصيانة</p>
                  <p className="text-2xl font-bold text-orange-600">{formatCurrency(reportData.statistics.totalMaintenanceCost)}</p>
                  <p className="text-xs text-gray-500">متوسط: {formatCurrency(reportData.statistics.averageMaintenanceCost)}</p>
                </div>
              </div>
            </div>

            <div className={`${isDarkMode ? 'bg-gray-700' : 'bg-red-50'} p-6 rounded-lg`}>
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-red-500" />
                <div className="mr-4">
                  <p className="text-sm text-gray-600 dark:text-gray-400">عقود تنتهي هذا الشهر</p>
                  <p className="text-2xl font-bold text-red-600">{reportData.statistics.contractsExpiringThisMonth}</p>
                  <p className="text-xs text-gray-500">تحتاج متابعة</p>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return <div>التقرير غير متاح</div>;
    }
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto p-6">
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-sm p-6 mb-6`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Building className="h-8 w-8 text-indigo-600" />
              <div>
                <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  تقارير الأصول
                </h1>
                <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  تقارير شاملة عن السيارات والشقق والعقود
                </p>
              </div>
            </div>

            <div className="flex gap-2">
              <button
                onClick={() => exportReport('excel')}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                تصدير Excel
              </button>
              <button
                onClick={() => exportReport('pdf')}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                تصدير PDF
              </button>
              <button
                onClick={printReport}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
              >
                <Printer className="h-4 w-4" />
                طباعة
              </button>
            </div>
          </div>
        </div>

        {/* أنواع التقارير */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
          {assetReports.map((report) => (
            <button
              key={report.id}
              onClick={() => setSelectedReport(report.id)}
              className={`p-4 rounded-lg border-2 transition-all ${
                selectedReport === report.id
                  ? `border-${report.color}-500 bg-${report.color}-50 dark:bg-${report.color}-900/20`
                  : `border-gray-200 dark:border-gray-700 ${isDarkMode ? 'bg-gray-800' : 'bg-white'} hover:border-${report.color}-300`
              }`}
            >
              <report.icon className={`h-8 w-8 text-${report.color}-500 mx-auto mb-2`} />
              <h3 className={`font-medium text-sm ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {report.title}
              </h3>
              <p className={`text-xs mt-1 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                {report.description}
              </p>
            </button>
          ))}
        </div>

        {/* محتوى التقرير */}
        <div className={`${isDarkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-sm p-6`}>
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <RefreshCw className="h-8 w-8 animate-spin text-blue-500" />
              <span className="mr-3">جاري تحميل التقرير...</span>
            </div>
          ) : (
            renderReportContent()
          )}
        </div>
      </div>
    </MainLayout>
  );
}
