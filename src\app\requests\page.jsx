'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/MainLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import LeaveBalancesTab from '@/components/LeaveBalancesTab';
import {
  FiCalendar,
  FiClock,
  FiMapPin,
  FiMoon,
  FiDownload,
  FiFileText,
  FiPlus,
  FiList,
  FiPrinter,
  FiTrendingUp
} from 'react-icons/fi';

const RequestsPage = () => {
  const router = useRouter();
  const { isArabic } = useLanguage();
  const [activeTab, setActiveTab] = useState('forms');
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(false);

  // أنواع الطلبات المتاحة
  const requestTypes = [
    {
      id: 'leave',
      title: isArabic ? 'طلب إجازة' : 'Leave Request',
      description: isArabic ? 'طلب إجازة سنوية أو عارضة أو مرضية' : 'Annual, emergency or sick leave request',
      color: 'blue',
      formPath: '/requests/leave'
    },
    {
      id: 'mission',
      title: isArabic ? 'طلب مأمورية' : 'Mission Request',
      description: isArabic ? 'طلب مأمورية عمل خارجية' : 'External work mission request',
      color: 'green',
      formPath: '/requests/mission'
    },
    {
      id: 'permission',
      title: isArabic ? 'طلب إذن' : 'Permission Request',
      description: isArabic ? 'طلب إذن مغادرة مؤقت' : 'Temporary leave permission request',
      color: 'orange',
      formPath: '/requests/permission'
    },
    {
      id: 'night-shift',
      title: isArabic ? 'إخطار وردية ليلية' : 'Night Shift Notification',
      description: isArabic ? 'إخطار بالعمل في الوردية الليلية' : 'Night shift work notification',
      color: 'purple',
      formPath: '/requests/night-shift'
    }
  ];

  // تحميل النموذج
  const downloadForm = async (formType) => {
    try {
      const response = await fetch(`/api/requests/download-form?type=${formType}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `نموذج_${getFormName(formType)}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        alert('خطأ في تحميل النموذج');
      }
    } catch (error) {

      alert('خطأ في تحميل النموذج');
    }
  };

  const getFormName = (type) => {
    const names = {
      leave: 'طلب_الإجازة',
      mission: 'طلب_المأمورية',
      permission: 'طلب_الإذن',
      'night-shift': 'إذن_الوردية_الليلية'
    };
    return names[type] || type;
  };

  const renderIcon = (typeId, color) => {
    const iconClass = `text-xl text-gray-600 dark:text-gray-400`;

    switch (typeId) {
      case 'leave':
        return <FiCalendar className={iconClass} />;
      case 'mission':
        return <FiMapPin className={iconClass} />;
      case 'permission':
        return <FiClock className={iconClass} />;
      case 'night-shift':
        return <FiMoon className={iconClass} />;
      default:
        return <FiFileText className={iconClass} />;
    }
  };

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto">
          {/* العنوان الرئيسي */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-200 mb-2">
              {isArabic ? 'الطلبات الدورية الورقية' : 'Periodic Paper Requests'}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {isArabic ? 'إدارة وتقديم الطلبات الورقية المختلفة' : 'Manage and submit various paper requests'}
            </p>
          </div>

          {/* التبويبات */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 mb-6">
            <div className="flex flex-wrap border-b border-gray-200 dark:border-gray-700">
              <button
                onClick={() => setActiveTab('forms')}
                className={`flex items-center gap-2 px-4 py-3 font-medium transition-colors ${
                  activeTab === 'forms'
                    ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50 dark:bg-blue-900/20'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
                }`}
              >
                <FiFileText className="text-lg" />
                {isArabic ? 'النماذج المتاحة' : 'Available Forms'}
              </button>
              <button
                onClick={() => setActiveTab('my-requests')}
                className={`flex items-center gap-2 px-4 py-3 font-medium transition-colors ${
                  activeTab === 'my-requests'
                    ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50 dark:bg-blue-900/20'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
                }`}
              >
                <FiList className="text-lg" />
                {isArabic ? 'طلباتي' : 'My Requests'}
              </button>
              <button
                onClick={() => setActiveTab('balances')}
                className={`flex items-center gap-2 px-4 py-3 font-medium transition-colors ${
                  activeTab === 'balances'
                    ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50 dark:bg-blue-900/20'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
                }`}
              >
                <FiTrendingUp className="text-lg" />
                {isArabic ? 'أرصدة الإجازات' : 'Leave Balances'}
              </button>
              <button
                onClick={() => setActiveTab('reports')}
                className={`flex items-center gap-2 px-4 py-3 font-medium transition-colors ${
                  activeTab === 'reports'
                    ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50 dark:bg-blue-900/20'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
                }`}
              >
                <FiTrendingUp className="text-lg" />
                {isArabic ? 'تقارير الطلبات' : 'Requests Reports'}
              </button>
              <button
                onClick={() => setActiveTab('print')}
                className={`flex items-center gap-2 px-4 py-3 font-medium transition-colors ${
                  activeTab === 'print'
                    ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50 dark:bg-blue-900/20'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
                }`}
              >
                <FiPrinter className="text-lg" />
                {isArabic ? 'طباعة الطلبات' : 'Print Requests'}
              </button>
            </div>
          </div>

          {/* محتوى التبويبات */}
          {activeTab === 'forms' && (
            // النماذج المتاحة
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {requestTypes.map((type) => (
                <div
                  key={type.id}
                  className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow"
                >
                  <div className="w-12 h-12 rounded-lg flex items-center justify-center mb-4 bg-gray-100 dark:bg-gray-700">
                    {renderIcon(type.id, type.color)}
                  </div>
                  
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2">
                    {type.title}
                  </h3>
                  
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                    {type.description}
                  </p>
                  
                  <div className="flex gap-2">
                    <button
                      onClick={() => router.push(type.formPath)}
                      className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center gap-2 text-sm"
                    >
                      <FiPlus className="text-sm" />
                      {isArabic ? 'تقديم طلب' : 'Submit Request'}
                    </button>
                    
                    <button
                      onClick={() => downloadForm(type.id)}
                      className="bg-gray-600 text-white px-3 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center justify-center"
                      title={isArabic ? 'تحميل النموذج' : 'Download Form'}
                    >
                      <FiDownload className="text-sm" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeTab === 'my-requests' && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
                {isArabic ? 'طلباتي المقدمة' : 'My Submitted Requests'}
              </h2>
              <div className="text-center py-8">
                <FiFileText className="text-4xl text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-400">
                  {isArabic ? 'لا توجد طلبات مقدمة' : 'No submitted requests'}
                </p>
              </div>
            </div>
          )}

          {activeTab === 'balances' && (
            <LeaveBalancesTab isArabic={isArabic} />
          )}

          {activeTab === 'reports' && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
                {isArabic ? 'تقارير الطلبات' : 'Requests Reports'}
              </h2>
              <div className="text-center py-8">
                <FiTrendingUp className="text-4xl text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-400">
                  {isArabic ? 'قريباً - تقارير مفصلة للطلبات' : 'Coming Soon - Detailed Reports'}
                </p>
              </div>
            </div>
          )}

          {activeTab === 'print' && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
                {isArabic ? 'طباعة الطلبات' : 'Print Requests'}
              </h2>
              <div className="text-center py-8">
                <FiPrinter className="text-4xl text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 dark:text-gray-400">
                  {isArabic ? 'قريباً - طباعة الطلبات' : 'Coming Soon - Print Requests'}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
};

export default RequestsPage;
