import { NextResponse } from 'next/server';
import { getConnection } from '@/utils/db';

export async function GET(request) {
  try {

    const pool = await getConnection();
    
    // جلب قائمة الجداول
    const result = await pool.request().query(`
      SELECT 
        TABLE_NAME,
        TABLE_TYPE
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_TYPE = 'BASE TABLE'
      ORDER BY TABLE_NAME
    `);

    return NextResponse.json({
      success: true,
      message: `تم العثور على ${result.recordset.length} جدول`,
      tables: result.recordset
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message,
      details: {
        name: error.name,
        code: error.code,
        state: error.state
      }
    }, { status: 500 });
  }
}
