{"unused_import": [{"type": "unused_import", "import": "config", "line": "import { config, getConnection } from '@/lib/db';", "file": "src\\api\\auth\\authenticate\\route.js", "suggestion": "Remove unused import: config"}, {"type": "unused_import", "import": "config", "line": "import { config, getConnection } from '@/lib/db';\r", "file": "src\\api\\import-excel\\consolidated\\route.js", "suggestion": "Remove unused import: config"}, {"type": "unused_import", "import": "FiUser", "line": "import { FiAlertTriangle, FiCheck, FiX, FiRefreshCw, FiHome, FiTruck, FiUser } from 'react-icons/fi';", "file": "src\\app\\admin\\fix-assets\\page.jsx", "suggestion": "Remove unused import: FiUser"}, {"type": "unused_import", "import": "sql", "line": "import { getConnection, sql } from '@/utils/db';", "file": "src\\app\\api\\check-action-types\\route.js", "suggestion": "Remove unused import: sql"}, {"type": "unused_import", "import": "sql", "line": "import { getConnection, sql } from '@/utils/db';", "file": "src\\app\\api\\check-apartments-table\\route.js", "suggestion": "Remove unused import: sql"}, {"type": "unused_import", "import": "sql", "line": "import { getConnection, sql } from '@/utils/db';", "file": "src\\app\\api\\check-employee-data\\route.js", "suggestion": "Remove unused import: sql"}, {"type": "unused_import", "import": "sql", "line": "import { getConnection, sql } from '@/utils/db';", "file": "src\\app\\api\\check-existing-tables\\route.js", "suggestion": "Remove unused import: sql"}, {"type": "unused_import", "import": "sql", "line": "import { getConnection, sql } from '@/utils/db';", "file": "src\\app\\api\\check-notifications\\route.js", "suggestion": "Remove unused import: sql"}, {"type": "unused_import", "import": "sql", "line": "import { getConnection, sql } from '@/utils/db';", "file": "src\\app\\api\\check-real-columns\\route.js", "suggestion": "Remove unused import: sql"}, {"type": "unused_import", "import": "sql", "line": "import { getConnection, sql } from '@/utils/db';", "file": "src\\app\\api\\compare-tables\\route.js", "suggestion": "Remove unused import: sql"}, {"type": "unused_import", "import": "sql", "line": "import { getConnection, sql } from '@/utils/db';", "file": "src\\app\\api\\complete-setup\\route.js", "suggestion": "Remove unused import: sql"}, {"type": "unused_import", "import": "sql", "line": "import { getConnection, sql } from '@/utils/db';", "file": "src\\app\\api\\create-archiv-table\\route.js", "suggestion": "Remove unused import: sql"}, {"type": "unused_import", "import": "sql", "line": "import { getConnection, sql } from '@/utils/db';", "file": "src\\app\\api\\create-leave-balances-table\\route.js", "suggestion": "Remove unused import: sql"}, {"type": "unused_import", "import": "sql", "line": "import { getConnection, executeQuery, sql } from '@/lib/db';\r", "file": "src\\app\\api\\data-service\\route.js", "suggestion": "Remove unused import: sql"}, {"type": "unused_import", "import": "sql", "line": "import { getConnection, sql } from '@/utils/db';", "file": "src\\app\\api\\database-consistency-fix\\route.js", "suggestion": "Remove unused import: sql"}, {"type": "unused_import", "import": "sql", "line": "import { getConnection, sql } from '@/utils/db';", "file": "src\\app\\api\\database-tables-scan\\route.js", "suggestion": "Remove unused import: sql"}, {"type": "unused_import", "import": "sql", "line": "import { getConnection, sql } from '@/utils/db';", "file": "src\\app\\api\\employee-archive\\route.js", "suggestion": "Remove unused import: sql"}, {"type": "unused_import", "import": "sql", "line": "import { getConnection, sql } from '@/utils/db';", "file": "src\\app\\api\\fix-all-database-issues\\route.js", "suggestion": "Remove unused import: sql"}, {"type": "unused_import", "import": "sql", "line": "import { getConnection, sql } from '@/utils/db';", "file": "src\\app\\api\\main-dashboard-stats\\route.js", "suggestion": "Remove unused import: sql"}, {"type": "unused_import", "import": "sql", "line": "import { getConnection, sql } from '@/utils/db';", "file": "src\\app\\api\\setup-database\\route.js", "suggestion": "Remove unused import: sql"}, {"type": "unused_import", "import": "sql", "line": "import { getConnection, sql } from '@/utils/db';", "file": "src\\app\\api\\setup-leave-management-system\\route.js", "suggestion": "Remove unused import: sql"}, {"type": "unused_import", "import": "sql", "line": "import { getConnection, sql } from '@/utils/db';", "file": "src\\app\\api\\setup-test-data\\route.js", "suggestion": "Remove unused import: sql"}, {"type": "unused_import", "import": "sql", "line": "import { getConnection, sql } from '@/utils/db';", "file": "src\\app\\api\\system-health\\route.js", "suggestion": "Remove unused import: sql"}, {"type": "unused_import", "import": "sql", "line": "import { getConnection, sql } from '@/utils/db';", "file": "src\\app\\api\\test-archive-table\\route.js", "suggestion": "Remove unused import: sql"}, {"type": "unused_import", "import": "formatDateToDDMMYYYY", "line": "import { formatDateToDDMMYYYY } from '@/utils/dateFormat';", "file": "src\\app\\approved-leaves\\page.jsx", "suggestion": "Remove unused import: formatDateToDDMMYYYY"}, {"type": "unused_import", "import": "FiSearch", "line": "import { Fi<PERSON><PERSON>ndar, FiSearch, FiPrinter, FiFilter } from 'react-icons/fi';", "file": "src\\app\\approved-leaves\\page.jsx", "suggestion": "Remove unused import: FiSearch"}, {"type": "unused_import", "import": "Upload", "line": "import { Calendar, Users, Download, Printer, Edit3, Save, RefreshCw, BarChart3, FileText, Upload } from 'lucide-react';", "file": "src\\app\\attendance\\monthly-report\\page.jsx", "suggestion": "Remove unused import: Upload"}, {"type": "unused_import", "import": "useEffect", "line": "import { useState, useEffect } from 'react';", "file": "src\\app\\costs\\apartments\\page.jsx", "suggestion": "Remove unused import: useEffect"}, {"type": "unused_import", "import": "useEffect", "line": "import { useState, useEffect } from 'react';", "file": "src\\app\\costs\\cars\\page.jsx", "suggestion": "Remove unused import: useEffect"}, {"type": "unused_import", "import": "FiTrendingUp", "line": "import { FiBarChart, FiTrendingUp, FiDollarSign, FiRefreshCw, FiPrinter } from 'react-icons/fi';", "file": "src\\app\\costs\\charts\\page.jsx", "suggestion": "Remove unused import: FiTrendingUp"}, {"type": "unused_import", "import": "FiRefreshCw", "line": "import { FiBarChart, FiTrendingUp, FiDollarSign, FiRefreshCw, FiPrinter } from 'react-icons/fi';", "file": "src\\app\\costs\\charts\\page.jsx", "suggestion": "Remove unused import: FiRefreshCw"}, {"type": "unused_import", "import": "useEffect", "line": "import { useState, useEffect } from 'react';", "file": "src\\app\\costs\\temp-workers\\page.jsx", "suggestion": "Remove unused import: useEffect"}, {"type": "unused_import", "import": "FiCalendar", "line": "import { FiUpload, FiSave, FiPrinter, FiCalendar, FiDownload } from 'react-icons/fi';", "file": "src\\app\\daily-attendance\\page.jsx", "suggestion": "Remove unused import: FiCalendar"}, {"type": "unused_import", "import": "useEffect", "line": "import { useState, useEffect } from 'react';", "file": "src\\app\\debug-my-requests\\page.jsx", "suggestion": "Remove unused import: useEffect"}, {"type": "unused_import", "import": "FiMail", "line": "import { FiSave, FiArrowLeft, FiUser, FiMail, FiPhone, FiMapPin } from 'react-icons/fi';", "file": "src\\app\\employees\\edit\\[id]\\page.jsx", "suggestion": "Remove unused import: FiMail"}, {"type": "unused_import", "import": "Download", "line": "import { Calendar, User, FileText, Clock, Search, Download, RefreshCw } from 'lucide-react';", "file": "src\\app\\leaves\\balances\\page.jsx", "suggestion": "Remove unused import: Download"}, {"type": "unused_import", "import": "Edit", "line": "import { Search, Eye, Edit, Trash2, CheckCircle, XCircle, Clock, FileText, Download } from 'lucide-react';", "file": "src\\app\\leaves\\list\\page.jsx", "suggestion": "Remove unused import: Edit"}, {"type": "unused_import", "import": "Trash2", "line": "import { Search, Eye, Edit, Trash2, CheckCircle, XCircle, Clock, FileText, Download } from 'lucide-react';", "file": "src\\app\\leaves\\list\\page.jsx", "suggestion": "Remove unused import: Trash2"}, {"type": "unused_import", "import": "Calendar", "line": "import { Calendar, User, FileText, Clock, Save, Printer, Search } from 'lucide-react';", "file": "src\\app\\leaves\\request\\page.jsx", "suggestion": "Remove unused import: Calendar"}, {"type": "unused_import", "import": "User", "line": "import { Calendar, User, FileText, Clock, Save, Printer, Search } from 'lucide-react';", "file": "src\\app\\leaves\\request\\page.jsx", "suggestion": "Remove unused import: User"}, {"type": "unused_import", "import": "FileText", "line": "import { Calendar, User, FileText, Clock, Save, Printer, Search } from 'lucide-react';", "file": "src\\app\\leaves\\request\\page.jsx", "suggestion": "Remove unused import: FileText"}, {"type": "unused_import", "import": "Clock", "line": "import { Calendar, User, FileText, Clock, Save, Printer, Search } from 'lucide-react';", "file": "src\\app\\leaves\\request\\page.jsx", "suggestion": "Remove unused import: Clock"}, {"type": "unused_import", "import": "Calendar", "line": "import { Calendar, User, FileText, Clock, CheckCircle, XCircle, Search, Filter, Download } from 'lucide-react';", "file": "src\\app\\leaves\\requests\\page.jsx", "suggestion": "Remove unused import: Calendar"}, {"type": "unused_import", "import": "User", "line": "import { Calendar, User, FileText, Clock, CheckCircle, XCircle, Search, Filter, Download } from 'lucide-react';", "file": "src\\app\\leaves\\requests\\page.jsx", "suggestion": "Remove unused import: User"}, {"type": "unused_import", "import": "Download", "line": "import { Calendar, User, FileText, Clock, CheckCircle, XCircle, Search, Filter, Download } from 'lucide-react';", "file": "src\\app\\leaves\\requests\\page.jsx", "suggestion": "Remove unused import: Download"}, {"type": "unused_import", "import": "FaEdit", "line": "import { FaPlus, FaEdit, FaTrash, FaCheck, FaTimes, FaEye } from 'react-icons/fa';", "file": "src\\app\\missions\\page.jsx", "suggestion": "Remove unused import: FaEdit"}, {"type": "unused_import", "import": "FaEye", "line": "import { FaPlus, FaEdit, FaTrash, FaCheck, FaTimes, FaEye } from 'react-icons/fa';", "file": "src\\app\\missions\\page.jsx", "suggestion": "Remove unused import: FaEye"}, {"type": "unused_import", "import": "FiDownload", "line": "import { FiCalendar, FiSave, FiArrowLeft, FiDownload, FiUser, FiPrinter, FiRefreshCw } from 'react-icons/fi';", "file": "src\\app\\requests\\leave\\page.jsx", "suggestion": "Remove unused import: FiDownload"}, {"type": "unused_import", "import": "formatDateToDDMMYYYY", "line": "import { formatDateToDDMMYYYY, convertDDMMYYYYToISO } from '@/utils/dateFormat';", "file": "src\\app\\requests\\mission\\page.jsx", "suggestion": "Remove unused import: formatDateToDDMMYYYY"}, {"type": "unused_import", "import": "convertDDMMYYYYToISO", "line": "import { formatDateToDDMMYYYY, convertDDMMYYYYToISO } from '@/utils/dateFormat';", "file": "src\\app\\requests\\mission\\page.jsx", "suggestion": "Remove unused import: convertDDMMYYYYToISO"}, {"type": "unused_import", "import": "<PERSON><PERSON><PERSON>", "line": "import { FiMapPin, FiSave, FiArrowLeft, FiDownload, FiUser, FiClock, FiPrinter, FiRefreshCw } from 'react-icons/fi';", "file": "src\\app\\requests\\mission\\page.jsx", "suggestion": "Remove unused import: FiClock"}, {"type": "unused_import", "import": "FiUser", "line": "import { FiMoon, FiSave, FiArrowLeft, FiDownload, FiUser, FiPrinter } from 'react-icons/fi';", "file": "src\\app\\requests\\night-shift\\page.jsx", "suggestion": "Remove unused import: FiUser"}, {"type": "unused_import", "import": "FiUser", "line": "import { FiClock, FiSave, FiArrowLeft, FiDownload, FiUser, FiPrinter } from 'react-icons/fi';", "file": "src\\app\\requests\\permission\\page.jsx", "suggestion": "Remove unused import: FiUser"}, {"type": "unused_import", "import": "FiDatabase", "line": "import { FiDatabase, FiCheck, FiX, FiSettings, FiRefreshCw, FiInfo } from 'react-icons/fi';", "file": "src\\app\\setup-direct-managers\\page.js", "suggestion": "Remove unused import: FiDatabase"}, {"type": "unused_import", "import": "useState", "line": "import { useState } from 'react';", "file": "src\\app\\test-cards\\page.jsx", "suggestion": "Remove unused import: useState"}, {"type": "unused_import", "import": "Clock", "line": "import { Bell, Send, CheckCircle, XCircle, Clock } from 'lucide-react';", "file": "src\\app\\test-notification-format\\page.jsx", "suggestion": "Remove unused import: Clock"}, {"type": "unused_import", "import": "User", "line": "import { Bell, Send, User, Calendar } from 'lucide-react';", "file": "src\\app\\test-notifications\\page.jsx", "suggestion": "Remove unused import: User"}, {"type": "unused_import", "import": "Calendar", "line": "import { Bell, Send, User, Calendar } from 'lucide-react';", "file": "src\\app\\test-notifications\\page.jsx", "suggestion": "Remove unused import: Calendar"}], "unused_default_import": [{"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\app\\add-apartment\\page.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\app\\add-car\\page.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\app\\add-worker\\page.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\app\\ar\\page.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\app\\assets\\page.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\app\\attendance-management\\page.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from \"react\";", "file": "src\\app\\employee\\add\\page.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\app\\employee\\id\\page.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\app\\employee-archive\\page.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\app\\employee-data\\page.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\app\\employee-documents\\page.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\app\\employee-management\\page.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\app\\employees\\data\\page.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\app\\employees\\management\\page.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\app\\home\\page.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from \"react\";", "file": "src\\app\\import-employees\\page.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\app\\leave-management\\page.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\app\\leaves\\approved\\page.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\app\\leaves\\balance\\page.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\app\\monthly-attendance\\page.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\app\\reports\\page.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from \"react\";", "file": "src\\app\\system\\cleanup\\page.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';\r", "file": "src\\app\\system-management\\monthly-attendance.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "MonthlyAttendance", "line": "import MonthlyAttendance from './monthly-attendance';", "file": "src\\app\\system-management\\page.jsx", "suggestion": "Remove unused default import: MonthlyAttendance"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\app\\unified-dashboard\\page.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\components\\action-buttons.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\components\\arabic-navigation.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\components\\auth-check.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\components\\back-button-arabic.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from \"react\";", "file": "src\\components\\back-button.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\components\\CustomDateInput.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\components\\form-builder.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';\r", "file": "src\\components\\PageTitle.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\components\\shared-layout.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\components\\SimpleDateInput.jsx", "suggestion": "Remove unused default import: React"}, {"type": "unused_default_import", "import": "React", "line": "import React from 'react';", "file": "src\\components\\ThemeToggle.jsx", "suggestion": "Remove unused default import: React"}], "console_statement": [{"type": "console_statement", "line": "console.log(`     الوصف: ${result.CostDescription.substring(0, 50)}...`);", "lineNumber": 146, "file": "add-sample-costs.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   ✅ تم إضافة التكلفة ${newCostId}: ${cost.description.substring(0, 50)}...`);", "lineNumber": 153, "file": "add-sample-custody-costs.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`      ${cost.CostDescription.substring(0, 60)}... (${cost.Amount} جنيه)`);", "lineNumber": 177, "file": "add-sample-custody-costs.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('   • المغ<PERSON>رب ≠ المقيم في شقة الشركة (مفهومان منفصلان)');", "lineNumber": 226, "file": "create-employee-template.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "createEmployeeTemplate().catch(console.error);", "lineNumber": 233, "file": "create-employee-template.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('4. ابحث عن موظف (مثل: 1450) لرؤية التحديثات الجديدة');", "lineNumber": 47, "file": "force-refresh.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('   - مربع ملون (تيل فاتح) يحتوي على:');", "lineNumber": 49, "file": "force-refresh.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('     • اسم المؤجر (إذا كان مسجل بشقة)');", "lineNumber": 51, "file": "force-refresh.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('   - مربع ملون (نيلي فاتح) يحتوي على:');", "lineNumber": 53, "file": "force-refresh.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('     • خط السير (إذا كان مسجل بسيارة)');", "lineNumber": 55, "file": "force-refresh.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('🟢 أخضر: للحالات الإيجابية (مسجل)');", "lineNumber": 57, "file": "force-refresh.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('🔴 أحمر: للحالات السلبية (غير مسجل)');", "lineNumber": 58, "file": "force-refresh.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('2. امسح cache المتصفح (Ctrl+Shift+Delete)');", "lineNumber": 60, "file": "force-refresh.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('4. تأكد من عدم وجود أخطاء في console المتصفح (F12)');", "lineNumber": 62, "file": "force-refresh.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('✅ تم تحديث قيم العمولة (5% من قيمة الإيجار)');", "lineNumber": 44, "file": "scripts\\add-commission-field.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`- متوسط العمولة: ${(data.AvgCommission || 0).toFixed(2)} ج.م`);", "lineNumber": 63, "file": "scripts\\add-commission-field.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "// فحص console.log المتبقية", "lineNumber": 107, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "if (line.includes('console.log') || line.includes('console.warn') || line.includes('console.error')) {", "lineNumber": 109, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.error(`❌ خطأ في قراءة ${filePath}:`, error.message);", "lineNumber": 146, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.error(`❌ خطأ في قراءة المجلد ${dir}:`, error.message);", "lineNumber": 176, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('\\n📊 تقرير تحليل الكود:\\n');", "lineNumber": 197, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`🔍 الواردات غير المستخدمة (${grouped.unused_import.length}):`);", "lineNumber": 201, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   📁 ${issue.file}`);", "lineNumber": 203, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   ❌ ${issue.import}`);", "lineNumber": 204, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   💡 ${issue.suggestion}\\n`);", "lineNumber": 205, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`🔍 الواردات الافتراضية غير المستخدمة (${grouped.unused_default_import.length}):`);", "lineNumber": 211, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   📁 ${issue.file}`);", "lineNumber": 213, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   ❌ ${issue.import}`);", "lineNumber": 214, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   💡 ${issue.suggestion}\\n`);", "lineNumber": 215, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`🔍 عبارات Console المتبقية (${grouped.console_statement.length}):`);", "lineNumber": 221, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   📁 ${issue.file}:${issue.lineNumber}`);", "lineNumber": 223, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   ❌ ${issue.line}`);", "lineNumber": 224, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   💡 ${issue.suggestion}\\n`);", "lineNumber": 225, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`🔍 التعليقات المؤقتة (${grouped.temp_comment.length}):`);", "lineNumber": 231, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   📁 ${issue.file}:${issue.lineNumber}`);", "lineNumber": 233, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   ❌ ${issue.line}`);", "lineNumber": 234, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   💡 ${issue.suggestion}\\n`);", "lineNumber": 235, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`📈 الملخص:`);", "lineNumber": 241, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   - إجمالي المشاكل: ${totalIssues}`);", "lineNumber": 242, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   - واردات غير مستخدمة: ${grouped.unused_import.length + grouped.unused_default_import.length}`);", "lineNumber": 243, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   - عبارات console: ${grouped.console_statement.length}`);", "lineNumber": 244, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   - تعليقات مؤقتة: ${grouped.temp_comment.length}`);", "lineNumber": 245, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('🔍 بدء تحليل الكود للبحث عن الواردات غير المستخدمة...\\n');", "lineNumber": 252, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('\\n✅ تم الانتهاء من تحليل الكود!');", "lineNumber": 257, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`📄 تم حفظ التقرير في: ${reportPath}`);", "lineNumber": 262, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "main().catch(console.error);", "lineNumber": 267, "file": "scripts\\clean-imports.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "// دالة لتنظيف console.log من ملفات JavaScript", "lineNumber": 137, "file": "scripts\\clean-project.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "// إزالة console.log statements", "lineNumber": 142, "file": "scripts\\clean-project.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "main().catch(console.error);", "lineNumber": 215, "file": "scripts\\clean-project.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('📊 الجداول الموجودة:', tablesCheck.recordset.map(t => t.TABLE_NAME));", "lineNumber": 50, "file": "scripts\\setup-leave-tables.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`  - ${col.COLUMN_NAME} (${col.DATA_TYPE})`);", "lineNumber": 18, "file": "src\\app\\api\\analyze-database\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`✅ تم العثور على الموظف: ${employee.EmployeeName} (${employee.EmployeeCode})`);", "lineNumber": 296, "file": "src\\app\\api\\apartments\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`✅ تم إضافة المستفيد بنجاح: ${employee.EmployeeName} (ID: ${newBeneficiaryId})`);", "lineNumber": 356, "file": "src\\app\\api\\apartments\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`✅ تم تحديث بيانات الموظف: ${employee.EmployeeName} (${employeeCode})`);", "lineNumber": 501, "file": "src\\app\\api\\apartments\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`Successfully saved attendance for employee: ${employeeNameStr} (${employeeCodeStr})`);", "lineNumber": 520, "file": "src\\app\\api\\attendance\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`⚠️ نوع إجازة غير معروف: \"${req.LeaveType}\" (منظف: \"${cleanLeaveType}\")`);", "lineNumber": 149, "file": "src\\app\\api\\attendance-requests\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`✅ تم تحديث ${record.EmployeeName} - ${record.AttendanceDate.toISOString().split('T')[0]} إلى ${attendanceStatus}`);", "lineNumber": 114, "file": "src\\app\\api\\auto-fix-all-attendance\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('🔄 بدء النسخ الاحتياطي البديل (تصدير البيانات)...');", "lineNumber": 116, "file": "src\\app\\api\\backup-system\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`🏢 هيكل جدول الأقسام (${tableName}):`, deptStructure.recordset);", "lineNumber": 64, "file": "src\\app\\api\\check-existing-tables\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`📊 عينة من بيانات الأقسام (${departmentsTableStructure.tableName}):`, departmentsSampleData);", "lineNumber": 92, "file": "src\\app\\api\\check-existing-tables\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('📋 الجداول الموجودة:', tablesResult.recordset.map(t => t.TABLE_NAME));", "lineNumber": 17, "file": "src\\app\\api\\check-real-database\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('📊 أعمدة جدول Employees:', employeesColumns.map(c => c.COLUMN_NAME));", "lineNumber": 29, "file": "src\\app\\api\\check-real-database\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('🏠 أعمدة جدول Apartments:', apartmentsColumns.map(c => c.COLUMN_NAME));", "lineNumber": 56, "file": "src\\app\\api\\check-real-database\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('👥 أعمدة جدول ApartmentBeneficiaries:', apartmentBeneficiariesColumns.map(c => c.COLUMN_NAME));", "lineNumber": 71, "file": "src\\app\\api\\check-real-database\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`👤 تم العثور على: ${samer.EmployeeName} (${samer.EmployeeCode})`);", "lineNumber": 37, "file": "src\\app\\api\\check-samer-specific\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`- المؤمن عليهم (الاثنين): ${finalStats.insurance.bothInsured}`);", "lineNumber": 167, "file": "src\\app\\api\\correct-dashboard-stats\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('📊 جلب بيانات الداشبورد (الإصدار الآمن)...');", "lineNumber": 6, "file": "src\\app\\api\\dashboard-safe\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`- المؤمن عليهم (الاثنين): ${report.summary.bothInsured}`);", "lineNumber": 166, "file": "src\\app\\api\\database-report\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`🏖️ معالجة إجازة ${leave.LeaveType} من ${startDate.toLocaleDateString('ar-EG')} إلى ${endDate.toLocaleDateString('ar-EG')}`);", "lineNumber": 71, "file": "src\\app\\api\\direct-fix-attendance\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   - نسبة الحضور: ${attendancePercentage.toFixed(2)}%`);", "lineNumber": 182, "file": "src\\app\\api\\direct-fix-attendance\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('Received employee data:', JSON.stringify(data, null, 2));", "lineNumber": 99, "file": "src\\app\\api\\employee-data\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('🔍 تحليل النص:', {", "lineNumber": 125, "file": "src\\app\\api\\employee-live-search\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`⚠️ رقم قومي غير صحيح في الصف ${rowNumber}: ${nationalIdStr} (الطول: ${nationalIdStr.length})`);", "lineNumber": 142, "file": "src\\app\\api\\employees\\bulk-add\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`❌ الصف ${rowNumber}: حقول مفقودة: ${missingAlwaysRequired.join(', ')}`);", "lineNumber": 223, "file": "src\\app\\api\\employees\\bulk-add\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`🔍 فحص الموظف: ${employee.fullName} (كود: ${employee.employeeCode}, رقم قومي: ${employee.nationalId})`);", "lineNumber": 279, "file": "src\\app\\api\\employees\\bulk-add\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`📋 موظف موجود: ${existing.EmployeeName} (كود: ${existing.EmployeeCode}, رقم قومي: ${existing.NationalID})`);", "lineNumber": 283, "file": "src\\app\\api\\employees\\bulk-add\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`🔄 تم تحديث الموظف: ${employee.fullName} (${updateFields.length} حقل)`);", "lineNumber": 489, "file": "src\\app\\api\\employees\\bulk-add\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`📊 Employees API called with limit: ${limitParam} (parsed: ${limit}), date: ${selectedDate}`);", "lineNumber": 90, "file": "src\\app\\api\\employees-unified\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`- المؤمن عليهم (الاثنين): ${finalStats.insurance.bothInsured}`);", "lineNumber": 168, "file": "src\\app\\api\\final-dashboard-stats\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   - نسبة الحضور: ${attendancePercentage.toFixed(2)}%`);", "lineNumber": 215, "file": "src\\app\\api\\fix-attendance-calculation\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   ${col.COLUMN_NAME}: ${col.DATA_TYPE} (NULL: ${col.IS_NULLABLE})`);", "lineNumber": 24, "file": "src\\app\\api\\fix-attendance-status-issue\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('✅ جدول LeaveRequests غير موجود (هذا جيد)');", "lineNumber": 155, "file": "src\\app\\api\\fix-leave-requests-table\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`👤 تم العثور على الموظف: ${employeeName} (${employeeCode})`);", "lineNumber": 48, "file": "src\\app\\api\\fix-samer-attendance\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`🏖️ معالجة إجازة من ${startDate.toLocaleDateString('ar-EG')} إلى ${endDate.toLocaleDateString('ar-EG')}`);", "lineNumber": 92, "file": "src\\app\\api\\fix-samer-attendance\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   - نسبة الحضور: ${attendancePercentage.toFixed(2)}%`);", "lineNumber": 268, "file": "src\\app\\api\\fix-samer-attendance\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`👤 تم العثور على سامر: ${employee.EmployeeName} (${employeeCode})`);", "lineNumber": 44, "file": "src\\app\\api\\fix-samer-direct\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`👤 إصلاح: ${samer.EmployeeName} (${samer.EmployeeCode})`);", "lineNumber": 37, "file": "src\\app\\api\\fix-samer-specific\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   - نسبة الحضور: ${attendancePercentage.toFixed(2)}%`);", "lineNumber": 147, "file": "src\\app\\api\\fix-samer-specific\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('✅ تكاليف العمالة المؤقتة:', allCostsResult.tempWorkers.total.toLocaleString());", "lineNumber": 299, "file": "src\\app\\api\\main-dashboard-stats\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('✅ تكاليف السيارات:', allCostsResult.carsCosts.total.toLocaleString());", "lineNumber": 346, "file": "src\\app\\api\\main-dashboard-stats\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('✅ تكاليف السيارات (محسوبة):', allCostsResult.carsCosts.total.toLocaleString());", "lineNumber": 367, "file": "src\\app\\api\\main-dashboard-stats\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('✅ تكاليف الشقق:', allCostsResult.apartmentsCosts.total.toLocaleString());", "lineNumber": 390, "file": "src\\app\\api\\main-dashboard-stats\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('✅ تكاليف المشروع:', allCostsResult.projectCosts.total.toLocaleString());", "lineNumber": 409, "file": "src\\app\\api\\main-dashboard-stats\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('✅ التكاليف الشهرية:', allCostsResult.monthlyCosts.total.toLocaleString());", "lineNumber": 427, "file": "src\\app\\api\\main-dashboard-stats\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('✅ مصروفات العهد القديمة:', allCostsResult.custodyExpenses.total.toLocaleString());", "lineNumber": 509, "file": "src\\app\\api\\main-dashboard-stats\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('✅ التكاليف العامة:', allCostsResult.generalCosts.total.toLocaleString());", "lineNumber": 528, "file": "src\\app\\api\\main-dashboard-stats\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('💰 إجمالي جميع التكاليف:', totalAllCosts.toLocaleString());", "lineNumber": 544, "file": "src\\app\\api\\main-dashboard-stats\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`📅 فترة البحث: من ${startDate.toISOString().split('T')[0]} إلى ${endDate.toISOString().split('T')[0]}`);", "lineNumber": 27, "file": "src\\app\\api\\monthly-attendance-sheet\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`📅 الفترة المطلوبة: من ${startDate.toISOString().split('T')[0]} إلى ${endDate.toISOString().split('T')[0]}`);", "lineNumber": 47, "file": "src\\app\\api\\monthly-attendance-sheet\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`📅 الفترة المطلوبة: من ${formatDate(startDate)} إلى ${formatDate(endDate)}`);", "lineNumber": 270, "file": "src\\app\\api\\monthly-attendance-sheet\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`🔍 startDate = new Date(${year}, ${month - 2}, 11) = ${startDate.toString()}`);", "lineNumber": 272, "file": "src\\app\\api\\monthly-attendance-sheet\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`🔍 endDate = new Date(${year}, ${month - 1}, 10) = ${endDate.toString()}`);", "lineNumber": 273, "file": "src\\app\\api\\monthly-attendance-sheet\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`🔍 معالجة استقالة الموظف ${employeeCode}: تاريخ الاستقالة ${resignationDate.toISOString().split('T')[0]}`);", "lineNumber": 434, "file": "src\\app\\api\\monthly-attendance-sheet\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`✅ تم ملء الأيام ${resignationDayNumber + 1}-31 بـ \"-\" للموظف ${employeeCode} (الموظف يبقى في الكشف)`);", "lineNumber": 444, "file": "src\\app\\api\\monthly-attendance-sheet\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`🔍 معالجة نقل الموظف ${employeeCode}: تاريخ النقل ${transferDate.toISOString().split('T')[0]}`);", "lineNumber": 452, "file": "src\\app\\api\\monthly-attendance-sheet\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`✅ تم ملء الأيام ${transferDayNumber + 1}-31 بـ \"-\" للموظف ${employeeCode} (الموظف يبقى في الكشف)`);", "lineNumber": 462, "file": "src\\app\\api\\monthly-attendance-sheet\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`🔍 معالجة انضمام الموظف ${employeeCode}: تاريخ الانضمام ${joinDate.toISOString().split('T')[0]}`);", "lineNumber": 470, "file": "src\\app\\api\\monthly-attendance-sheet\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.error('خطأ في إنشاء الإشعار (لكن الإجراء تم تسجيله):', notificationError);", "lineNumber": 317, "file": "src\\app\\api\\notifications\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`✅ تم تحديث رصيد الإجازات للموظف ${request.EmployeeCode}: ${operation === 'subtract' ? 'خصم' : 'إضافة'} ${request.DaysCount} أيام من ${balanceColumn} (${currentBalance} → ${newBalance})`);", "lineNumber": 1020, "file": "src\\app\\api\\paper-requests\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('⚠️ لا يمكن جلب إحصائيات البيانات (الجداول غير موجودة)');", "lineNumber": 187, "file": "src\\app\\api\\setup-direct-managers-system\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`✅ ${record.EmployeeName} - ${record.AttendanceDate.toISOString().split('T')[0]} → ${attendanceType}`);", "lineNumber": 85, "file": "src\\app\\api\\smart-fix-attendance\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`📋 موظف موجود: ${existing.EmployeeName} (كود: ${existing.EmployeeCode}, رقم قومي: ${existing.NationalID})`);", "lineNumber": 38, "file": "src\\app\\api\\test-single-employee-update\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`✅ تم العثور على الموظف: ${employee.FullName} (${employee.EmployeeID})`);", "lineNumber": 73, "file": "src\\app\\api\\transport\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`✅ تم إضافة الموظف للمواصلات بنجاح: ${employee.FullName} (ID: ${newEmployeeId})`);", "lineNumber": 111, "file": "src\\app\\api\\transport\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   - النوع الأول (حضور/إجازة): ${type1Result.recordset.length}`);", "lineNumber": 66, "file": "src\\app\\api\\ultimate-fix-all\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   - النوع الثاني (كلمات متضاربة): ${type2Result.recordset.length}`);", "lineNumber": 67, "file": "src\\app\\api\\ultimate-fix-all\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`   - النوع الثالث (فارغ مع ملاحظات): ${type3Result.recordset.length}`);", "lineNumber": 68, "file": "src\\app\\api\\ultimate-fix-all\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`📊 تم إعادة بناء ${Math.min(i + batchSize, allMonths.length)}/${allMonths.length} ملخص`);", "lineNumber": 229, "file": "src\\app\\api\\ultimate-fix-all\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('📋 الاحتفاظ بالبيانات القديمة (carscost, housingcost, 3amala) لبنود التكاليف');", "lineNumber": 12, "file": "src\\app\\api\\update-archive-table\\route.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`📋 طلب معتمد: ${req.employeeName} (${req.employeeCode}) - ${req.attendanceType} - ${req.notes}`);", "lineNumber": 95, "file": "src\\app\\attendance\\daily\\page.jsx", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('🔍 تم جلب الطلبات المعتمدة (مزامن):', requestsData);", "lineNumber": 115, "file": "src\\app\\attendance\\daily\\page.jsx", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`📋 طلب معتمد (مزامن): ${req.employeeName} (${req.employeeCode}) - ${req.attendanceType} - ${req.notes}`);", "lineNumber": 118, "file": "src\\app\\attendance\\daily\\page.jsx", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.error('خطأ في جلب الطلبات المعتمدة (مزامن)');", "lineNumber": 122, "file": "src\\app\\attendance\\daily\\page.jsx", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.error('خطأ في جلب الطلبات المعتمدة (مزامن):', error);", "lineNumber": 127, "file": "src\\app\\attendance\\daily\\page.jsx", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`👥 Loaded ${data.length} employees (all employees requested)`);", "lineNumber": 349, "file": "src\\app\\attendance\\daily\\page.jsx", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log('Table check response:', await tableCheckResponse.text());", "lineNumber": 749, "file": "src\\app\\attendance\\daily\\page.jsx", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`🎯 بيانات العرض: ${emp.EmployeeName || emp.FullName} (${code}) - التمام: \"${emp.attendance}\" - الملاحظات: \"${emp.notes || 'لا توجد'}\"`);", "lineNumber": 917, "file": "src\\app\\attendance\\daily\\page.jsx", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`🔍 فحص الموظف إسلام جمعة:`, {", "lineNumber": 927, "file": "src\\app\\attendance\\daily\\page.jsx", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`📋 قرار العرض لإسلام جمعة:`, {", "lineNumber": 944, "file": "src\\app\\attendance\\daily\\page.jsx", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.log(`${index + 1}. العُهدة ${custody.CustodyNumber}: ${custody.CustodianName} (ID: ${custody.ID})`);", "lineNumber": 198, "file": "src\\app\\custody-costs\\page.js", "suggestion": "Remove console statement"}, {"type": "console_statement", "line": "console.error('فشل في تسجيل الإجراء:', await response.text());", "lineNumber": 59, "file": "src\\utils\\actionLogger.js", "suggestion": "Remove console statement"}], "temp_comment": []}