'use client';

import { useEffect, useState } from 'react';

export default function ClearSession() {
  const [cleared, setCleared] = useState(false);
  const [sessionData, setSessionData] = useState({
    localStorage: {},
    cookies: ''
  });

  useEffect(() => {
    // عرض البيانات الحالية
    const currentData = {
      localStorage: {
        isLoggedIn: localStorage.getItem('isLoggedIn'),
        userInfo: localStorage.getItem('userInfo')
      },
      cookies: document.cookie
    };
    setSessionData(currentData);
  }, []);

  const clearAllData = () => {
    // مسح localStorage
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('userInfo');
    localStorage.clear(); // مسح كل شيء

    // مسح جميع الـ cookies
    const cookies = document.cookie.split(";");
    for (let cookie of cookies) {
      const eqPos = cookie.indexOf("=");
      const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
      document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;
      document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; domain=${window.location.hostname}`;
    }

    // مسح cookies محددة
    document.cookie = 'isLoggedIn=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/';
    document.cookie = 'userInfo=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/';

    setCleared(true);

    // تحديث البيانات المعروضة
    setSessionData({
      localStorage: {},
      cookies: document.cookie
    });

  };

  const goToLogin = () => {
    window.location.href = '/login';
  };

  const checkCurrentState = () => {
    const currentData = {
      localStorage: {
        isLoggedIn: localStorage.getItem('isLoggedIn'),
        userInfo: localStorage.getItem('userInfo')
      },
      cookies: document.cookie
    };
    setSessionData(currentData);

  };

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-8">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-2xl w-full">
        <h1 className="text-3xl font-bold text-gray-800 mb-6 text-center">
          مسح بيانات الجلسة
        </h1>

        {cleared && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
            ✅ تم مسح جميع بيانات الجلسة بنجاح!
          </div>
        )}

        <div className="space-y-6">
          {/* عرض البيانات الحالية */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-semibold text-gray-800 mb-3">البيانات الحالية:</h3>
            
            <div className="space-y-3">
              <div>
                <strong>localStorage:</strong>
                <pre className="mt-1 p-2 bg-white rounded text-xs overflow-auto border">
                  {JSON.stringify(sessionData.localStorage, null, 2)}
                </pre>
              </div>
              
              <div>
                <strong>Cookies:</strong>
                <pre className="mt-1 p-2 bg-white rounded text-xs overflow-auto border">
                  {sessionData.cookies || 'لا توجد cookies'}
                </pre>
              </div>
            </div>
          </div>

          {/* الأزرار */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={clearAllData}
              className="bg-red-600 text-white py-3 px-4 rounded-lg hover:bg-red-700 transition-colors font-medium"
            >
              🗑️ مسح جميع البيانات
            </button>

            <button
              onClick={checkCurrentState}
              className="bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium"
            >
              🔍 فحص الحالة الحالية
            </button>

            <button
              onClick={goToLogin}
              className="bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors font-medium"
            >
              🔐 الذهاب لتسجيل الدخول
            </button>
          </div>

          {/* معلومات إضافية */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h3 className="font-semibold text-yellow-800 mb-2">ملاحظات مهمة:</h3>
            <ul className="text-sm text-yellow-700 space-y-1">
              <li>• هذه الصفحة تمسح جميع بيانات تسجيل الدخول المحفوظة</li>
              <li>• يتم مسح localStorage و cookies</li>
              <li>• بعد المسح، ستحتاج لتسجيل الدخول مرة أخرى</li>
              <li>• استخدم هذه الصفحة إذا كنت تواجه مشاكل في تسجيل الدخول</li>
            </ul>
          </div>

          {/* خطوات الاستخدام */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold text-blue-800 mb-2">خطوات حل مشكلة التوجيه التلقائي:</h3>
            <ol className="text-sm text-blue-700 space-y-1 list-decimal list-inside">
              <li>اضغط على "مسح جميع البيانات"</li>
              <li>تأكد من أن localStorage و cookies فارغة</li>
              <li>اضغط على "الذهاب لتسجيل الدخول"</li>
              <li>يجب أن تظهر صفحة تسجيل الدخول بدون توجيه تلقائي</li>
              <li>قم بتسجيل الدخول باستخدام المستخدم 1450 وكلمة المرور 123</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
}
