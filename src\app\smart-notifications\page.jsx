'use client';
import React, { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTheme } from '@/contexts/ThemeContext';
import { 
  Bell, 
  RefreshCw, 
  Check, 
  X, 
  AlertTriangle, 
  Calendar, 
  User, 
  Clock,
  Filter,
  Eye,
  CheckCircle,
  XCircle
} from 'lucide-react';

export default function SmartNotificationsPage() {
  const { isArabic } = useLanguage();
  const { isDarkMode } = useTheme();
  
  const [notifications, setNotifications] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');

  // جلب الإشعارات الذكية
  const fetchNotifications = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/smart-notifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'list',
          limit: 100,
          status: 'all'
        })
      });
      const result = await response.json();

      if (result.success) {
        setNotifications(result.notifications || []);

      } else {

      }
    } catch (error) {

    }
    setLoading(false);
  };

  useEffect(() => {
    fetchNotifications();
    // تحديث كل 30 ثانية
    const interval = setInterval(fetchNotifications, 30000);
    return () => clearInterval(interval);
  }, []);

  // فلترة الإشعارات
  const filteredNotifications = notifications.filter(notification => {
    const matchesStatus = filter === 'all' || notification.Status === filter;
    const matchesPriority = priorityFilter === 'all' || notification.Priority === priorityFilter;
    return matchesStatus && matchesPriority;
  });

  // اتخاذ إجراء على الإشعار
  const takeAction = async (notificationId) => {
    try {
      const response = await fetch('/api/smart-notifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'takeAction',
          notificationId: notificationId,
          actionBy: 'المدير'
        })
      });

      const result = await response.json();
      if (result.success) {
        fetchNotifications();
      }
    } catch (error) {

    }
  };

  // تجاهل الإشعار
  const dismissNotification = async (notificationId) => {
    try {
      const response = await fetch('/api/smart-notifications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'dismiss',
          notificationId: notificationId
        })
      });

      const result = await response.json();
      if (result.success) {
        fetchNotifications();
      }
    } catch (error) {

    }
  };

  // لون الأولوية
  const getPriorityColor = (priority) => {
    const colors = {
      'low': 'bg-gray-100 text-gray-800',
      'medium': 'bg-blue-100 text-blue-800',
      'high': 'bg-orange-100 text-orange-800',
      'urgent': 'bg-red-100 text-red-800'
    };
    return colors[priority] || 'bg-gray-100 text-gray-800';
  };

  // أيقونة نوع الإشعار
  const getNotificationIcon = (type) => {
    const icons = {
      'LEAVE_REQUEST_SUBMITTED': Calendar,
      'LEAVE_REQUEST_APPROVED': CheckCircle,
      'LEAVE_REQUEST_REJECTED': XCircle,
      'LEAVE_REQUEST_UPDATED': Clock,
      'SYSTEM_ALERT': AlertTriangle,
      'DATA_MISSING': AlertTriangle,
      'EXPIRY_WARNING': Clock
    };
    return icons[type] || Bell;
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto">
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="flex items-center justify-between">
            <div>
              <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
                الإشعارات الذكية
              </h1>
              <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                نظام الإشعارات الذكية الشامل للنظام
              </p>
            </div>
            <div className="flex gap-3">
              <button
                onClick={fetchNotifications}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
              >
                <RefreshCw className="w-4 h-4" />
                تحديث
              </button>
            </div>
          </div>
        </div>

        {/* فلاتر */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-4 mb-6`}>
          <div className="flex items-center gap-4">
            <Filter className="w-5 h-5 text-gray-500" />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className={`px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                isDarkMode 
                  ? 'bg-slate-800 border-slate-600 text-white' 
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              <option value="all">جميع الحالات</option>
              <option value="pending">قيد الانتظار</option>
              <option value="read">مقروءة</option>
              <option value="action_taken">تم اتخاذ إجراء</option>
              <option value="dismissed">مُتجاهلة</option>
            </select>

            <select
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className={`px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                isDarkMode 
                  ? 'bg-slate-800 border-slate-600 text-white' 
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              <option value="all">جميع الأولويات</option>
              <option value="low">منخفضة</option>
              <option value="medium">متوسطة</option>
              <option value="high">عالية</option>
              <option value="urgent">عاجلة</option>
            </select>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <Bell className="w-8 h-8 text-blue-500 mr-3" />
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>إجمالي الإشعارات</p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {filteredNotifications.length}
                </p>
              </div>
            </div>
          </div>
          
          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <Clock className="w-8 h-8 text-orange-500 mr-3" />
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>قيد الانتظار</p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {filteredNotifications.filter(n => n.Status === 'pending').length}
                </p>
              </div>
            </div>
          </div>
          
          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <AlertTriangle className="w-8 h-8 text-red-500 mr-3" />
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>أولوية عالية</p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {filteredNotifications.filter(n => n.Priority === 'high' || n.Priority === 'urgent').length}
                </p>
              </div>
            </div>
          </div>
          
          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <CheckCircle className="w-8 h-8 text-green-500 mr-3" />
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>تم اتخاذ إجراء</p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {filteredNotifications.filter(n => n.ActionTaken).length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* قائمة الإشعارات */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border overflow-hidden`}>
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <RefreshCw className="w-6 h-6 animate-spin text-blue-500" />
              <span className="mr-2">جاري التحميل...</span>
            </div>
          ) : filteredNotifications.length === 0 ? (
            <div className="text-center py-12">
              <Bell className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className={`text-lg ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                لا توجد إشعارات
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {filteredNotifications.map((notification) => {
                const IconComponent = getNotificationIcon(notification.NotificationType);
                return (
                  <div
                    key={notification.ID}
                    className={`p-6 ${
                      notification.Status === 'pending' 
                        ? isDarkMode ? 'bg-blue-900/20' : 'bg-blue-50'
                        : isDarkMode ? 'hover:bg-slate-800' : 'hover:bg-gray-50'
                    } transition-colors`}
                  >
                    <div className="flex items-start gap-4">
                      <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${
                        notification.Priority === 'high' || notification.Priority === 'urgent'
                          ? 'bg-red-100 text-red-600'
                          : 'bg-blue-100 text-blue-600'
                      }`}>
                        <IconComponent className="w-5 h-5" />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                            {notification.Title}
                          </h3>
                          <div className="flex items-center gap-2">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(notification.Priority)}`}>
                              {notification.Priority === 'low' ? 'منخفضة' :
                               notification.Priority === 'medium' ? 'متوسطة' :
                               notification.Priority === 'high' ? 'عالية' : 'عاجلة'}
                            </span>
                            <span className={`text-xs ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>
                              {new Date(notification.CreatedAt).toLocaleString('ar-EG')}
                            </span>
                          </div>
                        </div>
                        
                        <p className={`text-sm mb-3 ${isDarkMode ? 'text-slate-300' : 'text-gray-700'}`}>
                          {notification.Message}
                        </p>
                        
                        {/* تفاصيل إضافية */}
                        <div className="flex items-center gap-4 text-xs text-gray-500 mb-3">
                          {notification.EmployeeName && (
                            <div className="flex items-center gap-1">
                              <User className="w-3 h-3" />
                              <span>{notification.EmployeeName} ({notification.EmployeeID})</span>
                            </div>
                          )}
                          {notification.LeaveStartDate && (
                            <div className="flex items-center gap-1">
                              <Calendar className="w-3 h-3" />
                              <span>تاريخ البداية: {new Date(notification.LeaveStartDate).toLocaleDateString('ar-EG')}</span>
                            </div>
                          )}
                          {notification.SystemUserID && (
                            <div className="flex items-center gap-1">
                              <User className="w-3 h-3" />
                              <span>النظام: {notification.SystemUserID}</span>
                            </div>
                          )}
                        </div>
                        
                        {/* أزرار الإجراءات */}
                        {notification.Status === 'pending' && (
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => takeAction(notification.ID)}
                              className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs flex items-center gap-1"
                            >
                              <Check className="w-3 h-3" />
                              اتخاذ إجراء
                            </button>
                            <button
                              onClick={() => dismissNotification(notification.ID)}
                              className="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-xs flex items-center gap-1"
                            >
                              <X className="w-3 h-3" />
                              تجاهل
                            </button>
                          </div>
                        )}
                        
                        {notification.ActionTaken && (
                          <div className={`text-xs ${isDarkMode ? 'text-green-400' : 'text-green-600'}`}>
                            تم اتخاذ إجراء بواسطة: {notification.ActionBy} في {new Date(notification.ActionDate).toLocaleString('ar-EG')}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
