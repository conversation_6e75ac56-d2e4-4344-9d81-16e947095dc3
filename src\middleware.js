import { NextResponse } from 'next/server';

export function middleware(request) {
  try {
    const { pathname } = request.nextUrl;

    // إنشاء response جديد لتجنب مشكلة body locked
    const response = NextResponse.next();

    // المسارات المسموحة بدون تسجيل دخول (فقط الضروري)
    const publicPaths = [
      '/',
      '/login',
      '/api/login',
      '/api/auth/authenticate',
      '/clear-session',
      '/check-users',
      '/api/check-users',
      '/quick-login',
      '/test-login'
    ];

  // جميع المسارات المحمية (تحتاج تسجيل دخول)
  const protectedPaths = [
    '/dashboard',
    '/employees',
    '/attendance',
    '/cars',
    '/apartments',
    '/custody-costs',
    '/costs',
    '/reports',
    '/organizational-chart',
    '/organizational-chart-simple',
    '/organizational-chart-pro',
    '/notifications',
    '/alerts',
    '/smart-notifications',
    '/backup-system',
    '/system-health',
    '/database-scan',
    '/database-audit',
    '/column-impact',
    '/api-audit',
    '/check-tables',
    '/test-data',
    '/system-setup',
    '/system-management',
    '/system-health-review',
    '/database-analysis',
    '/complete-setup',
    '/cycle-filters-demo'
  ];

  // مسارات API المحمية التي تحتاج تسجيل دخول
  const protectedApiPaths = [
    '/api/dashboard-stats',
    '/api/employee-dashboard-stats',
    '/api/employees',
    '/api/attendance',
    '/api/cars',
    '/api/apartments',
    '/api/custody-costs',
    '/api/costs',
    '/api/reports',
    '/api/organizational-chart',
    '/api/notifications',
    '/api/alerts',
    '/api/project-custody-costs'
  ];

  // مسارات النظام التي يجب تجاهلها
  const systemPaths = [
    '/_next/',
    '/favicon.ico',
    '/static/',
    '/__nextjs',
    '/api/_next'
  ];

  // التحقق من مسارات النظام
  const isSystemPath = systemPaths.some(path => pathname.startsWith(path));
  if (isSystemPath) {
    return response;
  }

  // التحقق من أنواع المسارات
  const isPublicPath = publicPaths.some(path => pathname === path || pathname.startsWith(path));
  const isProtectedPath = protectedPaths.some(path => pathname.startsWith(path));
  const isProtectedApiPath = protectedApiPaths.some(path => pathname.startsWith(path));

  // السماح للمسارات العامة
  if (isPublicPath && !isProtectedPath) {
    return response;
  }

  // فحص التوكن للمسارات المحمية
  if (isProtectedPath || isProtectedApiPath) {
    const token = request.cookies.get('auth-token')?.value;

    if (!token) {

      if (isProtectedApiPath) {
        return new NextResponse(
          JSON.stringify({ success: false, error: 'غير مصرح - يجب تسجيل الدخول أولاً' }),
          {
            status: 401,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      }

      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('redirect', pathname);
      return NextResponse.redirect(loginUrl);
    }

  }

  // إذا كان المسار عام، السماح بالمرور
  if (isPublicPath) {
    return response;
  }

  // للمسارات المحمية (صفحات أو API)، التحقق من cookies
  if (isProtectedApiPath || (!isPublicPath && !pathname.startsWith('/api'))) {
    const isLoggedIn = request.cookies.get('isLoggedIn')?.value;

    if (isLoggedIn === 'true') {
      return response;
    }

    // للـ API المحمية، إرجاع 401
    if (isProtectedApiPath) {
      return new NextResponse(
        JSON.stringify({ error: 'غير مصرح بالوصول' }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // للصفحات، إعادة توجيه لتسجيل الدخول
    const loginUrl = new URL('/login', request.url);
    return NextResponse.redirect(loginUrl);
  }

  // السماح بباقي مسارات API غير المحمية
  return response;

} catch (error) {
    console.error('خطأ في middleware:', error);

    // في حالة الخطأ، السماح بالمرور لتجنب كسر الموقع
    return NextResponse.next();
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - api routes that don't need protection
     */
    '/((?!_next/static|_next/image|favicon.ico|__nextjs).*)',
  ],
};
