async function handler() {
  const today = new Date().toISOString().split('T')[0];

  const [totalEmployees, presentToday, activeLeaves, assets] =
    await sql.transaction([
      sql`
      SELECT COUNT(*) as count 
      FROM Employees 
      WHERE IsActive = true
    `,

      sql`
      SELECT COUNT(*) as count 
      FROM Attendance 
      WHERE AttendanceDate = ${today} 
      AND Status = 'Present'
    `,

      sql`
      SELECT COUNT(*) as count 
      FROM LeaveRequests 
      WHERE Status = 'Approved' 
      AND ${today} BETWEEN StartDate AND EndDate
    `,

      sql`
      SELECT 
        (SELECT COUNT(*) FROM Apartments WHERE Status = 'Active') +
        (SELECT COUNT(*) FROM Cars WHERE Status = 'Active') 
      as count
    `,
    ]);

  return {
    totalEmployees: totalEmployees[0].count,
    presentToday: presentToday[0].count,
    activeLeaves: activeLeaves[0].count,
    totalAssets: assets[0].count,
  };
}
