'use client';

import { useState, useEffect } from 'react';

export default function ActiveCarSelector({ 
  selectedCarCode, 
  onCarSelect, 
  showDetails = true,
  className = "" 
}) {
  const [activeCars, setActiveCars] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedCarDetails, setSelectedCarDetails] = useState(null);

  useEffect(() => {
    loadActiveCars();
  }, []);

  useEffect(() => {
    if (selectedCarCode) {
      loadCarDetails(selectedCarCode);
    } else {
      setSelectedCarDetails(null);
    }
  }, [selectedCarCode]);

  // جلب السيارات النشطة
  const loadActiveCars = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/active-cars');
      const result = await response.json();

      if (result.success) {
        setActiveCars(result.data);
      } else {

        setActiveCars([]);
      }
    } catch (error) {

      setActiveCars([]);
    } finally {
      setLoading(false);
    }
  };

  // جلب تفاصيل السيارة المحددة
  const loadCarDetails = async (carCode) => {
    try {
      const response = await fetch('/api/active-cars', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getCarDetails',
          carCode: carCode
        })
      });

      const result = await response.json();
      if (result.success) {
        setSelectedCarDetails(result.data);
      } else {
        setSelectedCarDetails(null);
      }
    } catch (error) {

      setSelectedCarDetails(null);
    }
  };

  // معالجة تغيير السيارة المحددة
  const handleCarChange = (carCode) => {
    onCarSelect(carCode);
  };

  // تنسيق العملة
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(amount || 0);
  };

  // تنسيق التاريخ
  const formatDate = (dateString) => {
    if (!dateString) return 'غير محدد';
    return new Date(dateString).toLocaleDateString('ar-EG');
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* قائمة اختيار السيارة */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          اختيار السيارة *
        </label>
        <select
          value={selectedCarCode || ''}
          onChange={(e) => handleCarChange(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
          disabled={loading}
        >
          <option value="">-- اختر السيارة --</option>
          {activeCars.map((car) => (
            <option key={car.CarCode} value={car.CarCode}>
              {car.CarCode} - {car.CarNumber} - {car.CarType}
              {car.ModelYear && ` (${car.ModelYear})`}
            </option>
          ))}
        </select>
        
        {loading && (
          <p className="text-sm text-gray-500 mt-1">جاري تحميل السيارات النشطة...</p>
        )}
        
        {!loading && activeCars.length === 0 && (
          <p className="text-sm text-red-500 mt-1">لا توجد سيارات نشطة متاحة</p>
        )}
      </div>

      {/* تفاصيل السيارة المحددة */}
      {showDetails && selectedCarDetails && (
        <div className="bg-gray-50 rounded-lg p-4 border">
          <h4 className="font-medium text-gray-800 mb-3">تفاصيل السيارة</h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* معلومات أساسية */}
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">كود السيارة:</span>
                <span className="text-sm font-medium">{selectedCarDetails.CarCode}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">رقم السيارة:</span>
                <span className="text-sm font-medium">{selectedCarDetails.CarNumber}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">النوع:</span>
                <span className="text-sm font-medium">
                  {selectedCarDetails.CarType} {selectedCarDetails.ModelYear}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">المسار:</span>
                <span className="text-sm font-medium">{selectedCarDetails.Route || 'غير محدد'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">المقاول:</span>
                <span className="text-sm font-medium">{selectedCarDetails.ContractorName || 'غير محدد'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">قيمة الإيجار:</span>
                <span className="text-sm font-medium text-green-600">
                  {formatCurrency(selectedCarDetails.RentalValue)}
                </span>
              </div>
            </div>

            {/* إحصائيات التكاليف */}
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">إجمالي التكاليف:</span>
                <span className="text-sm font-medium text-orange-600">
                  {formatCurrency(selectedCarDetails.costsSummary?.TotalAmount)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">عدد التكاليف:</span>
                <span className="text-sm font-medium">
                  {selectedCarDetails.costsSummary?.TotalCosts || 0}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">متوسط التكلفة:</span>
                <span className="text-sm font-medium">
                  {formatCurrency(selectedCarDetails.costsSummary?.AvgAmount)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">تاريخ البداية:</span>
                <span className="text-sm font-medium">
                  {formatDate(selectedCarDetails.StartDate)}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">تاريخ النهاية:</span>
                <span className="text-sm font-medium">
                  {formatDate(selectedCarDetails.EndDate)}
                </span>
              </div>
            </div>
          </div>

          {/* المستفيدين */}
          {selectedCarDetails.beneficiaries && selectedCarDetails.beneficiaries.length > 0 && (
            <div className="mt-4">
              <h5 className="text-sm font-medium text-gray-700 mb-2">المستفيدين:</h5>
              <div className="space-y-1">
                {selectedCarDetails.beneficiaries.map((beneficiary, index) => (
                  <div key={index} className="flex items-center justify-between text-xs bg-white p-2 rounded">
                    <div>
                      <span className="font-medium">{beneficiary.EmployeeName}</span>
                      <span className="text-gray-500 mr-2">({beneficiary.EmployeeCode})</span>
                    </div>
                    <div className="text-gray-600">
                      {beneficiary.JobTitle} - {beneficiary.Department}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* آخر التكاليف */}
          {selectedCarDetails.recentCosts && selectedCarDetails.recentCosts.length > 0 && (
            <div className="mt-4">
              <h5 className="text-sm font-medium text-gray-700 mb-2">آخر التكاليف:</h5>
              <div className="space-y-1">
                {selectedCarDetails.recentCosts.slice(0, 3).map((cost, index) => (
                  <div key={index} className="flex items-center justify-between text-xs bg-white p-2 rounded">
                    <div>
                      <span className="font-medium">{cost.Description}</span>
                      <span className="text-gray-500 mr-2">({cost.CostType})</span>
                    </div>
                    <div className="text-right">
                      <div className="font-medium text-orange-600">{formatCurrency(cost.Amount)}</div>
                      <div className="text-gray-500">{formatDate(cost.Date)}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
