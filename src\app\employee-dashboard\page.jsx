"use client";
import React, { useState } from "react";

function MainComponent() {
  const [selectedLang] = useState("ar");
  const [employeeStats, setEmployeeStats] = useState({
    total: 0,
    delegated: 0,
    expatriates: 0,
  });
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch("/api/employee-stats");
        if (!response.ok) {
          throw new Error("فشل في تحميل إحصائيات الموظفين");
        }
        const data = await response.json();
        setEmployeeStats(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  return (
    <div
      dir="rtl"
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          لوحة إدارة الموظفين
        </h1>

        {/* إحصائيات الموظفين */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-blue-50 dark:bg-blue-900 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-600 dark:text-blue-200">
                  إجمالي الموظفين
                </p>
                <h3 className="text-2xl font-bold">{employeeStats.total}</h3>
              </div>
              <i className="fas fa-users text-3xl text-blue-500 dark:text-blue-300"></i>
            </div>
          </div>

          <div className="bg-green-50 dark:bg-green-900 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-600 dark:text-green-200">
                  الموظفين المنتدبين
                </p>
                <h3 className="text-2xl font-bold">
                  {employeeStats.delegated}
                </h3>
              </div>
              <i className="fas fa-user-tie text-3xl text-green-500 dark:text-green-300"></i>
            </div>
          </div>

          <div className="bg-purple-50 dark:bg-purple-900 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-600 dark:text-purple-200">
                  الموظفين المغتربين
                </p>
                <h3 className="text-2xl font-bold">
                  {employeeStats.expatriates}
                </h3>
              </div>
              <i className="fas fa-plane text-3xl text-purple-500 dark:text-purple-300"></i>
            </div>
          </div>
        </div>

        {/* أزرار سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <a
            href="/add-employee"
            className="flex items-center justify-center bg-blue-600 text-white p-4 rounded-lg hover:bg-blue-700 transition-colors"
          >
            <i className="fas fa-user-plus ml-2"></i>
            إضافة موظف جديد
          </a>

          <a
            href="/search-employee"
            className="flex items-center justify-center bg-green-600 text-white p-4 rounded-lg hover:bg-green-700 transition-colors"
          >
            <i className="fas fa-search ml-2"></i>
            البحث عن موظف
          </a>

          <a
            href="/departments"
            className="flex items-center justify-center bg-purple-600 text-white p-4 rounded-lg hover:bg-purple-700 transition-colors"
          >
            <i className="fas fa-sitemap ml-2"></i>
            البحث بالأقسام
          </a>
        </div>

        {/* تفاصيل الموظف */}
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
          {selectedEmployee ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <img
                  src={selectedEmployee.photo || "/default-avatar.png"}
                  alt="صورة الموظف"
                  className="w-48 h-48 rounded-full mx-auto mb-4 object-cover"
                />
                <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                  {selectedEmployee.name}
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {selectedEmployee.position}
                </p>
              </div>

              <div className="md:col-span-2 space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-gray-600 dark:text-gray-400">
                      الرقم الوظيفي
                    </p>
                    <p className="font-bold">{selectedEmployee.employeeId}</p>
                  </div>
                  <div>
                    <p className="text-gray-600 dark:text-gray-400">القسم</p>
                    <p className="font-bold">{selectedEmployee.department}</p>
                  </div>
                  <div>
                    <p className="text-gray-600 dark:text-gray-400">
                      تاريخ التعيين
                    </p>
                    <p className="font-bold">{selectedEmployee.hireDate}</p>
                  </div>
                  <div>
                    <p className="text-gray-600 dark:text-gray-400">الحالة</p>
                    <p className="font-bold">{selectedEmployee.status}</p>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-600 dark:text-gray-400">
              <i className="fas fa-user-circle text-6xl mb-4"></i>
              <p>اختر موظفاً لعرض تفاصيله</p>
            </div>
          )}
        </div>

        {/* أزرار التحكم */}
        <div className="flex flex-wrap gap-4 mt-8">
          <button className="flex items-center px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <i className="fas fa-save ml-2"></i>
            حفظ
          </button>

          <button className="flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <i className="fas fa-edit ml-2"></i>
            تعديل
          </button>

          <button className="flex items-center px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
            <i className="fas fa-print ml-2"></i>
            طباعة
          </button>

          <button className="flex items-center px-6 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
            <i className="fas fa-archive ml-2"></i>
            أرشيف
          </button>

          <a
            href="/"
            className="flex items-center px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            <i className="fas fa-arrow-right ml-2"></i>
            عودة
          </a>
        </div>
      </div>
    </div>
  );
}

export default MainComponent;