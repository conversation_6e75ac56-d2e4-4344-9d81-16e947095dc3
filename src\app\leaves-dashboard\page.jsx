'use client';

import { useState, useEffect } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';
import MainLayout from '@/components/MainLayout';
import DashboardCard from '@/components/DashboardCard';
import {
  FiCalendar, FiUsers, FiClock, FiCheckCircle, FiXCircle,
  FiAlertCircle, FiTrendingUp, FiTrendingDown, FiBarChart2,
  FiPieChart, FiActivity, FiFileText, FiUserCheck, FiUserX
} from 'react-icons/fi';

export default function LeavesDashboardPage() {
  const { isArabic } = useLanguage();
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState('month');

  // جلب بيانات الداش بورد
  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/leaves-dashboard', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          period: selectedPeriod
        }),
      });

      const result = await response.json();
      if (result.success) {
        setDashboardData(result.data);
      } else {

      }
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDashboardData();
  }, [selectedPeriod]);

  // بيانات وهمية للعرض (سيتم استبدالها بالبيانات الحقيقية)
  const mockData = {
    totalRequests: 156,
    approvedRequests: 128,
    pendingRequests: 18,
    rejectedRequests: 10,
    totalDays: 1240,
    averageDays: 8.5,
    topEmployees: [
      { name: 'أحمد محمد', days: 25 },
      { name: 'فاطمة علي', days: 22 },
      { name: 'محمد حسن', days: 20 }
    ],
    monthlyTrend: {
      current: 156,
      previous: 142,
      change: 9.9
    },
    leaveTypes: {
      annual: 89,
      emergency: 34,
      sick: 23,
      unpaid: 10
    }
  };

  const data = dashboardData || mockData;

  return (
    <MainLayout>
      <div className="p-6 max-w-7xl mx-auto">
        {/* العنوان والفلاتر */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                {isArabic ? 'لوحة تحكم الإجازات' : 'Leaves Dashboard'}
              </h1>
              <p className="text-gray-700 dark:text-gray-400">
                {isArabic ? 'نظرة شاملة على إحصائيات وبيانات الإجازات' : 'Comprehensive overview of leave statistics and data'}
              </p>
            </div>
            
            <div className="flex items-center gap-4">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value)}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
              >
                <option value="week">{isArabic ? 'هذا الأسبوع' : 'This Week'}</option>
                <option value="month">{isArabic ? 'هذا الشهر' : 'This Month'}</option>
                <option value="quarter">{isArabic ? 'هذا الربع' : 'This Quarter'}</option>
                <option value="year">{isArabic ? 'هذا العام' : 'This Year'}</option>
              </select>
              
              <button
                onClick={fetchDashboardData}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <FiActivity />
                {isArabic ? 'تحديث' : 'Refresh'}
              </button>
            </div>
          </div>
        </div>

        {/* الإحصائيات الرئيسية */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <DashboardCard
            title={isArabic ? 'إجمالي الطلبات' : 'Total Requests'}
            value={data.totalRequests}
            icon={FiFileText}
            color="blue"
            href="/my-requests"
            description={isArabic ? 'جميع طلبات الإجازة' : 'All leave requests'}
            trend="up"
            trendValue={`+${data.monthlyTrend?.change || 0}%`}
            isLoading={loading}
            animationDelay={0}
          />

          <DashboardCard
            title={isArabic ? 'الطلبات المعتمدة' : 'Approved Requests'}
            value={data.approvedRequests}
            icon={FiCheckCircle}
            color="green"
            href="/approved-leaves"
            description={isArabic ? 'الطلبات المعتمدة' : 'Approved requests'}
            trend="up"
            trendValue={`${Math.round((data.approvedRequests / data.totalRequests) * 100)}%`}
            isLoading={loading}
            animationDelay={100}
          />

          <DashboardCard
            title={isArabic ? 'قيد المراجعة' : 'Pending Review'}
            value={data.pendingRequests}
            icon={FiClock}
            color="yellow"
            href="/my-requests?status=pending"
            description={isArabic ? 'تحتاج مراجعة' : 'Needs review'}
            trend="down"
            trendValue={`${Math.round((data.pendingRequests / data.totalRequests) * 100)}%`}
            isLoading={loading}
            animationDelay={200}
          />

          <DashboardCard
            title={isArabic ? 'الطلبات المرفوضة' : 'Rejected Requests'}
            value={data.rejectedRequests}
            icon={FiXCircle}
            color="red"
            href="/my-requests?status=rejected"
            description={isArabic ? 'الطلبات المرفوضة' : 'Rejected requests'}
            trend="down"
            trendValue={`${Math.round((data.rejectedRequests / data.totalRequests) * 100)}%`}
            isLoading={loading}
            animationDelay={300}
          />
        </div>

        {/* إحصائيات الأيام */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <DashboardCard
            title={isArabic ? 'إجمالي أيام الإجازة' : 'Total Leave Days'}
            value={data.totalDays}
            icon={FiCalendar}
            color="purple"
            description={isArabic ? 'جميع الأيام المعتمدة' : 'All approved days'}
            isLoading={loading}
            animationDelay={400}
          />

          <DashboardCard
            title={isArabic ? 'متوسط أيام الإجازة' : 'Average Leave Days'}
            value={data.averageDays}
            icon={FiBarChart2}
            color="indigo"
            description={isArabic ? 'متوسط لكل طلب' : 'Average per request'}
            isLoading={loading}
            animationDelay={500}
          />

          <DashboardCard
            title={isArabic ? 'الموظفين النشطين' : 'Active Employees'}
            value={data.topEmployees?.length || 0}
            icon={FiUsers}
            color="pink"
            href="/employees"
            description={isArabic ? 'لديهم إجازات' : 'With leaves'}
            isLoading={loading}
            animationDelay={600}
          />
        </div>

        {/* أنواع الإجازات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <DashboardCard
            title={isArabic ? 'إجازة اعتيادية' : 'Annual Leave'}
            value={data.leaveTypes?.annual || 0}
            icon={FiCalendar}
            color="blue"
            href="/requests/leave?type=annual"
            description={isArabic ? 'الإجازة السنوية' : 'Regular annual leave'}
            isLoading={loading}
            animationDelay={700}
          />

          <DashboardCard
            title={isArabic ? 'إجازة عارضة' : 'Emergency Leave'}
            value={data.leaveTypes?.emergency || 0}
            icon={FiAlertCircle}
            color="orange"
            href="/requests/leave?type=emergency"
            description={isArabic ? 'الإجازة الطارئة' : 'Emergency situations'}
            isLoading={loading}
            animationDelay={800}
          />

          <DashboardCard
            title={isArabic ? 'إجازة مرضية' : 'Sick Leave'}
            value={data.leaveTypes?.sick || 0}
            icon={FiUserX}
            color="red"
            href="/requests/leave?type=sick"
            description={isArabic ? 'الإجازة المرضية' : 'Medical leave'}
            isLoading={loading}
            animationDelay={900}
          />

          <DashboardCard
            title={isArabic ? 'إجازة بدون أجر' : 'Unpaid Leave'}
            value={data.leaveTypes?.unpaid || 0}
            icon={FiUserCheck}
            color="gray"
            href="/requests/leave?type=unpaid"
            description={isArabic ? 'بدون راتب' : 'Without salary'}
            isLoading={loading}
            animationDelay={1000}
          />
        </div>

        {/* الإجراءات السريعة */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
            {isArabic ? 'الإجراءات السريعة' : 'Quick Actions'}
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={() => window.location.href = '/requests/leave'}
              className="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg flex items-center gap-3 transition-colors"
            >
              <FiCalendar className="text-xl" />
              <span>{isArabic ? 'طلب إجازة جديد' : 'New Leave Request'}</span>
            </button>
            
            <button
              onClick={() => window.location.href = '/leave-balances'}
              className="bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg flex items-center gap-3 transition-colors"
            >
              <FiPieChart className="text-xl" />
              <span>{isArabic ? 'رصيد الإجازات' : 'Leave Balance'}</span>
            </button>
            
            <button
              onClick={() => window.location.href = '/approved-leaves'}
              className="bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-lg flex items-center gap-3 transition-colors"
            >
              <FiCheckCircle className="text-xl" />
              <span>{isArabic ? 'الإجازات المعتمدة' : 'Approved Leaves'}</span>
            </button>
          </div>
        </div>

        {/* أهم الموظفين */}
        {data.topEmployees && data.topEmployees.length > 0 && (
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4">
              {isArabic ? 'الموظفين الأكثر استخداماً للإجازات' : 'Top Leave Users'}
            </h2>
            
            <div className="space-y-3">
              {data.topEmployees.map((employee, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold ${
                      index === 0 ? 'bg-yellow-500' : 
                      index === 1 ? 'bg-gray-400' : 
                      'bg-orange-400'
                    }`}>
                      {index + 1}
                    </div>
                    <span className="font-medium text-gray-800 dark:text-gray-200">
                      {employee.name}
                    </span>
                  </div>
                  <span className="text-blue-600 font-semibold">
                    {employee.days} {isArabic ? 'يوم' : 'days'}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
