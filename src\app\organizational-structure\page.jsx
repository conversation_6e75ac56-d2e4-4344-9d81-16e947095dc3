'use client';

import MainLayout from '@/components/MainLayout';
import {
    Building,
    ChevronDown,
    ChevronRight,
    Edit,
    Plus,
    Search,
    Trash2,
    UserPlus,
    Users
} from 'lucide-react';
import { useEffect, useState } from 'react';

export default function OrganizationalStructurePage() {
  const [structure, setStructure] = useState([]);
  const [employees, setEmployees] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showEmployeeModal, setShowEmployeeModal] = useState(false);
  const [selectedNode, setSelectedNode] = useState(null);
  const [expandedNodes, setExpandedNodes] = useState(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const [syncing, setSyncing] = useState(false);
  const [autoSyncing, setAutoSyncing] = useState(false);
  const [syncStats, setSyncStats] = useState(null);

  // جلب البيانات عند تحميل الصفحة
  useEffect(() => {
    fetchStructure();
    fetchEmployees();
    fetchSyncStats();
  }, []);

  const fetchStructure = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/organizational-structure');
      const result = await response.json();

      if (result.success) {
        setStructure(result.data);
      }
    } catch (error) {
      console.error('خطأ في جلب الهيكل الوظيفي:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchEmployees = async () => {
    try {
      const response = await fetch('/api/employees');
      const result = await response.json();

      if (result.success) {
        setEmployees(result.data);
      }
    } catch (error) {
      console.error('خطأ في جلب الموظفين:', error);
    }
  };

  // تبديل توسيع/طي العقدة
  const toggleNode = (nodeId) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  // إضافة قسم جديد
  const handleAddDepartment = () => {
    setSelectedNode(null);
    setShowAddModal(true);
  };

  // تعديل قسم
  const handleEditDepartment = (node) => {
    setSelectedNode(node);
    setShowEditModal(true);
  };

  // حذف قسم
  const handleDeleteDepartment = async (nodeId) => {
    if (confirm('هل أنت متأكد من حذف هذا القسم؟')) {
      try {
        const response = await fetch(`/api/organizational-structure/${nodeId}`, {
          method: 'DELETE'
        });

        if (response.ok) {
          fetchStructure();
        }
      } catch (error) {
        console.error('خطأ في حذف القسم:', error);
      }
    }
  };

  // مزامنة الموظفين مع الهيكل الوظيفي
  const handleSyncEmployees = async () => {
    if (confirm('هل تريد مزامنة جميع الموظفين مع الهيكل الوظيفي؟\nسيتم ربط كل موظف بالقسم الذي يديره مديره المباشر.')) {
      setSyncing(true);
      try {
        const response = await fetch('/api/organizational-structure/sync-employees', {
          method: 'POST'
        });

        const result = await response.json();

        if (result.success) {
          alert(`تم بنجاح!\nتم مزامنة ${result.data.syncedCount} موظف من أصل ${result.data.totalEmployees} موظف`);
          fetchStructure();
        } else {
          alert('خطأ في المزامنة: ' + result.error);
        }
      } catch (error) {
        console.error('خطأ في مزامنة الموظفين:', error);
        alert('خطأ في مزامنة الموظفين');
      } finally {
        setSyncing(false);
      }
    }
  };

  // جلب إحصائيات المزامنة
  const fetchSyncStats = async () => {
    try {
      const response = await fetch('/api/auto-sync-employees');
      const result = await response.json();

      if (result.success) {
        setSyncStats(result.data);
      }
    } catch (error) {
      console.error('خطأ في جلب إحصائيات المزامنة:', error);
    }
  };

  // المزامنة التلقائية للموظفين الجدد
  const handleAutoSync = async () => {
    if (confirm('هل تريد مزامنة جميع الموظفين الجدد تلقائياً؟\nسيتم إنشاء فرق جديدة للمديرين حسب الحاجة.')) {
      setAutoSyncing(true);
      try {
        const response = await fetch('/api/auto-sync-employees', {
          method: 'POST'
        });

        const result = await response.json();

        if (result.success) {
          alert(`تم بنجاح!\nتم مزامنة ${result.data.syncedCount} موظف\nتم إنشاء ${result.data.newUnitsCreated} وحدة جديدة`);
          fetchStructure();
          fetchSyncStats();
        } else {
          alert('خطأ في المزامنة: ' + result.error);
        }
      } catch (error) {
        console.error('خطأ في المزامنة التلقائية:', error);
        alert('خطأ في المزامنة التلقائية');
      } finally {
        setAutoSyncing(false);
      }
    }
  };

  // رسم عقدة في الشجرة
  const renderNode = (node, level = 0) => {
    const hasChildren = node.children && node.children.length > 0;
    const isExpanded = expandedNodes.has(node.id);

    return (
      <div key={node.id} className="mb-2">
        <div
          className={`flex items-center p-3 rounded-lg border transition-all duration-200 ${
            level === 0
              ? 'bg-blue-50 border-blue-200 shadow-md'
              : level === 1
              ? 'bg-green-50 border-green-200'
              : 'bg-gray-50 border-gray-200'
          } hover:shadow-lg`}
          style={{ marginRight: `${level * 24}px` }}
        >
          {/* أيقونة التوسيع/الطي */}
          {hasChildren && (
            <button
              onClick={() => toggleNode(node.id)}
              className="ml-2 p-1 hover:bg-white rounded"
            >
              {isExpanded ? (
                <ChevronDown className="w-4 h-4 text-gray-600" />
              ) : (
                <ChevronRight className="w-4 h-4 text-gray-600" />
              )}
            </button>
          )}

          {/* أيقونة القسم */}
          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ml-3 ${
            level === 0 ? 'bg-blue-600' : level === 1 ? 'bg-green-600' : 'bg-gray-600'
          }`}>
            {level === 0 ? (
              <Building className="w-5 h-5 text-white" />
            ) : (
              <Users className="w-5 h-5 text-white" />
            )}
          </div>

          {/* معلومات القسم */}
          <div className="flex-1">
            <h3 className="font-bold text-gray-800">{node.name}</h3>
            <p className="text-sm text-gray-600">
              مدير: {node.managerName} - كود: {node.managerCode}
            </p>
            <p className="text-xs text-gray-500">
              {node.employeeCount || 0} موظف
            </p>
          </div>

          {/* أزرار الإجراءات */}
          <div className="flex gap-2">
            <button
              onClick={() => viewUnitEmployees(node)}
              className="p-2 text-purple-600 hover:bg-purple-100 rounded-lg transition-colors"
              title="عرض الموظفين"
            >
              <Users className="w-4 h-4" />
            </button>
            <button
              onClick={() => handleEditDepartment(node)}
              className="p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors"
              title="تعديل"
            >
              <Edit className="w-4 h-4" />
            </button>
            <button
              onClick={() => handleDeleteDepartment(node.id)}
              className="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors"
              title="حذف"
            >
              <Trash2 className="w-4 h-4" />
            </button>
            <button
              onClick={() => {
                setSelectedNode(node);
                setShowAddModal(true);
              }}
              className="p-2 text-green-600 hover:bg-green-100 rounded-lg transition-colors"
              title="إضافة قسم فرعي"
            >
              <Plus className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* الأقسام الفرعية */}
        {hasChildren && isExpanded && (
          <div className="mt-2">
            {node.children.map(child => renderNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  // عرض موظفي الوحدة
  const viewUnitEmployees = async (node) => {
    try {
      const response = await fetch(`/api/organizational-structure/add-employee?unitId=${node.id}`);
      const result = await response.json();

      if (result.success) {
        const employeesList = result.data.map(emp =>
          `• ${emp.EmployeeName} (${emp.EmployeeCode}) - ${emp.Position}${emp.IsDirectManager ? ' (مدير)' : ''}`
        ).join('\n');

        alert(`موظفو ${node.name}:\n\n${employeesList || 'لا يوجد موظفين مسجلين'}`);
      }
    } catch (error) {
      console.error('خطأ في جلب الموظفين:', error);
    }
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto p-6">
        {/* رأس الصفحة */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="text-3xl text-blue-600">🏢</div>
              <div>
                <h1 className="text-3xl font-bold text-gray-800">الهيكل الوظيفي</h1>
                <p className="text-gray-600">إدارة وتنظيم الهيكل الوظيفي للمؤسسة</p>
              </div>
            </div>

            <div className="flex gap-3 flex-wrap">
              <button
                onClick={() => setShowEmployeeModal(true)}
                className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center gap-2 transition-colors"
              >
                <UserPlus className="w-4 h-4" />
                إضافة موظف للهيكل
              </button>
              <button
                onClick={handleAutoSync}
                disabled={autoSyncing}
                className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 flex items-center gap-2 transition-colors disabled:bg-orange-400"
              >
                <Users className="w-4 h-4" />
                {autoSyncing ? 'جاري المزامنة...' : 'مزامنة تلقائية'}
              </button>
              <button
                onClick={handleSyncEmployees}
                disabled={syncing}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2 transition-colors disabled:bg-green-400"
              >
                <Users className="w-4 h-4" />
                {syncing ? 'جاري المزامنة...' : 'مزامنة يدوية'}
              </button>
              <button
                onClick={handleAddDepartment}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2 transition-colors"
              >
                <Plus className="w-4 h-4" />
                إضافة قسم رئيسي
              </button>
            </div>
          </div>
        </div>

        {/* إحصائيات المزامنة */}
        {syncStats && (
          <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
            <h3 className="text-lg font-bold text-gray-800 mb-3">إحصائيات المزامنة</h3>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{syncStats.totalEmployees}</div>
                <div className="text-sm text-gray-600">إجمالي الموظفين</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{syncStats.assignedEmployees}</div>
                <div className="text-sm text-gray-600">مسجلين في الهيكل</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">{syncStats.unassignedEmployees}</div>
                <div className="text-sm text-gray-600">غير مسجلين</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{syncStats.totalUnits}</div>
                <div className="text-sm text-gray-600">إجمالي الوحدات</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-indigo-600">{syncStats.assignmentPercentage}%</div>
                <div className="text-sm text-gray-600">نسبة التسجيل</div>
              </div>
            </div>
          </div>
        )}

        {/* شريط البحث والفلاتر */}
        <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="البحث في الأقسام..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <button
              onClick={() => setExpandedNodes(new Set(structure.map(node => node.id)))}
              className="px-4 py-2 text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
            >
              توسيع الكل
            </button>
            <button
              onClick={() => setExpandedNodes(new Set())}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
            >
              طي الكل
            </button>
          </div>
        </div>

        {/* الهيكل الوظيفي */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-bold text-gray-800 mb-4">شجرة الهيكل الوظيفي</h2>

          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="mr-3 text-gray-600">جاري تحميل الهيكل الوظيفي...</span>
            </div>
          ) : structure.length === 0 ? (
            <div className="text-center py-12">
              <Building className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-600 mb-2">لا يوجد هيكل وظيفي</h3>
              <p className="text-gray-500 mb-4">ابدأ بإضافة الأقسام الرئيسية لبناء الهيكل الوظيفي</p>
              <button
                onClick={handleAddDepartment}
                className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 flex items-center gap-2 mx-auto transition-colors"
              >
                <Plus className="w-5 h-5" />
                إضافة أول قسم
              </button>
            </div>
          ) : (
            <div className="space-y-2">
              {structure.map(node => renderNode(node))}
            </div>
          )}
        </div>

        {/* مودال إضافة قسم */}
        {showAddModal && (
          <AddDepartmentModal
            isOpen={showAddModal}
            onClose={() => setShowAddModal(false)}
            onSave={fetchStructure}
            parentUnit={selectedNode}
            employees={employees}
          />
        )}

        {/* مودال تعديل قسم */}
        {showEditModal && selectedNode && (
          <EditDepartmentModal
            isOpen={showEditModal}
            onClose={() => setShowEditModal(false)}
            onSave={fetchStructure}
            unit={selectedNode}
            employees={employees}
          />
        )}

        {/* مودال إضافة موظف */}
        {showEmployeeModal && (
          <AddEmployeeModal
            isOpen={showEmployeeModal}
            onClose={() => setShowEmployeeModal(false)}
            onSave={fetchStructure}
            structure={structure}
          />
        )}
      </div>
    </MainLayout>
  );
}

// مودال إضافة قسم
function AddDepartmentModal({ isOpen, onClose, onSave, parentUnit, employees }) {
  const [formData, setFormData] = useState({
    unitName: '',
    unitCode: '',
    managerEmployeeCode: '',
    managerName: '',
    unitType: 'قسم',
    description: ''
  });
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch('/api/organizational-structure', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...formData,
          parentUnitId: parentUnit?.id || null
        })
      });

      const result = await response.json();

      if (result.success) {
        onSave();
        onClose();
        alert('تم إضافة القسم بنجاح');
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {
      alert('خطأ في إضافة القسم');
    } finally {
      setLoading(false);
    }
  };

  const handleEmployeeSelect = (employeeCode) => {
    const employee = employees.find(emp => emp.EmployeeCode === employeeCode);
    if (employee) {
      setFormData(prev => ({
        ...prev,
        managerEmployeeCode: employee.EmployeeCode,
        managerName: employee.EmployeeName
      }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h3 className="text-xl font-bold text-gray-800">
            {parentUnit ? `إضافة قسم فرعي تحت: ${parentUnit.name}` : 'إضافة قسم رئيسي'}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                اسم القسم *
              </label>
              <input
                type="text"
                required
                value={formData.unitName}
                onChange={(e) => setFormData(prev => ({ ...prev, unitName: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                placeholder="مثال: المكتب الفني"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                كود القسم
              </label>
              <input
                type="text"
                value={formData.unitCode}
                onChange={(e) => setFormData(prev => ({ ...prev, unitCode: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                placeholder="مثال: TECH-001"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              نوع الوحدة
            </label>
            <select
              value={formData.unitType}
              onChange={(e) => setFormData(prev => ({ ...prev, unitType: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="قسم">قسم</option>
              <option value="إدارة">إدارة</option>
              <option value="وحدة">وحدة</option>
              <option value="فرع">فرع</option>
            </select>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                كود المدير *
              </label>
              <input
                type="text"
                required
                value={formData.managerEmployeeCode}
                onChange={(e) => setFormData(prev => ({ ...prev, managerEmployeeCode: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                placeholder="مثال: 1414"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                اسم المدير *
              </label>
              <input
                type="text"
                required
                value={formData.managerName}
                onChange={(e) => setFormData(prev => ({ ...prev, managerName: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                placeholder="مثال: إبراهيم سيد"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              الوصف
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              placeholder="وصف مختصر للقسم ومهامه..."
            />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              إلغاء
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-blue-400"
            >
              {loading ? 'جاري الحفظ...' : 'حفظ'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// مودال تعديل قسم
function EditDepartmentModal({ isOpen, onClose, onSave, unit, employees }) {
  const [formData, setFormData] = useState({
    unitName: unit?.name || '',
    unitCode: unit?.code || '',
    managerEmployeeCode: unit?.managerCode || '',
    managerName: unit?.managerName || '',
    unitType: unit?.type || 'قسم',
    description: unit?.description || ''
  });
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch('/api/organizational-structure', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          id: unit.id,
          ...formData
        })
      });

      const result = await response.json();

      if (result.success) {
        onSave();
        onClose();
        alert('تم تحديث القسم بنجاح');
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {
      alert('خطأ في تحديث القسم');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h3 className="text-xl font-bold text-gray-800">تعديل القسم: {unit?.name}</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                اسم القسم *
              </label>
              <input
                type="text"
                required
                value={formData.unitName}
                onChange={(e) => setFormData(prev => ({ ...prev, unitName: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                كود القسم
              </label>
              <input
                type="text"
                value={formData.unitCode}
                onChange={(e) => setFormData(prev => ({ ...prev, unitCode: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              نوع الوحدة
            </label>
            <select
              value={formData.unitType}
              onChange={(e) => setFormData(prev => ({ ...prev, unitType: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="قسم">قسم</option>
              <option value="إدارة">إدارة</option>
              <option value="وحدة">وحدة</option>
              <option value="فرع">فرع</option>
            </select>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                كود المدير *
              </label>
              <input
                type="text"
                required
                value={formData.managerEmployeeCode}
                onChange={(e) => setFormData(prev => ({ ...prev, managerEmployeeCode: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                اسم المدير *
              </label>
              <input
                type="text"
                required
                value={formData.managerName}
                onChange={(e) => setFormData(prev => ({ ...prev, managerName: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              الوصف
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              إلغاء
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-blue-400"
            >
              {loading ? 'جاري الحفظ...' : 'حفظ التغييرات'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// مودال إضافة موظف للهيكل
function AddEmployeeModal({ isOpen, onClose, onSave, structure }) {
  const [employeeCode, setEmployeeCode] = useState('');
  const [employeeData, setEmployeeData] = useState(null);
  const [selectedUnit, setSelectedUnit] = useState('');
  const [position, setPosition] = useState('');
  const [loading, setLoading] = useState(false);
  const [searching, setSearching] = useState(false);

  // البحث عن الموظف بالكود
  const searchEmployee = async (code) => {
    if (!code || code.length < 2) {
      setEmployeeData(null);
      return;
    }

    setSearching(true);
    try {
      const response = await fetch(`/api/employees?search=${code}`);
      const result = await response.json();

      if (result.success && result.data.length > 0) {
        const employee = result.data.find(emp => emp.EmployeeCode === code) || result.data[0];
        setEmployeeData(employee);
        setPosition(employee.JobTitle || '');
      } else {
        setEmployeeData(null);
      }
    } catch (error) {
      console.error('خطأ في البحث عن الموظف:', error);
      setEmployeeData(null);
    } finally {
      setSearching(false);
    }
  };

  // تأثير البحث عند تغيير الكود
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchEmployee(employeeCode);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [employeeCode]);

  // إضافة الموظف للوحدة
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!employeeData || !selectedUnit) {
      alert('يرجى اختيار موظف ووحدة تنظيمية');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/organizational-structure/add-employee', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          employeeCode: employeeData.EmployeeCode,
          unitId: selectedUnit,
          position: position
        })
      });

      const result = await response.json();

      if (result.success) {
        onSave();
        onClose();
        alert('تم إضافة الموظف للهيكل الوظيفي بنجاح');
        // إعادة تعيين النموذج
        setEmployeeCode('');
        setEmployeeData(null);
        setSelectedUnit('');
        setPosition('');
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {
      alert('خطأ في إضافة الموظف');
    } finally {
      setLoading(false);
    }
  };

  // تحويل الهيكل إلى قائمة مسطحة للاختيار
  const flattenStructure = (nodes, level = 0) => {
    let result = [];
    nodes.forEach(node => {
      result.push({
        id: node.id,
        name: node.name,
        level: level,
        managerName: node.managerName
      });
      if (node.children && node.children.length > 0) {
        result = result.concat(flattenStructure(node.children, level + 1));
      }
    });
    return result;
  };

  const flatUnits = flattenStructure(structure);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h3 className="text-xl font-bold text-gray-800">إضافة موظف للهيكل الوظيفي</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* البحث عن الموظف */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              كود الموظف *
            </label>
            <div className="relative">
              <input
                type="text"
                required
                value={employeeCode}
                onChange={(e) => setEmployeeCode(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                placeholder="أدخل كود الموظف للبحث..."
              />
              {searching && (
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                </div>
              )}
            </div>
          </div>

          {/* عرض بيانات الموظف */}
          {employeeData && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-800 mb-2">بيانات الموظف:</h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">الاسم:</span>
                  <span className="font-medium mr-2">{employeeData.EmployeeName}</span>
                </div>
                <div>
                  <span className="text-gray-600">الكود:</span>
                  <span className="font-medium mr-2">{employeeData.EmployeeCode}</span>
                </div>
                <div>
                  <span className="text-gray-600">المسمى الوظيفي:</span>
                  <span className="font-medium mr-2">{employeeData.JobTitle || 'غير محدد'}</span>
                </div>
                <div>
                  <span className="text-gray-600">المنطقة:</span>
                  <span className="font-medium mr-2">{employeeData.Area || 'غير محدد'}</span>
                </div>
              </div>
            </div>
          )}

          {/* اختيار الوحدة التنظيمية */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              الوحدة التنظيمية *
            </label>
            <select
              required
              value={selectedUnit}
              onChange={(e) => setSelectedUnit(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">اختر الوحدة التنظيمية...</option>
              {flatUnits.map(unit => (
                <option key={unit.id} value={unit.id}>
                  {'  '.repeat(unit.level)}
                  {unit.name} - مدير: {unit.managerName}
                </option>
              ))}
            </select>
          </div>

          {/* المنصب في الوحدة */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              المنصب في الوحدة
            </label>
            <input
              type="text"
              value={position}
              onChange={(e) => setPosition(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              placeholder="مثال: مهندس، محاسب، سكرتير..."
            />
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              إلغاء
            </button>
            <button
              type="submit"
              disabled={loading || !employeeData || !selectedUnit}
              className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:bg-purple-400"
            >
              {loading ? 'جاري الإضافة...' : 'إضافة للهيكل'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
