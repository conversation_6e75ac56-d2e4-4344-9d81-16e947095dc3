import { getConnection } from '@/lib/db';
import sql from 'mssql';
import { NextResponse } from 'next/server';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');

    const pool = await getConnection();

    // إضافة الأعمدة المطلوبة للموظفين فقط
    await ensureEmployeeColumns(pool);

    if (type === 'departments') {
      try {
        // جلب الأقسام من جدول Department الموجود
        const result = await pool.request().query(`
          SELECT
            ID,
            Department as DepartmentName,
            'DEPT_' + CAST(ID as NVARCHAR) as DepartmentCode,
            '' as ManagerId,
            0 as ParentDepartmentId,
            Department as Description,
            1 as Level,
            GETDATE() as CreatedAt,
            GETDATE() as UpdatedAt
          FROM Department
          ORDER BY ID
        `);

        console.log('تم جلب الأقسام بنجاح:', result.recordset.length);

        return NextResponse.json({
          success: true,
          departments: result.recordset
        });
      } catch (error) {
        console.error('خطأ في جلب الأقسام:', error);
        // إرجاع قائمة فارغة في حالة الخطأ
        return NextResponse.json({
          success: true,
          departments: []
        });
      }
    }

    if (type === 'employees') {
      // جلب الموظفين مع اكتشاف العمود الصحيح
      const { employeeCodeColumn, employeeNameColumn } = await discoverCorrectColumns(pool);

      // التأكد من وجود الأعمدة المطلوبة
      await ensureEmployeeColumns(pool);

      const result = await pool.request().query(`
        SELECT
          ${employeeCodeColumn} as EmployeeCode,
          ${employeeNameColumn} as EmployeeName,
          ISNULL(JobTitle, '') as JobTitle,
          ISNULL(Department, '') as Department,
          ISNULL(DirectManagerId, '') as DirectManagerId,
          ISNULL(DepartmentId, 0) as DepartmentId
        FROM Employees
        ORDER BY ${employeeNameColumn}
      `);

      return NextResponse.json({
        success: true,
        employees: result.recordset
      });
    }

    if (type === 'all-employees') {
      // جلب جميع الموظفين للبحث والاختيار
      const { employeeCodeColumn, employeeNameColumn } = await discoverCorrectColumns(pool);

      // التأكد من وجود الأعمدة المطلوبة
      await ensureEmployeeColumns(pool);

      // بناء الاستعلام بناءً على الأعمدة الموجودة
      const availableColumns = await getAvailableColumns(pool, 'Employees');

      let selectColumns = `
        ${employeeCodeColumn} as EmployeeCode,
        ${employeeNameColumn} as EmployeeName,
        ISNULL(JobTitle, '') as JobTitle,
        ISNULL(Department, '') as Department
      `;

      // إضافة الأعمدة الاختيارية إذا كانت موجودة
      if (availableColumns.includes('Area')) {
        selectColumns += `, ISNULL(Area, '') as Area`;
      } else {
        selectColumns += `, '' as Area`;
      }

      if (availableColumns.includes('TransportMethod')) {
        selectColumns += `, ISNULL(TransportMethod, '') as TransportMethod`;
      } else {
        selectColumns += `, '' as TransportMethod`;
      }

      // إضافة أعمدة المديرين المباشرين
      for (let i = 1; i <= 4; i++) {
        if (availableColumns.includes(`DirectManagerCode${i}`)) {
          selectColumns += `, ISNULL(DirectManagerCode${i}, '') as DirectManagerCode${i}`;
        } else {
          selectColumns += `, '' as DirectManagerCode${i}`;
        }

        if (availableColumns.includes(`DirectManagerName${i}`)) {
          selectColumns += `, ISNULL(DirectManagerName${i}, '') as DirectManagerName${i}`;
        } else {
          selectColumns += `, '' as DirectManagerName${i}`;
        }
      }

      const result = await pool.request().query(`
        SELECT ${selectColumns}
        FROM Employees
        WHERE ${employeeNameColumn} IS NOT NULL AND ${employeeNameColumn} != ''
        ORDER BY ${employeeNameColumn}
      `);

      return NextResponse.json({
        success: true,
        employees: result.recordset
      });
    }

    // جلب الهيكل الكامل
    const { employeeCodeColumn, employeeNameColumn } = await discoverCorrectColumns(pool);

    // جلب الأقسام باستخدام نفس المنطق المحسن
    let departmentsResult = { recordset: [] };
    try {
      // التحقق من الجداول الموجودة
      const tablesResult = await pool.request().query(`
        SELECT TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_TYPE = 'BASE TABLE'
        AND (TABLE_NAME = 'Department' OR TABLE_NAME = 'Departments')
      `);

      // جرب جدول Department أولاً
      if (tablesResult.recordset.some(t => t.TABLE_NAME === 'Department')) {
        try {
          const columnsResult = await pool.request().query(`
            SELECT COLUMN_NAME
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'Department'
          `);

          const columns = columnsResult.recordset.map(r => r.COLUMN_NAME);

          // بناء الاستعلام بناءً على الأعمدة الموجودة
          let selectQuery = 'SELECT ';
          const selectParts = [];

          if (columns.includes('ID')) {
            selectParts.push('ID');
          } else {
            selectParts.push('1 as ID');
          }

          if (columns.includes('DepartmentName')) {
            selectParts.push('DepartmentName');
          } else if (columns.includes('Name')) {
            selectParts.push('Name as DepartmentName');
          } else {
            selectParts.push("'قسم غير محدد' as DepartmentName");
          }

          if (columns.includes('DepartmentCode')) {
            selectParts.push('DepartmentCode');
          } else if (columns.includes('Code')) {
            selectParts.push('Code as DepartmentCode');
          } else {
            selectParts.push("'D' + CAST(ID as NVARCHAR) as DepartmentCode");
          }

          selectParts.push("'' as ManagerId");
          selectParts.push("0 as ParentDepartmentId");
          selectParts.push("'' as Description");
          selectParts.push("1 as Level");
          selectParts.push("GETDATE() as CreatedAt");
          selectParts.push("GETDATE() as UpdatedAt");

          selectQuery += selectParts.join(', ') + ' FROM Department';

          departmentsResult = await pool.request().query(selectQuery);

        } catch (error) {
          console.log('خطأ في جدول Department:', error.message);
        }
      }

      // إذا فشل، جرب جدول Departments
      if (departmentsResult.recordset.length === 0 && tablesResult.recordset.some(t => t.TABLE_NAME === 'Departments')) {
        try {
          departmentsResult = await pool.request().query(`
            SELECT
              ID,
              DepartmentName,
              DepartmentCode,
              ISNULL(ManagerCode, '') as ManagerId,
              ISNULL(ParentDepartmentID, 0) as ParentDepartmentId,
              ISNULL(Description, '') as Description,
              ISNULL(Level, 1) as Level,
              CreatedAt,
              ISNULL(UpdatedAt, CreatedAt) as UpdatedAt
            FROM Departments
            WHERE IsActive = 1
            ORDER BY Level, DepartmentName
          `);
        } catch (error2) {
          console.log('خطأ في جدول Departments:', error2.message);
        }
      }

      // إذا لم نجد أي أقسام، أنشئ قائمة افتراضية
      if (departmentsResult.recordset.length === 0) {
        departmentsResult = {
          recordset: [
            {
              ID: 1,
              DepartmentName: 'إدارة التنفيذ',
              DepartmentCode: 'EXEC',
              ManagerId: '1428',
              ParentDepartmentId: 0,
              Description: 'إدارة تنفيذ المشاريع',
              Level: 1,
              CreatedAt: new Date(),
              UpdatedAt: new Date()
            },
            {
              ID: 2,
              DepartmentName: 'إدارة المكتب الفني',
              DepartmentCode: 'TECH',
              ManagerId: '1414',
              ParentDepartmentId: 0,
              Description: 'المكتب الفني والتصميم',
              Level: 1,
              CreatedAt: new Date(),
              UpdatedAt: new Date()
            },
            {
              ID: 3,
              DepartmentName: 'إدارة السلامة والصحة المهنية',
              DepartmentCode: 'SAFETY',
              ManagerId: '5632',
              ParentDepartmentId: 0,
              Description: 'السلامة والصحة المهنية',
              Level: 1,
              CreatedAt: new Date(),
              UpdatedAt: new Date()
            }
          ]
        };
      }
    } catch (error) {
      console.error('خطأ في جلب الأقسام:', error);
      departmentsResult = { recordset: [] };
    }

    const employeesResult = await pool.request().query(`
      SELECT
        ${employeeCodeColumn} as EmployeeCode,
        ${employeeNameColumn} as EmployeeName,
        ISNULL(JobTitle, '') as JobTitle,
        ISNULL(Department, '') as Department,
        ISNULL(DirectManagerId, '') as DirectManagerId,
        ISNULL(DepartmentId, 0) as DepartmentId
      FROM Employees
      ORDER BY ${employeeNameColumn}
    `);

    return NextResponse.json({
      success: true,
      departments: departmentsResult.recordset,
      employees: employeesResult.recordset
    });

  } catch (error) {
    console.error('خطأ في جلب الهيكل التنظيمي:', error);
    return NextResponse.json({
      success: false,
      error: 'فشل في جلب البيانات: ' + error.message
    }, { status: 500 });
  }
}

// دالة للحصول على الأعمدة المتاحة في جدول
async function getAvailableColumns(pool, tableName) {
  try {
    const result = await pool.request()
      .input('tableName', sql.NVarChar, tableName)
      .query(`
        SELECT COLUMN_NAME
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = @tableName
      `);

    return result.recordset.map(row => row.COLUMN_NAME);
  } catch (error) {
    console.error('خطأ في جلب الأعمدة:', error);
    return [];
  }
}

// دالة اكتشاف الأعمدة الصحيحة
async function discoverCorrectColumns(pool) {
  try {
    const columnsResult = await pool.request().query(`
      SELECT COLUMN_NAME
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'Employees'
      AND COLUMN_NAME IN ('EmployeeCode', 'EmpCode', 'Code', 'EmployeeName', 'EmpName', 'Name', 'FullName')
    `);

    const columns = columnsResult.recordset.map(row => row.COLUMN_NAME);

    // اكتشاف عمود كود الموظف
    let employeeCodeColumn = 'EmployeeCode';
    if (columns.includes('EmpCode')) {
      employeeCodeColumn = 'EmpCode';
    } else if (columns.includes('Code')) {
      employeeCodeColumn = 'Code';
    }

    // اكتشاف عمود اسم الموظف
    let employeeNameColumn = 'EmployeeName';
    if (columns.includes('EmpName')) {
      employeeNameColumn = 'EmpName';
    } else if (columns.includes('Name')) {
      employeeNameColumn = 'Name';
    } else if (columns.includes('FullName')) {
      employeeNameColumn = 'FullName';
    }

    return { employeeCodeColumn, employeeNameColumn };
  } catch (error) {
    // في حالة الخطأ، استخدم الأعمدة الافتراضية
    return { employeeCodeColumn: 'EmployeeCode', employeeNameColumn: 'EmployeeName' };
  }
}

export async function POST(request) {
  try {
    const body = await request.json();
    const { type, ...data } = body;

    const pool = await getConnection();

    if (type === 'department') {
      // إضافة قسم جديد
      const { departmentName, departmentCode, managerId, parentDepartmentId, description } = data;

      // التحقق من وجود جدول الأقسام
      const tableExists = await checkTableExists(pool, 'Departments');
      if (!tableExists) {
        await createDepartmentsTable(pool);
      }

      // حساب المستوى
      let level = 1;
      if (parentDepartmentId) {
        const parentResult = await pool.request()
          .input('parentId', sql.Int, parentDepartmentId)
          .query('SELECT Level FROM Departments WHERE ID = @parentId');

        if (parentResult.recordset.length > 0) {
          level = parentResult.recordset[0].Level + 1;
        }
      }

      const result = await pool.request()
        .input('departmentName', sql.NVarChar, departmentName)
        .input('departmentCode', sql.NVarChar, departmentCode)
        .input('managerId', sql.Int, managerId || null)
        .input('parentDepartmentId', sql.Int, parentDepartmentId || null)
        .input('description', sql.NVarChar, description || null)
        .input('level', sql.Int, level)
        .query(`
          INSERT INTO Departments (
            DepartmentName, DepartmentCode, ManagerId, ParentDepartmentId,
            Description, Level, CreatedAt, UpdatedAt
          )
          OUTPUT INSERTED.ID
          VALUES (
            @departmentName, @departmentCode, @managerId, @parentDepartmentId,
            @description, @level, GETDATE(), GETDATE()
          )
        `);

      return NextResponse.json({
        success: true,
        message: 'تم إضافة القسم بنجاح',
        id: result.recordset[0].ID
      });
    }

    if (type === 'employee') {
      // إضافة موظف جديد إلى جدول الموظفين الموجود
      const { employeeCode, employeeName, jobTitle, directManagerId, departmentId } = data;
      const { employeeCodeColumn, employeeNameColumn } = await discoverCorrectColumns(pool);

      // التحقق من وجود الأعمدة المطلوبة
      await ensureEmployeeColumns(pool);

      const result = await pool.request()
        .input('employeeCode', sql.NVarChar, employeeCode)
        .input('employeeName', sql.NVarChar, employeeName)
        .input('jobTitle', sql.NVarChar, jobTitle)
        .input('directManagerId', sql.Int, directManagerId || null)
        .input('departmentId', sql.Int, departmentId || null)
        .query(`
          INSERT INTO Employees (
            ${employeeCodeColumn}, ${employeeNameColumn}, JobTitle, DirectManagerId, DepartmentId
          )
          VALUES (
            @employeeCode, @employeeName, @jobTitle, @directManagerId, @departmentId
          )
        `);

      return NextResponse.json({
        success: true,
        message: 'تم إضافة الموظف بنجاح'
      });
    }

    if (type === 'assignment') {
      // ربط مسؤولية قسم
      const { departmentId, managerId } = data;

      await pool.request()
        .input('departmentId', sql.Int, departmentId)
        .input('managerId', sql.Int, managerId)
        .query(`
          UPDATE Departments
          SET ManagerId = @managerId, UpdatedAt = GETDATE()
          WHERE ID = @departmentId
        `);

      return NextResponse.json({
        success: true,
        message: 'تم ربط المسؤولية بنجاح'
      });
    }

    return NextResponse.json({
      success: false,
      error: 'نوع العملية غير مدعوم'
    }, { status: 400 });

  } catch (error) {
    console.error('خطأ في إضافة البيانات:', error);
    return NextResponse.json({
      success: false,
      error: 'فشل في إضافة البيانات: ' + error.message
    }, { status: 500 });
  }
}

// إنشاء جدول الأقسام إذا لم يكن موجوداً
async function createDepartmentsTableIfNotExists(pool) {
  try {
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Departments' AND xtype='U')
      BEGIN
        CREATE TABLE Departments (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          DepartmentName NVARCHAR(255) NOT NULL,
          DepartmentCode NVARCHAR(50) UNIQUE,
          ManagerCode NVARCHAR(50),
          ParentDepartmentID INT,
          Description NVARCHAR(500),
          Level INT DEFAULT 1,
          IsActive BIT DEFAULT 1,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE()
        )

        -- إدراج بيانات افتراضية
        INSERT INTO Departments (DepartmentName, DepartmentCode, ManagerCode, Description, Level) VALUES
        (N'إدارة التنفيذ', 'EXEC', '1428', N'إدارة تنفيذ المشاريع', 1),
        (N'إدارة المكتب الفني', 'TECH', '1414', N'المكتب الفني والتصميم', 1),
        (N'إدارة السلامة والصحة المهنية', 'SAFETY', '5632', N'السلامة والصحة المهنية', 1),
        (N'إدارة المساحة', 'SURVEY', '', N'أعمال المساحة والقياس', 1),
        (N'إدارة الأمن', 'SECURITY', '', N'الأمن والحراسة', 1),
        (N'إدارة تكنولوجيا المعلومات', 'IT', '', N'تكنولوجيا المعلومات والحاسوب', 1)
      END
    `);
    console.log('تم التحقق من جدول الأقسام وإنشاؤه إذا لزم الأمر');
  } catch (error) {
    console.error('خطأ في إنشاء جدول الأقسام:', error);
  }
}

// إنشاء جدول الأقسام
async function createDepartmentsTable(pool) {
  try {
    await pool.request().query(`
      CREATE TABLE Departments (
        ID INT IDENTITY(1,1) PRIMARY KEY,
        DepartmentName NVARCHAR(255) NOT NULL,
        DepartmentCode NVARCHAR(50) UNIQUE,
        ManagerId NVARCHAR(50),
        ParentDepartmentId INT,
        Description NVARCHAR(500),
        Level INT DEFAULT 1,
        CreatedAt DATETIME DEFAULT GETDATE(),
        UpdatedAt DATETIME DEFAULT GETDATE()
      )
    `);
    console.log('تم إنشاء جدول الأقسام بنجاح');
  } catch (error) {
    console.error('خطأ في إنشاء جدول الأقسام:', error);
  }
}

// التحقق من وجود جدول
async function checkTableExists(pool, tableName) {
  try {
    const result = await pool.request()
      .input('tableName', sql.NVarChar, tableName)
      .query(`
        SELECT COUNT(*) as count
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_NAME = @tableName
      `);

    return result.recordset[0].count > 0;
  } catch (error) {
    return false;
  }
}



// التأكد من وجود الأعمدة المطلوبة في جدول الموظفين
async function ensureEmployeeColumns(pool) {
  try {
    // قائمة الأعمدة المطلوبة للمديرين المباشرين (4 مستويات)
    const requiredColumns = [
      { name: 'DirectManagerId', type: 'NVARCHAR(50)' },
      { name: 'DirectManagerCode1', type: 'NVARCHAR(50)' },
      { name: 'DirectManagerName1', type: 'NVARCHAR(255)' },
      { name: 'DirectManagerCode2', type: 'NVARCHAR(50)' },
      { name: 'DirectManagerName2', type: 'NVARCHAR(255)' },
      { name: 'DirectManagerCode3', type: 'NVARCHAR(50)' },
      { name: 'DirectManagerName3', type: 'NVARCHAR(255)' },
      { name: 'DirectManagerCode4', type: 'NVARCHAR(50)' },
      { name: 'DirectManagerName4', type: 'NVARCHAR(255)' },
      { name: 'DepartmentId', type: 'INT' }
    ];

    for (const column of requiredColumns) {
      const columnExists = await pool.request()
        .input('columnName', sql.NVarChar, column.name)
        .query(`
          SELECT COUNT(*) as count
          FROM INFORMATION_SCHEMA.COLUMNS
          WHERE TABLE_NAME = 'Employees' AND COLUMN_NAME = @columnName
        `);

      if (columnExists.recordset[0].count === 0) {
        await pool.request().query(`
          ALTER TABLE Employees ADD ${column.name} ${column.type} NULL
        `);
        console.log(`تم إضافة العمود: ${column.name}`);
      }
    }
  } catch (error) {
    console.error('خطأ في إضافة الأعمدة:', error);
  }
}

// دالة لاستيراد بيانات المديرين المباشرين من Excel
export async function importDirectManagersData(pool, data) {
  try {
    const { employeeCodeColumn, employeeNameColumn } = await discoverCorrectColumns(pool);

    for (const row of data) {
      const updateQuery = `
        UPDATE Employees SET
          DirectManagerCode1 = @managerCode1,
          DirectManagerName1 = @managerName1,
          DirectManagerCode2 = @managerCode2,
          DirectManagerName2 = @managerName2,
          DirectManagerCode3 = @managerCode3,
          DirectManagerName3 = @managerName3,
          DirectManagerCode4 = @managerCode4,
          DirectManagerName4 = @managerName4
        WHERE ${employeeCodeColumn} = @employeeCode
      `;

      await pool.request()
        .input('employeeCode', sql.NVarChar, row.employeeCode)
        .input('managerCode1', sql.NVarChar, row.managerCode1 || null)
        .input('managerName1', sql.NVarChar, row.managerName1 || null)
        .input('managerCode2', sql.NVarChar, row.managerCode2 || null)
        .input('managerName2', sql.NVarChar, row.managerName2 || null)
        .input('managerCode3', sql.NVarChar, row.managerCode3 || null)
        .input('managerName3', sql.NVarChar, row.managerName3 || null)
        .input('managerCode4', sql.NVarChar, row.managerCode4 || null)
        .input('managerName4', sql.NVarChar, row.managerName4 || null)
        .query(updateQuery);
    }

    return { success: true, message: `تم تحديث ${data.length} موظف بنجاح` };
  } catch (error) {
    console.error('خطأ في استيراد بيانات المديرين:', error);
    throw error;
  }
}

export async function PUT(request) {
  try {
    const body = await request.json();
    const { type, id, ...data } = body;

    const pool = await getConnection();

    if (type === 'department') {
      // تحديث قسم
      const { departmentName, departmentCode, managerId, parentDepartmentId, description } = data;

      // حساب المستوى الجديد
      let level = 1;
      if (parentDepartmentId) {
        const parentResult = await pool.request()
          .input('parentId', sql.Int, parentDepartmentId)
          .query('SELECT Level FROM Departments WHERE ID = @parentId');

        if (parentResult.recordset.length > 0) {
          level = parentResult.recordset[0].Level + 1;
        }
      }

      await pool.request()
        .input('id', sql.Int, id)
        .input('departmentName', sql.NVarChar, departmentName)
        .input('departmentCode', sql.NVarChar, departmentCode)
        .input('managerId', sql.Int, managerId || null)
        .input('parentDepartmentId', sql.Int, parentDepartmentId || null)
        .input('description', sql.NVarChar, description || null)
        .input('level', sql.Int, level)
        .query(`
          UPDATE Departments SET
            DepartmentName = @departmentName,
            DepartmentCode = @departmentCode,
            ManagerId = @managerId,
            ParentDepartmentId = @parentDepartmentId,
            Description = @description,
            Level = @level,
            UpdatedAt = GETDATE()
          WHERE ID = @id
        `);

      return NextResponse.json({
        success: true,
        message: 'تم تحديث القسم بنجاح'
      });
    }

    if (type === 'employee') {
      // تحديث موظف
      const { employeeCode, employeeName, jobTitle, directManagerId, departmentId } = data;
      const { employeeCodeColumn, employeeNameColumn } = await discoverCorrectColumns(pool);

      await pool.request()
        .input('employeeCode', sql.NVarChar, employeeCode)
        .input('employeeName', sql.NVarChar, employeeName)
        .input('jobTitle', sql.NVarChar, jobTitle)
        .input('directManagerId', sql.Int, directManagerId || null)
        .input('departmentId', sql.Int, departmentId || null)
        .query(`
          UPDATE Employees SET
            ${employeeCodeColumn} = @employeeCode,
            ${employeeNameColumn} = @employeeName,
            JobTitle = @jobTitle,
            DirectManagerId = @directManagerId,
            DepartmentId = @departmentId
          WHERE ${employeeCodeColumn} = @employeeCode
        `);

      return NextResponse.json({
        success: true,
        message: 'تم تحديث الموظف بنجاح'
      });
    }

    return NextResponse.json({
      success: false,
      error: 'نوع العملية غير مدعوم'
    }, { status: 400 });

  } catch (error) {
    console.error('خطأ في تحديث البيانات:', error);
    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث البيانات: ' + error.message
    }, { status: 500 });
  }
}

export async function DELETE(request) {
  try {
    const body = await request.json();
    const { type, id } = body;

    const pool = await getConnection();

    if (type === 'department') {
      // التحقق من وجود أقسام فرعية
      const childrenResult = await pool.request()
        .input('id', sql.Int, id)
        .query('SELECT COUNT(*) as count FROM Departments WHERE ParentDepartmentId = @id');

      if (childrenResult.recordset[0].count > 0) {
        return NextResponse.json({
          success: false,
          error: 'لا يمكن حذف القسم لوجود أقسام فرعية تابعة له'
        }, { status: 400 });
      }

      // حذف القسم
      await pool.request()
        .input('id', sql.Int, id)
        .query('DELETE FROM Departments WHERE ID = @id');

      return NextResponse.json({
        success: true,
        message: 'تم حذف القسم بنجاح'
      });
    }

    if (type === 'employee') {
      // حذف الموظف (تحديث بدلاً من حذف فعلي)
      const { employeeCodeColumn } = await discoverCorrectColumns(pool);

      await pool.request()
        .input('id', sql.NVarChar, id)
        .query(`
          UPDATE Employees
          SET DirectManagerId = NULL, DepartmentId = NULL
          WHERE ${employeeCodeColumn} = @id
        `);

      return NextResponse.json({
        success: true,
        message: 'تم إزالة الموظف من الهيكل التنظيمي بنجاح'
      });
    }

    return NextResponse.json({
      success: false,
      error: 'نوع العملية غير مدعوم'
    }, { status: 400 });

  } catch (error) {
    console.error('خطأ في حذف البيانات:', error);
    return NextResponse.json({
      success: false,
      error: 'فشل في حذف البيانات: ' + error.message
    }, { status: 500 });
  }
}
