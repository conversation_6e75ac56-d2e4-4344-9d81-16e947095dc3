import React from 'react';

function StoryComponent() {
  const sampleFields = [
    {
      name: 'name',
      type: 'text',
      label: 'Name',
    },
    {
      name: 'email',
      type: 'email',
      label: 'Email',
    },
  ];

  return (
    <form>
      {sampleFields.map((field) => (
        <div key={field.name}>
          <label>{field.label}</label>
          <input type={field.type} name={field.name} />
        </div>
      ))}
    </form>
  );
}

export default StoryComponent;