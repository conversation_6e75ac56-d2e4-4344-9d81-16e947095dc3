'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import ActiveCarSelector from '@/components/ActiveCarSelector';

export default function CarCostsPage() {
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [selectedCarCode, setSelectedCarCode] = useState('');
  const [showAddCostModal, setShowAddCostModal] = useState(false);
  const [costs, setCosts] = useState([]);
  const [loading, setLoading] = useState(false);

  // رفع ملف طلب الإصدار
  const uploadVersionDocument = async (file) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('costType', 'carscost');
      formData.append('month', selectedMonth);
      formData.append('year', selectedYear);

      const response = await fetch('/api/upload-version', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();
      if (result.success) {
        alert(`تم رفع ملف ${selectedMonth}-${selectedYear}.pdf بنجاح`);
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {
      alert('خطأ في رفع الملف: ' + error.message);
    }
  };

  // فتح نافذة اختيار الملف
  const openFileUpload = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.pdf';
    input.onchange = (e) => {
      const file = e.target.files[0];
      if (file) {
        if (file.type !== 'application/pdf') {
          alert('يجب اختيار ملف PDF فقط');
          return;
        }
        uploadVersionDocument(file);
      }
    };
    input.click();
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto">
        {/* رأس الصفحة */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="text-3xl text-orange-600">🚗</div>
              <div>
                <h1 className="text-3xl font-bold text-gray-800">تكاليف السيارات</h1>
                <p className="text-gray-600">إدارة ومتابعة تكاليف السيارات الشهرية</p>
              </div>
            </div>
            <div className="flex gap-3">
              <select
                value={selectedMonth}
                onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-orange-500 focus:border-orange-500"
              >
                {[
                  { value: 1, label: 'يناير' },
                  { value: 2, label: 'فبراير' },
                  { value: 3, label: 'مارس' },
                  { value: 4, label: 'أبريل' },
                  { value: 5, label: 'مايو' },
                  { value: 6, label: 'يونيو' },
                  { value: 7, label: 'يوليو' },
                  { value: 8, label: 'أغسطس' },
                  { value: 9, label: 'سبتمبر' },
                  { value: 10, label: 'أكتوبر' },
                  { value: 11, label: 'نوفمبر' },
                  { value: 12, label: 'ديسمبر' }
                ].map(month => (
                  <option key={month.value} value={month.value}>
                    {month.label}
                  </option>
                ))}
              </select>
              <select
                value={selectedYear}
                onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-orange-500 focus:border-orange-500"
              >
                {[2023, 2024, 2025, 2026].map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
              <button
                onClick={openFileUpload}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
              >
                📎 رفع طلب إصدار
              </button>
              <a
                href="/costs/version-requests?type=cars"
                className="bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700 flex items-center gap-2"
              >
                📋 عرض طلبات الإصدار
              </a>
              <button className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                تصدير البيانات
              </button>
              <button
                onClick={() => setShowAddCostModal(true)}
                className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700"
              >
                إضافة تكلفة جديدة
              </button>
            </div>
          </div>
        </div>

        {/* الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-orange-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي التكاليف</p>
                <p className="text-3xl font-bold text-orange-600">4,500 ج.م</p>
              </div>
              <div className="text-2xl text-orange-600">💰</div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-blue-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">تكاليف الصيانة</p>
                <p className="text-2xl font-bold text-blue-600">1,500 ج.م</p>
              </div>
              <div className="text-2xl text-blue-600">🔧</div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-green-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">تكاليف الوقود</p>
                <p className="text-2xl font-bold text-green-600">800 ج.م</p>
              </div>
              <div className="text-2xl text-green-600">⛽</div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-red-500">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">تكاليف الإصلاحات</p>
                <p className="text-2xl font-bold text-red-600">2,200 ج.م</p>
              </div>
              <div className="text-2xl text-red-600">🛠️</div>
            </div>
          </div>
        </div>

        {/* جدول التكاليف */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-800">تكاليف السيارات - يناير 2024</h3>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    كود السيارة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    رقم السيارة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الوصف
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    نوع التكلفة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    المبلغ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    التاريخ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                <tr className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    CAR-001
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    أ ب ج 123
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    صيانة دورية
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                      صيانة
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    1,500 ج.م
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    15/01/2024
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex gap-2">
                      <button className="text-blue-600 hover:text-blue-900" title="تعديل">
                        ✏️
                      </button>
                      <button className="text-red-600 hover:text-red-900" title="حذف">
                        🗑️
                      </button>
                    </div>
                  </td>
                </tr>
                <tr className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    CAR-001
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    أ ب ج 123
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    تعبئة وقود
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                      وقود
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    800 ج.م
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    20/01/2024
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex gap-2">
                      <button className="text-blue-600 hover:text-blue-900" title="تعديل">
                        ✏️
                      </button>
                      <button className="text-red-600 hover:text-red-900" title="حذف">
                        🗑️
                      </button>
                    </div>
                  </td>
                </tr>
                <tr className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    CAR-002
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    د هـ و 456
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    إصلاح فرامل
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm">
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                      إصلاحات
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    2,200 ج.م
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    25/01/2024
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex gap-2">
                      <button className="text-blue-600 hover:text-blue-900" title="تعديل">
                        ✏️
                      </button>
                      <button className="text-red-600 hover:text-red-900" title="حذف">
                        🗑️
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* مودال إضافة تكلفة جديدة */}
        {showAddCostModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between p-6 border-b">
                <h3 className="text-xl font-bold text-gray-800">إضافة تكلفة سيارة جديدة</h3>
                <button
                  onClick={() => setShowAddCostModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <div className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* اختيار السيارة */}
                  <div className="lg:col-span-2">
                    <ActiveCarSelector
                      selectedCarCode={selectedCarCode}
                      onCarSelect={setSelectedCarCode}
                      showDetails={true}
                    />
                  </div>

                  {/* نموذج إضافة التكلفة */}
                  {selectedCarCode && (
                    <>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          نوع التكلفة *
                        </label>
                        <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500">
                          <option value="">-- اختر نوع التكلفة --</option>
                          <option value="maintenance">صيانة</option>
                          <option value="fuel">وقود</option>
                          <option value="insurance">تأمين</option>
                          <option value="repairs">إصلاحات</option>
                          <option value="rental">إيجار شهري</option>
                          <option value="other">أخرى</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          المبلغ (ج.م) *
                        </label>
                        <input
                          type="number"
                          step="0.01"
                          min="0"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                          placeholder="0.00"
                        />
                      </div>

                      <div className="lg:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          وصف التكلفة *
                        </label>
                        <input
                          type="text"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                          placeholder="مثال: صيانة دورية، تعبئة وقود، إصلاح فرامل"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          التاريخ *
                        </label>
                        <input
                          type="date"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                          defaultValue={new Date().toISOString().split('T')[0]}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          الشهر والسنة
                        </label>
                        <div className="flex gap-2">
                          <select
                            value={selectedMonth}
                            onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                          >
                            {[
                              { value: 1, label: 'يناير' },
                              { value: 2, label: 'فبراير' },
                              { value: 3, label: 'مارس' },
                              { value: 4, label: 'أبريل' },
                              { value: 5, label: 'مايو' },
                              { value: 6, label: 'يونيو' },
                              { value: 7, label: 'يوليو' },
                              { value: 8, label: 'أغسطس' },
                              { value: 9, label: 'سبتمبر' },
                              { value: 10, label: 'أكتوبر' },
                              { value: 11, label: 'نوفمبر' },
                              { value: 12, label: 'ديسمبر' }
                            ].map(month => (
                              <option key={month.value} value={month.value}>
                                {month.label}
                              </option>
                            ))}
                          </select>
                          <select
                            value={selectedYear}
                            onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                          >
                            {[2023, 2024, 2025, 2026].map(year => (
                              <option key={year} value={year}>{year}</option>
                            ))}
                          </select>
                        </div>
                      </div>

                      <div className="lg:col-span-2">
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          ملاحظات
                        </label>
                        <textarea
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                          rows="3"
                          placeholder="ملاحظات إضافية..."
                        />
                      </div>
                    </>
                  )}
                </div>

                {/* أزرار الإجراءات */}
                <div className="flex gap-3 pt-6 border-t mt-6">
                  {selectedCarCode ? (
                    <button className="bg-orange-600 text-white py-2 px-6 rounded-md hover:bg-orange-700 flex items-center gap-2">
                      💰 حفظ التكلفة
                    </button>
                  ) : (
                    <div className="bg-gray-100 text-gray-500 py-2 px-6 rounded-md flex items-center gap-2">
                      ⚠️ يجب اختيار السيارة أولاً
                    </div>
                  )}
                  <button
                    onClick={() => {
                      setShowAddCostModal(false);
                      setSelectedCarCode('');
                    }}
                    className="bg-gray-300 text-gray-700 py-2 px-6 rounded-md hover:bg-gray-400"
                  >
                    إلغاء
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
