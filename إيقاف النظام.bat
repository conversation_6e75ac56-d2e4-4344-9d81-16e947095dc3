@echo off
chcp 65001 >nul
title إيقاف نظام إدارة التكاليف
color 0C

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    إيقاف نظام إدارة التكاليف                  ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 البحث عن عمليات Node.js النشطة...

:: البحث عن عمليات Node.js
tasklist /fi "imagename eq node.exe" 2>nul | find /i "node.exe" >nul
if %errorlevel% equ 0 (
    echo ✅ تم العثور على عمليات Node.js نشطة
    echo.
    echo 🛑 جاري إيقاف جميع عمليات Node.js...
    taskkill /f /im node.exe >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ تم إيقاف جميع عمليات Node.js بنجاح
    ) else (
        echo ❌ فشل في إيقاف بعض العمليات
    )
) else (
    echo ℹ️  لا توجد عمليات Node.js نشطة
)

echo.

:: البحث عن عمليات npm
tasklist /fi "imagename eq npm.cmd" 2>nul | find /i "npm" >nul
if %errorlevel% equ 0 (
    echo 🛑 جاري إيقاف عمليات npm...
    taskkill /f /im npm.cmd >nul 2>&1
    taskkill /f /im npm >nul 2>&1
    echo ✅ تم إيقاف عمليات npm
) else (
    echo ℹ️  لا توجد عمليات npm نشطة
)

echo.

:: التحقق من المنفذ 3001
netstat -ano | findstr :3001 >nul
if %errorlevel% equ 0 (
    echo ⚠️  المنفذ 3001 لا يزال مستخدماً
    echo 🔄 محاولة تحرير المنفذ...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3001') do (
        taskkill /f /pid %%a >nul 2>&1
    )
    echo ✅ تم تحرير المنفذ 3001
) else (
    echo ✅ المنفذ 3001 متاح
)

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    تم إيقاف النظام بنجاح                     ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 💡 يمكنك الآن تشغيل النظام مرة أخرى باستخدام:
echo    - تشغيل النظام.cmd
echo    - start-system.bat
echo.

pause
