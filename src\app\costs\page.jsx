'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import MainLayout from '@/components/MainLayout';
import {
  FiHome,
  FiTruck,
  FiUsers,
  FiPlus,
  FiEdit,
  FiTrash2,
  FiDollarSign,
  FiCalendar,
  FiFilter,
  FiDownload,
  FiRefreshCw,
  FiBarChart
} from 'react-icons/fi';

export default function CostsPage() {
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState(searchParams.get('tab') || 'apartments');
  const [loading, setLoading] = useState(false);
  const [costs, setCosts] = useState({
    apartments: [],
    cars: [],
    tempWorkers: []
  });
  const [filters, setFilters] = useState({
    month: new Date().getMonth() + 1,
    year: new Date().getFullYear(),
    status: 'all'
  });
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [summary, setSummary] = useState({
    totalAmount: 0,
    totalRecords: 0,
    totalCars: 0,
    avgAmount: 0,
    reports: [],
    monthlyData: []
  });
  const [showSummary, setShowSummary] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(false);

  // جلب البيانات
  useEffect(() => {
    fetchCosts();
    fetchSummary();
  }, [filters, activeTab]);

  // جلب ملخص التكاليف
  const fetchSummary = async () => {
    try {
      const response = await fetch(`/api/costs-summary?type=${activeTab}&year=${filters.year}`);
      const data = await response.json();
      
      if (data.success) {
        setSummary(data.summary);
      }
    } catch (error) {

    }
  };

  const fetchCosts = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/costs?tab=${activeTab}&month=${filters.month}&year=${filters.year}`);
      const data = await response.json();
      
      if (data.success) {
        setCosts(prev => ({
          ...prev,
          [activeTab]: data.costs
        }));
      }
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  // تبويبات التكاليف
  const tabs = [
    {
      id: 'apartments',
      label: 'تكاليف الشقق',
      icon: FiHome,
      color: 'blue'
    },
    {
      id: 'cars',
      label: 'تكاليف السيارات',
      icon: FiTruck,
      color: 'green'
    },
    {
      id: 'tempWorkers',
      label: 'تكاليف العمالة المؤقتة',
      icon: FiUsers,
      color: 'purple'
    }
  ];

  // حساب إجمالي التكاليف
  const getTotalCosts = (type) => {
    return costs[type]?.reduce((sum, item) => sum + (item.amount || 0), 0) || 0;
  };

  // تنسيق الأرقام بطريقة آمنة للـ hydration
  const formatNumber = (number) => {
    if (typeof window === 'undefined') {
      // على الخادم، استخدم تنسيق بسيط
      return number?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') || '0';
    }
    // على العميل، استخدم toLocaleString
    return number?.toLocaleString() || '0';
  };

  // تصدير البيانات
  const exportData = () => {
    const data = costs[activeTab];
    const csvContent = "data:text/csv;charset=utf-8," 
      + "النوع,الوصف,المبلغ,التاريخ,الحالة\n"
      + data.map(item => 
          `${item.type},${item.description},${item.amount},${item.date},${item.status}`
        ).join("\n");
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `costs_${activeTab}_${filters.month}_${filters.year}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // فتح طلب الإصدار
  const openVersionRequest = () => {
    const monthNumber = filters.month;
    const year = filters.year;
    const versionCode = `${monthNumber}-${year}`;
    
    // البحث عن طلب الإصدار المطابق
    const matchingReport = summary.reports?.find(report => 
      report.Path.includes(versionCode)
    );
    
    if (matchingReport) {
      // فتح الملف
      const fileUrl = `/api/files/${matchingReport.Path}`;
      window.open(fileUrl, '_blank');
    } else {
      alert(`لا يوجد طلب إصدار للشهر ${monthNumber}/${year}`);
    }
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto">
        {/* رأس الصفحة */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-800 mb-2">إدارة التكاليف</h1>
              <p className="text-gray-600">متابعة وإدارة تكاليف الشقق والسيارات والعمالة المؤقتة</p>
            </div>
            
            <div className="flex gap-3">
              <button
                onClick={() => setShowAddModal(true)}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
              >
                <FiPlus />
                إضافة تكلفة
              </button>

              <button
                onClick={() => setShowSummary(true)}
                className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 flex items-center gap-2"
              >
                <FiDollarSign />
                لوحة التكاليف
              </button>

              <button
                onClick={() => window.open('/costs/charts', '_blank')}
                className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 flex items-center gap-2"
              >
                <FiBarChart />
                الرسوم البيانية
              </button>
              
              {activeTab === 'cars' && (
                <>
                  <button
                    onClick={openVersionRequest}
                    className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 flex items-center gap-2"
                  >
                    <FiCalendar />
                    طلب الإصدار
                  </button>

                  <button
                    onClick={() => setShowUploadModal(true)}
                    className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 flex items-center gap-2"
                  >
                    <FiDownload className="rotate-180" />
                    رفع طلب إصدار
                  </button>
                </>
              )}
              
              <button
                onClick={exportData}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center gap-2"
              >
                <FiDownload />
                تصدير
              </button>
              
              <button
                onClick={fetchCosts}
                className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center gap-2"
              >
                <FiRefreshCw />
                تحديث
              </button>
            </div>
          </div>
        </div>

        {/* الفلاتر */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <FiCalendar className="text-gray-500" />
              <select
                value={filters.month}
                onChange={(e) => setFilters(prev => ({ ...prev, month: parseInt(e.target.value) }))}
                className="border border-gray-300 rounded-lg px-3 py-2"
              >
                {Array.from({ length: 12 }, (_, i) => (
                  <option key={i + 1} value={i + 1}>
                    {new Date(2024, i).toLocaleDateString('ar-EG', { month: 'long' })}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <select
                value={filters.year}
                onChange={(e) => setFilters(prev => ({ ...prev, year: parseInt(e.target.value) }))}
                className="border border-gray-300 rounded-lg px-3 py-2"
              >
                {Array.from({ length: 5 }, (_, i) => (
                  <option key={2025 + i} value={2025 + i}>
                    {2025 + i}
                  </option>
                ))}
              </select>
            </div>
            
            <div className="flex items-center gap-2">
              <FiFilter className="text-gray-500" />
              <select
                value={filters.status}
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                className="border border-gray-300 rounded-lg px-3 py-2"
              >
                <option value="all">جميع الحالات</option>
                <option value="paid">مدفوع</option>
                <option value="pending">معلق</option>
                <option value="overdue">متأخر</option>
              </select>
            </div>
          </div>
        </div>

        {/* التبويبات */}
        <div className="bg-white rounded-lg shadow-sm mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                const isActive = activeTab === tab.id;
                
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center gap-2 px-6 py-4 border-b-2 font-medium transition-colors ${
                      isActive
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="text-lg" />
                    {tab.label}
                    <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                      {costs[tab.id]?.length || 0}
                    </span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* إحصائيات سريعة */}
          <div className="p-6 border-b border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center gap-3">
                  <FiDollarSign className="text-blue-600 text-2xl" />
                  <div>
                    <p className="text-sm text-gray-600">إجمالي التكاليف</p>
                    <p className="text-xl font-bold text-blue-600">
                      {formatNumber(getTotalCosts(activeTab))} ج.م
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex items-center gap-3">
                  <FiDollarSign className="text-green-600 text-2xl" />
                  <div>
                    <p className="text-sm text-gray-600">المدفوع</p>
                    <p className="text-xl font-bold text-green-600">
                      {formatNumber(costs[activeTab]?.filter(item => item.status === 'paid').reduce((sum, item) => sum + item.amount, 0) || 0)} ج.م
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="bg-yellow-50 p-4 rounded-lg">
                <div className="flex items-center gap-3">
                  <FiDollarSign className="text-yellow-600 text-2xl" />
                  <div>
                    <p className="text-sm text-gray-600">معلق</p>
                    <p className="text-xl font-bold text-yellow-600">
                      {formatNumber(costs[activeTab]?.filter(item => item.status === 'pending').reduce((sum, item) => sum + item.amount, 0) || 0)} ج.م
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="bg-red-50 p-4 rounded-lg">
                <div className="flex items-center gap-3">
                  <FiDollarSign className="text-red-600 text-2xl" />
                  <div>
                    <p className="text-sm text-gray-600">متأخر</p>
                    <p className="text-xl font-bold text-red-600">
                      {formatNumber(costs[activeTab]?.filter(item => item.status === 'overdue').reduce((sum, item) => sum + item.amount, 0) || 0)} ج.م
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* محتوى التبويب */}
        <div className="bg-white rounded-lg shadow-sm">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <FiRefreshCw className="animate-spin text-4xl text-gray-400" />
              <span className="mr-3 text-gray-600">جاري تحميل البيانات...</span>
            </div>
          ) : (
            <CostTable
              costs={costs[activeTab] || []}
              type={activeTab}
              onEdit={setEditingItem}
              onDelete={(id) => {
                setCosts(prev => ({
                  ...prev,
                  [activeTab]: prev[activeTab].filter(item => item.id !== id)
                }));
              }}
              formatNumber={formatNumber}
            />
          )}
        </div>

        {/* مودال إضافة/تعديل التكلفة */}
        {(showAddModal || editingItem) && (
          <CostModal
            isOpen={showAddModal || !!editingItem}
            onClose={() => {
              setShowAddModal(false);
              setEditingItem(null);
            }}
            onSave={async (costData) => {
              try {
                let method, url, requestBody;

                if (activeTab === 'cars') {
                  method = editingItem ? 'PUT' : 'POST';
                  url = '/api/cars-costs';
                  requestBody = {
                    id: editingItem?.id,
                    month: costData.month,
                    year: costData.year,
                    carCount: costData.carCount,
                    rentalValue: costData.rentalValue
                  };
                } else {
                  method = editingItem ? 'PUT' : 'POST';
                  url = '/api/costs';
                  requestBody = {
                    ...costData,
                    id: editingItem?.id,
                    type: getTypeFromTab(activeTab)
                  };
                }

                const response = await fetch(url, {
                  method,
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify(requestBody),
                });

                const data = await response.json();

                if (data.success) {
                  setShowAddModal(false);
                  setEditingItem(null);
                  fetchCosts();
                  fetchSummary();
                  alert('تم حفظ التكلفة بنجاح');
                } else {
                  alert('خطأ: ' + data.message);
                }
              } catch (error) {

                alert('حدث خطأ في حفظ التكلفة');
              }
            }}
            editData={editingItem}
            costType={activeTab}
          />
        )}

        {/* مودال لوحة التكاليف */}
        {showSummary && (
          <CostsSummaryModal
            isOpen={showSummary}
            onClose={() => setShowSummary(false)}
            summary={summary}
            activeTab={activeTab}
            filters={filters}
            onOpenReport={openVersionRequest}
          />
        )}

        {/* مودال رفع طلب الإصدار */}
        {showUploadModal && (
          <UploadVersionRequestModal
            isOpen={showUploadModal}
            onClose={() => setShowUploadModal(false)}
            onUpload={async (uploadData) => {
              try {
                const formData = new FormData();
                formData.append('file', uploadData.file);
                formData.append('month', uploadData.month);
                formData.append('year', uploadData.year);
                formData.append('type', 'carscost');

                const response = await fetch('/api/upload-version-request', {
                  method: 'POST',
                  body: formData,
                });

                const data = await response.json();

                if (data.success) {
                  setShowUploadModal(false);
                  fetchSummary(); // إعادة تحميل طلبات الإصدار
                  alert('تم رفع طلب الإصدار بنجاح');
                } else {
                  alert('خطأ: ' + data.message);
                }
              } catch (error) {

                alert('حدث خطأ في رفع طلب الإصدار');
              }
            }}
          />
        )}
      </div>
    </MainLayout>
  );
}

// دالة مساعدة لتحويل نوع التبويب إلى نوع التكلفة
function getTypeFromTab(tab) {
  switch (tab) {
    case 'apartments': return 'apartment';
    case 'cars': return 'car';
    case 'tempWorkers': return 'temp_worker';
    default: return 'other';
  }
}

// مكون جدول التكاليف
function CostTable({ costs, type, onEdit, onDelete, formatNumber }) {
  const getStatusColor = (status) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'paid': return 'مدفوع';
      case 'pending': return 'معلق';
      case 'overdue': return 'متأخر';
      default: return 'غير محدد';
    }
  };

  if (!costs || costs.length === 0) {
    return (
      <div className="text-center py-12">
        <FiDollarSign className="text-6xl text-gray-300 mx-auto mb-4" />
        <h3 className="text-xl font-medium text-gray-600 mb-2">لا توجد تكاليف</h3>
        <p className="text-gray-500">لم يتم العثور على تكاليف للفترة المحددة</p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              النوع
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              الوصف
            </th>
            {type === 'cars' && (
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                عدد السيارات
              </th>
            )}
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              المبلغ
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              التاريخ
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              الحالة
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              الإجراءات
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {costs.map((cost, index) => (
            <tr key={cost.id || index} className="hover:bg-gray-50">
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                {cost.type}
              </td>
              <td className="px-6 py-4 text-sm text-gray-900">
                {cost.description}
              </td>
              {type === 'cars' && (
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                    {cost.car_count || 0} سيارة
                  </span>
                </td>
              )}
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {formatNumber(cost.amount)} ج.م
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {new Date(cost.date).toLocaleDateString('ar-EG')}
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(cost.status)}`}>
                  {getStatusText(cost.status)}
                </span>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div className="flex gap-2">
                  <button
                    onClick={() => onEdit(cost)}
                    className="text-blue-600 hover:text-blue-900"
                    title="تعديل"
                  >
                    <FiEdit />
                  </button>
                  <button
                    onClick={() => {
                      if (confirm('هل أنت متأكد من حذف هذه التكلفة؟')) {
                        onDelete(cost.id);
                      }
                    }}
                    className="text-red-600 hover:text-red-900"
                    title="حذف"
                  >
                    <FiTrash2 />
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}

// مكون مودال إضافة/تعديل التكلفة
function CostModal({ isOpen, onClose, onSave, editData, costType }) {
  const [showVersionUpload, setShowVersionUpload] = useState(false);
  const [formData, setFormData] = useState({
    // لتكاليف السيارات
    month: '',
    year: new Date().getFullYear().toString(),
    carCount: '',
    rentalValue: '',
    // للتكاليف العامة
    category: '',
    description: '',
    amount: '',
    date: new Date().toISOString().split('T')[0],
    status: 'pending',
    reference: ''
  });

  useEffect(() => {
    if (editData) {
      if (costType === 'cars') {
        // بيانات تكاليف السيارات
        setFormData({
          month: editData.month_name || '',
          year: editData.year_name || new Date().getFullYear().toString(),
          carCount: editData.car_count || '',
          rentalValue: editData.amount || '',
          category: '',
          description: '',
          amount: '',
          date: new Date().toISOString().split('T')[0],
          status: 'pending',
          reference: ''
        });
      } else {
        // بيانات التكاليف العامة
        setFormData({
          month: '',
          year: new Date().getFullYear().toString(),
          carCount: '',
          rentalValue: '',
          category: editData.Category || '',
          description: editData.description || '',
          amount: editData.amount || '',
          date: editData.date ? new Date(editData.date).toISOString().split('T')[0] : '',
          status: editData.status || 'pending',
          reference: editData.Reference || ''
        });
      }
    } else {
      setFormData({
        month: '',
        year: new Date().getFullYear().toString(),
        carCount: '',
        rentalValue: '',
        category: '',
        description: '',
        amount: '',
        date: new Date().toISOString().split('T')[0],
        status: 'pending',
        reference: ''
      });
    }
  }, [editData, costType]);

  const handleSubmit = (e) => {
    e.preventDefault();

    if (costType === 'cars') {
      // التحقق من بيانات تكاليف السيارات
      if (!formData.month || !formData.year || !formData.carCount || !formData.rentalValue) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
      }
    } else {
      // التحقق من بيانات التكاليف العامة
      if (!formData.description || !formData.amount) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
      }
    }

    onSave(formData);
  };

  const getCategoryOptions = () => {
    switch (costType) {
      case 'apartments':
        return [
          'صيانة دورية',
          'إصلاحات طارئة',
          'تجديدات',
          'مرافق',
          'تنظيف',
          'أخرى'
        ];
      case 'cars':
        return [
          'صيانة دورية',
          'إصلاحات',
          'وقود',
          'تأمين',
          'رخص',
          'إطارات',
          'أخرى'
        ];
      case 'tempWorkers':
        return [
          'رواتب',
          'مكافآت',
          'تأمينات',
          'مواصلات',
          'أدوات عمل',
          'أخرى'
        ];
      default:
        return ['أخرى'];
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h3 className="text-xl font-bold text-gray-800">
            {editData ? 'تعديل التكلفة' : 'إضافة تكلفة جديدة'}
            {costType === 'cars' && ' - السيارات'}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ✕
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {costType === 'cars' ? (
            // نموذج تكاليف السيارات
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الشهر *
                </label>
                <select
                  value={formData.month}
                  onChange={(e) => setFormData(prev => ({ ...prev, month: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="">اختر الشهر</option>
                  <option value="يناير">يناير</option>
                  <option value="فبراير">فبراير</option>
                  <option value="مارس">مارس</option>
                  <option value="أبريل">أبريل</option>
                  <option value="مايو">مايو</option>
                  <option value="يونيو">يونيو</option>
                  <option value="يوليو">يوليو</option>
                  <option value="أغسطس">أغسطس</option>
                  <option value="سبتمبر">سبتمبر</option>
                  <option value="أكتوبر">أكتوبر</option>
                  <option value="نوفمبر">نوفمبر</option>
                  <option value="ديسمبر">ديسمبر</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  السنة *
                </label>
                <select
                  value={formData.year}
                  onChange={(e) => setFormData(prev => ({ ...prev, year: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="">اختر السنة</option>
                  <option value="2020">2020</option>
                  <option value="2021">2021</option>
                  <option value="2022">2022</option>
                  <option value="2023">2023</option>
                  <option value="2024">2024</option>
                  <option value="2025">2025</option>
                  <option value="2026">2026</option>
                  <option value="2027">2027</option>
                  <option value="2028">2028</option>
                  <option value="2029">2029</option>
                  <option value="2030">2030</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  عدد السيارات *
                </label>
                <input
                  type="number"
                  min="1"
                  value={formData.carCount}
                  onChange={(e) => setFormData(prev => ({ ...prev, carCount: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  placeholder="عدد السيارات"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  القيمة الإيجارية (ج.م) *
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.rentalValue}
                  onChange={(e) => setFormData(prev => ({ ...prev, rentalValue: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0.00"
                  required
                />
              </div>
            </>
          ) : (
            // نموذج التكاليف العامة
            <>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الفئة *
                </label>
                <select
                  value={formData.category}
                  onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="">اختر الفئة</option>
                  {getCategoryOptions().map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الوصف *
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  rows="3"
                  placeholder="وصف تفصيلي للتكلفة..."
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  المبلغ (ج.م) *
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.amount}
                  onChange={(e) => setFormData(prev => ({ ...prev, amount: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0.00"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  التاريخ *
                </label>
                <input
                  type="date"
                  value={formData.date}
                  onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  الحالة
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="pending">معلق</option>
                  <option value="paid">مدفوع</option>
                  <option value="overdue">متأخر</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  المرجع
                </label>
                <input
                  type="text"
                  value={formData.reference}
                  onChange={(e) => setFormData(prev => ({ ...prev, reference: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  placeholder="رقم الفاتورة أو المرجع..."
                />
              </div>
            </>
          )}

          {/* زر إضافة طلب الإصدار لتكاليف السيارات */}
          {costType === 'cars' && !editData && formData.month && formData.year && (
            <div className="bg-orange-50 p-4 rounded-lg border border-orange-200">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-orange-800">طلب الإصدار</h4>
                  <p className="text-xs text-orange-600">
                    إضافة طلب إصدار لشهر {formData.month} سنة {formData.year}
                  </p>
                </div>
                <button
                  type="button"
                  onClick={() => setShowVersionUpload(true)}
                  className="bg-orange-600 text-white px-3 py-1 rounded text-sm hover:bg-orange-700 flex items-center gap-1"
                >
                  <FiCalendar className="text-xs" />
                  إضافة طلب إصدار
                </button>
              </div>
            </div>
          )}

          {/* مودال رفع طلب الإصدار المدمج */}
          {showVersionUpload && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60] p-4">
              <div className="bg-white rounded-lg shadow-xl max-w-sm w-full">
                <div className="flex items-center justify-between p-4 border-b">
                  <h4 className="text-lg font-bold text-gray-800">
                    رفع طلب إصدار {formData.month} {formData.year}
                  </h4>
                  <button
                    onClick={() => setShowVersionUpload(false)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    ✕
                  </button>
                </div>

                <div className="p-4">
                  <input
                    type="file"
                    accept=".pdf"
                    onChange={async (e) => {
                      const file = e.target.files[0];
                      if (file) {
                        try {
                          // تحويل اسم الشهر إلى رقم
                          const monthNumbers = {
                            'يناير': '1', 'فبراير': '2', 'مارس': '3', 'أبريل': '4',
                            'مايو': '5', 'يونيو': '6', 'يوليو': '7', 'أغسطس': '8',
                            'سبتمبر': '9', 'أكتوبر': '10', 'نوفمبر': '11', 'ديسمبر': '12'
                          };

                          const monthNumber = monthNumbers[formData.month];

                          const uploadFormData = new FormData();
                          uploadFormData.append('file', file);
                          uploadFormData.append('month', monthNumber);
                          uploadFormData.append('year', formData.year);
                          uploadFormData.append('type', 'carscost');

                          const response = await fetch('/api/upload-version-request', {
                            method: 'POST',
                            body: uploadFormData,
                          });

                          const data = await response.json();

                          if (data.success) {
                            setShowVersionUpload(false);
                            alert('تم رفع طلب الإصدار بنجاح');
                          } else {
                            alert('خطأ: ' + data.message);
                          }
                        } catch (error) {

                          alert('حدث خطأ في رفع طلب الإصدار');
                        }
                      }
                    }}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500"
                  />
                  <p className="text-xs text-gray-500 mt-2">
                    سيتم حفظ الملف باسم: {formData.month && formData.year ?
                      `${{'يناير': '1', 'فبراير': '2', 'مارس': '3', 'أبريل': '4', 'مايو': '5', 'يونيو': '6', 'يوليو': '7', 'أغسطس': '8', 'سبتمبر': '9', 'أكتوبر': '10', 'نوفمبر': '11', 'ديسمبر': '12'}[formData.month]}-${formData.year}.pdf` :
                      'الشهر-السنة.pdf'
                    }
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="flex gap-3 pt-4">
            <button
              type="submit"
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500"
            >
              {editData ? 'تحديث' : 'إضافة'}
            </button>
            <button
              type="button"
              onClick={onClose}
              className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 focus:ring-2 focus:ring-gray-500"
            >
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// مكون مودال رفع طلب الإصدار
function UploadVersionRequestModal({ isOpen, onClose, onUpload }) {
  const [formData, setFormData] = useState({
    month: '',
    year: new Date().getFullYear().toString(),
    file: null
  });
  const [uploading, setUploading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.month || !formData.year || !formData.file) {
      alert('يرجى ملء جميع الحقول واختيار ملف PDF');
      return;
    }

    setUploading(true);
    try {
      await onUpload(formData);
    } finally {
      setUploading(false);
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (!file.name.toLowerCase().endsWith('.pdf')) {
        alert('يجب أن يكون الملف من نوع PDF');
        e.target.value = '';
        return;
      }
      setFormData(prev => ({ ...prev, file }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full">
        <div className="flex items-center justify-between p-6 border-b">
          <h3 className="text-xl font-bold text-gray-800">
            📤 رفع طلب إصدار جديد
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={uploading}
          >
            ✕
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              الشهر *
            </label>
            <select
              value={formData.month}
              onChange={(e) => setFormData(prev => ({ ...prev, month: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
              required
              disabled={uploading}
            >
              <option value="">اختر الشهر</option>
              <option value="1">يناير</option>
              <option value="2">فبراير</option>
              <option value="3">مارس</option>
              <option value="4">أبريل</option>
              <option value="5">مايو</option>
              <option value="6">يونيو</option>
              <option value="7">يوليو</option>
              <option value="8">أغسطس</option>
              <option value="9">سبتمبر</option>
              <option value="10">أكتوبر</option>
              <option value="11">نوفمبر</option>
              <option value="12">ديسمبر</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              السنة *
            </label>
            <select
              value={formData.year}
              onChange={(e) => setFormData(prev => ({ ...prev, year: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
              required
              disabled={uploading}
            >
              <option value="">اختر السنة</option>
              <option value="2020">2020</option>
              <option value="2021">2021</option>
              <option value="2022">2022</option>
              <option value="2023">2023</option>
              <option value="2024">2024</option>
              <option value="2025">2025</option>
              <option value="2026">2026</option>
              <option value="2027">2027</option>
              <option value="2028">2028</option>
              <option value="2029">2029</option>
              <option value="2030">2030</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              ملف طلب الإصدار (PDF) *
            </label>
            <input
              type="file"
              accept=".pdf"
              onChange={handleFileChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500"
              required
              disabled={uploading}
            />
            <p className="text-xs text-gray-500 mt-1">
              سيتم حفظ الملف باسم: {formData.month && formData.year ? `${formData.month}-${formData.year}.pdf` : 'الشهر-السنة.pdf'}
            </p>
          </div>

          {formData.file && (
            <div className="bg-blue-50 p-3 rounded-md">
              <div className="flex items-center gap-2">
                <FiCalendar className="text-blue-600" />
                <div>
                  <p className="text-sm font-medium text-blue-800">
                    {formData.file.name}
                  </p>
                  <p className="text-xs text-blue-600">
                    حجم الملف: {(formData.file.size / 1024 / 1024).toFixed(2)} ميجابايت
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="flex gap-3 pt-4">
            <button
              type="submit"
              disabled={uploading}
              className="flex-1 bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {uploading ? (
                <>
                  <FiRefreshCw className="animate-spin" />
                  جاري الرفع...
                </>
              ) : (
                <>
                  <FiDownload className="rotate-180" />
                  رفع طلب الإصدار
                </>
              )}
            </button>
            <button
              type="button"
              onClick={onClose}
              disabled={uploading}
              className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 focus:ring-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              إلغاء
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

// مكون لوحة التكاليف مع الشارت
function CostsSummaryModal({ isOpen, onClose, summary, activeTab, filters, onOpenReport }) {
  if (!isOpen) return null;

  const getTabTitle = () => {
    switch (activeTab) {
      case 'apartments': return 'تكاليف الشقق';
      case 'cars': return 'تكاليف السيارات';
      case 'tempWorkers': return 'تكاليف العمالة المؤقتة';
      default: return 'التكاليف';
    }
  };

  const getMonthName = (monthNumber) => {
    const months = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return months[monthNumber - 1] || 'غير محدد';
  };

  const currentMonthData = summary.monthlyData?.find(data => {
    const monthNames = {
      'يناير': 1, 'فبراير': 2, 'مارس': 3, 'أبريل': 4,
      'مايو': 5, 'يونيو': 6, 'يوليو': 7, 'أغسطس': 8,
      'سبتمبر': 9, 'أكتوبر': 10, 'نوفمبر': 11, 'ديسمبر': 12
    };
    return monthNames[data.Month] === filters.month;
  });

  const hasVersionRequest = summary.reports?.some(report =>
    report.Path.includes(`${filters.month}-${filters.year}`)
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b">
          <h3 className="text-2xl font-bold text-gray-800">
            📊 لوحة {getTabTitle()} - {getMonthName(filters.month)} {filters.year}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 text-2xl"
          >
            ✕
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* إحصائيات الشهر المحدد */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg">
            <h4 className="text-xl font-bold text-gray-800 mb-4">
              📅 إحصائيات {getMonthName(filters.month)} {filters.year}
            </h4>

            {currentMonthData ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-white p-4 rounded-lg shadow">
                  <div className="flex items-center gap-3">
                    <FiDollarSign className="text-green-600 text-2xl" />
                    <div>
                      <p className="text-sm text-gray-600">إجمالي التكلفة</p>
                      <p className="text-2xl font-bold text-green-600">
                        {currentMonthData.Amount?.toLocaleString()} ج.م
                      </p>
                    </div>
                  </div>
                </div>

                {activeTab === 'cars' && (
                  <div className="bg-white p-4 rounded-lg shadow">
                    <div className="flex items-center gap-3">
                      <FiTruck className="text-blue-600 text-2xl" />
                      <div>
                        <p className="text-sm text-gray-600">عدد السيارات</p>
                        <p className="text-2xl font-bold text-blue-600">
                          {currentMonthData.CarCount} سيارة
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                <div className="bg-white p-4 rounded-lg shadow">
                  <div className="flex items-center gap-3">
                    <FiCalendar className="text-purple-600 text-2xl" />
                    <div>
                      <p className="text-sm text-gray-600">طلب الإصدار</p>
                      <p className="text-lg font-bold text-purple-600">
                        {hasVersionRequest ? '✅ متوفر' : '❌ غير متوفر'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <FiDollarSign className="text-6xl text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-gray-600 mb-2">لا توجد بيانات</h3>
                <p className="text-gray-500">لا توجد تكاليف مسجلة لهذا الشهر</p>
              </div>
            )}
          </div>

          {/* الإحصائيات السنوية */}
          <div className="bg-gray-50 p-6 rounded-lg">
            <h4 className="text-xl font-bold text-gray-800 mb-4">
              📈 إحصائيات السنة {filters.year}
            </h4>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-white p-4 rounded-lg shadow">
                <div className="flex items-center gap-3">
                  <FiDollarSign className="text-green-600 text-2xl" />
                  <div>
                    <p className="text-sm text-gray-600">إجمالي السنة</p>
                    <p className="text-xl font-bold text-green-600">
                      {summary.totalAmount?.toLocaleString()} ج.م
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-4 rounded-lg shadow">
                <div className="flex items-center gap-3">
                  <FiRefreshCw className="text-blue-600 text-2xl" />
                  <div>
                    <p className="text-sm text-gray-600">عدد السجلات</p>
                    <p className="text-xl font-bold text-blue-600">
                      {summary.totalRecords}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-4 rounded-lg shadow">
                <div className="flex items-center gap-3">
                  <FiDollarSign className="text-purple-600 text-2xl" />
                  <div>
                    <p className="text-sm text-gray-600">متوسط التكلفة</p>
                    <p className="text-xl font-bold text-purple-600">
                      {summary.avgAmount?.toLocaleString()} ج.م
                    </p>
                  </div>
                </div>
              </div>

              {activeTab === 'cars' && (
                <div className="bg-white p-4 rounded-lg shadow">
                  <div className="flex items-center gap-3">
                    <FiTruck className="text-orange-600 text-2xl" />
                    <div>
                      <p className="text-sm text-gray-600">إجمالي السيارات</p>
                      <p className="text-xl font-bold text-orange-600">
                        {summary.totalCars} سيارة
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* شارت التكاليف الشهرية */}
            {summary.monthlyData && summary.monthlyData.length > 0 && (
              <div className="bg-white p-4 rounded-lg shadow">
                <h5 className="text-lg font-bold text-gray-800 mb-4">📊 التكاليف الشهرية</h5>
                <MonthlyCostsChart data={summary.monthlyData} selectedMonth={filters.month} />
              </div>
            )}
          </div>

          {/* أزرار الإجراءات */}
          <div className="flex gap-3 pt-4 border-t">
            {hasVersionRequest && (
              <button
                onClick={onOpenReport}
                className="bg-orange-600 text-white py-2 px-4 rounded-md hover:bg-orange-700 flex items-center gap-2"
              >
                <FiCalendar />
                فتح طلب إصدار {getMonthName(filters.month)}
              </button>
            )}

            <button
              onClick={onClose}
              className="bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400"
            >
              إغلاق
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// مكون الشارت البسيط
function MonthlyCostsChart({ data, selectedMonth }) {
  const maxAmount = Math.max(...data.map(item => item.Amount || 0));

  const monthNames = {
    'يناير': 1, 'فبراير': 2, 'مارس': 3, 'أبريل': 4,
    'مايو': 5, 'يونيو': 6, 'يوليو': 7, 'أغسطس': 8,
    'سبتمبر': 9, 'أكتوبر': 10, 'نوفمبر': 11, 'ديسمبر': 12
  };

  return (
    <div className="space-y-3">
      {data.map((item, index) => {
        const monthNumber = monthNames[item.Month];
        const isSelected = monthNumber === selectedMonth;
        const percentage = maxAmount > 0 ? (item.Amount / maxAmount) * 100 : 0;

        return (
          <div key={index} className="flex items-center gap-4">
            <div className="w-20 text-sm font-medium text-gray-700">
              {item.Month}
            </div>
            <div className="flex-1 bg-gray-200 rounded-full h-6 relative">
              <div
                className={`h-6 rounded-full transition-all duration-300 ${
                  isSelected
                    ? 'bg-gradient-to-r from-blue-500 to-purple-600'
                    : 'bg-gradient-to-r from-blue-400 to-blue-500'
                }`}
                style={{ width: `${percentage}%` }}
              />
              {isSelected && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-white text-xs font-bold">المحدد</span>
                </div>
              )}
            </div>
            <div className="w-24 text-sm font-medium text-gray-700 text-left">
              {item.Amount?.toLocaleString()} ج.م
            </div>
          </div>
        );
      })}
    </div>
  );
}
