import { NextResponse } from 'next/server';
import sql from 'mssql';

// إعداد الاتصال بقاعدة البيانات
const config = {
  user: process.env.DB_USER || 'sa',
  password: process.env.DB_PASSWORD || 'MyStrongPassword123',
  server: process.env.DB_SERVER || 'localhost',
  database: process.env.DB_NAME || 'EmployeeManagement',
  options: {
    encrypt: false,
    trustServerCertificate: true,
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
};

// إنشاء جداول كشف الرواتب
async function createPayrollTables(pool) {
  try {
    // 1. جدول الرواتب الأساسية
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='EmployeeSalaries' AND xtype='U')
      BEGIN
        CREATE TABLE EmployeeSalaries (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          EmployeeCode NVARCHAR(20) NOT NULL UNIQUE,
          EmployeeName NVARCHAR(100) NOT NULL,
          Department NVARCHAR(100),
          JobTitle NVARCHAR(100),
          BasicSalary DECIMAL(10,2) NOT NULL DEFAULT 0,
          HousingAllowance DECIMAL(10,2) DEFAULT 0,
          TransportAllowance DECIMAL(10,2) DEFAULT 0,
          MealAllowance DECIMAL(10,2) DEFAULT 0,
          OtherAllowances DECIMAL(10,2) DEFAULT 0,
          SocialInsurance DECIMAL(10,2) DEFAULT 0,
          MedicalInsurance DECIMAL(10,2) DEFAULT 0,
          TaxDeduction DECIMAL(10,2) DEFAULT 0,
          OtherDeductions DECIMAL(10,2) DEFAULT 0,
          IsActive BIT DEFAULT 1,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),
          
          INDEX idx_employee_code (EmployeeCode)
        )
      END
    `);

    // 2. جدول كشف الرواتب الشهرية
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MonthlyPayroll' AND xtype='U')
      BEGIN
        CREATE TABLE MonthlyPayroll (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          EmployeeCode NVARCHAR(20) NOT NULL,
          EmployeeName NVARCHAR(100) NOT NULL,
          Department NVARCHAR(100),
          JobTitle NVARCHAR(100),
          Month INT NOT NULL,
          Year INT NOT NULL,
          BasicSalary DECIMAL(10,2) NOT NULL DEFAULT 0,
          TotalAllowances DECIMAL(10,2) DEFAULT 0,
          TotalEffects DECIMAL(10,2) DEFAULT 0, -- صافي المؤثرات الشهرية
          TotalDeductions DECIMAL(10,2) DEFAULT 0,
          GrossSalary DECIMAL(10,2) DEFAULT 0, -- الراتب الإجمالي
          NetSalary DECIMAL(10,2) DEFAULT 0, -- الراتب الصافي
          WorkingDays INT DEFAULT 30,
          ActualWorkingDays INT DEFAULT 30,
          AbsentDays INT DEFAULT 0,
          OvertimeHours DECIMAL(5,2) DEFAULT 0,
          IsCalculated BIT DEFAULT 0,
          CalculatedAt DATETIME,
          Notes NVARCHAR(MAX),
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),
          
          INDEX idx_employee_month (EmployeeCode, Month, Year),
          UNIQUE(EmployeeCode, Month, Year)
        )
      END
    `);

    // 3. جدول ملخص كشف الرواتب الشهرية
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PayrollSummary' AND xtype='U')
      BEGIN
        CREATE TABLE PayrollSummary (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          Month INT NOT NULL,
          Year INT NOT NULL,
          TotalEmployees INT DEFAULT 0,
          TotalBasicSalary DECIMAL(15,2) DEFAULT 0,
          TotalAllowances DECIMAL(15,2) DEFAULT 0,
          TotalEffects DECIMAL(15,2) DEFAULT 0,
          TotalDeductions DECIMAL(15,2) DEFAULT 0,
          TotalGrossSalary DECIMAL(15,2) DEFAULT 0,
          TotalNetSalary DECIMAL(15,2) DEFAULT 0,
          TotalTax DECIMAL(15,2) DEFAULT 0,
          TotalInsurance DECIMAL(15,2) DEFAULT 0,
          IsCalculated BIT DEFAULT 0,
          CalculatedAt DATETIME,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),
          
          UNIQUE(Month, Year)
        )
      END
    `);

    return true;

  } catch (error) {

    throw error;
  }
}

// معالج الطلبات
export async function POST(request) {
  let pool;
  
  try {
    const data = await request.json();
    const { action } = data;

    // الاتصال بقاعدة البيانات
    pool = await sql.connect(config);

    // إنشاء الجداول إذا لم تكن موجودة
    await createPayrollTables(pool);

    switch (action) {
      case 'list':
        return await getMonthlyPayroll(pool, data);
      case 'calculate':
        return await calculateMonthlyPayroll(pool, data);
      case 'update':
        return await updatePayrollRecord(pool, data);
      case 'summary':
        return await getPayrollSummary(pool, data);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      try {
        await pool.close();
      } catch (error) {

      }
    }
  }
}

// جلب كشف الرواتب الشهرية
async function getMonthlyPayroll(pool, data) {
  try {
    const { month, year, department, employeeCode } = data;

    let whereClause = 'WHERE 1=1';
    const request = pool.request();

    if (month) {
      whereClause += ' AND Month = @month';
      request.input('month', sql.Int, month);
    }

    if (year) {
      whereClause += ' AND Year = @year';
      request.input('year', sql.Int, year);
    }

    if (department) {
      whereClause += ' AND Department = @department';
      request.input('department', sql.NVarChar, department);
    }

    if (employeeCode) {
      whereClause += ' AND EmployeeCode = @employeeCode';
      request.input('employeeCode', sql.NVarChar, employeeCode);
    }

    // جلب كشف الرواتب
    const payrollQuery = `
      SELECT
        ID,
        EmployeeCode,
        EmployeeName,
        Department,
        JobTitle,
        Month,
        Year,
        BasicSalary,
        TotalAllowances,
        TotalEffects,
        TotalDeductions,
        GrossSalary,
        NetSalary,
        WorkingDays,
        ActualWorkingDays,
        AbsentDays,
        OvertimeHours,
        IsCalculated,
        CalculatedAt,
        Notes
      FROM MonthlyPayroll
      ${whereClause}
      ORDER BY EmployeeName
    `;

    const payrollResult = await request.query(payrollQuery);

    // جلب الملخص
    const summaryResult = await pool.request()
      .input('month', sql.Int, month || new Date().getMonth() + 1)
      .input('year', sql.Int, year || new Date().getFullYear())
      .query(`
        SELECT
          COUNT(*) as TotalEmployees,
          ISNULL(SUM(BasicSalary), 0) as TotalBasicSalary,
          ISNULL(SUM(TotalAllowances), 0) as TotalAllowances,
          ISNULL(SUM(TotalEffects), 0) as TotalEffects,
          ISNULL(SUM(TotalDeductions), 0) as TotalDeductions,
          ISNULL(SUM(GrossSalary), 0) as TotalGrossSalary,
          ISNULL(SUM(NetSalary), 0) as TotalNetSalary
        FROM MonthlyPayroll
        WHERE Month = @month AND Year = @year
      `);

    const summary = summaryResult.recordset[0] || {};

    return NextResponse.json({
      success: true,
      data: payrollResult.recordset,
      summary: summary
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب كشف الرواتب: ' + error.message
    }, { status: 500 });
  }
}

// حساب كشف الرواتب الشهرية
async function calculateMonthlyPayroll(pool, data) {
  try {
    const { month, year } = data;

    // 1. حذف البيانات السابقة للشهر
    await pool.request()
      .input('month', sql.Int, month)
      .input('year', sql.Int, year)
      .query(`
        DELETE FROM MonthlyPayroll 
        WHERE Month = @month AND Year = @year
      `);

    // 2. جلب بيانات الموظفين والرواتب الأساسية
    const employeesResult = await pool.request().query(`
      SELECT 
        e.EmployeeID as EmployeeCode,
        e.FullName as EmployeeName,
        e.Department,
        e.JobTitle,
        ISNULL(s.BasicSalary, 0) as BasicSalary,
        ISNULL(s.HousingAllowance, 0) as HousingAllowance,
        ISNULL(s.TransportAllowance, 0) as TransportAllowance,
        ISNULL(s.MealAllowance, 0) as MealAllowance,
        ISNULL(s.OtherAllowances, 0) as OtherAllowances,
        ISNULL(s.SocialInsurance, 0) as SocialInsurance,
        ISNULL(s.MedicalInsurance, 0) as MedicalInsurance,
        ISNULL(s.TaxDeduction, 0) as TaxDeduction,
        ISNULL(s.OtherDeductions, 0) as OtherDeductions
      FROM Employees e
      LEFT JOIN EmployeeSalaries s ON e.EmployeeID = s.EmployeeCode
      WHERE e.CurrentStatus = 'active'
    `);

    // 3. حساب المؤثرات الشهرية لكل موظف
    for (const employee of employeesResult.recordset) {
      // جلب المؤثرات الشهرية للموظف
      const effectsResult = await pool.request()
        .input('employeeCode', sql.NVarChar, employee.EmployeeCode)
        .input('month', sql.Int, month)
        .input('year', sql.Int, year)
        .query(`
          SELECT
            ISNULL(SUM(CASE WHEN EffectType IN ('missions', 'holiday_work', 'meal_allowance') THEN Amount ELSE 0 END), 0) as PositiveEffects,
            ISNULL(SUM(CASE WHEN EffectType IN ('unpaid_leave', 'sick_leave', 'penalties') THEN Amount ELSE 0 END), 0) as NegativeEffects
          FROM MonthlyEffects
          WHERE EmployeeCode = @employeeCode AND Month = @month AND Year = @year AND IsActive = 1
        `);

      const effects = effectsResult.recordset[0] || {};
      const totalEffects = (effects.PositiveEffects || 0) - (effects.NegativeEffects || 0);

      // حساب الراتب
      const totalAllowances = (employee.HousingAllowance || 0) + 
                             (employee.TransportAllowance || 0) + 
                             (employee.MealAllowance || 0) + 
                             (employee.OtherAllowances || 0);

      const totalDeductions = (employee.SocialInsurance || 0) + 
                             (employee.MedicalInsurance || 0) + 
                             (employee.TaxDeduction || 0) + 
                             (employee.OtherDeductions || 0);

      const grossSalary = (employee.BasicSalary || 0) + totalAllowances + totalEffects;
      const netSalary = grossSalary - totalDeductions;

      // إدراج السجل في جدول كشف الرواتب
      await pool.request()
        .input('employeeCode', sql.NVarChar, employee.EmployeeCode)
        .input('employeeName', sql.NVarChar, employee.EmployeeName)
        .input('department', sql.NVarChar, employee.Department)
        .input('jobTitle', sql.NVarChar, employee.JobTitle)
        .input('month', sql.Int, month)
        .input('year', sql.Int, year)
        .input('basicSalary', sql.Decimal(10, 2), employee.BasicSalary || 0)
        .input('totalAllowances', sql.Decimal(10, 2), totalAllowances)
        .input('totalEffects', sql.Decimal(10, 2), totalEffects)
        .input('totalDeductions', sql.Decimal(10, 2), totalDeductions)
        .input('grossSalary', sql.Decimal(10, 2), grossSalary)
        .input('netSalary', sql.Decimal(10, 2), netSalary)
        .query(`
          INSERT INTO MonthlyPayroll (
            EmployeeCode, EmployeeName, Department, JobTitle, Month, Year,
            BasicSalary, TotalAllowances, TotalEffects, TotalDeductions,
            GrossSalary, NetSalary, IsCalculated, CalculatedAt
          )
          VALUES (
            @employeeCode, @employeeName, @department, @jobTitle, @month, @year,
            @basicSalary, @totalAllowances, @totalEffects, @totalDeductions,
            @grossSalary, @netSalary, 1, GETDATE()
          )
        `);
    }

    // 4. حساب الملخص العام
    const summaryResult = await pool.request()
      .input('month', sql.Int, month)
      .input('year', sql.Int, year)
      .query(`
        SELECT
          COUNT(*) as TotalEmployees,
          SUM(BasicSalary) as TotalBasicSalary,
          SUM(TotalAllowances) as TotalAllowances,
          SUM(TotalEffects) as TotalEffects,
          SUM(TotalDeductions) as TotalDeductions,
          SUM(GrossSalary) as TotalGrossSalary,
          SUM(NetSalary) as TotalNetSalary
        FROM MonthlyPayroll
        WHERE Month = @month AND Year = @year
      `);

    const summary = summaryResult.recordset[0];

    // 5. حفظ الملخص
    await pool.request()
      .input('month', sql.Int, month)
      .input('year', sql.Int, year)
      .input('totalEmployees', sql.Int, summary.TotalEmployees)
      .input('totalBasicSalary', sql.Decimal(15, 2), summary.TotalBasicSalary)
      .input('totalAllowances', sql.Decimal(15, 2), summary.TotalAllowances)
      .input('totalEffects', sql.Decimal(15, 2), summary.TotalEffects)
      .input('totalDeductions', sql.Decimal(15, 2), summary.TotalDeductions)
      .input('totalGrossSalary', sql.Decimal(15, 2), summary.TotalGrossSalary)
      .input('totalNetSalary', sql.Decimal(15, 2), summary.TotalNetSalary)
      .query(`
        MERGE PayrollSummary AS target
        USING (SELECT @month as Month, @year as Year) AS source
        ON target.Month = source.Month AND target.Year = source.Year
        WHEN MATCHED THEN
          UPDATE SET
            TotalEmployees = @totalEmployees,
            TotalBasicSalary = @totalBasicSalary,
            TotalAllowances = @totalAllowances,
            TotalEffects = @totalEffects,
            TotalDeductions = @totalDeductions,
            TotalGrossSalary = @totalGrossSalary,
            TotalNetSalary = @totalNetSalary,
            IsCalculated = 1,
            CalculatedAt = GETDATE(),
            UpdatedAt = GETDATE()
        WHEN NOT MATCHED THEN
          INSERT (Month, Year, TotalEmployees, TotalBasicSalary, TotalAllowances,
                  TotalEffects, TotalDeductions, TotalGrossSalary, TotalNetSalary,
                  IsCalculated, CalculatedAt)
          VALUES (@month, @year, @totalEmployees, @totalBasicSalary, @totalAllowances,
                  @totalEffects, @totalDeductions, @totalGrossSalary, @totalNetSalary,
                  1, GETDATE());
      `);

    return NextResponse.json({
      success: true,
      message: 'تم حساب كشف الرواتب بنجاح',
      summary: summary
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في حساب كشف الرواتب: ' + error.message
    }, { status: 500 });
  }
}
