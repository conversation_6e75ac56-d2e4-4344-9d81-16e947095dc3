-- ===================================
-- نظام إدارة المديرين المباشرين
-- Direct Managers Management System
-- ===================================

-- 1. جدول المديرين المباشرين
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DirectManagers' AND xtype='U')
BEGIN
    CREATE TABLE DirectManagers (
        ID INT IDENTITY(1,1) PRIMARY KEY,
        EmployeeCode NVARCHAR(20) NOT NULL,
        EmployeeName NVARCHAR(100) NOT NULL,
        JobTitle NVARCHAR(100),
        Department NVARCHAR(100),
        
        -- المدير المباشر الأول (الأقرب)
        DirectManager1Code NVARCHAR(20),
        DirectManager1Name NVARCHAR(100),
        DirectManager1JobTitle NVARCHAR(100),
        
        -- المدير المباشر الثاني
        DirectManager2Code NVARCHAR(20),
        DirectManager2Name NVARCHAR(100),
        DirectManager2JobTitle NVARCHAR(100),
        
        -- المدير المباشر الثالث
        DirectManager3Code NVARCHAR(20),
        DirectManager3Name NVARCHAR(100),
        DirectManager3JobTitle NVARCHAR(100),
        
        -- المدير المباشر الرابع (الأعلى)
        DirectManager4Code NVARCHAR(20),
        DirectManager4Name NVARCHAR(100),
        DirectManager4JobTitle NVARCHAR(100),
        
        -- معلومات إضافية
        HierarchyLevel INT DEFAULT 0, -- المستوى في الهيكل التنظيمي
        IsActive BIT DEFAULT 1,
        Notes NVARCHAR(500),
        
        -- معلومات التتبع
        CreatedAt DATETIME DEFAULT GETDATE(),
        UpdatedAt DATETIME DEFAULT GETDATE(),
        CreatedBy NVARCHAR(100),
        UpdatedBy NVARCHAR(100),
        
        -- فهارس
        UNIQUE(EmployeeCode),
        INDEX IX_DirectManagers_EmployeeCode (EmployeeCode),
        INDEX IX_DirectManagers_Manager1 (DirectManager1Code),
        INDEX IX_DirectManagers_Manager2 (DirectManager2Code),
        INDEX IX_DirectManagers_Manager3 (DirectManager3Code),
        INDEX IX_DirectManagers_Manager4 (DirectManager4Code),
        INDEX IX_DirectManagers_Level (HierarchyLevel),
        INDEX IX_DirectManagers_Active (IsActive)
    )
END

-- 2. جدول سجل التحديثات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DirectManagersHistory' AND xtype='U')
BEGIN
    CREATE TABLE DirectManagersHistory (
        ID INT IDENTITY(1,1) PRIMARY KEY,
        EmployeeCode NVARCHAR(20) NOT NULL,
        ChangeType NVARCHAR(20) NOT NULL, -- INSERT, UPDATE, DELETE
        OldData NVARCHAR(MAX), -- JSON للبيانات القديمة
        NewData NVARCHAR(MAX), -- JSON للبيانات الجديدة
        ChangedBy NVARCHAR(100),
        ChangedAt DATETIME DEFAULT GETDATE(),
        Reason NVARCHAR(500),
        
        INDEX IX_DirectManagersHistory_Employee (EmployeeCode),
        INDEX IX_DirectManagersHistory_Date (ChangedAt),
        INDEX IX_DirectManagersHistory_Type (ChangeType)
    )
END

-- 3. جدول رفع الملفات
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DirectManagersFileUploads' AND xtype='U')
BEGIN
    CREATE TABLE DirectManagersFileUploads (
        ID INT IDENTITY(1,1) PRIMARY KEY,
        FileName NVARCHAR(255) NOT NULL,
        OriginalFileName NVARCHAR(255) NOT NULL,
        FilePath NVARCHAR(500) NOT NULL,
        FileSize BIGINT,
        FileType NVARCHAR(50),
        
        -- معلومات المعالجة
        ProcessingStatus NVARCHAR(20) DEFAULT 'PENDING', -- PENDING, PROCESSING, COMPLETED, FAILED
        TotalRecords INT DEFAULT 0,
        ProcessedRecords INT DEFAULT 0,
        SuccessfulRecords INT DEFAULT 0,
        FailedRecords INT DEFAULT 0,
        ErrorLog NVARCHAR(MAX),
        
        -- معلومات التتبع
        UploadedBy NVARCHAR(100),
        UploadedAt DATETIME DEFAULT GETDATE(),
        ProcessedAt DATETIME,
        
        INDEX IX_FileUploads_Status (ProcessingStatus),
        INDEX IX_FileUploads_Date (UploadedAt),
        INDEX IX_FileUploads_User (UploadedBy)
    )
END

-- 4. إجراء مخزن لتحديث المديرين المباشرين
IF EXISTS (SELECT * FROM sysobjects WHERE name='sp_UpdateDirectManager' AND type='P')
    DROP PROCEDURE sp_UpdateDirectManager
GO

CREATE PROCEDURE sp_UpdateDirectManager
    @EmployeeCode NVARCHAR(20),
    @EmployeeName NVARCHAR(100),
    @JobTitle NVARCHAR(100) = NULL,
    @Department NVARCHAR(100) = NULL,
    @DirectManager1Code NVARCHAR(20) = NULL,
    @DirectManager1Name NVARCHAR(100) = NULL,
    @DirectManager2Code NVARCHAR(20) = NULL,
    @DirectManager2Name NVARCHAR(100) = NULL,
    @DirectManager3Code NVARCHAR(20) = NULL,
    @DirectManager3Name NVARCHAR(100) = NULL,
    @DirectManager4Code NVARCHAR(20) = NULL,
    @DirectManager4Name NVARCHAR(100) = NULL,
    @UpdatedBy NVARCHAR(100) = NULL,
    @Notes NVARCHAR(500) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @HierarchyLevel INT = 0;
    DECLARE @OldData NVARCHAR(MAX);
    DECLARE @NewData NVARCHAR(MAX);
    DECLARE @ChangeType NVARCHAR(20);
    
    -- حساب المستوى الهرمي
    IF @DirectManager4Code IS NOT NULL SET @HierarchyLevel = 4;
    ELSE IF @DirectManager3Code IS NOT NULL SET @HierarchyLevel = 3;
    ELSE IF @DirectManager2Code IS NOT NULL SET @HierarchyLevel = 2;
    ELSE IF @DirectManager1Code IS NOT NULL SET @HierarchyLevel = 1;
    
    -- التحقق من وجود السجل
    IF EXISTS (SELECT 1 FROM DirectManagers WHERE EmployeeCode = @EmployeeCode)
    BEGIN
        -- حفظ البيانات القديمة
        SELECT @OldData = (
            SELECT * FROM DirectManagers 
            WHERE EmployeeCode = @EmployeeCode 
            FOR JSON AUTO, WITHOUT_ARRAY_WRAPPER
        );
        
        SET @ChangeType = 'UPDATE';
        
        -- تحديث السجل
        UPDATE DirectManagers SET
            EmployeeName = @EmployeeName,
            JobTitle = ISNULL(@JobTitle, JobTitle),
            Department = ISNULL(@Department, Department),
            DirectManager1Code = @DirectManager1Code,
            DirectManager1Name = @DirectManager1Name,
            DirectManager2Code = @DirectManager2Code,
            DirectManager2Name = @DirectManager2Name,
            DirectManager3Code = @DirectManager3Code,
            DirectManager3Name = @DirectManager3Name,
            DirectManager4Code = @DirectManager4Code,
            DirectManager4Name = @DirectManager4Name,
            HierarchyLevel = @HierarchyLevel,
            Notes = ISNULL(@Notes, Notes),
            UpdatedAt = GETDATE(),
            UpdatedBy = @UpdatedBy
        WHERE EmployeeCode = @EmployeeCode;
    END
    ELSE
    BEGIN
        SET @ChangeType = 'INSERT';
        
        -- إدراج سجل جديد
        INSERT INTO DirectManagers (
            EmployeeCode, EmployeeName, JobTitle, Department,
            DirectManager1Code, DirectManager1Name,
            DirectManager2Code, DirectManager2Name,
            DirectManager3Code, DirectManager3Name,
            DirectManager4Code, DirectManager4Name,
            HierarchyLevel, Notes, CreatedBy, UpdatedBy
        ) VALUES (
            @EmployeeCode, @EmployeeName, @JobTitle, @Department,
            @DirectManager1Code, @DirectManager1Name,
            @DirectManager2Code, @DirectManager2Name,
            @DirectManager3Code, @DirectManager3Name,
            @DirectManager4Code, @DirectManager4Name,
            @HierarchyLevel, @Notes, @UpdatedBy, @UpdatedBy
        );
    END
    
    -- حفظ البيانات الجديدة
    SELECT @NewData = (
        SELECT * FROM DirectManagers 
        WHERE EmployeeCode = @EmployeeCode 
        FOR JSON AUTO, WITHOUT_ARRAY_WRAPPER
    );
    
    -- تسجيل التغيير في السجل
    INSERT INTO DirectManagersHistory (
        EmployeeCode, ChangeType, OldData, NewData, ChangedBy, Reason
    ) VALUES (
        @EmployeeCode, @ChangeType, @OldData, @NewData, @UpdatedBy, 
        'تحديث بيانات المدير المباشر'
    );
    
    SELECT 'SUCCESS' as Status, 'تم تحديث البيانات بنجاح' as Message;
END
GO

-- 5. إجراء مخزن للحصول على الهيكل التنظيمي
IF EXISTS (SELECT * FROM sysobjects WHERE name='sp_GetOrganizationalHierarchy' AND type='P')
    DROP PROCEDURE sp_GetOrganizationalHierarchy
GO

CREATE PROCEDURE sp_GetOrganizationalHierarchy
    @EmployeeCode NVARCHAR(20) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    IF @EmployeeCode IS NOT NULL
    BEGIN
        -- الحصول على هيكل موظف محدد
        SELECT 
            dm.*,
            -- معلومات المدير الأول
            m1.EmployeeName as Manager1EmployeeName,
            m1.JobTitle as Manager1JobTitle,
            -- معلومات المدير الثاني
            m2.EmployeeName as Manager2EmployeeName,
            m2.JobTitle as Manager2JobTitle,
            -- معلومات المدير الثالث
            m3.EmployeeName as Manager3EmployeeName,
            m3.JobTitle as Manager3JobTitle,
            -- معلومات المدير الرابع
            m4.EmployeeName as Manager4EmployeeName,
            m4.JobTitle as Manager4JobTitle
        FROM DirectManagers dm
        LEFT JOIN Employees m1 ON dm.DirectManager1Code = m1.EmployeeCode
        LEFT JOIN Employees m2 ON dm.DirectManager2Code = m2.EmployeeCode
        LEFT JOIN Employees m3 ON dm.DirectManager3Code = m3.EmployeeCode
        LEFT JOIN Employees m4 ON dm.DirectManager4Code = m4.EmployeeCode
        WHERE dm.EmployeeCode = @EmployeeCode AND dm.IsActive = 1;
    END
    ELSE
    BEGIN
        -- الحصول على الهيكل الكامل
        SELECT 
            dm.*,
            -- عدد المرؤوسين المباشرين
            (SELECT COUNT(*) FROM DirectManagers sub 
             WHERE sub.DirectManager1Code = dm.EmployeeCode AND sub.IsActive = 1) as DirectReports
        FROM DirectManagers dm
        WHERE dm.IsActive = 1
        ORDER BY dm.HierarchyLevel, dm.EmployeeName;
    END
END
GO

-- 6. بيانات تجريبية (اختيارية)
-- يمكن إضافة بعض البيانات التجريبية هنا إذا لزم الأمر

PRINT 'تم إنشاء نظام إدارة المديرين المباشرين بنجاح';
PRINT 'الجداول المنشأة:';
PRINT '- DirectManagers: جدول المديرين المباشرين';
PRINT '- DirectManagersHistory: سجل التحديثات';
PRINT '- DirectManagersFileUploads: سجل رفع الملفات';
PRINT 'الإجراءات المخزنة:';
PRINT '- sp_UpdateDirectManager: تحديث بيانات المدير المباشر';
PRINT '- sp_GetOrganizationalHierarchy: الحصول على الهيكل التنظيمي';
