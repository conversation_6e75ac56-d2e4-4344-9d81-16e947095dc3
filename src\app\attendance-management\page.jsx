'use client';
import React from 'react';

function MainComponent() {
  const [selectedLang, setSelectedLang] = useState('ar');
  const dir = selectedLang === 'ar' ? 'rtl' : 'ltr';

  const navigationItems = [
    {
      title: { ar: 'تسجيل الحضور اليومي', en: 'Daily Attendance' },
      icon: 'fa-clock',
      link: '/daily-attendance',
      description: {
        ar: 'تسجيل حضور وانصراف الموظفين اليومي',
        en: 'Record daily employee check-in and check-out',
      },
    },
    {
      title: { ar: 'تقرير الحضور الشهري', en: 'Monthly Attendance Report' },
      icon: 'fa-calendar-alt',
      link: '/monthly-attendance',
      description: {
        ar: 'عرض تقارير الحضور الشهرية للموظفين',
        en: 'View monthly attendance reports for employees',
      },
    },
    {
      title: { ar: 'التقارير التفصيلية', en: 'Detailed Reports' },
      icon: 'fa-chart-bar',
      link: '/attendance-reports',
      description: {
        ar: 'تقارير تفصيلية للحضور والانصراف',
        en: 'Detailed attendance and departure reports',
      },
    },
  ];

  return (
    <div
      dir={dir}
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div className="flex items-center gap-4">
            <a
              href="/"
              className="flex items-center text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
            >
              <i
                className={`fas fa-arrow-${
                  selectedLang === 'ar' ? 'left' : 'right'
                } ml-2`}
              ></i>
              {selectedLang === 'ar' ? 'عودة' : 'Back'}
            </a>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              {selectedLang === 'ar' ? 'إدارة الحضور' : 'Attendance Management'}
            </h1>
          </div>
          <button
            onClick={() => setSelectedLang(selectedLang === 'ar' ? 'en' : 'ar')}
            className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
          >
            {selectedLang === 'ar' ? 'English' : 'العربية'}
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {navigationItems.map((item, index) => (
            <a
              key={index}
              href={item.link}
              className="bg-gray-50 dark:bg-gray-800 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300"
            >
              <div className="flex items-center mb-4">
                <i
                  className={`fas ${item.icon} text-3xl text-blue-600 dark:text-blue-400`}
                ></i>
              </div>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-3">
                {item.title[selectedLang]}
              </h2>
              <p className="text-gray-600 dark:text-gray-300">
                {item.description[selectedLang]}
              </p>
            </a>
          ))}
        </div>
      </div>
    </div>
  );
}

export default MainComponent;
