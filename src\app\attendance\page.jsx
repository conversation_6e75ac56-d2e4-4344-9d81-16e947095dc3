'use client';
import React, { useState, useEffect } from 'react';

function MainComponent() {
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [loading, setLoading] = useState(true);
  const [employees, setEmployees] = useState([]);
  const [error, setError] = useState('');
  const [searchId, setSearchId] = useState('');

  useEffect(() => {
    async function fetchEmployees() {
      setLoading(true);
      try {
        const response = await fetch('/api/data-service', {
          method: 'POST',
          body: JSON.stringify({
            table: 'monthly_attendance',
            action: 'list',
          }),
        });
        if (!response.ok) {
          throw new Error('Failed to fetch employees');
        }
        const result = await response.json();
        if (result.success) {
          setEmployees(result.data);
        }
      } catch (error) {

        setError(error.message);
      } finally {
        setLoading(false);
      }
    }

    fetchEmployees();
  }, []);

  const handleAttendanceUpdate = async (employeeId, status) => {
    try {
      const response = await fetch('/api/data-service', {
        method: 'POST',
        body: JSON.stringify({
          table: 'monthly_attendance',
          action: 'update',
          id: employeeId,
          data: {
            [`attendance_data`]: {
              [date]: status,
            },
          },
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update attendance');
      }

      setEmployees((prevEmployees) =>
        prevEmployees.map((emp) =>
          emp.employee_id === employeeId
            ? {
                ...emp,
                attendance_data: {
                  ...emp.attendance_data,
                  [date]: status,
                },
              }
            : emp
        )
      );
    } catch (error) {

      setError(error.message);
    }
  };

  return (
    <div dir="rtl" className="min-h-screen bg-white dark:bg-gray-900">
      <MainComponent currentPath="/attendance" />

      <div className="max-w-7xl mx-auto p-4 md:p-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          تسجيل الحضور والانصراف
        </h1>

        <div className="bg-gray-50 dark:bg-gray-800 p-6 rounded-lg mb-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <input
              type="date"
              value={date}
              onChange={(e) => setDate(e.target.value)}
              className="p-2 border border-gray-300 rounded-md w-full"
              name="date"
            />
            <input
              type="text"
              value={searchId}
              onChange={(e) => setSearchId(e.target.value)}
              placeholder="البحث برقم الموظف"
              className="p-2 border border-gray-300 rounded-md w-full"
              name="searchId"
            />
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-sm">
          <table className="w-full">
            <thead className="bg-gray-100 dark:bg-gray-700">
              <tr>
                <th className="px-4 py-2 text-right">اسم الموظف</th>
                <th className="px-4 py-2 text-right">القسم</th>
                <th className="px-4 py-2 text-right">الحالة</th>
                <th className="px-4 py-2 text-right">الإجراءات</th>
              </tr>
            </thead>
            <tbody>
              {loading ? (
                <tr>
                  <td colSpan="4" className="px-4 py-2 text-center">
                    جاري التحميل...
                  </td>
                </tr>
              ) : error ? (
                <tr>
                  <td
                    colSpan="4"
                    className="px-4 py-2 text-center text-red-600"
                  >
                    {error}
                  </td>
                </tr>
              ) : employees.length === 0 ? (
                <tr>
                  <td colSpan="4" className="px-4 py-2 text-center">
                    لا يوجد موظفين
                  </td>
                </tr>
              ) : (
                employees
                  .filter(
                    (emp) =>
                      searchId === '' ||
                      emp.employee_id
                        .toLowerCase()
                        .includes(searchId.toLowerCase())
                  )
                  .map((emp) => (
                    <tr
                      key={emp.employee_id}
                      className="border-b hover:bg-gray-50 dark:hover:bg-gray-700"
                    >
                      <td className="px-4 py-2">{emp.employee_name}</td>
                      <td className="px-4 py-2">{emp.department}</td>
                      <td className="px-4 py-2">
                        {emp.attendance_data[date] || 'لم يسجل'}
                      </td>
                      <td className="px-4 py-2">
                        <select
                          onChange={(e) =>
                            handleAttendanceUpdate(
                              emp.employee_id,
                              e.target.value
                            )
                          }
                          value={emp.attendance_data[date] || ''}
                          className="p-2 border border-gray-300 rounded-md w-full"
                        >
                          <option value="">اختر الحالة</option>
                          <option value="حاضر">حاضر</option>
                          <option value="غائب">غائب</option>
                          <option value="إجازة">إجازة</option>
                          <option value="مهمة">مهمة رسمية</option>
                        </select>
                      </td>
                    </tr>
                  ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default MainComponent;
