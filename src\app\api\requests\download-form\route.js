import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const formType = searchParams.get('type');

    if (!formType) {
      return NextResponse.json({
        success: false,
        message: 'نوع النموذج مطلوب'
      }, { status: 400 });
    }

    // تحديد مسار الملف بناءً على نوع النموذج
    const formPaths = {
      'leave': 'نموذج طلب الإجازة.xlsx',
      'mission': 'نموذج طلب المأمورية.xlsx',
      'permission': 'نموذج طلب الإذن.xlsx',
      'night-shift': 'أوجيستا  منظومة الطلبات الورقية_.xlsx' // سنستخدم الملف الرئيسي للوردية الليلية
    };

    const fileName = formPaths[formType];
    if (!fileName) {
      return NextResponse.json({
        success: false,
        message: 'نوع النموذج غير مدعوم'
      }, { status: 400 });
    }

    // مسار الملف الكامل - نحاول عدة مسارات
    let filePath = path.join(process.cwd(), 'archiv', 'namazg', fileName);

    // إذا لم يوجد الملف، نحاول المسار المطلق
    if (!fs.existsSync(filePath)) {
      filePath = path.join('E:', 'webapp', 'createxyz-project', 'archiv', 'namazg', fileName);
    }

    // التحقق من وجود الملف
    if (!fs.existsSync(filePath)) {

      return NextResponse.json({
        success: false,
        message: 'النموذج غير متوفر'
      }, { status: 404 });
    }

    // قراءة الملف
    const fileBuffer = fs.readFileSync(filePath);
    
    // تحديد اسم الملف للتحميل
    const downloadNames = {
      'leave': 'نموذج_طلب_الإجازة.xlsx',
      'mission': 'نموذج_طلب_المأمورية.xlsx',
      'permission': 'نموذج_طلب_الإذن.xlsx',
      'night-shift': 'نموذج_إذن_الوردية_الليلية.xlsx'
    };

    const downloadName = downloadNames[formType] || 'نموذج_طلب.xlsx';

    // إرجاع الملف
    return new NextResponse(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': `attachment; filename="${downloadName}"`,
        'Content-Length': fileBuffer.length.toString()
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'خطأ في تحميل النموذج',
      error: error.message
    }, { status: 500 });
  }
}
