'use client';
import React from 'react';
import SharedLayout from '../../components/shared-layout';
import AnimatedEffects from '../../components/animated-effects';

function MainComponent() {
  const { data: user } = useUser();
  const [selectedLang, setSelectedLang] = useState('ar');
  const [formData, setFormData] = useState({
    worker_code: '',
    name: '',
    job_title: '',
    daily_rate: '',
    phone: '',
    nationality: '',
    id_number: '',
    status: 'active',
    notes: '',
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});

  const validateForm = () => {
    const errors = {};

    // التحقق من رمز العامل
    if (!formData.worker_code.trim()) {
      errors.worker_code =
        selectedLang === 'ar' ? 'رمز العامل مطلوب' : 'Worker code is required';
    } else if (!/^[A-Za-z0-9-]+$/.test(formData.worker_code)) {
      errors.worker_code =
        selectedLang === 'ar'
          ? 'رمز العامل يجب أن يحتوي على أحرف وأرقام فقط'
          : 'Worker code must contain only letters and numbers';
    }

    // التحقق من الاسم
    if (!formData.name.trim()) {
      errors.name = selectedLang === 'ar' ? 'الاسم مطلوب' : 'Name is required';
    }

    // التحقق من المسمى الوظيفي
    if (!formData.job_title.trim()) {
      errors.job_title =
        selectedLang === 'ar'
          ? 'المسمى الوظيفي مطلوب'
          : 'Job title is required';
    }

    // التحقق من المعدل اليومي
    if (!formData.daily_rate) {
      errors.daily_rate =
        selectedLang === 'ar'
          ? 'المعدل اليومي مطلوب'
          : 'Daily rate is required';
    } else if (isNaN(formData.daily_rate) || Number(formData.daily_rate) <= 0) {
      errors.daily_rate =
        selectedLang === 'ar'
          ? 'المعدل اليومي يجب أن يكون رقماً موجباً'
          : 'Daily rate must be a positive number';
    }

    // التحقق من رقم الهاتف
    if (!formData.phone.trim()) {
      errors.phone =
        selectedLang === 'ar' ? 'رقم الهاتف مطلوب' : 'Phone number is required';
    } else if (!/^\d{10,}$/.test(formData.phone.replace(/[^0-9]/g, ''))) {
      errors.phone =
        selectedLang === 'ar' ? 'رقم الهاتف غير صالح' : 'Invalid phone number';
    }

    // التحقق من الجنسية
    if (!formData.nationality.trim()) {
      errors.nationality =
        selectedLang === 'ar' ? 'الجنسية مطلوبة' : 'Nationality is required';
    }

    // التحقق من رقم الهوية
    if (!formData.id_number.trim()) {
      errors.id_number =
        selectedLang === 'ar' ? 'رقم الهوية مطلوب' : 'ID number is required';
    } else if (!/^\d{10,}$/.test(formData.id_number.replace(/[^0-9]/g, ''))) {
      errors.id_number =
        selectedLang === 'ar' ? 'رقم الهوية غير صالح' : 'Invalid ID number';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // مسح رسالة الخطأ عند الكتابة
    if (validationErrors[name]) {
      setValidationErrors((prev) => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setError('');
    setSuccess(false);

    try {
      const response = await fetch('/api/data-service', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          table: 'temp_workers',
          action: 'create',
          data: formData,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message ||
            (selectedLang === 'ar'
              ? 'حدث خطأ أثناء إضافة العامل'
              : 'Error adding worker')
        );
      }

      setSuccess(true);
      setFormData({
        worker_code: '',
        name: '',
        job_title: '',
        daily_rate: '',
        phone: '',
        nationality: '',
        id_number: '',
        status: 'active',
        notes: '',
      });
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthCheck>
      <SharedLayout selectedLang={selectedLang}>
        <div className="space-y-8">
          <AnimatedEffects effect="slide" direction="down" delay={100}>
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center">
                  <i className="fas fa-user-plus text-blue-500 ml-2"></i>
                  {selectedLang === 'ar'
                    ? 'إضافة عامل مؤقت'
                    : 'Add Temporary Worker'}
                </h1>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6">
                <AnimatedEffects effect="fade" delay={200}>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {selectedLang === 'ar' ? 'رمز العامل' : 'Worker Code'}
                      </label>
                      <input
                        type="text"
                        name="worker_code"
                        value={formData.worker_code}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-2 rounded-lg border ${
                          validationErrors.worker_code
                            ? 'border-red-500 focus:ring-red-500'
                            : 'border-gray-300 focus:ring-blue-500'
                        } focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white`}
                      />
                      {validationErrors.worker_code && (
                        <p className="mt-1 text-sm text-red-600">
                          {validationErrors.worker_code}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {selectedLang === 'ar' ? 'الاسم' : 'Name'}
                      </label>
                      <input
                        type="text"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-2 rounded-lg border ${
                          validationErrors.name
                            ? 'border-red-500 focus:ring-red-500'
                            : 'border-gray-300 focus:ring-blue-500'
                        } focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white`}
                      />
                      {validationErrors.name && (
                        <p className="mt-1 text-sm text-red-600">
                          {validationErrors.name}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {selectedLang === 'ar' ? 'المسمى الوظيفي' : 'Job Title'}
                      </label>
                      <input
                        type="text"
                        name="job_title"
                        value={formData.job_title}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-2 rounded-lg border ${
                          validationErrors.job_title
                            ? 'border-red-500 focus:ring-red-500'
                            : 'border-gray-300 focus:ring-blue-500'
                        } focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white`}
                      />
                      {validationErrors.job_title && (
                        <p className="mt-1 text-sm text-red-600">
                          {validationErrors.job_title}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {selectedLang === 'ar' ? 'المعدل اليومي' : 'Daily Rate'}
                      </label>
                      <input
                        type="number"
                        name="daily_rate"
                        value={formData.daily_rate}
                        onChange={handleInputChange}
                        min="0"
                        step="0.01"
                        className={`w-full px-4 py-2 rounded-lg border ${
                          validationErrors.daily_rate
                            ? 'border-red-500 focus:ring-red-500'
                            : 'border-gray-300 focus:ring-blue-500'
                        } focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white`}
                      />
                      {validationErrors.daily_rate && (
                        <p className="mt-1 text-sm text-red-600">
                          {validationErrors.daily_rate}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {selectedLang === 'ar' ? 'رقم الهاتف' : 'Phone Number'}
                      </label>
                      <input
                        type="tel"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-2 rounded-lg border ${
                          validationErrors.phone
                            ? 'border-red-500 focus:ring-red-500'
                            : 'border-gray-300 focus:ring-blue-500'
                        } focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white`}
                      />
                      {validationErrors.phone && (
                        <p className="mt-1 text-sm text-red-600">
                          {validationErrors.phone}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {selectedLang === 'ar' ? 'الجنسية' : 'Nationality'}
                      </label>
                      <input
                        type="text"
                        name="nationality"
                        value={formData.nationality}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-2 rounded-lg border ${
                          validationErrors.nationality
                            ? 'border-red-500 focus:ring-red-500'
                            : 'border-gray-300 focus:ring-blue-500'
                        } focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white`}
                      />
                      {validationErrors.nationality && (
                        <p className="mt-1 text-sm text-red-600">
                          {validationErrors.nationality}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {selectedLang === 'ar' ? 'رقم الهوية' : 'ID Number'}
                      </label>
                      <input
                        type="text"
                        name="id_number"
                        value={formData.id_number}
                        onChange={handleInputChange}
                        className={`w-full px-4 py-2 rounded-lg border ${
                          validationErrors.id_number
                            ? 'border-red-500 focus:ring-red-500'
                            : 'border-gray-300 focus:ring-blue-500'
                        } focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white`}
                      />
                      {validationErrors.id_number && (
                        <p className="mt-1 text-sm text-red-600">
                          {validationErrors.id_number}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        {selectedLang === 'ar' ? 'الحالة' : 'Status'}
                      </label>
                      <select
                        name="status"
                        value={formData.status}
                        onChange={handleInputChange}
                        className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      >
                        <option value="active">
                          {selectedLang === 'ar' ? 'نشط' : 'Active'}
                        </option>
                        <option value="inactive">
                          {selectedLang === 'ar' ? 'غير نشط' : 'Inactive'}
                        </option>
                        <option value="on_leave">
                          {selectedLang === 'ar' ? 'في إجازة' : 'On Leave'}
                        </option>
                      </select>
                    </div>
                  </div>
                </AnimatedEffects>

                <AnimatedEffects effect="slide" direction="up" delay={300}>
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      {selectedLang === 'ar' ? 'ملاحظات' : 'Notes'}
                    </label>
                    <textarea
                      name="notes"
                      value={formData.notes}
                      onChange={handleInputChange}
                      className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                      rows="4"
                    />
                  </div>
                </AnimatedEffects>

                <AnimatedEffects effect="fade" delay={400}>
                  {error && (
                    <div className="p-4 bg-red-100 text-red-700 rounded-lg flex items-center">
                      <i className="fas fa-exclamation-circle ml-2"></i>
                      {error}
                    </div>
                  )}

                  {success && (
                    <div className="p-4 bg-green-100 text-green-700 rounded-lg flex items-center">
                      <i className="fas fa-check-circle ml-2"></i>
                      {selectedLang === 'ar'
                        ? 'تمت إضافة العامل بنجاح'
                        : 'Worker added successfully'}
                    </div>
                  )}

                  <div className="flex justify-end space-x-4 rtl:space-x-reverse">
                    <button
                      type="button"
                      onClick={() => window.history.back()}
                      className="px-6 py-2 rounded-lg border border-gray-300 text-gray-700 hover:bg-gray-50 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700 transition-colors"
                    >
                      {selectedLang === 'ar' ? 'رجوع' : 'Back'}
                    </button>
                    <button
                      type="submit"
                      disabled={loading}
                      className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-blue-400 transition-colors flex items-center"
                    >
                      {loading && (
                        <i className="fas fa-circle-notch fa-spin ml-2"></i>
                      )}
                      {loading
                        ? selectedLang === 'ar'
                          ? 'جاري الإضافة...'
                          : 'Adding...'
                        : selectedLang === 'ar'
                          ? 'إضافة العامل'
                          : 'Add Worker'}
                    </button>
                  </div>
                </AnimatedEffects>
              </form>
            </div>
          </AnimatedEffects>
        </div>
      </SharedLayout>

      <style jsx global>{`
        .form-appear {
          opacity: 0;
          transform: translateY(20px);
          animation: formAppear 0.5s ease forwards;
        }

        @keyframes formAppear {
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .input-focus {
          transition: all 0.3s ease;
        }

        .input-focus:focus {
          transform: scale(1.02);
        }

        .button-hover {
          transition: all 0.3s ease;
        }

        .button-hover:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
      `}</style>
    </AuthCheck>
  );
}

export default MainComponent;
