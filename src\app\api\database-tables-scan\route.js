import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function POST(request) {
  try {

    const pool = await getConnection();
    const scanResults = {
      databaseInfo: null,
      allTables: [],
      tableDetails: {},
      fieldConsistency: {},
      summary: {},
      errors: []
    };

    // 1. معلومات قاعدة البيانات

    try {
      const dbInfo = await pool.request().query(`
        SELECT 
          @@VERSION as version, 
          DB_NAME() as database,
          @@SERVERNAME as server
      `);
      
      scanResults.databaseInfo = {
        version: dbInfo.recordset[0].version,
        database: dbInfo.recordset[0].database,
        server: dbInfo.recordset[0].server,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      scanResults.errors.push('خطأ في جمع معلومات قاعدة البيانات: ' + error.message);
    }

    // 2. فحص جميع الجداول

    try {
      const allTablesQuery = await pool.request().query(`
        SELECT 
          TABLE_NAME,
          TABLE_TYPE,
          TABLE_SCHEMA
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
      `);

      scanResults.allTables = allTablesQuery.recordset.map(table => table.TABLE_NAME);

    } catch (error) {
      scanResults.errors.push('خطأ في فحص الجداول: ' + error.message);
    }

    // 3. فحص تفاصيل كل جدول

    for (const tableName of scanResults.allTables) {
      try {
        // فحص أعمدة الجدول
        const columnsQuery = await pool.request().query(`
          SELECT 
            COLUMN_NAME,
            DATA_TYPE,
            IS_NULLABLE,
            COLUMN_DEFAULT,
            CHARACTER_MAXIMUM_LENGTH
          FROM INFORMATION_SCHEMA.COLUMNS 
          WHERE TABLE_NAME = '${tableName}'
          ORDER BY ORDINAL_POSITION
        `);

        // عدد السجلات
        const countQuery = await pool.request().query(`
          SELECT COUNT(*) as recordCount FROM [${tableName}]
        `);

        scanResults.tableDetails[tableName] = {
          columns: columnsQuery.recordset,
          recordCount: countQuery.recordset[0].recordCount,
          status: 'success'
        };

      } catch (error) {
        scanResults.tableDetails[tableName] = {
          columns: [],
          recordCount: 0,
          status: 'error',
          error: error.message
        };

      }
    }

    // 4. تحليل تطابق الحقول

    // فحص حقول الموظفين
    const employeeRelatedTables = scanResults.allTables.filter(table => 
      table.toLowerCase().includes('employee') || 
      table.toLowerCase().includes('beneficiar') ||
      table === 'Employees'
    );

    scanResults.fieldConsistency.employeeFields = {};
    
    for (const table of employeeRelatedTables) {
      if (scanResults.tableDetails[table]?.columns) {
        const columns = scanResults.tableDetails[table].columns.map(col => col.COLUMN_NAME);
        
        scanResults.fieldConsistency.employeeFields[table] = {
          hasEmployeeID: columns.includes('EmployeeID'),
          hasEmployeeCode: columns.includes('EmployeeCode'),
          hasFullName: columns.includes('FullName'),
          hasEmployeeName: columns.includes('EmployeeName'),
          allColumns: columns
        };
      }
    }

    // 5. إحصائيات عامة
    const totalTables = scanResults.allTables.length;
    const successfulTables = Object.values(scanResults.tableDetails).filter(t => t.status === 'success').length;
    const totalRecords = Object.values(scanResults.tableDetails)
      .filter(t => t.status === 'success')
      .reduce((sum, t) => sum + t.recordCount, 0);

    scanResults.summary = {
      totalTables,
      successfulTables,
      failedTables: totalTables - successfulTables,
      totalRecords,
      scanTime: new Date().toISOString()
    };

    return NextResponse.json({
      success: true,
      data: scanResults,
      message: `تم فحص ${totalTables} جدول بنجاح`
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'حدث خطأ في فحص قاعدة البيانات',
      error: error.message
    }, { status: 500 });
  }
}

export async function GET(request) {
  try {
    return NextResponse.json({
      success: true,
      message: 'API فحص جداول قاعدة البيانات',
      usage: 'POST /api/database-tables-scan',
      description: 'فحص شامل لجميع الجداول في قاعدة البيانات'
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
