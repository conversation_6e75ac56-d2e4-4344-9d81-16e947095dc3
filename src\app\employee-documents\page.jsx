'use client';
import React from 'react';

function MainComponent() {
  const [selectedLang, setSelectedLang] = useState('ar');
  const [employeeCode, setEmployeeCode] = useState('');
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [selectedDocument, setSelectedDocument] = useState(null);

  const dir = selectedLang === 'ar' ? 'rtl' : 'ltr';

  const searchDocuments = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await fetch(`/api/data-service`, {
        method: 'POST',
        body: JSON.stringify({
          table: 'employee_documents',
          action: 'search',
          employee_code: employeeCode,
        }),
      });

      if (!response.ok) {
        throw new Error(
          selectedLang === 'ar'
            ? 'حدث خطأ أثناء البحث عن المستندات'
            : 'Error searching for documents'
        );
      }

      const data = await response.json();
      setDocuments(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const documentTypes = {
    personal_photo: { ar: 'صورة شخصية', en: 'Personal Photo' },
    id_card: { ar: 'بطاقة الهوية', en: 'ID Card' },
    passport: { ar: 'جواز السفر', en: 'Passport' },
    contract: { ar: 'عقد العمل', en: 'Work Contract' },
    certificates: { ar: 'الشهادات', en: 'Certificates' },
    other: { ar: 'مستندات أخرى', en: 'Other Documents' },
  };

  return (
    <div
      dir={dir}
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <a
            href="/employees"
            className="flex items-center text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
          >
            <i
              className={`fas fa-arrow-${
                selectedLang === 'ar' ? 'left' : 'right'
              } ml-2`}
            ></i>
            {selectedLang === 'ar' ? 'عودة' : 'Back'}
          </a>
          <button
            onClick={() => setSelectedLang(selectedLang === 'ar' ? 'en' : 'ar')}
            className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
          >
            {selectedLang === 'ar' ? 'English' : 'العربية'}
          </button>
        </div>

        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          {selectedLang === 'ar'
            ? 'أرشيف مستندات الموظفين'
            : 'Employee Documents Archive'}
        </h1>

        <form onSubmit={searchDocuments} className="mb-8">
          <div className="flex gap-4">
            <input
              type="text"
              value={employeeCode}
              onChange={(e) => setEmployeeCode(e.target.value)}
              placeholder={
                selectedLang === 'ar'
                  ? 'أدخل كود الموظف'
                  : 'Enter Employee Code'
              }
              className="flex-1 p-2 border border-gray-300 rounded-md"
              required
            />
            <button
              type="submit"
              disabled={loading}
              className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors disabled:bg-blue-300"
            >
              {loading
                ? selectedLang === 'ar'
                  ? 'جاري البحث...'
                  : 'Searching...'
                : selectedLang === 'ar'
                  ? 'بحث'
                  : 'Search'}
            </button>
          </div>
        </form>

        {error && (
          <div className="p-4 mb-6 bg-red-100 text-red-700 rounded-md">
            {error}
          </div>
        )}

        {documents.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {documents.map((doc, index) => (
              <div
                key={index}
                className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 hover:shadow-lg transition-shadow"
              >
                <div className="flex items-center mb-4">
                  <i className="fas fa-file-pdf text-2xl text-red-600 mr-3"></i>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white">
                      {documentTypes[doc.type]?.[selectedLang] || doc.type}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {new Date(doc.upload_date).toLocaleDateString(
                        selectedLang === 'ar' ? 'ar-SA' : 'en-US'
                      )}
                    </p>
                  </div>
                </div>

                <div className="flex gap-2">
                  <button
                    onClick={() => setSelectedDocument(doc)}
                    className="flex-1 px-3 py-1 text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
                  >
                    <i className="fas fa-eye mr-2"></i>
                    {selectedLang === 'ar' ? 'معاينة' : 'Preview'}
                  </button>
                  <a
                    href={doc.file_url}
                    download
                    className="flex-1 px-3 py-1 text-sm bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors text-center"
                  >
                    <i className="fas fa-download mr-2"></i>
                    {selectedLang === 'ar' ? 'تحميل' : 'Download'}
                  </a>
                </div>
              </div>
            ))}
          </div>
        )}

        {selectedDocument && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-auto">
              <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                <h3 className="font-semibold text-lg">
                  {documentTypes[selectedDocument.type]?.[selectedLang] ||
                    selectedDocument.type}
                </h3>
                <button
                  onClick={() => setSelectedDocument(null)}
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                >
                  <i className="fas fa-times"></i>
                </button>
              </div>
              <div className="p-4">
                <iframe
                  src={selectedDocument.file_url}
                  className="w-full h-[70vh]"
                  title={
                    documentTypes[selectedDocument.type]?.[selectedLang] ||
                    selectedDocument.type
                  }
                />
              </div>
            </div>
          </div>
        )}

        {documents.length === 0 && employeeCode && !loading && (
          <div className="text-center py-12 text-gray-600 dark:text-gray-400">
            <i className="fas fa-folder-open text-4xl mb-4"></i>
            <p>
              {selectedLang === 'ar'
                ? 'لا توجد مستندات متاحة لهذا الموظف'
                : 'No documents available for this employee'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

export default MainComponent;
