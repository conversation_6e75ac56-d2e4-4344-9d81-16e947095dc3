import { NextResponse } from 'next/server';
import { getConnection } from '@/utils/db';

export async function GET(request) {
  try {

    const pool = await getConnection();
    
    // 1. إحصائيات الموظفين الصحيحة
    const employeeStatsResult = await pool.request().query(`
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN CurrentStatus = N'ساري' OR CurrentStatus = N'نشط' OR CurrentStatus IS NULL THEN 1 END) as active,
        COUNT(CASE WHEN CurrentStatus = N'منقول' THEN 1 END) as transferred,
        COUNT(CASE WHEN CurrentStatus = N'مستقيل' THEN 1 END) as resigned,
        COUNT(CASE WHEN CompanyHousing = N'نعم' THEN 1 END) as resident,
        COUNT(CASE WHEN CompanyHousing = N'لا' OR CompanyHousing IS NULL THEN 1 END) as nonResident,
        -- إحصائيات التأمين المفصلة
        COUNT(CASE WHEN SocialInsurance IN (N'مؤمن', N'1', N'نعم', N'Yes') AND MedicalInsurance IN (N'مؤمن', N'1', N'نعم', N'Yes') THEN 1 END) as bothInsured,
        COUNT(CASE WHEN SocialInsurance IN (N'مؤمن', N'1', N'نعم', N'Yes') THEN 1 END) as socialInsured,
        COUNT(CASE WHEN MedicalInsurance IN (N'مؤمن', N'1', N'نعم', N'Yes') AND (SocialInsurance NOT IN (N'مؤمن', N'1', N'نعم', N'Yes') OR SocialInsurance IS NULL) THEN 1 END) as medicalOnly,
        COUNT(CASE WHEN (SocialInsurance NOT IN (N'مؤمن', N'1', N'نعم', N'Yes') OR SocialInsurance IS NULL) AND (MedicalInsurance NOT IN (N'مؤمن', N'1', N'نعم', N'Yes') OR MedicalInsurance IS NULL) THEN 1 END) as notInsured
      FROM Employees
    `);
    
    const employeeStats = employeeStatsResult.recordset[0];

    // 2. إحصائيات الشقق الصحيحة
    const apartmentStatsResult = await pool.request().query(`
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN IsActive = 1 THEN 1 END) as active,
        ISNULL(SUM(CASE WHEN IsActive = 1 THEN RentAmount ELSE 0 END), 0) as totalRent,
        -- عدد الشقق المشغولة (التي لها مستفيدين نشطين)
        (SELECT COUNT(DISTINCT a.ID) 
         FROM Apartments a 
         INNER JOIN ApartmentBeneficiaries ab ON a.ID = ab.ApartmentID 
         WHERE a.IsActive = 1 AND ab.IsActive = 1) as occupied,
        -- عدد الشقق الفارغة (النشطة بدون مستفيدين)
        (SELECT COUNT(*) 
         FROM Apartments a 
         WHERE a.IsActive = 1 
         AND NOT EXISTS (SELECT 1 FROM ApartmentBeneficiaries ab WHERE ab.ApartmentID = a.ID AND ab.IsActive = 1)) as vacant
      FROM Apartments
    `);
    
    const apartmentStats = apartmentStatsResult.recordset[0];

    // 3. إحصائيات السيارات
    let carStats = { total: 0, active: 0, totalRent: 0 };
    try {
      const carStatsResult = await pool.request().query(`
        SELECT
          COUNT(*) as total,
          COUNT(CASE WHEN IsActive = 1 THEN 1 END) as active,
          ISNULL(SUM(CASE WHEN IsActive = 1 THEN RentAmount ELSE 0 END), 0) as totalRent
        FROM Cars
      `);
      carStats = carStatsResult.recordset[0];

    } catch (error) {

    }
    
    // 4. إحصائيات النقل والاستقالة
    let transferStats = { total: 0, thisYear: 0 };
    try {
      const transferStatsResult = await pool.request().query(`
        SELECT
          COUNT(*) as total,
          COUNT(CASE WHEN YEAR(TransferDate) = YEAR(GETDATE()) THEN 1 END) as thisYear
        FROM EmployeeTransfers
      `);
      transferStats = transferStatsResult.recordset[0];

    } catch (error) {

    }
    
    let resignationStats = { total: 0, thisYear: 0 };
    try {
      const resignationStatsResult = await pool.request().query(`
        SELECT
          COUNT(*) as total,
          COUNT(CASE WHEN YEAR(ResignationDate) = YEAR(GETDATE()) THEN 1 END) as thisYear
        FROM EmployeeResignations
      `);
      resignationStats = resignationStatsResult.recordset[0];

    } catch (error) {

    }
    
    // 5. إحصائيات مستفيدي الشقق
    const beneficiariesStatsResult = await pool.request().query(`
      SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN IsActive = 1 THEN 1 END) as active,
        COUNT(DISTINCT EmployeeCode) as uniqueEmployees,
        COUNT(DISTINCT ApartmentID) as apartmentsWithBeneficiaries
      FROM ApartmentBeneficiaries
    `);
    
    const beneficiariesStats = beneficiariesStatsResult.recordset[0];

    // 6. إحصائيات الأقسام
    const departmentStatsResult = await pool.request().query(`
      SELECT
        Department as DepartmentName,
        COUNT(*) as employeeCount
      FROM Employees
      WHERE (CurrentStatus = N'ساري' OR CurrentStatus = N'نشط' OR CurrentStatus IS NULL)
        AND Department IS NOT NULL
        AND Department != ''
      GROUP BY Department
      ORDER BY employeeCount DESC
    `);
    
    const departmentStats = departmentStatsResult.recordset;

    // تجميع الإحصائيات النهائية
    const finalStats = {
      employees: {
        total: employeeStats.total || 0,
        active: employeeStats.active || 0,
        transferred: employeeStats.transferred || 0,
        resigned: employeeStats.resigned || 0,
        resident: employeeStats.resident || 0,
        nonResident: employeeStats.nonResident || 0
      },
      insurance: {
        bothInsured: employeeStats.bothInsured || 0,
        socialInsured: employeeStats.socialInsured || 0,
        medicalOnly: employeeStats.medicalOnly || 0,
        notInsured: employeeStats.notInsured || 0
      },
      apartments: {
        total: apartmentStats.total || 0,
        active: apartmentStats.active || 0,
        occupied: apartmentStats.occupied || 0,
        vacant: apartmentStats.vacant || 0,
        totalRent: apartmentStats.totalRent || 0
      },
      cars: {
        total: carStats.total || 0,
        active: carStats.active || 0,
        totalRent: carStats.totalRent || 0
      },
      transfers: {
        total: transferStats.total || 0,
        thisYear: transferStats.thisYear || 0
      },
      resignations: {
        total: resignationStats.total || 0,
        thisYear: resignationStats.thisYear || 0
      },
      apartmentBeneficiaries: {
        total: beneficiariesStats.total || 0,
        active: beneficiariesStats.active || 0,
        uniqueEmployees: beneficiariesStats.uniqueEmployees || 0,
        apartmentsWithBeneficiaries: beneficiariesStats.apartmentsWithBeneficiaries || 0
      },
      departments: departmentStats || [],
      lastUpdated: new Date().toISOString()
    };

    console.log(`- المؤمن عليهم (الاثنين): ${finalStats.insurance.bothInsured}`);

    return NextResponse.json({
      success: true,
      data: finalStats,
      message: 'تم جلب الإحصائيات الصحيحة بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message,
      details: {
        name: error.name,
        code: error.code,
        state: error.state
      }
    }, { status: 500 });
  }
}
