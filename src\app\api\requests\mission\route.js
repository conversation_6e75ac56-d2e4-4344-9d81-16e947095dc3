import { NextResponse } from 'next/server';
import sql from 'mssql';

// إعدادات قاعدة البيانات
const config = {
  server: process.env.DB_SERVER || 'localhost',
  database: process.env.DB_NAME || 'EmployeeDB',
  user: process.env.DB_USER || 'sa',
  password: process.env.DB_PASSWORD || 'YourPassword123',
  options: {
    encrypt: false,
    trustServerCertificate: true,
    enableArithAbort: true,
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
};

export async function POST(request) {
  let pool;
  
  try {

    // الحصول على البيانات من الطلب
    const requestData = await request.json();

    // التحقق من الحقول المطلوبة
    const requiredFields = [
      'employeeName', 'missionType', 'destination', 'startDate', 'endDate', 'purpose'
    ];

    const missingFields = requiredFields.filter(field => 
      !requestData[field] || requestData[field].trim() === ''
    );

    if (missingFields.length > 0) {
      return NextResponse.json({
        success: false,
        message: `الحقول التالية مطلوبة: ${missingFields.join(', ')}`
      }, { status: 400 });
    }

    // التحقق من صحة التواريخ
    const startDate = new Date(requestData.startDate);
    const endDate = new Date(requestData.endDate);
    
    if (endDate < startDate) {
      return NextResponse.json({
        success: false,
        message: 'تاريخ النهاية يجب أن يكون بعد تاريخ البداية'
      }, { status: 400 });
    }

    // الاتصال بقاعدة البيانات
    pool = await sql.connect(config);

    // إنشاء جدول طلبات المأمورية إذا لم يكن موجوداً
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MissionRequests' AND xtype='U')
      CREATE TABLE MissionRequests (
        RequestID int IDENTITY(1,1) PRIMARY KEY,
        EmployeeName nvarchar(255) NOT NULL,
        EmployeeID nvarchar(50),
        Department nvarchar(255),
        JobTitle nvarchar(255),
        MissionType nvarchar(100) NOT NULL,
        Destination nvarchar(255) NOT NULL,
        StartDate date NOT NULL,
        EndDate date NOT NULL,
        StartTime time NULL,
        EndTime time NULL,
        TotalDays int,
        Purpose nvarchar(max) NOT NULL,
        TransportMethod nvarchar(100),
        AccommodationNeeded bit DEFAULT 0,
        AdvancePayment decimal(10,2),
        ExpectedExpenses decimal(10,2),
        AccompaniedBy nvarchar(255),
        EmergencyContact nvarchar(255),
        EmergencyPhone nvarchar(50),
        Notes nvarchar(max),
        Status nvarchar(50) DEFAULT 'pending',
        SubmittedAt datetime DEFAULT GETDATE(),
        ReviewedAt datetime NULL,
        ReviewedBy nvarchar(255) NULL,
        ReviewComments nvarchar(max) NULL,
        CreatedAt datetime DEFAULT GETDATE(),
        UpdatedAt datetime DEFAULT GETDATE()
      )
    `);

    // إنشاء طلب الإدراج
    const insertRequest = pool.request();
    
    // إضافة المعاملات
    insertRequest.input('EmployeeName', sql.NVarChar, requestData.employeeName);
    insertRequest.input('EmployeeID', sql.NVarChar, requestData.employeeId || null);
    insertRequest.input('Department', sql.NVarChar, requestData.department || null);
    insertRequest.input('JobTitle', sql.NVarChar, requestData.jobTitle || null);
    insertRequest.input('MissionType', sql.NVarChar, requestData.missionType);
    insertRequest.input('Destination', sql.NVarChar, requestData.destination);
    insertRequest.input('StartDate', sql.Date, startDate);
    insertRequest.input('EndDate', sql.Date, endDate);
    insertRequest.input('StartTime', sql.Time, requestData.startTime || null);
    insertRequest.input('EndTime', sql.Time, requestData.endTime || null);
    insertRequest.input('TotalDays', sql.Int, parseInt(requestData.totalDays) || null);
    insertRequest.input('Purpose', sql.NVarChar, requestData.purpose);
    insertRequest.input('TransportMethod', sql.NVarChar, requestData.transportMethod || null);
    insertRequest.input('AccommodationNeeded', sql.Bit, requestData.accommodationNeeded || false);
    insertRequest.input('AdvancePayment', sql.Decimal(10,2), parseFloat(requestData.advancePayment) || null);
    insertRequest.input('ExpectedExpenses', sql.Decimal(10,2), parseFloat(requestData.expectedExpenses) || null);
    insertRequest.input('AccompaniedBy', sql.NVarChar, requestData.accompaniedBy || null);
    insertRequest.input('EmergencyContact', sql.NVarChar, requestData.emergencyContact || null);
    insertRequest.input('EmergencyPhone', sql.NVarChar, requestData.emergencyPhone || null);
    insertRequest.input('Notes', sql.NVarChar, requestData.notes || null);

    // استعلام الإدراج
    const insertQuery = `
      INSERT INTO MissionRequests (
        EmployeeName, EmployeeID, Department, JobTitle, MissionType,
        Destination, StartDate, EndDate, StartTime, EndTime, TotalDays,
        Purpose, TransportMethod, AccommodationNeeded, AdvancePayment,
        ExpectedExpenses, AccompaniedBy, EmergencyContact, EmergencyPhone, Notes
      ) VALUES (
        @EmployeeName, @EmployeeID, @Department, @JobTitle, @MissionType,
        @Destination, @StartDate, @EndDate, @StartTime, @EndTime, @TotalDays,
        @Purpose, @TransportMethod, @AccommodationNeeded, @AdvancePayment,
        @ExpectedExpenses, @AccompaniedBy, @EmergencyContact, @EmergencyPhone, @Notes
      );
      SELECT SCOPE_IDENTITY() as RequestID;
    `;

    const result = await insertRequest.query(insertQuery);
    const newRequestId = result.recordset[0].RequestID;

    return NextResponse.json({
      success: true,
      message: 'تم تقديم طلب المأمورية بنجاح',
      requestId: newRequestId,
      data: {
        requestId: newRequestId,
        employeeName: requestData.employeeName,
        missionType: requestData.missionType,
        destination: requestData.destination,
        startDate: requestData.startDate,
        endDate: requestData.endDate,
        totalDays: requestData.totalDays,
        status: 'pending'
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'خطأ في معالجة طلب المأمورية',
      error: error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      await pool.close();

    }
  }
}

export async function GET(request) {
  let pool;
  
  try {

    // الاتصال بقاعدة البيانات
    pool = await sql.connect(config);

    // جلب جميع طلبات المأمورية
    const result = await pool.request().query(`
      SELECT 
        RequestID,
        EmployeeName,
        EmployeeID,
        Department,
        JobTitle,
        MissionType,
        Destination,
        StartDate,
        EndDate,
        StartTime,
        EndTime,
        TotalDays,
        Purpose,
        TransportMethod,
        AccommodationNeeded,
        AdvancePayment,
        ExpectedExpenses,
        AccompaniedBy,
        EmergencyContact,
        EmergencyPhone,
        Notes,
        Status,
        SubmittedAt,
        ReviewedAt,
        ReviewedBy,
        ReviewComments
      FROM MissionRequests
      ORDER BY SubmittedAt DESC
    `);

    return NextResponse.json({
      success: true,
      requests: result.recordset
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'خطأ في جلب طلبات المأمورية',
      error: error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      await pool.close();

    }
  }
}
