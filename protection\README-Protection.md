# 🔐 نظام الحماية المتقدم
## حماية شاملة لنظام إدارة التكاليف

---

## 📋 نظرة عامة

هذا النظام يوفر حماية شاملة متعددة المستويات لمشروعك، يشمل:

### 🛡️ الحماية التقنية:
- **نظام ترخيص متقدم** مربوط بالجهاز
- **تشفير وتشويش الكود** المصدري
- **حماية من أدوات التصحيح** والهندسة العكسية
- **مراقبة الاستخدام** والتحقق الدوري

### ⚖️ الحماية القانونية:
- **قوالب العقود** الجاهزة
- **اتفاقيات عدم الإفشاء** (NDA)
- **شروط الترخيص** الواضحة

---

## 📁 هيكل الملفات

```
protection/
├── license-system.js          # نظام الترخيص الأساسي
├── code-obfuscator.js         # تشويش وتشفير الكود
├── license-generator.js       # مولد التراخيص التفاعلي
├── license-middleware.js      # middleware للتحقق من الترخيص
├── build-protected.js         # بناء النسخ المحمية
├── run-protection.bat         # تشغيل نظام الحماية
├── legal-templates/           # القوالب القانونية
│   ├── NDA-template.md       # قالب اتفاقية عدم الإفشاء
│   ├── license-agreement.md  # قالب اتفاقية الترخيص
│   └── terms-of-use.md       # قالب شروط الاستخدام
└── README-Protection.md       # هذا الملف
```

---

## 🚀 كيفية الاستخدام

### 1. إنشاء ترخيص جديد

```bash
# الطريقة السهلة
double-click على run-protection.bat

# أو من سطر الأوامر
cd protection
node license-generator.js
```

### 2. إنشاء نسخة محمية للبيع

```bash
# تشغيل مولد التراخيص واختيار "إنشاء نسخة محمية كاملة"
node license-generator.js
```

### 3. التحقق من ترخيص موجود

```javascript
const LicenseSystem = require('./protection/license-system');
const licenseSystem = new LicenseSystem();
const validation = licenseSystem.validateLicense();

if (validation.valid) {
    console.log('الترخيص صالح');
} else {
    console.log('خطأ:', validation.error);
}
```

---

## 🔧 التكوين والإعداد

### 1. تفعيل نظام الحماية في المشروع

انسخ `license-middleware.js` إلى مجلد `src` واستبدل محتوى `src/middleware.js`:

```javascript
// src/middleware.js
export { middleware, config } from './protection/license-middleware';
```

### 2. تخصيص مفاتيح التشفير

في `license-system.js`:
```javascript
this.secretKey = 'YOUR_UNIQUE_SECRET_KEY_HERE'; // غيّر هذا المفتاح
```

### 3. تخصيص معلومات الدعم

في `license-middleware.js` عدّل معلومات الاتصال:
```javascript
<p>📧 البريد الإلكتروني: <EMAIL></p>
<p>📞 الهاتف: +20 ************</p>
```

---

## 📊 أنواع التراخيص المدعومة

### 1. ترخيص دائم
- لا ينتهي أبداً
- مناسب للعملاء المميزين

### 2. ترخيص سنوي
- صالح لمدة سنة
- يمكن تجديده

### 3. ترخيص نصف سنوي
- صالح لمدة 6 أشهر
- مناسب للمشاريع المتوسطة

### 4. ترخيص تجريبي
- صالح لمدة 30 يوم
- للتقييم والاختبار

---

## 🎯 الميزات المدعومة

### 1. جميع الميزات (`all`)
- وصول كامل للنظام
- جميع الوحدات والصفحات

### 2. إدارة التكاليف (`costs-management`)
- صفحات التكاليف فقط
- APIs التكاليف

### 3. طلبات الإصدار (`version-requests`)
- صفحة طلبات الإصدار فقط
- APIs طلبات الإصدار

### 4. ميزات مخصصة
- تحديد ميزات محددة حسب الحاجة

---

## 🔒 مستويات الحماية

### المستوى الأول: التحقق من الترخيص
- التحقق من صحة الترخيص
- التحقق من تاريخ الانتهاء
- التحقق من معرف الجهاز

### المستوى الثاني: تشفير الكود
- تشويش أسماء المتغيرات والدوال
- تشفير النصوص الثابتة
- ضغط وتعقيد الكود

### المستوى الثالث: مكافحة التصحيح
- اكتشاف أدوات المطور
- منع الهندسة العكسية
- حماية من النسخ

### المستوى الرابع: المراقبة
- تسجيل محاولات الوصول
- مراقبة الاستخدام غير المصرح
- تنبيهات الأمان

---

## 📋 خطوات البيع الآمن

### 1. التحضير
```bash
# إنشاء نسخة محمية
node license-generator.js
# اختيار "إنشاء نسخة محمية كاملة"
```

### 2. التسليم
- تسليم الحزمة المحمية فقط
- عدم تسليم الكود المصدري
- تقديم ملف الترخيص منفصل

### 3. التدريب والدعم
- تدريب العميل على الاستخدام
- تقديم الدعم الفني
- تحديثات دورية مدفوعة

---

## ⚠️ تحذيرات مهمة

### للمطور:
1. **احتفظ بنسخة من المفاتيح السرية**
2. **لا تشارك ملفات الحماية مع العملاء**
3. **استخدم عقود قانونية واضحة**
4. **راجع القوانين المحلية**

### للعميل:
1. **لا تحاول كسر الحماية**
2. **التزم بشروط الترخيص**
3. **لا تشارك النظام مع آخرين**
4. **تواصل مع الدعم عند المشاكل**

---

## 🆘 الدعم والمساعدة

### مشاكل شائعة:

**"ملف الترخيص غير موجود"**
- تأكد من وجود ملف `.license` في مجلد المشروع
- تواصل مع المطور للحصول على ترخيص صالح

**"الترخيص غير صالح لهذا الجهاز"**
- الترخيص مربوط بجهاز محدد
- تواصل مع المطور لنقل الترخيص

**"انتهت صلاحية الترخيص"**
- تواصل مع المطور لتجديد الترخيص
- أو شراء ترخيص جديد

---

## 📞 معلومات الاتصال

للدعم الفني والاستفسارات:
- 📧 البريد الإلكتروني: <EMAIL>
- 📞 الهاتف: +20 ************
- 🌐 الموقع: www.example.com

---

**© 2025 جميع الحقوق محفوظة**
