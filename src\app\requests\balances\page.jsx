'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/MainLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import {
  FiSearch,
  FiCalendar,
  FiUser,
  FiCheck,
  FiX,
  FiClock,
  FiFilter,
  FiRefreshCw,
  FiDownload,
  FiEye
} from 'react-icons/fi';

const LeaveBalancesPage = () => {
  const router = useRouter();
  const { isArabic } = useLanguage();
  const [loading, setLoading] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [employeeSearchResults, setEmployeeSearchResults] = useState([]);
  const [showEmployeeSearch, setShowEmployeeSearch] = useState(false);

  // بيانات البحث
  const [searchData, setSearchData] = useState({
    employeeId: '',
    employeeName: '',
    status: '',
    fromDate: '',
    toDate: '',
    leaveType: ''
  });

  // حالات الإجازات
  const statusOptions = [
    { value: '', label: isArabic ? 'جميع الحالات' : 'All Status' },
    { value: 'pending', label: isArabic ? 'قيد المراجعة' : 'Pending' },
    { value: 'approved', label: isArabic ? 'معتمدة' : 'Approved' },
    { value: 'rejected', label: isArabic ? 'مرفوضة' : 'Rejected' }
  ];

  // أنواع الإجازات
  const leaveTypeOptions = [
    { value: '', label: isArabic ? 'جميع الأنواع' : 'All Types' },
    { value: 'annual', label: isArabic ? 'إجازة سنوية' : 'Annual Leave' },
    { value: 'emergency', label: isArabic ? 'إجازة عارضة' : 'Emergency Leave' },
    { value: 'sick', label: isArabic ? 'إجازة مرضية' : 'Sick Leave' },
    { value: 'unpaid', label: isArabic ? 'إجازة بدون أجر' : 'Unpaid Leave' },
    { value: 'badal', label: isArabic ? 'إجازة بدل' : 'Compensation Leave' }
  ];

  // البحث عن الموظفين
  const searchEmployees = async (searchTerm) => {
    if (!searchTerm || searchTerm.length < 2) {
      setEmployeeSearchResults([]);
      setShowEmployeeSearch(false);
      return;
    }

    try {
      const response = await fetch('/api/employee-live-search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          searchTerm: searchTerm,
          limit: 10
        })
      });

      const result = await response.json();
      if (result.success) {
        const employees = result.data || [];
        const formattedEmployees = employees.map(emp => ({
          employeeId: emp.EmployeeID,
          fullName: emp.FullName,
          department: emp.Department,
          jobTitle: emp.JobTitle
        }));

        setEmployeeSearchResults(formattedEmployees);
        setShowEmployeeSearch(formattedEmployees.length > 0);
      }
    } catch (error) {

    }
  };

  // اختيار موظف
  const selectEmployee = (employee) => {
    setSearchData(prev => ({
      ...prev,
      employeeId: employee.employeeId,
      employeeName: employee.fullName || employee.EmployeeName
    }));
    setShowEmployeeSearch(false);
    setEmployeeSearchResults([]);
  };

  // تحديث بيانات البحث
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setSearchData(prev => ({ ...prev, [name]: value }));

    if (name === 'employeeId') {
      searchEmployees(value);
      if (value !== searchData.employeeId) {
        setSearchData(prev => ({ ...prev, employeeName: '' }));
      }
    }
  };

  // تنفيذ البحث
  const handleSearch = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/leave-balances/search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(searchData)
      });

      const result = await response.json();
      if (result.success) {
        setSearchResults(result.data || []);
      } else {
        alert(result.message || 'خطأ في البحث');
      }
    } catch (error) {

      alert('خطأ في الاتصال بالخادم');
    } finally {
      setLoading(false);
    }
  };

  // تحديث حالة الإجازة
  const updateLeaveStatus = async (leaveId, newStatus) => {
    try {
      const response = await fetch('/api/leave-balances/update-status', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          leaveId: leaveId,
          status: newStatus
        })
      });

      const result = await response.json();
      if (result.success) {
        // تحديث النتائج محلياً
        setSearchResults(prev => 
          prev.map(leave => 
            leave.id === leaveId 
              ? { ...leave, status: newStatus, updatedAt: new Date().toISOString() }
              : leave
          )
        );
        alert(isArabic ? 'تم تحديث حالة الإجازة بنجاح' : 'Leave status updated successfully');
      } else {
        alert(result.message || 'خطأ في تحديث الحالة');
      }
    } catch (error) {

      alert('خطأ في الاتصال بالخادم');
    }
  };

  // تنسيق التاريخ
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  // لون حالة الإجازة
  const getStatusColor = (status) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'rejected':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };

  // نص حالة الإجازة
  const getStatusText = (status) => {
    switch (status) {
      case 'approved':
        return isArabic ? 'معتمدة' : 'Approved';
      case 'rejected':
        return isArabic ? 'مرفوضة' : 'Rejected';
      case 'pending':
        return isArabic ? 'قيد المراجعة' : 'Pending';
      default:
        return status;
    }
  };

  return (
    <MainLayout>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto">
          {/* العنوان */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-200 mb-2">
              {isArabic ? 'رصيد الإجازات' : 'Leave Balances'}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {isArabic ? 'البحث في طلبات الإجازات وإدارة حالاتها' : 'Search leave requests and manage their status'}
            </p>
          </div>

          {/* نموذج البحث */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
            <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center gap-2">
              <FiSearch className="text-blue-600" />
              {isArabic ? 'البحث المتقدم' : 'Advanced Search'}
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* بحث الموظف */}
              <div className="relative">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {isArabic ? 'كود الموظف' : 'Employee ID'}
                </label>
                <input
                  type="text"
                  name="employeeId"
                  value={searchData.employeeId}
                  onChange={handleInputChange}
                  placeholder={isArabic ? 'ابحث بكود الموظف أو اتركه فارغاً للكل' : 'Search by employee ID or leave empty for all'}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
                />

                {/* نتائج البحث */}
                {showEmployeeSearch && employeeSearchResults.length > 0 && (
                  <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                    {employeeSearchResults.map((employee) => (
                      <div
                        key={employee.employeeId}
                        onClick={() => selectEmployee(employee)}
                        className="px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-200 dark:border-gray-600 last:border-b-0"
                      >
                        <div className="font-medium text-gray-900 dark:text-gray-100">
                          {employee.employeeId} - {employee.fullName}
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {employee.department} | {employee.jobTitle}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* اسم الموظف */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {isArabic ? 'اسم الموظف' : 'Employee Name'}
                </label>
                <input
                  type="text"
                  name="employeeName"
                  value={searchData.employeeName}
                  readOnly
                  placeholder={isArabic ? 'سيتم ملء الاسم تلقائياً' : 'Name will be filled automatically'}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-600 dark:text-gray-200"
                />
              </div>

              {/* حالة الإجازة */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {isArabic ? 'حالة الإجازة' : 'Leave Status'}
                </label>
                <select
                  name="status"
                  value={searchData.status}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
                >
                  {statusOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* نوع الإجازة */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {isArabic ? 'نوع الإجازة' : 'Leave Type'}
                </label>
                <select
                  name="leaveType"
                  value={searchData.leaveType}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
                >
                  {leaveTypeOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* من تاريخ */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {isArabic ? 'من تاريخ' : 'From Date'}
                </label>
                <input
                  type="text"
                  name="fromDate"
                  value={searchData.fromDate}
                  onChange={handleInputChange}
                  placeholder="dd/mm/yyyy"
                  pattern="\d{2}/\d{2}/\d{4}"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
                  style={{ direction: 'ltr' }}
                />
              </div>

              {/* إلى تاريخ */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {isArabic ? 'إلى تاريخ' : 'To Date'}
                </label>
                <input
                  type="text"
                  name="toDate"
                  value={searchData.toDate}
                  onChange={handleInputChange}
                  placeholder="dd/mm/yyyy"
                  pattern="\d{2}/\d{2}/\d{4}"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
                  style={{ direction: 'ltr' }}
                />
              </div>
            </div>

            {/* أزرار البحث */}
            <div className="flex gap-4 mt-6">
              <button
                onClick={handleSearch}
                disabled={loading}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <FiSearch />
                )}
                {loading ? (isArabic ? 'جاري البحث...' : 'Searching...') : (isArabic ? 'بحث' : 'Search')}
              </button>
              
              <button
                onClick={() => setSearchData({
                  employeeId: '',
                  employeeName: '',
                  status: '',
                  fromDate: '',
                  toDate: '',
                  leaveType: ''
                })}
                className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-2"
              >
                <FiRefreshCw />
                {isArabic ? 'مسح' : 'Clear'}
              </button>
            </div>
          </div>

          {/* نتائج البحث */}
          {searchResults.length > 0 && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
                    {isArabic ? 'نتائج البحث' : 'Search Results'}
                  </h2>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {isArabic ? `${searchResults.length} نتيجة` : `${searchResults.length} results`}
                    </span>
                    <button
                      onClick={() => {/* تصدير النتائج */}}
                      className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 transition-colors flex items-center gap-1"
                    >
                      <FiDownload className="text-xs" />
                      {isArabic ? 'تصدير' : 'Export'}
                    </button>
                  </div>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'كود الموظف' : 'Employee ID'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'اسم الموظف' : 'Employee Name'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'نوع الإجازة' : 'Leave Type'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'من تاريخ' : 'From Date'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'إلى تاريخ' : 'To Date'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'عدد الأيام' : 'Days'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'الحالة' : 'Status'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'الإجراءات' : 'Actions'}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {searchResults.map((leave, index) => (
                      <tr key={leave.id || index} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                          {leave.employeeId}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {leave.employeeName}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {leave.leaveType}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {formatDate(leave.startDate)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {formatDate(leave.endDate)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {leave.totalDays}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(leave.status)}`}>
                            {getStatusText(leave.status)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          {leave.status === 'pending' ? (
                            <div className="flex gap-2">
                              <button
                                onClick={() => updateLeaveStatus(leave.id, 'approved')}
                                className="bg-green-600 text-white px-3 py-1 rounded text-xs hover:bg-green-700 transition-colors flex items-center gap-1"
                                title={isArabic ? 'اعتماد' : 'Approve'}
                              >
                                <FiCheck className="text-xs" />
                                {isArabic ? 'اعتماد' : 'Approve'}
                              </button>
                              <button
                                onClick={() => updateLeaveStatus(leave.id, 'rejected')}
                                className="bg-red-600 text-white px-3 py-1 rounded text-xs hover:bg-red-700 transition-colors flex items-center gap-1"
                                title={isArabic ? 'رفض' : 'Reject'}
                              >
                                <FiX className="text-xs" />
                                {isArabic ? 'رفض' : 'Reject'}
                              </button>
                            </div>
                          ) : (
                            <button
                              onClick={() => {/* عرض تفاصيل الإجازة */}}
                              className="bg-blue-600 text-white px-3 py-1 rounded text-xs hover:bg-blue-700 transition-colors flex items-center gap-1"
                              title={isArabic ? 'عرض التفاصيل' : 'View Details'}
                            >
                              <FiEye className="text-xs" />
                              {isArabic ? 'عرض' : 'View'}
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* رسالة عدم وجود نتائج */}
          {searchResults.length === 0 && !loading && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8 text-center">
              <FiCalendar className="text-4xl text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400">
                {isArabic ? 'لا توجد نتائج للبحث. استخدم نموذج البحث أعلاه للعثور على طلبات الإجازات.' : 'No search results found. Use the search form above to find leave requests.'}
              </p>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
};

export default LeaveBalancesPage;
