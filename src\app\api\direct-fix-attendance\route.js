import { NextResponse } from 'next/server';
import { getConnection } from '@/lib/db';

export async function POST(request) {
  try {
    const { employeeCode } = await request.json();

    const pool = await getConnection();

    // الحصول على الشهر والسنة الحاليين
    const currentDate = new Date();
    const month = currentDate.getMonth() + 1;
    const year = currentDate.getFullYear();

    // الخطوة 1: جلب معلومات الموظف
    const employeeResult = await pool.request()
      .input('employeeCode', employeeCode)
      .query(`
        SELECT EmployeeCode, EmployeeName, Department, JobTitle
        FROM Employees 
        WHERE EmployeeCode = @employeeCode
      `);

    if (employeeResult.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: `لم يتم العثور على موظف بالكود: ${employeeCode}`
      }, { status: 404 });
    }

    const employee = employeeResult.recordset[0];

    // الخطوة 2: جلب جميع الإجازات المعتمدة للموظف في الشهر الحالي
    const leavesResult = await pool.request()
      .input('employeeCode', employeeCode)
      .input('month', month)
      .input('year', year)
      .query(`
        SELECT 
          LeaveType,
          StartDate,
          EndDate,
          DaysCount
        FROM LeaveRequests 
        WHERE EmployeeCode = @employeeCode
          AND Status = N'معتمد'
          AND (
            (MONTH(StartDate) = @month AND YEAR(StartDate) = @year) OR
            (MONTH(EndDate) = @month AND YEAR(EndDate) = @year) OR
            (StartDate <= DATEFROMPARTS(@year, @month, 1) AND EndDate >= EOMONTH(DATEFROMPARTS(@year, @month, 1)))
          )
      `);

    const leaves = leavesResult.recordset;

    // الخطوة 3: تحديث التمام اليومي لكل إجازة
    let updatedDays = 0;
    const leaveDetails = [];

    for (const leave of leaves) {
      const startDate = new Date(leave.StartDate);
      const endDate = new Date(leave.EndDate);
      
      let attendanceStatus = 'إجازة اعتيادية';
      if (leave.LeaveType === 'عارضة') {
        attendanceStatus = 'إجازة عارضة';
      } else if (leave.LeaveType === 'مرضية') {
        attendanceStatus = 'إجازة مرضية';
      }

      console.log(`🏖️ معالجة إجازة ${leave.LeaveType} من ${startDate.toLocaleDateString('ar-EG')} إلى ${endDate.toLocaleDateString('ar-EG')}`);

      let leaveDaysUpdated = 0;
      let currentLeaveDate = new Date(startDate);

      while (currentLeaveDate <= endDate) {
        // تجاهل أيام الجمعة والسبت
        if (currentLeaveDate.getDay() !== 5 && currentLeaveDate.getDay() !== 6) {
          const dateStr = currentLeaveDate.toISOString().split('T')[0];
          
          try {
            // محاولة تحديث السجل الموجود
            const updateResult = await pool.request()
              .input('employeeCode', employeeCode)
              .input('date', dateStr)
              .input('attendance', attendanceStatus)
              .input('notes', `إجازة ${leave.LeaveType} معتمدة`)
              .query(`
                UPDATE DailyAttendance 
                SET 
                  Attendance = @attendance,
                  Notes = @notes,
                  UpdatedAt = GETDATE()
                WHERE EmployeeCode = @employeeCode AND AttendanceDate = @date
              `);

            if (updateResult.rowsAffected[0] > 0) {
              updatedDays++;
              leaveDaysUpdated++;

            } else {
              // إنشاء سجل جديد إذا لم يكن موجوداً
              await pool.request()
                .input('employeeCode', employeeCode)
                .input('employeeName', employee.EmployeeName)
                .input('department', employee.Department || '')
                .input('jobTitle', employee.JobTitle || '')
                .input('date', dateStr)
                .input('attendance', attendanceStatus)
                .input('notes', `إجازة ${leave.LeaveType} معتمدة`)
                .query(`
                  INSERT INTO DailyAttendance (
                    EmployeeCode, EmployeeName, Department, JobTitle, 
                    AttendanceDate, Attendance, Notes, CreatedAt
                  ) VALUES (
                    @employeeCode, @employeeName, @department, @jobTitle,
                    @date, @attendance, @notes, GETDATE()
                  )
                `);
              updatedDays++;
              leaveDaysUpdated++;

            }
          } catch (dayError) {

          }
        }
        
        currentLeaveDate.setDate(currentLeaveDate.getDate() + 1);
      }

      leaveDetails.push({
        type: leave.LeaveType,
        startDate: startDate.toLocaleDateString('ar-EG'),
        endDate: endDate.toLocaleDateString('ar-EG'),
        daysCount: leave.DaysCount,
        updatedDays: leaveDaysUpdated
      });
    }

    // الخطوة 4: إعادة حساب الملخص الشهري
    const summaryResult = await pool.request()
      .input('employeeCode', employeeCode)
      .input('month', month)
      .input('year', year)
      .query(`
        SELECT
          COUNT(*) as TotalWorkingDays,
          SUM(CASE WHEN Attendance = N'حضور' THEN 1 ELSE 0 END) as TotalPresent,
          SUM(CASE WHEN Attendance = N'غياب' THEN 1 ELSE 0 END) as TotalAbsent,
          SUM(CASE WHEN Attendance LIKE N'%إجازة%' THEN 1 ELSE 0 END) as TotalLeaves,
          SUM(CASE WHEN Attendance = N'مأمورية' THEN 1 ELSE 0 END) as TotalMissions,
          SUM(CASE WHEN Attendance LIKE N'%اعتيادية%' THEN 1 ELSE 0 END) as AnnualLeaves,
          SUM(CASE WHEN Attendance LIKE N'%عارضة%' THEN 1 ELSE 0 END) as CasualLeaves,
          SUM(CASE WHEN Attendance LIKE N'%مرضية%' THEN 1 ELSE 0 END) as SickLeaves
        FROM DailyAttendance
        WHERE EmployeeCode = @employeeCode
          AND MONTH(AttendanceDate) = @month
          AND YEAR(AttendanceDate) = @year
      `);

    let finalSummary = null;

    if (summaryResult.recordset.length > 0) {
      const stats = summaryResult.recordset[0];
      const attendancePercentage = stats.TotalWorkingDays > 0
        ? (stats.TotalPresent / stats.TotalWorkingDays) * 100
        : 0;

      finalSummary = {
        totalWorkingDays: stats.TotalWorkingDays,
        totalPresent: stats.TotalPresent,
        totalAbsent: stats.TotalAbsent,
        totalLeaves: stats.TotalLeaves,
        totalMissions: stats.TotalMissions,
        annualLeaves: stats.AnnualLeaves,
        casualLeaves: stats.CasualLeaves,
        sickLeaves: stats.SickLeaves,
        attendancePercentage: attendancePercentage.toFixed(2)
      };

      console.log(`   - نسبة الحضور: ${attendancePercentage.toFixed(2)}%`);

      // الخطوة 5: تحديث جدول الملخص الشهري
      try {
        // حذف الملخص القديم
        await pool.request()
          .input('employeeCode', employeeCode)
          .input('month', month)
          .input('year', year)
          .query(`
            DELETE FROM MonthlyAttendanceSummary
            WHERE EmployeeCode = @employeeCode AND Month = @month AND Year = @year
          `);

        // إدراج الملخص الجديد
        await pool.request()
          .input('employeeCode', employeeCode)
          .input('employeeName', employee.EmployeeName)
          .input('department', employee.Department || '')
          .input('jobTitle', employee.JobTitle || '')
          .input('month', month)
          .input('year', year)
          .input('totalWorkingDays', stats.TotalWorkingDays)
          .input('totalPresent', stats.TotalPresent)
          .input('totalAbsent', stats.TotalAbsent)
          .input('totalLeaves', stats.TotalLeaves)
          .input('totalMissions', stats.TotalMissions || 0)
          .input('sickLeaves', stats.SickLeaves || 0)
          .input('attendancePercentage', attendancePercentage)
          .query(`
            INSERT INTO MonthlyAttendanceSummary (
              EmployeeCode, EmployeeName, Department, JobTitle, Month, Year,
              TotalWorkingDays, TotalPresent, TotalAbsent, TotalLeaves, TotalMissions,
              TotalSickLeave, TotalUnpaidLeave, TotalNightShifts, AttendancePercentage
            )
            VALUES (
              @employeeCode, @employeeName, @department, @jobTitle, @month, @year,
              @totalWorkingDays, @totalPresent, @totalAbsent, @totalLeaves, @totalMissions,
              @sickLeaves, 0, 0, @attendancePercentage
            )
          `);

      } catch (summaryError) {

      }
    }

    return NextResponse.json({
      success: true,
      message: `تم إصلاح التمام الشهري لـ ${employee.EmployeeName} بنجاح`,
      data: {
        employee: {
          code: employeeCode,
          name: employee.EmployeeName
        },
        month,
        year,
        updatedDays,
        leavesProcessed: leaves.length,
        leaveDetails,
        summary: finalSummary
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message,
      details: 'فشل في إصلاح التمام الشهري'
    }, { status: 500 });
  }
}
