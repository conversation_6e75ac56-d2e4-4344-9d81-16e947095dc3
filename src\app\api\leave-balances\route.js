import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function POST(request) {
  try {
    const body = await request.json();
    const { action } = body;

    const pool = await getConnection();

    switch (action) {
      case 'list':
        return await getLeaveBalances(pool, body);
      case 'update':
        return await updateLeaveBalance(pool, body);
      case 'reset':
        return await resetLeaveBalances(pool, body);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  }
}

// جلب جميع أرصدة الإجازات
async function getLeaveBalances(pool, data) {
  try {
    const { searchTerm } = data;
    
    let query = `
      SELECT 
        lb.*,
        COALESCE(used_regular.UsedDays, 0) as UsedRegular,
        COALESCE(used_casual.UsedDays, 0) as UsedCasual,
        (lb.RegularBalance - COALESCE(used_regular.UsedDays, 0)) as RemainingRegular,
        (lb.CasualBalance - COALESCE(used_casual.UsedDays, 0)) as RemainingCasual,
        last_leave.LastLeaveDate
      FROM LeaveBalances lb
      LEFT JOIN (
        SELECT EmployeeCode, SUM(DaysCount) as UsedDays
        FROM LeaveRequests 
        WHERE LeaveType = N'اعتيادية' AND Status = N'معتمد' 
        AND YEAR(StartDate) = YEAR(GETDATE())
        GROUP BY EmployeeCode
      ) used_regular ON lb.EmployeeCode = used_regular.EmployeeCode
      LEFT JOIN (
        SELECT EmployeeCode, SUM(DaysCount) as UsedDays
        FROM LeaveRequests 
        WHERE LeaveType = N'عارضة' AND Status = N'معتمد' 
        AND YEAR(StartDate) = YEAR(GETDATE())
        GROUP BY EmployeeCode
      ) used_casual ON lb.EmployeeCode = used_casual.EmployeeCode
      LEFT JOIN (
        SELECT EmployeeCode, MAX(EndDate) as LastLeaveDate
        FROM LeaveRequests 
        WHERE Status = N'معتمد'
        GROUP BY EmployeeCode
      ) last_leave ON lb.EmployeeCode = last_leave.EmployeeCode
      WHERE lb.Year = YEAR(GETDATE())
    `;

    const request = pool.request();

    if (searchTerm) {
      query += ` AND (
        lb.EmployeeCode LIKE @searchTerm OR 
        lb.EmployeeName LIKE @searchTerm OR 
        lb.JobTitle LIKE @searchTerm
      )`;
      request.input('searchTerm', sql.NVarChar, `%${searchTerm}%`);
    }

    query += ' ORDER BY lb.EmployeeName';

    const result = await request.query(query);

    return NextResponse.json({
      success: true,
      data: result.recordset
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب أرصدة الإجازات'
    }, { status: 500 });
  }
}

// تحديث رصيد إجازات موظف
async function updateLeaveBalance(pool, data) {
  try {
    const { employeeCode, regularBalance, casualBalance } = data;
    
    await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .input('regularBalance', sql.Int, regularBalance)
      .input('casualBalance', sql.Int, casualBalance)
      .query(`
        UPDATE LeaveBalances
        SET
          RegularBalance = @regularBalance,
          CasualBalance = @casualBalance
        WHERE EmployeeCode = @employeeCode AND Year = YEAR(GETDATE())
      `);

    return NextResponse.json({
      success: true,
      message: 'تم تحديث رصيد الإجازات بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث رصيد الإجازات'
    }, { status: 500 });
  }
}

// إعادة تعيين أرصدة الإجازات للسنة الجديدة
async function resetLeaveBalances(pool, data) {
  try {
    const { year } = data;
    
    // تحديث السنة وإعادة تعيين الأرصدة
    await pool.request()
      .input('year', sql.Int, year || new Date().getFullYear())
      .query(`
        UPDATE LeaveBalances 
        SET 
          Year = @year,
          RegularBalance = 15,
          CasualBalance = 6,
          UpdatedAt = GETDATE()
        WHERE Year != @year
      `);

    // إضافة موظفين جدد إذا لم يكونوا موجودين
    await pool.request()
      .input('year', sql.Int, year || new Date().getFullYear())
      .query(`
        INSERT INTO LeaveBalances (EmployeeCode, EmployeeName, JobTitle, Department, Year)
        SELECT DISTINCT
          e.EmployeeCode,
          CONCAT(e.FirstName, ' ', e.LastName),
          e.JobTitle,
          e.Department,
          @year
        FROM Employees e
        WHERE e.EmployeeCode IS NOT NULL
          AND e.EmployeeCode != ''
          AND NOT EXISTS (
            SELECT 1 FROM LeaveBalances lb
            WHERE lb.EmployeeCode = e.EmployeeCode AND lb.Year = @year
          )
      `);

    return NextResponse.json({
      success: true,
      message: 'تم إعادة تعيين أرصدة الإجازات بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في إعادة تعيين أرصدة الإجازات'
    }, { status: 500 });
  }
}

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'list';
    const employeeCode = searchParams.get('employeeCode');

    const pool = await getConnection();

    if (action === 'list') {
      // جلب رصيد موظف محدد أو جميع الموظفين
      let query = `
        SELECT
          EmployeeCode,
          Year,
          COALESCE(AnnualBalance, RegularBalance, 15) as AnnualBalance,
          COALESCE(CasualBalance, 6) as CasualBalance,
          CreatedAt
        FROM LeaveBalances
        WHERE Year = YEAR(GETDATE())
      `;

      const request = pool.request();

      if (employeeCode) {
        query += ` AND EmployeeCode = @employeeCode`;
        request.input('employeeCode', sql.NVarChar, employeeCode);
      }

      query += ' ORDER BY EmployeeCode';

      const result = await request.query(query);

      return NextResponse.json({
        success: true,
        balances: result.recordset
      });
    }

    return NextResponse.json({
      success: false,
      error: 'إجراء غير صحيح'
    }, { status: 400 });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  }
}
