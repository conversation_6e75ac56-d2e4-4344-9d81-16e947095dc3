import { NextResponse } from 'next/server';
import { getConnection } from '@/utils/db';

export async function GET() {
  let pool = null;

  try {

    pool = await getConnection();

    // إنشاء جدول النقل إذا لم يكن موجوداً
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='EmployeeTransfers' AND xtype='U')
      CREATE TABLE EmployeeTransfers (
        ID int IDENTITY(1,1) PRIMARY KEY,
        EmployeeID int NOT NULL,
        EmployeeName nvarchar(255),
        PreviousDepartment nvarchar(255),
        NewDepartment nvarchar(255),
        PreviousJobTitle nvarchar(255),
        NewJobTitle nvarchar(255),
        ProjectOrDepartment nvarchar(255),
        TransferDate datetime,
        TransferReason nvarchar(500),
        TransferRequestPath nvarchar(500),
        ApprovalDocumentPath nvarchar(500),
        CreatedAt datetime DEFAULT GETDATE(),
        <PERSON><PERSON>y nvarchar(100),
        IsActive bit DEFAULT 1
      )

      -- إضافة العمود الجديد إذا لم يكن موجوداً
      IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'EmployeeTransfers' AND COLUMN_NAME = 'ProjectOrDepartment')
      BEGIN
        ALTER TABLE EmployeeTransfers ADD ProjectOrDepartment nvarchar(255)
      END
    `);

    // إنشاء جدول أرشيف النقل إذا لم يكن موجوداً
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='TransferArchive' AND xtype='U')
      CREATE TABLE TransferArchive (
        ID int IDENTITY(1,1) PRIMARY KEY,
        TransferID int NOT NULL,
        DocumentType nvarchar(100),
        DocumentPath nvarchar(500),
        UploadDate datetime DEFAULT GETDATE(),
        UploadedBy nvarchar(100),
        IsActive bit DEFAULT 1,
        FOREIGN KEY (TransferID) REFERENCES EmployeeTransfers(ID)
      )
    `);

    // جلب بيانات النقل
    const transfersResult = await pool.request().query(`
      SELECT
        t.*,
        e.EmployeeName as CurrentEmployeeName,
        e.Department as CurrentDepartment,
        e.JobTitle as CurrentJobTitle
      FROM EmployeeTransfers t
      LEFT JOIN Employees e ON t.EmployeeCode = e.EmployeeCode
      WHERE t.IsActive = 1
      ORDER BY t.TransferDate DESC, t.CreatedAt DESC
    `);

    return NextResponse.json({
      success: true,
      data: transfersResult.recordset
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'حدث خطأ في جلب بيانات النقل',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}

export async function POST(request) {
  let pool = null;

  try {
    const transferData = await request.json();

    pool = await getConnection();

    // إدراج بيانات النقل
    const result = await pool.request()
      .input('EmployeeID', transferData.employeeId)
      .input('EmployeeName', transferData.employeeName)
      .input('PreviousDepartment', transferData.previousDepartment)
      .input('NewDepartment', transferData.newDepartment)
      .input('PreviousJobTitle', transferData.previousJobTitle)
      .input('NewJobTitle', transferData.newJobTitle)
      .input('ProjectOrDepartment', transferData.projectOrDepartment)
      .input('TransferDate', transferData.transferDate)
      .input('TransferReason', transferData.transferReason)
      .input('CreatedBy', transferData.createdBy || 'النظام')
      .query(`
        INSERT INTO EmployeeTransfers
        (EmployeeID, EmployeeName, PreviousDepartment, NewDepartment,
         PreviousJobTitle, NewJobTitle, ProjectOrDepartment, TransferDate, TransferReason, CreatedBy)
        OUTPUT INSERTED.ID
        VALUES
        (@EmployeeID, @EmployeeName, @PreviousDepartment, @NewDepartment,
         @PreviousJobTitle, @NewJobTitle, @ProjectOrDepartment, @TransferDate, @TransferReason, @CreatedBy)
      `);

    const transferId = result.recordset[0].ID;

    // تحديث حالة الموظف في جدول الموظفين
    await pool.request()
      .input('EmployeeID', transferData.employeeId)
      .input('NewDepartment', transferData.newDepartment)
      .input('NewJobTitle', transferData.newJobTitle)
      .query(`
        UPDATE Employees 
        SET 
          Department = @NewDepartment,
          JobTitle = @NewJobTitle,
          CurrentStatus = N'منقول',
          LastModified = GETDATE()
        WHERE EmployeeID = @EmployeeID
      `);

    return NextResponse.json({
      success: true,
      transferId: transferId,
      message: 'تم إضافة النقل بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'حدث خطأ في إضافة النقل',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}
