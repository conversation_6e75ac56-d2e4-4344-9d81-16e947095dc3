import { NextResponse } from 'next/server';
import { getConnection } from '@/lib/db';
import sql from 'mssql';

export async function GET() {
  try {

    const pool = await getConnection();

    // جلب جميع الموظفين النشطين مع معلومات المدير المباشر
    const employeesQuery = `
      SELECT 
        e.EmployeeCode,
        e.EmployeeName,
        e.JobTitle,
        e.Department,
        e.direct as DirectManager,
        e.CurrentStatus,
        e.HireDate,
        e.Governorate,
        e.Mobile,
        e.email,
        -- محاولة العثور على معلومات المدير المباشر
        mgr.EmployeeName as ManagerName,
        mgr.JobTitle as ManagerJobTitle,
        mgr.EmployeeCode as ManagerCode
      FROM Employees e
      LEFT JOIN Employees mgr ON (
        mgr.EmployeeName = e.direct 
        OR mgr.EmployeeCode = e.direct
        OR CAST(mgr.EmployeeCode AS NVARCHAR) = e.direct
      )
      WHERE e.CurrentStatus IN (N'نشط', N'ساري', N'سارى', N'active', N'Active')
        AND e.EmployeeName IS NOT NULL 
        AND e.EmployeeName != ''
      ORDER BY 
        CASE 
          WHEN e.JobTitle LIKE '%مدير المنطقة%' OR e.JobTitle LIKE '%Regional Manager%' THEN 1
          WHEN e.JobTitle LIKE '%مدير عام%' OR e.JobTitle LIKE '%General Manager%' THEN 2
          WHEN e.JobTitle LIKE '%مدير%' OR e.JobTitle LIKE '%Manager%' THEN 3
          WHEN e.JobTitle LIKE '%رئيس%' OR e.JobTitle LIKE '%Head%' THEN 4
          ELSE 5
        END,
        e.Department,
        e.EmployeeName
    `;

    const employeesResult = await pool.request().query(employeesQuery);
    const employees = employeesResult.recordset;

    // جلب قائمة الأقسام المتاحة
    const departmentsQuery = `
      SELECT DISTINCT Department
      FROM Employees
      WHERE Department IS NOT NULL 
        AND Department != ''
        AND CurrentStatus IN (N'نشط', N'ساري', N'سارى', N'active', N'Active')
      ORDER BY Department
    `;

    const departmentsResult = await pool.request().query(departmentsQuery);
    const departments = departmentsResult.recordset.map(row => row.Department);

    // إحصائيات إضافية
    const statsQuery = `
      SELECT 
        COUNT(*) as TotalEmployees,
        COUNT(CASE WHEN JobTitle LIKE '%مدير%' THEN 1 END) as TotalManagers,
        COUNT(DISTINCT Department) as TotalDepartments,
        COUNT(CASE WHEN direct IS NOT NULL AND direct != '' THEN 1 END) as EmployeesWithManagers
      FROM Employees
      WHERE CurrentStatus IN (N'نشط', N'ساري', N'سارى', N'active', N'Active')
    `;

    const statsResult = await pool.request().query(statsQuery);
    const stats = statsResult.recordset[0];

    return NextResponse.json({
      success: true,
      employees: employees,
      departments: departments,
      stats: {
        totalEmployees: stats.TotalEmployees,
        totalManagers: stats.TotalManagers,
        totalDepartments: stats.TotalDepartments,
        employeesWithManagers: stats.EmployeesWithManagers,
        orphanedEmployees: stats.TotalEmployees - stats.EmployeesWithManagers
      },
      message: `تم جلب ${employees.length} موظف بنجاح`
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب بيانات الهيكل الوظيفي',
      details: error.message
    }, { status: 500 });
  }
}

// إضافة endpoint لتحديث المدير المباشر
export async function POST(request) {
  try {
    const body = await request.json();
    const { action, employeeCode, newManagerCode } = body;

    if (action === 'updateManager') {
      const pool = await getConnection();

      // التحقق من وجود الموظف والمدير الجديد
      const checkQuery = `
        SELECT 
          (SELECT COUNT(*) FROM Employees WHERE EmployeeCode = @employeeCode) as EmployeeExists,
          (SELECT COUNT(*) FROM Employees WHERE EmployeeCode = @newManagerCode) as ManagerExists,
          (SELECT EmployeeName FROM Employees WHERE EmployeeCode = @newManagerCode) as ManagerName
      `;

      const checkResult = await pool.request()
        .input('employeeCode', sql.NVarChar, employeeCode)
        .input('newManagerCode', sql.NVarChar, newManagerCode)
        .query(checkQuery);

      const check = checkResult.recordset[0];

      if (check.EmployeeExists === 0) {
        return NextResponse.json({
          success: false,
          error: 'الموظف غير موجود'
        }, { status: 404 });
      }

      if (check.ManagerExists === 0) {
        return NextResponse.json({
          success: false,
          error: 'المدير المحدد غير موجود'
        }, { status: 404 });
      }

      // تحديث المدير المباشر
      const updateQuery = `
        UPDATE Employees 
        SET direct = @newManagerCode
        WHERE EmployeeCode = @employeeCode
      `;

      await pool.request()
        .input('employeeCode', sql.NVarChar, employeeCode)
        .input('newManagerCode', sql.NVarChar, newManagerCode)
        .query(updateQuery);

      return NextResponse.json({
        success: true,
        message: `تم تحديث المدير المباشر إلى ${check.ManagerName}`
      });
    }

    // إضافة actions أخرى حسب الحاجة
    return NextResponse.json({
      success: false,
      error: 'إجراء غير مدعوم'
    }, { status: 400 });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث البيانات',
      details: error.message
    }, { status: 500 });
  }
}

// إضافة endpoint لتحليل الهيكل الوظيفي
export async function PUT(request) {
  try {
    const body = await request.json();
    const { action } = body;

    if (action === 'analyzeStructure') {
      const pool = await getConnection();

      // تحليل المشاكل في الهيكل الوظيفي
      const analysisQuery = `
        SELECT 
          'موظفين بدون مدير مباشر' as IssueType,
          COUNT(*) as Count,
          STRING_AGG(EmployeeName + ' (' + EmployeeCode + ')', ', ') as Details
        FROM Employees
        WHERE (direct IS NULL OR direct = '')
          AND CurrentStatus IN (N'نشط', N'ساري', N'سارى')
          AND JobTitle NOT LIKE '%مدير المنطقة%'
          AND JobTitle NOT LIKE '%مدير عام%'
        
        UNION ALL
        
        SELECT 
          'مديرين غير موجودين' as IssueType,
          COUNT(*) as Count,
          STRING_AGG(e.EmployeeName + ' (مدير: ' + e.direct + ')', ', ') as Details
        FROM Employees e
        WHERE e.direct IS NOT NULL 
          AND e.direct != ''
          AND e.CurrentStatus IN (N'نشط', N'ساري', N'سارى')
          AND NOT EXISTS (
            SELECT 1 FROM Employees mgr 
            WHERE mgr.EmployeeName = e.direct 
               OR mgr.EmployeeCode = e.direct
               OR CAST(mgr.EmployeeCode AS NVARCHAR) = e.direct
          )
        
        UNION ALL
        
        SELECT 
          'مستويات هرمية عميقة' as IssueType,
          COUNT(*) as Count,
          'تحتاج مراجعة يدوية' as Details
        FROM Employees
        WHERE CurrentStatus IN (N'نشط', N'ساري', N'سارى')
      `;

      const analysisResult = await pool.request().query(analysisQuery);
      const issues = analysisResult.recordset;

      // إحصائيات الهيكل
      const structureStatsQuery = `
        SELECT 
          Department,
          COUNT(*) as EmployeeCount,
          COUNT(CASE WHEN JobTitle LIKE '%مدير%' THEN 1 END) as ManagerCount,
          COUNT(CASE WHEN direct IS NOT NULL AND direct != '' THEN 1 END) as WithManagerCount
        FROM Employees
        WHERE CurrentStatus IN (N'نشط', N'ساري', N'سارى')
          AND Department IS NOT NULL
        GROUP BY Department
        ORDER BY EmployeeCount DESC
      `;

      const structureStatsResult = await pool.request().query(structureStatsQuery);
      const departmentStats = structureStatsResult.recordset;

      return NextResponse.json({
        success: true,
        analysis: {
          issues: issues,
          departmentStats: departmentStats,
          recommendations: [
            'تحديد مدير مباشر لجميع الموظفين',
            'التأكد من صحة أسماء وأكواد المديرين',
            'مراجعة المستويات الهرمية العميقة',
            'توزيع المسؤوليات بشكل متوازن'
          ]
        }
      });
    }

    return NextResponse.json({
      success: false,
      error: 'إجراء غير مدعوم'
    }, { status: 400 });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في تحليل الهيكل الوظيفي',
      details: error.message
    }, { status: 500 });
  }
}
