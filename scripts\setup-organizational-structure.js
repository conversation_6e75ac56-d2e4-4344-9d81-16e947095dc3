const sql = require('mssql');

// إعدادات قاعدة البيانات
const dbConfig = {
  user: 'SA',
  password: 'admin@123',
  server: 'localhost\\DBOJESTA',
  database: 'EMP',
  options: {
    encrypt: false,
    trustServerCertificate: true,
    enableArithAbort: true,
    instanceName: 'DBOJESTA'
  }
};

async function setupOrganizationalStructure() {
  try {
    console.log('🔗 الاتصال بقاعدة البيانات...');
    const pool = await sql.connect(dbConfig);

    // إنشاء الجداول
    console.log('📋 إنشاء جداول الهيكل الوظيفي...');
    
    // جدول الأقسام والوحدات التنظيمية
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='OrganizationalUnits' AND xtype='U')
      BEGIN
        CREATE TABLE OrganizationalUnits (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          UnitName NVARCHAR(200) NOT NULL,
          UnitCode NVARCHAR(50) UNIQUE,
          ParentUnitID INT NULL,
          ManagerEmployeeCode NVARCHAR(20),
          ManagerName NVARCHAR(100),
          UnitLevel INT DEFAULT 1,
          UnitType NVARCHAR(50) DEFAULT N'قسم',
          Description NVARCHAR(MAX),
          IsActive BIT DEFAULT 1,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),
          
          FOREIGN KEY (ParentUnitID) REFERENCES OrganizationalUnits(ID)
        )
      END
    `);

    // جدول ربط الموظفين بالأقسام
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='EmployeeUnits' AND xtype='U')
      BEGIN
        CREATE TABLE EmployeeUnits (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          EmployeeCode NVARCHAR(20) NOT NULL,
          UnitID INT NOT NULL,
          Position NVARCHAR(100),
          IsDirectManager BIT DEFAULT 0,
          AssignmentDate DATE DEFAULT GETDATE(),
          IsActive BIT DEFAULT 1,
          
          FOREIGN KEY (UnitID) REFERENCES OrganizationalUnits(ID),
          UNIQUE(EmployeeCode, UnitID)
        )
      END
    `);

    console.log('✅ تم إنشاء الجداول بنجاح');

    // إضافة البيانات التجريبية
    console.log('📝 إضافة البيانات التجريبية...');

    // 1. إدارة المنطقة (المستوى الأعلى)
    const regionResult = await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM OrganizationalUnits WHERE UnitName = N'إدارة المنطقة')
      BEGIN
        INSERT INTO OrganizationalUnits (UnitName, UnitCode, ManagerEmployeeCode, ManagerName, UnitLevel, UnitType, Description)
        OUTPUT INSERTED.ID
        VALUES (N'إدارة المنطقة', 'REG-001', '1000', N'مدير المنطقة', 1, N'إدارة', N'الإدارة العليا للمنطقة')
      END
      ELSE
      BEGIN
        SELECT ID FROM OrganizationalUnits WHERE UnitName = N'إدارة المنطقة'
      END
    `);

    const regionId = regionResult.recordset[0]?.ID;

    if (regionId) {
      // 2. الأقسام الرئيسية
      const departments = [
        {
          name: 'المكتب الفني',
          code: 'TECH-001',
          managerCode: '1414',
          managerName: 'إبراهيم سيد',
          description: 'قسم المكتب الفني والدراسات التقنية'
        },
        {
          name: 'قسم التنفيذ',
          code: 'EXEC-001',
          managerCode: '1428',
          managerName: 'هاني إمام',
          description: 'قسم تنفيذ المشاريع والأعمال'
        },
        {
          name: 'السلامة والصحة المهنية',
          code: 'SAFETY-001',
          managerCode: '5632',
          managerName: 'محمد عبد الحكم',
          description: 'قسم السلامة والصحة المهنية'
        },
        {
          name: 'الشؤون الإدارية',
          code: 'ADMIN-001',
          managerCode: '2100',
          managerName: 'أحمد محمود',
          description: 'قسم الشؤون الإدارية والموارد البشرية'
        },
        {
          name: 'الشؤون المالية',
          code: 'FIN-001',
          managerCode: '3200',
          managerName: 'سارة أحمد',
          description: 'قسم الشؤون المالية والمحاسبة'
        }
      ];

      for (const dept of departments) {
        const deptResult = await pool.request()
          .input('unitName', dept.name)
          .input('unitCode', dept.code)
          .input('parentUnitId', regionId)
          .input('managerEmployeeCode', dept.managerCode)
          .input('managerName', dept.managerName)
          .input('description', dept.description)
          .query(`
            IF NOT EXISTS (SELECT * FROM OrganizationalUnits WHERE UnitName = @unitName)
            BEGIN
              INSERT INTO OrganizationalUnits (
                UnitName, UnitCode, ParentUnitID, ManagerEmployeeCode, 
                ManagerName, UnitLevel, UnitType, Description
              )
              OUTPUT INSERTED.ID
              VALUES (
                @unitName, @unitCode, @parentUnitId, @managerEmployeeCode,
                @managerName, 2, N'قسم', @description
              )
            END
            ELSE
            BEGIN
              SELECT ID FROM OrganizationalUnits WHERE UnitName = @unitName
            END
          `);

        const deptId = deptResult.recordset[0]?.ID;

        if (deptId) {
          // إضافة المدير إلى جدول ربط الموظفين
          await pool.request()
            .input('employeeCode', dept.managerCode)
            .input('unitId', deptId)
            .input('position', 'مدير القسم')
            .query(`
              IF NOT EXISTS (SELECT * FROM EmployeeUnits WHERE EmployeeCode = @employeeCode AND UnitID = @unitId)
              BEGIN
                INSERT INTO EmployeeUnits (EmployeeCode, UnitID, Position, IsDirectManager)
                VALUES (@employeeCode, @unitId, @position, 1)
              END
            `);

          console.log(`✅ تم إضافة قسم: ${dept.name} - مدير: ${dept.managerName}`);

          // إضافة وحدات فرعية للمكتب الفني
          if (dept.name === 'المكتب الفني') {
            const techSubUnits = [
              {
                name: 'وحدة التصميم',
                code: 'TECH-DESIGN',
                managerCode: '1415',
                managerName: 'محمد علي',
                description: 'وحدة التصميم والرسم الهندسي'
              },
              {
                name: 'وحدة الدراسات',
                code: 'TECH-STUDY',
                managerCode: '1416',
                managerName: 'فاطمة حسن',
                description: 'وحدة الدراسات الفنية والتقنية'
              }
            ];

            for (const subUnit of techSubUnits) {
              const subResult = await pool.request()
                .input('unitName', subUnit.name)
                .input('unitCode', subUnit.code)
                .input('parentUnitId', deptId)
                .input('managerEmployeeCode', subUnit.managerCode)
                .input('managerName', subUnit.managerName)
                .input('description', subUnit.description)
                .query(`
                  IF NOT EXISTS (SELECT * FROM OrganizationalUnits WHERE UnitName = @unitName)
                  BEGIN
                    INSERT INTO OrganizationalUnits (
                      UnitName, UnitCode, ParentUnitID, ManagerEmployeeCode, 
                      ManagerName, UnitLevel, UnitType, Description
                    )
                    OUTPUT INSERTED.ID
                    VALUES (
                      @unitName, @unitCode, @parentUnitId, @managerEmployeeCode,
                      @managerName, 3, N'وحدة', @description
                    )
                  END
                  ELSE
                  BEGIN
                    SELECT ID FROM OrganizationalUnits WHERE UnitName = @unitName
                  END
                `);

              const subUnitId = subResult.recordset[0]?.ID;

              if (subUnitId) {
                await pool.request()
                  .input('employeeCode', subUnit.managerCode)
                  .input('unitId', subUnitId)
                  .input('position', 'رئيس الوحدة')
                  .query(`
                    IF NOT EXISTS (SELECT * FROM EmployeeUnits WHERE EmployeeCode = @employeeCode AND UnitID = @unitId)
                    BEGIN
                      INSERT INTO EmployeeUnits (EmployeeCode, UnitID, Position, IsDirectManager)
                      VALUES (@employeeCode, @unitId, @position, 1)
                    END
                  `);

                console.log(`  ✅ تم إضافة وحدة فرعية: ${subUnit.name} - رئيس: ${subUnit.managerName}`);
              }
            }
          }
        }
      }
    }

    // عرض النتيجة النهائية
    const finalResult = await pool.request().query(`
      SELECT 
        ou.ID,
        ou.UnitName,
        ou.UnitCode,
        ou.ManagerEmployeeCode,
        ou.ManagerName,
        ou.UnitLevel,
        ou.UnitType,
        parent.UnitName as ParentUnitName
      FROM OrganizationalUnits ou
      LEFT JOIN OrganizationalUnits parent ON ou.ParentUnitID = parent.ID
      WHERE ou.IsActive = 1
      ORDER BY ou.UnitLevel, ou.UnitName
    `);

    console.log('\n📊 الهيكل الوظيفي النهائي:');
    finalResult.recordset.forEach((unit, index) => {
      const indent = '  '.repeat(unit.UnitLevel - 1);
      console.log(`${index + 1}. ${indent}${unit.UnitName} (${unit.UnitType})`);
      console.log(`${indent}   مدير: ${unit.ManagerName} - كود: ${unit.ManagerEmployeeCode}`);
      if (unit.ParentUnitName) {
        console.log(`${indent}   تابع لـ: ${unit.ParentUnitName}`);
      }
      console.log('');
    });

    await pool.close();
    console.log('\n✅ تم إعداد الهيكل الوظيفي بنجاح!');

  } catch (error) {
    console.error('❌ خطأ في إعداد الهيكل الوظيفي:', error.message);
  }
}

setupOrganizationalStructure();
