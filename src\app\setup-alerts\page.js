'use client';

import { useState } from 'react';

export default function SetupAlerts() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);

  const setupAlerts = async () => {
    try {
      setLoading(true);
      setResult(null);

      const response = await fetch('/api/alerts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'setup'
        })
      });

      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({ success: false, error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const testAlerts = async () => {
    try {
      setLoading(true);
      setResult(null);

      const response = await fetch('/api/alerts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getAlerts',
          limit: 10
        })
      });

      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({ success: false, error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const getAlertCounts = async () => {
    try {
      setLoading(true);
      setResult(null);

      const response = await fetch('/api/alerts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getAlertCounts'
        })
      });

      const data = await response.json();
      setResult(data);
    } catch (error) {
      setResult({ success: false, error: error.message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-8">إعداد نظام التنبيهات</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <button
            onClick={setupAlerts}
            disabled={loading}
            className="bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white font-bold py-3 px-6 rounded-lg"
          >
            {loading ? 'جاري الإعداد...' : 'إعداد نظام التنبيهات'}
          </button>
          
          <button
            onClick={testAlerts}
            disabled={loading}
            className="bg-green-500 hover:bg-green-600 disabled:bg-green-300 text-white font-bold py-3 px-6 rounded-lg"
          >
            {loading ? 'جاري الاختبار...' : 'اختبار جلب التنبيهات'}
          </button>
          
          <button
            onClick={getAlertCounts}
            disabled={loading}
            className="bg-purple-500 hover:bg-purple-600 disabled:bg-purple-300 text-white font-bold py-3 px-6 rounded-lg"
          >
            {loading ? 'جاري الجلب...' : 'جلب عدد التنبيهات'}
          </button>
        </div>

        {result && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-bold mb-4">النتيجة:</h2>
            <div className={`p-4 rounded-lg ${
              result.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
            }`}>
              <div className={`font-bold mb-2 ${
                result.success ? 'text-green-800' : 'text-red-800'
              }`}>
                {result.success ? '✅ نجح' : '❌ فشل'}
              </div>
              
              <pre className="text-sm overflow-x-auto whitespace-pre-wrap">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          </div>
        )}

        <div className="mt-8 bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold mb-4">معلومات النظام:</h2>
          <div className="space-y-2 text-sm">
            <p><strong>الإشعارات (Notifications):</strong> جدول SmartNotifications - أيقونة الجرس 🔔</p>
            <p><strong>التنبيهات (Alerts):</strong> جدول SystemAlerts - أيقونة المثلث ⚠️</p>
            <p><strong>الفرق:</strong> الإشعارات للأحداث التفاعلية، التنبيهات للتحذيرات العامة</p>
          </div>
        </div>
      </div>
    </div>
  );
}
