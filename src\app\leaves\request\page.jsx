'use client';

import React, { useState, useEffect } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { useLanguage, useTranslation } from '@/contexts/LanguageContext';
import MainLayout from '@/components/MainLayout';
import { Calendar, User, FileText, Clock, Save, Printer, Search } from 'lucide-react';

export default function LeaveRequestForm() {
  const { isDarkMode } = useTheme();
  const { isRTL, isArabic } = useLanguage();
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    employeeCode: '',
    employeeName: '',
    jobTitle: '',
    department: '',
    project: '',
    leaveType: 'اعتيادية',
    startDate: '',
    endDate: '',
    daysCount: 0,
    reason: ''
  });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [employeeBalance, setEmployeeBalance] = useState(null);
  const [lastLeaveDate, setLastLeaveDate] = useState('لا توجد إجازات سابقة');

  // تحويل التاريخ من DD/MM/YYYY إلى YYYY-MM-DD للحسابات
  const convertDateForCalculation = (dateStr) => {
    if (!dateStr || dateStr === 'undefined' || dateStr === 'null') return '';
    if (typeof dateStr === 'string' && dateStr.includes('/')) {
      const parts = dateStr.split('/');
      if (parts.length === 3) {
        const [day, month, year] = parts;
        if (day && month && year) {
          return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        }
      }
    }
    return dateStr;
  };

  // تحويل التاريخ من YYYY-MM-DD إلى DD/MM/YYYY للعرض
  const convertDateForDisplay = (dateStr) => {
    if (!dateStr || dateStr === 'undefined' || dateStr === 'null') return '';
    if (typeof dateStr === 'string' && dateStr.includes('-') && dateStr.length === 10) {
      const parts = dateStr.split('-');
      if (parts.length === 3) {
        const [year, month, day] = parts;
        if (year && month && day) {
          return `${day}/${month}/${year}`;
        }
      }
    }
    return dateStr;
  };

  // حساب عدد الأيام تلقائياً
  useEffect(() => {
    if (formData.startDate && formData.endDate) {
      const start = new Date(convertDateForCalculation(formData.startDate));
      const end = new Date(convertDateForCalculation(formData.endDate));
      const diffTime = Math.abs(end - start);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
      setFormData(prev => ({ ...prev, daysCount: diffDays }));
    }
  }, [formData.startDate, formData.endDate]);

  // البحث عن الموظف بالكود
  const handleEmployeeSearch = async () => {
    if (!formData.employeeCode) {
      setMessage('يرجى إدخال كود الموظف');
      return;
    }

    setLoading(true);
    try {

      const response = await fetch('/api/leave-management', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getEmployee',
          employeeCode: formData.employeeCode
        })
      });

      const result = await response.json();

      if (result.success) {
        setFormData(prev => ({
          ...prev,
          employeeName: result.data.EmployeeName,
          jobTitle: result.data.JobTitle,
          department: result.data.Department,
          project: result.data.Project || ''
        }));

        // جلب رصيد الإجازات وتاريخ آخر إجازة
        await getEmployeeBalance(formData.employeeCode);
        await getLastLeaveDate(formData.employeeCode);
        setMessage('تم العثور على بيانات الموظف');
      } else {
        setMessage(result.error || 'لم يتم العثور على الموظف');
        setFormData(prev => ({
          ...prev,
          employeeName: '',
          jobTitle: '',
          department: ''
        }));
      }
    } catch (error) {

      setMessage('خطأ في البحث عن الموظف: ' + error.message);
    }
    setLoading(false);
  };

  // جلب رصيد الإجازات
  const getEmployeeBalance = async (employeeCode) => {
    try {
      const response = await fetch('/api/leave-management', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getBalance',
          employeeCode: employeeCode
        })
      });

      const result = await response.json();
      if (result.success) {
        setEmployeeBalance(result.data);
      }
    } catch (error) {

    }
  };

  // جلب تاريخ آخر إجازة
  const getLastLeaveDate = async (employeeCode) => {
    try {
      const response = await fetch('/api/leave-management', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getLastLeaveDate',
          employeeCode: employeeCode
        })
      });

      const result = await response.json();
      if (result.success && result.data) {
        setLastLeaveDate(result.data.FormattedDate || 'لا توجد إجازات سابقة');
      }
    } catch (error) {

      setLastLeaveDate('لا توجد إجازات سابقة');
    }
  };

  // إرسال طلب الإجازة
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.employeeCode || !formData.employeeName) {
      setMessage('يرجى البحث عن الموظف أولاً');
      return;
    }

    if (!formData.startDate || !formData.endDate) {
      setMessage('يرجى تحديد تاريخ بداية ونهاية الإجازة');
      return;
    }

    if (!formData.leaveType) {
      setMessage('يرجى تحديد نوع الإجازة');
      return;
    }

    // التحقق من الرصيد قبل الإرسال (فقط للإجازات الاعتيادية والعارضة)
    // إجازة البدل والمرضية والأمومة وبدون راتب لا تحتاج رصيد
    if (employeeBalance && (formData.leaveType === 'اعتيادية' || formData.leaveType === 'عارضة')) {
      const availableBalance = formData.leaveType === 'اعتيادية'
        ? (employeeBalance.RemainingRegular || employeeBalance.RegularBalance)
        : (employeeBalance.RemainingCasual || employeeBalance.CasualBalance);

      if (availableBalance < formData.daysCount) {
        setMessage(`الرصيد المتاح غير كافي. المتاح: ${availableBalance} يوم، المطلوب: ${formData.daysCount} يوم`);
        return;
      }
    }

    setLoading(true);
    try {
      const response = await fetch('/api/leave-management', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'create',
          ...formData
        })
      });

      const result = await response.json();

      if (result.success) {
        setMessage('تم إرسال طلب الإجازة بنجاح');
        // إعادة تعيين النموذج
        setFormData({
          employeeCode: '',
          employeeName: '',
          jobTitle: '',
          department: '',
          project: '',
          leaveType: 'اعتيادية',
          startDate: '',
          endDate: '',
          daysCount: 0,
          reason: ''
        });
        setEmployeeBalance(null);
        setLastLeaveDate('');
      } else {
        setMessage(result.error);
      }
    } catch (error) {
      setMessage('خطأ في إرسال الطلب: ' + error.message);
    }
    setLoading(false);
  };

  // طباعة النموذج
  const handlePrint = () => {
    const printContents = document.getElementById('printable-form').innerHTML;

    // إنشاء نافذة طباعة جديدة
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>طلب إجازة</title>
        <style>
          @page {
            size: A4;
            margin: 1cm;
          }

          body {
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.4;
            color: black;
            background: white;
            margin: 0;
            padding: 0;
            direction: rtl;
          }

          .border-2 { border-width: 2px; }
          .border { border-width: 1px; }
          .border-black { border-color: black; }
          .border-b { border-bottom-width: 1px; }
          .border-l { border-left-width: 1px; }
          .border-dotted { border-style: dotted; }
          .grid { display: grid; }
          .grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
          .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
          .gap-4 { gap: 1rem; }
          .gap-8 { gap: 2rem; }
          .p-3 { padding: 0.75rem; }
          .p-2 { padding: 0.5rem; }
          .p-4 { padding: 1rem; }
          .mb-2 { margin-bottom: 0.5rem; }
          .mb-4 { margin-bottom: 1rem; }
          .mb-6 { margin-bottom: 1.5rem; }
          .mt-8 { margin-top: 2rem; }
          .pt-8 { padding-top: 2rem; }
          .pb-1 { padding-bottom: 0.25rem; }
          .text-center { text-align: center; }
          .text-sm { font-size: 0.875rem; }
          .text-lg { font-size: 1.125rem; }
          .font-bold { font-weight: bold; }
          .font-medium { font-weight: 500; }
          .flex { display: flex; }
          .items-center { align-items: center; }
          .items-start { align-items: flex-start; }
          .justify-between { justify-content: space-between; }
          .flex-1 { flex: 1 1 0%; }
          .w-20 { width: 5rem; }
          .h-12 { height: 3rem; }
          .h-4 { height: 1rem; }
          .w-4 { width: 1rem; }
          .w-32 { width: 8rem; }
          .min-h-24 { min-height: 6rem; }
          .space-y-6 > * + * { margin-top: 1.5rem; }
          .space-y-3 > * + * { margin-top: 0.75rem; }
          .mr-2 { margin-right: 0.5rem; }
          .ml-2 { margin-left: 0.5rem; }
          .bg-gray-200 { background-color: #e5e7eb; }
          .bg-blue-200 { background-color: #dbeafe; }
          .whitespace-nowrap { white-space: nowrap; }
          .resize-none { resize: none; }

          input, textarea {
            border: none;
            background: transparent;
            color: black;
            font-size: 14px;
            outline: none;
            font-family: Arial, sans-serif;
          }

          input[type="checkbox"] {
            width: 1rem;
            height: 1rem;
            border: 1px solid black;
          }
        </style>
      </head>
      <body>
        ${printContents}
      </body>
      </html>
    `);

    printWindow.document.close();
    printWindow.focus();

    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 250);
  };

  return (
    <MainLayout>
      <div className="max-w-4xl mx-auto">
        {/* رأس الصفحة - مخفي في الطباعة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6 print:hidden`}>
          <div className="flex items-center justify-between">
            <div>
              <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
                طلب إجازة جديد
              </h1>
              <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                نموذج طلب الإجازات - شركة كونكورد للهندسة والمقاولات
              </p>
            </div>
            <div className="flex gap-3">
              <button
                onClick={handlePrint}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                <Printer className="w-4 h-4" />
                طباعة
              </button>
            </div>
          </div>
        </div>

        {/* رسائل التنبيه - مخفية في الطباعة */}
        {message && (
          <div className={`p-4 rounded-lg mb-6 print:hidden ${
            message.includes('نجاح') || message.includes('تم العثور') || message.includes('تم')
              ? 'bg-green-100 text-green-800 border border-green-200'
              : 'bg-red-100 text-red-800 border border-red-200'
          }`}>
            <div className="flex items-center gap-2">
              {message.includes('نجاح') || message.includes('تم العثور') || message.includes('تم') ? (
                <span className="text-green-600 font-bold">✓</span>
              ) : (
                <span className="text-red-600 font-bold">✗</span>
              )}
              {message}
            </div>
          </div>
        )}

        {/* خط أخضر يشير إلى بداية المنطقة المطبوعة */}
        <div className="print:hidden">
          <div className="w-full h-1 bg-green-500 mb-4"></div>
          <p className="text-sm text-gray-600 mb-4 text-center">↑ بداية المنطقة المطبوعة ↑</p>
        </div>

        {/* نموذج طلب الإجازة - يبدأ الطباعة من هنا */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-8 print:shadow-none print:border-none print:p-0 print:m-0 print:bg-white`} id="printable-form">
          {/* رأس النموذج */}
          <div className="border-2 border-black mb-6">
            <div className="grid grid-cols-3 border-b border-black">
              <div className="p-3 border-l border-black text-center">
                <div className="w-20 h-12 bg-gray-200 mx-auto mb-2 flex items-center justify-center text-xs font-bold border border-black">
                  CONCORD
                </div>
              </div>
              <div className="p-3 border-l border-black text-center">
                <div className="font-bold text-lg mb-1">طلب إجازة</div>
                <div className="text-sm">HR-OP-02-F01</div>
              </div>
              <div className="p-3 text-center">
                <div className="font-bold text-sm">شركة كونكورد للهندسة والمقاولات</div>
                <div className="text-xs">Concord for Engineering & Contracting</div>
              </div>
            </div>
            <div className="bg-blue-200 p-2 text-center font-bold print:bg-gray-200">
              بيانات الطلب
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* الصف الأول: الكود والاسم وتاريخ آخر إجازة */}
            <div className="grid grid-cols-3 gap-4 mb-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <input
                    type="text"
                    value={formData.employeeCode}
                    onChange={(e) => setFormData(prev => ({ ...prev, employeeCode: e.target.value }))}
                    className="flex-1 text-center border-none bg-transparent text-sm font-medium"
                    placeholder="1450"
                  />
                  <span className="text-sm mr-2">الكود</span>
                </div>
                <div className="border-b border-dotted border-black pb-1"></div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <input
                    type="text"
                    value={formData.employeeName}
                    readOnly
                    className="flex-1 text-center border-none bg-transparent text-sm font-medium"
                    placeholder="محمد ممدوح محمد"
                  />
                  <span className="text-sm mr-2">الاسم</span>
                  <button
                    type="button"
                    onClick={handleEmployeeSearch}
                    disabled={loading}
                    className="mr-2 text-blue-600 hover:text-blue-800 print:hidden"
                  >
                    <Search className="w-4 h-4" />
                  </button>
                </div>
                <div className="border-b border-dotted border-black pb-1"></div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium">{lastLeaveDate}</span>
                  <span className="text-sm">تاريخ آخر إجازة</span>
                </div>
                <div className="border-b border-dotted border-black pb-1"></div>
              </div>
            </div>

            {/* الصف الثاني: الوظيفة والقسم */}
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <input
                    type="text"
                    value={formData.jobTitle}
                    readOnly
                    className="flex-1 text-center border-none bg-transparent text-sm font-medium"
                    placeholder="منسق مستندات"
                  />
                  <span className="text-sm mr-2">الوظيفة</span>
                </div>
                <div className="border-b border-dotted border-black pb-1"></div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <input
                    type="text"
                    value={formData.department}
                    readOnly
                    className="flex-1 text-center border-none bg-transparent text-sm font-medium"
                    placeholder="الإدارة"
                  />
                  <span className="text-sm mr-2">القسم</span>
                </div>
                <div className="border-b border-dotted border-black pb-1"></div>
              </div>
            </div>

            {/* الصف الثالث: الإدارة/الشعبة والمشروع */}
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <input
                    type="text"
                    value={formData.department}
                    readOnly
                    className="flex-1 text-center border-none bg-transparent text-sm font-medium"
                    placeholder="الإدارة/الشعبة"
                  />
                  <span className="text-sm mr-2">الإدارة/الشعبة</span>
                </div>
                <div className="border-b border-dotted border-black pb-1"></div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <input
                    type="text"
                    value={formData.project}
                    onChange={(e) => setFormData(prev => ({ ...prev, project: e.target.value }))}
                    className="flex-1 text-center border-none bg-transparent text-sm font-medium"
                    placeholder="مشروع أوسيتا"
                  />
                  <span className="text-sm mr-2">مشروع أوسيتا</span>
                </div>
                <div className="border-b border-dotted border-black pb-1"></div>
              </div>
            </div>

            {/* رصيد الإجازات */}
            {employeeBalance && (
              <div className="bg-blue-50 dark:bg-slate-800 p-4 rounded-lg border print:bg-gray-100 print:border-black print:hidden">
                <h3 className="font-bold text-lg mb-3">رصيد الإجازات المتاح</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {employeeBalance.RemainingRegular || employeeBalance.RegularBalance}
                    </div>
                    <div className="text-sm text-gray-600">إجازة اعتيادية</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {employeeBalance.RemainingCasual || employeeBalance.CasualBalance}
                    </div>
                    <div className="text-sm text-gray-600">إجازة عارضة</div>
                  </div>
                </div>
              </div>
            )}

            {/* نوع الإجازة */}
            <div className="mb-6">
              <div className="text-center mb-4">
                <span className="text-sm font-medium">الطلب من الإجازة الاعتيادية بعد الإجازة الأسبوعية</span>
              </div>

              <div className="grid grid-cols-3 gap-4 mb-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.leaveType === 'اعتيادية'}
                        onChange={(e) => setFormData(prev => ({ ...prev, leaveType: e.target.checked ? 'اعتيادية' : '' }))}
                        className="ml-2 w-4 h-4 border border-black"
                      />
                      <span className="text-sm">إجازة اعتيادية</span>
                    </div>
                    <span className="text-sm">نوع الإجازة</span>
                  </div>
                  <div className="border-b border-dotted border-black pb-1"></div>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.leaveType === 'عارضة'}
                        onChange={(e) => setFormData(prev => ({ ...prev, leaveType: e.target.checked ? 'عارضة' : '' }))}
                        className="ml-2 w-4 h-4 border border-black"
                      />
                      <span className="text-sm">إجازة عارضة</span>
                    </div>
                  </div>
                  <div className="border-b border-dotted border-black pb-1"></div>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.leaveType === 'مرضية'}
                        onChange={(e) => setFormData(prev => ({ ...prev, leaveType: e.target.checked ? 'مرضية' : '' }))}
                        className="ml-2 w-4 h-4 border border-black"
                      />
                      <span className="text-sm">إجازة مرضية</span>
                    </div>
                  </div>
                  <div className="border-b border-dotted border-black pb-1"></div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.leaveType === 'أمومة'}
                        onChange={(e) => setFormData(prev => ({ ...prev, leaveType: e.target.checked ? 'أمومة' : '' }))}
                        className="ml-2 w-4 h-4 border border-black"
                      />
                      <span className="text-sm">إجازة أمومة</span>
                    </div>
                  </div>
                  <div className="border-b border-dotted border-black pb-1"></div>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.leaveType === 'بدل'}
                        onChange={(e) => setFormData(prev => ({ ...prev, leaveType: e.target.checked ? 'بدل' : '' }))}
                        className="ml-2 w-4 h-4 border border-black"
                      />
                      <span className="text-sm">إجازة بدل</span>
                    </div>
                  </div>
                  <div className="border-b border-dotted border-black pb-1"></div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.leaveType === 'بدون راتب'}
                        onChange={(e) => setFormData(prev => ({ ...prev, leaveType: e.target.checked ? 'بدون راتب' : '' }))}
                        className="ml-2 w-4 h-4 border border-black"
                      />
                      <span className="text-sm">بدون راتب</span>
                    </div>
                  </div>
                  <div className="border-b border-dotted border-black pb-1"></div>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={formData.leaveType === 'أخرى'}
                        onChange={(e) => setFormData(prev => ({ ...prev, leaveType: e.target.checked ? 'أخرى' : '' }))}
                        className="ml-2 w-4 h-4 border border-black"
                      />
                      <span className="text-sm">أخرى</span>
                    </div>
                  </div>
                  <div className="border-b border-dotted border-black pb-1"></div>
                </div>
              </div>
            </div>

            {/* التواريخ */}
            <div className="grid grid-cols-3 gap-4 mb-6">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <input
                    type="number"
                    value={formData.daysCount}
                    readOnly
                    className="flex-1 text-center border-none bg-transparent text-sm font-medium"
                    placeholder="2"
                  />
                  <span className="text-sm mr-2">مدة الإجازة</span>
                </div>
                <div className="border-b border-dotted border-black pb-1"></div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <input
                    type="text"
                    value={convertDateForDisplay(formData.endDate)}
                    onChange={(e) => {
                      const value = e.target.value;
                      // السماح فقط بالأرقام والشرطة المائلة
                      if (/^[\d/]*$/.test(value) && value.length <= 10) {
                        setFormData(prev => ({ ...prev, endDate: value }));
                      }
                    }}
                    className="flex-1 text-center border-none bg-transparent text-sm font-medium"
                    placeholder="15/01/2025"
                    maxLength="10"
                    required
                  />
                  <span className="text-sm mr-2">إلى</span>
                </div>
                <div className="border-b border-dotted border-black pb-1"></div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <input
                    type="text"
                    value={convertDateForDisplay(formData.startDate)}
                    onChange={(e) => {
                      const value = e.target.value;
                      // السماح فقط بالأرقام والشرطة المائلة
                      if (/^[\d/]*$/.test(value) && value.length <= 10) {
                        setFormData(prev => ({ ...prev, startDate: value }));
                      }
                    }}
                    className="flex-1 text-center border-none bg-transparent text-sm font-medium"
                    placeholder="14/01/2025"
                    maxLength="10"
                    required
                  />
                  <span className="text-sm mr-2">من</span>
                </div>
                <div className="border-b border-dotted border-black pb-1"></div>
              </div>
            </div>

            {/* ملاحظة حول أنواع الإجازات */}
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg print:hidden">
              <p className="text-sm text-blue-800">
                <strong>ملاحظة:</strong> الإجازات الاعتيادية والعارضة تحتاج رصيد متاح.
                إجازة البدل والمرضية والأمومة وبدون راتب لا تحتاج رصيد.
              </p>
            </div>

            {/* سبب الإجازة */}
            <div className="mb-6">
              <div className="flex items-start justify-between mb-2">
                <textarea
                  value={formData.reason}
                  onChange={(e) => setFormData(prev => ({ ...prev, reason: e.target.value }))}
                  rows={2}
                  className="flex-1 border-none bg-transparent text-sm resize-none"
                  placeholder="اذكر سبب طلب الإجازة..."
                  required
                />
                <span className="text-sm mr-2 whitespace-nowrap">سبب الإجازة</span>
              </div>
              <div className="border-b border-dotted border-black pb-1"></div>
            </div>

            {/* التوقيعات */}
            <div className="grid grid-cols-2 gap-8 mt-8 pt-8">
              <div className="space-y-6">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="w-32 h-12"></div>
                    <span className="text-sm">توقيع الموظف</span>
                  </div>
                  <div className="border-b border-black"></div>
                </div>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="w-32 h-12"></div>
                    <span className="text-sm">اعتماد مدير المشروع</span>
                  </div>
                  <div className="border-b border-black"></div>
                </div>
              </div>
              <div className="space-y-6">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="w-32 h-12"></div>
                    <span className="text-sm">اعتماد الرئيس المباشر</span>
                  </div>
                  <div className="border-b border-black"></div>
                </div>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <div className="w-32 h-12"></div>
                    <span className="text-sm">المدير الإداري</span>
                  </div>
                  <div className="border-b border-black"></div>
                </div>
              </div>
            </div>

            {/* عمليات الموارد البشرية */}
            <div className="mt-8">
              <div className="bg-blue-200 p-2 text-center font-bold print:bg-gray-200 border border-black">
                عمليات الموارد البشرية
              </div>
              <div className="border-2 border-black p-4 min-h-32">
                <div className="flex items-start mb-4">
                  <span className="text-sm">ملاحظات:</span>
                </div>
                <div className="space-y-4">
                  <div className="border-b border-dotted border-gray-400 h-4"></div>
                  <div className="border-b border-dotted border-gray-400 h-4"></div>
                  <div className="border-b border-dotted border-gray-400 h-4"></div>
                </div>
                <div className="mt-8 pt-4 border-t border-black">
                  <div className="flex justify-between items-center">
                    <div className="text-center">
                      <div className="border-b border-black w-48 h-12 mb-2"></div>
                      <span className="text-sm">اختصاص مدير عمليات الموارد البشرية</span>
                    </div>
                  </div>
                </div>
                <div className="mt-6 text-center text-xs">
                  <p>في حالة الإجازة المرضية يرفق التقرير الطبي</p>
                  <p>في حالة عدم وجود إجازات متبقية يتحمل الطالب أجازات عينية بدون راتب</p>
                </div>
              </div>
            </div>

            {/* أزرار الإجراءات */}
            <div className="flex gap-4 pt-6 border-t print:hidden">
              <button
                type="submit"
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
              >
                <Save className="w-4 h-4" />
                {loading ? 'جاري الإرسال...' : 'إرسال الطلب'}
              </button>

              <button
                type="button"
                onClick={() => {
                  setFormData({
                    employeeCode: '',
                    employeeName: '',
                    jobTitle: '',
                    department: '',
                    project: '',
                    leaveType: 'اعتيادية',
                    startDate: '',
                    endDate: '',
                    daysCount: 0,
                    reason: ''
                  });
                  setEmployeeBalance(null);
                  setLastLeaveDate('لا توجد إجازات سابقة');
                  setMessage('');
                }}
                className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg transition-colors"
              >
                مسح البيانات
              </button>
            </div>
          </form>
        </div>
      </div>
      {/* CSS محسن للطباعة */}
      <style jsx global>{`
        @media print {
          @page {
            size: A4;
            margin: 1cm;
          }

          body {
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.4;
            color: black !important;
            background: white !important;
          }

          /* إخفاء جميع العناصر غير المطبوعة */
          .print\\:hidden, *[class*="print:hidden"],
          [class*="print:hidden"] {
            display: none !important;
            visibility: hidden !important;
          }

          /* إخفاء جميع العناصر التفاعلية */
          button, input[type="button"], input[type="submit"],
          .cursor-pointer, [onclick], [role="button"] {
            display: none !important;
            visibility: hidden !important;
          }

          /* إخفاء أزرار محددة بالكلاس */
          .bg-blue-600, .bg-gray-500, .bg-green-600 {
            display: none !important;
          }

          /* إخفاء MainLayout والعناصر الخارجية */
          nav, header, .sidebar, .navigation {
            display: none !important;
          }

          /* جعل النموذج يبدأ من أعلى الصفحة */
          .max-w-4xl {
            max-width: 100% !important;
            margin: 0 !important;
            padding: 0 !important;
          }

          .print\\:border-none {
            border: none !important;
          }

          .print\\:p-4 {
            padding: 1rem !important;
          }

          .print\\:bg-gray-200 {
            background-color: #e5e7eb !important;
          }

          /* إخفاء عناصر التنقل والأزرار والعناصر غير المرغوبة */
          nav, .sidebar, button, .no-print,
          .rounded-lg, .shadow-sm, .mb-6, .gap-3,
          .bg-green-600, .hover\\:bg-green-700,
          .bg-blue-600, .bg-gray-500, .hover\\:bg-blue-700, .hover\\:bg-gray-600 {
            display: none !important;
          }

          /* إخفاء جميع الأزرار في الطباعة */
          button, input[type="button"], input[type="submit"],
          .print\\:hidden, [class*="print:hidden"] {
            display: none !important;
          }

          /* إخفاء أزرار محددة */
          .flex.gap-4.pt-6.border-t,
          .bg-blue-600.hover\\:bg-blue-700,
          .bg-gray-500.hover\\:bg-gray-600 {
            display: none !important;
          }

          /* قاعدة شاملة لإخفاء جميع الأزرار */
          * button {
            display: none !important;
          }

          /* إخفاء العناصر التي تحتوي على كلمات مفتاحية */
          *[class*="button"], *[class*="btn"] {
            display: none !important;
          }

          /* إخفاء MainLayout تماماً */
          .main-layout, [data-layout="main"] {
            all: unset !important;
          }

          /* جعل النموذج يملأ الصفحة */
          html, body {
            margin: 0 !important;
            padding: 0 !important;
            width: 100% !important;
            height: 100% !important;
          }

          /* تحسين عرض الحدود */
          .border-black {
            border-color: black !important;
          }

          .border-dotted {
            border-style: dotted !important;
          }

          .border-b {
            border-bottom-width: 1px !important;
          }

          .border {
            border-width: 1px !important;
          }

          .border-2 {
            border-width: 2px !important;
          }

          /* تحسين عرض النصوص */
          input, textarea, select {
            border: none !important;
            background: transparent !important;
            color: black !important;
            font-size: 14px !important;
            outline: none !important;
            box-shadow: none !important;
          }

          /* إزالة الألوان الداكنة */
          * {
            color: black !important;
            background-color: transparent !important;
          }

          .bg-blue-200, .print\\:bg-gray-200 {
            background-color: #e5e7eb !important;
          }

          /* تحسين التخطيط */
          .grid {
            display: grid !important;
          }

          .grid-cols-3 {
            grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
          }

          .grid-cols-2 {
            grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
          }

          .grid-cols-4 {
            grid-template-columns: repeat(4, minmax(0, 1fr)) !important;
          }

          .gap-4 {
            gap: 1rem !important;
          }

          .gap-8 {
            gap: 2rem !important;
          }

          .text-center {
            text-align: center !important;
          }

          .text-sm {
            font-size: 0.875rem !important;
          }

          .text-lg {
            font-size: 1.125rem !important;
          }

          .font-bold {
            font-weight: bold !important;
          }

          .font-medium {
            font-weight: 500 !important;
          }

          .mb-2 {
            margin-bottom: 0.5rem !important;
          }

          .mb-4 {
            margin-bottom: 1rem !important;
          }

          .mb-6 {
            margin-bottom: 1.5rem !important;
          }

          .mt-8 {
            margin-top: 2rem !important;
          }

          .pt-8 {
            padding-top: 2rem !important;
          }

          .p-2 {
            padding: 0.5rem !important;
          }

          .p-3 {
            padding: 0.75rem !important;
          }

          .p-4 {
            padding: 1rem !important;
          }

          .pb-1 {
            padding-bottom: 0.25rem !important;
          }

          .h-12 {
            height: 3rem !important;
          }

          .h-4 {
            height: 1rem !important;
          }

          .min-h-24 {
            min-height: 6rem !important;
          }

          .w-20 {
            width: 5rem !important;
          }

          .w-full {
            width: 100% !important;
          }

          .space-y-3 > * + * {
            margin-top: 0.75rem !important;
          }

          .space-y-6 > * + * {
            margin-top: 1.5rem !important;
          }

          .flex {
            display: flex !important;
          }

          .items-center {
            align-items: center !important;
          }

          .items-start {
            align-items: flex-start !important;
          }

          .justify-center {
            justify-content: center !important;
          }

          .mr-2 {
            margin-right: 0.5rem !important;
          }

          .mr-auto {
            margin-right: auto !important;
          }

          .mx-auto {
            margin-left: auto !important;
            margin-right: auto !important;
          }

          .whitespace-nowrap {
            white-space: nowrap !important;
          }

          .resize-none {
            resize: none !important;
          }

          /* تحسينات إضافية للطباعة */
          .w-32 {
            width: 8rem !important;
          }

          .flex-1 {
            flex: 1 1 0% !important;
          }

          /* إخفاء أزرار البحث في الطباعة */
          button {
            display: none !important;
          }

          /* تحسين عرض التواريخ */
          input[type="date"], input[type="number"] {
            -webkit-appearance: none !important;
            -moz-appearance: textfield !important;
          }

          input[type="date"]::-webkit-calendar-picker-indicator {
            display: none !important;
          }
        }
      `}</style>
    </MainLayout>
  );
}
