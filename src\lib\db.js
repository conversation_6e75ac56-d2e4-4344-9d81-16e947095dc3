import sql from 'mssql';

const config = {
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  server: process.env.DB_SERVER,
  database: process.env.DB_NAME,
  options: {
    encrypt: false,
    trustServerCertificate: true,
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000
  }
};

let pool = null;

export async function getConnection() {
  try {
    if (!pool) {
      pool = await new sql.ConnectionPool(config).connect();

    }
    return pool;
  } catch (err) {

    throw err;
  }
}

// إغلاق الاتصال عند إيقاف التطبيق
process.on('SIGINT', async () => {
  if (pool) {
    try {
      await pool.close();

    } catch (err) {

    }
  }
  process.exit(0);
}); 