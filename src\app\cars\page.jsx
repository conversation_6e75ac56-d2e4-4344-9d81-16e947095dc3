'use client';

import CustomDateInput from '@/components/CustomDateInput';
import MainLayout from '@/components/MainLayout';
import { useEffect, useState } from 'react';
import {
    FiAlertTriangle,
    FiBarChart,
    FiDollarSign,
    FiEdit,
    FiEye,
    FiMapPin,
    FiPlus,
    FiRefreshCw,
    FiSearch,
    FiTrash2,
    FiTruck,
    FiUserMinus,
    FiUserPlus,
    FiUsers,
    FiUserX
} from 'react-icons/fi';

export default function CarsPage() {
  const [cars, setCars] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [showBeneficiaryModal, setShowBeneficiaryModal] = useState(false);
  const [showBeneficiariesListModal, setShowBeneficiariesListModal] = useState(false);
  const [editingCar, setEditingCar] = useState(null);
  const [selectedCar, setSelectedCar] = useState(null);
  const [statistics, setStatistics] = useState(null);
  const [editingInlineId, setEditingInlineId] = useState(null);
  const [inlineEditData, setInlineEditData] = useState({});

  const [formData, setFormData] = useState({
    carCode: '',
    contractorName: '',
    carNumber: '',
    carType: '',
    carModel: '',
    manufactureYear: '',
    route: '',
    rentAmount: '',
    notes: '',
    beneficiaries: []
  });

  const [beneficiaryForm, setBeneficiaryForm] = useState({
    employeeCode: '',
    employeeName: '',
    jobTitle: '',
    department: '',
    startDate: ''
  });

  // حالات البحث عن الموظفين
  const [employeeSearchQuery, setEmployeeSearchQuery] = useState('');
  const [employeeSearchResults, setEmployeeSearchResults] = useState([]);
  const [showEmployeeSearch, setShowEmployeeSearch] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [employeeStatus, setEmployeeStatus] = useState(null);

  useEffect(() => {
    loadCars();
    loadStatistics();
  }, []);

  // جلب السيارات
  const loadCars = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/cars', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getAll',
          search: searchTerm
        })
      });

      const result = await response.json();
      if (result.success) {
        setCars(result.data);
      } else {

      }
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  // جلب الإحصائيات
  const loadStatistics = async () => {
    try {
      const response = await fetch('/api/cars', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'getStatistics' })
      });

      const result = await response.json();
      if (result.success) {
        setStatistics(result.data);
      }
    } catch (error) {

    }
  };

  // إعداد النظام
  const setupSystem = async () => {
    try {
      const response = await fetch('/api/cars', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'setup' })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم إعداد نظام السيارات بنجاح');
        loadCars();
        loadStatistics();
      } else {
        alert('خطأ في إعداد النظام: ' + result.error);
      }
    } catch (error) {
      alert('خطأ في إعداد النظام: ' + error.message);
    }
  };

  // حفظ السيارة
  const saveCar = async (e) => {
    e.preventDefault();

    try {
      const action = editingCar ? 'update' : 'create';
      const payload = {
        action,
        ...formData,
        ...(editingCar && { carId: editingCar.ID })
      };

      const response = await fetch('/api/cars', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      const result = await response.json();
      if (result.success) {
        alert(editingCar ? 'تم تحديث السيارة بنجاح' : 'تم إنشاء السيارة بنجاح');
        setShowModal(false);
        resetForm();
        loadCars();
        loadStatistics();
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {
      alert('خطأ في حفظ السيارة: ' + error.message);
    }
  };

  // حذف السيارة
  const deleteCar = async (carId) => {
    if (!confirm('هل أنت متأكد من حذف هذه السيارة؟')) return;

    try {
      const response = await fetch('/api/cars', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'delete',
          carId
        })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم حذف السيارة بنجاح');
        loadCars();
        loadStatistics();
      } else {
        alert('خطأ في حذف السيارة: ' + result.error);
      }
    } catch (error) {
      alert('خطأ في حذف السيارة: ' + error.message);
    }
  };

  // البحث عن الموظفين
  const searchEmployees = async (searchTerm) => {
    if (!searchTerm || searchTerm.length < 2) {
      setEmployeeSearchResults([]);
      setShowEmployeeSearch(false);
      return;
    }

    try {
      const response = await fetch('/api/employee-live-search', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          searchTerm: searchTerm,
          limit: 10
        })
      });

      const result = await response.json();
      if (result.success) {
        const employees = result.data || [];
        const formattedEmployees = employees.map(emp => ({
          employeeCode: emp.EmployeeCode || emp.employeeCode,
          employeeName: emp.EmployeeName || emp.employeeName || emp.FullName,
          jobTitle: emp.JobTitle || emp.jobTitle,
          department: emp.Department || emp.department
        }));

        setEmployeeSearchResults(formattedEmployees);
        setShowEmployeeSearch(formattedEmployees.length > 0);
      }
    } catch (error) {

    }
  };

  // اختيار موظف من نتائج البحث
  const selectEmployee = async (employee) => {
    setBeneficiaryForm(prev => ({
      ...prev,
      employeeCode: employee.employeeCode,
      employeeName: employee.employeeName,
      jobTitle: employee.jobTitle,
      department: employee.department
    }));
    setSelectedEmployee(employee);
    setEmployeeSearchQuery(`${employee.employeeName} (${employee.employeeCode})`);
    setShowEmployeeSearch(false);

    // التحقق من حالة الموظف الحالية
    await checkEmployeeStatus(employee.employeeCode);
  };

  // التحقق من حالة الموظف
  const checkEmployeeStatus = async (employeeCode) => {
    try {
      const response = await fetch('/api/cars', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'checkEmployeeStatus',
          employeeCode: employeeCode
        })
      });

      const result = await response.json();
      if (result.success && result.data) {
        const status = result.data;
        setEmployeeStatus(status);

        if (status.isActive) {
          alert(`⚠️ تنبيه: الموظف مسجل حالياً في السيارة:\n\n` +
                `🚗 كود السيارة: ${status.carCode}\n` +
                `🏢 المقاول: ${status.contractorName}\n` +
                `🚙 رقم السيارة: ${status.carNumber}\n` +
                `📅 تاريخ البداية: ${status.startDate}\n\n` +
                `يجب إنهاء استفادته من السيارة الحالية أولاً.`);
        }
      }
    } catch (error) {

      setEmployeeStatus(null);
    }
  };

  // إعادة تعيين نموذج المستفيد
  const resetBeneficiaryForm = () => {
    setBeneficiaryForm({
      employeeCode: '',
      employeeName: '',
      jobTitle: '',
      department: '',
      startDate: ''
    });
    setSelectedEmployee(null);
    setEmployeeSearchQuery('');
    setEmployeeSearchResults([]);
    setShowEmployeeSearch(false);
    setEmployeeStatus(null);
  };

  // إضافة مستفيد
  const addBeneficiary = async (e) => {
    e.preventDefault();

    if (!selectedEmployee) {
      alert('يرجى اختيار موظف من قائمة البحث');
      return;
    }

    try {
      const requestData = {
        action: 'addBeneficiary',
        carId: selectedCar.ID,
        employeeCode: beneficiaryForm.employeeCode,
        startDate: beneficiaryForm.startDate
      };

      console.log('إرسال بيانات المستفيد:', requestData);

      const response = await fetch('/api/cars', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData)
      });

      const result = await response.json();
      if (result.success) {
        alert('✅ تم إضافة المستفيد بنجاح');
        setShowBeneficiaryModal(false);
        resetBeneficiaryForm();
        loadCars();
      } else {
        // عرض رسالة خطأ مفصلة
        const errorMessage = result.error || 'خطأ غير معروف';
        console.error('تفاصيل الخطأ:', result);

        if (errorMessage.includes('مسجل بالفعل')) {
          alert('⚠️ تعذر إضافة المستفيد:\n\n' + errorMessage + '\n\nيرجى التحقق من حالة الموظف الحالية.');
        } else {
          let detailedError = '❌ خطأ في إضافة المستفيد:\n\n' + errorMessage;

          if (result.details) {
            detailedError += '\n\nتفاصيل إضافية:';
            detailedError += '\n- كود السيارة: ' + (result.details.carId || 'غير محدد');
            detailedError += '\n- كود الموظف: ' + (result.details.employeeCode || 'غير محدد');
            detailedError += '\n- تاريخ البداية: ' + (result.details.startDate || 'غير محدد');
          }

          alert(detailedError);
        }
      }
    } catch (error) {
      alert('❌ خطأ في الاتصال بالخادم:\n\n' + error.message);
    }
  };

  // إزالة مستفيد
  const removeBeneficiary = async (beneficiaryId) => {
    if (!confirm('هل أنت متأكد من إزالة هذا المستفيد؟')) return;

    try {
      const response = await fetch('/api/cars', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'removeBeneficiary',
          beneficiaryId
        })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم إزالة المستفيد بنجاح');
        loadCars();
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {
      alert('خطأ في إزالة المستفيد: ' + error.message);
    }
  };

  // إعادة تعيين النموذج
  const resetForm = () => {
    setFormData({
      carCode: '',
      contractorName: '',
      carNumber: '',
      carType: '',
      carModel: '',
      manufactureYear: '',
      route: '',
      rentAmount: '',
      notes: '',
      beneficiaries: []
    });
    setEditingCar(null);
  };

  // فتح نموذج التعديل
  const openEditModal = (car) => {
    setFormData({
      carCode: car.CarCode,
      contractorName: car.ContractorName,
      carNumber: car.CarNumber,
      carType: car.CarType,
      carModel: car.CarModel,
      manufactureYear: car.ManufactureYear || '',
      route: car.Route,
      rentAmount: car.RentAmount,
      notes: car.Notes || '',
      beneficiaries: car.beneficiaries || []
    });
    setEditingCar(car);
    setShowModal(true);
  };

  // بدء التعديل المباشر
  const startInlineEdit = (car) => {
    setEditingInlineId(car.ID);
    setInlineEditData({
      carCode: car.CarCode,
      contractorName: car.ContractorName,
      carNumber: car.CarNumber,
      carType: car.CarType,
      carModel: car.CarModel,
      manufactureYear: car.ManufactureYear || '',
      route: car.Route,
      rentAmount: car.RentAmount,
      notes: car.Notes || ''
    });
  };

  // إلغاء التعديل المباشر
  const cancelInlineEdit = () => {
    setEditingInlineId(null);
    setInlineEditData({});
  };

  // حفظ التعديل المباشر
  const saveInlineEdit = async () => {
    try {
      const response = await fetch('/api/cars', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'update',
          carId: editingInlineId,
          ...inlineEditData
        })
      });

      const result = await response.json();
      if (result.success) {
        alert('تم تحديث السيارة بنجاح');
        setEditingInlineId(null);
        setInlineEditData({});
        loadCars();
        loadStatistics();
      } else {
        alert('خطأ: ' + result.error);
      }
    } catch (error) {
      alert('خطأ في حفظ التعديل: ' + error.message);
    }
  };

  const handleInlineEditChange = (field, value) => {
    setInlineEditData(prev => ({ ...prev, [field]: value }));
  };

  // تنسيق العملة
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('ar-EG', {
      style: 'currency',
      currency: 'EGP',
      minimumFractionDigits: 0
    }).format(amount || 0);
  };

  return (
    <MainLayout>
      <div className="max-w-full mx-auto px-4">
        {/* رأس الصفحة */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FiTruck className="text-3xl text-blue-600" />
              <div>
                <h1 className="text-3xl font-bold text-gray-800">إدارة السيارات</h1>
                <p className="text-gray-600">إدارة سيارات الشركة والمستفيدين</p>
              </div>
            </div>
            <div className="flex gap-3">
              <button
                onClick={setupSystem}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
              >
                <FiRefreshCw />
                إعداد النظام
              </button>
              <button
                onClick={() => {
                  resetForm();
                  setShowModal(true);
                }}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
              >
                <FiPlus />
                إضافة سيارة جديدة
              </button>
            </div>
          </div>
        </div>

        {/* الإحصائيات */}
        {statistics && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-blue-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي السيارات النشطة</p>
                  <p className="text-3xl font-bold text-blue-600">{statistics.TotalActiveCars || 0}</p>
                </div>
                <FiTruck className="text-2xl text-blue-600" />
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-slate-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي المستفيدين</p>
                  <p className="text-3xl font-bold text-slate-600">{statistics.TotalActiveBeneficiaries || 0}</p>
                </div>
                <FiUsers className="text-2xl text-slate-600" />
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-emerald-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">إجمالي الإيجار الشهري</p>
                  <p className="text-2xl font-bold text-emerald-600">{formatCurrency(statistics.TotalMonthlyRent)}</p>
                </div>
                <FiDollarSign className="text-2xl text-emerald-600" />
              </div>
            </div>

            <div className="bg-white rounded-lg shadow-sm p-6 border-l-4 border-orange-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">عدد الخطوط</p>
                  <p className="text-3xl font-bold text-orange-600">{statistics.TotalRoutes || 0}</p>
                </div>
                <FiMapPin className="text-2xl text-orange-600" />
              </div>
            </div>
          </div>
        )}

        {/* شريط البحث والفلترة */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center gap-4">
            <div className="flex-1">
              <div className="relative">
                <FiSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="البحث في السيارات (كود، مقاول، رقم، خط سير)..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <button
              onClick={loadCars}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center gap-2"
            >
              <FiSearch />
              بحث
            </button>
            <button
              onClick={() => {
                setSearchTerm('');
                loadCars();
              }}
              className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center gap-2"
            >
              <FiRefreshCw />
              إعادة تعيين
            </button>
          </div>
        </div>

        {/* جدول السيارات */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-800">قائمة السيارات</h3>
          </div>

          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600 mt-4">جاري تحميل السيارات...</p>
            </div>
          ) : cars.length === 0 ? (
            <div className="text-center py-12">
              <FiTruck className="text-6xl text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-medium text-gray-600 mb-2">لا توجد سيارات</h3>
              <p className="text-gray-500 mb-4">لم يتم العثور على سيارات مطابقة للبحث</p>
              <button
                onClick={() => {
                  resetForm();
                  setShowModal(true);
                }}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                إضافة أول سيارة
              </button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full min-w-full divide-y divide-gray-200 table-fixed">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[10%]">
                      كود السيارة
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[15%]">
                      المقاول
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[10%]">
                      رقم السيارة
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[15%]">
                      النوع والموديل
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[12%]">
                      خط السير
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[25%]">
                      المستفيدين
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[10%]">
                      القيمة الإيجارية
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[8%]">
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {cars.map((car) => (
                    <tr key={car.ID} className="hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                      <td className="px-4 py-4 text-sm font-medium text-gray-900 text-center">
                        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-bold">
                          {car.CarCode}
                        </span>
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-900">
                        <div className="truncate" title={car.ContractorName}>
                          {car.ContractorName}
                        </div>
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-900 text-center">
                        <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs font-mono">
                          {car.CarNumber}
                        </span>
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-900">
                        <div>
                          <div className="font-medium truncate" title={car.CarType}>{car.CarType}</div>
                          <div className="text-gray-500 text-xs truncate" title={`${car.CarModel} ${car.ManufactureYear ? `(${car.ManufactureYear})` : ''}`}>
                            {car.CarModel} {car.ManufactureYear && `(${car.ManufactureYear})`}
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-900">
                        <div className="truncate" title={car.Route}>
                          {car.Route}
                        </div>
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-900">
                        <div className="space-y-2 min-w-0">
                          {/* عرض المستفيدين النشطين */}
                          {car.beneficiaries && car.beneficiaries.filter(b => b.IsActive).length > 0 ? (
                            <div className="space-y-1">
                              {car.beneficiaries.filter(b => b.IsActive).slice(0, 2).map((beneficiary) => (
                                <div key={beneficiary.ID} className="flex items-center gap-1 text-xs bg-gray-50 p-2 rounded border">
                                  <span className="bg-slate-100 text-slate-800 px-2 py-1 rounded-full font-medium text-xs whitespace-nowrap">
                                    {beneficiary.EmployeeCode}
                                  </span>
                                  <div className="flex-1 min-w-0">
                                    <div className="font-medium text-gray-900 break-words text-xs" title={beneficiary.EmployeeName}>
                                      {beneficiary.EmployeeName}
                                    </div>
                                    <div className="text-gray-500 break-words text-xs" title={beneficiary.JobTitle}>
                                      {beneficiary.JobTitle}
                                    </div>
                                  </div>
                                  <button
                                    onClick={() => removeBeneficiary(beneficiary.ID)}
                                    className="text-red-600 hover:text-red-900 flex-shrink-0"
                                    title="إزالة المستفيد"
                                  >
                                    <FiUserMinus className="w-3 h-3" />
                                  </button>
                                </div>
                              ))}
                              {car.beneficiaries.filter(b => b.IsActive).length > 2 && (
                                <button
                                  onClick={() => {
                                    setSelectedCar(car);
                                    setShowBeneficiariesListModal(true);
                                  }}
                                  className="text-blue-600 hover:text-blue-900 text-xs bg-blue-50 px-2 py-1 rounded w-full"
                                >
                                  +{car.beneficiaries.filter(b => b.IsActive).length - 2} مستفيد آخر
                                </button>
                              )}
                            </div>
                          ) : (
                            <div className="text-center py-2">
                              <span className="text-gray-400 text-xs">لا يوجد مستفيدين</span>
                            </div>
                          )}

                          {/* أزرار إدارة المستفيدين */}
                          <div className="flex gap-1 mt-2">
                            <button
                              onClick={() => {
                                setSelectedCar(car);
                                resetBeneficiaryForm();
                                setShowBeneficiaryModal(true);
                              }}
                              className="text-blue-600 hover:text-blue-900 text-xs flex items-center gap-1 bg-blue-50 px-2 py-1 rounded flex-1 justify-center"
                              title="إضافة مستفيد"
                            >
                              <FiUserPlus className="w-3 h-3" />
                              إضافة
                            </button>

                            {car.beneficiaries && car.beneficiaries.length > 0 && (
                              <button
                                onClick={() => {
                                  setSelectedCar(car);
                                  setShowBeneficiariesListModal(true);
                                }}
                                className="text-slate-600 hover:text-slate-900 text-xs flex items-center gap-1 bg-slate-50 px-2 py-1 rounded flex-1 justify-center"
                                title="عرض جميع المستفيدين"
                              >
                                <FiEye className="w-3 h-3" />
                                عرض ({car.beneficiaries.filter(b => b.IsActive).length})
                              </button>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-900 text-center">
                        <div className="bg-emerald-100 text-emerald-800 px-3 py-1 rounded font-bold text-sm">
                          {formatCurrency(car.RentAmount)}
                        </div>
                      </td>
                      <td className="px-4 py-4 text-sm font-medium">
                        <div className="flex gap-2 justify-center">
                          <button
                            onClick={() => openEditModal(car)}
                            className="text-blue-600 hover:text-blue-900 bg-blue-50 hover:bg-blue-100 p-2 rounded transition-colors"
                            title="تعديل"
                          >
                            <FiEdit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => deleteCar(car.ID)}
                            className="text-red-600 hover:text-red-900 bg-red-50 hover:bg-red-100 p-2 rounded transition-colors"
                            title="حذف"
                          >
                            <FiTrash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
                {statistics && (
                  <tfoot className="bg-gray-800 dark:bg-gray-950 border-t-4 border-blue-600 dark:border-blue-400">
                    <tr className="font-extrabold text-gray-900 dark:text-white">
                      {/* Cell for the general "الإجمالي" label - Aligned with first column (كود السيارة) */}
                      <td className="px-6 py-4 text-right" colSpan={1} style={{ minWidth: '150px', whiteSpace: 'nowrap' }}>
                        <div className="flex items-center gap-2 justify-end text-3xl font-bold text-blue-400" style={{ whiteSpace: 'nowrap', wordBreak: 'keep-all' }}>
                            <FiBarChart className="text-3xl" />
                            <span style={{ whiteSpace: 'nowrap', wordBreak: 'keep-all', display: 'inline-block' }}>الإجمالي</span>
                        </div>
                      </td>

                      {/* Empty cell for spacing, replicating the first dash in the apartment example - Aligned with المقاول */}
                      <td className="px-6 py-4 text-center text-gray-500" colSpan={1}>
                        <span className="text-2xl font-bold">-</span>
                      </td>

                      {/* Empty cell for spacing, replicating the second dash in the apartment example - Aligned with رقم السيارة */}
                      <td className="px-6 py-4 text-center text-gray-500" colSpan={1}>
                        <span className="text-2xl font-bold">-</span>
                      </td>

                      {/* Cell for combined "إجمالي المستفيدين" and "إجمالي عدد السيارات" - Aligned with النوع والموديل and خط السير */}
                      <td className="px-6 py-4 text-right" colSpan={2}>
                        <div className="flex flex-col items-end gap-1 text-lg">
                            <div className="flex items-center gap-1 text-orange-400">
                                <FiUsers className="text-2xl" />
                                <span className="font-bold">{statistics.TotalActiveBeneficiaries || 0}</span>
                                <span className="text-base">مستفيد</span>
                            </div>
                            <div className="flex items-center gap-1 text-blue-400">
                                <FiTruck className="text-2xl" />
                                <span className="font-bold">{statistics.TotalActiveCars || 0}</span>
                                <span className="text-base">سيارة</span>
                            </div>
                        </div>
                      </td>

                      {/* Cell for "إجمالي القيمة الإيجارية" - Aligned with المستفيدين and القيمة الإيجارية */}
                      <td className="px-6 py-4 text-right" colSpan={2}>
                        <div className="flex items-center gap-1 justify-end text-xl font-bold text-emerald-400">
                            <FiDollarSign className="text-2xl" />
                            <span>{formatCurrency(statistics.TotalMonthlyRent)}</span>
                        </div>
                      </td>

                      {/* Final empty cell for remaining space - Aligned with الإجراءات */}
                      <td className="px-6 py-4" colSpan={1}>
                      </td>
                    </tr>
                  </tfoot>
                )}
              </table>
            </div>
          )}
        </div>

        {/* مودال إضافة/تعديل السيارة */}
        {showModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between p-6 border-b">
                <h3 className="text-xl font-bold text-gray-800">
                  {editingCar ? 'تعديل السيارة' : 'إضافة سيارة جديدة'}
                </h3>
                <button
                  onClick={() => setShowModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <form onSubmit={saveCar} className="p-6 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      كود السيارة *
                    </label>
                    <input
                      type="text"
                      value={formData.carCode}
                      onChange={(e) => setFormData(prev => ({ ...prev, carCode: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      placeholder="مثال: CAR-001"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      اسم المقاول *
                    </label>
                    <input
                      type="text"
                      value={formData.contractorName}
                      onChange={(e) => setFormData(prev => ({ ...prev, contractorName: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      placeholder="اسم شركة أو مقاول النقل"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      رقم السيارة *
                    </label>
                    <input
                      type="text"
                      value={formData.carNumber}
                      onChange={(e) => setFormData(prev => ({ ...prev, carNumber: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      placeholder="مثال: أ ب ج 123"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      نوع السيارة *
                    </label>
                    <input
                      type="text"
                      value={formData.carType}
                      onChange={(e) => setFormData(prev => ({ ...prev, carType: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      placeholder="مثال: ميكروباص، أتوبيس، سيارة خاصة"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      موديل السيارة *
                    </label>
                    <input
                      type="text"
                      value={formData.carModel}
                      onChange={(e) => setFormData(prev => ({ ...prev, carModel: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      placeholder="مثال: هيونداي H1، مرسيدس سبرينتر"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      سنة الصنع
                    </label>
                    <input
                      type="number"
                      min="1990"
                      max="2030"
                      value={formData.manufactureYear}
                      onChange={(e) => setFormData(prev => ({ ...prev, manufactureYear: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      placeholder="مثال: 2020"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    خط السير *
                  </label>
                  <input
                    type="text"
                    value={formData.route}
                    onChange={(e) => setFormData(prev => ({ ...prev, route: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="مثال: القاهرة - الجيزة، المعادي - مدينة نصر"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    القيمة الإيجارية (ج.م) *
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.rentAmount}
                    onChange={(e) => setFormData(prev => ({ ...prev, rentAmount: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="0.00"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    ملاحظات
                  </label>
                  <textarea
                    value={formData.notes}
                    onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    rows={3}
                    placeholder="ملاحظات إضافية..."
                  />
                </div>

                <div className="flex gap-3 pt-4 border-t">
                  <button
                    type="submit"
                    className="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 flex items-center gap-2"
                  >
                    <FiTruck />
                    {editingCar ? 'تحديث السيارة' : 'إضافة السيارة'}
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowModal(false)}
                    className="bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400"
                  >
                    إلغاء
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* مودال إضافة مستفيد */}
        {showBeneficiaryModal && selectedCar && (
          <div className="fixed inset-0 bg-black bg-opacity-60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
            <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-3xl w-full max-h-[95vh] overflow-y-auto border border-gray-200 dark:border-gray-700">
              {/* Header */}
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6 rounded-t-2xl">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="bg-white bg-opacity-20 p-3 rounded-full">
                      <FiUserPlus className="text-2xl text-white" />
                    </div>
                    <div>
                      <h3 className="text-2xl font-bold text-white">
                        إضافة مستفيد جديد
                      </h3>
                      <p className="text-blue-100 mt-1">
                        السيارة: {selectedCar.CarCode} - {selectedCar.ContractorName}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowBeneficiaryModal(false)}
                    className="text-white hover:text-red-200 text-2xl bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full p-2 transition-all duration-200"
                  >
                    ✕
                  </button>
                </div>
              </div>

              <form onSubmit={addBeneficiary} className="p-8 space-y-8">
                {/* قسم البحث عن الموظف */}
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 rounded-xl border border-blue-200 dark:border-blue-700">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="bg-blue-600 p-2 rounded-lg">
                      <FiSearch className="text-white text-lg" />
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-800 dark:text-white">البحث عن الموظف</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">ابحث عن الموظف المراد إضافته كمستفيد</p>
                    </div>
                  </div>
                  <div className="relative">
                    <input
                      type="text"
                      value={employeeSearchQuery}
                      onChange={(e) => {
                        setEmployeeSearchQuery(e.target.value);
                        searchEmployees(e.target.value);
                      }}
                      onFocus={() => {
                        if (employeeSearchResults.length > 0) {
                          setShowEmployeeSearch(true);
                        }
                      }}
                      className="w-full px-6 py-4 pr-14 border-2 border-blue-300 dark:border-blue-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white text-lg font-medium transition-all duration-200 shadow-sm"
                      placeholder="ابحث بالاسم أو كود الموظف..."
                      required
                    />
                    <div className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-blue-600 p-2 rounded-lg">
                      <FiSearch className="text-white text-lg" />
                    </div>

                    {/* نتائج البحث */}
                    {showEmployeeSearch && employeeSearchResults.length > 0 && (
                      <div className="absolute top-full left-0 right-0 bg-white dark:bg-gray-800 border-2 border-blue-200 dark:border-blue-600 rounded-xl shadow-2xl z-50 mt-2 max-h-64 overflow-y-auto">
                        <div className="p-3 bg-blue-50 dark:bg-blue-900/30 border-b border-blue-200 dark:border-blue-600">
                          <p className="text-sm font-medium text-blue-800 dark:text-blue-300">
                            تم العثور على {employeeSearchResults.length} موظف
                          </p>
                        </div>
                        {employeeSearchResults.map((employee, index) => (
                          <button
                            key={index}
                            type="button"
                            onClick={() => selectEmployee(employee)}
                            className="w-full px-4 py-3 text-right hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/20 dark:hover:to-indigo-900/20 border-b border-gray-100 dark:border-gray-600 last:border-b-0 transition-all duration-200 group"
                          >
                            <div className="flex items-center justify-between">
                              <div className="text-right flex-1">
                                <div className="font-bold text-gray-900 dark:text-white group-hover:text-blue-700 dark:group-hover:text-blue-300 transition-colors">
                                  {employee.employeeName}
                                </div>
                                <div className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                                  {employee.jobTitle} - {employee.department}
                                </div>
                              </div>
                              <div className="bg-blue-600 text-white px-3 py-1 rounded-lg font-bold text-sm group-hover:bg-blue-700 transition-colors">
                                #{employee.employeeCode}
                              </div>
                            </div>
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* عرض بيانات الموظف المختار */}
                {selectedEmployee && (
                  <div className="bg-gradient-to-br from-slate-50 to-gray-50 dark:from-slate-900/20 dark:to-gray-900/20 border-2 border-slate-200 dark:border-slate-700 rounded-xl p-6 shadow-lg">
                    <div className="flex items-center gap-3 mb-6">
                      <div className="bg-slate-600 p-3 rounded-xl">
                        <FiUsers className="text-white text-xl" />
                      </div>
                      <div>
                        <h4 className="text-xl font-bold text-slate-900 dark:text-slate-300">بيانات الموظف المختار</h4>
                        <p className="text-sm text-slate-700 dark:text-slate-400">تم اختيار الموظف بنجاح</p>
                      </div>
                      <div className="mr-auto">
                        <div className="bg-emerald-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                          ✓ محدد
                        </div>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-white dark:bg-gray-700 p-4 rounded-xl shadow-sm border border-gray-200 dark:border-gray-600">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                          <span className="text-gray-600 dark:text-gray-400 text-sm font-medium">كود الموظف</span>
                        </div>
                        <span className="font-bold text-blue-600 dark:text-blue-400 text-xl">#{selectedEmployee.employeeCode}</span>
                      </div>
                      <div className="bg-white dark:bg-gray-700 p-4 rounded-xl shadow-sm border border-gray-200 dark:border-gray-600">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                          <span className="text-gray-600 dark:text-gray-400 text-sm font-medium">الاسم</span>
                        </div>
                        <span className="font-bold text-gray-900 dark:text-white text-lg">{selectedEmployee.employeeName}</span>
                      </div>
                      <div className="bg-white dark:bg-gray-700 p-4 rounded-xl shadow-sm border border-gray-200 dark:border-gray-600">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="w-2 h-2 bg-orange-600 rounded-full"></div>
                          <span className="text-gray-600 dark:text-gray-400 text-sm font-medium">المسمى الوظيفي</span>
                        </div>
                        <span className="font-medium text-gray-900 dark:text-white">{selectedEmployee.jobTitle}</span>
                      </div>
                      <div className="bg-white dark:bg-gray-700 p-4 rounded-xl shadow-sm border border-gray-200 dark:border-gray-600">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="w-2 h-2 bg-teal-600 rounded-full"></div>
                          <span className="text-gray-600 dark:text-gray-400 text-sm font-medium">القسم</span>
                        </div>
                        <span className="font-medium text-gray-900 dark:text-white">{selectedEmployee.department}</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* تنبيه حالة الموظف */}
                {employeeStatus && employeeStatus.isActive && (
                  <div className="bg-gradient-to-br from-red-50 to-orange-50 border-2 border-red-200 rounded-xl p-6 shadow-lg">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="bg-red-600 p-3 rounded-xl">
                        <FiAlertTriangle className="text-white text-xl" />
                      </div>
                      <div>
                        <h4 className="text-xl font-bold text-red-900">⚠️ تحذير: الموظف مسجل في سيارة أخرى</h4>
                        <p className="text-sm text-red-700">يجب إنهاء استفادته من السيارة الحالية أولاً</p>
                      </div>
                    </div>
                    <div className="bg-white p-4 rounded-lg border border-red-200">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                        <div>
                          <span className="text-red-600 font-medium">🚗 كود السيارة:</span>
                          <span className="mr-2 font-bold">{employeeStatus.carCode}</span>
                        </div>
                        <div>
                          <span className="text-red-600 font-medium">🏢 المقاول:</span>
                          <span className="mr-2 font-bold">{employeeStatus.contractorName}</span>
                        </div>
                        <div>
                          <span className="text-red-600 font-medium">🚙 رقم السيارة:</span>
                          <span className="mr-2 font-bold">{employeeStatus.carNumber}</span>
                        </div>
                        <div>
                          <span className="text-red-600 font-medium">📅 تاريخ البداية:</span>
                          <span className="mr-2 font-bold">{employeeStatus.startDate}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* قسم التاريخ */}
                <CustomDateInput
                  key="beneficiary-date-input-v2"
                  value={beneficiaryForm.startDate}
                  onChange={(e) => setBeneficiaryForm(prev => ({ ...prev, startDate: e.target.value }))}
                  required={true}
                  label="تاريخ بداية الاستفادة *"
                  id="beneficiary-start-date"
                />

                {/* الأزرار */}
                <div className="flex gap-4 pt-8 border-t-2 border-gray-200 dark:border-gray-700">
                  <button
                    type="submit"
                    disabled={!selectedEmployee}
                    className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-4 px-8 rounded-xl hover:from-blue-700 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed flex items-center justify-center gap-3 font-bold text-lg transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none"
                  >
                    <div className="bg-white bg-opacity-20 p-2 rounded-lg">
                      <FiUserPlus className="text-xl" />
                    </div>
                    إضافة المستفيد
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowBeneficiaryModal(false)}
                    className="bg-gradient-to-r from-gray-400 to-gray-500 dark:from-gray-600 dark:to-gray-700 text-white py-4 px-8 rounded-xl hover:from-gray-500 hover:to-gray-600 dark:hover:from-gray-700 dark:hover:to-gray-800 transition-all duration-200 font-medium text-lg shadow-lg hover:shadow-xl transform hover:scale-105"
                  >
                    إلغاء
                  </button>
                </div>
              </form>
            </div>
          </div>
        )}

        {/* مودال عرض قائمة المستفيدين */}
        {showBeneficiariesListModal && selectedCar && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="flex items-center justify-between p-6 border-b">
                <h3 className="text-xl font-bold text-gray-800">
                  مستفيدي السيارة - {selectedCar.CarCode}
                </h3>
                <button
                  onClick={() => setShowBeneficiariesListModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <div className="p-6">
                {selectedCar.beneficiaries && selectedCar.beneficiaries.length > 0 ? (
                  <div className="space-y-4">
                    {/* المستفيدين النشطين */}
                    <div>
                      <h4 className="font-semibold text-blue-800 mb-3 flex items-center gap-2">
                        <FiUsers className="text-blue-600" />
                        المستفيدين النشطين ({selectedCar.beneficiaries.filter(b => b.IsActive).length})
                      </h4>
                      <div className="space-y-2">
                        {selectedCar.beneficiaries.filter(b => b.IsActive).map((beneficiary) => (
                          <div key={beneficiary.ID} className="flex items-center justify-between bg-blue-50 border border-blue-200 p-3 rounded-lg">
                            <div className="flex items-center gap-3">
                              <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full font-bold text-sm">
                                {beneficiary.EmployeeCode}
                              </span>
                              <div>
                                <div className="font-medium text-gray-900">{beneficiary.EmployeeName}</div>
                                <div className="text-sm text-gray-600">{beneficiary.JobTitle} - {beneficiary.Department}</div>
                                <div className="text-xs text-gray-500">
                                  بداية الاستفادة: {new Date(beneficiary.StartDate).toLocaleDateString('ar-EG')}
                                </div>
                              </div>
                            </div>
                            <button
                              onClick={() => {
                                removeBeneficiary(beneficiary.ID);
                                setShowBeneficiariesListModal(false);
                              }}
                              className="text-red-600 hover:text-red-900 bg-red-50 hover:bg-red-100 p-2 rounded transition-colors"
                              title="إزالة المستفيد"
                            >
                              <FiUserMinus className="w-4 h-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* المستفيدين غير النشطين */}
                    {selectedCar.beneficiaries.filter(b => !b.IsActive).length > 0 && (
                      <div>
                        <h4 className="font-semibold text-gray-600 mb-3 flex items-center gap-2">
                          <FiUserX className="text-gray-500" />
                          المستفيدين السابقين ({selectedCar.beneficiaries.filter(b => !b.IsActive).length})
                        </h4>
                        <div className="space-y-2">
                          {selectedCar.beneficiaries.filter(b => !b.IsActive).map((beneficiary) => (
                            <div key={beneficiary.ID} className="flex items-center justify-between bg-gray-50 border border-gray-200 p-3 rounded-lg">
                              <div className="flex items-center gap-3">
                                <span className="bg-gray-100 text-gray-600 px-3 py-1 rounded-full font-bold text-sm">
                                  {beneficiary.EmployeeCode}
                                </span>
                                <div>
                                  <div className="font-medium text-gray-700">{beneficiary.EmployeeName}</div>
                                  <div className="text-sm text-gray-500">{beneficiary.JobTitle} - {beneficiary.Department}</div>
                                  <div className="text-xs text-gray-400">
                                    من {new Date(beneficiary.StartDate).toLocaleDateString('ar-EG')}
                                    {beneficiary.EndDate && ` إلى ${new Date(beneficiary.EndDate).toLocaleDateString('ar-EG')}`}
                                  </div>
                                </div>
                              </div>
                              <span className="text-gray-400 text-sm">منتهي</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <FiUsers className="text-6xl text-gray-300 mx-auto mb-4" />
                    <h3 className="text-xl font-medium text-gray-600 mb-2">لا يوجد مستفيدين</h3>
                    <p className="text-gray-500">لم يتم إضافة أي مستفيدين لهذه السيارة بعد</p>
                  </div>
                )}

                <div className="flex gap-3 pt-4 border-t mt-6">
                  <button
                    onClick={() => {
                      setShowBeneficiariesListModal(false);
                      resetBeneficiaryForm();
                      setShowBeneficiaryModal(true);
                    }}
                    className="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 flex items-center gap-2"
                  >
                    <FiUserPlus />
                    إضافة مستفيد جديد
                  </button>
                  <button
                    onClick={() => setShowBeneficiariesListModal(false)}
                    className="bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400"
                  >
                    إغلاق
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
