-- جدول رصيد الإجازات
CREATE TABLE IF NOT EXISTS leave_balances (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_code VARCHAR(20) NOT NULL UNIQUE,
    employee_name VARCHAR(100) NOT NULL,
    job_title VARCHAR(100) NOT NULL,
    regular_balance INT DEFAULT 15, -- رصيد إعتيادي (15 يوم)
    casual_balance INT DEFAULT 6,  -- رصيد عارضة (6 أيام)
    year YEAR DEFAULT YEAR(CURDATE()),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_employee_code (employee_code),
    INDEX idx_year (year)
);

-- جدول طلبات الإجازات
CREATE TABLE IF NOT EXISTS leave_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_code VARCHAR(20) NOT NULL,
    employee_name VARCHAR(100) NOT NULL,
    job_title VARCHAR(100) NOT NULL,
    department VARCHAR(100),
    project VARCHAR(100),
    leave_type ENUM('اعتيادية', 'عارضة', 'مرضية', 'أمومة', 'أبوة', 'بدل', 'بدون راتب', 'أخرى') NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    days_count INT NOT NULL,
    reason TEXT,
    status ENUM('قيد المراجعة', 'معتمد', 'مرفوض') DEFAULT 'قيد المراجعة',
    request_date DATE DEFAULT CURDATE(),
    employee_signature VARCHAR(100),
    direct_manager_signature VARCHAR(100),
    project_manager_signature VARCHAR(100),
    hr_manager_signature VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_employee_code (employee_code),
    INDEX idx_status (status),
    INDEX idx_leave_type (leave_type),
    INDEX idx_dates (start_date, end_date),
    FOREIGN KEY (employee_code) REFERENCES employees(EmployeeCode) ON UPDATE CASCADE
);

-- إدراج بيانات رصيد الإجازات للموظفين الموجودين
INSERT IGNORE INTO leave_balances (employee_code, employee_name, job_title)
SELECT 
    EmployeeCode,
    CONCAT(FirstName, ' ', LastName) as employee_name,
    JobTitle
FROM employees 
WHERE EmployeeCode IS NOT NULL AND EmployeeCode != '';

-- تحديث أرصدة الإجازات للسنة الحالية
UPDATE leave_balances 
SET 
    regular_balance = 15,
    casual_balance = 6,
    year = YEAR(CURDATE())
WHERE year != YEAR(CURDATE());
