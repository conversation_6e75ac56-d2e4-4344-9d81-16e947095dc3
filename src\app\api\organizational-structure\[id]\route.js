import { NextResponse } from 'next/server';
import sql from 'mssql';

// إعدادات قاعدة البيانات
const dbConfig = {
  user: 'SA',
  password: 'admin@123',
  server: 'localhost\\DBOJESTA',
  database: 'EMP',
  options: {
    encrypt: false,
    trustServerCertificate: true,
    enableArithAbort: true,
    instanceName: 'DBOJESTA'
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
  requestTimeout: 30000,
  connectionTimeout: 30000,
};

// DELETE - حذف وحدة تنظيمية
export async function DELETE(request, { params }) {
  let pool;
  
  try {
    const { id } = params;

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف الوحدة مطلوب'
      }, { status: 400 });
    }

    pool = await sql.connect(dbConfig);

    // التحقق من وجود وحدات فرعية
    const childrenResult = await pool.request()
      .input('parentId', id)
      .query('SELECT COUNT(*) as count FROM OrganizationalUnits WHERE ParentUnitID = @parentId AND IsActive = 1');

    if (childrenResult.recordset[0].count > 0) {
      return NextResponse.json({
        success: false,
        error: 'لا يمكن حذف هذه الوحدة لأنها تحتوي على وحدات فرعية'
      }, { status: 400 });
    }

    // التحقق من وجود موظفين مرتبطين
    const employeesResult = await pool.request()
      .input('unitId', id)
      .query('SELECT COUNT(*) as count FROM EmployeeUnits WHERE UnitID = @unitId AND IsActive = 1');

    if (employeesResult.recordset[0].count > 1) { // أكثر من المدير
      return NextResponse.json({
        success: false,
        error: 'لا يمكن حذف هذه الوحدة لأنها تحتوي على موظفين'
      }, { status: 400 });
    }

    // حذف ربط الموظفين
    await pool.request()
      .input('unitId', id)
      .query('DELETE FROM EmployeeUnits WHERE UnitID = @unitId');

    // حذف الوحدة (soft delete)
    await pool.request()
      .input('id', id)
      .query('UPDATE OrganizationalUnits SET IsActive = 0, UpdatedAt = GETDATE() WHERE ID = @id');

    return NextResponse.json({
      success: true,
      message: 'تم حذف الوحدة التنظيمية بنجاح'
    });

  } catch (error) {
    console.error('❌ خطأ في حذف الوحدة:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في حذف الوحدة: ' + error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      await pool.close();
    }
  }
}

// GET - جلب تفاصيل وحدة محددة
export async function GET(request, { params }) {
  let pool;
  
  try {
    const { id } = params;

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'معرف الوحدة مطلوب'
      }, { status: 400 });
    }

    pool = await sql.connect(dbConfig);

    // جلب تفاصيل الوحدة
    const unitResult = await pool.request()
      .input('id', id)
      .query(`
        SELECT 
          ou.ID,
          ou.UnitName,
          ou.UnitCode,
          ou.ParentUnitID,
          ou.ManagerEmployeeCode,
          ou.ManagerName,
          ou.UnitLevel,
          ou.UnitType,
          ou.Description,
          parent.UnitName as ParentUnitName
        FROM OrganizationalUnits ou
        LEFT JOIN OrganizationalUnits parent ON ou.ParentUnitID = parent.ID
        WHERE ou.ID = @id AND ou.IsActive = 1
      `);

    if (unitResult.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الوحدة غير موجودة'
      }, { status: 404 });
    }

    // جلب الموظفين المرتبطين
    const employeesResult = await pool.request()
      .input('unitId', id)
      .query(`
        SELECT 
          eu.EmployeeCode,
          eu.Position,
          eu.IsDirectManager,
          eu.AssignmentDate,
          emp.EmployeeName
        FROM EmployeeUnits eu
        LEFT JOIN Employees emp ON eu.EmployeeCode = emp.EmployeeCode
        WHERE eu.UnitID = @unitId AND eu.IsActive = 1
        ORDER BY eu.IsDirectManager DESC, eu.AssignmentDate
      `);

    const unit = unitResult.recordset[0];
    unit.employees = employeesResult.recordset;

    return NextResponse.json({
      success: true,
      data: unit,
      message: 'تم جلب تفاصيل الوحدة بنجاح'
    });

  } catch (error) {
    console.error('❌ خطأ في جلب تفاصيل الوحدة:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب تفاصيل الوحدة: ' + error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      await pool.close();
    }
  }
}
