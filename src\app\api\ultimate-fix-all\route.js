import { NextResponse } from 'next/server';
import { getConnection } from '@/lib/db';

export async function GET() {
  try {

    const pool = await getConnection();
    const startTime = new Date();

    // الخطوة 1: تحليل شامل لجميع أنواع المشاكل

    // النوع الأول: سجلات "حضور" ولكن الملاحظات تشير لإجازة
    const type1Result = await pool.request().query(`
      SELECT 
        EmployeeCode, EmployeeName, AttendanceDate, Attendance, Notes,
        'TYPE1_ATTENDANCE_NOTES_MISMATCH' as ProblemType
      FROM DailyAttendance 
      WHERE Attendance = N'حضور' 
        AND (
          Notes LIKE N'%إجازة%' OR 
          Notes LIKE N'%معتمد%' OR
          Notes LIKE N'%اعتيادية%' OR
          Notes LIKE N'%عارضة%' OR
          Notes LIKE N'%مرضية%'
        )
        AND AttendanceDate >= DATEADD(YEAR, -5, GETDATE())
    `);

    // النوع الثاني: سجلات تحتوي على كلمات متضاربة
    const type2Result = await pool.request().query(`
      SELECT 
        EmployeeCode, EmployeeName, AttendanceDate, Attendance, Notes,
        'TYPE2_CONFLICTING_WORDS' as ProblemType
      FROM DailyAttendance 
      WHERE (
        (Attendance LIKE N'%حضور%' AND Attendance LIKE N'%إجازة%') OR
        (Attendance LIKE N'%حضور%' AND Notes LIKE N'%إجازة%') OR
        (Attendance = N'حضور' AND Notes LIKE N'%غياب%')
      )
      AND AttendanceDate >= DATEADD(YEAR, -5, GETDATE())
    `);

    // النوع الثالث: سجلات فارغة أو غير واضحة مع ملاحظات مفيدة
    const type3Result = await pool.request().query(`
      SELECT 
        EmployeeCode, EmployeeName, AttendanceDate, Attendance, Notes,
        'TYPE3_EMPTY_WITH_NOTES' as ProblemType
      FROM DailyAttendance 
      WHERE (
        (Attendance IS NULL OR Attendance = '' OR Attendance = N'غير محدد') 
        AND (
          Notes LIKE N'%إجازة%' OR 
          Notes LIKE N'%حضور%' OR
          Notes LIKE N'%غياب%'
        )
      )
      AND AttendanceDate >= DATEADD(YEAR, -5, GETDATE())
    `);

    const allProblems = [
      ...type1Result.recordset,
      ...type2Result.recordset,
      ...type3Result.recordset
    ];

    console.log(`   - النوع الأول (حضور/إجازة): ${type1Result.recordset.length}`);
    console.log(`   - النوع الثاني (كلمات متضاربة): ${type2Result.recordset.length}`);
    console.log(`   - النوع الثالث (فارغ مع ملاحظات): ${type3Result.recordset.length}`);

    // الخطوة 2: إصلاح جميع المشاكل

    let fixedRecords = 0;
    const fixedDetails = [];

    for (const problem of allProblems) {
      try {
        let correctAttendance = 'حضور'; // القيمة الافتراضية
        let correctNotes = problem.Notes || '';

        // تحديد الحضور الصحيح بناءً على الملاحظات
        if (problem.Notes) {
          if (problem.Notes.includes('إجازة اعتيادية') || problem.Notes.includes('اعتيادية')) {
            correctAttendance = 'إجازة اعتيادية';
          } else if (problem.Notes.includes('إجازة عارضة') || problem.Notes.includes('عارضة')) {
            correctAttendance = 'إجازة عارضة';
          } else if (problem.Notes.includes('إجازة مرضية') || problem.Notes.includes('مرضية')) {
            correctAttendance = 'إجازة مرضية';
          } else if (problem.Notes.includes('إجازة')) {
            correctAttendance = 'إجازة اعتيادية';
          } else if (problem.Notes.includes('غياب')) {
            correctAttendance = 'غياب';
          } else if (problem.Notes.includes('مأمورية')) {
            correctAttendance = 'مأمورية';
          } else if (problem.Notes.includes('حضور')) {
            correctAttendance = 'حضور';
          }
        }

        // تحديث السجل
        await pool.request()
          .input('employeeCode', problem.EmployeeCode)
          .input('date', problem.AttendanceDate)
          .input('attendance', correctAttendance)
          .input('notes', correctNotes + ' - تم الإصلاح الشامل')
          .query(`
            UPDATE DailyAttendance 
            SET 
              Attendance = @attendance,
              Notes = @notes,
              UpdatedAt = GETDATE()
            WHERE EmployeeCode = @employeeCode AND AttendanceDate = @date
          `);

        fixedRecords++;
        
        if (fixedRecords <= 50) {
          fixedDetails.push({
            employee: problem.EmployeeName,
            date: problem.AttendanceDate.toISOString().split('T')[0],
            problemType: problem.ProblemType,
            from: problem.Attendance,
            to: correctAttendance,
            notes: problem.Notes?.substring(0, 50) + '...'
          });
        }

        if (fixedRecords % 500 === 0) {

        }

      } catch (error) {

      }
    }

    // الخطوة 3: إعادة بناء الملخص الشهري بالكامل

    // حذف جميع الملخصات
    await pool.request().query(`TRUNCATE TABLE MonthlyAttendanceSummary`);

    // جلب جميع الموظفين والشهور
    const allMonthsResult = await pool.request().query(`
      SELECT DISTINCT 
        da.EmployeeCode,
        da.EmployeeName,
        MONTH(da.AttendanceDate) as Month,
        YEAR(da.AttendanceDate) as Year
      FROM DailyAttendance da
      INNER JOIN Employees e ON da.EmployeeCode = e.EmployeeCode
      WHERE da.AttendanceDate >= DATEADD(YEAR, -5, GETDATE())
      ORDER BY da.EmployeeCode, Year, Month
    `);

    const allMonths = allMonthsResult.recordset;

    let rebuiltSummaries = 0;
    const batchSize = 100;

    for (let i = 0; i < allMonths.length; i += batchSize) {
      const batch = allMonths.slice(i, i + batchSize);
      
      for (const am of batch) {
        try {
          // حساب الإحصائيات الدقيقة
          const statsResult = await pool.request()
            .input('employeeCode', am.EmployeeCode)
            .input('month', am.Month)
            .input('year', am.Year)
            .query(`
              SELECT
                COUNT(*) as TotalWorkingDays,
                SUM(CASE WHEN Attendance = N'حضور' THEN 1 ELSE 0 END) as TotalPresent,
                SUM(CASE WHEN Attendance = N'غياب' THEN 1 ELSE 0 END) as TotalAbsent,
                SUM(CASE WHEN Attendance LIKE N'%إجازة%' THEN 1 ELSE 0 END) as TotalLeaves,
                SUM(CASE WHEN Attendance = N'مأمورية' THEN 1 ELSE 0 END) as TotalMissions,
                SUM(CASE WHEN Attendance LIKE N'%اعتيادية%' THEN 1 ELSE 0 END) as AnnualLeaves,
                SUM(CASE WHEN Attendance LIKE N'%عارضة%' THEN 1 ELSE 0 END) as CasualLeaves,
                SUM(CASE WHEN Attendance LIKE N'%مرضية%' THEN 1 ELSE 0 END) as SickLeaves,
                SUM(CASE WHEN Attendance LIKE N'%تأخير%' THEN 1 ELSE 0 END) as LateArrivals,
                SUM(CASE WHEN Attendance LIKE N'%انصراف مبكر%' THEN 1 ELSE 0 END) as EarlyDepartures
              FROM DailyAttendance
              WHERE EmployeeCode = @employeeCode
                AND MONTH(AttendanceDate) = @month
                AND YEAR(AttendanceDate) = @year
            `);

          if (statsResult.recordset.length > 0) {
            const stats = statsResult.recordset[0];
            
            if (stats.TotalWorkingDays > 0) {
              const attendancePercentage = (stats.TotalPresent / stats.TotalWorkingDays) * 100;

              // إدراج الملخص الجديد
              await pool.request()
                .input('employeeCode', am.EmployeeCode)
                .input('employeeName', am.EmployeeName)
                .input('month', am.Month)
                .input('year', am.Year)
                .input('totalWorkingDays', stats.TotalWorkingDays)
                .input('totalPresent', stats.TotalPresent)
                .input('totalAbsent', stats.TotalAbsent)
                .input('totalLeaves', stats.TotalLeaves)
                .input('totalMissions', stats.TotalMissions || 0)
                .input('annualLeaves', stats.AnnualLeaves || 0)
                .input('casualLeaves', stats.CasualLeaves || 0)
                .input('sickLeaves', stats.SickLeaves || 0)
                .input('attendancePercentage', attendancePercentage)
                .query(`
                  INSERT INTO MonthlyAttendanceSummary (
                    EmployeeCode, EmployeeName, Department, JobTitle, Month, Year,
                    TotalWorkingDays, TotalPresent, TotalAbsent, TotalLeaves, TotalMissions,
                    TotalSickLeave, TotalUnpaidLeave, TotalNightShifts, AttendancePercentage
                  )
                  VALUES (
                    @employeeCode, @employeeName, '', '', @month, @year,
                    @totalWorkingDays, @totalPresent, @totalAbsent, @totalLeaves, @totalMissions,
                    @sickLeaves, 0, 0, @attendancePercentage
                  )
                `);

              rebuiltSummaries++;
            }
          }
        } catch (error) {

        }
      }

      console.log(`📊 تم إعادة بناء ${Math.min(i + batchSize, allMonths.length)}/${allMonths.length} ملخص`);
    }

    // الخطوة 4: إحصائيات نهائية وتحقق
    const finalStatsResult = await pool.request().query(`
      SELECT 
        COUNT(DISTINCT EmployeeCode) as UniqueEmployees,
        COUNT(*) as TotalSummaries,
        SUM(TotalLeaves) as TotalLeaveDays,
        SUM(TotalPresent) as TotalPresentDays,
        SUM(TotalAbsent) as TotalAbsentDays,
        AVG(AttendancePercentage) as AvgAttendancePercentage,
        MIN(CAST(CONCAT(Year, '-', FORMAT(Month, '00'), '-01') AS DATE)) as EarliestDate,
        MAX(CAST(CONCAT(Year, '-', FORMAT(Month, '00'), '-01') AS DATE)) as LatestDate
      FROM MonthlyAttendanceSummary
    `);

    const finalStats = finalStatsResult.recordset[0];

    // تحقق من بعض الحالات المحددة
    const verificationResult = await pool.request().query(`
      SELECT TOP 10
        EmployeeName,
        Month,
        Year,
        TotalWorkingDays,
        TotalPresent,
        TotalLeaves,
        AttendancePercentage
      FROM MonthlyAttendanceSummary
      WHERE TotalLeaves > 0
      ORDER BY Year DESC, Month DESC, TotalLeaves DESC
    `);

    const endTime = new Date();
    const duration = Math.round((endTime - startTime) / 1000);

    return NextResponse.json({
      success: true,
      message: '🚀 تم الإصلاح النهائي الشامل لجميع الحالات بنجاح',
      executionTime: `${duration} ثانية`,
      data: {
        problemsFound: {
          type1_attendance_notes_mismatch: type1Result.recordset.length,
          type2_conflicting_words: type2Result.recordset.length,
          type3_empty_with_notes: type3Result.recordset.length,
          total: allProblems.length
        },
        recordsFixed: fixedRecords,
        summariesRebuilt: rebuiltSummaries,
        finalStats: {
          uniqueEmployees: finalStats.UniqueEmployees,
          totalSummaries: finalStats.TotalSummaries,
          totalLeaveDays: finalStats.TotalLeaveDays,
          totalPresentDays: finalStats.TotalPresentDays,
          totalAbsentDays: finalStats.TotalAbsentDays,
          avgAttendancePercentage: finalStats.AvgAttendancePercentage?.toFixed(2) + '%',
          dateRange: `${finalStats.EarliestDate?.toISOString().split('T')[0]} إلى ${finalStats.LatestDate?.toISOString().split('T')[0]}`
        },
        sampleFixedRecords: fixedDetails.slice(0, 20),
        verificationSample: verificationResult.recordset
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
