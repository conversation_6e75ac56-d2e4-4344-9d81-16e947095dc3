import { NextResponse } from 'next/server';
import { getConnection } from '@/lib/db';

export async function POST(request) {
  try {
    const { startDate, endDate } = await request.json();
    
    if (!startDate || !endDate) {
      return NextResponse.json(
        { error: 'تاريخ البداية والنهاية مطلوبان' },
        { status: 400 }
      );
    }

    const pool = await getConnection();

    // جلب جميع الموظفين مرتبين حسب الكود
    const employeesQuery = `
      SELECT 
        EmployeeCode,
        EmployeeName,
        JobTitle,
        Department
      FROM Employees 
      WHERE CurrentStatus = 'يعمل'
      ORDER BY CAST(EmployeeCode AS INT) ASC
    `;

    const employeesResult = await pool.request().query(employeesQuery);
    const employees = employeesResult.recordset;

    // جلب بيانات الحضور للفترة المحددة (التركيز على حقل التمام)
    const attendanceQuery = `
      SELECT
        EmployeeCode,
        CAST(AttendanceDate AS DATE) as AttendanceDate,
        Attendance as AttendanceStatus,
        Notes
      FROM DailyAttendance
      WHERE AttendanceDate >= @startDate
        AND AttendanceDate <= @endDate
      ORDER BY EmployeeCode, AttendanceDate
    `;

    const attendanceResult = await pool.request()
      .input('startDate', startDate)
      .input('endDate', endDate)
      .query(attendanceQuery);

    const attendanceRecords = attendanceResult.recordset;

    // جلب بيانات الإجازات المعتمدة للفترة
    const leavesQuery = `
      SELECT 
        EmployeeCode,
        LeaveType,
        StartDate,
        EndDate,
        Status
      FROM LeaveRequests 
      WHERE Status = 'معتمد'
        AND (
          (StartDate <= @endDate AND EndDate >= @startDate) OR
          (StartDate >= @startDate AND StartDate <= @endDate)
        )
    `;

    const leavesResult = await pool.request()
      .input('startDate', startDate)
      .input('endDate', endDate)
      .query(leavesQuery);

    const leaveRecords = leavesResult.recordset;

    // معالجة البيانات لكل موظف
    const processedEmployees = employees.map(employee => {
      const employeeAttendance = {};
      const employeeSummary = {
        'W': 0, 'Ab': 0, 'S': 0, 'R': 0, 'NH': 0, 
        'CR': 0, 'M': 0, 'AL': 0, 'CL': 0, 'UL': 0, 'ML': 0
      };

      // إنشاء قائمة الأيام للفترة
      const currentDate = new Date(startDate);
      const endDateObj = new Date(endDate);

      while (currentDate <= endDateObj) {
        const dateStr = currentDate.toISOString().split('T')[0];
        const dayOfWeek = currentDate.getDay();
        
        // تحديد نوع اليوم (عمل، راحة، عطلة)
        let defaultStatus = 'W'; // عمل
        
        // إذا كان جمعة أو سبت
        if (dayOfWeek === 5 || dayOfWeek === 6) {
          defaultStatus = 'R'; // راحة
        }

        // البحث عن سجل حضور لهذا اليوم
        const attendanceRecord = attendanceRecords.find(
          record => record.EmployeeCode === employee.EmployeeCode && 
                   record.AttendanceDate.toISOString().split('T')[0] === dateStr
        );

        // البحث عن إجازة لهذا اليوم
        const leaveRecord = leaveRecords.find(
          record => record.EmployeeCode === employee.EmployeeCode &&
                   new Date(record.StartDate) <= currentDate &&
                   new Date(record.EndDate) >= currentDate
        );

        let finalStatus = defaultStatus;

        if (leaveRecord) {
          // تحويل نوع الإجازة إلى رمز
          switch (leaveRecord.LeaveType) {
            case 'إجازة اعتيادية':
              finalStatus = 'AL';
              break;
            case 'إجازة عارضة':
              finalStatus = 'CL';
              break;
            case 'إجازة مرضية':
              finalStatus = 'S';
              break;
            case 'إجازة بدون أجر':
              finalStatus = 'UL';
              break;
            case 'إجازة بدل':
              finalStatus = 'CR';
              break;
            case 'إجازة وضع':
              finalStatus = 'ML';
              break;
            default:
              finalStatus = 'AL';
          }
        } else if (attendanceRecord) {
          // تحويل حالة الحضور إلى رمز
          switch (attendanceRecord.AttendanceStatus) {
            case 'حضور':
              finalStatus = 'W';
              break;
            case 'غياب':
              finalStatus = 'Ab';
              break;
            case 'مأمورية':
              finalStatus = 'M';
              break;
            case 'مرضى':
            case 'إجازة مرضية':
              finalStatus = 'S';
              break;
            case 'إجازة اعتيادية':
              finalStatus = 'AL';
              break;
            case 'إجازة عارضة':
              finalStatus = 'CL';
              break;
            case 'إجازة بدون أجر':
              finalStatus = 'UL';
              break;
            case 'إجازة وضع':
            case 'إجازة أمومة':
              finalStatus = 'ML';
              break;
            case 'إجازة بدل':
              finalStatus = 'CR';
              break;
            case 'إجازة رسمية':
              finalStatus = 'NH';
              break;
            default:
              finalStatus = 'W'; // افتراضي: عمل
          }
        } else if (dayOfWeek !== 5 && dayOfWeek !== 6) {
          // إذا لم يكن هناك سجل حضور وليس عطلة نهاية أسبوع
          finalStatus = 'Ab'; // غياب
        }

        employeeAttendance[dateStr] = finalStatus;
        employeeSummary[finalStatus]++;

        currentDate.setDate(currentDate.getDate() + 1);
      }

      return {
        ...employee,
        attendance: employeeAttendance,
        summary: employeeSummary
      };
    });

    const responseData = {
      employees: processedEmployees,
      period: {
        startDate,
        endDate,
        totalDays: Math.ceil((new Date(endDate) - new Date(startDate)) / (1000 * 60 * 60 * 24)) + 1
      },
      summary: {
        totalEmployees: employees.length,
        totalAttendanceRecords: attendanceRecords.length,
        totalLeaveRecords: leaveRecords.length
      }
    };

    return NextResponse.json(responseData);

  } catch (error) {

    return NextResponse.json(
      { error: 'خطأ في إنشاء كشف التمام', details: error.message },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { message: 'استخدم POST لجلب كشف التمام الشهري' },
    { status: 405 }
  );
}
