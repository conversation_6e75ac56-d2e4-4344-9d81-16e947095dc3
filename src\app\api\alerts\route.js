import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function POST(request) {
  try {
    const body = await request.json();
    const { action } = body;

    const pool = await getConnection();

    switch (action) {
      case 'setup':
        return await setupAlertsSystem(pool);
      case 'getAlerts':
        return await getSystemAlerts(pool, body);
      case 'dismissAlert':
        return await dismissAlert(pool, body);
      case 'checkMissingAttendance':
        return await checkMissingAttendance(pool, body);
      case 'checkExpiringContracts':
        return await checkExpiringContracts(pool, body);
      case 'checkMissingDocuments':
        return await checkMissingDocuments(pool, body);
      case 'getAlertCounts':
        return await getAlertCounts(pool);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  }
}

// إعداد نظام التنبيهات
async function setupAlertsSystem(pool) {
  try {

    // 1. جدول التنبيهات
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SystemAlerts' AND xtype='U')
      BEGIN
        CREATE TABLE SystemAlerts (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          AlertType NVARCHAR(50) NOT NULL,
          AlertCategory NVARCHAR(50) NOT NULL,
          Title NVARCHAR(200) NOT NULL,
          Message NVARCHAR(1000) NOT NULL,
          Severity NVARCHAR(20) DEFAULT 'medium',
          EmployeeCode NVARCHAR(50),
          EmployeeName NVARCHAR(100),
          RelatedTable NVARCHAR(100),
          RelatedID NVARCHAR(50),
          DueDate DATETIME,
          DaysOverdue INT DEFAULT 0,
          IsActive BIT DEFAULT 1,
          IsDismissed BIT DEFAULT 0,
          DismissedBy NVARCHAR(50),
          DismissedAt DATETIME,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),
          AdditionalData NVARCHAR(MAX)
        )

        CREATE INDEX IX_SystemAlerts_AlertType ON SystemAlerts(AlertType)
        CREATE INDEX IX_SystemAlerts_AlertCategory ON SystemAlerts(AlertCategory)
        CREATE INDEX IX_SystemAlerts_EmployeeCode ON SystemAlerts(EmployeeCode)
        CREATE INDEX IX_SystemAlerts_IsActive ON SystemAlerts(IsActive)
        CREATE INDEX IX_SystemAlerts_IsDismissed ON SystemAlerts(IsDismissed)
        CREATE INDEX IX_SystemAlerts_CreatedAt ON SystemAlerts(CreatedAt)
      END
    `);

    // 2. جدول أنواع التنبيهات
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='AlertTypes' AND xtype='U')
      BEGIN
        CREATE TABLE AlertTypes (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          AlertCode NVARCHAR(50) NOT NULL UNIQUE,
          AlertName NVARCHAR(100) NOT NULL,
          AlertNameAr NVARCHAR(100) NOT NULL,
          Category NVARCHAR(50) NOT NULL,
          Description NVARCHAR(500),
          DefaultSeverity NVARCHAR(20) DEFAULT 'medium',
          CheckIntervalHours INT DEFAULT 24,
          IsActive BIT DEFAULT 1,
          AutoDismissAfterDays INT,
          NotificationTemplate NVARCHAR(1000),
          CreatedAt DATETIME DEFAULT GETDATE()
        )

        CREATE INDEX IX_AlertTypes_AlertCode ON AlertTypes(AlertCode)
        CREATE INDEX IX_AlertTypes_Category ON AlertTypes(Category)
      END
    `);

    // إدراج أنواع التنبيهات الأساسية
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM AlertTypes WHERE AlertCode = 'MISSING_ATTENDANCE')
      BEGIN
        INSERT INTO AlertTypes (AlertCode, AlertName, AlertNameAr, Category, Description, DefaultSeverity, CheckIntervalHours, NotificationTemplate)
        VALUES
        ('MISSING_ATTENDANCE', 'Missing Attendance', 'تمام مفقود', 'Attendance', 'Employee attendance not recorded', 'high', 6, 'لم يتم تسجيل تمام الموظف {EmployeeName} ليوم {Date}'),
        ('EXPIRING_CONTRACT', 'Expiring Contract', 'انتهاء تعاقد', 'Contracts', 'Employee contract expiring soon', 'high', 24, 'ينتهي تعاقد الموظف {EmployeeName} في {ExpiryDate}'),
        ('MISSING_DOCUMENTS', 'Missing Documents', 'مستندات مفقودة', 'Documents', 'Required employee documents missing', 'medium', 168, 'مستندات مفقودة للموظف {EmployeeName}: {DocumentTypes}'),
        ('OVERDUE_LEAVE', 'Overdue Leave Return', 'تأخر عودة من إجازة', 'Leave', 'Employee overdue from leave', 'high', 12, 'الموظف {EmployeeName} متأخر في العودة من الإجازة'),
        ('INCOMPLETE_PROFILE', 'Incomplete Profile', 'بيانات ناقصة', 'Employee Data', 'Employee profile incomplete', 'low', 168, 'بيانات الموظف {EmployeeName} غير مكتملة'),
        ('APARTMENT_VACANCY', 'Apartment Vacancy', 'شقة شاغرة', 'Housing', 'Apartment without beneficiaries', 'medium', 72, 'الشقة {ApartmentCode} بدون مستفيدين'),
        ('CAR_UNASSIGNED', 'Unassigned Car', 'سيارة غير مخصصة', 'Transportation', 'Car without assigned employees', 'medium', 72, 'السيارة {CarCode} غير مخصصة لموظفين'),
        ('COST_OVERDUE', 'Overdue Cost Entry', 'تكاليف متأخرة', 'Costs', 'Monthly costs not entered', 'medium', 24, 'لم يتم إدخال تكاليف {CostType} لشهر {Month}'),
        ('SYSTEM_BACKUP', 'System Backup Due', 'نسخ احتياطي مطلوب', 'System', 'System backup overdue', 'high', 24, 'النسخ الاحتياطي للنظام متأخر'),
        ('DATA_INCONSISTENCY', 'Data Inconsistency', 'تضارب في البيانات', 'Data Quality', 'Inconsistent data detected', 'medium', 48, 'تم اكتشاف تضارب في البيانات: {Details}')
      END
    `);

    // إنشاء تنبيهات تجريبية
    await createSampleAlerts(pool);

    return NextResponse.json({
      success: true,
      message: 'تم إعداد نظام التنبيهات بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في إعداد نظام التنبيهات: ' + error.message
    }, { status: 500 });
  }
}

// إنشاء تنبيهات تجريبية
async function createSampleAlerts(pool) {
  try {

    // التحقق من وجود تنبيهات مسبقة
    const existingAlerts = await pool.request().query(`
      SELECT COUNT(*) as count FROM SystemAlerts WHERE IsActive = 1
    `);

    if (existingAlerts.recordset[0].count > 0) {

      return;
    }

    // جلب بعض الموظفين للتنبيهات التجريبية
    const employeesResult = await pool.request().query(`
      SELECT TOP 5 EmployeeCode, EmployeeName, Department
      FROM Employees
      WHERE CurrentStatus IN ('نشط', 'ساري', 'سارى')
      ORDER BY EmployeeCode
    `);

    const employees = employeesResult.recordset;
    if (employees.length === 0) {

      return;
    }

    // إنشاء تنبيهات متنوعة
    const sampleAlerts = [
      {
        type: 'MISSING_ATTENDANCE',
        category: 'Attendance',
        title: 'تمام مفقود',
        message: `لم يتم تسجيل تمام الموظف ${employees[0]?.EmployeeName} ليوم أمس`,
        severity: 'high',
        employeeCode: employees[0]?.EmployeeCode,
        employeeName: employees[0]?.EmployeeName
      },
      {
        type: 'EXPIRING_CONTRACT',
        category: 'Contracts',
        title: 'انتهاء تعاقد قريب',
        message: `ينتهي تعاقد الموظف ${employees[1]?.EmployeeName} خلال 15 يوم`,
        severity: 'medium',
        employeeCode: employees[1]?.EmployeeCode,
        employeeName: employees[1]?.EmployeeName
      },
      {
        type: 'MISSING_DOCUMENTS',
        category: 'Documents',
        title: 'مستندات مفقودة',
        message: `مستندات مفقودة للموظف ${employees[2]?.EmployeeName}: صورة شخصية، بطاقة الرقم القومي`,
        severity: 'medium',
        employeeCode: employees[2]?.EmployeeCode,
        employeeName: employees[2]?.EmployeeName
      },
      {
        type: 'APARTMENT_VACANCY',
        category: 'Housing',
        title: 'شقة شاغرة',
        message: 'الشقة رقم A-101 بدون مستفيدين منذ أسبوعين',
        severity: 'low',
        employeeCode: null,
        employeeName: null
      },
      {
        type: 'CAR_UNASSIGNED',
        category: 'Transportation',
        title: 'سيارة غير مخصصة',
        message: 'السيارة رقم 1190 غير مخصصة لأي موظفين',
        severity: 'medium',
        employeeCode: null,
        employeeName: null
      },
      {
        type: 'SYSTEM_BACKUP',
        category: 'System',
        title: 'نسخ احتياطي مطلوب',
        message: 'النسخ الاحتياطي للنظام متأخر منذ 3 أيام',
        severity: 'high',
        employeeCode: null,
        employeeName: null
      }
    ];

    // إدراج التنبيهات التجريبية
    for (const alert of sampleAlerts) {
      if (!alert.employeeCode && alert.type.includes('EMPLOYEE')) continue; // تخطي إذا لم يكن هناك موظف

      await pool.request()
        .input('alertType', sql.NVarChar, alert.type)
        .input('alertCategory', sql.NVarChar, alert.category)
        .input('title', sql.NVarChar, alert.title)
        .input('message', sql.NVarChar, alert.message)
        .input('severity', sql.NVarChar, alert.severity)
        .input('employeeCode', sql.NVarChar, alert.employeeCode)
        .input('employeeName', sql.NVarChar, alert.employeeName)
        .query(`
          INSERT INTO SystemAlerts (
            AlertType, AlertCategory, Title, Message, Severity,
            EmployeeCode, EmployeeName, DaysOverdue,
            AdditionalData
          )
          VALUES (
            @alertType, @alertCategory, @title, @message, @severity,
            @employeeCode, @employeeName,
            CASE
              WHEN @alertType = 'MISSING_ATTENDANCE' THEN 1
              WHEN @alertType = 'SYSTEM_BACKUP' THEN 3
              ELSE 0
            END,
            '{"source": "sample_data", "created_by": "system_setup"}'
          )
        `);
    }

  } catch (error) {

  }
}

// جلب التنبيهات النشطة
async function getSystemAlerts(pool, data) {
  try {
    const {
      alertType,
      category,
      severity,
      employeeCode,
      includeDismissed = false,
      limit = 50
    } = data;

    let whereClause = 'WHERE sa.IsActive = 1';
    const request = pool.request();

    if (!includeDismissed) {
      whereClause += ' AND sa.IsDismissed = 0';
    }

    if (alertType) {
      whereClause += ' AND sa.AlertType = @alertType';
      request.input('alertType', sql.NVarChar, alertType);
    }

    if (category) {
      whereClause += ' AND sa.AlertCategory = @category';
      request.input('category', sql.NVarChar, category);
    }

    if (severity) {
      whereClause += ' AND sa.Severity = @severity';
      request.input('severity', sql.NVarChar, severity);
    }

    if (employeeCode) {
      whereClause += ' AND sa.EmployeeCode = @employeeCode';
      request.input('employeeCode', sql.NVarChar, employeeCode);
    }

    request.input('limit', sql.Int, limit);

    const query = `
      SELECT TOP (@limit)
        sa.ID,
        sa.AlertType,
        sa.AlertCategory,
        sa.Title,
        sa.Message,
        sa.Severity,
        sa.EmployeeCode,
        sa.EmployeeName,
        sa.RelatedTable,
        sa.RelatedID,
        sa.DueDate,
        sa.DaysOverdue,
        sa.IsActive as AlertIsActive,
        sa.IsDismissed,
        sa.DismissedBy,
        sa.DismissedAt,
        sa.CreatedAt,
        sa.UpdatedAt,
        sa.AdditionalData,
        at.AlertNameAr,
        at.DefaultSeverity
      FROM SystemAlerts sa
      LEFT JOIN AlertTypes at ON sa.AlertType = at.AlertCode
      ${whereClause}
      ORDER BY
        CASE sa.Severity
          WHEN 'critical' THEN 1
          WHEN 'high' THEN 2
          WHEN 'medium' THEN 3
          WHEN 'low' THEN 4
          ELSE 5
        END,
        sa.CreatedAt DESC
    `;

    const result = await request.query(query);

    return NextResponse.json({
      success: true,
      data: result.recordset
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب التنبيهات: ' + error.message
    }, { status: 500 });
  }
}

// إخفاء تنبيه
async function dismissAlert(pool, data) {
  try {
    const { alertId, userCode } = data;

    await pool.request()
      .input('alertId', sql.Int, alertId)
      .input('userCode', sql.NVarChar, userCode)
      .query(`
        UPDATE SystemAlerts
        SET IsDismissed = 1,
            DismissedBy = @userCode,
            DismissedAt = GETDATE(),
            UpdatedAt = GETDATE()
        WHERE ID = @alertId
      `);

    return NextResponse.json({
      success: true,
      message: 'تم إخفاء التنبيه بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في إخفاء التنبيه: ' + error.message
    }, { status: 500 });
  }
}

// فحص التمام المفقود
async function checkMissingAttendance(pool, data) {
  try {
    const { checkDate } = data;
    const targetDate = checkDate ? new Date(checkDate) : new Date();

    // التحقق من اليوم السابق إذا لم يتم تحديد تاريخ
    if (!checkDate) {
      targetDate.setDate(targetDate.getDate() - 1);
    }

    const dateStr = targetDate.toISOString().split('T')[0];

    // جلب جميع الموظفين النشطين
    const employeesResult = await pool.request().query(`
      SELECT EmployeeCode, EmployeeName, Department, JobTitle
      FROM Employees
      WHERE CurrentStatus IN ('نشط', 'ساري', 'سارى')
    `);

    // جلب سجلات التمام للتاريخ المحدد
    const attendanceResult = await pool.request()
      .input('targetDate', sql.Date, targetDate)
      .query(`
        SELECT DISTINCT EmployeeCode
        FROM DailyAttendance
        WHERE CAST(AttendanceDate AS DATE) = @targetDate
      `);

    const attendedEmployees = new Set(attendanceResult.recordset.map(r => r.EmployeeCode));
    const missingEmployees = employeesResult.recordset.filter(emp =>
      !attendedEmployees.has(emp.EmployeeCode)
    );

    // إنشاء تنبيهات للموظفين المفقودين
    for (const employee of missingEmployees) {
      // التحقق من عدم وجود تنبيه مسبق لنفس الموظف والتاريخ
      const existingAlert = await pool.request()
        .input('employeeCode', sql.NVarChar, employee.EmployeeCode)
        .input('targetDate', sql.Date, targetDate)
        .query(`
          SELECT ID FROM SystemAlerts
          WHERE AlertType = 'MISSING_ATTENDANCE'
            AND EmployeeCode = @employeeCode
            AND CAST(CreatedAt AS DATE) = @targetDate
            AND IsActive = 1
        `);

      if (existingAlert.recordset.length === 0) {
        await pool.request()
          .input('employeeCode', sql.NVarChar, employee.EmployeeCode)
          .input('employeeName', sql.NVarChar, employee.EmployeeName)
          .input('targetDate', sql.Date, targetDate)
          .input('message', sql.NVarChar, `لم يتم تسجيل تمام الموظف ${employee.EmployeeName} (${employee.EmployeeCode}) ليوم ${dateStr}`)
          .query(`
            INSERT INTO SystemAlerts (
              AlertType, AlertCategory, Title, Message, Severity,
              EmployeeCode, EmployeeName, DueDate, DaysOverdue,
              AdditionalData
            )
            VALUES (
              'MISSING_ATTENDANCE', 'Attendance', 'تمام مفقود', @message, 'high',
              @employeeCode, @employeeName, @targetDate, DATEDIFF(day, @targetDate, GETDATE()),
              '{"date": "' + CAST(@targetDate AS NVARCHAR) + '", "department": "' + @employeeName + '"}'
            )
          `);
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        totalEmployees: employeesResult.recordset.length,
        attendedEmployees: attendedEmployees.size,
        missingEmployees: missingEmployees.length,
        missingList: missingEmployees
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في فحص التمام المفقود: ' + error.message
    }, { status: 500 });
  }
}

// فحص التعاقدات المنتهية الصلاحية
async function checkExpiringContracts(pool, data) {
  try {
    const { daysAhead = 30 } = data;
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + daysAhead);

    // البحث عن الموظفين الذين ينتهي تعاقدهم قريباً
    const expiringResult = await pool.request()
      .input('futureDate', sql.Date, futureDate)
      .query(`
        SELECT EmployeeCode, EmployeeName, Department, JobTitle, ContractEndDate
        FROM Employees
        WHERE ContractEndDate IS NOT NULL
          AND ContractEndDate <= @futureDate
          AND ContractEndDate >= GETDATE()
          AND CurrentStatus IN ('نشط', 'ساري', 'سارى')
      `);

    // إنشاء تنبيهات للتعاقدات المنتهية
    for (const employee of expiringResult.recordset) {
      const daysUntilExpiry = Math.ceil((new Date(employee.ContractEndDate) - new Date()) / (1000 * 60 * 60 * 24));

      // التحقق من عدم وجود تنبيه مسبق
      const existingAlert = await pool.request()
        .input('employeeCode', sql.NVarChar, employee.EmployeeCode)
        .query(`
          SELECT ID FROM SystemAlerts
          WHERE AlertType = 'EXPIRING_CONTRACT'
            AND EmployeeCode = @employeeCode
            AND IsActive = 1
            AND IsDismissed = 0
        `);

      if (existingAlert.recordset.length === 0) {
        const severity = daysUntilExpiry <= 7 ? 'critical' : daysUntilExpiry <= 15 ? 'high' : 'medium';

        await pool.request()
          .input('employeeCode', sql.NVarChar, employee.EmployeeCode)
          .input('employeeName', sql.NVarChar, employee.EmployeeName)
          .input('contractEndDate', sql.Date, employee.ContractEndDate)
          .input('severity', sql.NVarChar, severity)
          .input('message', sql.NVarChar, `ينتهي تعاقد الموظف ${employee.EmployeeName} (${employee.EmployeeCode}) في ${employee.ContractEndDate.toLocaleDateString('ar-EG')} - متبقي ${daysUntilExpiry} يوم`)
          .query(`
            INSERT INTO SystemAlerts (
              AlertType, AlertCategory, Title, Message, Severity,
              EmployeeCode, EmployeeName, DueDate,
              AdditionalData
            )
            VALUES (
              'EXPIRING_CONTRACT', 'Contracts', 'انتهاء تعاقد قريب', @message, @severity,
              @employeeCode, @employeeName, @contractEndDate,
              '{"daysUntilExpiry": ' + CAST(@contractEndDate AS NVARCHAR) + ', "department": "' + @employeeName + '"}'
            )
          `);
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        expiringContracts: expiringResult.recordset.length,
        contracts: expiringResult.recordset
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في فحص التعاقدات المنتهية: ' + error.message
    }, { status: 500 });
  }
}

// فحص المستندات المفقودة
async function checkMissingDocuments(pool, data) {
  try {
    // الحصول على قائمة الموظفين
    const employeesResult = await pool.request().query(`
      SELECT EmployeeCode, EmployeeName, Department
      FROM Employees
      WHERE CurrentStatus IN ('نشط', 'ساري', 'سارى')
    `);

    // أنواع المستندات المطلوبة
    const requiredDocuments = [
      { type: 'photo', name: 'صورة شخصية' },
      { type: 'NationalIDs', name: 'بطاقة الرقم القومي' },
      { type: 'StatusReports', name: 'بيان حالة اجتماعية' },
      { type: 'WorkReceipts', name: 'استلام العمل' }
    ];

    // الحصول على مسارات الأرشيف
    const archiveResult = await pool.request().query(`
      SELECT itemType, folderPath FROM ARCHIV
    `);

    const archivePaths = {};
    archiveResult.recordset.forEach(item => {
      archivePaths[item.itemType] = item.folderPath;
    });

    const fs = require('fs');
    const path = require('path');

    for (const employee of employeesResult.recordset) {
      const missingDocs = [];

      for (const docType of requiredDocuments) {
        const folderPath = archivePaths[docType.type];
        if (folderPath) {
          const possibleExtensions = ['.pdf', '.jpg', '.jpeg', '.png'];
          let documentExists = false;

          for (const ext of possibleExtensions) {
            const filePath = path.join(folderPath, `${employee.employeeCode}${ext}`);
            try {
              if (fs.existsSync(filePath)) {
                documentExists = true;
                break;
              }
            } catch (error) {
              // تجاهل أخطاء الوصول للملف
            }
          }

          if (!documentExists) {
            missingDocs.push(docType.name);
          }
        }
      }

      // إنشاء تنبيه إذا كانت هناك مستندات مفقودة
      if (missingDocs.length > 0) {
        // التحقق من عدم وجود تنبيه مسبق
        const existingAlert = await pool.request()
          .input('employeeCode', sql.NVarChar, employee.employeeCode)
          .query(`
            SELECT ID FROM SystemAlerts
            WHERE AlertType = 'MISSING_DOCUMENTS'
              AND EmployeeCode = @employeeCode
              AND IsActive = 1
              AND IsDismissed = 0
              AND DATEDIFF(day, CreatedAt, GETDATE()) < 7
          `);

        if (existingAlert.recordset.length === 0) {
          await pool.request()
            .input('employeeCode', sql.NVarChar, employee.employeeCode)
            .input('employeeName', sql.NVarChar, employee.fullName)
            .input('message', sql.NVarChar, `مستندات مفقودة للموظف ${employee.fullName} (${employee.employeeCode}): ${missingDocs.join('، ')}`)
            .query(`
              INSERT INTO SystemAlerts (
                AlertType, AlertCategory, Title, Message, Severity,
                EmployeeCode, EmployeeName,
                AdditionalData
              )
              VALUES (
                'MISSING_DOCUMENTS', 'Documents', 'مستندات مفقودة', @message, 'medium',
                @employeeCode, @employeeName,
                '{"missingDocuments": "' + @message + '"}'
              )
            `);
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: 'تم فحص المستندات المفقودة بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في فحص المستندات المفقودة: ' + error.message
    }, { status: 500 });
  }
}

// جلب عدد التنبيهات
async function getAlertCounts(pool) {
  try {
    const result = await pool.request().query(`
      SELECT
        COUNT(*) as TotalAlerts,
        COUNT(CASE WHEN Severity = 'critical' THEN 1 END) as CriticalAlerts,
        COUNT(CASE WHEN Severity = 'high' THEN 1 END) as HighAlerts,
        COUNT(CASE WHEN Severity = 'medium' THEN 1 END) as MediumAlerts,
        COUNT(CASE WHEN Severity = 'low' THEN 1 END) as LowAlerts,
        COUNT(CASE WHEN AlertCategory = 'Attendance' THEN 1 END) as AttendanceAlerts,
        COUNT(CASE WHEN AlertCategory = 'Contracts' THEN 1 END) as ContractAlerts,
        COUNT(CASE WHEN AlertCategory = 'Documents' THEN 1 END) as DocumentAlerts
      FROM SystemAlerts
      WHERE IsActive = 1 AND IsDismissed = 0
    `);

    return NextResponse.json({
      success: true,
      data: result.recordset[0]
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب عدد التنبيهات: ' + error.message
    }, { status: 500 });
  }
}
