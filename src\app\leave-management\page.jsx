'use client';
import React from 'react';

function MainComponent() {
  const [selectedLang, setSelectedLang] = useState('ar');
  const dir = selectedLang === 'ar' ? 'rtl' : 'ltr';

  const leaveManagementLinks = [
    {
      title: { ar: 'تسجيل إجازة جديدة', en: 'Request New Leave' },
      icon: 'fa-calendar-plus',
      link: '/leave-request',
      description: {
        ar: 'تقديم طلب إجازة جديد',
        en: 'Submit a new leave request',
      },
    },
    {
      title: { ar: 'الإجازات قيد المراجعة', en: 'Pending Leaves' },
      icon: 'fa-clock',
      link: '/pending-leaves',
      description: {
        ar: 'عرض طلبات الإجازات التي تنتظر الموافقة',
        en: 'View leave requests awaiting approval',
      },
    },
    {
      title: { ar: 'الإجازات المعتمدة', en: 'Approved Leaves' },
      icon: 'fa-check-circle',
      link: '/approved-leaves',
      description: {
        ar: 'عرض الإجازات التي تمت الموافقة عليها',
        en: 'View approved leave requests',
      },
    },
    {
      title: { ar: 'الإجازات المرفوضة', en: 'Rejected Leaves' },
      icon: 'fa-times-circle',
      link: '/rejected-leaves',
      description: {
        ar: 'عرض الإجازات التي تم رفضها',
        en: 'View rejected leave requests',
      },
    },
    {
      title: { ar: 'رصيد الإجازات', en: 'Leave Balance' },
      icon: 'fa-calculator',
      link: '/leave-balance',
      description: {
        ar: 'عرض رصيد الإجازات المتبقي',
        en: 'View remaining leave balance',
      },
    },
  ];

  return (
    <div
      dir={dir}
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="max-w-7xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div className="flex items-center gap-4">
            <a
              href="/"
              className="flex items-center text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
            >
              <i
                className={`fas fa-arrow-${
                  selectedLang === 'ar' ? 'left' : 'right'
                } ml-2`}
              ></i>
              {selectedLang === 'ar' ? 'الرئيسية' : 'Home'}
            </a>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              {selectedLang === 'ar' ? 'إدارة الإجازات' : 'Leave Management'}
            </h1>
          </div>
          <button
            onClick={() => setSelectedLang(selectedLang === 'ar' ? 'en' : 'ar')}
            className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
          >
            {selectedLang === 'ar' ? 'English' : 'العربية'}
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {leaveManagementLinks.map((item, index) => (
            <a
              key={index}
              href={item.link}
              className="bg-gray-50 dark:bg-gray-800 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300"
            >
              <div className="flex items-center gap-4">
                <div className="flex-shrink-0">
                  <i
                    className={`fas ${item.icon} text-3xl text-blue-600 dark:text-blue-400`}
                  ></i>
                </div>
                <div>
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
                    {item.title[selectedLang]}
                  </h2>
                  <p className="text-gray-600 dark:text-gray-300">
                    {item.description[selectedLang]}
                  </p>
                </div>
              </div>
            </a>
          ))}
        </div>
      </div>
    </div>
  );
}

export default MainComponent;
