import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function POST(request) {
  let pool = null;
  
  try {
    const body = await request.json();
    const { action } = body;

    pool = await getConnection();

    switch (action) {
      case 'list':
        return await getDailyAttendance(pool, body);
      case 'add':
        return await addAttendanceRecord(pool, body);
      case 'update':
        return await updateAttendanceRecord(pool, body);
      case 'delete':
        return await deleteAttendanceRecord(pool, body);
      case 'import_biometric':
        return await importBiometricData(pool, body);
      case 'process_biometric':
        return await processBiometricData(pool, body);
      case 'get_summary':
        return await getAttendanceSummary(pool, body);
      case 'bulk_upload':
        return await bulkUploadAttendance(pool, body);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      try {
        await pool.close();
      } catch (error) {

      }
    }
  }
}

// إنشاء الجداول إذا لم تكن موجودة
async function setupAttendanceTables(pool) {
  try {
    // إنشاء جدول البصمة
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='BiometricData' AND xtype='U')
      BEGIN
          CREATE TABLE BiometricData (
              ID INT IDENTITY(1,1) PRIMARY KEY,
              EmployeeCode NVARCHAR(20) NOT NULL,
              EmployeeName NVARCHAR(100),
              CheckTime DATETIME NOT NULL,
              CheckType NVARCHAR(20) NOT NULL,
              DeviceID NVARCHAR(50),
              VerifyMode NVARCHAR(20),
              IsProcessed BIT DEFAULT 0,
              ProcessedAt DATETIME,
              ImportedAt DATETIME DEFAULT GETDATE(),
              Notes NVARCHAR(500)
          )
          
          CREATE INDEX IX_BiometricData_Employee ON BiometricData(EmployeeCode)
          CREATE INDEX IX_BiometricData_Date ON BiometricData(CheckTime)
          CREATE INDEX IX_BiometricData_Processed ON BiometricData(IsProcessed)
      END
    `);

    // إنشاء جدول التمام اليومي (النسخة البسيطة المتوافقة)
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DailyAttendance' AND xtype='U')
      BEGIN
          CREATE TABLE DailyAttendance (
              ID INT IDENTITY(1,1) PRIMARY KEY,
              AttendanceDate DATE NOT NULL,
              EmployeeCode NVARCHAR(20) NOT NULL,
              EmployeeName NVARCHAR(100) NOT NULL,
              Department NVARCHAR(100),
              JobTitle NVARCHAR(100),
              Attendance NVARCHAR(50) NOT NULL,
              CheckInTime NVARCHAR(20),
              CheckOutTime NVARCHAR(20),
              Notes NVARCHAR(MAX),
              IsFromRequest BIT DEFAULT 0,
              RequestID INT NULL,
              CreatedAt DATETIME DEFAULT GETDATE(),
              UpdatedAt DATETIME DEFAULT GETDATE(),

              UNIQUE (AttendanceDate, EmployeeCode)
          )

          CREATE INDEX IX_DailyAttendance_Date ON DailyAttendance(AttendanceDate)
          CREATE INDEX IX_DailyAttendance_Employee ON DailyAttendance(EmployeeCode)
      END
    `);

    // إنشاء جدول أنواع المؤثرات
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DailyEffectTypes' AND xtype='U')
      BEGIN
          CREATE TABLE DailyEffectTypes (
              ID INT IDENTITY(1,1) PRIMARY KEY,
              EffectCode NVARCHAR(10) NOT NULL UNIQUE,
              EffectNameAr NVARCHAR(100) NOT NULL,
              EffectNameEn NVARCHAR(100),
              EffectCategory NVARCHAR(50) NOT NULL,
              EffectType NVARCHAR(20) NOT NULL,
              DefaultAmount DECIMAL(8,2) DEFAULT 0,
              Description NVARCHAR(500),
              IsActive BIT DEFAULT 1
          )
          
          -- إدراج البيانات الأساسية
          INSERT INTO DailyEffectTypes (EffectCode, EffectNameAr, EffectNameEn, EffectCategory, EffectType, Description) VALUES
          ('W', 'حضور', 'Present', 'إداري', 'محايد', 'حضور عادي في الوقت المحدد'),
          ('Ab', 'غياب', 'Absent', 'مالي', 'سلبي', 'غياب بدون عذر'),
          ('S', 'إجازة مرضية', 'Sick Leave', 'إداري', 'محايد', 'إجازة مرضية بشهادة طبية'),
          ('R', 'إجازة سنوية', 'Annual Leave', 'إداري', 'محايد', 'إجازة سنوية اعتيادية'),
          ('NH', 'إجازة رسمية', 'National Holiday', 'إداري', 'محايد', 'إجازة رسمية/عطلة'),
          ('CR', 'إجازة عارضة', 'Casual Leave', 'إداري', 'محايد', 'إجازة عارضة طارئة'),
          ('M', 'مأمورية', 'Mission', 'مالي', 'إيجابي', 'مأمورية رسمية'),
          ('AL', 'إجازة بدون راتب', 'Unpaid Leave', 'مالي', 'سلبي', 'إجازة بدون راتب'),
          ('CL', 'إجازة أمومة', 'Maternity Leave', 'إداري', 'محايد', 'إجازة أمومة'),
          ('UL', 'إجازة طارئة', 'Emergency Leave', 'إداري', 'محايد', 'إجازة طارئة'),
          ('ML', 'إجازة زواج', 'Marriage Leave', 'إداري', 'محايد', 'إجازة زواج'),
          ('L', 'تأخير', 'Late', 'مالي', 'سلبي', 'تأخير عن موعد العمل'),
          ('NS', 'وردية ليلية', 'Night Shift', 'مالي', 'إيجابي', 'عمل في الوردية الليلية'),
          ('OT', 'ساعات إضافية', 'Overtime', 'مالي', 'إيجابي', 'ساعات عمل إضافية')
      END
    `);

  } catch (error) {

    throw error;
  }
}

// جلب سجلات التمام اليومي
async function getDailyAttendance(pool, data) {
  try {
    await setupAttendanceTables(pool);
    
    const { 
      date, 
      startDate, 
      endDate, 
      employeeCode, 
      department, 
      attendanceStatus,
      page = 1, 
      limit = 50 
    } = data;

    let whereClause = 'WHERE 1=1';
    const request = pool.request();

    if (date) {
      whereClause += ' AND AttendanceDate = @date';
      request.input('date', sql.Date, date);
    }

    if (startDate && endDate) {
      whereClause += ' AND AttendanceDate BETWEEN @startDate AND @endDate';
      request.input('startDate', sql.Date, startDate);
      request.input('endDate', sql.Date, endDate);
    }

    if (employeeCode) {
      whereClause += ' AND EmployeeCode LIKE @employeeCode';
      request.input('employeeCode', sql.NVarChar, `%${employeeCode}%`);
    }

    if (department) {
      whereClause += ' AND Department = @department';
      request.input('department', sql.NVarChar, department);
    }

    if (attendanceStatus) {
      whereClause += ' AND Attendance = @attendanceStatus';
      request.input('attendanceStatus', sql.NVarChar, attendanceStatus);
    }

    const offset = (page - 1) * limit;
    request.input('offset', sql.Int, offset);
    request.input('limit', sql.Int, limit);

    const query = `
      SELECT
        ID,
        AttendanceDate,
        EmployeeCode,
        EmployeeName,
        ISNULL(JobTitle, '') as JobTitle,
        ISNULL(Department, '') as Department,
        ISNULL(CheckInTime, '') as CheckInTime,
        ISNULL(CheckOutTime, '') as CheckOutTime,
        ISNULL(Attendance, '') as AttendanceStatus,
        ISNULL(Notes, '') as Notes,
        ISNULL(IsFromRequest, 0) as IsFromRequest,
        ISNULL(RequestID, 0) as RequestID,
        FORMAT(CreatedAt, 'yyyy-MM-dd HH:mm:ss') as CreatedAt,
        FORMAT(UpdatedAt, 'yyyy-MM-dd HH:mm:ss') as UpdatedAt
      FROM DailyAttendance
      ${whereClause}
      ORDER BY AttendanceDate DESC, EmployeeCode
      OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY
    `;

    const result = await request.query(query);

    // جلب العدد الإجمالي
    const countQuery = `
      SELECT COUNT(*) as total
      FROM DailyAttendance
      ${whereClause}
    `;

    const countResult = await pool.request().query(countQuery);
    const total = countResult.recordset[0].total;

    return NextResponse.json({
      success: true,
      data: result.recordset,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب التمام اليومي: ' + error.message
    }, { status: 500 });
  }
}

// إضافة سجل تمام جديد
async function addAttendanceRecord(pool, data) {
  try {
    await setupAttendanceTables(pool);

    const {
      attendanceDate,
      employeeCode,
      employeeName,
      jobTitle,
      department,
      attendanceStatus,
      effectType,
      effectAmount = 0,
      checkInTime,
      checkOutTime,
      leaveType,
      leaveStartDate,
      leaveEndDate,
      leaveReason,
      notes,
      remarks,
      createdBy
    } = data;

    const request = pool.request();
    request.input('attendanceDate', sql.Date, attendanceDate);
    request.input('employeeCode', sql.NVarChar, employeeCode);
    request.input('employeeName', sql.NVarChar, employeeName);
    request.input('jobTitle', sql.NVarChar, jobTitle);
    request.input('department', sql.NVarChar, department);
    request.input('attendanceStatus', sql.NVarChar, attendanceStatus);
    request.input('effectType', sql.NVarChar, effectType);
    request.input('effectAmount', sql.Decimal(8,2), effectAmount);
    request.input('checkInTime', sql.Time, checkInTime);
    request.input('checkOutTime', sql.Time, checkOutTime);
    request.input('leaveType', sql.NVarChar, leaveType);
    request.input('leaveStartDate', sql.Date, leaveStartDate);
    request.input('leaveEndDate', sql.Date, leaveEndDate);
    request.input('leaveReason', sql.NVarChar, leaveReason);
    request.input('notes', sql.NVarChar, notes);
    request.input('remarks', sql.NVarChar, remarks);
    request.input('createdBy', sql.NVarChar, createdBy);

    const result = await request.query(`
      INSERT INTO DailyAttendance (
        AttendanceDate, EmployeeCode, EmployeeName, JobTitle, Department,
        Attendance, attendanceStatus, CheckInTime, CheckOutTime, Notes, IsFromRequest
      ) VALUES (
        @attendanceDate, @employeeCode, @employeeName, @jobTitle, @department,
        @attendanceStatus, @attendanceStatus, @checkInTime, @checkOutTime, @notes, 0
      )
    `);

    return NextResponse.json({
      success: true,
      message: 'تم إضافة سجل التمام بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في إضافة سجل التمام: ' + error.message
    }, { status: 500 });
  }
}

// رفع بيانات البصمة
async function importBiometricData(pool, data) {
  try {
    await setupAttendanceTables(pool);

    const { biometricRecords } = data;

    if (!biometricRecords || !Array.isArray(biometricRecords)) {
      return NextResponse.json({
        success: false,
        error: 'بيانات البصمة غير صحيحة'
      }, { status: 400 });
    }

    let successCount = 0;
    let errorCount = 0;

    for (const record of biometricRecords) {
      try {
        const request = pool.request();
        request.input('employeeCode', sql.NVarChar, record.employeeCode);
        request.input('employeeName', sql.NVarChar, record.employeeName);
        request.input('checkTime', sql.DateTime, record.checkTime);
        request.input('checkType', sql.NVarChar, record.checkType);
        request.input('deviceID', sql.NVarChar, record.deviceID);
        request.input('verifyMode', sql.NVarChar, record.verifyMode);

        await request.query(`
          INSERT INTO BiometricData (
            EmployeeCode, EmployeeName, CheckTime, CheckType, DeviceID, VerifyMode
          ) VALUES (
            @employeeCode, @employeeName, @checkTime, @checkType, @deviceID, @verifyMode
          )
        `);

        successCount++;
      } catch (error) {

        errorCount++;
      }
    }

    return NextResponse.json({
      success: true,
      message: `تم رفع ${successCount} سجل بنجاح، فشل في ${errorCount} سجل`,
      successCount,
      errorCount
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في رفع بيانات البصمة: ' + error.message
    }, { status: 500 });
  }
}

// معالجة بيانات البصمة وتحويلها لتمام يومي
async function processBiometricData(pool, data) {
  try {
    await setupAttendanceTables(pool);

    const { processDate } = data;
    const targetDate = processDate || new Date().toISOString().split('T')[0];

    const request = pool.request();
    request.input('processDate', sql.Date, targetDate);

    // معالجة بيانات البصمة
    const result = await request.query(`
      MERGE DailyAttendance AS target
      USING (
          SELECT
              @processDate as AttendanceDate,
              bd.EmployeeCode,
              bd.EmployeeName,
              e.JobTitle,
              e.Department,
              MIN(CASE WHEN bd.CheckType = 'IN' THEN bd.CheckTime END) as FirstCheckIn,
              MAX(CASE WHEN bd.CheckType = 'OUT' THEN bd.CheckTime END) as LastCheckOut
          FROM BiometricData bd
          LEFT JOIN Employees e ON bd.EmployeeCode = e.EmployeeCode
          WHERE CAST(bd.CheckTime AS DATE) = @processDate
              AND bd.IsProcessed = 0
          GROUP BY bd.EmployeeCode, bd.EmployeeName, e.JobTitle, e.Department
      ) AS source ON target.EmployeeCode = source.EmployeeCode
                  AND target.AttendanceDate = source.AttendanceDate

      WHEN MATCHED THEN
          UPDATE SET
              CheckInTime = FORMAT(source.FirstCheckIn, 'HH:mm'),
              CheckOutTime = FORMAT(source.LastCheckOut, 'HH:mm'),
              Attendance = 'حضور',
              UpdatedAt = GETDATE()

      WHEN NOT MATCHED THEN
          INSERT (AttendanceDate, EmployeeCode, EmployeeName, JobTitle, Department,
                 CheckInTime, CheckOutTime, Attendance, IsFromRequest)
          VALUES (source.AttendanceDate, source.EmployeeCode, source.EmployeeName,
                 source.JobTitle, source.Department, FORMAT(source.FirstCheckIn, 'HH:mm'),
                 FORMAT(source.LastCheckOut, 'HH:mm'), 'حضور', 0);
    `);

    // تحديث حالة المعالجة
    await pool.request()
      .input('processDate', sql.Date, targetDate)
      .query(`
        UPDATE BiometricData
        SET IsProcessed = 1, ProcessedAt = GETDATE()
        WHERE CAST(CheckTime AS DATE) = @processDate AND IsProcessed = 0
      `);

    return NextResponse.json({
      success: true,
      message: `تم معالجة بيانات البصمة لتاريخ ${targetDate} بنجاح`,
      processedDate: targetDate
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في معالجة بيانات البصمة: ' + error.message
    }, { status: 500 });
  }
}
