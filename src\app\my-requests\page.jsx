'use client';

import MainLayout from '@/components/MainLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import actionLogger from '@/utils/actionLogger';
import { formatDateToDDMMYYYY } from '@/utils/dateFormat';
import { useEffect, useState } from 'react';
import {
    FiCalendar,
    FiCheck,
    FiChevronLeft, FiChevronRight,
    FiEdit,
    FiFilter,
    FiPrinter,
    FiRefreshCw,
    FiRotateCcw, FiSettings,
    FiTrash2,
    FiX
} from 'react-icons/fi';

export default function MyRequestsPage() {
  const { isArabic } = useLanguage();
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({
    status: 'all',
    requestType: 'all',
    leaveType: 'all',
    startDate: '',
    endDate: '',
    employeeCode: '',
    employeeName: ''
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0
  });

  // حالات الطلبات
  const statusOptions = [
    { value: 'all', label: isArabic ? 'جميع الحالات' : 'All Status' },
    { value: 'قيد المراجعة', label: isArabic ? 'قيد المراجعة' : 'Under Review' },
    { value: 'معتمدة', label: isArabic ? 'معتمدة' : 'Approved' },
    { value: 'مرفوضة', label: isArabic ? 'مرفوضة' : 'Rejected' }
  ];

  // أنواع الطلبات الورقية
  const requestTypeOptions = [
    { value: 'all', label: isArabic ? 'جميع الطلبات' : 'All Requests' },
    { value: 'leave', label: isArabic ? 'طلب إجازة' : 'Leave Request' },
    { value: 'mission', label: isArabic ? 'طلب مأمورية' : 'Mission Request' },
    { value: 'permission', label: isArabic ? 'طلب إذن' : 'Permission Request' },
    { value: 'night_shift', label: isArabic ? 'إخطار وردية ليلية' : 'Night Shift Notification' }
  ];

  // أنواع الإجازات (للفلترة الفرعية)
  const leaveTypeOptions = [
    { value: 'all', label: isArabic ? 'جميع أنواع الإجازات' : 'All Leave Types' },
    { value: 'اعتيادية', label: isArabic ? 'إجازة اعتيادية' : 'Regular Leave' },
    { value: 'مرضية', label: isArabic ? 'إجازة مرضية' : 'Sick Leave' },
    { value: 'عارضة', label: isArabic ? 'إجازة عارضة' : 'Emergency Leave' },
    { value: 'بدل', label: isArabic ? 'إجازة بدل' : 'Compensation Leave' }
  ];

  // جلب جميع الطلبات الورقية
  const fetchRequests = async () => {
    try {
      setLoading(true);

      // جلب جميع الطلبات الورقية
      const queryParams = new URLSearchParams({
        action: 'list',
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...filters
      });

      const response = await fetch(`/api/paper-requests?${queryParams}`);
      const result = await response.json();

      if (result.success) {
        setRequests(result.requests || []);

        // حساب الترقيم محلياً
        const total = (result.requests || []).length;
        const totalPages = Math.ceil(total / pagination.limit);
        setPagination(prev => ({ ...prev, total, totalPages }));
      } else {

      }
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  // تحديث الفلاتر
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, page: 1 })); // العودة للصفحة الأولى
  };

  // تغيير الصفحة
  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  // تحديث حالة الطلب مع حفظ التغييرات
  const updateRequestStatus = async (requestId, status, notes = '') => {
    try {
      // الحصول على كود المستخدم من localStorage
      const userInfo = localStorage.getItem('userInfo');
      const userCode = userInfo ? JSON.parse(userInfo).code || '1450' : '1450';

      const response = await fetch('/api/paper-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-code': userCode
        },
        body: JSON.stringify({
          action: 'update-status',
          requestId,
          status,
          notes,
          approvedBy: userCode
        })
      });

      const result = await response.json();
      if (result.success) {
        // تحديث الطلب محلياً لتجنب إعادة التحميل
        const updatedRequest = requests.find(req => req.ID === requestId);
        setRequests(prev => prev.map(req =>
          req.ID === requestId
            ? { ...req, Status: status, ApprovalNotes: notes, ApprovedBy: userCode }
            : req
        ));

        // تسجيل الإجراء في نظام الإشعارات والتنبيهات
        if (updatedRequest) {
          // تسجيل الإجراء حسب نوع الطلب
          if (updatedRequest.RequestType === 'leave') {
            actionLogger.logLeaveAction(
              status === 'معتمدة' ? 'approve' : 'reject',
              {
                ...updatedRequest,
                leaveType: updatedRequest.LeaveType,
                employeeName: updatedRequest.EmployeeName,
                employeeCode: updatedRequest.EmployeeCode,
                startDate: updatedRequest.StartDate,
                endDate: updatedRequest.EndDate,
                daysCount: updatedRequest.DaysCount,
                requestId: updatedRequest.ID
              }
            );
          } else {
            // تسجيل إجراءات الطلبات الورقية الأخرى
            actionLogger.logPaperRequestAction(
              status === 'معتمدة' ? 'approve' : 'reject',
              updatedRequest.RequestType,
              {
                ...updatedRequest,
                employeeName: updatedRequest.EmployeeName,
                employeeCode: updatedRequest.EmployeeCode,
                requestId: updatedRequest.ID
              }
            );
          }
        }

        alert(result.message || `تم ${status === 'معتمدة' ? 'اعتماد' : 'رفض'} الطلب بنجاح`);
      } else {
        alert(result.error || (isArabic ? 'خطأ في تحديث الطلب' : 'Error updating request'));
      }
    } catch (error) {

      alert(isArabic ? 'خطأ في الاتصال بالخادم' : 'Server connection error');
    }
  };

  // حذف الطلب
  const deleteRequest = async (requestId) => {
    if (!confirm(isArabic ? 'هل أنت متأكد من حذف هذا الطلب؟' : 'Are you sure you want to delete this request?')) {
      return;
    }

    try {
      const userInfo = localStorage.getItem('userInfo');
      const userCode = userInfo ? JSON.parse(userInfo).code || '1450' : '1450';

      const response = await fetch('/api/paper-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-code': userCode
        },
        body: JSON.stringify({
          action: 'delete',
          requestId
        })
      });

      const result = await response.json();
      if (result.success) {
        // تسجيل إجراء الحذف قبل إزالة الطلب
        const deletedRequest = requests.find(req => req.ID === requestId);
        if (deletedRequest) {
          if (deletedRequest.RequestType === 'leave') {
            actionLogger.logLeaveAction('delete', {
              ...deletedRequest,
              leaveType: deletedRequest.LeaveType,
              employeeName: deletedRequest.EmployeeName,
              employeeCode: deletedRequest.EmployeeCode,
              requestId: deletedRequest.ID
            });
          } else {
            actionLogger.logAction(
              'DELETE_PAPER_REQUEST',
              `حذف طلب ${deletedRequest.RequestType} للموظف ${deletedRequest.EmployeeName}`,
              deletedRequest,
              'PaperRequests',
              deletedRequest.ID
            );
          }
        }

        // إزالة الطلب محلياً
        setRequests(prev => prev.filter(req => req.ID !== requestId));
        alert(isArabic ? 'تم حذف الطلب بنجاح' : 'Request deleted successfully');
      } else {
        alert(result.error || (isArabic ? 'خطأ في حذف الطلب' : 'Error deleting request'));
      }
    } catch (error) {

      alert(isArabic ? 'خطأ في الاتصال بالخادم' : 'Server connection error');
    }
  };

  // دالة تنظيف الطلبات المكررة
  const cleanupDuplicates = async () => {
    if (!confirm(isArabic ? 'هل أنت متأكد من تنظيف الطلبات المكررة؟ سيتم الاحتفاظ بأقدم طلب وحذف الباقي.' : 'Are you sure you want to cleanup duplicate requests? The oldest request will be kept and others will be deleted.')) {
      return;
    }

    try {
      setLoading(true);

      const response = await fetch('/api/paper-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'cleanup-duplicates'
        }),
      });

      const result = await response.json();

      if (result.success) {
        alert(isArabic ? `تم تنظيف ${result.cleaned} طلب مكرر بنجاح` : `Successfully cleaned ${result.cleaned} duplicate requests`);
        fetchRequests(); // إعادة تحميل القائمة
      } else {
        alert(result.error || (isArabic ? 'خطأ في تنظيف الطلبات المكررة' : 'Error cleaning duplicate requests'));
      }
    } catch (error) {

      alert(isArabic ? 'خطأ في الاتصال بالخادم' : 'Server connection error');
    } finally {
      setLoading(false);
    }
  };

  // تعديل الطلب
  const editRequest = (request) => {
    // توجيه المستخدم لصفحة التعديل حسب نوع الطلب
    const editUrls = {
      'leave': '/requests/leave',
      'mission': '/requests/mission',
      'permission': '/requests/permission',
      'night_shift': '/requests/night-shift'
    };

    const editUrl = editUrls[request.RequestType];
    if (editUrl) {
      // إضافة معرف الطلب للتعديل
      window.location.href = `${editUrl}?edit=${request.ID}`;
    } else {
      alert(isArabic ? 'نوع الطلب غير مدعوم للتعديل' : 'Request type not supported for editing');
    }
  };

  // تحويل اللوجو إلى base64
  const getLogoBase64 = async () => {
    try {
      const response = await fetch('/logo.png');
      const blob = await response.blob();
      return new Promise((resolve) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result.split(',')[1]);
        reader.readAsDataURL(blob);
      });
    } catch (error) {

      return '';
    }
  };

  // طباعة الطلب
  const printRequest = async (requestId) => {
    try {
      const response = await fetch(`/api/paper-requests?action=print&requestId=${requestId}`);
      const result = await response.json();

      if (result.success) {
        if (result.data.RequestType === 'leave') {
          // استخدام النموذج المحسن لطلبات الإجازة
          await printLeaveRequestCorrect(result.data);
        } else {
          // باقي أنواع الطلبات - تحميل اللوجو أولاً
          const logoBase64 = await getLogoBase64();
          const printWindow = window.open('', '_blank');
          const printContent = generatePrintContent(result.data, logoBase64);

          if (printWindow && printWindow.document) {
            printWindow.document.write(printContent);
            printWindow.document.close();

            printWindow.onload = () => {
              setTimeout(() => {
                printWindow.print();
              }, 500);
            };
          }
        }
      } else {
        alert(result.error || 'خطأ في جلب بيانات الطباعة');
      }
    } catch (error) {

      alert('خطأ في الطباعة');
    }
  };

  // طباعة طلب الإجازة الصحيح (مطابق لصفحة طلب الإجازة)
  const printLeaveRequestCorrect = async (requestData) => {
    // جلب البيانات الإضافية المطلوبة
    let lastLeaveDate = '';
    let remainingBalance = '';

    if (requestData.EmployeeCode && requestData.LeaveType) {
      try {
        // جلب تاريخ آخر إجازة معتمدة
        const lastLeaveResponse = await fetch(`/api/paper-requests?action=getLastApprovedLeave&employeeId=${requestData.EmployeeCode}`);
        if (lastLeaveResponse.ok) {
          const lastLeaveResult = await lastLeaveResponse.json();
          if (lastLeaveResult.success && lastLeaveResult.lastLeave) {
            lastLeaveDate = formatDate(lastLeaveResult.lastLeave.EndDate);
          }
        }

        // جلب الرصيد المتبقي
        const balanceResponse = await fetch(`/api/leave-balance?employeeId=${requestData.EmployeeCode}&leaveType=${requestData.LeaveType}`);
        if (balanceResponse.ok) {
          const balanceResult = await balanceResponse.json();
          if (balanceResult.success) {
            remainingBalance = balanceResult.balance || '';
          }
        }
      } catch (error) {

      }
    }

    // تحميل اللوجو أولاً قبل فتح نافذة الطباعة
    const logoBase64 = await getLogoBase64();

    // انتظار إضافي للتأكد من تحميل اللوجو
    await new Promise(resolve => setTimeout(resolve, 1000));

    const printWindow = window.open('', '_blank');

    // إضافة timestamp لمنع cache
    const timestamp = new Date().getTime();

    const printContent = `
      <!DOCTYPE html>
      <html dir="rtl">
      <!-- Updated: ${timestamp} - Fixed name/code order, spacing, notes alignment -->
      <head>
        <meta charset="UTF-8">
        <title>نموذج طلب إجازة - ${requestData.EmployeeName}</title>
        <style>
          @page {
            size: A4 portrait;
            margin: 10mm;
          }

          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          body {
            font-family: 'Arial', sans-serif;
            font-size: 11px;
            line-height: 1.3;
            color: #000 !important;
            background: white !important;
            direction: rtl;
            text-align: right;
            width: 190mm;
            max-width: 190mm;
            margin: 0 auto;
            padding: 5mm;
          }

          /* Header Table - مطابق للنموذج الأصلي تماماً */
          .header-table {
            width: 100%;
            border-collapse: collapse;
            border: 2px solid #000;
            margin-bottom: 8px;
            table-layout: fixed;
          }

          .header-table td {
            border: 1px solid #000;
            padding: 6px;
            text-align: center;
            vertical-align: middle;
            height: 50px;
            font-size: 10px;
          }

          .logo-cell {
            width: 33.33%;
          }

          .logo-img {
            max-width: 100px;
            max-height: 70px;
            object-fit: contain;
          }

          .form-title-cell {
            width: 33.33%;
            font-weight: bold;
            text-align: center;
          }

          .company-cell {
            width: 33.33%;
            font-weight: bold;
          }

          .form-title {
            font-size: 14px;
            margin-bottom: 3px;
            font-weight: bold;
          }

          .form-code {
            font-size: 10px;
            color: #666;
          }

          .company-name-ar {
            font-size: 10px;
            margin-bottom: 2px;
            font-weight: bold;
          }

          .company-name-en {
            font-size: 8px;
            color: #666;
            font-style: italic;
          }

          /* Section Headers - مطابق للنموذج الأصلي */
          .section-header {
            background-color: #f0f0f0;
            text-align: center;
            padding: 6px;
            font-size: 11px;
            font-weight: bold;
            border: 1px solid #000;
            margin: 8px 0 5px 0;
          }

          /* Form Fields */
          .form-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            min-height: 25px;
            font-size: 10px;
          }

          .field-label {
            font-weight: bold;
            margin-left: 8px;
            min-width: 100px;
            font-size: 10px;
            text-align: right;
            flex-shrink: 0;
          }

          .field-value {
            border-bottom: 1px solid #000;
            padding: 2px 6px;
            min-width: 120px;
            text-align: center;
            font-size: 10px;
          }

          .full-width-field {
            border-bottom: 1px solid #000;
            padding: 2px 6px;
            flex: 1;
            text-align: right;
            font-size: 10px;
            margin-right: 0;
          }

          /* محاذاة خاصة للصفوف الكاملة العرض */
          .full-width-row {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            min-height: 20px;
            font-size: 10px;
          }

          .full-width-row .field-label {
            width: 100px;
            min-width: 100px;
            text-align: right;
            margin-left: 8px;
            flex-shrink: 0;
          }

          /* Leave Type Section */
          .leave-types-container {
            display: flex;
            margin: 5px 0;
            font-size: 10px;
            border: 1px solid #000 !important;
          }

          .leave-types-left {
            width: 50%;
            padding: 8px;
            border-right: 1px solid #000 !important;
          }

          .leave-types-right {
            width: 50%;
            padding: 8px;
          }

          .leave-option {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            font-size: 10px;
          }

          .checkbox {
            width: 10px;
            height: 10px;
            border: 1px solid #000;
            margin-left: 6px;
            display: inline-block;
            text-align: center;
            line-height: 8px;
            font-size: 7px;
          }

          .checkbox.checked {
            background-color: #000;
            color: white;
          }

          .leave-duration {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 5px 0;
            padding: 5px;
            border: 1px solid #000;
          }

          /* Signatures Section */
          .signatures-container {
            margin-top: 20px;
          }

          .signature-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
          }

          .signature-box {
            text-align: center;
            width: 200px;
          }

          .signature-title {
            font-weight: bold;
            font-size: 10px;
            margin-bottom: 5px;
          }

          .signature-line {
            border-bottom: 1px solid #000;
            height: 20px;
            margin-bottom: 5px;
          }

          /* HR Section */
          .hr-section {
            margin-top: 20px;
            border: 1px solid #000;
          }

          .hr-header {
            background-color: #a8c5f0;
            text-align: center;
            padding: 8px;
            font-weight: bold;
            font-size: 12px;
            border-bottom: 1px solid #000;
          }

          .hr-content {
            padding: 15px;
          }

          .notes-lines {
            line-height: 2;
            margin-bottom: 15px;
          }

          .hr-specialist {
            text-align: center;
            margin: 15px 0;
          }

          .hr-notes {
            font-size: 10px;
            border-top: 1px solid #000;
            padding-top: 10px;
            margin-top: 15px;
          }

          @media print {
            @page {
              size: A4 portrait;
              margin: 10mm;
            }

            body {
              margin: 0 !important;
              padding: 0 !important;
              width: 190mm !important;
              max-width: 190mm !important;
              font-size: 10px !important;
            }

            .no-print {
              display: none !important;
            }

            * {
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
            }
          }
        </style>
      </head>
      <body>
        <!-- Header Table - مطابق للنموذج الأصلي تماماً -->
        <table class="header-table">
          <tr>
            <td class="company-cell">
              <div class="company-name-ar">شركة كونكورد للهندسة والمقاولات</div>
              <div class="company-name-en">Concord for Engineering & Contracting</div>
            </td>
            <td class="form-title-cell">
              <div class="form-title">طلب إجازة</div>
              <div class="form-code">HR-OP-01-F01</div>
            </td>
            <td class="logo-cell">
              ${logoBase64 ?
                `<img src="data:image/png;base64,${logoBase64}" alt="Concord Logo" class="logo-img" />` :
                `<div style="background:#1e40af;color:white;padding:8px;text-align:center;font-weight:bold;font-size:10px;border-radius:4px;">CONCORD<br>COMPANY</div>`
              }
            </td>
          </tr>
        </table>

        <!-- Section Header -->
        <div class="section-header">بيانات الطلب</div>

        <!-- Employee Information - مطابق للنموذج الأصلي -->
        <div class="form-row" style="justify-content: space-between; align-items: center; margin-bottom: 10px;">
          <div style="display: flex; align-items: center;">
            <span class="field-label" style="margin-left: 8px;">الاسم:</span>
            <div class="field-value" style="width: 300px; text-align: center;">${requestData.EmployeeName || ''}</div>
          </div>
          <div style="display: flex; align-items: center;">
            <span class="field-label" style="margin-left: 8px;">الكود الوظيفي:</span>
            <div class="field-value" style="width: 100px; text-align: center;">${requestData.EmployeeCode || ''}</div>
          </div>
        </div>

        <div class="full-width-row">
          <span class="field-label">الوظيفة:</span>
          <div class="full-width-field">${requestData.JobTitle || ''}</div>
        </div>

        <div class="full-width-row">
          <span class="field-label">الإدارة / المشروع:</span>
          <div class="full-width-field">مشروع مجمع مبانى أوجيستا</div>
        </div>

        <!-- البيانات الإضافية المطلوبة -->
        <div class="form-row" style="justify-content: space-between; align-items: center; margin-bottom: 10px;">
          <div style="display: flex; align-items: center;">
            <span class="field-label" style="margin-left: 8px;">تاريخ الطباعة:</span>
            <div class="field-value" style="width: 120px; text-align: center;">${formatDate(new Date())}</div>
          </div>
          <div style="display: flex; align-items: center;">
            <span class="field-label" style="margin-left: 8px;">تاريخ آخر إجازة:</span>
            <div class="field-value" style="width: 120px; text-align: center;">${lastLeaveDate}</div>
          </div>
          <div style="display: flex; align-items: center;">
            <span class="field-label" style="margin-left: 8px;">الرصيد المتبقي:</span>
            <div class="field-value" style="width: 100px; text-align: center;">${remainingBalance}</div>
          </div>
        </div>

        <!-- Leave Types Section - مطابق للنموذج الأصلي مع إضافة الضلع الناقص -->
        <div class="leave-types-container" style="border: 1px solid #000;">
          <div class="leave-types-left" style="border-right: 1px solid #000;">
            <div class="leave-option">
              <span class="checkbox ${requestData.LeaveType === 'اعتيادية' || requestData.LeaveType === 'annual' ? 'checked' : ''}">
                ${requestData.LeaveType === 'اعتيادية' || requestData.LeaveType === 'annual' ? '■' : ''}
              </span>
              <span>إجازة إعتيادية</span>
            </div>
            <div class="leave-option">
              <span class="checkbox ${requestData.LeaveType === 'مرضية' || requestData.LeaveType === 'sick' ? 'checked' : ''}">
                ${requestData.LeaveType === 'مرضية' || requestData.LeaveType === 'sick' ? '■' : ''}
              </span>
              <span>إجازة مرضية / إصابة</span>
            </div>
          </div>
          <div class="leave-types-right">
            <div class="leave-option">
              <span class="checkbox ${requestData.LeaveType === 'عارضة' || requestData.LeaveType === 'emergency' ? 'checked' : ''}">
                ${requestData.LeaveType === 'عارضة' || requestData.LeaveType === 'emergency' ? '■' : ''}
              </span>
              <span>إجازة عارضة</span>
            </div>
            <div class="leave-option">
              <span class="checkbox ${requestData.LeaveType === 'بدون أجر' || requestData.LeaveType === 'unpaid' ? 'checked' : ''}">
                ${requestData.LeaveType === 'بدون أجر' || requestData.LeaveType === 'unpaid' ? '■' : ''}
              </span>
              <span>إجازة بدون أجر</span>
            </div>
          </div>
        </div>

        <div style="text-align: center; font-size: 10px; margin: 5px 0;">
          ( بدل - حج - عمرة - وضع - ولادة - وفاة - زواج )
        </div>
        <div style="text-align: center; font-size: 10px; margin-bottom: 10px;">
          ( إجازات أخرى )
        </div>

        <div class="leave-option" style="text-align: center; margin: 10px 0;">
          <span class="checkbox ${requestData.LeaveType === 'بدل' || requestData.LeaveType === 'badal' ? 'checked' : ''}">
            ${requestData.LeaveType === 'بدل' || requestData.LeaveType === 'badal' ? '■' : ''}
          </span>
          <span>إجازة بدل</span>
        </div>

        <!-- Leave Duration - مطابق للنموذج الأصلي -->
        <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
          <tr>
            <td style="border: 1px solid #000; padding: 8px; text-align: center; width: 33.33%;">
              <span style="font-weight: bold;">عدد الأيام:</span>
              <div style="margin-top: 5px;">
                <span style="border-bottom: 1px solid #000; padding: 2px 8px; margin: 0 5px;">${requestData.DaysCount || ''}</span>
              </div>
            </td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center; width: 33.33%;">
              <span style="font-weight: bold;">إلى:</span>
              <div style="margin-top: 5px;">
                <span style="border-bottom: 1px solid #000; padding: 2px 8px; margin: 0 5px;">${formatDate(requestData.EndDate) || ''}</span>
              </div>
            </td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center; width: 33.33%;">
              <span style="font-weight: bold;">مدة الإجازة من:</span>
              <div style="margin-top: 5px;">
                <span style="border-bottom: 1px solid #000; padding: 2px 8px; margin: 0 5px;">${formatDate(requestData.StartDate) || ''}</span>
              </div>
            </td>
          </tr>
        </table>

        <!-- Signatures Section - مطابق للنموذج الأصلي -->
        <div style="margin: 15px 0;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
            <div style="text-align: center; width: 45%;">
              <div style="font-weight: bold; margin-bottom: 30px;">توقيع الموظف</div>
              <div style="border-bottom: 1px solid #000; width: 150px; margin: 0 auto;"></div>
            </div>
            <div style="text-align: center; width: 45%;">
              <div style="font-weight: bold; margin-bottom: 30px;">اعتماد الرئيس المباشر</div>
              <div style="border-bottom: 1px solid #000; width: 150px; margin: 0 auto;"></div>
            </div>
          </div>
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
            <div style="text-align: center; width: 45%;">
              <div style="font-weight: bold; margin-bottom: 30px;">المدير الإداري</div>
              <div style="border-bottom: 1px solid #000; width: 150px; margin: 0 auto;"></div>
            </div>
            <div style="text-align: center; width: 45%;">
              <div style="font-weight: bold; margin-bottom: 30px;">اعتماد مدير المشروع</div>
              <div style="border-bottom: 1px solid #000; width: 150px; margin: 0 auto;"></div>
            </div>
          </div>
        </div>

        <!-- HR Section - مطابق للنموذج الأصلي -->
        <div class="hr-section">
          <div class="hr-header">إدارة الموارد البشرية</div>
          <div class="hr-content">
            <!-- الملاحظات في الأعلى -->
            <div style="text-align: right; margin-bottom: 20px;">
              <div style="font-weight: bold; margin-bottom: 8px;">ملاحظات:</div>
              <div style="line-height: 1.8;">
                ................................................................................................................................................................................................<br>
                ................................................................................................................................................................................................
              </div>
            </div>

            <!-- توقيع أخصائي موارد بشرية تحت الملاحظات -->
            <div style="text-align: left; margin-bottom: 15px;">
              <div style="font-weight: bold; margin-bottom: 30px;">أخصائي موارد بشرية</div>
              <div style="border-bottom: 1px solid #000; width: 150px; height: 15px;"></div>
            </div>

            <!-- الملاحظات السفلية -->
            <div style="font-size: 10px; text-align: right; border-top: 1px solid #000; padding-top: 10px;">
              <div style="margin-bottom: 3px;">
                في حالة الإجازة المرضية يتم إرفاق التقرير الطبي.
              </div>
              <div>
                في حالة عدم وجود رصيد إجازات سنوية يتم احتساب الطلب إجازة بدون أجر.
              </div>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();

    // انتظار تحميل المحتوى والصور قبل الطباعة
    printWindow.onload = () => {
      setTimeout(() => {
        printWindow.print();
      }, 500);
    };

    // احتياطي في حالة عدم تشغيل onload
    setTimeout(() => {
      printWindow.print();
    }, 1500);
  };

  // إنشاء محتوى الطباعة
  const generatePrintContent = (requestData, logoBase64 = '') => {
    switch (requestData.RequestType) {
      case 'leave':
        return generateLeaveFormContent(requestData, logoBase64);
      case 'mission':
        return generateMissionFormContent(requestData, logoBase64);
      case 'permission':
        return generatePermissionFormContent(requestData, logoBase64);
      case 'night_shift':
        return generateNightShiftFormContent(requestData, logoBase64);
      default:
        return generateGenericFormContent(requestData, logoBase64);
    }
  };

  // إنشاء نموذج طلب الإجازة للطباعة - مطابق للنموذج الأصلي
  const generateLeaveFormContent = (requestData) => {
    const startDate = requestData.StartDate ? formatDate(requestData.StartDate) : '';
    const endDate = requestData.EndDate ? formatDate(requestData.EndDate) : '';

    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>طلب إجازة</title>
        <style>
          @page {
            size: A4 portrait;
            margin: 10mm;
          }

          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.3;
            color: black !important;
            background: white !important;
            margin: 0;
            padding: 5mm;
            direction: rtl;
            width: 190mm;
            max-width: 190mm;
          }
          .header-table {
            width: 100%;
            border-collapse: collapse;
            border: 2px solid #000;
            margin-bottom: 8px;
            table-layout: fixed;
          }
          .header-table td {
            border: 1px solid #000;
            padding: 6px;
            text-align: center;
            vertical-align: middle;
            height: 50px;
            font-size: 10px;
          }
          .company-cell {
            width: 35%;
            font-size: 9px;
          }
          .company-name-ar {
            font-weight: bold;
            margin-bottom: 2px;
            font-size: 10px;
          }
          .company-name-en {
            font-size: 8px;
            color: #666;
          }
          .form-title-cell {
            width: 30%;
          }
          .form-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 3px;
          }
          .form-code {
            font-size: 10px;
            color: #666;
          }
          .logo-cell {
            width: 35%;
          }
          .section-header {
            text-align: center;
            font-weight: bold;
            margin: 8px 0 5px 0;
            padding: 6px;
            border: 1px solid #000;
            background-color: #f0f0f0;
            font-size: 11px;
          }
          .form-row {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            margin-bottom: 10px;
            min-height: 25px;
            font-size: 10px;
          }
          .field-label {
            font-weight: bold;
            margin-left: 8px;
            min-width: 100px;
            font-size: 10px;
            text-align: right;
            flex-shrink: 0;
          }
          .field-value {
            border-bottom: 1px solid #000;
            padding: 2px 6px;
            text-align: center;
            min-height: 16px;
            font-size: 10px;
          }
          .full-width-field {
            border-bottom: 1px solid #000;
            padding: 2px 6px;
            flex: 1;
            text-align: right;
            min-height: 16px;
            font-size: 10px;
            margin-right: 0;
          }

          /* محاذاة خاصة للصفوف الكاملة العرض */
          .full-width-row {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            min-height: 20px;
            font-size: 10px;
          }

          .full-width-row .field-label {
            width: 100px;
            min-width: 100px;
            text-align: right;
            margin-left: 8px;
            flex-shrink: 0;
          }
          .leave-types-container {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            border: 1px solid #000;
            padding: 8px;
            font-size: 10px;
          }
          .leave-types-left, .leave-types-right {
            width: 48%;
          }
          .leave-option {
            display: flex;
            align-items: center;
            margin-bottom: 6px;
            font-size: 10px;
          }
          .checkbox {
            width: 10px;
            height: 10px;
            border: 1px solid #000;
            margin-left: 6px;
            text-align: center;
            line-height: 8px;
            font-size: 7px;
          }
          .checkbox.checked {
            background: #000;
            color: white;
          }
          .hr-section {
            border: 1px solid #000;
            margin-top: 15px;
            padding: 8px;
          }
          .hr-header {
            text-align: center;
            font-weight: bold;
            margin-bottom: 8px;
            border-bottom: 1px solid #000;
            padding-bottom: 4px;
            font-size: 11px;
          }
          .hr-content {
            font-size: 9px;
          }

          @media print {
            @page {
              size: A4 portrait;
              margin: 10mm;
            }

            body {
              margin: 0 !important;
              padding: 0 !important;
              width: 190mm !important;
              max-width: 190mm !important;
              font-size: 10px !important;
            }

            .no-print {
              display: none !important;
            }

            * {
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
            }
          }
        </style>
      </head>
      <body>
        <!-- Header Table - مطابق للنموذج الأصلي تماماً -->
        <table class="header-table">
          <tr>
            <td class="company-cell">
              <div class="company-name-ar">شركة كونكورد للهندسة والمقاولات</div>
              <div class="company-name-en">Concord for Engineering & Contracting</div>
            </td>
            <td class="form-title-cell">
              <div class="form-title">طلب إجازة</div>
              <div class="form-code">HR-OP-01-F01</div>
            </td>
            <td class="logo-cell">
              <div style="background:#1e40af;color:white;padding:8px;text-align:center;font-weight:bold;font-size:10px;border-radius:4px;">CONCORD<br>COMPANY</div>
            </td>
          </tr>
        </table>

        <!-- Section Header -->
        <div class="section-header">بيانات الطلب</div>

        <!-- Employee Information - مطابق للنموذج الأصلي -->
        <div class="form-row" style="justify-content: flex-end; margin-right: 0;">
          <span class="field-label" style="margin-right: 0;">الاسم:</span>
          <div class="field-value" style="width: 300px;">${requestData.EmployeeName || ''}</div>
          <span style="margin: 0 20px;"></span>
          <span class="field-label" style="margin-right: 0;">الكود الوظيفي:</span>
          <div class="field-value" style="width: 100px;">${requestData.EmployeeCode || ''}</div>
        </div>

        <div class="full-width-row">
          <span class="field-label">الوظيفة:</span>
          <div class="full-width-field">${requestData.JobTitle || ''}</div>
        </div>

        <div class="full-width-row">
          <span class="field-label">الإدارة / المشروع:</span>
          <div class="full-width-field">مشروع مجمع مبانى أوجيستا</div>
        </div>

        <!-- البيانات الإضافية المطلوبة -->
        <div class="form-row" style="justify-content: flex-end; margin-right: 0;">
          <span class="field-label" style="margin-right: 0;">تاريخ الطباعة:</span>
          <div class="field-value" style="width: 120px;">${formatDate(new Date())}</div>
          <span style="margin: 0 20px;"></span>
          <span class="field-label" style="margin-right: 0;">تاريخ آخر إجازة:</span>
          <div class="field-value" style="width: 120px;">${requestData.LastLeaveDate ? formatDate(requestData.LastLeaveDate) : ''}</div>
          <span style="margin: 0 20px;"></span>
          <span class="field-label" style="margin-right: 0;">الرصيد المتبقي:</span>
          <div class="field-value" style="width: 100px;">${requestData.RemainingBalance || ''}</div>
        </div>

        <!-- Leave Types Section - مطابق للنموذج الأصلي -->
        <div class="leave-types-container">
          <div class="leave-types-left">
            <div class="leave-option">
              <span class="checkbox ${requestData.LeaveType === 'اعتيادية' ? 'checked' : ''}">
                ${requestData.LeaveType === 'اعتيادية' ? '■' : ''}
              </span>
              <span>إجازة إعتيادية</span>
            </div>
            <div class="leave-option">
              <span class="checkbox ${requestData.LeaveType === 'مرضية' ? 'checked' : ''}">
                ${requestData.LeaveType === 'مرضية' ? '■' : ''}
              </span>
              <span>إجازة مرضية / إصابة</span>
            </div>
          </div>
          <div class="leave-types-right">
            <div class="leave-option">
              <span class="checkbox ${requestData.LeaveType === 'عارضة' ? 'checked' : ''}">
                ${requestData.LeaveType === 'عارضة' ? '■' : ''}
              </span>
              <span>إجازة عارضة</span>
            </div>
            <div class="leave-option">
              <span class="checkbox ${requestData.LeaveType === 'بدون أجر' ? 'checked' : ''}">
                ${requestData.LeaveType === 'بدون أجر' ? '■' : ''}
              </span>
              <span>إجازة بدون أجر</span>
            </div>
          </div>
        </div>

        <div style="text-align: center; font-size: 10px; margin: 5px 0;">
          ( بدل - حج - عمرة - وضع - ولادة - وفاة - زواج )
        </div>
        <div style="text-align: center; font-size: 10px; margin-bottom: 10px;">
          ( إجازات أخرى )
        </div>

        <div class="leave-option" style="text-align: center; margin: 10px 0;">
          <span class="checkbox">
          </span>
          <span>إجازة بدل</span>
        </div>

        <!-- Leave Duration - مطابق للنموذج الأصلي -->
        <table style="width: 100%; border-collapse: collapse; margin: 10px 0;">
          <tr>
            <td style="border: 1px solid #000; padding: 8px; text-align: center; width: 33.33%;">
              <span style="font-weight: bold;">عدد الأيام:</span>
              <div style="margin-top: 5px;">
                <span style="border-bottom: 1px solid #000; padding: 2px 8px; margin: 0 5px;">${requestData.DaysCount || ''}</span>
              </div>
            </td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center; width: 33.33%;">
              <span style="font-weight: bold;">إلى:</span>
              <div style="margin-top: 5px;">
                <span style="border-bottom: 1px solid #000; padding: 2px 8px; margin: 0 5px;">${endDate}</span>
              </div>
            </td>
            <td style="border: 1px solid #000; padding: 8px; text-align: center; width: 33.33%;">
              <span style="font-weight: bold;">مدة الإجازة من:</span>
              <div style="margin-top: 5px;">
                <span style="border-bottom: 1px solid #000; padding: 2px 8px; margin: 0 5px;">${startDate}</span>
              </div>
            </td>
          </tr>
        </table>

        <!-- Signatures Section - مطابق للنموذج الأصلي -->
        <div style="margin: 15px 0;">
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
            <div style="text-align: center; width: 45%;">
              <div style="font-weight: bold; margin-bottom: 30px;">توقيع الموظف</div>
              <div style="border-bottom: 1px solid #000; width: 150px; margin: 0 auto;"></div>
            </div>
            <div style="text-align: center; width: 45%;">
              <div style="font-weight: bold; margin-bottom: 30px;">اعتماد الرئيس المباشر</div>
              <div style="border-bottom: 1px solid #000; width: 150px; margin: 0 auto;"></div>
            </div>
          </div>
          <div style="display: flex; justify-content: space-between; margin-bottom: 15px;">
            <div style="text-align: center; width: 45%;">
              <div style="font-weight: bold; margin-bottom: 30px;">المدير الإداري</div>
              <div style="border-bottom: 1px solid #000; width: 150px; margin: 0 auto;"></div>
            </div>
            <div style="text-align: center; width: 45%;">
              <div style="font-weight: bold; margin-bottom: 30px;">اعتماد مدير المشروع</div>
              <div style="border-bottom: 1px solid #000; width: 150px; margin: 0 auto;"></div>
            </div>
          </div>
        </div>

        <!-- HR Section - مطابق للنموذج الأصلي -->
        <div class="hr-section">
          <div class="hr-header">إدارة الموارد البشرية</div>
          <div class="hr-content">
            <div style="display: flex; justify-content: flex-end; align-items: flex-start; margin-bottom: 10px;">
              <div style="flex: 1;">
                <div style="line-height: 1.8;">
                  ................................................................................................................................................................................................<br>
                  ................................................................................................................................................................................................
                </div>
              </div>
              <div style="font-weight: bold; text-align: right; margin-left: 10px;">
                ملاحظات:
              </div>
            </div>

            <div style="display: flex; justify-content: flex-start; align-items: center; margin: 10px 0;">
              <div style="text-align: left;">
                <div style="font-weight: bold; margin-bottom: 8px;">أخصائي موارد بشرية</div>
                <div style="border-bottom: 1px solid #000; width: 150px; height: 15px;"></div>
              </div>
            </div>

            <div style="font-size: 10px; text-align: right;">
              <div style="margin-bottom: 3px;">
                في حالة الإجازة المرضية يتم إرفاق التقرير الطبي.
              </div>
              <div>
                في حالة عدم وجود رصيد إجازات سنوية يتم احتساب الطلب إجازة بدون أجر.
              </div>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
  };

  // تحميل الطلبات عند تغيير الفلاتر أو الصفحة
  useEffect(() => {
    fetchRequests();
  }, [filters, pagination.page]);

  // تحميل الطلبات عند تحميل الصفحة
  useEffect(() => {
    fetchRequests();
  }, []);

  // استخدام دالة تنسيق التاريخ الموحدة
  const formatDate = (dateString) => {
    if (!dateString) return '-';

    // إذا كان التاريخ بصيغة DD/MM/YYYY بالفعل، أرجعه كما هو
    if (typeof dateString === 'string' && /^\d{2}\/\d{2}\/\d{4}$/.test(dateString)) {
      return dateString;
    }

    // وإلا قم بتنسيقه
    return formatDateToDDMMYYYY(dateString) || '-';
  };

  // دالة لحساب عدد الأيام بين تاريخين
  const calculateDays = (startDate, endDate) => {
    if (!startDate || !endDate) return '';

    try {
      const start = new Date(startDate);
      const end = new Date(endDate);
      const diffTime = Math.abs(end - start);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 لتضمين اليوم الأول
      return diffDays;
    } catch (error) {
      return '';
    }
  };

  // دالة لتنسيق حالة الطلب
  const getStatusBadge = (status) => {
    const statusConfig = {
      'قيد المراجعة': {
        color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
        label: isArabic ? 'قيد المراجعة' : 'Under Review'
      },
      'معتمدة': {
        color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        label: isArabic ? 'معتمدة' : 'Approved'
      },
      'معتمد': {
        color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        label: isArabic ? 'معتمدة' : 'Approved'
      },
      'مرفوضة': {
        color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
        label: isArabic ? 'مرفوضة' : 'Rejected'
      },
      // للتوافق مع الحالات القديمة
      pending: {
        color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
        label: isArabic ? 'قيد المراجعة' : 'Pending'
      },
      approved: {
        color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
        label: isArabic ? 'معتمدة' : 'Approved'
      },
      rejected: {
        color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
        label: isArabic ? 'مرفوضة' : 'Rejected'
      }
    };

    const config = statusConfig[status] || statusConfig['قيد المراجعة'];
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        {config.label}
      </span>
    );
  };

  // دالة لتنسيق نوع الطلب
  const getRequestTypeLabel = (type) => {
    const types = {
      leave: isArabic ? 'طلب إجازة' : 'Leave Request',
      mission: isArabic ? 'طلب مأمورية' : 'Mission Request',
      permission: isArabic ? 'طلب إذن' : 'Permission Request',
      night_shift: isArabic ? 'إخطار وردية ليلية' : 'Night Shift Notification'
    };
    return types[type] || type;
  };

  // دالة لتنسيق نوع الإجازة
  const getLeaveTypeLabel = (type) => {
    const types = {
      // الأنواع العربية
      'اعتيادية': isArabic ? 'إجازة اعتيادية' : 'Regular Leave',
      'إعتيادية': isArabic ? 'إجازة اعتيادية' : 'Regular Leave',
      'مرضية': isArabic ? 'إجازة مرضية' : 'Sick Leave',
      'عارضة': isArabic ? 'إجازة عارضة' : 'Emergency Leave',
      'بدل': isArabic ? 'إجازة بدل' : 'Compensation Leave',
      'بدون أجر': isArabic ? 'إجازة بدون أجر' : 'Unpaid Leave',

      // الأنواع الإنجليزية (تحويل للعربية)
      'annual': isArabic ? 'إجازة اعتيادية' : 'Regular Leave',
      'regular': isArabic ? 'إجازة اعتيادية' : 'Regular Leave',
      'sick': isArabic ? 'إجازة مرضية' : 'Sick Leave',
      'emergency': isArabic ? 'إجازة عارضة' : 'Emergency Leave',
      'casual': isArabic ? 'إجازة عارضة' : 'Emergency Leave',
      'compensatory': isArabic ? 'إجازة بدل' : 'Compensation Leave',
      'unpaid': isArabic ? 'إجازة بدون أجر' : 'Unpaid Leave'
    };
    return types[type] || (isArabic ? type : type);
  };

  // دالة لتنسيق تفاصيل الطلب
  const getRequestDetails = (request) => {
    switch (request.RequestType) {
      case 'leave':
        return `${getLeaveTypeLabel(request.LeaveType)} - ${request.DaysCount} ${isArabic ? 'يوم' : 'days'}`;
      case 'mission':
        return `${request.MissionDestination} - ${request.MissionDuration}`;
      case 'permission':
        return `${request.PermissionStartTime} - ${request.PermissionEndTime}`;
      case 'night_shift':
        return formatDate(request.NightShiftDate);
      default:
        return '-';
    }
  };

  return (
    <MainLayout>
      <div className="p-6 max-w-7xl mx-auto">
        {/* العنوان */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FiCalendar className="text-3xl text-blue-600" />
              <div>
                <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                  {isArabic ? 'الطلبات المقدمة' : 'Submitted Requests'}
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  {isArabic ? 'عرض وإدارة جميع الطلبات الورقية' : 'View and manage all paper requests'}
                </p>
              </div>
            </div>

            <button
              onClick={fetchRequests}
              disabled={loading}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              <FiRefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              {isArabic ? 'تحديث' : 'Refresh'}
            </button>
          </div>
        </div>

        {/* الفلاتر */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 mb-6 shadow-sm gentle-animated-box">
          <div className="flex items-center gap-2 mb-4">
            <FiFilter className="text-gray-600" />
            <h2 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
              {isArabic ? 'البحث والفلترة' : 'Search & Filter'}
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-7 gap-4">
            {/* حالة الطلب */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {isArabic ? 'حالة الطلب' : 'Request Status'}
              </label>
              <select
                value={filters.status}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
              >
                {statusOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* نوع الطلب */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {isArabic ? 'نوع الطلب' : 'Request Type'}
              </label>
              <select
                value={filters.requestType}
                onChange={(e) => handleFilterChange('requestType', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
              >
                {requestTypeOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* نوع الإجازة (يظهر فقط عند اختيار طلب إجازة) */}
            {filters.requestType === 'leave' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {isArabic ? 'نوع الإجازة' : 'Leave Type'}
                </label>
                <select
                  value={filters.leaveType}
                  onChange={(e) => handleFilterChange('leaveType', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
                >
                  {leaveTypeOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {/* من تاريخ */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {isArabic ? 'من تاريخ' : 'From Date'}
              </label>
              <input
                type="text"
                value={filters.startDate}
                onChange={(e) => handleFilterChange('startDate', e.target.value)}
                placeholder="dd/mm/yyyy"
                pattern="\d{2}/\d{2}/\d{4}"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
              />
            </div>

            {/* إلى تاريخ */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {isArabic ? 'إلى تاريخ' : 'To Date'}
              </label>
              <input
                type="text"
                value={filters.endDate}
                onChange={(e) => handleFilterChange('endDate', e.target.value)}
                placeholder="dd/mm/yyyy"
                pattern="\d{2}/\d{2}/\d{4}"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
              />
            </div>

            {/* كود الموظف */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {isArabic ? 'كود الموظف' : 'Employee Code'}
              </label>
              <input
                type="text"
                value={filters.employeeCode}
                onChange={(e) => handleFilterChange('employeeCode', e.target.value)}
                placeholder={isArabic ? 'أدخل كود الموظف...' : 'Enter employee code...'}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
              />
            </div>

            {/* اسم الموظف */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {isArabic ? 'اسم الموظف' : 'Employee Name'}
              </label>
              <input
                type="text"
                value={filters.employeeName}
                onChange={(e) => handleFilterChange('employeeName', e.target.value)}
                placeholder={isArabic ? 'أدخل اسم الموظف...' : 'Enter employee name...'}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-gray-200"
              />
            </div>
          </div>

          <div className="flex justify-between items-center mt-4">
            <div className="flex gap-2">
              <button
                onClick={() => {
                  setFilters({ status: 'all', requestType: 'all', leaveType: 'all', startDate: '', endDate: '', employeeCode: '', employeeName: '' });
                  setPagination(prev => ({ ...prev, page: 1 }));
                }}
                className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
              >
                {isArabic ? 'مسح الفلاتر' : 'Clear Filters'}
              </button>

              <button
                onClick={cleanupDuplicates}
                className="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors flex items-center gap-2"
                title={isArabic ? 'تنظيف الطلبات المكررة' : 'Cleanup Duplicates'}
              >
                <FiTrash2 />
                {isArabic ? 'تنظيف المكررات' : 'Cleanup Duplicates'}
              </button>

              <a
                href="/fix-missions"
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2"
                title="إصلاح المأموريات"
              >
                <FiSettings className="w-4 h-4" />
                إصلاح المأموريات
              </a>
            </div>

            <div className="text-sm text-gray-600 dark:text-gray-400">
              {isArabic ? `إجمالي النتائج: ${pagination.total}` : `Total Results: ${pagination.total}`}
            </div>
          </div>
        </div>

        {/* جدول الطلبات */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <FiRefreshCw className="w-8 h-8 animate-spin text-blue-600" />
              <span className="ml-2 text-gray-600 dark:text-gray-400">
                {isArabic ? 'جاري التحميل...' : 'Loading...'}
              </span>
            </div>
          ) : requests.length === 0 ? (
            <div className="text-center py-12">
              <FiCalendar className="mx-auto text-6xl text-gray-400 mb-4" />
              <h3 className="text-xl font-medium text-gray-600 dark:text-gray-400 mb-2">
                {isArabic ? 'لا توجد طلبات' : 'No Requests Found'}
              </h3>
              <p className="text-gray-500 dark:text-gray-500">
                {isArabic ? 'لم يتم العثور على طلبات تطابق معايير البحث' : 'No requests match your search criteria'}
              </p>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 dark:bg-gray-700">
                    <tr>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'نوع الطلب' : 'Request Type'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'اسم الموظف' : 'Employee Name'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'من تاريخ' : 'From Date'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'إلى تاريخ' : 'To Date'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'التفاصيل' : 'Details'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'ملاحظات' : 'Notes'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'الحالة' : 'Status'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'تاريخ التقديم' : 'Request Date'}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        {isArabic ? 'الإجراءات' : 'Actions'}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                    {requests.map((request) => (
                      <tr key={request.ID} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{getRequestTypeLabel(request.RequestType)}</span>
                            {request.RequestType === 'leave' && (
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                ({getLeaveTypeLabel(request.LeaveType)})
                              </span>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          <div>
                            <div className="font-medium">{request.EmployeeName}</div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">{request.EmployeeCode}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {request.StartDate ? formatDate(request.StartDate) : '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {request.EndDate ? formatDate(request.EndDate) : '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {getRequestDetails(request)}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                          <div className="max-w-xs truncate" title={request.Notes || '-'}>
                            {request.Notes || '-'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {getStatusBadge(request.Status)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {formatDate(request.RequestDate)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center gap-2">
                            {/* طباعة الطلب - متاح لجميع الطلبات */}
                            <button
                              onClick={() => printRequest(request.ID)}
                              className="text-purple-600 hover:text-purple-900 dark:text-purple-400 dark:hover:text-purple-300"
                              title={isArabic ? 'طباعة' : 'Print'}
                            >
                              <FiPrinter className="w-4 h-4" />
                            </button>

                            {/* أزرار مختلفة حسب نوع الطلب */}
                            {request.RequestType === 'leave' ? (
                              // طلبات الإجازة: اعتماد، رفض، رجوع عن الاعتماد، حذف
                              <>
                                {request.Status === 'قيد المراجعة' && (
                                  <>
                                    <button
                                      onClick={() => updateRequestStatus(request.ID, 'معتمدة')}
                                      className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                                      title={isArabic ? 'اعتماد' : 'Approve'}
                                    >
                                      <FiCheck className="w-4 h-4" />
                                    </button>
                                    <button
                                      onClick={() => updateRequestStatus(request.ID, 'مرفوضة')}
                                      className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                      title={isArabic ? 'رفض' : 'Reject'}
                                    >
                                      <FiX className="w-4 h-4" />
                                    </button>
                                  </>
                                )}

                                {/* زر الرجوع عن الاعتماد للطلبات المعتمدة */}
                                {request.Status === 'معتمدة' && (
                                  <button
                                    onClick={() => updateRequestStatus(request.ID, 'قيد المراجعة')}
                                    className="text-orange-600 hover:text-orange-900 dark:text-orange-400 dark:hover:text-orange-300"
                                    title={isArabic ? 'الرجوع عن الاعتماد' : 'Revert Approval'}
                                  >
                                    <FiRotateCcw className="w-4 h-4" />
                                  </button>
                                )}

                                {/* زر الحذف متاح فقط للطلبات غير المعتمدة */}
                                {request.Status !== 'معتمدة' && (
                                  <button
                                    onClick={() => deleteRequest(request.ID)}
                                    className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                    title={isArabic ? 'حذف' : 'Delete'}
                                  >
                                    <FiTrash2 className="w-4 h-4" />
                                  </button>
                                )}
                              </>
                            ) : (
                              // الطلبات الأخرى (مأمورية، إذن، وردية ليلية): اعتماد، رفض، تعديل، حذف
                              <>
                                {request.Status === 'قيد المراجعة' && (
                                  <>
                                    <button
                                      onClick={() => updateRequestStatus(request.ID, 'معتمدة')}
                                      className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                                      title={isArabic ? 'اعتماد' : 'Approve'}
                                    >
                                      <FiCheck className="w-4 h-4" />
                                    </button>
                                    <button
                                      onClick={() => updateRequestStatus(request.ID, 'مرفوضة')}
                                      className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                      title={isArabic ? 'رفض' : 'Reject'}
                                    >
                                      <FiX className="w-4 h-4" />
                                    </button>
                                  </>
                                )}

                                {/* زر الرجوع عن الاعتماد للطلبات المعتمدة */}
                                {request.Status === 'معتمدة' && (
                                  <button
                                    onClick={() => updateRequestStatus(request.ID, 'قيد المراجعة')}
                                    className="text-orange-600 hover:text-orange-900 dark:text-orange-400 dark:hover:text-orange-300"
                                    title={isArabic ? 'الرجوع عن الاعتماد' : 'Revert Approval'}
                                  >
                                    <FiRotateCcw className="w-4 h-4" />
                                  </button>
                                )}

                                <button
                                  onClick={() => editRequest(request)}
                                  className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                                  title={isArabic ? 'تعديل' : 'Edit'}
                                >
                                  <FiEdit className="w-4 h-4" />
                                </button>

                                {/* زر الحذف متاح فقط للطلبات غير المعتمدة */}
                                {request.Status !== 'معتمدة' && (
                                  <button
                                    onClick={() => deleteRequest(request.ID)}
                                    className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                    title={isArabic ? 'حذف' : 'Delete'}
                                  >
                                    <FiTrash2 className="w-4 h-4" />
                                  </button>
                                )}
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* الترقيم */}
              {pagination.totalPages > 1 && (
                <div className="bg-white dark:bg-gray-800 px-4 py-3 border-t border-gray-200 dark:border-gray-600 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1 flex justify-between sm:hidden">
                      <button
                        onClick={() => handlePageChange(pagination.page - 1)}
                        disabled={pagination.page <= 1}
                        className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                      >
                        {isArabic ? 'السابق' : 'Previous'}
                      </button>
                      <button
                        onClick={() => handlePageChange(pagination.page + 1)}
                        disabled={pagination.page >= pagination.totalPages}
                        className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                      >
                        {isArabic ? 'التالي' : 'Next'}
                      </button>
                    </div>
                    <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                      <div>
                        <p className="text-sm text-gray-700 dark:text-gray-300">
                          {isArabic ? 'عرض' : 'Showing'}{' '}
                          <span className="font-medium">{((pagination.page - 1) * pagination.limit) + 1}</span>
                          {' '}{isArabic ? 'إلى' : 'to'}{' '}
                          <span className="font-medium">
                            {Math.min(pagination.page * pagination.limit, pagination.total)}
                          </span>
                          {' '}{isArabic ? 'من' : 'of'}{' '}
                          <span className="font-medium">{pagination.total}</span>
                          {' '}{isArabic ? 'نتيجة' : 'results'}
                        </p>
                      </div>
                      <div>
                        <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                          <button
                            onClick={() => handlePageChange(pagination.page - 1)}
                            disabled={pagination.page <= 1}
                            className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                          >
                            <FiChevronLeft className="h-5 w-5" />
                          </button>

                          {[...Array(pagination.totalPages)].map((_, index) => {
                            const pageNumber = index + 1;
                            return (
                              <button
                                key={pageNumber}
                                onClick={() => handlePageChange(pageNumber)}
                                className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                  pageNumber === pagination.page
                                    ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                                }`}
                              >
                                {pageNumber}
                              </button>
                            );
                          })}

                          <button
                            onClick={() => handlePageChange(pagination.page + 1)}
                            disabled={pagination.page >= pagination.totalPages}
                            className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                          >
                            <FiChevronRight className="h-5 w-5" />
                          </button>
                        </nav>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </MainLayout>
  );

  // دوال إنشاء النماذج للأنواع المختلفة
  function generateMissionFormContent(requestData, logoBase64 = '') {
    return `
      <!DOCTYPE html>
      <html dir="rtl">
      <head>
        <meta charset="UTF-8">
        <title>نموذج طلب مأمورية - ${requestData.EmployeeName}</title>
        <style>
          @page {
            size: A4;
            margin: 15mm;
          }
          body {
            font-family: 'Arial', sans-serif;
            font-size: 14px;
            line-height: 1.4;
            color: #000;
            margin: 0;
            padding: 0;
            direction: rtl;
            background: white;
          }
          .header {
            display: table;
            width: 100%;
            border: 2px solid #000;
            margin-bottom: 15px;
            border-collapse: collapse;
          }
          .header-row {
            display: table-row;
          }
          .logo-section {
            display: table-cell;
            width: 33.33%;
            border-left: 1px solid #000;
            text-align: center;
            vertical-align: middle;
            padding: 10px;
          }
          .logo-img {
            max-width: 100px;
            max-height: 70px;
            object-fit: contain;
          }
          .form-code {
            display: table-cell;
            width: 33.33%;
            border-left: 1px solid #000;
            text-align: center;
            vertical-align: middle;
            padding: 10px;
          }
          .form-code-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
          }
          .form-code-number {
            font-size: 12px;
            color: #666;
          }
          .company-info {
            display: table-cell;
            width: 33.33%;
            text-align: center;
            vertical-align: middle;
            padding: 10px;
          }
          .company-name-ar {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 3px;
          }
          .company-name-en {
            font-size: 10px;
            color: #666;
            font-style: italic;
          }
          .section-title {
            background-color: #f0f0f0;
            text-align: center;
            padding: 8px;
            font-size: 14px;
            font-weight: bold;
            border: 1px solid #000;
            margin: 15px 0 10px 0;
          }
          .form-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            font-size: 12px;
          }
          .field-group {
            display: flex;
            align-items: center;
          }
          .field-label {
            font-weight: bold;
            margin-left: 8px;
            font-size: 12px;
          }
          .field-value {
            border-bottom: 1px solid #000;
            padding: 3px 8px;
            min-width: 120px;
            text-align: center;
            font-size: 12px;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="header-row">
            <div class="company-info">
              <div class="company-name-ar">شركة كونكورد للهندسة والمقاولات</div>
              <div class="company-name-en">Concord for Engineering & Contracting</div>
            </div>
            <div class="form-code">
              <div class="form-code-title">طلب مأمورية</div>
              <div class="form-code-number">HR-OP-01-F02</div>
            </div>
            <div class="logo-section">
              ${logoBase64 ?
                `<img src="data:image/png;base64,${logoBase64}" alt="Concord Logo" class="logo-img" />` :
                `<div style="background:#1e40af;color:white;padding:10px;text-align:center;font-weight:bold;font-size:10px;">CONCORD<br>COMPANY</div>`
              }
            </div>
          </div>
        </div>

        <!-- Employee Info Section - مطابق للنموذج الأصلي تماماً -->

        <!-- السطر الأول: التاريخ (يسار) فقط -->
        <div style="margin-bottom: 15px; padding: 8px 0;">
          <div style="text-align: left;">
            <span style="font-weight: bold;">التاريخ/</span>
            <span style="margin-left: 20px;">${formatDate(requestData.RequestDate)}</span>
          </div>
        </div>

        <!-- السطر الثاني: الاسم (يمين) والكود الوظيفي (وسط) -->
        <div style="margin-bottom: 15px; padding: 8px 0;">
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div style="text-align: right;">
              <span style="font-weight: bold; font-size: 15px;">الإســـم/</span>
              <span style="margin-right: 20px; font-size: 15px;">${requestData.EmployeeName || ''}</span>
            </div>
            <div style="text-align: center; flex: 1;">
              <span style="font-weight: bold; font-size: 15px;">الكود الوظيفى /</span>
              <span style="margin-left: 20px; font-size: 15px;">${requestData.EmployeeCode || ''}</span>
            </div>
            <div style="width: 200px;"></div>
          </div>
        </div>

        <!-- السطر الثالث: الوظيفة (يمين) فقط -->
        <div style="margin-bottom: 15px; padding: 8px 0;">
          <div style="text-align: right;">
            <span style="font-weight: bold; font-size: 15px;">الوظيفة/</span>
            <span style="margin-right: 20px; font-size: 15px;">${requestData.JobTitle || ''}</span>
          </div>
        </div>

        <!-- السطر الرابع: الإدارة/المشروع (يمين) فقط -->
        <div style="margin-bottom: 20px; padding: 8px 0;">
          <div style="text-align: right;">
            <span style="font-weight: bold; font-size: 15px;">الإدارة/المشروع/</span>
            <span style="margin-right: 20px; font-size: 15px;">مشروع مجمع مبانى أوجيستا</span>
          </div>
        </div>

        <!-- Mission Details Section -->
        <div style="border: 2px solid #000; margin: 20px 0; padding: 15px;">
          <div style="font-weight: bold; text-align: center; margin-bottom: 15px; font-size: 14px;">
            تفاصيل المأمورية
          </div>

          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <!-- عدد الأيام - أقصى اليسار في الكود = أقصى اليمين في العرض -->
            <div style="display: flex; align-items: center; gap: 10px;">
              <span style="font-weight: bold; font-size: 15px;">عدد الأيام:</span>
              <span style="border-bottom: 2px solid #000; padding: 5px 10px; min-width: 80px; text-align: center; font-size: 15px;">
                ${requestData.DaysCount || calculateDays(requestData.StartDate, requestData.EndDate) || '____'} أيام
              </span>
            </div>
            <!-- من تاريخ - الوسط -->
            <div style="display: flex; align-items: center; gap: 10px;">
              <span style="font-weight: bold; font-size: 15px;">من تاريخ:</span>
              <span style="border-bottom: 2px solid #000; padding: 5px 10px; min-width: 100px; text-align: center; font-size: 15px;">
                ${formatDate(requestData.StartDate)}
              </span>
            </div>
            <!-- إلى تاريخ - أقصى اليمين في الكود = أقصى اليسار في العرض -->
            <div style="display: flex; align-items: center; gap: 10px;">
              <span style="font-weight: bold; font-size: 15px;">إلى تاريخ:</span>
              <span style="border-bottom: 2px solid #000; padding: 5px 10px; min-width: 100px; text-align: center; font-size: 15px;">
                ${formatDate(requestData.EndDate)}
              </span>
            </div>
          </div>

          <div style="margin-bottom: 15px;">
            <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
              <span style="font-weight: bold;">مكان المأمورية:</span>
              <span style="border-bottom: 2px solid #000; padding: 5px 10px; flex: 1; text-align: center;">
                ${requestData.MissionDestination || ''}
              </span>
            </div>
          </div>

          <div style="margin-bottom: 15px;">
            <div style="font-weight: bold; margin-bottom: 10px;">الغرض من المأمورية:</div>
            <div style="border: 2px solid #000; min-height: 80px; padding: 15px; line-height: 1.6;">
              ${requestData.MissionPurpose || ''}
            </div>
          </div>

          <div style="margin-top: 15px;">
            <div style="display: flex; justify-content: flex-start; align-items: center; gap: 15px; flex-wrap: wrap;">
              <div style="font-weight: bold; margin-left: 10px;">
                وسيلة الانتقال:
              </div>
              <div style="display: flex; align-items: center; gap: 8px;">
                <span style="border: 2px solid #000; width: 15px; height: 15px; display: inline-block; text-align: center; line-height: 11px; font-size: 10px; ${requestData.TransportMethod === 'company-car' ? 'background: #000; color: white;' : ''}">
                  ${requestData.TransportMethod === 'company-car' ? '✓' : ''}
                </span>
                <span style="font-size: 12px;">سيارة الشركة</span>
              </div>
              <div style="display: flex; align-items: center; gap: 8px;">
                <span style="border: 2px solid #000; width: 15px; height: 15px; display: inline-block; text-align: center; line-height: 11px; font-size: 10px; ${requestData.TransportMethod === 'employee-transport' ? 'background: #000; color: white;' : ''}">
                  ${requestData.TransportMethod === 'employee-transport' ? '✓' : ''}
                </span>
                <span style="font-size: 12px;">انتقالات بمعرفة الموظف</span>
              </div>
            </div>
          </div>
        </div>

        <div style="margin-top: 40px;">
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="text-align: center; padding: 20px; width: 50%; vertical-align: top;">
                <div style="font-weight: bold; margin-bottom: 50px;">توقيع الموظف</div>
                <div style="border-bottom: 1px solid #000; width: 200px; margin: 0 auto;"></div>
              </td>
              <td style="text-align: center; padding: 20px; width: 50%; vertical-align: top;">
                <div style="font-weight: bold; margin-bottom: 50px;">اعتماد المدير المباشر</div>
                <div style="border-bottom: 1px solid #000; width: 200px; margin: 0 auto;"></div>
              </td>
            </tr>
            <tr>
              <td style="text-align: center; padding: 20px; width: 50%; vertical-align: top;">
                <div style="font-weight: bold; margin-bottom: 50px;">اعتماد مدير المشروع</div>
                <div style="border-bottom: 1px solid #000; width: 200px; margin: 0 auto;"></div>
              </td>
              <td style="text-align: center; padding: 20px; width: 50%; vertical-align: top;">
                <div style="font-weight: bold; margin-bottom: 50px;">اعتماد جهة المأمورية</div>
                <div style="border-bottom: 1px solid #000; width: 200px; margin: 0 auto;"></div>
              </td>
            </tr>
          </table>
        </div>
      </body>
      </html>
    `;
  }

  function generatePermissionFormContent(requestData) {
    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>طلب إذن</title>
        <style>
          @page { size: A4 portrait; margin: 10mm; }
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.3;
            color: black !important;
            background: white !important;
            direction: rtl;
            width: 190mm;
            max-width: 190mm;
            margin: 0 auto;
            padding: 5mm;
          }
          .header-table {
            width: 100%;
            border-collapse: collapse;
            border: 2px solid #000;
            margin-bottom: 8px;
          }
          .header-table td {
            border: 1px solid #000;
            padding: 6px;
            text-align: center;
            vertical-align: middle;
            height: 50px;
            font-size: 10px;
          }
          .section-header {
            text-align: center;
            font-weight: bold;
            margin: 8px 0 5px 0;
            padding: 6px;
            border: 1px solid #000;
            background-color: #f0f0f0;
            font-size: 11px;
          }
          .form-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-size: 10px;
          }
          .field-label {
            font-weight: bold;
            margin-left: 8px;
            font-size: 10px;
          }
          .field-value {
            border-bottom: 1px solid #000;
            padding: 2px 6px;
            text-align: center;
            font-size: 10px;
          }
        </style>
      </head>
      <body>
        <table class="header-table">
          <tr>
            <td class="logo-cell">
              <div style="background:#1e40af;color:white;padding:8px;text-align:center;font-weight:bold;font-size:10px;border-radius:4px;">CONCORD<br>COMPANY</div>
            </td>
            <td class="form-title-cell">
              <div style="font-size: 14px; font-weight: bold;">طلب إذن</div>
              <div style="font-size: 10px; color: #666;">HR-OP-03-F01</div>
            </td>
            <td class="company-cell">
              <div style="font-weight: bold; font-size: 10px;">شركة كونكورد للهندسة والمقاولات</div>
              <div style="font-size: 8px; color: #666;">Concord for Engineering & Contracting</div>
            </td>
          </tr>
        </table>

        <div class="section-header">بيانات طلب الإذن</div>

        <!-- Employee Information - مطابق للنموذج الأصلي -->
        <div class="form-row" style="justify-content: flex-end; margin-right: 0;">
          <span class="field-label" style="margin-right: 0;">الاسم:</span>
          <div class="field-value" style="width: 300px;">${requestData.EmployeeName || ''}</div>
          <span style="margin: 0 20px;"></span>
          <span class="field-label" style="margin-right: 0;">الكود الوظيفي:</span>
          <div class="field-value" style="width: 100px;">${requestData.EmployeeCode || ''}</div>
        </div>

        <div class="form-row">
          <div>
            <span class="field-label">نوع الإذن:</span>
            <span class="field-value" style="width: 150px;">${requestData.PermissionType || ''}</span>
          </div>
          <div>
            <span class="field-label">المدة:</span>
            <span class="field-value" style="width: 100px;">${requestData.Duration || ''}</span>
          </div>
        </div>

        <div class="form-row">
          <div>
            <span class="field-label">من:</span>
            <span class="field-value" style="width: 120px;">${requestData.StartTime || ''}</span>
          </div>
          <div>
            <span class="field-label">إلى:</span>
            <span class="field-value" style="width: 120px;">${requestData.EndTime || ''}</span>
          </div>
        </div>

        <div style="margin-top: 30px; text-align: center; font-size: 12px;">
          <p>طلب إذن مطبوع من النظام الإلكتروني</p>
          <p style="font-size: 10px; color: #666; margin-top: 5px;">تاريخ الطباعة: ${formatDate(new Date())}</p>
        </div>
      </body>
      </html>
    `;
  }

  function generateNightShiftFormContent(requestData) {
    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>إخطار وردية ليلية</title>
        <style>
          @page { size: A4 portrait; margin: 10mm; }
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.3;
            color: black !important;
            background: white !important;
            direction: rtl;
            width: 190mm;
            max-width: 190mm;
            margin: 0 auto;
            padding: 5mm;
          }
          .header-table {
            width: 100%;
            border-collapse: collapse;
            border: 2px solid #000;
            margin-bottom: 8px;
          }
          .header-table td {
            border: 1px solid #000;
            padding: 6px;
            text-align: center;
            vertical-align: middle;
            height: 50px;
            font-size: 10px;
          }
          .section-header {
            text-align: center;
            font-weight: bold;
            margin: 8px 0 5px 0;
            padding: 6px;
            border: 1px solid #000;
            background-color: #f0f0f0;
            font-size: 11px;
          }
          .form-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-size: 10px;
          }
          .field-label {
            font-weight: bold;
            margin-left: 8px;
            font-size: 10px;
          }
          .field-value {
            border-bottom: 1px solid #000;
            padding: 2px 6px;
            text-align: center;
            font-size: 10px;
          }
        </style>
      </head>
      <body>
        <table class="header-table">
          <tr>
            <td class="logo-cell">
              <div style="background:#1e40af;color:white;padding:8px;text-align:center;font-weight:bold;font-size:10px;border-radius:4px;">CONCORD<br>COMPANY</div>
            </td>
            <td class="form-title-cell">
              <div style="font-size: 14px; font-weight: bold;">إخطار وردية ليلية</div>
              <div style="font-size: 10px; color: #666;">HR-OP-04-F01</div>
            </td>
            <td class="company-cell">
              <div style="font-weight: bold; font-size: 10px;">شركة كونكورد للهندسة والمقاولات</div>
              <div style="font-size: 8px; color: #666;">Concord for Engineering & Contracting</div>
            </td>
          </tr>
        </table>

        <div class="section-header">بيانات إخطار الوردية الليلية</div>

        <!-- Employee Information - مطابق للنموذج الأصلي -->
        <div class="form-row" style="justify-content: flex-end; margin-right: 0;">
          <span class="field-label" style="margin-right: 0;">الاسم:</span>
          <div class="field-value" style="width: 300px;">${requestData.EmployeeName || ''}</div>
          <span style="margin: 0 20px;"></span>
          <span class="field-label" style="margin-right: 0;">الكود الوظيفي:</span>
          <div class="field-value" style="width: 100px;">${requestData.EmployeeCode || ''}</div>
        </div>

        <div class="form-row">
          <div>
            <span class="field-label">تاريخ الوردية:</span>
            <span class="field-value" style="width: 150px;">${formatDate(requestData.ShiftDate)}</span>
          </div>
          <div>
            <span class="field-label">نوع الوردية:</span>
            <span class="field-value" style="width: 100px;">${requestData.ShiftType || ''}</span>
          </div>
        </div>

        <div class="form-row">
          <div>
            <span class="field-label">من:</span>
            <span class="field-value" style="width: 120px;">${requestData.StartTime || ''}</span>
          </div>
          <div>
            <span class="field-label">إلى:</span>
            <span class="field-value" style="width: 120px;">${requestData.EndTime || ''}</span>
          </div>
        </div>

        <div style="margin-top: 30px; text-align: center; font-size: 12px;">
          <p>إخطار وردية ليلية مطبوع من النظام الإلكتروني</p>
          <p style="font-size: 10px; color: #666; margin-top: 5px;">تاريخ الطباعة: ${formatDate(new Date())}</p>
        </div>
      </body>
      </html>
    `;
  }

  function generateGenericFormContent(requestData) {
    return `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <title>طلب ${getRequestTypeLabel(requestData.RequestType)}</title>
        <style>
          @page { size: A4 portrait; margin: 10mm; }
          * { margin: 0; padding: 0; box-sizing: border-box; }
          body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: black !important;
            background: white !important;
            direction: rtl;
            margin: 0 auto;
            padding: 20px;
          }
          .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #000;
            padding-bottom: 20px;
          }
          .form-row {
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
          }
          .field-label {
            font-weight: bold;
            margin-left: 10px;
          }
          .field-value {
            border-bottom: 1px solid #000;
            padding: 5px;
            min-width: 200px;
            text-align: center;
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>شركة كونكورد للهندسة والمقاولات</h1>
          <h2>طلب ${getRequestTypeLabel(requestData.RequestType)}</h2>
        </div>

        <div class="form-row">
          <span class="field-label">اسم الموظف:</span>
          <span class="field-value">${requestData.EmployeeName || ''}</span>
        </div>

        <div class="form-row">
          <span class="field-label">كود الموظف:</span>
          <span class="field-value">${requestData.EmployeeCode || ''}</span>
        </div>

        <div class="form-row">
          <span class="field-label">تاريخ الطلب:</span>
          <span class="field-value">${formatDate(requestData.RequestDate)}</span>
        </div>

        <div class="form-row">
          <span class="field-label">الحالة:</span>
          <span class="field-value">${requestData.Status || ''}</span>
        </div>

        <div style="margin-top: 50px; text-align: center;">
          <p>طلب مطبوع من النظام الإلكتروني</p>
          <p style="font-size: 10px; color: #666; margin-top: 10px;">تاريخ الطباعة: ${formatDate(new Date())}</p>
        </div>
      </body>
      </html>
    `;
  }
}
