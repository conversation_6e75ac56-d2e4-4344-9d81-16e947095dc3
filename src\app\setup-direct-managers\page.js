'use client';

import { useState, useEffect } from 'react';
import { FiDatabase, FiCheck, FiX, FiSettings, FiRefreshCw, FiInfo } from 'react-icons/fi';
import MainLayout from '@/components/MainLayout';

export default function SetupDirectManagersPage() {
  const [systemStatus, setSystemStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [setupLoading, setSetupLoading] = useState(false);
  const [error, setError] = useState('');
  const [setupResult, setSetupResult] = useState(null);

  // التحقق من حالة النظام
  const checkSystemStatus = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/setup-direct-managers-system');
      const data = await response.json();

      if (data.success) {
        setSystemStatus(data.systemStatus);
      } else {
        setError(data.error || 'فشل في التحقق من حالة النظام');
      }
    } catch (error) {

      setError('حدث خطأ في الاتصال بالخادم');
    } finally {
      setLoading(false);
    }
  };

  // إعداد النظام
  const setupSystem = async () => {
    try {
      setSetupLoading(true);
      setError('');

      const response = await fetch('/api/setup-direct-managers-system', {
        method: 'POST'
      });

      const data = await response.json();

      if (data.success) {
        setSetupResult(data);
        alert('تم إعداد النظام بنجاح!');
        await checkSystemStatus(); // إعادة فحص الحالة
      } else {
        setError(data.error || 'فشل في إعداد النظام');
        alert(`خطأ في إعداد النظام: ${data.error}`);
      }
    } catch (error) {

      setError('حدث خطأ في إعداد النظام');
      alert('حدث خطأ في إعداد النظام');
    } finally {
      setSetupLoading(false);
    }
  };

  useEffect(() => {
    checkSystemStatus();
  }, []);

  return (
    <MainLayout>
      <div className="max-w-6xl mx-auto p-6">
        {/* العنوان */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            ⚙️ إعداد نظام إدارة المديرين المباشرين
          </h1>
          <p className="text-gray-600">
            إعداد وتهيئة قاعدة البيانات والجداول المطلوبة لنظام إدارة المديرين المباشرين
          </p>
        </div>

        {/* حالة النظام */}
        <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900">حالة النظام</h2>
            <button
              onClick={checkSystemStatus}
              disabled={loading}
              className="flex items-center gap-2 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white py-2 px-4 rounded-lg transition-colors"
            >
              <FiRefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              تحديث الحالة
            </button>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
              <span className="mr-3 text-gray-600">جاري فحص حالة النظام...</span>
            </div>
          ) : error ? (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <FiX className="w-5 h-5 text-red-500 mr-2" />
                <span className="text-red-700">{error}</span>
              </div>
            </div>
          ) : systemStatus ? (
            <div className="space-y-6">
              {/* الحالة العامة */}
              <div className={`p-4 rounded-lg border ${
                systemStatus.setupComplete 
                  ? 'bg-green-50 border-green-200' 
                  : 'bg-yellow-50 border-yellow-200'
              }`}>
                <div className="flex items-center">
                  {systemStatus.setupComplete ? (
                    <FiCheck className="w-6 h-6 text-green-500 mr-3" />
                  ) : (
                    <FiInfo className="w-6 h-6 text-yellow-500 mr-3" />
                  )}
                  <div>
                    <h3 className={`font-semibold ${
                      systemStatus.setupComplete ? 'text-green-800' : 'text-yellow-800'
                    }`}>
                      {systemStatus.setupComplete ? 'النظام مُعد بالكامل' : 'النظام يحتاج إلى إعداد'}
                    </h3>
                    <p className={`text-sm ${
                      systemStatus.setupComplete ? 'text-green-600' : 'text-yellow-600'
                    }`}>
                      {systemStatus.setupComplete 
                        ? 'جميع الجداول والإجراءات المخزنة جاهزة للاستخدام'
                        : 'بعض المكونات مفقودة ويجب إعدادها'
                      }
                    </p>
                  </div>
                </div>
              </div>

              {/* تفاصيل الجداول */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">الجداول المطلوبة</h4>
                  <div className="space-y-2">
                    {['DirectManagers', 'DirectManagersHistory', 'DirectManagersFileUploads'].map(tableName => {
                      const tableExists = systemStatus.tables.find(t => t.TABLE_NAME === tableName);
                      return (
                        <div key={tableName} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                          <span className="text-sm text-gray-700">{tableName}</span>
                          {tableExists ? (
                            <div className="flex items-center text-green-600">
                              <FiCheck className="w-4 h-4 mr-1" />
                              <span className="text-xs">موجود ({tableExists.COLUMN_COUNT} عمود)</span>
                            </div>
                          ) : (
                            <div className="flex items-center text-red-600">
                              <FiX className="w-4 h-4 mr-1" />
                              <span className="text-xs">غير موجود</span>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">الإجراءات المخزنة</h4>
                  <div className="space-y-2">
                    {['sp_UpdateDirectManager', 'sp_GetOrganizationalHierarchy'].map(procName => {
                      const procExists = systemStatus.procedures.find(p => p.ROUTINE_NAME === procName);
                      return (
                        <div key={procName} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                          <span className="text-sm text-gray-700">{procName}</span>
                          {procExists ? (
                            <div className="flex items-center text-green-600">
                              <FiCheck className="w-4 h-4 mr-1" />
                              <span className="text-xs">موجود</span>
                            </div>
                          ) : (
                            <div className="flex items-center text-red-600">
                              <FiX className="w-4 h-4 mr-1" />
                              <span className="text-xs">غير موجود</span>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>

              {/* أعمدة جدول الموظفين */}
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">أعمدة المديرين في جدول الموظفين</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {['DirectManager1', 'DirectManager2', 'DirectManager3', 'DirectManager4'].map(columnName => {
                    const columnExists = systemStatus.employeeColumns.find(c => c.COLUMN_NAME === columnName);
                    return (
                      <div key={columnName} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm text-gray-700">{columnName}</span>
                        {columnExists ? (
                          <FiCheck className="w-4 h-4 text-green-600" />
                        ) : (
                          <FiX className="w-4 h-4 text-red-600" />
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* إحصائيات البيانات */}
              {systemStatus.dataStats && Object.keys(systemStatus.dataStats).length > 0 && (
                <div>
                  <h4 className="font-semibold text-gray-900 mb-3">إحصائيات البيانات</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="bg-blue-50 p-3 rounded-lg">
                      <p className="text-sm text-blue-600">السجلات النشطة</p>
                      <p className="text-xl font-bold text-blue-800">{systemStatus.dataStats.TotalActiveRecords || 0}</p>
                    </div>
                    <div className="bg-green-50 p-3 rounded-lg">
                      <p className="text-sm text-green-600">سجلات التاريخ</p>
                      <p className="text-xl font-bold text-green-800">{systemStatus.dataStats.TotalHistoryRecords || 0}</p>
                    </div>
                    <div className="bg-purple-50 p-3 rounded-lg">
                      <p className="text-sm text-purple-600">الملفات المرفوعة</p>
                      <p className="text-xl font-bold text-purple-800">{systemStatus.dataStats.TotalFileUploads || 0}</p>
                    </div>
                    <div className="bg-orange-50 p-3 rounded-lg">
                      <p className="text-sm text-orange-600">أقصى مستوى هرمي</p>
                      <p className="text-xl font-bold text-orange-800">{systemStatus.dataStats.MaxHierarchyLevel || 0}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : null}
        </div>

        {/* زر الإعداد */}
        {systemStatus && !systemStatus.setupComplete && (
          <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">إعداد النظام</h2>
            <p className="text-gray-600 mb-6">
              اضغط على الزر أدناه لإنشاء الجداول والإجراءات المخزنة المطلوبة لنظام إدارة المديرين المباشرين.
            </p>
            <button
              onClick={setupSystem}
              disabled={setupLoading}
              className="flex items-center gap-2 bg-green-500 hover:bg-green-600 disabled:bg-green-300 text-white py-3 px-6 rounded-lg transition-colors"
            >
              <FiSettings className={`w-5 h-5 ${setupLoading ? 'animate-spin' : ''}`} />
              {setupLoading ? 'جاري الإعداد...' : 'إعداد النظام'}
            </button>
          </div>
        )}

        {/* نتائج الإعداد */}
        {setupResult && (
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">نتائج الإعداد</h2>
            
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
              <div className="flex items-center">
                <FiCheck className="w-5 h-5 text-green-500 mr-2" />
                <span className="text-green-700 font-medium">{setupResult.message}</span>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold text-gray-900 mb-3">إحصائيات الإعداد</h4>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">إجمالي الاستعلامات:</span>
                    <span className="font-medium">{setupResult.stats.totalStatements}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">الناجحة:</span>
                    <span className="font-medium text-green-600">{setupResult.stats.successfulStatements}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">الفاشلة:</span>
                    <span className="font-medium text-red-600">{setupResult.stats.failedStatements}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">الجداول المنشأة:</span>
                    <span className="font-medium">{setupResult.stats.createdTables}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">الإجراءات المنشأة:</span>
                    <span className="font-medium">{setupResult.stats.createdProcedures}</span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-semibold text-gray-900 mb-3">المكونات المنشأة</h4>
                <div className="space-y-2">
                  <div>
                    <p className="text-sm text-gray-600 mb-1">الجداول:</p>
                    <div className="flex flex-wrap gap-1">
                      {setupResult.details.tablesCreated.map(table => (
                        <span key={table} className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                          {table}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 mb-1">الإجراءات المخزنة:</p>
                    <div className="flex flex-wrap gap-1">
                      {setupResult.details.proceduresCreated.map(proc => (
                        <span key={proc} className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                          {proc}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {setupResult.errors && setupResult.errors.length > 0 && (
              <div className="mt-4">
                <h4 className="font-semibold text-red-900 mb-2">الأخطاء:</h4>
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <ul className="text-sm text-red-700 space-y-1">
                    {setupResult.errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              </div>
            )}
          </div>
        )}

        {/* روابط سريعة */}
        {systemStatus && systemStatus.setupComplete && (
          <div className="bg-white rounded-xl shadow-lg p-6 mt-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">روابط سريعة</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <a
                href="/direct-managers"
                className="block p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors"
              >
                <h3 className="font-semibold text-blue-900 mb-2">إدارة المديرين المباشرين</h3>
                <p className="text-sm text-blue-700">إضافة وتعديل وإدارة بيانات المديرين المباشرين</p>
              </a>
              <a
                href="/organizational-chart"
                className="block p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors"
              >
                <h3 className="font-semibold text-green-900 mb-2">الهيكل التنظيمي</h3>
                <p className="text-sm text-green-700">عرض الهيكل التنظيمي الهرمي للشركة</p>
              </a>
              <a
                href="/templates/direct_managers_template.csv"
                download
                className="block p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors"
              >
                <h3 className="font-semibold text-purple-900 mb-2">تحميل قالب Excel</h3>
                <p className="text-sm text-purple-700">تحميل قالب Excel لرفع بيانات المديرين المباشرين</p>
              </a>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
