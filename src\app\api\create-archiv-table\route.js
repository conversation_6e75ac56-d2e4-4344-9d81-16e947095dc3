import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function POST(request) {
  try {
    const pool = await getConnection();
    
    // إنشاء جدول ARCHIV إذا لم يكن موجوداً
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ARCHIV' AND xtype='U')
      BEGIN
        CREATE TABLE ARCHIV (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          Item NVARCHAR(100) NOT NULL,
          Path NVARCHAR(500) NOT NULL,
          Description NVARCHAR(200),
          IsActive BIT DEFAULT 1,
          CreatedAt DATETIME DEFAULT GETDATE()
        )
      END
    `);

    // إدراج البيانات الافتراضية
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM ARCHIV WHERE Item = 'NationalIDs')
      BEGIN
        INSERT INTO ARCHIV (Item, Path, Description) VALUES
        ('NationalIDs', 'archiv\\NationalIDs\\', 'بطاقات الرقم القومي'),
        ('WorkReceipts', 'archiv\\WorkReceipts\\', 'إستلام العمل'),
        ('StatusReports', 'archiv\\StatusReports\\', 'بيانات الحالة الاجتماعية'),
        ('UnionCards', 'archiv\\UnionCards\\', 'كارنيات النقابة'),
        ('photo', 'archiv\\photo\\', 'الصور الشخصية PDF'),
        ('pic', 'archiv\\pic\\', 'الصور الشخصية JPG'),
        ('bh', 'archiv\\bh\\', 'بدل إسكان'),
        ('bt', 'archiv\\bt\\', 'بدل انتقال'),
        ('carscost', 'archiv\\carscost\\', 'تكاليف السيارات'),
        ('housingcost', 'archiv\\housingcost\\', 'تكاليف الإسكان'),
        ('3amala', 'archiv\\3amala\\', 'العمالة')
      END
    `);

    // فحص النتيجة
    const result = await pool.request().query(`
      SELECT Item, Path, Description FROM ARCHIV ORDER BY Item
    `);

    return NextResponse.json({
      success: true,
      message: 'تم إنشاء جدول ARCHIV بنجاح',
      data: result.recordset,
      totalRecords: result.recordset.length
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  }
}
