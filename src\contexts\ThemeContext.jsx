'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // تحميل الثيم المحفوظ عند بدء التطبيق
  useEffect(() => {
    // التأكد من أن localStorage متاح (client-side)
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem('theme');
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

      if (savedTheme) {
        const isDark = savedTheme === 'dark';
        setIsDarkMode(isDark);
        // تطبيق الثيم فوراً على الـ document
        if (isDark) {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
      } else {
        setIsDarkMode(prefersDark);
        // حفظ التفضيل الافتراضي
        localStorage.setItem('theme', prefersDark ? 'dark' : 'light');
        if (prefersDark) {
          document.documentElement.classList.add('dark');
        }
      }
      setIsInitialized(true);
    }
  }, []);

  // تطبيق الثيم على الـ document عند التغيير
  useEffect(() => {
    if (isInitialized && typeof window !== 'undefined') {
      if (isDarkMode) {
        document.documentElement.classList.add('dark');
        localStorage.setItem('theme', 'dark');
      } else {
        document.documentElement.classList.remove('dark');
        localStorage.setItem('theme', 'light');
      }
    }
  }, [isDarkMode, isInitialized]);

  const toggleTheme = () => {
    setIsDarkMode(prev => {
      const newValue = !prev;
      // حفظ فوري في localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('theme', newValue ? 'dark' : 'light');
        // تطبيق فوري على الـ document
        if (newValue) {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
      }
      return newValue;
    });
  };

  // كلاسات CSS للاستخدام في المكونات
  const themeClasses = {
    bg: {
      primary: isDarkMode ? 'bg-gray-900' : 'bg-gray-50',
      secondary: isDarkMode ? 'bg-gray-800' : 'bg-white',
      tertiary: isDarkMode ? 'bg-gray-700' : 'bg-gray-100',
      card: isDarkMode ? 'bg-gray-800' : 'bg-white',
      sidebar: isDarkMode ? 'bg-gray-800' : 'bg-white',
      hover: isDarkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'
    },
    text: {
      primary: isDarkMode ? 'text-gray-100' : 'text-gray-900',
      secondary: isDarkMode ? 'text-gray-300' : 'text-gray-700',
      tertiary: isDarkMode ? 'text-gray-400' : 'text-gray-600',
      muted: isDarkMode ? 'text-gray-500' : 'text-gray-500'
    },
    border: {
      primary: isDarkMode ? 'border-gray-600' : 'border-gray-300',
      secondary: isDarkMode ? 'border-gray-700' : 'border-gray-200'
    },
    shadow: {
      sm: isDarkMode ? 'shadow-lg shadow-black/20' : 'shadow-sm',
      md: isDarkMode ? 'shadow-xl shadow-black/25' : 'shadow-md',
      lg: isDarkMode ? 'shadow-2xl shadow-black/30' : 'shadow-lg'
    },
    input: {
      primary: isDarkMode
        ? 'bg-gray-700 border-gray-600 text-gray-100 placeholder-gray-400'
        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
    },
    button: {
      primary: isDarkMode
        ? 'bg-blue-600 hover:bg-blue-700 text-white'
        : 'bg-blue-600 hover:bg-blue-700 text-white',
      secondary: isDarkMode
        ? 'bg-gray-700 hover:bg-gray-600 text-gray-200'
        : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
    }
  };

  const value = {
    isDarkMode,
    toggleTheme,
    themeClasses,
    isInitialized
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
