import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

// GET /api/attendance?date=YYYY-MM-DD
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const date = searchParams.get('date');

    if (!date) {
      return NextResponse.json({ error: 'Date is required' }, { status: 400 });
    }

    const pool = await getConnection();

    // إنشاء جدول التمام إذا لم يكن موجوداً
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='DailyAttendance' AND xtype='U')
      BEGIN
        CREATE TABLE DailyAttendance (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          AttendanceDate DATE NOT NULL,
          EmployeeCode NVARCHAR(20) NOT NULL,
          EmployeeName NVARCHAR(100) NOT NULL,
          Attendance NVARCHAR(50) NOT NULL,
          CheckInTime NVARCHAR(20),
          Notes NVARCHAR(MAX),
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE()
        )

        CREATE INDEX IX_DailyAttendance_Date ON DailyAttendance(AttendanceDate)
        CREATE INDEX IX_DailyAttendance_Employee ON DailyAttendance(EmployeeCode)
      END
    `);

    // التأكد من وجود جدول التمام
    await ensureDailyAttendanceTable(pool);

    // جلب بيانات التمام لليوم المحدد
    const result = await pool.request()
      .input('date', sql.Date, date)
      .query(`
        SELECT
          EmployeeCode as employeeCode,
          EmployeeName as employeeName,
          ISNULL(Attendance, N'') as attendance,
          ISNULL(CheckInTime, N'') as checkIn,
          ISNULL(Notes, N'') as notes
        FROM DailyAttendance
        WHERE AttendanceDate = @date
        ORDER BY EmployeeCode
      `);

    return NextResponse.json({
      success: true,
      data: result.recordset
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب بيانات التمام: ' + error.message
    }, { status: 500 });
  }
}

// POST /api/attendance
export async function POST(request) {
  try {
    const data = await request.json();
    const { action, date, attendanceData } = data;

    const pool = await getConnection();

    // إنشاء جدول التمام إذا لم يكن موجوداً
    await ensureDailyAttendanceTable(pool);

    switch (action) {
      case 'save-daily-attendance':
        return await saveDailyAttendance(pool, { date, attendanceData });
      case 'update-from-requests':
        return await updateAttendanceFromRequests(pool, { date });
      case 'get-monthly-summary':
        return await getMonthlyAttendanceSummary(pool, data);
      case 'transfer-to-monthly-sheet':
        return await transferToMonthlySheet(pool, data);
      case 'calculate-monthly-attendance':
        return await calculateMonthlyAttendance(pool, data);
      case 'ensure-table':
        await ensureDailyAttendanceTable(pool);
        return NextResponse.json({ success: true, message: 'Table ensured' });
      default:
        // الحفظ العادي (للتوافق مع النسخة القديمة)
        return await saveDailyAttendance(pool, { date, attendanceData });
    }

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'خطأ في API التمام: ' + error.message
    }, { status: 500 });
  }
}

// ترحيل التمام اليومي إلى كشف التمام الشهري
async function transferToMonthlySheet(pool, data) {
  try {
    const { month, year } = data;

    // التأكد من وجود جدول كشف التمام الشهري (بدون أوقات الحضور)
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MonthlyAttendanceSheet' AND xtype='U')
      BEGIN
        CREATE TABLE MonthlyAttendanceSheet (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          EmployeeCode NVARCHAR(20) NOT NULL,
          EmployeeName NVARCHAR(100) NOT NULL,
          Department NVARCHAR(100),
          JobTitle NVARCHAR(100),
          Month INT NOT NULL,
          Year INT NOT NULL,
          AttendanceDate DATE NOT NULL,
          AttendanceStatus NVARCHAR(50) NOT NULL,
          Notes NVARCHAR(MAX),
          CreatedAt DATETIME DEFAULT GETDATE(),

          UNIQUE (EmployeeCode, AttendanceDate)
        )
      END
    `);

    // حذف البيانات السابقة للشهر
    await pool.request()
      .input('month', sql.Int, month)
      .input('year', sql.Int, year)
      .query(`
        DELETE FROM MonthlyAttendanceSheet
        WHERE Month = @month AND Year = @year
      `);

    // جلب بيانات التمام اليومي للشهر (التركيز على حقل التمام فقط)
    const attendanceResult = await pool.request()
      .input('month', sql.Int, month)
      .input('year', sql.Int, year)
      .query(`
        SELECT
          EmployeeCode,
          EmployeeName,
          Department,
          JobTitle,
          AttendanceDate,
          Attendance as AttendanceStatus,
          Notes
        FROM DailyAttendance
        WHERE MONTH(AttendanceDate) = @month
        AND YEAR(AttendanceDate) = @year
        ORDER BY EmployeeCode, AttendanceDate
      `);

    let transferredCount = 0;

    // ترحيل كل سجل تمام إلى كشف التمام الشهري (التركيز على حقل التمام فقط)
    for (const record of attendanceResult.recordset) {
      // التحقق من صحة البيانات الأساسية
      if (!record.EmployeeCode || !record.EmployeeName || !record.AttendanceStatus) {
        continue;
      }

      try {
        await pool.request()
          .input('employeeCode', sql.NVarChar, String(record.EmployeeCode))
          .input('employeeName', sql.NVarChar, String(record.EmployeeName))
          .input('department', sql.NVarChar, String(record.Department || ''))
          .input('jobTitle', sql.NVarChar, String(record.JobTitle || ''))
          .input('month', sql.Int, month)
          .input('year', sql.Int, year)
          .input('attendanceDate', sql.Date, record.AttendanceDate)
          .input('attendanceStatus', sql.NVarChar, String(record.AttendanceStatus))
          .input('notes', sql.NVarChar, String(record.Notes || ''))
          .query(`
            INSERT INTO MonthlyAttendanceSheet (
              EmployeeCode, EmployeeName, Department, JobTitle, Month, Year,
              AttendanceDate, AttendanceStatus, Notes
            )
            VALUES (
              @employeeCode, @employeeName, @department, @jobTitle, @month, @year,
              @attendanceDate, @attendanceStatus, @notes
            )
          `);

        transferredCount++;

        // طباعة تقدم العملية كل 50 سجل
        if (transferredCount % 50 === 0) {
        }

      } catch (recordError) {
        // استمر في معالجة السجلات الأخرى
      }
    }

    return NextResponse.json({
      success: true,
      message: `تم ترحيل ${transferredCount} سجل من التمام اليومي إلى كشف التمام الشهري`,
      transferredCount
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'فشل في ترحيل التمام اليومي: ' + error.message
    }, { status: 500 });
  }
}

// حساب ملخص التمام الشهري
async function calculateMonthlyAttendance(pool, data) {
  try {
    const { month, year } = data;

    // إنشاء جدول ملخص التمام الشهري إذا لم يكن موجوداً
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='MonthlyAttendanceSummary' AND xtype='U')
      BEGIN
        CREATE TABLE MonthlyAttendanceSummary (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          EmployeeCode NVARCHAR(20) NOT NULL,
          EmployeeName NVARCHAR(100) NOT NULL,
          Department NVARCHAR(100),
          JobTitle NVARCHAR(100),
          Month INT NOT NULL,
          Year INT NOT NULL,
          TotalWorkingDays INT DEFAULT 0,
          TotalPresent INT DEFAULT 0,
          TotalAbsent INT DEFAULT 0,
          TotalLeaves INT DEFAULT 0,
          TotalMissions INT DEFAULT 0,
          TotalSickLeave INT DEFAULT 0,
          TotalUnpaidLeave INT DEFAULT 0,
          TotalNightShifts INT DEFAULT 0,
          AttendancePercentage DECIMAL(5,2) DEFAULT 0,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),

          UNIQUE (EmployeeCode, Month, Year)
        )
      END
    `);

    // حذف البيانات السابقة للشهر
    await pool.request()
      .input('month', sql.Int, month)
      .input('year', sql.Int, year)
      .query(`
        DELETE FROM MonthlyAttendanceSummary
        WHERE Month = @month AND Year = @year
      `);

    // حساب ملخص التمام لكل موظف
    const summaryResult = await pool.request()
      .input('month', sql.Int, month)
      .input('year', sql.Int, year)
      .query(`
        SELECT
          EmployeeCode,
          EmployeeName,
          Department,
          JobTitle,
          COUNT(*) as TotalWorkingDays,
          SUM(CASE WHEN Attendance = 'حضور' THEN 1 ELSE 0 END) as TotalPresent,
          SUM(CASE WHEN Attendance = 'غياب' THEN 1 ELSE 0 END) as TotalAbsent,
          SUM(CASE WHEN Attendance LIKE '%إجازة%' THEN 1 ELSE 0 END) as TotalLeaves,
          SUM(CASE WHEN Attendance = 'مأمورية' THEN 1 ELSE 0 END) as TotalMissions,
          SUM(CASE WHEN Attendance = 'إجازة مرضية' THEN 1 ELSE 0 END) as TotalSickLeave,
          SUM(CASE WHEN Attendance = 'إجازة بدون أجر' THEN 1 ELSE 0 END) as TotalUnpaidLeave,
          SUM(CASE WHEN Attendance = 'وردية ليلية' THEN 1 ELSE 0 END) as TotalNightShifts
        FROM DailyAttendance
        WHERE MONTH(AttendanceDate) = @month
        AND YEAR(AttendanceDate) = @year
        GROUP BY EmployeeCode, EmployeeName, Department, JobTitle
      `);

    // إدراج ملخص التمام لكل موظف
    for (const summary of summaryResult.recordset) {
      // التحقق من صحة البيانات
      if (!summary.EmployeeCode || !summary.EmployeeName) {
        continue;
      }

      try {
        const attendancePercentage = summary.TotalWorkingDays > 0
          ? (summary.TotalPresent / summary.TotalWorkingDays) * 100
          : 0;

        await pool.request()
          .input('employeeCode', sql.NVarChar, String(summary.EmployeeCode || ''))
          .input('employeeName', sql.NVarChar, String(summary.EmployeeName || ''))
          .input('department', sql.NVarChar, String(summary.Department || ''))
          .input('jobTitle', sql.NVarChar, String(summary.JobTitle || ''))
          .input('month', sql.Int, month)
          .input('year', sql.Int, year)
          .input('totalWorkingDays', sql.Int, summary.TotalWorkingDays || 0)
          .input('totalPresent', sql.Int, summary.TotalPresent || 0)
          .input('totalAbsent', sql.Int, summary.TotalAbsent || 0)
          .input('totalLeaves', sql.Int, summary.TotalLeaves || 0)
          .input('totalMissions', sql.Int, summary.TotalMissions || 0)
          .input('totalSickLeave', sql.Int, summary.TotalSickLeave || 0)
          .input('totalUnpaidLeave', sql.Int, summary.TotalUnpaidLeave || 0)
          .input('totalNightShifts', sql.Int, summary.TotalNightShifts || 0)
          .input('attendancePercentage', sql.Decimal(5, 2), attendancePercentage)
          .query(`
            INSERT INTO MonthlyAttendanceSummary (
              EmployeeCode, EmployeeName, Department, JobTitle, Month, Year,
              TotalWorkingDays, TotalPresent, TotalAbsent, TotalLeaves, TotalMissions,
              TotalSickLeave, TotalUnpaidLeave, TotalNightShifts, AttendancePercentage
            )
            VALUES (
              @employeeCode, @employeeName, @department, @jobTitle, @month, @year,
              @totalWorkingDays, @totalPresent, @totalAbsent, @totalLeaves, @totalMissions,
              @totalSickLeave, @totalUnpaidLeave, @totalNightShifts, @attendancePercentage
            )
          `);
      } catch (summaryError) {
        // استمر في معالجة الموظفين الآخرين
      }
    }

    return NextResponse.json({
      success: true,
      message: `تم حساب ملخص التمام الشهري لـ ${summaryResult.recordset.length} موظف`,
      employeesCount: summaryResult.recordset.length
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'فشل في حساب ملخص التمام الشهري: ' + error.message
    }, { status: 500 });
  }
}

// إنشاء جدول التمام اليومي
async function ensureDailyAttendanceTable(pool) {
  try {
    // التحقق من وجود الجدول أولاً
    const tableExists = await pool.request().query(`
      SELECT COUNT(*) as count
      FROM INFORMATION_SCHEMA.TABLES
      WHERE TABLE_NAME = 'DailyAttendance'
    `);

    if (tableExists.recordset[0].count === 0) {
      // إنشاء الجدول إذا لم يكن موجوداً
      await pool.request().query(`
        CREATE TABLE DailyAttendance (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          AttendanceDate DATE NOT NULL,
          EmployeeCode NVARCHAR(20) NOT NULL,
          EmployeeName NVARCHAR(100) NOT NULL,
          Department NVARCHAR(100),
          JobTitle NVARCHAR(100),
          Attendance NVARCHAR(50) NOT NULL,
          CheckInTime NVARCHAR(20),
          CheckOutTime NVARCHAR(20),
          Notes NVARCHAR(MAX),
          IsFromRequest BIT DEFAULT 0,
          RequestID INT NULL,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),

          UNIQUE (AttendanceDate, EmployeeCode)
        )
      `);

      // إضافة الفهارس
      await pool.request().query(`
        CREATE INDEX IX_DailyAttendance_Date ON DailyAttendance (AttendanceDate)
      `);
      await pool.request().query(`
        CREATE INDEX IX_DailyAttendance_Employee ON DailyAttendance (EmployeeCode)
      `);
      await pool.request().query(`
        CREATE INDEX IX_DailyAttendance_Request ON DailyAttendance (RequestID)
      `);
    } else {
      // التحقق من وجود العمود Attendance وإضافته إذا لم يكن موجوداً
      const columnExists = await pool.request().query(`
        SELECT COUNT(*) as count
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE TABLE_NAME = 'DailyAttendance' AND COLUMN_NAME = 'Attendance'
      `);

      if (columnExists.recordset[0].count === 0) {
        await pool.request().query(`
          ALTER TABLE DailyAttendance
          ADD Attendance NVARCHAR(50) NOT NULL DEFAULT N'حضور'
        `);
      }

      // التحقق من الأعمدة الأخرى وإضافتها إذا لم تكن موجودة
      const checkAndAddColumn = async (columnName, columnType, defaultValue = null) => {
        const exists = await pool.request().query(`
          SELECT COUNT(*) as count
          FROM INFORMATION_SCHEMA.COLUMNS
          WHERE TABLE_NAME = 'DailyAttendance' AND COLUMN_NAME = '${columnName}'
        `);

        if (exists.recordset[0].count === 0) {
          const defaultClause = defaultValue ? ` DEFAULT ${defaultValue}` : '';
          await pool.request().query(`
            ALTER TABLE DailyAttendance
            ADD ${columnName} ${columnType}${defaultClause}
          `);
        }
      };

      await checkAndAddColumn('CheckInTime', 'NVARCHAR(20)');
      await checkAndAddColumn('CheckOutTime', 'NVARCHAR(20)');
      await checkAndAddColumn('Notes', 'NVARCHAR(MAX)');
      await checkAndAddColumn('IsFromRequest', 'BIT', '0');
      await checkAndAddColumn('RequestID', 'INT');
      await checkAndAddColumn('attendanceStatus', 'NVARCHAR(50)', "'حضور'"); // إضافة العمود المطلوب
      await checkAndAddColumn('CreatedAt', 'DATETIME', 'GETDATE()');
      await checkAndAddColumn('UpdatedAt', 'DATETIME', 'GETDATE()');
    }
  } catch (error) {
    throw error;
  }
}

// حفظ التمام اليومي
async function saveDailyAttendance(pool, { date, attendanceData }) {
  if (!date || !attendanceData || !Array.isArray(attendanceData)) {
    return NextResponse.json({
      success: false,
      error: 'تنسيق البيانات غير صحيح'
    }, { status: 400 });
  }

  // حذف التمام القديم لهذا اليوم (فقط الذي ليس من طلبات)
  await pool.request()
    .input('date', sql.Date, date)
    .query(`
      DELETE FROM DailyAttendance
      WHERE AttendanceDate = @date AND (IsFromRequest = 0 OR IsFromRequest IS NULL)
    `);

  let savedCount = 0;

  // إضافة التمام الجديد
  for (const record of attendanceData) {
    const employeeCode = record.EmployeeCode || record.employeeCode;
    const employeeName = record.EmployeeName || record.employeeName;
    const department = record.Department || record.department || '';
    const jobTitle = record.JobTitle || record.jobTitle || '';
    const attendance = record.attendance;
    const checkIn = record.checkIn || '';
    const checkOut = record.checkOut || '';
    const notes = record.notes || '';

    // تخطي السجلات التي لا تحتوي على بيانات أساسية
    if (!employeeCode || !employeeName || !attendance) {
      continue;
    }

    // تحويل نوع البيانات إلى string إذا لزم الأمر
    const employeeCodeStr = String(employeeCode);
    const employeeNameStr = String(employeeName);

    // التحقق من صحة البيانات بعد التحويل
    if (!employeeCodeStr || !employeeNameStr || employeeCodeStr === 'undefined' || employeeNameStr === 'undefined') {
      continue;
    }

    // التحقق من وجود طلب معتمد لهذا اليوم
    const existingRequest = await pool.request()
      .input('date', sql.Date, date)
      .input('employeeCode', sql.NVarChar, employeeCodeStr)
      .query(`
        SELECT TOP 1 ID, LeaveType
        FROM PaperRequests
        WHERE EmployeeCode = @employeeCode
        AND Status = N'معتمد'
        AND @date BETWEEN StartDate AND EndDate
      `);

    let isFromRequest = false;
    let requestID = null;

    if (existingRequest.recordset.length > 0) {
      isFromRequest = true;
      requestID = existingRequest.recordset[0].ID;
      // إذا كان هناك طلب معتمد، استخدم نوع الإجازة من الطلب
      // attendance = existingRequest.recordset[0].LeaveType;
    }

    try {
      await pool.request()
        .input('date', sql.Date, date)
        .input('employeeCode', sql.NVarChar, employeeCodeStr)
        .input('employeeName', sql.NVarChar, employeeNameStr)
        .input('department', sql.NVarChar, String(department))
        .input('jobTitle', sql.NVarChar, String(jobTitle))
        .input('attendance', sql.NVarChar, String(attendance))
        .input('checkIn', sql.NVarChar, String(checkIn))
        .input('checkOut', sql.NVarChar, String(checkOut))
        .input('notes', sql.NVarChar, String(notes))
        .input('isFromRequest', sql.Bit, isFromRequest)
        .input('requestID', sql.Int, requestID)
        .input('attendanceStatus', sql.NVarChar, String(attendance)) // إضافة attendanceStatus
        .query(`
          INSERT INTO DailyAttendance
          (AttendanceDate, EmployeeCode, EmployeeName, Department, JobTitle,
           Attendance, CheckInTime, CheckOutTime, Notes, IsFromRequest, RequestID, attendanceStatus)
          VALUES (@date, @employeeCode, @employeeName, @department, @jobTitle,
                  @attendance, @checkIn, @checkOut, @notes, @isFromRequest, @requestID, @attendanceStatus)
        `);

      savedCount++;
      console.log(`Successfully saved attendance for employee: ${employeeNameStr} (${employeeCodeStr})`);
    } catch (insertError) {
      // استمر في معالجة السجلات الأخرى
    }
  }

  return NextResponse.json({
    success: true,
    message: 'تم حفظ التمام بنجاح',
    savedRecords: savedCount
  });
}

// تحديث التمام من الطلبات المعتمدة
async function updateAttendanceFromRequests(pool, { date }) {
  try {
    // جلب الطلبات المعتمدة لهذا اليوم
    const approvedRequests = await pool.request()
      .input('date', sql.Date, date)
      .query(`
        SELECT
          pr.ID,
          pr.EmployeeCode,
          pr.EmployeeName,
          pr.Department,
          pr.JobTitle,
          pr.LeaveType,
          pr.StartDate,
          pr.EndDate
        FROM PaperRequests pr
        WHERE pr.Status = N'معتمدة'
        AND @date BETWEEN pr.StartDate AND pr.EndDate
      `);

    let updatedCount = 0;

    for (const request of approvedRequests.recordset) {
      // التحقق من وجود سجل تمام لهذا الموظف في هذا اليوم
      const existingAttendance = await pool.request()
        .input('date', sql.Date, date)
        .input('employeeCode', sql.NVarChar, request.EmployeeCode)
        .query(`
          SELECT ID FROM DailyAttendance
          WHERE AttendanceDate = @date AND EmployeeCode = @employeeCode
        `);

      if (existingAttendance.recordset.length > 0) {
        // تحديث السجل الموجود
        await pool.request()
          .input('date', sql.Date, date)
          .input('employeeCode', sql.NVarChar, request.EmployeeCode)
          .input('attendance', sql.NVarChar, request.LeaveType)
          .input('requestID', sql.Int, request.ID)
          .input('attendanceStatus', sql.NVarChar, request.LeaveType)
          .query(`
            UPDATE DailyAttendance
            SET
              Attendance = @attendance,
              attendanceStatus = @attendanceStatus,
              IsFromRequest = 1,
              RequestID = @requestID,
              UpdatedAt = GETDATE()
            WHERE AttendanceDate = @date AND EmployeeCode = @employeeCode
          `);
      } else {
        // إنشاء سجل جديد
        await pool.request()
          .input('date', sql.Date, date)
          .input('employeeCode', sql.NVarChar, request.EmployeeCode)
          .input('employeeName', sql.NVarChar, request.EmployeeName)
          .input('department', sql.NVarChar, request.Department || '')
          .input('jobTitle', sql.NVarChar, request.JobTitle || '')
          .input('attendance', sql.NVarChar, request.LeaveType)
          .input('requestID', sql.Int, request.ID)
          .input('attendanceStatus', sql.NVarChar, request.LeaveType) // إضافة attendanceStatus
          .query(`
            INSERT INTO DailyAttendance
            (AttendanceDate, EmployeeCode, EmployeeName, Department, JobTitle,
             Attendance, IsFromRequest, RequestID, attendanceStatus)
            VALUES (@date, @employeeCode, @employeeName, @department, @jobTitle,
                    @attendance, 1, @requestID, @attendanceStatus)
          `);
      }

      updatedCount++;
    }

    return NextResponse.json({
      success: true,
      message: `تم تحديث ${updatedCount} سجل تمام من الطلبات المعتمدة`,
      updatedCount: updatedCount
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'خطأ في تحديث التمام من الطلبات: ' + error.message
    }, { status: 500 });
  }
}