'use client';
import React, { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import { useLanguage } from '@/contexts/LanguageContext';
import { useTheme } from '@/contexts/ThemeContext';
import { Search, RefreshCw, RotateCcw, Calendar, CheckCircle, User, AlertTriangle } from 'lucide-react';

export default function ApprovedLeavesPage() {
  const { isArabic } = useLanguage();
  const { isDarkMode } = useTheme();
  
  const [approvedLeaves, setApprovedLeaves] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLeave, setSelectedLeave] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [notes, setNotes] = useState('');

  // جلب الإجازات المعتمدة
  const fetchApprovedLeaves = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/paper-requests?action=list&status=معتمدة&requestType=leave');
      const result = await response.json();
      
      if (result.success) {
        setApprovedLeaves(result.requests || []);
      } else {

      }
    } catch (error) {

    }
    setLoading(false);
  };

  useEffect(() => {
    fetchApprovedLeaves();
  }, []);

  // فلترة الإجازات حسب البحث
  const filteredLeaves = approvedLeaves.filter(leave => 
    leave.EmployeeName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    leave.EmployeeCode?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    leave.LeaveType?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // فتح نافذة الرجوع عن الاعتماد
  const openRevokeModal = (leave) => {
    setSelectedLeave(leave);
    setNotes('');
    setShowModal(true);
  };

  // الرجوع عن الاعتماد
  const revokeApproval = async () => {
    if (!selectedLeave) return;

    try {
      // الحصول على كود المستخدم من localStorage
      const userInfo = localStorage.getItem('userInfo');
      const userCode = userInfo ? JSON.parse(userInfo).code || '1450' : '1450';

      const response = await fetch('/api/paper-requests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-user-code': userCode // إرسال كود المستخدم
        },
        body: JSON.stringify({
          action: 'update-status',
          requestId: selectedLeave.ID,
          status: 'قيد المراجعة',
          notes: notes
        })
      });

      const result = await response.json();
      
      if (result.success) {
        alert('تم الرجوع عن اعتماد الإجازة بنجاح. ستعود الإجازة إلى قائمة الطلبات قيد المراجعة.');
        fetchApprovedLeaves(); // إعادة تحميل الإجازات
        setShowModal(false);

        await addDaysToBalance(selectedLeave);
      } else {
        alert(result.error || 'خطأ في الرجوع عن الاعتماد');
      }
    } catch (error) {

      alert('خطأ في الرجوع عن الاعتماد');
    }
  };

  // إضافة الأيام إلى رصيد الموظف (سيتم تطبيقها لاحقاً)
  const addDaysToBalance = async (leave) => {
    try {

    } catch (error) {

    }
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto">
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6 mb-6`}>
          <div className="flex items-center justify-between">
            <div>
              <h1 className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'} mb-2`}>
                الإجازات المعتمدة
              </h1>
              <p className={`${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                عرض وإدارة الإجازات المعتمدة مع إمكانية الرجوع عن الاعتماد
              </p>
            </div>
            <div className="flex gap-3">
              <button
                onClick={fetchApprovedLeaves}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50"
              >
                <RefreshCw className="w-4 h-4" />
                تحديث
              </button>
            </div>
          </div>
        </div>

        {/* شريط البحث */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-4 mb-6`}>
          <div className="flex items-center gap-4">
            <div className="flex-1 relative">
              <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="البحث بالاسم أو الكود أو نوع الإجازة..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`w-full pr-10 pl-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  isDarkMode 
                    ? 'bg-slate-800 border-slate-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                }`}
              />
            </div>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <CheckCircle className="w-8 h-8 text-green-500 mr-3" />
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>إجازات معتمدة</p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {filteredLeaves.length}
                </p>
              </div>
            </div>
          </div>
          
          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <Calendar className="w-8 h-8 text-blue-500 mr-3" />
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>إجمالي الأيام</p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {filteredLeaves.reduce((sum, leave) => sum + (leave.DaysCount || 0), 0)}
                </p>
              </div>
            </div>
          </div>
          
          <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border p-6`}>
            <div className="flex items-center">
              <User className="w-8 h-8 text-purple-500 mr-3" />
              <div>
                <p className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>موظفين مختلفين</p>
                <p className={`text-2xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  {new Set(filteredLeaves.map(leave => leave.EmployeeCode)).size}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* جدول الإجازات المعتمدة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937] border-slate-700' : 'bg-white border-gray-200'} rounded-lg shadow-sm border overflow-hidden`}>
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <RefreshCw className="w-6 h-6 animate-spin text-blue-500" />
              <span className="mr-2">جاري التحميل...</span>
            </div>
          ) : filteredLeaves.length === 0 ? (
            <div className="text-center py-12">
              <CheckCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className={`text-lg ${isDarkMode ? 'text-slate-400' : 'text-gray-600'}`}>
                لا توجد إجازات معتمدة
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className={`${isDarkMode ? 'bg-slate-800' : 'bg-gray-50'}`}>
                  <tr>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      الموظف
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      نوع الإجازة
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      التواريخ
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      عدد الأيام
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      تاريخ الاعتماد
                    </th>
                    <th className={`px-6 py-3 text-right text-xs font-medium ${isDarkMode ? 'text-slate-300' : 'text-gray-500'} uppercase tracking-wider`}>
                      الإجراءات
                    </th>
                  </tr>
                </thead>
                <tbody className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} divide-y ${isDarkMode ? 'divide-slate-700' : 'divide-gray-200'}`}>
                  {filteredLeaves.map((leave) => (
                    <tr key={leave.ID} className={`hover:${isDarkMode ? 'bg-slate-800' : 'bg-gray-50'} transition-colors`}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                            {leave.EmployeeName}
                          </div>
                          <div className={`text-sm ${isDarkMode ? 'text-slate-400' : 'text-gray-500'}`}>
                            {leave.EmployeeCode}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          leave.LeaveType === 'اعتيادية' 
                            ? 'bg-blue-100 text-blue-800' 
                            : leave.LeaveType === 'مرضية'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {leave.LeaveType}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <div className={isDarkMode ? 'text-slate-300' : 'text-gray-900'}>
                          {leave.StartDate && new Date(leave.StartDate).toLocaleDateString('ar-EG')}
                          {leave.EndDate && (
                            <> - {new Date(leave.EndDate).toLocaleDateString('ar-EG')}</>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`text-sm font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          {leave.DaysCount} يوم
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        <div className={isDarkMode ? 'text-slate-300' : 'text-gray-900'}>
                          {leave.ApprovalDate && new Date(leave.ApprovalDate).toLocaleDateString('ar-EG')}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => openRevokeModal(leave)}
                          className="bg-orange-600 hover:bg-orange-700 text-white px-3 py-1 rounded text-xs flex items-center gap-1"
                          title="رجوع عن الاعتماد"
                        >
                          <RotateCcw className="w-3 h-3" />
                          رجوع عن الاعتماد
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* نافذة تأكيد الرجوع عن الاعتماد */}
        {showModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg p-6 w-full max-w-md mx-4`}>
              <h3 className={`text-lg font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                رجوع عن اعتماد الإجازة
              </h3>
              
              {selectedLeave && (
                <div className={`mb-4 p-3 rounded ${isDarkMode ? 'bg-slate-800' : 'bg-gray-100'}`}>
                  <p className={`text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                    <strong>الموظف:</strong> {selectedLeave.EmployeeName} ({selectedLeave.EmployeeCode})
                  </p>
                  <p className={`text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                    <strong>نوع الإجازة:</strong> {selectedLeave.LeaveType}
                  </p>
                  <p className={`text-sm ${isDarkMode ? 'text-slate-300' : 'text-gray-600'}`}>
                    <strong>المدة:</strong> {selectedLeave.DaysCount} يوم
                  </p>
                </div>
              )}

              <div className={`mb-4 p-3 rounded border-r-4 border-orange-500 ${isDarkMode ? 'bg-orange-900/20' : 'bg-orange-50'}`}>
                <div className="flex items-center">
                  <AlertTriangle className="w-5 h-5 text-orange-500 mr-2" />
                  <p className={`text-sm ${isDarkMode ? 'text-orange-200' : 'text-orange-800'}`}>
                    سيتم إرجاع الإجازة إلى قائمة "قيد المراجعة" وإضافة الأيام إلى رصيد الموظف
                  </p>
                </div>
              </div>

              <div className="mb-4">
                <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-slate-300' : 'text-gray-700'}`}>
                  سبب الرجوع عن الاعتماد
                </label>
                <textarea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  rows={3}
                  className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 ${
                    isDarkMode
                      ? 'bg-slate-800 border-slate-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  }`}
                  placeholder="أدخل سبب الرجوع عن الاعتماد..."
                />
              </div>

              <div className="flex justify-end gap-3">
                <button
                  onClick={() => setShowModal(false)}
                  className={`px-4 py-2 rounded-lg ${
                    isDarkMode 
                      ? 'bg-slate-600 hover:bg-slate-700 text-white' 
                      : 'bg-gray-300 hover:bg-gray-400 text-gray-700'
                  }`}
                >
                  إلغاء
                </button>
                <button
                  onClick={revokeApproval}
                  className="px-4 py-2 rounded-lg text-white bg-orange-600 hover:bg-orange-700"
                >
                  رجوع عن الاعتماد
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
