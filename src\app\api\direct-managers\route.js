import { NextResponse } from 'next/server';
import { getConnection } from '@/lib/db';
import sql from 'mssql';

// GET - جلب بيانات المديرين المباشرين
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const employeeCode = searchParams.get('employeeCode');
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 50;
    const search = searchParams.get('search') || '';

    const pool = await getConnection();

    let query = `
      SELECT 
        dm.*,
        -- معلومات إضافية من جدول الموظفين
        e.Department as EmployeeDepartment,
        e.Mobile,
        e.email,
        -- عدد المرؤوسين المباشرين
        (SELECT COUNT(*) FROM DirectManagers sub 
         WHERE sub.DirectManager1Code = dm.EmployeeCode AND sub.IsActive = 1) as DirectReportsCount
      FROM DirectManagers dm
      LEFT JOIN Employees e ON dm.EmployeeCode = e.EmployeeCode
      WHERE dm.IsActive = 1
    `;

    const params = [];

    if (employeeCode) {
      query += ` AND dm.EmployeeCode = @employeeCode`;
      params.push({ name: 'employeeCode', type: sql.NVarChar, value: employeeCode });
    }

    if (search) {
      query += ` AND (dm.EmployeeName LIKE @search OR dm.EmployeeCode LIKE @search OR dm.JobTitle LIKE @search)`;
      params.push({ name: 'search', type: sql.NVarChar, value: `%${search}%` });
    }

    // إضافة الترتيب والتصفح
    query += ` ORDER BY dm.HierarchyLevel, dm.EmployeeName`;
    
    if (!employeeCode) {
      const offset = (page - 1) * limit;
      query += ` OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY`;
      params.push({ name: 'offset', type: sql.Int, value: offset });
      params.push({ name: 'limit', type: sql.Int, value: limit });
    }

    const request_db = pool.request();
    params.forEach(param => {
      request_db.input(param.name, param.type, param.value);
    });

    const result = await request_db.query(query);

    // إحصائيات إضافية
    const statsQuery = `
      SELECT 
        COUNT(*) as TotalRecords,
        COUNT(CASE WHEN HierarchyLevel = 0 THEN 1 END) as TopLevel,
        COUNT(CASE WHEN HierarchyLevel = 1 THEN 1 END) as Level1,
        COUNT(CASE WHEN HierarchyLevel = 2 THEN 1 END) as Level2,
        COUNT(CASE WHEN HierarchyLevel = 3 THEN 1 END) as Level3,
        COUNT(CASE WHEN HierarchyLevel = 4 THEN 1 END) as Level4,
        MAX(HierarchyLevel) as MaxLevel
      FROM DirectManagers 
      WHERE IsActive = 1
    `;

    const statsResult = await pool.request().query(statsQuery);

    return NextResponse.json({
      success: true,
      data: result.recordset,
      stats: statsResult.recordset[0],
      pagination: {
        page,
        limit,
        total: statsResult.recordset[0].TotalRecords
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// POST - إضافة أو تحديث مدير مباشر
export async function POST(request) {
  try {
    const data = await request.json();
    const {
      employeeCode,
      employeeName,
      jobTitle,
      department,
      directManager1Code,
      directManager1Name,
      directManager2Code,
      directManager2Name,
      directManager3Code,
      directManager3Name,
      directManager4Code,
      directManager4Name,
      notes,
      updatedBy
    } = data;

    if (!employeeCode || !employeeName) {
      return NextResponse.json({
        success: false,
        error: 'كود الموظف والاسم مطلوبان'
      }, { status: 400 });
    }

    const pool = await getConnection();

    // استخدام الإجراء المخزن
    const result = await pool.request()
      .input('EmployeeCode', sql.NVarChar, employeeCode)
      .input('EmployeeName', sql.NVarChar, employeeName)
      .input('JobTitle', sql.NVarChar, jobTitle)
      .input('Department', sql.NVarChar, department)
      .input('DirectManager1Code', sql.NVarChar, directManager1Code)
      .input('DirectManager1Name', sql.NVarChar, directManager1Name)
      .input('DirectManager2Code', sql.NVarChar, directManager2Code)
      .input('DirectManager2Name', sql.NVarChar, directManager2Name)
      .input('DirectManager3Code', sql.NVarChar, directManager3Code)
      .input('DirectManager3Name', sql.NVarChar, directManager3Name)
      .input('DirectManager4Code', sql.NVarChar, directManager4Code)
      .input('DirectManager4Name', sql.NVarChar, directManager4Name)
      .input('Notes', sql.NVarChar, notes)
      .input('UpdatedBy', sql.NVarChar, updatedBy || 'System')
      .execute('sp_UpdateDirectManager');

    return NextResponse.json({
      success: true,
      message: 'تم حفظ بيانات المدير المباشر بنجاح',
      data: result.recordset[0]
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// PUT - تحديث بيانات مدير مباشر
export async function PUT(request) {
  try {
    const data = await request.json();
    const { employeeCode } = data;

    if (!employeeCode) {
      return NextResponse.json({
        success: false,
        error: 'كود الموظف مطلوب'
      }, { status: 400 });
    }

    // استخدام نفس منطق POST للتحديث
    return await POST(request);

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}

// DELETE - حذف مدير مباشر (إلغاء تفعيل)
export async function DELETE(request) {
  try {
    const { searchParams } = new URL(request.url);
    const employeeCode = searchParams.get('employeeCode');
    const deletedBy = searchParams.get('deletedBy') || 'System';

    if (!employeeCode) {
      return NextResponse.json({
        success: false,
        error: 'كود الموظف مطلوب'
      }, { status: 400 });
    }

    const pool = await getConnection();

    // حفظ البيانات القديمة قبل الحذف
    const oldDataResult = await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .query('SELECT * FROM DirectManagers WHERE EmployeeCode = @employeeCode');

    if (oldDataResult.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الموظف غير موجود'
      }, { status: 404 });
    }

    // إلغاء تفعيل السجل
    await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .input('deletedBy', sql.NVarChar, deletedBy)
      .query(`
        UPDATE DirectManagers 
        SET IsActive = 0, UpdatedAt = GETDATE(), UpdatedBy = @deletedBy
        WHERE EmployeeCode = @employeeCode
      `);

    // تسجيل التغيير في السجل
    await pool.request()
      .input('employeeCode', sql.NVarChar, employeeCode)
      .input('oldData', sql.NVarChar, JSON.stringify(oldDataResult.recordset[0]))
      .input('deletedBy', sql.NVarChar, deletedBy)
      .query(`
        INSERT INTO DirectManagersHistory (
          EmployeeCode, ChangeType, OldData, ChangedBy, Reason
        ) VALUES (
          @employeeCode, 'DELETE', @oldData, @deletedBy, 'حذف بيانات المدير المباشر'
        )
      `);

    return NextResponse.json({
      success: true,
      message: 'تم حذف بيانات المدير المباشر بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
