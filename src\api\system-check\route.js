async function handler() {
  try {
    const startTime = Date.now();

    const dbCheck = await sql`SELECT 1 as connection_test`;

    const endTime = Date.now();
    const responseTime = endTime - startTime;

    return {
      status: 'healthy',
      database: {
        connected: dbCheck.length > 0 && dbCheck[0].connection_test === 1,
        responseTime: `${responseTime}ms`,
      },
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      database: {
        connected: false,
        error: error.message,
      },
      timestamp: new Date().toISOString(),
    };
  }
}
