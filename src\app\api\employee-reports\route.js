import { NextResponse } from 'next/server';
import { getConnection } from '@/lib/db';

export async function POST(request) {
  try {
    const { reportType, filters } = await request.json();

    if (!reportType) {
      return NextResponse.json({
        success: false,
        error: 'نوع التقرير مطلوب'
      }, { status: 400 });
    }

    const pool = await getConnection();

    let query = '';
    let whereConditions = [];
    let params = {};

    // بناء الاستعلام الأساسي
    const baseQuery = `
      SELECT
        e.EmployeeCode as employeeCode,
        e.EmployeeName as fullName,
        e.Department as department,
        e.Governorate as governorate,
        e.JobTitle as jobTitle,
        e.HireDate as hireDate,
        e.CurrentStatus as status,
        CASE WHEN e.SocialInsurance IS NOT NULL AND e.SocialInsurance != '' THEN 'مؤمن' ELSE 'غير مؤمن' END as socialInsurance,
        CASE WHEN e.MedicalInsurance IS NOT NULL AND e.MedicalInsurance != '' THEN 'مؤمن' ELSE 'غير مؤمن' END as medicalInsurance,
        CASE
          WHEN EXISTS (
            SELECT 1 FROM ApartmentBeneficiaries ab
            WHERE ab.EmployeeCode = e.EmployeeCode
            AND ab.IsActive = 1
            AND (ab.EndDate IS NULL OR ab.EndDate >= GETDATE())
          ) THEN 'سكن شركة'
          ELSE 'سكن خاص'
        END as housing,
        e.Education as qualification,
        e.Gender as gender,
        e.MaritalStatus as maritalStatus,
        e.BirthDate as birthDate,
        e.NationalID as nationalID,
        e.Mobile as phone,
        '' as address,
        e.Mserv as militaryService
      FROM Employees e
    `;

    // إضافة شروط الفلترة
    if (filters.department) {
      whereConditions.push('e.Department = @department');
      params.department = filters.department;
    }

    if (filters.governorate) {
      whereConditions.push('e.Governorate = @governorate');
      params.governorate = filters.governorate;
    }

    if (filters.status && filters.status !== 'all') {
      if (filters.status === 'active') {
        whereConditions.push('(e.CurrentStatus = @activeStatus1 OR e.CurrentStatus = @activeStatus2 OR e.CurrentStatus = @activeStatus3)');
        params.activeStatus1 = 'ساري';
        params.activeStatus2 = 'نشط';
        params.activeStatus3 = 'سارى';
      } else if (filters.status === 'inactive') {
        whereConditions.push('(e.CurrentStatus != @inactiveStatus1 AND e.CurrentStatus != @inactiveStatus2 AND e.CurrentStatus != @inactiveStatus3)');
        params.inactiveStatus1 = 'ساري';
        params.inactiveStatus2 = 'نشط';
        params.inactiveStatus3 = 'سارى';
      }
    }

    if (filters.searchTerm) {
      whereConditions.push('(e.EmployeeName LIKE @searchTerm OR e.EmployeeCode LIKE @searchTerm)');
      params.searchTerm = `%${filters.searchTerm}%`;
    }

    if (filters.dateFrom) {
      whereConditions.push('e.HireDate >= @dateFrom');
      params.dateFrom = filters.dateFrom;
    }

    if (filters.dateTo) {
      whereConditions.push('e.HireDate <= @dateTo');
      params.dateTo = filters.dateTo;
    }

    // بناء الاستعلام النهائي حسب نوع التقرير
    switch (reportType) {
      case 'complete-list':
        query = baseQuery;
        break;

      case 'by-department':
        query = baseQuery;
        break;

      case 'by-governorate':
        query = baseQuery;
        break;

      case 'new-hires':
        // الموظفين المعينين في آخر 6 أشهر
        whereConditions.push('e.HireDate >= DATEADD(MONTH, -6, GETDATE())');
        query = baseQuery;
        break;

      case 'departures':
        // الموظفين المغادرين
        whereConditions.push('(e.CurrentStatus = @resignStatus OR e.CurrentStatus = @transferStatus OR e.CurrentStatus = @terminatedStatus OR e.CurrentStatus = @resignStatus2)');
        params.resignStatus = 'استقالة';
        params.resignStatus2 = 'مستقيل';
        params.transferStatus = 'نقل';
        params.terminatedStatus = 'انتهاء خدمة';
        query = baseQuery;
        break;

      case 'insurance':
        query = baseQuery;
        break;

      case 'housing':
        query = baseQuery;
        break;

      case 'qualifications':
        query = baseQuery;
        break;

      case 'statistics':
        // للإحصائيات، نحتاج استعلام مختلف بدون subqueries في COUNT
        let statsQuery = `
          SELECT
            COUNT(*) as totalEmployees,
            COUNT(CASE WHEN CurrentStatus IN ('ساري', 'نشط', 'سارى') THEN 1 END) as activeEmployees,
            COUNT(CASE WHEN CurrentStatus NOT IN ('ساري', 'نشط', 'سارى') THEN 1 END) as inactiveEmployees,
            COUNT(CASE WHEN Gender = 'ذكر' THEN 1 END) as maleEmployees,
            COUNT(CASE WHEN Gender = 'أنثى' THEN 1 END) as femaleEmployees,
            COUNT(CASE WHEN SocialInsurance IS NOT NULL AND SocialInsurance != '' THEN 1 END) as socialInsured,
            COUNT(CASE WHEN MedicalInsurance IS NOT NULL AND MedicalInsurance != '' THEN 1 END) as medicalInsured
          FROM Employees e
        `;

        // إضافة شروط WHERE إذا وجدت
        if (whereConditions.length > 0) {
          statsQuery += ' WHERE ' + whereConditions.join(' AND ');
        }

        const statsRequest = pool.request();
        Object.keys(params).forEach(key => {
          statsRequest.input(key, params[key]);
        });

        const statsResult = await statsRequest.query(statsQuery);

        // حساب إحصائيات السكن بشكل منفصل
        let housingQuery = `
          SELECT
            COUNT(DISTINCT e.EmployeeCode) as companyHousing
          FROM Employees e
          INNER JOIN ApartmentBeneficiaries ab ON e.EmployeeCode = ab.EmployeeCode
          WHERE ab.IsActive = 1 AND (ab.EndDate IS NULL OR ab.EndDate >= GETDATE())
        `;

        if (whereConditions.length > 0) {
          housingQuery += ' AND ' + whereConditions.join(' AND ');
        }

        const housingRequest = pool.request();
        Object.keys(params).forEach(key => {
          housingRequest.input(key, params[key]);
        });

        const housingResult = await housingRequest.query(housingQuery);

        const finalStats = {
          ...statsResult.recordset[0],
          companyHousing: housingResult.recordset[0]?.companyHousing || 0,
          privateHousing: (statsResult.recordset[0]?.totalEmployees || 0) - (housingResult.recordset[0]?.companyHousing || 0)
        };

        return NextResponse.json({
          success: true,
          data: [],
          stats: finalStats
        });

      default:
        query = baseQuery;
    }

    // إضافة شروط WHERE إذا وجدت
    if (whereConditions.length > 0) {
      query += ' WHERE ' + whereConditions.join(' AND ');
    }

    // إضافة ترتيب حسب نوع التقرير
    if (!query.includes('ORDER BY')) {
      switch (reportType) {
        case 'by-department':
          query += ' ORDER BY e.Department, e.EmployeeName';
          break;
        case 'by-governorate':
          query += ' ORDER BY e.Governorate, e.EmployeeName';
          break;
        case 'new-hires':
          query += ' ORDER BY e.HireDate DESC';
          break;
        case 'departures':
          query += ' ORDER BY e.EmployeeName';
          break;
        case 'qualifications':
          query += ' ORDER BY e.Education, e.EmployeeName';
          break;
        default:
          query += ' ORDER BY CAST(e.EmployeeCode AS INT)';
      }
    }

    // تنفيذ الاستعلام
    const queryRequest = pool.request();
    Object.keys(params).forEach(key => {
      queryRequest.input(key, params[key]);
    });

    const result = await queryRequest.query(query);

    // معالجة البيانات وتحويل الحالة
    const data = result.recordset.map(emp => ({
      ...emp,
      id: emp.employeeCode,
      status: (emp.status === 'ساري' || emp.status === 'نشط' || emp.status === 'سارى') ? 'نشط' : 'غير نشط'
    }));

    // حساب إحصائيات إضافية
    const stats = {
      totalEmployees: data.length,
      activeEmployees: data.filter(emp => emp.status === 'نشط').length,
      inactiveEmployees: data.filter(emp => emp.status === 'غير نشط').length,
      maleEmployees: data.filter(emp => emp.gender === 'ذكر').length,
      femaleEmployees: data.filter(emp => emp.gender === 'أنثى').length,
      socialInsured: data.filter(emp => emp.socialInsurance === 'مؤمن').length,
      medicalInsured: data.filter(emp => emp.medicalInsurance === 'مؤمن').length,
      companyHousing: data.filter(emp => emp.housing === 'سكن شركة').length,
      privateHousing: data.filter(emp => emp.housing === 'سكن خاص').length
    };

    return NextResponse.json({
      success: true,
      data: data,
      stats: stats,
      reportType: reportType,
      filters: filters
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب تقرير الموظفين',
      details: error.message
    }, { status: 500 });
  }
}

export async function GET(request) {
  try {
    const pool = await getConnection();

    // جلب إحصائيات سريعة للتقارير
    const statsQuery = `
      SELECT
        COUNT(*) as totalEmployees,
        COUNT(CASE WHEN CurrentStatus IN ('ساري', 'نشط', 'سارى') THEN 1 END) as activeEmployees,
        COUNT(CASE WHEN CurrentStatus NOT IN ('ساري', 'نشط', 'سارى') THEN 1 END) as inactiveEmployees,
        COUNT(DISTINCT Department) as totalDepartments,
        COUNT(DISTINCT Governorate) as totalGovernorates,
        COUNT(CASE WHEN HireDate >= DATEADD(MONTH, -1, GETDATE()) THEN 1 END) as newHiresThisMonth,
        COUNT(CASE WHEN SocialInsurance IS NOT NULL AND SocialInsurance != '' THEN 1 END) as socialInsured,
        COUNT(CASE WHEN MedicalInsurance IS NOT NULL AND MedicalInsurance != '' THEN 1 END) as medicalInsured
      FROM Employees
    `;

    const result = await pool.request().query(statsQuery);

    return NextResponse.json({
      success: true,
      stats: result.recordset[0]
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب الإحصائيات',
      details: error.message
    }, { status: 500 });
  }
}
