'use client';

import { useState } from 'react';
import ExcelUpload from '@/components/excel-upload';

export default function ImportEmployees() {
  const [uploadStatus, setUploadStatus] = useState(null);
  const [lang] = useState('ar');
  const [isDownloading, setIsDownloading] = useState(false);

  const handleUploadComplete = (result) => {
    setUploadStatus(result);
  };

  // تحميل قالب Excel
  const downloadTemplate = async () => {
    try {
      setIsDownloading(true);

      const response = await fetch('/api/download-employee-template');
      if (!response.ok) {
        throw new Error('فشل في تحميل القالب');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `employee_template_${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

    } catch (error) {
      alert('فشل في تحميل القالب. يرجى المحاولة مرة أخرى.');
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      {/* العنوان الرئيسي */}
      <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div className="flex items-center gap-3 mb-4">
          <span className="text-3xl">📋</span>
          <div>
            <h1 className="text-3xl font-bold text-gray-800">
              {lang === 'ar' ? 'استيراد بيانات الموظفين' : 'Import Employee Data'}
            </h1>
            <p className="text-gray-600 mt-1">
              {lang === 'ar' ? 'رفع بيانات الموظفين من ملف Excel' : 'Upload employee data from Excel file'}
            </p>
          </div>
        </div>

        {/* تحميل القالب */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-start gap-3">
            <span className="text-2xl">📥</span>
            <div className="flex-1">
              <h3 className="font-semibold text-blue-800 mb-2">
                {lang === 'ar' ? 'تحميل قالب Excel' : 'Download Excel Template'}
              </h3>
              <p className="text-blue-700 text-sm mb-3">
                {lang === 'ar'
                  ? 'قم بتحميل القالب المحدث أولاً، ثم املأ البيانات وارفع الملف'
                  : 'Download the updated template first, fill in the data, then upload the file'
                }
              </p>
              <button
                onClick={downloadTemplate}
                disabled={isDownloading}
                className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
              >
                {isDownloading ? (
                  <>
                    <span className="animate-spin">⏳</span>
                    {lang === 'ar' ? 'جاري التحميل...' : 'Downloading...'}
                  </>
                ) : (
                  <>
                    <span>📥</span>
                    {lang === 'ar' ? 'تحميل القالب' : 'Download Template'}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* ملاحظات مهمة */}
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6">
          <div className="flex items-start gap-3">
            <span className="text-2xl">⚠️</span>
            <div>
              <h3 className="font-semibold text-amber-800 mb-2">
                {lang === 'ar' ? 'ملاحظات مهمة' : 'Important Notes'}
              </h3>
              <ul className="text-amber-700 text-sm space-y-1">
                <li>• {lang === 'ar' ? 'تم فصل بيانات الشقق والسيارات عن جدول الموظفين' : 'Apartment and car data separated from employee table'}</li>
                <li>• {lang === 'ar' ? 'المغترب ≠ المقيم في شقة الشركة (مفهومان منفصلان)' : 'Expatriate ≠ Company housing resident (separate concepts)'}</li>
                <li>• {lang === 'ar' ? 'احذف الصف المثال قبل إدراج البيانات الحقيقية' : 'Delete example row before entering real data'}</li>
                <li>• {lang === 'ar' ? 'الحقول الحمراء مطلوبة، الحقول الزرقاء اختيارية' : 'Red fields required, blue fields optional'}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* منطقة الرفع */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
          <span>📤</span>
          {lang === 'ar' ? 'رفع ملف Excel' : 'Upload Excel File'}
        </h2>

        <ExcelUpload onUpload={handleUploadComplete} lang={lang} />

        {uploadStatus && (
          <div className={`mt-6 p-4 rounded-lg ${uploadStatus.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
            <div className="flex items-start gap-3">
              <span className="text-2xl">{uploadStatus.success ? '✅' : '❌'}</span>
              <div>
                <h3 className={`font-semibold mb-2 ${uploadStatus.success ? 'text-green-800' : 'text-red-800'}`}>
                  {uploadStatus.success
                    ? (lang === 'ar' ? 'تم الرفع بنجاح' : 'Upload Successful')
                    : (lang === 'ar' ? 'فشل في الرفع' : 'Upload Failed')
                  }
                </h3>
                <p className={`text-sm ${uploadStatus.success ? 'text-green-700' : 'text-red-700'}`}>
                  {uploadStatus.message || uploadStatus.error}
                </p>

                {/* عرض تفاصيل النتائج */}
                {uploadStatus.success && uploadStatus.summary && (
                  <div className="mt-3 text-sm text-green-700">
                    <p>📊 {lang === 'ar' ? 'ملخص النتائج:' : 'Results Summary:'}</p>
                    <ul className="mt-1 space-y-1">
                      <li>• {lang === 'ar' ? 'إجمالي:' : 'Total:'} {uploadStatus.summary.total}</li>
                      <li>• {lang === 'ar' ? 'نجح:' : 'Success:'} {uploadStatus.summary.success}</li>
                      <li>• {lang === 'ar' ? 'فشل:' : 'Failed:'} {uploadStatus.summary.failed}</li>
                      <li>• {lang === 'ar' ? 'جديد:' : 'Added:'} {uploadStatus.summary.added}</li>
                      <li>• {lang === 'ar' ? 'محدث:' : 'Updated:'} {uploadStatus.summary.updated}</li>
                    </ul>
                  </div>
                )}

                {/* عرض الأخطاء */}
                {uploadStatus.errors && uploadStatus.errors.length > 0 && (
                  <div className="mt-3">
                    <p className="text-sm font-semibold text-red-800 mb-2">
                      {lang === 'ar' ? 'الأخطاء:' : 'Errors:'}
                    </p>
                    <ul className="text-xs text-red-700 space-y-1 max-h-32 overflow-y-auto">
                      {uploadStatus.errors.slice(0, 10).map((error, index) => (
                        <li key={index}>• {error}</li>
                      ))}
                      {uploadStatus.errors.length > 10 && (
                        <li>... و {uploadStatus.errors.length - 10} خطأ آخر</li>
                      )}
                    </ul>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* الخطوات التالية */}
      <div className="bg-white rounded-lg shadow-sm p-6 mt-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
          <span>🎯</span>
          {lang === 'ar' ? 'الخطوات التالية' : 'Next Steps'}
        </h2>
        <div className="grid md:grid-cols-2 gap-4">
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-800 mb-2">
              🏠 {lang === 'ar' ? 'إدارة الشقق' : 'Apartment Management'}
            </h3>
            <p className="text-sm text-gray-600">
              {lang === 'ar'
                ? 'أضف المستفيدين من الشقق من خلال نموذج إدارة الشقق المنفصل'
                : 'Add apartment beneficiaries through separate apartment management form'
              }
            </p>
          </div>
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-800 mb-2">
              🚗 {lang === 'ar' ? 'إدارة السيارات' : 'Car Management'}
            </h3>
            <p className="text-sm text-gray-600">
              {lang === 'ar'
                ? 'أضف المستفيدين من السيارات من خلال نموذج إدارة السيارات المنفصل'
                : 'Add car beneficiaries through separate car management form'
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}