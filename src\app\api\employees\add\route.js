import { NextResponse } from 'next/server';
import sql from 'mssql';

// إعدادات قاعدة البيانات
const config = {
  server: process.env.DB_SERVER || 'localhost',
  database: process.env.DB_NAME || 'EmployeeDB',
  user: process.env.DB_USER || 'sa',
  password: process.env.DB_PASSWORD || 'YourPassword123',
  options: {
    encrypt: false,
    trustServerCertificate: true,
    enableArithAbort: true,
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
};

export async function POST(request) {
  let pool;

  try {

    // الحصول على البيانات من الطلب
    const employeeData = await request.json();

    // التحقق من الحقول المطلوبة
    const requiredFields = [
      'EmployeeName', 'JobTitle', 'Department', 'NationalID'
    ];

    const missingFields = requiredFields.filter(field =>
      !employeeData[field] || employeeData[field].trim() === ''
    );

    if (missingFields.length > 0) {
      return NextResponse.json({
        success: false,
        message: `الحقول التالية مطلوبة: ${missingFields.join(', ')}`
      }, { status: 400 });
    }

    // التحقق من صحة الرقم القومي
    if (employeeData.NationalID && employeeData.NationalID.length !== 14) {
      return NextResponse.json({
        success: false,
        message: 'الرقم القومي يجب أن يكون 14 رقم'
      }, { status: 400 });
    }

    // الاتصال بقاعدة البيانات
    pool = await sql.connect(config);

    // التحقق من عدم تكرار الرقم القومي
    const checkRequest = pool.request();
    checkRequest.input('NationalID', sql.NVarChar, employeeData.NationalID);

    const existingEmployee = await checkRequest.query(`
      SELECT EmployeeID FROM Employees WHERE NationalID = @NationalID
    `);

    if (existingEmployee.recordset.length > 0) {
      return NextResponse.json({
        success: false,
        message: 'يوجد موظف آخر بنفس الرقم القومي'
      }, { status: 400 });
    }

    // إنشاء طلب الإدراج
    const request = pool.request();

    // إضافة المعاملات
    request.input('EmployeeName', sql.NVarChar, employeeData.EmployeeName);
    request.input('JobTitle', sql.NVarChar, employeeData.JobTitle);
    request.input('Department', sql.NVarChar, employeeData.Department);
    request.input('NationalID', sql.NVarChar, employeeData.NationalID);
    request.input('BirthDate', sql.Date, employeeData.BirthDate ? new Date(employeeData.BirthDate) : null);
    request.input('Gender', sql.NVarChar, employeeData.Gender || null);
    request.input('Governorate', sql.NVarChar, employeeData.Governorate || null);
    request.input('Area', sql.NVarChar, employeeData.Area || null);
    request.input('MaritalStatus', sql.NVarChar, employeeData.MaritalStatus || null);
    request.input('Mobile', sql.NVarChar, employeeData.Mobile || null);
    request.input('Email', sql.NVarChar, employeeData.Email || null);
    request.input('HireDate', sql.Date, employeeData.HireDate ? new Date(employeeData.HireDate) : null);
    request.input('JoinDate', sql.Date, employeeData.JoinDate ? new Date(employeeData.JoinDate) : null);
    request.input('Education', sql.NVarChar, employeeData.Education || null);
    request.input('University', sql.NVarChar, employeeData.University || null);
    request.input('Major', sql.NVarChar, employeeData.Major || null);
    request.input('Grade', sql.NVarChar, employeeData.Grade || null);
    request.input('Batch', sql.NVarChar, employeeData.Batch || null);

    // استعلام الإدراج
    const insertQuery = `
      INSERT INTO Employees (
        EmployeeName, JobTitle, Department, NationalID, BirthDate, Gender,
        Governorate, area, MaritalStatus, Mobile, email, HireDate, JoinDate,
        Education, University, Major, Grade, Batch, CurrentStatus
      ) VALUES (
        @EmployeeName, @JobTitle, @Department, @NationalID, @BirthDate, @Gender,
        @Governorate, @Area, @MaritalStatus, @Mobile, @Email, @HireDate, @JoinDate,
        @Education, @University, @Major, @Grade, @Batch, 'ساري'
      );
      SELECT SCOPE_IDENTITY() as EmployeeCode;
    `;

    const result = await request.query(insertQuery);
    const newEmployeeCode = result.recordset[0].EmployeeCode;

    return NextResponse.json({
      success: true,
      message: 'تم إضافة الموظف بنجاح',
      employeeCode: newEmployeeCode
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      message: 'خطأ في إضافة الموظف',
      error: error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      await pool.close();

    }
  }
}
