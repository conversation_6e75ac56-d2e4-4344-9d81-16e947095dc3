'use client';

import React, { useState } from 'react';
import { useTheme } from '@/contexts/ThemeContext';
import { useLanguage } from '@/contexts/LanguageContext';
import MainLayout from '@/components/MainLayout';
import { 
  Upload, 
  FileSpreadsheet, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Download,
  RefreshCw,
  Eye,
  Trash2
} from 'lucide-react';

export default function UploadDailyAttendancePage() {
  const { isDarkMode } = useTheme();
  const { isRTL, isArabic } = useLanguage();
  
  const [selectedFile, setSelectedFile] = useState(null);
  const [uploading, setUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState(null);
  const [previewData, setPreviewData] = useState([]);
  const [validationErrors, setValidationErrors] = useState([]);

  // معالجة اختيار الملف
  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
        alert('يرجى اختيار ملف Excel صحيح (.xlsx أو .xls)');
        return;
      }
      
      if (file.size > 10 * 1024 * 1024) { // 10MB
        alert('حجم الملف يجب أن يكون أقل من 10 ميجابايت');
        return;
      }

      setSelectedFile(file);
      setUploadResult(null);
      setValidationErrors([]);
      previewFile(file);
    }
  };

  // معاينة الملف
  const previewFile = async (file) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('preview', 'true');

      const response = await fetch('/api/upload/daily-attendance', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();
      
      if (result.success) {
        setPreviewData(result.preview || []);
        setValidationErrors(result.validationErrors || []);
      } else {
        alert('خطأ في معاينة الملف: ' + result.error);
      }
    } catch (error) {

      alert('خطأ في معاينة الملف');
    }
  };

  // رفع الملف
  const uploadFile = async () => {
    if (!selectedFile) {
      alert('يرجى اختيار ملف أولاً');
      return;
    }

    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', selectedFile);

      const response = await fetch('/api/upload/daily-attendance', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();
      setUploadResult(result);
      
      if (result.success) {
        setSelectedFile(null);
        setPreviewData([]);
        setValidationErrors([]);
        // إعادة تعيين input الملف
        const fileInput = document.getElementById('file-input');
        if (fileInput) fileInput.value = '';
      }
    } catch (error) {

      setUploadResult({
        success: false,
        error: 'خطأ في رفع الملف'
      });
    } finally {
      setUploading(false);
    }
  };

  // تحميل النموذج
  const downloadTemplate = async () => {
    try {
      const response = await fetch('/api/templates/download/daily-attendance');
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'daily_attendance_template.xlsx';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        alert('خطأ في تحميل النموذج');
      }
    } catch (error) {

      alert('خطأ في تحميل النموذج');
    }
  };

  // إزالة الملف المحدد
  const removeFile = () => {
    setSelectedFile(null);
    setPreviewData([]);
    setValidationErrors([]);
    setUploadResult(null);
    const fileInput = document.getElementById('file-input');
    if (fileInput) fileInput.value = '';
  };

  return (
    <MainLayout>
      <div className={`p-6 ${isDarkMode ? 'bg-[#0f172a] text-white' : 'bg-gray-50 text-gray-900'}`}>
        {/* رأس الصفحة */}
        <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm p-6 mb-6`}>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <Clock className="h-8 w-8 text-blue-600" />
              <div>
                <h1 className="text-2xl font-bold">رفع ملف التمام اليومي</h1>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  رفع بيانات الحضور والغياب اليومي للموظفين من ملف Excel
                </p>
              </div>
            </div>
            
            <button
              onClick={downloadTemplate}
              className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
            >
              <Download className="h-4 w-4" />
              تحميل النموذج
            </button>
          </div>

          {/* تعليمات سريعة */}
          <div className={`p-4 rounded-lg border-l-4 border-blue-500 ${
            isDarkMode ? 'bg-blue-900/20 border-blue-400' : 'bg-blue-50 border-blue-500'
          }`}>
            <h3 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">تعليمات الرفع:</h3>
            <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
              <li>• حمل النموذج أولاً وامل البيانات</li>
              <li>• تأكد من صحة أكواد الموظفين والتواريخ</li>
              <li>• استخدم أنواع الحضور المحددة: حضور، غياب، إجازة، مرضي، مأمورية</li>
              <li>• احفظ الملف بصيغة Excel (.xlsx)</li>
            </ul>
          </div>
        </div>

        {/* منطقة رفع الملف */}
        <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm p-6 mb-6`}>
          <h3 className="text-lg font-semibold mb-4">اختيار الملف</h3>
          
          {!selectedFile ? (
            <div className={`border-2 border-dashed rounded-lg p-8 text-center ${
              isDarkMode ? 'border-gray-600' : 'border-gray-300'
            }`}>
              <FileSpreadsheet className="mx-auto h-16 w-16 text-gray-400 mb-4" />
              <div className="space-y-2">
                <p className="text-lg font-medium">اختر ملف Excel للرفع</p>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  يدعم ملفات .xlsx و .xls حتى 10MB
                </p>
              </div>
              
              <div className="mt-6">
                <label className="cursor-pointer">
                  <span className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg inline-flex items-center gap-2 transition-colors">
                    <Upload className="h-5 w-5" />
                    اختيار ملف
                  </span>
                  <input
                    id="file-input"
                    type="file"
                    accept=".xlsx,.xls"
                    onChange={handleFileSelect}
                    className="hidden"
                  />
                </label>
              </div>
            </div>
          ) : (
            <div className={`border rounded-lg p-4 ${
              isDarkMode ? 'border-gray-600 bg-gray-800' : 'border-gray-300 bg-gray-50'
            }`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <FileSpreadsheet className="h-8 w-8 text-green-600" />
                  <div>
                    <p className="font-medium">{selectedFile.name}</p>
                    <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                      {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <button
                    onClick={uploadFile}
                    disabled={uploading || validationErrors.length > 0}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {uploading ? (
                      <>
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        جاري الرفع...
                      </>
                    ) : (
                      <>
                        <Upload className="h-4 w-4" />
                        رفع الملف
                      </>
                    )}
                  </button>
                  
                  <button
                    onClick={removeFile}
                    className={`px-3 py-2 rounded-lg transition-colors ${
                      isDarkMode 
                        ? 'bg-gray-700 hover:bg-gray-600 text-white' 
                        : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                    }`}
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* أخطاء التحقق */}
        {validationErrors.length > 0 && (
          <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm p-6 mb-6`}>
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2 text-red-600">
              <XCircle className="h-5 w-5" />
              أخطاء في البيانات ({validationErrors.length})
            </h3>
            
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {validationErrors.map((error, index) => (
                <div key={index} className={`p-3 rounded-lg border-l-4 border-red-500 ${
                  isDarkMode ? 'bg-red-900/20' : 'bg-red-50'
                }`}>
                  <p className="text-sm text-red-700 dark:text-red-300">
                    <span className="font-medium">الصف {error.row}:</span> {error.message}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* معاينة البيانات */}
        {previewData.length > 0 && (
          <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm overflow-hidden mb-6`}>
            <div className="p-4 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Eye className="h-5 w-5" />
                معاينة البيانات ({previewData.length} سجل)
              </h3>
            </div>
            
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className={`${isDarkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
                  <tr>
                    <th className="px-4 py-3 text-right text-xs font-medium uppercase">كود الموظف</th>
                    <th className="px-4 py-3 text-right text-xs font-medium uppercase">اسم الموظف</th>
                    <th className="px-4 py-3 text-right text-xs font-medium uppercase">القسم</th>
                    <th className="px-4 py-3 text-center text-xs font-medium uppercase">التاريخ</th>
                    <th className="px-4 py-3 text-center text-xs font-medium uppercase">نوع الحضور</th>
                    <th className="px-4 py-3 text-center text-xs font-medium uppercase">وقت الدخول</th>
                    <th className="px-4 py-3 text-center text-xs font-medium uppercase">وقت الخروج</th>
                    <th className="px-4 py-3 text-right text-xs font-medium uppercase">ملاحظات</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                  {previewData.slice(0, 10).map((row, index) => (
                    <tr key={index} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td className="px-4 py-3 text-sm font-medium">{row.employeeCode}</td>
                      <td className="px-4 py-3 text-sm">{row.employeeName}</td>
                      <td className="px-4 py-3 text-sm">{row.department}</td>
                      <td className="px-4 py-3 text-center text-sm">{row.date}</td>
                      <td className="px-4 py-3 text-center">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          row.attendanceType === 'حضور' 
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : row.attendanceType === 'غياب'
                            ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                        }`}>
                          {row.attendanceType}
                        </span>
                      </td>
                      <td className="px-4 py-3 text-center text-sm">{row.checkIn || '-'}</td>
                      <td className="px-4 py-3 text-center text-sm">{row.checkOut || '-'}</td>
                      <td className="px-4 py-3 text-sm">{row.notes || '-'}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
              
              {previewData.length > 10 && (
                <div className="p-4 text-center text-sm text-gray-500">
                  عرض أول 10 سجلات فقط من إجمالي {previewData.length} سجل
                </div>
              )}
            </div>
          </div>
        )}

        {/* نتيجة الرفع */}
        {uploadResult && (
          <div className={`${isDarkMode ? 'bg-[#1f2937]' : 'bg-white'} rounded-lg shadow-sm p-6`}>
            <div className="flex items-center gap-3 mb-4">
              {uploadResult.success ? (
                <CheckCircle className="h-8 w-8 text-green-600" />
              ) : (
                <XCircle className="h-8 w-8 text-red-600" />
              )}
              <div>
                <h3 className={`text-lg font-semibold ${
                  uploadResult.success ? 'text-green-600' : 'text-red-600'
                }`}>
                  {uploadResult.success ? 'تم الرفع بنجاح!' : 'فشل في الرفع'}
                </h3>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {uploadResult.message || uploadResult.error}
                </p>
              </div>
            </div>

            {uploadResult.success && uploadResult.stats && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                <div className={`p-3 rounded-lg ${isDarkMode ? 'bg-green-900/20' : 'bg-green-50'}`}>
                  <p className="text-sm text-green-600 font-medium">تم إدراجها</p>
                  <p className="text-lg font-bold text-green-700">{uploadResult.stats.inserted || 0}</p>
                </div>
                <div className={`p-3 rounded-lg ${isDarkMode ? 'bg-blue-900/20' : 'bg-blue-50'}`}>
                  <p className="text-sm text-blue-600 font-medium">تم تحديثها</p>
                  <p className="text-lg font-bold text-blue-700">{uploadResult.stats.updated || 0}</p>
                </div>
                <div className={`p-3 rounded-lg ${isDarkMode ? 'bg-red-900/20' : 'bg-red-50'}`}>
                  <p className="text-sm text-red-600 font-medium">أخطاء</p>
                  <p className="text-lg font-bold text-red-700">{uploadResult.stats.errors || 0}</p>
                </div>
                <div className={`p-3 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
                  <p className="text-sm text-gray-600 font-medium">إجمالي</p>
                  <p className="text-lg font-bold text-gray-700">{uploadResult.stats.total || 0}</p>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </MainLayout>
  );
}
