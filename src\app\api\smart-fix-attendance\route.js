import { NextResponse } from 'next/server';
import { getConnection } from '@/lib/db';

export async function GET() {
  return await executeSmartFix();
}

export async function POST() {
  return await executeSmartFix();
}

async function executeSmartFix() {
  try {

    const pool = await getConnection();

    // الخطوة 1: تحليل سجلات التمام اليومي للعثور على الإجازات المخفية

    const analysisResult = await pool.request().query(`
      SELECT
        EmployeeCode,
        EmployeeName,
        AttendanceDate,
        Attendance,
        Notes,
        MONTH(AttendanceDate) as Month,
        YEAR(AttendanceDate) as Year,
        CASE
          WHEN Attendance = N'حضور' AND (
            Notes LIKE N'%إجازة%' OR
            Notes LIKE N'%معتمد%' OR
            Notes LIKE N'%اعتيادية%' OR
            Notes LIKE N'%عارضة%' OR
            Notes LIKE N'%مرضية%'
          ) THEN 'NEEDS_FIX'
          WHEN Attendance LIKE N'%إجازة%' THEN 'ALREADY_CORRECT'
          ELSE 'NORMAL'
        END as Status
      FROM DailyAttendance
      WHERE AttendanceDate >= DATEADD(YEAR, -2, GETDATE()) -- آخر سنتين فقط
      ORDER BY EmployeeCode, AttendanceDate
    `);

    const records = analysisResult.recordset;

    // فلترة السجلات التي تحتاج إصلاح
    const recordsToFix = records.filter(r => r.Status === 'NEEDS_FIX');

    // الخطوة 2: إصلاح السجلات التي تحتاج تعديل
    let fixedRecords = 0;

    for (const record of recordsToFix) {
      try {
        // تحديد نوع الإجازة من الملاحظات
        let attendanceType = 'إجازة اعتيادية';

        if (record.Notes) {
          if (record.Notes.includes('عارضة')) {
            attendanceType = 'إجازة عارضة';
          } else if (record.Notes.includes('مرضية')) {
            attendanceType = 'إجازة مرضية';
          } else if (record.Notes.includes('اعتيادية')) {
            attendanceType = 'إجازة اعتيادية';
          }
        }

        // تحديث السجل
        await pool.request()
          .input('employeeCode', record.EmployeeCode)
          .input('date', record.AttendanceDate)
          .input('attendance', attendanceType)
          .input('notes', record.Notes || `تم تصحيح من حضور إلى ${attendanceType}`)
          .query(`
            UPDATE DailyAttendance
            SET
              Attendance = @attendance,
              Notes = @notes,
              UpdatedAt = GETDATE()
            WHERE EmployeeCode = @employeeCode AND AttendanceDate = @date
          `);

        fixedRecords++;

        if (fixedRecords <= 20) {
          console.log(`✅ ${record.EmployeeName} - ${record.AttendanceDate.toISOString().split('T')[0]} → ${attendanceType}`);
        } else if (fixedRecords === 21) {

        }

      } catch (fixError) {

      }
    }

    // الخطوة 3: إعادة بناء الملخص الشهري بالكامل

    // حذف الملخصات القديمة
    await pool.request().query(`DELETE FROM MonthlyAttendanceSummary`);

    // جلب جميع الموظفين والشهور
    const employeeMonthsResult = await pool.request().query(`
      SELECT DISTINCT
        da.EmployeeCode,
        da.EmployeeName,
        MONTH(da.AttendanceDate) as Month,
        YEAR(da.AttendanceDate) as Year
      FROM DailyAttendance da
      INNER JOIN Employees e ON da.EmployeeCode = e.EmployeeCode
      WHERE da.AttendanceDate >= DATEADD(YEAR, -2, GETDATE())
      ORDER BY da.EmployeeCode, Year, Month
    `);

    const employeeMonths = employeeMonthsResult.recordset;

    let processedSummaries = 0;

    for (const em of employeeMonths) {
      try {
        // حساب الإحصائيات لكل موظف في كل شهر
        const statsResult = await pool.request()
          .input('employeeCode', em.EmployeeCode)
          .input('month', em.Month)
          .input('year', em.Year)
          .query(`
            SELECT
              COUNT(*) as TotalWorkingDays,
              SUM(CASE WHEN Attendance = N'حضور' THEN 1 ELSE 0 END) as TotalPresent,
              SUM(CASE WHEN Attendance = N'غياب' THEN 1 ELSE 0 END) as TotalAbsent,
              SUM(CASE WHEN Attendance LIKE N'%إجازة%' THEN 1 ELSE 0 END) as TotalLeaves,
              SUM(CASE WHEN Attendance = N'مأمورية' THEN 1 ELSE 0 END) as TotalMissions,
              SUM(CASE WHEN Attendance LIKE N'%اعتيادية%' THEN 1 ELSE 0 END) as AnnualLeaves,
              SUM(CASE WHEN Attendance LIKE N'%عارضة%' THEN 1 ELSE 0 END) as CasualLeaves,
              SUM(CASE WHEN Attendance LIKE N'%مرضية%' THEN 1 ELSE 0 END) as SickLeaves
            FROM DailyAttendance
            WHERE EmployeeCode = @employeeCode
              AND MONTH(AttendanceDate) = @month
              AND YEAR(AttendanceDate) = @year
          `);

        if (statsResult.recordset.length > 0) {
          const stats = statsResult.recordset[0];

          // تجاهل الشهور التي لا تحتوي على أيام عمل
          if (stats.TotalWorkingDays === 0) continue;

          const attendancePercentage = stats.TotalPresent > 0
            ? (stats.TotalPresent / stats.TotalWorkingDays) * 100
            : 0;

          // إدراج الملخص الشهري
          await pool.request()
            .input('employeeCode', em.EmployeeCode)
            .input('employeeName', em.EmployeeName)
            .input('month', em.Month)
            .input('year', em.Year)
            .input('totalWorkingDays', stats.TotalWorkingDays)
            .input('totalPresent', stats.TotalPresent)
            .input('totalAbsent', stats.TotalAbsent)
            .input('totalLeaves', stats.TotalLeaves)
            .input('totalMissions', stats.TotalMissions || 0)
            .input('annualLeaves', stats.AnnualLeaves || 0)
            .input('casualLeaves', stats.CasualLeaves || 0)
            .input('sickLeaves', stats.SickLeaves || 0)
            .input('attendancePercentage', attendancePercentage)
            .query(`
              INSERT INTO MonthlyAttendanceSummary (
                EmployeeCode, EmployeeName, Department, JobTitle, Month, Year,
                TotalWorkingDays, TotalPresent, TotalAbsent, TotalLeaves, TotalMissions,
                TotalSickLeave, TotalUnpaidLeave, TotalNightShifts, AttendancePercentage
              )
              VALUES (
                @employeeCode, @employeeName, '', '', @month, @year,
                @totalWorkingDays, @totalPresent, @totalAbsent, @totalLeaves, @totalMissions,
                @sickLeaves, 0, 0, @attendancePercentage
              )
            `);

          processedSummaries++;
        }

      } catch (summaryError) {

      }
    }

    // الخطوة 4: إحصائيات نهائية
    const finalStatsResult = await pool.request().query(`
      SELECT
        COUNT(DISTINCT EmployeeCode) as UniqueEmployees,
        COUNT(*) as TotalSummaries,
        SUM(TotalLeaves) as TotalLeaveDays,
        SUM(TotalPresent) as TotalPresentDays,
        AVG(AttendancePercentage) as AvgAttendancePercentage,
        MIN(CAST(CONCAT(Year, '-', FORMAT(Month, '00'), '-01') AS DATE)) as EarliestDate,
        MAX(CAST(CONCAT(Year, '-', FORMAT(Month, '00'), '-01') AS DATE)) as LatestDate
      FROM MonthlyAttendanceSummary
    `);

    const finalStats = finalStatsResult.recordset[0];

    // عينة من النتائج للتحقق
    const sampleResult = await pool.request().query(`
      SELECT TOP 5
        EmployeeName,
        Month,
        Year,
        TotalWorkingDays,
        TotalPresent,
        TotalLeaves,
        AttendancePercentage
      FROM MonthlyAttendanceSummary
      WHERE TotalLeaves > 0
      ORDER BY Year DESC, Month DESC
    `);

    return NextResponse.json({
      success: true,
      message: 'تم الإصلاح الذكي للتمام الشهري بنجاح',
      data: {
        recordsAnalyzed: records.length,
        recordsFixed: fixedRecords,
        summariesCreated: processedSummaries,
        finalStats: {
          uniqueEmployees: finalStats.UniqueEmployees,
          totalSummaries: finalStats.TotalSummaries,
          totalLeaveDays: finalStats.TotalLeaveDays,
          totalPresentDays: finalStats.TotalPresentDays,
          avgAttendancePercentage: finalStats.AvgAttendancePercentage?.toFixed(2),
          dateRange: `${finalStats.EarliestDate?.toISOString().split('T')[0]} إلى ${finalStats.LatestDate?.toISOString().split('T')[0]}`
        },
        sampleResults: sampleResult.recordset
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message,
      details: 'فشل في الإصلاح الذكي للتمام الشهري'
    }, { status: 500 });
  }
}
