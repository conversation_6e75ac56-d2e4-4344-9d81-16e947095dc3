{"version": 1.1, "atDirectives": [{"name": "@tailwind", "description": "Use the @tailwind directive to insert Tailwind's `base`, `components`, `utilities`, and `screens` styles into your CSS.", "references": [{"name": "Tailwind Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#tailwind"}]}, {"name": "@apply", "description": "Use @apply to inline any existing utility classes into your own custom CSS.", "references": [{"name": "Tailwind Documentation", "url": "https://tailwindcss.com/docs/functions-and-directives#apply"}]}]}