import { NextResponse } from 'next/server';
import sql from 'mssql';

// إعدادات قاعدة البيانات
const dbConfig = {
  user: 'SA',
  password: 'admin@123',
  server: 'localhost\\DBOJESTA',
  database: 'EMP',
  options: {
    encrypt: false,
    trustServerCertificate: true,
    enableArithAbort: true,
    instanceName: 'DBOJESTA'
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
  requestTimeout: 30000,
  connectionTimeout: 30000,
};

// إنشاء جداول الهيكل الوظيفي
async function createTables(pool) {
  try {
    // جدول الأقسام والوحدات التنظيمية
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='OrganizationalUnits' AND xtype='U')
      BEGIN
        CREATE TABLE OrganizationalUnits (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          UnitName NVARCHAR(200) NOT NULL,
          UnitCode NVARCHAR(50) UNIQUE,
          ParentUnitID INT NULL,
          ManagerEmployeeCode NVARCHAR(20),
          ManagerName NVARCHAR(100),
          UnitLevel INT DEFAULT 1,
          UnitType NVARCHAR(50) DEFAULT N'قسم', -- قسم، إدارة، وحدة
          Description NVARCHAR(MAX),
          IsActive BIT DEFAULT 1,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE(),
          
          FOREIGN KEY (ParentUnitID) REFERENCES OrganizationalUnits(ID)
        )
      END
    `);

    // جدول ربط الموظفين بالأقسام
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='EmployeeUnits' AND xtype='U')
      BEGIN
        CREATE TABLE EmployeeUnits (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          EmployeeCode NVARCHAR(20) NOT NULL,
          UnitID INT NOT NULL,
          Position NVARCHAR(100),
          IsDirectManager BIT DEFAULT 0,
          AssignmentDate DATE DEFAULT GETDATE(),
          IsActive BIT DEFAULT 1,
          
          FOREIGN KEY (UnitID) REFERENCES OrganizationalUnits(ID),
          UNIQUE(EmployeeCode, UnitID)
        )
      END
    `);

    console.log('✅ تم إنشاء جداول الهيكل الوظيفي بنجاح');
  } catch (error) {
    console.error('❌ خطأ في إنشاء الجداول:', error);
    throw error;
  }
}

// بناء الشجرة الهرمية
function buildTree(units) {
  const unitMap = new Map();
  const rootUnits = [];

  // إنشاء خريطة للوحدات
  units.forEach(unit => {
    unitMap.set(unit.ID, {
      id: unit.ID,
      name: unit.UnitName,
      code: unit.UnitCode,
      managerCode: unit.ManagerEmployeeCode,
      managerName: unit.ManagerName,
      level: unit.UnitLevel,
      type: unit.UnitType,
      description: unit.Description,
      parentId: unit.ParentUnitID,
      employeeCount: unit.EmployeeCount || 0,
      children: []
    });
  });

  // بناء الشجرة
  unitMap.forEach(unit => {
    if (unit.parentId) {
      const parent = unitMap.get(unit.parentId);
      if (parent) {
        parent.children.push(unit);
      }
    } else {
      rootUnits.push(unit);
    }
  });

  return rootUnits;
}

// GET - جلب الهيكل الوظيفي
export async function GET(request) {
  let pool;
  
  try {
    pool = await sql.connect(dbConfig);
    
    // إنشاء الجداول إذا لم تكن موجودة
    await createTables(pool);

    // جلب جميع الوحدات مع عدد الموظفين
    const result = await pool.request().query(`
      SELECT 
        ou.ID,
        ou.UnitName,
        ou.UnitCode,
        ou.ParentUnitID,
        ou.ManagerEmployeeCode,
        ou.ManagerName,
        ou.UnitLevel,
        ou.UnitType,
        ou.Description,
        COUNT(eu.EmployeeCode) as EmployeeCount
      FROM OrganizationalUnits ou
      LEFT JOIN EmployeeUnits eu ON ou.ID = eu.UnitID AND eu.IsActive = 1
      WHERE ou.IsActive = 1
      GROUP BY ou.ID, ou.UnitName, ou.UnitCode, ou.ParentUnitID, 
               ou.ManagerEmployeeCode, ou.ManagerName, ou.UnitLevel, 
               ou.UnitType, ou.Description
      ORDER BY ou.UnitLevel, ou.UnitName
    `);

    const tree = buildTree(result.recordset);

    return NextResponse.json({
      success: true,
      data: tree,
      message: 'تم جلب الهيكل الوظيفي بنجاح'
    });

  } catch (error) {
    console.error('❌ خطأ في جلب الهيكل الوظيفي:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في جلب الهيكل الوظيفي: ' + error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      await pool.close();
    }
  }
}

// POST - إضافة وحدة تنظيمية جديدة
export async function POST(request) {
  let pool;
  
  try {
    const body = await request.json();
    const { 
      unitName, 
      unitCode, 
      parentUnitId, 
      managerEmployeeCode, 
      managerName, 
      unitType, 
      description 
    } = body;

    if (!unitName || !managerEmployeeCode || !managerName) {
      return NextResponse.json({
        success: false,
        error: 'اسم الوحدة وبيانات المدير مطلوبة'
      }, { status: 400 });
    }

    pool = await sql.connect(dbConfig);

    // تحديد مستوى الوحدة
    let unitLevel = 1;
    if (parentUnitId) {
      const parentResult = await pool.request()
        .input('parentId', parentUnitId)
        .query('SELECT UnitLevel FROM OrganizationalUnits WHERE ID = @parentId');
      
      if (parentResult.recordset.length > 0) {
        unitLevel = parentResult.recordset[0].UnitLevel + 1;
      }
    }

    // إضافة الوحدة الجديدة
    const result = await pool.request()
      .input('unitName', unitName)
      .input('unitCode', unitCode)
      .input('parentUnitId', parentUnitId)
      .input('managerEmployeeCode', managerEmployeeCode)
      .input('managerName', managerName)
      .input('unitLevel', unitLevel)
      .input('unitType', unitType || 'قسم')
      .input('description', description)
      .query(`
        INSERT INTO OrganizationalUnits (
          UnitName, UnitCode, ParentUnitID, ManagerEmployeeCode, 
          ManagerName, UnitLevel, UnitType, Description
        )
        OUTPUT INSERTED.ID
        VALUES (
          @unitName, @unitCode, @parentUnitId, @managerEmployeeCode,
          @managerName, @unitLevel, @unitType, @description
        )
      `);

    const newUnitId = result.recordset[0].ID;

    // إضافة المدير إلى جدول ربط الموظفين
    await pool.request()
      .input('employeeCode', managerEmployeeCode)
      .input('unitId', newUnitId)
      .input('position', 'مدير ' + unitType)
      .query(`
        INSERT INTO EmployeeUnits (EmployeeCode, UnitID, Position, IsDirectManager)
        VALUES (@employeeCode, @unitId, @position, 1)
      `);

    return NextResponse.json({
      success: true,
      data: { id: newUnitId },
      message: 'تم إضافة الوحدة التنظيمية بنجاح'
    });

  } catch (error) {
    console.error('❌ خطأ في إضافة الوحدة:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في إضافة الوحدة: ' + error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      await pool.close();
    }
  }
}

// PUT - تحديث وحدة تنظيمية
export async function PUT(request) {
  let pool;
  
  try {
    const body = await request.json();
    const { 
      id,
      unitName, 
      unitCode, 
      managerEmployeeCode, 
      managerName, 
      unitType, 
      description 
    } = body;

    if (!id || !unitName || !managerEmployeeCode || !managerName) {
      return NextResponse.json({
        success: false,
        error: 'معرف الوحدة واسمها وبيانات المدير مطلوبة'
      }, { status: 400 });
    }

    pool = await sql.connect(dbConfig);

    // تحديث الوحدة
    await pool.request()
      .input('id', id)
      .input('unitName', unitName)
      .input('unitCode', unitCode)
      .input('managerEmployeeCode', managerEmployeeCode)
      .input('managerName', managerName)
      .input('unitType', unitType)
      .input('description', description)
      .query(`
        UPDATE OrganizationalUnits 
        SET UnitName = @unitName,
            UnitCode = @unitCode,
            ManagerEmployeeCode = @managerEmployeeCode,
            ManagerName = @managerName,
            UnitType = @unitType,
            Description = @description,
            UpdatedAt = GETDATE()
        WHERE ID = @id
      `);

    // تحديث بيانات المدير في جدول الربط
    await pool.request()
      .input('id', id)
      .input('managerEmployeeCode', managerEmployeeCode)
      .input('position', 'مدير ' + unitType)
      .query(`
        UPDATE EmployeeUnits 
        SET EmployeeCode = @managerEmployeeCode,
            Position = @position
        WHERE UnitID = @id AND IsDirectManager = 1
      `);

    return NextResponse.json({
      success: true,
      message: 'تم تحديث الوحدة التنظيمية بنجاح'
    });

  } catch (error) {
    console.error('❌ خطأ في تحديث الوحدة:', error);
    return NextResponse.json({
      success: false,
      error: 'خطأ في تحديث الوحدة: ' + error.message
    }, { status: 500 });
  } finally {
    if (pool) {
      await pool.close();
    }
  }
}
