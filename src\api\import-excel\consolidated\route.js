import sql from 'mssql';
import { config, getConnection } from '@/lib/db';

export async function POST(req) {
  try {
    const { fileContent, lang = 'ar' } = await req.json();
    const pool = await getConnection();

    // تحويل محتوى الملف من Base64
    const buffer = Buffer.from(fileContent.split(',')[1], 'base64');
    const csvString = buffer.toString('utf-8');
    const rows = csvString
      .split('\n')
      .map(row => row.split(',').map(cell => cell.trim()))
      .filter(row => row.length > 1);

    const headers = rows[0];
    const values = rows.slice(1);

    // إعداد معاملة SQL
    const transaction = new sql.Transaction(pool);
    await transaction.begin();

    try {
      for (const row of values) {
        const employeeData = {};
        headers.forEach((header, index) => {
          employeeData[header] = row[index];
        });

        // إدراج البيانات في جدول الموظفين
        await transaction.request()          .input('employeeCode', sql.VarChar, employeeData.EmployeeID)
          .input('name', sql.NVarChar, employeeData.FullName)
          .input('position', sql.NVarChar, employeeData.JobTitle)
          .input('department', sql.NVarChar, employeeData.Department)
          .input('email', sql.VarChar, employeeData.email)
          .input('phone', sql.VarChar, employeeData.Mobile)
          .input('hireDate', sql.Date, new Date(employeeData.HireDate))
          .input('documentsPath', sql.NVarChar, employeeData.documents_path)
          .query(`            MERGE INTO Employees AS target
            USING (VALUES (@employeeCode)) AS source (EmployeeID)
            ON target.EmployeeID = source.EmployeeID
            WHEN MATCHED THEN
              UPDATE SET
                FullName = @name,
                JobTitle = @position,
                Department = @department,
                email = @email,
                Mobile = @phone,
                hire_date = @hireDate,
                documents_path = @documentsPath,
                updated_at = GETDATE()
            WHEN NOT MATCHED THEN
              INSERT (employee_code, name, position, department, email, phone, hire_date, documents_path, created_at, updated_at)
              VALUES (@employeeCode, @name, @position, @department, @email, @phone, @hireDate, @documentsPath, GETDATE(), GETDATE());
          `);
      }

      await transaction.commit();

      return Response.json({
        success: true,
        message: lang === 'ar' ? 'تم استيراد البيانات بنجاح' : 'Data imported successfully',
        count: values.length
      });

    } catch (error) {
      await transaction.rollback();
      throw error;
    }

  } catch (error) {
    return Response.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}