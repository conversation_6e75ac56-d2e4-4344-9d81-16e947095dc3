import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

// اكتشاف الأعمدة الصحيحة تلقائياً
async function discoverCorrectColumns(pool) {
  try {
    const sampleResult = await pool.request().query(`SELECT TOP 1 * FROM Employees`);
    if (sampleResult.recordset.length === 0) return { employeeCodeColumn: 'EmployeeCode', employeeNameColumn: 'EmployeeName' };

    const sampleRow = sampleResult.recordset[0];

    // استخدام الأسماء الصحيحة مباشرة
    return {
      employeeCodeColumn: 'EmployeeCode',
      employeeNameColumn: 'EmployeeName'
    };
  } catch (error) {
    return { employeeCodeColumn: 'EmployeeCode', employeeNameColumn: 'EmployeeName' };
  }
}

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'getAll';

    const pool = await getConnection();

    switch (action) {
      case 'getAll':
        return await getAllApartments(pool, {
          includeInactive: searchParams.get('includeInactive') === 'true',
          search: searchParams.get('search') || ''
        });
      case 'getById':
        return await getApartmentById(pool, {
          apartmentId: parseInt(searchParams.get('apartmentId'))
        });
      case 'getStatistics':
        return await getStatistics(pool);
      case 'setup':
        return await setupApartmentTables(pool);
      default:
        return await getAllApartments(pool, {});
    }
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  }
}

export async function POST(request) {
  try {
    const body = await request.json();
    const { action } = body;

    const pool = await getConnection();

    switch (action) {
      case 'create':
        return await createApartment(pool, body);
      case 'update':
        return await updateApartment(pool, body);
      case 'delete':
        return await deleteApartment(pool, body);
      case 'getAll':
        return await getAllApartments(pool, body);
      case 'getById':
        return await getApartmentById(pool, body);
      case 'addBeneficiary':
        return await addBeneficiary(pool, body);
      case 'removeBeneficiary':
        return await removeBeneficiary(pool, body);
      case 'getStatistics':
        return await getStatistics(pool);
      case 'setup':
        return await setupApartmentTables(pool);
      default:
        return NextResponse.json({
          success: false,
          error: 'إجراء غير صحيح'
        }, { status: 400 });
    }
  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في الخادم: ' + error.message
    }, { status: 500 });
  }
}

// إعداد جداول الشقق
async function setupApartmentTables(pool) {
  try {

    // 1. إنشاء جدول الشقق
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Apartments' AND xtype='U')
      BEGIN
        CREATE TABLE Apartments (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          ApartmentCode NVARCHAR(20) NOT NULL UNIQUE,
          LandlordName NVARCHAR(100) NOT NULL,
          Address NVARCHAR(500) NOT NULL,
          StartDate DATE NOT NULL,
          EndDate DATE,
          RentAmount DECIMAL(10,2) NOT NULL,
          InsuranceAmount DECIMAL(10,2) DEFAULT 0,
          CommissionAmount DECIMAL(10,2) DEFAULT 0,
          BacklogAmount DECIMAL(10,2) DEFAULT 0,
          Notes NVARCHAR(MAX),
          IsActive BIT DEFAULT 1,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE()
        )
        
        CREATE INDEX IX_Apartments_Code ON Apartments(ApartmentCode)
        CREATE INDEX IX_Apartments_Active ON Apartments(IsActive)
        CREATE INDEX IX_Apartments_Dates ON Apartments(StartDate, EndDate)
      END
    `);

    // 2. إنشاء جدول المستفيدين
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ApartmentBeneficiaries' AND xtype='U')
      BEGIN
        CREATE TABLE ApartmentBeneficiaries (
          ID INT IDENTITY(1,1) PRIMARY KEY,
          ApartmentID INT NOT NULL,
          EmployeeCode NVARCHAR(20) NOT NULL,
          EmployeeName NVARCHAR(100) NOT NULL,
          JobTitle NVARCHAR(100) NOT NULL,
          Department NVARCHAR(100),
          StartDate DATE NOT NULL,
          EndDate DATE,
          IsActive BIT DEFAULT 1,
          CreatedAt DATETIME DEFAULT GETDATE(),
          UpdatedAt DATETIME DEFAULT GETDATE()
        )
        
        CREATE INDEX IX_ApartmentBeneficiaries_ApartmentCode ON ApartmentBeneficiaries(ApartmentCode)
        CREATE INDEX IX_ApartmentBeneficiaries_EmployeeCode ON ApartmentBeneficiaries(EmployeeCode)
        CREATE INDEX IX_ApartmentBeneficiaries_Active ON ApartmentBeneficiaries(IsActive)
      END
    `);

    // تحديث أسماء الموظفين المفقودة
    await updateMissingEmployeeNames(pool);

    return NextResponse.json({
      success: true,
      message: 'تم إعداد جداول الشقق وتحديث أسماء الموظفين بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في إعداد جداول الشقق: ' + error.message
    }, { status: 500 });
  }
}

// إنشاء شقة جديدة
async function createApartment(pool, data) {
  try {
    await setupApartmentTables(pool);
    
    const {
      apartmentCode,
      landlordName,
      address,
      startDate,
      endDate,
      rentAmount,
      insuranceAmount = 0,
      commissionAmount = 0,
      backlogAmount = 0,
      notes,
      beneficiaries = []
    } = data;

    // التحقق من عدم تكرار كود الشقة
    const existingApartment = await pool.request()
      .input('apartmentCode', sql.NVarChar, apartmentCode)
      .query('SELECT ID FROM Apartments WHERE ApartmentCode = @apartmentCode');

    if (existingApartment.recordset.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'كود الشقة موجود مسبقاً'
      }, { status: 400 });
    }

    // إدراج الشقة
    const apartmentResult = await pool.request()
      .input('apartmentCode', sql.NVarChar, apartmentCode)
      .input('landlordName', sql.NVarChar, landlordName)
      .input('address', sql.NVarChar, address)
      .input('startDate', sql.Date, new Date(startDate))
      .input('endDate', sql.Date, endDate ? new Date(endDate) : null)
      .input('rentAmount', sql.Decimal(10, 2), rentAmount)
      .input('insuranceAmount', sql.Decimal(10, 2), insuranceAmount)
      .input('commissionAmount', sql.Decimal(10, 2), commissionAmount)
      .input('backlogAmount', sql.Decimal(10, 2), backlogAmount)
      .input('notes', sql.NVarChar, notes || null)
      .query(`
        INSERT INTO Apartments (
          ApartmentCode, LandlordName, Address, StartDate, EndDate,
          RentAmount, InsuranceAmount, CommissionAmount, BacklogAmount, Notes
        )
        OUTPUT INSERTED.ID
        VALUES (
          @apartmentCode, @landlordName, @address, @startDate, @endDate,
          @rentAmount, @insuranceAmount, @commissionAmount, @backlogAmount, @notes
        )
      `);

    const apartmentId = apartmentResult.recordset[0].ID;

    // إدراج المستفيدين
    for (const beneficiary of beneficiaries) {
      await addBeneficiaryToApartment(pool, apartmentId, beneficiary);
    }

    return NextResponse.json({
      success: true,
      message: 'تم إنشاء الشقة بنجاح',
      data: { apartmentId }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في إنشاء الشقة: ' + error.message
    }, { status: 500 });
  }
}

// إضافة مستفيد للشقة
async function addBeneficiaryToApartment(pool, apartmentId, beneficiary) {
  const { employeeCode, startDate } = beneficiary;

  // جلب كود الشقة من ID
  const apartmentResult = await pool.request()
    .input('apartmentId', sql.Int, apartmentId)
    .query('SELECT ApartmentCode FROM Apartments WHERE ID = @apartmentId');

  if (apartmentResult.recordset.length === 0) {
    throw new Error('الشقة غير موجودة');
  }

  const apartmentCode = apartmentResult.recordset[0].ApartmentCode;

  // جلب بيانات الموظف - محاولة عدة جداول
  let employeeResult;
  let employee;

  // التحقق من صحة كود الموظف
  if (!employeeCode || employeeCode.toString().trim() === '') {
    throw new Error('كود الموظف مطلوب ولا يمكن أن يكون فارغاً');
  }

  const cleanEmployeeCode = employeeCode.toString().trim();

  // اكتشاف الأعمدة الصحيحة
  const { employeeCodeColumn, employeeNameColumn } = await discoverCorrectColumns(pool);

  // البحث في جدول Employees باستخدام الأعمدة الصحيحة
  const searchQuery = `
    SELECT TOP 1
      EmployeeCode,
      EmployeeName,
      JobTitle,
      Department
    FROM Employees
    WHERE EmployeeCode = @employeeCode
       OR CAST(EmployeeCode AS NVARCHAR) = @employeeCode
       OR LTRIM(RTRIM(CAST(EmployeeCode AS NVARCHAR))) = @employeeCode
  `;

  // البحث في جدول Employees
  try {

    employeeResult = await pool.request()
      .input('employeeCode', sql.NVarChar, cleanEmployeeCode)
      .query(searchQuery);

    if (employeeResult.recordset.length > 0) {
      employee = employeeResult.recordset[0];
      console.log(`✅ تم العثور على الموظف: ${employee.EmployeeName} (${employee.EmployeeCode})`);
    } else {

    }
  } catch (error) {

  }

  // إذا لم يتم العثور على الموظف في أي جدول، رفض العملية
  if (!employee) {

    throw new Error(`كود الموظف ${employeeCode} غير موجود في قاعدة البيانات. يرجى التأكد من صحة الكود أو إضافة الموظف أولاً.`);
  }

  // التحقق من عدم وجود الموظف في نفس الشقة
  const existingBeneficiary = await pool.request()
    .input('apartmentCode', sql.NVarChar, apartmentCode)
    .input('employeeCode', sql.NVarChar, employeeCode)
    .query(`
      SELECT ID FROM ApartmentBeneficiaries
      WHERE ApartmentCode = @apartmentCode
        AND EmployeeCode = @employeeCode
        AND IsActive = 1
    `);

  if (existingBeneficiary.recordset.length > 0) {
    throw new Error(`الموظف ${employee.EmployeeName} مسجل بالفعل في هذه الشقة`);
  }

  // التحقق من صحة تاريخ البداية
  if (!startDate) {
    throw new Error('تاريخ بداية الاستفادة مطلوب');
  }

  const startDateObj = new Date(startDate);
  if (isNaN(startDateObj.getTime())) {
    throw new Error('تاريخ بداية الاستفادة غير صحيح');
  }

  // إدراج المستفيد مع معلومات كاملة ومحققة
  const insertResult = await pool.request()
    .input('apartmentCode', sql.NVarChar, apartmentCode)
    .input('employeeCode', sql.NVarChar, cleanEmployeeCode)
    .input('employeeName', sql.NVarChar, employee.EmployeeName || 'غير محدد')
    .input('jobTitle', sql.NVarChar, employee.JobTitle || 'غير محدد')
    .input('department', sql.NVarChar, employee.Department || 'غير محدد')
    .input('startDate', sql.Date, startDateObj)
    .query(`
      INSERT INTO ApartmentBeneficiaries (
        ApartmentCode, EmployeeCode, EmployeeName, JobTitle, Department,
        StartDate, IsActive, CreatedAt, UpdatedAt
      )
      OUTPUT INSERTED.ID
      VALUES (
        @apartmentCode, @employeeCode, @employeeName, @jobTitle, @department,
        @startDate, 1, GETDATE(), GETDATE()
      )
    `);

  const newBeneficiaryId = insertResult.recordset[0].ID;
  console.log(`✅ تم إضافة المستفيد بنجاح: ${employee.EmployeeName} (ID: ${newBeneficiaryId})`);

  // جلب بيانات الشقة لتحديث ملف الموظف
  const apartmentDetailsResult = await pool.request()
    .input('apartmentId', sql.Int, apartmentId)
    .query('SELECT ApartmentCode, LandlordName FROM Apartments WHERE ID = @apartmentId');

  if (apartmentDetailsResult.recordset.length > 0) {
    const apartment = apartmentDetailsResult.recordset[0];
    // تحديث بيانات السكن في ملف الموظف
    await updateEmployeeHousingData(pool, cleanEmployeeCode, apartment.ApartmentCode, apartment.LandlordName);
  }

  return newBeneficiaryId;
}

// تحديث بيانات الموظف في جدول Employees عند إضافة/إزالة من شقة
async function updateEmployeeHousingData(pool, employeeCode, apartmentCode = null, apartmentName = null) {
  try {

    if (apartmentCode) {
      // الموظف مسجل في شقة - تحديث البيانات
      await pool.request()
        .input('employeeCode', sql.NVarChar, employeeCode)
        .input('apartmentCode', sql.NVarChar, apartmentCode)
        .input('apartmentName', sql.NVarChar, apartmentName || '')
        .query(`
          UPDATE Employees
          SET
            CompanyHousing = 'نعم',
            codeHousing = @apartmentCode,
            UpdatedAt = GETDATE()
          WHERE EmployeeCode = @employeeCode
             OR CAST(EmployeeCode AS NVARCHAR) = @employeeCode
        `);

    } else {
      // الموظف تم إزالته من الشقة - مسح البيانات
      await pool.request()
        .input('employeeCode', sql.NVarChar, employeeCode)
        .query(`
          UPDATE Employees
          SET
            CompanyHousing = 'لا',
            codeHousing = NULL,
            UpdatedAt = GETDATE()
          WHERE EmployeeCode = @employeeCode
             OR CAST(EmployeeCode AS NVARCHAR) = @employeeCode
        `);

    }
  } catch (error) {

  }
}

// تحديث بيانات الموظف في جدول Employees عند إضافة/إزالة من مواصلات
async function updateEmployeeTransportData(pool, employeeCode, carCode = null, route = null) {
  try {

    if (carCode) {
      // الموظف مسجل في مواصلات - تحديث البيانات
      await pool.request()
        .input('employeeCode', sql.NVarChar, employeeCode)
        .input('carCode', sql.NVarChar, carCode)
        .input('transportMethod', sql.NVarChar, route ? `باص الشركة - ${route}` : 'باص الشركة')
        .query(`
          UPDATE Employees
          SET
            TransportMethod = @transportMethod,
            UpdatedAt = GETDATE()
          WHERE EmployeeCode = @employeeCode
             OR CAST(EmployeeCode AS NVARCHAR) = @employeeCode
        `);

    } else {
      // الموظف تم إزالته من المواصلات - مسح البيانات
      await pool.request()
        .input('employeeCode', sql.NVarChar, employeeCode)
        .query(`
          UPDATE Employees
          SET
            TransportMethod = NULL,
            UpdatedAt = GETDATE()
          WHERE EmployeeCode = @employeeCode
             OR CAST(EmployeeCode AS NVARCHAR) = @employeeCode
        `);

    }
  } catch (error) {

  }
}

// تحديث أسماء الموظفين المفقودة
async function updateMissingEmployeeNames(pool) {
  try {

    // جلب المستفيدين الذين لا يملكون أسماء
    const missingNamesResult = await pool.request().query(`
      SELECT DISTINCT ab.EmployeeCode
      FROM ApartmentBeneficiaries ab
      WHERE ab.EmployeeName IS NULL
         OR ab.EmployeeName = ''
         OR ab.EmployeeName = 'غير محدد'
         OR ab.EmployeeName = 'اسم غير محدد'
    `);

    for (const record of missingNamesResult.recordset) {
      const employeeCode = record.EmployeeCode;

      // البحث عن الموظف في جدول Employees
      const employeeResult = await pool.request()
        .input('employeeCode', sql.NVarChar, employeeCode)
        .query(`
          SELECT TOP 1
            EmployeeCode,
            EmployeeName,
            JobTitle,
            Department
          FROM Employees
          WHERE EmployeeCode = @employeeCode
             OR CAST(EmployeeCode AS NVARCHAR) = @employeeCode
             OR LTRIM(RTRIM(CAST(EmployeeCode AS NVARCHAR))) = @employeeCode
        `);

      if (employeeResult.recordset.length > 0) {
        const employee = employeeResult.recordset[0];

        // تحديث بيانات المستفيد
        await pool.request()
          .input('employeeCode', sql.NVarChar, employeeCode)
          .input('employeeName', sql.NVarChar, employee.EmployeeName || 'غير محدد')
          .input('jobTitle', sql.NVarChar, employee.JobTitle || 'غير محدد')
          .input('department', sql.NVarChar, employee.Department || 'غير محدد')
          .query(`
            UPDATE ApartmentBeneficiaries
            SET
              EmployeeName = @employeeName,
              JobTitle = @jobTitle,
              Department = @department,
              UpdatedAt = GETDATE()
            WHERE EmployeeCode = @employeeCode
          `);

        console.log(`✅ تم تحديث بيانات الموظف: ${employee.EmployeeName} (${employeeCode})`);
      } else {

      }
    }

  } catch (error) {

  }
}

// جلب جميع الشقق
async function getAllApartments(pool, data) {
  try {
    await setupApartmentTables(pool);

    // تحديث أسماء الموظفين المفقودة
    await updateMissingEmployeeNames(pool);

    const { includeInactive = false, search = '' } = data;

    let whereClause = 'WHERE 1=1';
    if (!includeInactive) {
      whereClause += ' AND a.IsActive = 1';
    }
    if (search) {
      whereClause += ` AND (a.ApartmentCode LIKE '%${search}%' OR a.LandlordName LIKE '%${search}%' OR a.Address LIKE '%${search}%')`;
    }

    const query = `
      SELECT 
        a.ID,
        a.ApartmentCode,
        a.LandlordName,
        a.Address,
        a.StartDate,
        a.EndDate,
        a.RentAmount,
        a.InsuranceAmount,
        a.CommissionAmount,
        a.BacklogAmount,
        a.Notes,
        a.IsActive,
        a.CreatedAt,
        
        -- عدد المستفيدين النشطين
        (SELECT COUNT(*) FROM ApartmentBeneficiaries ab
         WHERE ab.ApartmentCode = a.ApartmentCode AND ab.IsActive = 1) as ActiveBeneficiariesCount,
         
        -- إجمالي الإيجار المدفوع
        (a.RentAmount * DATEDIFF(MONTH, a.StartDate, ISNULL(a.EndDate, GETDATE()))) as TotalRentPaid,
        
        -- حالة انتهاء العقد
        CASE 
          WHEN a.EndDate IS NULL THEN N'مفتوح'
          WHEN a.EndDate > GETDATE() THEN N'ساري'
          ELSE N'منتهي'
        END as ContractStatus
        
      FROM Apartments a
      ${whereClause}
      ORDER BY a.CreatedAt DESC
    `;

    const result = await pool.request().query(query);

    // جلب المستفيدين لكل شقة
    for (let apartment of result.recordset) {

      const beneficiariesResult = await pool.request()
        .input('apartmentId', sql.Int, apartment.ID)
        .query(`
          SELECT
            ab.ID,
            ab.EmployeeCode,
            ab.EmployeeName,
            ab.JobTitle,
            ab.Department,
            ab.StartDate,
            ab.EndDate,
            ab.IsActive
          FROM ApartmentBeneficiaries ab
          INNER JOIN Apartments a ON ab.ApartmentCode = a.ApartmentCode
          WHERE a.ID = @apartmentId AND ab.IsActive = 1
          ORDER BY ab.IsActive DESC, ab.StartDate DESC
        `);

      apartment.beneficiaries = beneficiariesResult.recordset;
    }

    return NextResponse.json({
      success: true,
      data: result.recordset
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب الشقق: ' + error.message
    }, { status: 500 });
  }
}

// جلب شقة بالمعرف
async function getApartmentById(pool, data) {
  try {
    const { apartmentId } = data;

    const apartmentResult = await pool.request()
      .input('apartmentId', sql.Int, apartmentId)
      .query(`
        SELECT
          ID,
          ApartmentCode,
          LandlordName,
          Address,
          StartDate,
          EndDate,
          RentAmount,
          InsuranceAmount,
          CommissionAmount,
          BacklogAmount,
          Notes,
          IsActive,
          CreatedAt,
          UpdatedAt
        FROM Apartments
        WHERE ID = @apartmentId
      `);

    if (apartmentResult.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الشقة غير موجودة'
      }, { status: 404 });
    }

    const apartment = apartmentResult.recordset[0];

    // جلب المستفيدين
    const beneficiariesResult = await pool.request()
      .input('apartmentId', sql.Int, apartmentId)
      .query(`
        SELECT
          ab.ID,
          ab.EmployeeCode,
          ab.EmployeeName,
          ab.JobTitle,
          ab.Department,
          ab.StartDate,
          ab.EndDate,
          ab.IsActive
        FROM ApartmentBeneficiaries ab
        INNER JOIN Apartments a ON ab.ApartmentCode = a.ApartmentCode
        WHERE a.ID = @apartmentId AND ab.IsActive = 1
        ORDER BY ab.IsActive DESC, ab.StartDate DESC
      `);

    apartment.beneficiaries = beneficiariesResult.recordset;

    return NextResponse.json({
      success: true,
      data: apartment
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب الشقة: ' + error.message
    }, { status: 500 });
  }
}

// تحديث شقة
async function updateApartment(pool, data) {
  try {
    const {
      apartmentId,
      apartmentCode,
      landlordName,
      address,
      startDate,
      endDate,
      rentAmount,
      insuranceAmount,
      commissionAmount,
      backlogAmount,
      notes,
      isActive = true
    } = data;

    // التحقق من وجود الشقة
    const existingApartment = await pool.request()
      .input('apartmentId', sql.Int, apartmentId)
      .query('SELECT ID FROM Apartments WHERE ID = @apartmentId');

    if (existingApartment.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الشقة غير موجودة'
      }, { status: 404 });
    }

    // التحقق من عدم تكرار كود الشقة (إذا تم تغييره)
    const duplicateCheck = await pool.request()
      .input('apartmentCode', sql.NVarChar, apartmentCode)
      .input('apartmentId', sql.Int, apartmentId)
      .query('SELECT ID FROM Apartments WHERE ApartmentCode = @apartmentCode AND ID != @apartmentId');

    if (duplicateCheck.recordset.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'كود الشقة موجود مسبقاً'
      }, { status: 400 });
    }

    // تحديث الشقة
    await pool.request()
      .input('apartmentId', sql.Int, apartmentId)
      .input('apartmentCode', sql.NVarChar, apartmentCode)
      .input('landlordName', sql.NVarChar, landlordName)
      .input('address', sql.NVarChar, address)
      .input('startDate', sql.Date, new Date(startDate))
      .input('endDate', sql.Date, endDate ? new Date(endDate) : null)
      .input('rentAmount', sql.Decimal(10, 2), rentAmount)
      .input('insuranceAmount', sql.Decimal(10, 2), insuranceAmount)
      .input('commissionAmount', sql.Decimal(10, 2), commissionAmount)
      .input('backlogAmount', sql.Decimal(10, 2), backlogAmount)
      .input('notes', sql.NVarChar, notes || null)
      .input('isActive', sql.Bit, isActive)
      .query(`
        UPDATE Apartments SET
          ApartmentCode = @apartmentCode,
          LandlordName = @landlordName,
          Address = @address,
          StartDate = @startDate,
          EndDate = @endDate,
          RentAmount = @rentAmount,
          InsuranceAmount = @insuranceAmount,
          CommissionAmount = @commissionAmount,
          BacklogAmount = @backlogAmount,
          Notes = @notes,
          IsActive = @isActive,
          UpdatedAt = GETDATE()
        WHERE ID = @apartmentId
      `);

    return NextResponse.json({
      success: true,
      message: 'تم تحديث الشقة بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في تحديث الشقة: ' + error.message
    }, { status: 500 });
  }
}

// حذف شقة
async function deleteApartment(pool, data) {
  try {
    const { apartmentId } = data;

    // التحقق من وجود الشقة
    const existingApartment = await pool.request()
      .input('apartmentId', sql.Int, apartmentId)
      .query('SELECT ID FROM Apartments WHERE ID = @apartmentId');

    if (existingApartment.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الشقة غير موجودة'
      }, { status: 404 });
    }

    // حذف المستفيدين أولاً
    await pool.request()
      .input('apartmentId', sql.Int, apartmentId)
      .query(`
        DELETE ab FROM ApartmentBeneficiaries ab
        INNER JOIN Apartments a ON ab.ApartmentCode = a.ApartmentCode
        WHERE a.ID = @apartmentId
      `);

    // حذف الشقة
    await pool.request()
      .input('apartmentId', sql.Int, apartmentId)
      .query('DELETE FROM Apartments WHERE ID = @apartmentId');

    return NextResponse.json({
      success: true,
      message: 'تم حذف الشقة بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في حذف الشقة: ' + error.message
    }, { status: 500 });
  }
}

// إضافة مستفيد جديد
async function addBeneficiary(pool, data) {
  try {
    const { apartmentId, employeeCode, startDate, endDate } = data;

    await addBeneficiaryToApartment(pool, apartmentId, {
      employeeCode,
      startDate,
      endDate
    });

    return NextResponse.json({
      success: true,
      message: 'تم إضافة المستفيد بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في إضافة المستفيد: ' + error.message
    }, { status: 500 });
  }
}

// إزالة مستفيد
async function removeBeneficiary(pool, data) {
  try {
    const { beneficiaryId } = data;

    // التحقق من وجود المستفيد وجلب بياناته
    const existingBeneficiary = await pool.request()
      .input('beneficiaryId', sql.Int, beneficiaryId)
      .query('SELECT ID, EmployeeCode FROM ApartmentBeneficiaries WHERE ID = @beneficiaryId');

    if (existingBeneficiary.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'المستفيد غير موجود'
      }, { status: 404 });
    }

    const beneficiary = existingBeneficiary.recordset[0];

    // إزالة المستفيد (تعطيل بدلاً من حذف)
    await pool.request()
      .input('beneficiaryId', sql.Int, beneficiaryId)
      .query(`
        UPDATE ApartmentBeneficiaries
        SET IsActive = 0, EndDate = GETDATE(), UpdatedAt = GETDATE()
        WHERE ID = @beneficiaryId
      `);

    // تحديث بيانات السكن في ملف الموظف (مسح البيانات)
    await updateEmployeeHousingData(pool, beneficiary.EmployeeCode, null, null);

    return NextResponse.json({
      success: true,
      message: 'تم إزالة المستفيد بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في إزالة المستفيد: ' + error.message
    }, { status: 500 });
  }
}

// جلب الإحصائيات
async function getStatistics(pool) {
  try {
    await setupApartmentTables(pool);

    const statsResult = await pool.request().query(`
      SELECT
        -- إحصائيات عامة
        (SELECT COUNT(*) FROM Apartments WHERE IsActive = 1) as TotalActiveApartments,
        (SELECT COUNT(*) FROM Apartments WHERE IsActive = 0) as TotalInactiveApartments,
        (SELECT COUNT(*) FROM ApartmentBeneficiaries WHERE IsActive = 1) as TotalActiveBeneficiaries,

        -- إحصائيات مالية
        (SELECT SUM(RentAmount) FROM Apartments WHERE IsActive = 1) as TotalMonthlyRent,
        (SELECT SUM(InsuranceAmount) FROM Apartments WHERE IsActive = 1) as TotalInsurance,
        (SELECT SUM(CommissionAmount) FROM Apartments WHERE IsActive = 1) as TotalCommission,
        (SELECT SUM(BacklogAmount) FROM Apartments WHERE IsActive = 1) as TotalBacklog,

        -- الشقق التي تنتهي قريباً (خلال 30 يوم)
        (SELECT COUNT(*) FROM Apartments
         WHERE IsActive = 1
           AND EndDate IS NOT NULL
           AND EndDate <= DATEADD(DAY, 30, GETDATE())) as ApartmentsExpiringSoon,

        -- الشقق بدون مستفيدين
        (SELECT COUNT(*) FROM Apartments a
         WHERE a.IsActive = 1
           AND NOT EXISTS (
             SELECT 1 FROM ApartmentBeneficiaries ab
             WHERE ab.ApartmentCode = a.ApartmentCode AND ab.IsActive = 1
           )) as ApartmentsWithoutBeneficiaries
    `);

    const stats = statsResult.recordset[0];

    // إحصائيات إضافية
    const monthlyStatsResult = await pool.request().query(`
      SELECT
        YEAR(StartDate) as Year,
        MONTH(StartDate) as Month,
        COUNT(*) as NewApartments,
        SUM(RentAmount) as MonthlyRentTotal
      FROM Apartments
      WHERE StartDate >= DATEADD(MONTH, -12, GETDATE())
      GROUP BY YEAR(StartDate), MONTH(StartDate)
      ORDER BY Year DESC, Month DESC
    `);

    return NextResponse.json({
      success: true,
      data: {
        ...stats,
        monthlyStats: monthlyStatsResult.recordset
      }
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'فشل في جلب الإحصائيات: ' + error.message
    }, { status: 500 });
  }
}
