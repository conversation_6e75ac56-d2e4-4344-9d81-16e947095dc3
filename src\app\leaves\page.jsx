'use client';
import React, { useState } from 'react';

function MainComponent() {
  const [selectedLang, setSelectedLang] = useState('ar');
  const dir = selectedLang === 'ar' ? 'rtl' : 'ltr';

  return (
    <div
      dir={dir}
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          {selectedLang === 'ar' ? 'إدارة الإجازات' : 'Leave Management'}
        </h1>
        <button
          onClick={() => setSelectedLang(selectedLang === 'ar' ? 'en' : 'ar')}
          className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
        >
          {selectedLang === 'ar' ? 'English' : 'العربية'}
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <></>

        <></>

        <></>

        <></>
      </div>
    </div>
  );
}

export default MainComponent;
