async function handler({ path, method }) {
  if (method !== 'GET') {
    return {
      statusCode: 405,
      body: 'طريقة غير مسموح بها',
    };
  }

  // خريطة إعادة التوجيه المحلية
  const redirectMap = {
    '/login': '/unified-login',
    '/simple-login': '/unified-login',
    '/auth/signin': '/unified-login',
    '/signup': '/unified-login',
    '/register': '/unified-login',
    '/logout': '/unified-login',
  };

  const newPath = redirectMap[path];

  if (!newPath) {
    return {
      statusCode: 404,
      body: 'مسار إعادة التوجيه غير موجود',
    };
  }

  return {
    statusCode: 302,
    headers: {
      Location: newPath,
    },
  };
}
