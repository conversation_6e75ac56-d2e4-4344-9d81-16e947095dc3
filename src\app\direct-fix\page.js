'use client';

import { useState } from 'react';
import MainLayout from '@/components/MainLayout';

export default function DirectFixPage() {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');
  const [employeeCode, setEmployeeCode] = useState('');

  const handleFixSamer = async () => {
    try {
      setLoading(true);
      setError('');
      setResult(null);

      const response = await fetch('/api/fix-samer-direct', {
        method: 'POST'
      });

      const data = await response.json();

      if (data.success) {
        setResult(data);
        alert(`✅ تم إصلاح التمام الشهري لسامر بنجاح!`);
      } else {
        setError(data.error || 'فشل في إصلاح التمام الشهري لسامر');
        if (data.testEmployeeCode) {
          setEmployeeCode(data.testEmployeeCode);
        }
      }
    } catch (error) {
      setError('حدث خطأ في الاتصال بالخادم');
    } finally {
      setLoading(false);
    }
  };

  const handleDirectFix = async () => {
    if (!employeeCode.trim()) {
      setError('يرجى إدخال كود الموظف');
      return;
    }

    try {
      setLoading(true);
      setError('');
      setResult(null);

      const response = await fetch('/api/direct-fix-attendance', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          employeeCode: employeeCode.trim()
        })
      });

      const data = await response.json();

      if (data.success) {
        setResult(data);
        alert(`✅ تم إصلاح التمام الشهري لـ ${data.data.employee.name} بنجاح!`);
      } else {
        setError(data.error || 'فشل في إصلاح التمام الشهري');
      }
    } catch (error) {
      setError('حدث خطأ في الاتصال بالخادم');
    } finally {
      setLoading(false);
    }
  };

  return (
    <MainLayout>
      <div className="max-w-4xl mx-auto p-6">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            🔧 إصلاح مباشر للتمام الشهري
          </h1>
          <p className="text-gray-600">
            إصلاح فوري ومباشر لمشكلة حساب التمام الشهري
          </p>
        </div>

        {/* إصلاح سريع لسامر */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-start">
              <span className="text-blue-500 mr-3 mt-0.5">👤</span>
              <div>
                <h3 className="font-medium text-blue-800 mb-1">إصلاح سريع لسامر</h3>
                <p className="text-blue-700 text-sm">
                  إصلاح مباشر وفوري لمشكلة حساب التمام لسامر
                </p>
              </div>
            </div>
            <button
              onClick={handleFixSamer}
              disabled={loading}
              className="flex items-center gap-2 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white py-2 px-4 rounded-lg transition-colors"
            >
              <span className="text-white">
                {loading ? '🔄' : '⚡'}
              </span>
              {loading ? 'جاري الإصلاح...' : 'إصلاح سامر فوراً'}
            </button>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
          <h2 className="text-xl font-bold text-gray-900 mb-6">إصلاح موظف محدد</h2>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                كود الموظف
              </label>
              <input
                type="text"
                value={employeeCode}
                onChange={(e) => setEmployeeCode(e.target.value)}
                placeholder="أدخل كود الموظف (مثال: 1472 لسامر)"
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                disabled={loading}
              />
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-red-700 text-sm">❌ {error}</p>
              </div>
            )}

            <button
              onClick={handleDirectFix}
              disabled={loading || !employeeCode.trim()}
              className="w-full flex items-center justify-center gap-2 bg-green-500 hover:bg-green-600 disabled:bg-gray-300 text-white py-3 px-6 rounded-lg transition-colors font-medium"
            >
              <span className="text-white text-lg">
                {loading ? '🔄' : '🔧'}
              </span>
              {loading ? 'جاري الإصلاح المباشر...' : 'إصلاح التمام الشهري الآن'}
            </button>
          </div>
        </div>

        {result && (
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center gap-2">
              <span className="text-green-500">✅</span>
              نتائج الإصلاح المباشر
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="font-medium text-blue-900 mb-2">معلومات الموظف</h3>
                <p className="text-sm text-blue-700">الاسم: {result.data.employee.name}</p>
                <p className="text-sm text-blue-700">الكود: {result.data.employee.code}</p>
                <p className="text-sm text-blue-700">الشهر: {result.data.month}/{result.data.year}</p>
              </div>

              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="font-medium text-green-900 mb-2">الأيام المحدثة</h3>
                <p className="text-2xl font-bold text-green-800">{result.data.updatedDays}</p>
                <p className="text-sm text-green-700">يوم تم تحديثه</p>
              </div>

              <div className="bg-purple-50 p-4 rounded-lg">
                <h3 className="font-medium text-purple-900 mb-2">الإجازات المعالجة</h3>
                <p className="text-2xl font-bold text-purple-800">{result.data.leavesProcessed}</p>
                <p className="text-sm text-purple-700">إجازة معتمدة</p>
              </div>

              {result.data.summary && (
                <div className="md:col-span-2 lg:col-span-3 bg-gray-50 p-4 rounded-lg">
                  <h3 className="font-medium text-gray-900 mb-3">ملخص التمام الشهري الجديد</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div className="bg-white p-3 rounded">
                      <span className="text-gray-600 block">أيام العمل:</span>
                      <span className="font-bold text-gray-900 text-lg">{result.data.summary.totalWorkingDays}</span>
                    </div>
                    <div className="bg-white p-3 rounded">
                      <span className="text-gray-600 block">أيام الحضور:</span>
                      <span className="font-bold text-green-600 text-lg">{result.data.summary.totalPresent}</span>
                    </div>
                    <div className="bg-white p-3 rounded">
                      <span className="text-gray-600 block">أيام الإجازات:</span>
                      <span className="font-bold text-blue-600 text-lg">{result.data.summary.totalLeaves}</span>
                    </div>
                    <div className="bg-white p-3 rounded">
                      <span className="text-gray-600 block">نسبة الحضور:</span>
                      <span className="font-bold text-purple-600 text-lg">{result.data.summary.attendancePercentage}%</span>
                    </div>
                  </div>
                </div>
              )}

              {result.data.leaveDetails && result.data.leaveDetails.length > 0 && (
                <div className="md:col-span-2 lg:col-span-3 bg-yellow-50 p-4 rounded-lg">
                  <h3 className="font-medium text-yellow-900 mb-3">تفاصيل الإجازات المعالجة</h3>
                  <div className="space-y-2">
                    {result.data.leaveDetails.map((leave, index) => (
                      <div key={index} className="bg-white p-3 rounded text-sm">
                        <span className="font-medium text-gray-900">إجازة {leave.type}</span>
                        <span className="text-gray-600 mx-2">من {leave.startDate} إلى {leave.endDate}</span>
                        <span className="text-green-600">({leave.updatedDays} يوم محدث)</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* تعليمات */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-6">
          <h3 className="font-medium text-blue-800 mb-2">📋 كيفية الاستخدام:</h3>
          <ol className="text-blue-700 text-sm space-y-1">
            <li>1. <strong>للإصلاح السريع:</strong> اضغط "إصلاح سامر فوراً" في الأعلى</li>
            <li>2. <strong>لموظف محدد:</strong> أدخل كود الموظف واضغط "إصلاح التمام الشهري الآن"</li>
            <li>3. انتظر اكتمال العملية</li>
            <li>4. راجع النتائج والإحصائيات</li>
          </ol>
        </div>
      </div>
    </MainLayout>
  );
}
