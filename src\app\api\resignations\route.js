import { NextResponse } from 'next/server';
import { getConnection } from '@/utils/db';

export async function GET() {
  let pool = null;

  try {

    pool = await getConnection();

    // إنشاء جدول الاستقالة إذا لم يكن موجوداً
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='EmployeeResignations' AND xtype='U')
      CREATE TABLE EmployeeResignations (
        ID int IDENTITY(1,1) PRIMARY KEY,
        EmployeeCode nvarchar(50) NOT NULL,
        EmployeeName nvarchar(255),
        Department nvarchar(255),
        JobTitle nvarchar(255),
        ResignationDate datetime,
        LastWorkingDay datetime,
        ResignationReason nvarchar(500),
        ResignationRequestPath nvarchar(500),
        ClearanceFormPath nvarchar(500),
        AttendanceReportPath nvarchar(500),
        FinalSettlementAmount decimal(10,2),
        CreatedAt datetime DEFAULT GETDATE(),
        CreatedBy nvarchar(100),
        IsActive bit DEFAULT 1
      )
    `);

    // إنشاء جدول أرشيف الاستقالة إذا لم يكن موجوداً
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ResignationArchive' AND xtype='U')
      CREATE TABLE ResignationArchive (
        ID int IDENTITY(1,1) PRIMARY KEY,
        ResignationID int NOT NULL,
        DocumentType nvarchar(100),
        DocumentPath nvarchar(500),
        UploadDate datetime DEFAULT GETDATE(),
        UploadedBy nvarchar(100),
        IsActive bit DEFAULT 1,
        FOREIGN KEY (ResignationID) REFERENCES EmployeeResignations(ID)
      )
    `);

    // جلب بيانات الاستقالة
    const resignationsResult = await pool.request().query(`
      SELECT
        r.*,
        e.EmployeeName as CurrentEmployeeName,
        e.CurrentStatus
      FROM EmployeeResignations r
      LEFT JOIN Employees e ON r.EmployeeCode = e.EmployeeCode
      WHERE r.IsActive = 1
      ORDER BY r.ResignationDate DESC, r.CreatedAt DESC
    `);

    return NextResponse.json({
      success: true,
      data: resignationsResult.recordset
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'حدث خطأ في جلب بيانات الاستقالة',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}

export async function POST(request) {
  let pool = null;

  try {
    const resignationData = await request.json();

    pool = await getConnection();

    // إدراج بيانات الاستقالة
    const result = await pool.request()
      .input('EmployeeCode', resignationData.employeeId)
      .input('EmployeeName', resignationData.employeeName)
      .input('Department', resignationData.department)
      .input('JobTitle', resignationData.jobTitle)
      .input('ResignationDate', resignationData.resignationDate)
      .input('LastWorkingDay', resignationData.lastWorkingDay)
      .input('ResignationReason', resignationData.resignationReason)
      .input('FinalSettlementAmount', resignationData.finalSettlementAmount || 0)
      .input('CreatedBy', resignationData.createdBy || 'النظام')
      .query(`
        INSERT INTO EmployeeResignations
        (EmployeeCode, EmployeeName, Department, JobTitle,
         ResignationDate, LastWorkingDay, ResignationReason,
         FinalSettlementAmount, CreatedBy)
        OUTPUT INSERTED.ID
        VALUES
        (@EmployeeCode, @EmployeeName, @Department, @JobTitle,
         @ResignationDate, @LastWorkingDay, @ResignationReason,
         @FinalSettlementAmount, @CreatedBy)
      `);

    const resignationId = result.recordset[0].ID;

    // تحديث حالة الموظف في جدول الموظفين
    await pool.request()
      .input('EmployeeCode', resignationData.employeeId)
      .query(`
        UPDATE Employees
        SET
          CurrentStatus = N'مستقيل',
          LastModified = GETDATE()
        WHERE EmployeeCode = @EmployeeCode
      `);

    return NextResponse.json({
      success: true,
      resignationId: resignationId,
      message: 'تم إضافة الاستقالة بنجاح'
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'حدث خطأ في إضافة الاستقالة',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    }, { status: 500 });
  }
}
