# نظام إدارة الموظفين - CreateXYZ Project

نظام شامل لإدارة بيانات الموظفين مبني باستخدام Next.js و SQL Server.

## 🚀 المميزات

- ✅ إدارة بيانات الموظفين (إضافة، تعديل، حذف، عرض)
- ✅ رفع وإدارة المستندات والملفات
- ✅ نظام المحافظات والأقسام
- ✅ واجهة مستخدم عربية متجاوبة
- ✅ اتصال آمن بقاعدة البيانات SQL Server
- ✅ معالجة شاملة للأخطاء

## 🛠️ التقنيات المستخدمة

- **Frontend**: Next.js 13, React 18, Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: Microsoft SQL Server 2022
- **ORM**: mssql package
- **UI**: React Icons, Tailwind Forms

## 📋 متطلبات النظام

- Node.js 18+
- SQL Server 2019+ (يفضل 2022)
- npm أو yarn

## ⚙️ الإعداد والتشغيل

### 1. تحضير قاعدة البيانات

تأكد من تشغيل SQL Server وإنشاء قاعدة بيانات باسم `emp`:

```sql
CREATE DATABASE emp;
```

### 2. تحضير المشروع

```bash
# تحميل المكتبات
npm install

# نسخ ملف البيئة (إذا لم يكن موجوداً)
# cp .env.example .env
```

### 3. إعداد متغيرات البيئة

تأكد من صحة ملف `.env`:

```env
DB_USER=sa
DB_PASSWORD=admin@123
DB_SERVER=localhost\DBOJESTA
DB_NAME=emp
```

### 4. اختبار الاتصال

```bash
# اختبار الاتصال بقاعدة البيانات
node test-db-connection.js

# تشخيص شامل للمشروع
node diagnose-project.js
```

### 5. تشغيل المشروع

```bash
# تشغيل في وضع التطوير
npm run dev

# أو للإنتاج
npm run build
npm start
```

المشروع سيعمل على: [http://localhost:3000](http://localhost:3000)

## 🧪 الاختبار

```bash
# اختبار واجهة برمجة التطبيقات
node test-api-endpoints.js
```

## 📁 هيكل المشروع

```
├── src/
│   ├── app/
│   │   ├── api/                 # نقاط النهاية API
│   │   │   ├── employee-data/   # إدارة الموظفين
│   │   │   ├── governorates/    # المحافظات
│   │   │   ├── departments/     # الأقسام
│   │   │   └── upload/          # رفع الملفات
│   │   └── components/          # مكونات React
│   ├── utils/
│   │   └── db.js               # إعدادات قاعدة البيانات
│   └── styles/                 # ملفات التنسيق
├── archiv/                     # مجلد الأرشيف
├── public/                     # الملفات العامة
└── test-*.js                   # ملفات الاختبار
```

## 🔧 استكشاف الأخطاء

### مشاكل الاتصال بقاعدة البيانات

1. **تحقق من تشغيل SQL Server**:
   ```bash
   sqlcmd -S localhost\DBOJESTA -U sa -P admin@123 -Q "SELECT @@VERSION"
   ```

2. **تحقق من وجود قاعدة البيانات**:
   ```bash
   sqlcmd -S localhost\DBOJESTA -U sa -P admin@123 -Q "SELECT name FROM sys.databases"
   ```

3. **تحقق من الجداول**:
   ```bash
   sqlcmd -S localhost\DBOJESTA -U sa -P admin@123 -d emp -Q "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES"
   ```

### أخطاء شائعة

- **ECONNREFUSED**: تأكد من تشغيل SQL Server
- **ELOGIN**: تحقق من اسم المستخدم وكلمة المرور
- **Invalid object name**: تأكد من وجود الجداول المطلوبة

## 📊 قاعدة البيانات

### الجداول الرئيسية

- `Employees`: بيانات الموظفين
- `Governorates`: المحافظات
- `Department`: الأقسام
- `Archive`: الملفات والمستندات

## ✅ الحلول المطبقة

تم حل المشاكل التالية:

1. **توحيد إعدادات قاعدة البيانات**: جميع ملفات API تستخدم نفس الإعدادات
2. **إصلاح أسماء الجداول**: تم تصحيح `Employee` إلى `Employees`
3. **تحسين معالجة الأخطاء**: رسائل خطأ واضحة ومفيدة
4. **اختبارات شاملة**: ملفات اختبار للتحقق من سلامة النظام

## 📞 الدعم

للحصول على الدعم، يرجى فتح issue في GitHub أو التواصل مع فريق التطوير.
