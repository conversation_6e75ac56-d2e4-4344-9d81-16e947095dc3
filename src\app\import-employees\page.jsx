"use client";
import React from "react";

function MainComponent() {
  const [selectedLang, setSelectedLang] = useState("ar");
  const [file, setFile] = useState(null);
  const [results, setResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const dir = selectedLang === "ar" ? "rtl" : "ltr";

  const handleFileChange = (e) => {
    if (e.target.files) {
      setFile(e.target.files[0]);
      setResults(null);
      setError(null);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!file) return;

    setLoading(true);
    setError(null);

    const formData = new FormData();
    formData.append("xmlFile", file);

    try {
      const response = await fetch("/api/import-employees", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error(
          selectedLang === "ar"
            ? "حدث خطأ أثناء استيراد البيانات"
            : "Error importing data"
        );
      }

      const data = await response.json();
      setResults(data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const downloadTemplate = () => {
    const templateUrl = "/templates/employee-template.xml";
    const link = document.createElement("a");
    link.href = templateUrl;
    link.download = "employee-template.xml";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div
      dir={dir}
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <a
            href="/employees"
            className="flex items-center text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
          >
            <i
              className={`fas fa-arrow-${
                selectedLang === "ar" ? "left" : "right"
              } ml-2`}
            ></i>
            {selectedLang === "ar" ? "عودة" : "Back"}
          </a>
          <button
            onClick={() => setSelectedLang(selectedLang === "ar" ? "en" : "ar")}
            className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
          >
            {selectedLang === "ar" ? "English" : "العربية"}
          </button>
        </div>

        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-8">
          {selectedLang === "ar"
            ? "استيراد بيانات الموظفين"
            : "Import Employee Data"}
        </h1>

        <div className="bg-gray-50 dark:bg-gray-800 rounded-xl p-6 mb-6">
          <button
            onClick={downloadTemplate}
            className="mb-6 flex items-center text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
          >
            <i className="fas fa-download ml-2"></i>
            {selectedLang === "ar"
              ? "تحميل نموذج XML فارغ"
              : "Download Empty XML Template"}
          </button>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-gray-700 dark:text-gray-300 mb-2">
                {selectedLang === "ar" ? "ملف XML" : "XML File"}
              </label>
              <input
                type="file"
                accept=".xml"
                onChange={handleFileChange}
                className="w-full p-2 border border-gray-300 rounded-md"
                name="xmlFile"
              />
            </div>

            <button
              type="submit"
              disabled={!file || loading}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors disabled:bg-blue-300"
            >
              {loading
                ? selectedLang === "ar"
                  ? "جاري الاستيراد..."
                  : "Importing..."
                : selectedLang === "ar"
                ? "استيراد البيانات"
                : "Import Data"}
            </button>
          </form>
        </div>

        {error && (
          <div className="bg-red-100 text-red-700 p-4 rounded-md mb-6">
            {error}
          </div>
        )}

        {results && (
          <div className="bg-gray-50 dark:bg-gray-800 rounded-xl p-6">
            <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
              {selectedLang === "ar" ? "نتائج الاستيراد" : "Import Results"}
            </h2>

            <div className="space-y-4">
              <div className="flex justify-between p-3 bg-green-100 dark:bg-green-900 rounded-md">
                <span className="text-green-700 dark:text-green-300">
                  {selectedLang === "ar" ? "تم استيراد" : "Imported"}:
                </span>
                <span className="font-bold text-green-700 dark:text-green-300">
                  {results.imported}
                </span>
              </div>

              {results.duplicates > 0 && (
                <div className="flex justify-between p-3 bg-yellow-100 dark:bg-yellow-900 rounded-md">
                  <span className="text-yellow-700 dark:text-yellow-300">
                    {selectedLang === "ar" ? "تكرارات" : "Duplicates"}:
                  </span>
                  <span className="font-bold text-yellow-700 dark:text-yellow-300">
                    {results.duplicates}
                  </span>
                </div>
              )}

              {results.errors > 0 && (
                <div className="flex justify-between p-3 bg-red-100 dark:bg-red-900 rounded-md">
                  <span className="text-red-700 dark:text-red-300">
                    {selectedLang === "ar" ? "أخطاء" : "Errors"}:
                  </span>
                  <span className="font-bold text-red-700 dark:text-red-300">
                    {results.errors}
                  </span>
                </div>
              )}
            </div>

            {results.errorDetails && results.errorDetails.length > 0 && (
              <div className="mt-6">
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-3">
                  {selectedLang === "ar" ? "تفاصيل الأخطاء" : "Error Details"}
                </h3>
                <ul className="list-disc list-inside space-y-2 text-red-600 dark:text-red-400">
                  {results.errorDetails.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default MainComponent;