'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Fi<PERSON>rrowRight,
  FiUser,
  FiCalendar,
  FiFileText,
  FiEye,
  FiPlus,
  FiDownload,
  FiRefreshCw,
  FiSearch,
  FiBriefcase,
  FiExternalLink
} from 'react-icons/fi';

// مكون أرشيف النقل
function TransferArchive({ employeeId }) {
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (employeeId) {
      fetchTransferDocuments();
    }
  }, [employeeId]);

  const fetchTransferDocuments = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/document-archive', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getTransferDocuments',
          employeeId: employeeId
        })
      });

      const data = await response.json();
      if (data.success) {
        setDocuments(data.documents);
      }
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  const openDocument = (path) => {
    window.open(path, '_blank');
  };

  return (
    <div className="mt-6">
      <h4 className="text-lg font-semibold text-red-600 border-b border-red-200 pb-2 mb-4">
        أرشيف النقل
      </h4>

      {loading ? (
        <div className="text-center py-4">
          <FiRefreshCw className="animate-spin text-2xl text-gray-400 mx-auto mb-2" />
          <p className="text-gray-500">جاري تحميل المستندات...</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {documents.length > 0 ? (
            documents.map((doc, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center gap-3 mb-3">
                  <span className="text-xl">{doc.icon}</span>
                  <span className="font-medium">{doc.name}</span>
                </div>
                <button
                  onClick={() => openDocument(doc.path)}
                  className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition-colors text-sm flex items-center gap-2"
                >
                  <FiExternalLink />
                  عرض المستند
                </button>
              </div>
            ))
          ) : (
            <div className="col-span-2 text-center py-8 text-gray-500">
              <FiFileText className="text-4xl mx-auto mb-2 opacity-50" />
              <p>لا توجد مستندات نقل متاحة</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default function TransfersPage() {
  const router = useRouter();
  const [transfers, setTransfers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTransfer, setSelectedTransfer] = useState(null);
  const [showDetails, setShowDetails] = useState(false);

  // جلب بيانات النقل
  const fetchTransfers = async () => {
    try {
      setLoading(true);

      const response = await fetch('/api/transfers');
      const data = await response.json();

      if (data.success) {
        setTransfers(data.data);

      } else {
        setError(data.error || 'فشل في جلب البيانات');
      }
    } catch (error) {

      setError('حدث خطأ في جلب البيانات');
    } finally {
      setLoading(false);
    }
  };

  // فحص تسجيل الدخول وجلب البيانات
  useEffect(() => {
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    if (!isLoggedIn) {
      router.push('/login');
      return;
    }

    fetchTransfers();
  }, [router]);

  // تصفية البيانات حسب البحث
  const filteredTransfers = transfers.filter(transfer =>
    transfer.EmployeeName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    transfer.EmployeeID?.toString().includes(searchTerm) ||
    transfer.PreviousDepartment?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    transfer.NewDepartment?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    transfer.ProjectOrDepartment?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // عرض تفاصيل النقل
  const viewTransferDetails = (transfer) => {
    setSelectedTransfer(transfer);
    setShowDetails(true);
  };

  // تصدير البيانات
  const exportData = () => {

    alert('سيتم تنفيذ تصدير البيانات قريباً');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <FiRefreshCw className="animate-spin text-4xl text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">جاري تحميل حالات النقل...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* رأس الصفحة */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <FiArrowRight className="text-2xl text-blue-600" />
              <h1 className="text-3xl font-bold text-gray-800">حالات النقل</h1>
              <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                {transfers.length} حالة نقل
              </span>
            </div>
            
            <div className="flex gap-3">
              <button
                onClick={() => router.push('/employees/transfers/add')}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
              >
                <FiPlus />
                إضافة نقل جديد
              </button>
              <button
                onClick={exportData}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
              >
                <FiDownload />
                تصدير البيانات
              </button>
              <button
                onClick={fetchTransfers}
                className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-2"
              >
                <FiRefreshCw />
                تحديث
              </button>
              <button
                onClick={() => router.push('/employees/search')}
                className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors flex items-center gap-2"
              >
                ← العودة للموظفين
              </button>
            </div>
          </div>

          {/* شريط البحث */}
          <div className="relative">
            <FiSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="البحث بالاسم أو الكود أو القسم أو المشروع..."
              className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* رسالة الخطأ */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {/* جدول النقل */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الموظف
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    من قسم
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    إلى قسم
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    المشروع/الإدارة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    تاريخ النقل
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredTransfers.length > 0 ? (
                  filteredTransfers.map((transfer, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                              <FiUser className="text-blue-600" />
                            </div>
                          </div>
                          <div className="mr-4">
                            <div className="text-sm font-medium text-gray-900">
                              {transfer.EmployeeName || transfer.CurrentEmployeeName}
                            </div>
                            <div className="text-sm text-gray-500">
                              #{transfer.EmployeeID}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{transfer.PreviousDepartment}</div>
                        <div className="text-sm text-gray-500">{transfer.PreviousJobTitle}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{transfer.NewDepartment}</div>
                        <div className="text-sm text-gray-500">{transfer.NewJobTitle}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center text-sm text-gray-900">
                          <FiBriefcase className="ml-2 text-gray-400" />
                          {transfer.ProjectOrDepartment || 'غير محدد'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center text-sm text-gray-900">
                          <FiCalendar className="ml-2 text-gray-400" />
                          {transfer.TransferDate ? 
                            new Date(transfer.TransferDate).toLocaleDateString('ar-EG') : 
                            'غير محدد'
                          }
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => viewTransferDetails(transfer)}
                          className="text-blue-600 hover:text-blue-900 flex items-center gap-1"
                        >
                          <FiEye />
                          عرض التفاصيل
                        </button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="6" className="px-6 py-12 text-center">
                      <div className="text-gray-500">
                        <FiArrowRight className="text-4xl mx-auto mb-4 opacity-50" />
                        <p className="text-lg">لا توجد حالات نقل</p>
                        <p className="text-sm">ابدأ بإضافة نقل جديد</p>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* نافذة تفاصيل النقل */}
        {showDetails && selectedTransfer && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
              <div className="flex items-center justify-between p-6 border-b">
                <h3 className="text-xl font-bold text-gray-800">
                  تفاصيل نقل {selectedTransfer.EmployeeName}
                </h3>
                <button
                  onClick={() => setShowDetails(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <div className="p-6 overflow-y-auto max-h-[70vh]">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* بيانات الموظف */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-blue-600 border-b border-blue-200 pb-2">
                      بيانات الموظف
                    </h4>
                    <div><span className="font-medium">الكود:</span> {selectedTransfer.EmployeeID}</div>
                    <div><span className="font-medium">الاسم:</span> {selectedTransfer.EmployeeName}</div>
                    <div><span className="font-medium">الحالة الحالية:</span> 
                      <span className="mr-2 px-2 py-1 bg-orange-100 text-orange-800 rounded-full text-xs">
                        {selectedTransfer.CurrentStatus || 'منقول'}
                      </span>
                    </div>
                  </div>

                  {/* بيانات النقل */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-green-600 border-b border-green-200 pb-2">
                      بيانات النقل
                    </h4>
                    <div><span className="font-medium">تاريخ النقل:</span> 
                      {selectedTransfer.TransferDate ? 
                        new Date(selectedTransfer.TransferDate).toLocaleDateString('ar-EG') : 
                        'غير محدد'
                      }
                    </div>
                    <div><span className="font-medium">من قسم:</span> {selectedTransfer.PreviousDepartment}</div>
                    <div><span className="font-medium">إلى قسم:</span> {selectedTransfer.NewDepartment}</div>
                    <div><span className="font-medium">من مسمى:</span> {selectedTransfer.PreviousJobTitle}</div>
                    <div><span className="font-medium">إلى مسمى:</span> {selectedTransfer.NewJobTitle}</div>
                    <div><span className="font-medium">المشروع/الإدارة:</span> {selectedTransfer.ProjectOrDepartment || 'غير محدد'}</div>
                  </div>
                </div>

                {/* سبب النقل */}
                <div className="mt-6">
                  <h4 className="text-lg font-semibold text-purple-600 border-b border-purple-200 pb-2 mb-4">
                    سبب النقل
                  </h4>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-gray-700">
                      {selectedTransfer.TransferReason || 'لم يتم تحديد سبب النقل'}
                    </p>
                  </div>
                </div>

                {/* أرشيف النقل */}
                <TransferArchive employeeId={selectedTransfer.EmployeeID} />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
