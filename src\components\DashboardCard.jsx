'use client';

import Link from 'next/link';
import { useState } from 'react';

const DashboardCard = ({
  title,
  value,
  icon: Icon,
  color = 'blue',
  href,
  description,
  trend,
  trendValue,
  onClick,
  isLoading = false,
  animationDelay = 0,
  iconPosition = 'right'
}) => {
  const [isHovered, setIsHovered] = useState(false);

  // ألوان مختلفة للمربعات
  const colorClasses = {
    blue: {
      bg: 'bg-gradient-to-br from-blue-500 to-blue-600',
      border: 'border-blue-500',
      text: 'text-blue-700 dark:text-blue-400',
      shadow: 'shadow-blue-500/25',
      hoverShadow: 'hover:shadow-blue-500/40',
      glow: 'shadow-[0_0_30px_rgba(59,130,246,0.3)]'
    },
    green: {
      bg: 'bg-gradient-to-br from-green-500 to-green-600',
      border: 'border-green-500',
      text: 'text-green-700 dark:text-green-400',
      shadow: 'shadow-green-500/25',
      hoverShadow: 'hover:shadow-green-500/40',
      glow: 'shadow-[0_0_30px_rgba(34,197,94,0.3)]'
    },
    red: {
      bg: 'bg-gradient-to-br from-red-500 to-red-600',
      border: 'border-red-500',
      text: 'text-red-700 dark:text-red-400',
      shadow: 'shadow-red-500/25',
      hoverShadow: 'hover:shadow-red-500/40',
      glow: 'shadow-[0_0_30px_rgba(239,68,68,0.3)]'
    },
    yellow: {
      bg: 'bg-gradient-to-br from-yellow-500 to-yellow-600',
      border: 'border-yellow-500',
      text: 'text-yellow-700 dark:text-yellow-400',
      shadow: 'shadow-yellow-500/25',
      hoverShadow: 'hover:shadow-yellow-500/40',
      glow: 'shadow-[0_0_30px_rgba(234,179,8,0.3)]'
    },
    purple: {
      bg: 'bg-gradient-to-br from-purple-500 to-purple-600',
      border: 'border-purple-500',
      text: 'text-purple-700 dark:text-purple-400',
      shadow: 'shadow-purple-500/25',
      hoverShadow: 'hover:shadow-purple-500/40',
      glow: 'shadow-[0_0_30px_rgba(168,85,247,0.3)]'
    },
    indigo: {
      bg: 'bg-gradient-to-br from-indigo-500 to-indigo-600',
      border: 'border-indigo-500',
      text: 'text-indigo-700 dark:text-indigo-400',
      shadow: 'shadow-indigo-500/25',
      hoverShadow: 'hover:shadow-indigo-500/40',
      glow: 'shadow-[0_0_30px_rgba(99,102,241,0.3)]'
    },
    pink: {
      bg: 'bg-gradient-to-br from-pink-500 to-pink-600',
      border: 'border-pink-500',
      text: 'text-pink-700 dark:text-pink-400',
      shadow: 'shadow-pink-500/25',
      hoverShadow: 'hover:shadow-pink-500/40',
      glow: 'shadow-[0_0_30px_rgba(236,72,153,0.3)]'
    },
    orange: {
      bg: 'bg-gradient-to-br from-orange-500 to-orange-600',
      border: 'border-orange-500',
      text: 'text-orange-700 dark:text-orange-400',
      shadow: 'shadow-orange-500/25',
      hoverShadow: 'hover:shadow-orange-500/40',
      glow: 'shadow-[0_0_30px_rgba(249,115,22,0.3)]'
    }
  };
  const currentColor = colorClasses[color] || colorClasses.blue;

  // تحديد نوع التأثير حسب اللون
  const getAnimationClass = () => {
    switch(color) {
      case 'red': return 'pulse-box pulse-box-red';
      case 'yellow':
      case 'orange': return 'pulse-box pulse-box-yellow';
      case 'green': return 'pulse-box pulse-box-green';
      case 'purple': return 'pulse-box pulse-box-purple';
      case 'indigo': return 'pulse-box pulse-box-indigo';
      case 'gray': return 'pulse-box pulse-box-gray';
      case 'pink': return 'pulse-box pulse-box-pink';
      case 'cyan': return 'pulse-box pulse-box-cyan';
      case 'blue':
      default: return 'pulse-box';
    }
  };

  const cardContent = (
    <div
      className={`
        relative overflow-hidden
        bg-white dark:bg-gray-800
        rounded-xl
        border border-gray-200 dark:border-gray-700
        cursor-pointer group
        animate-fadeInUp
        ${getAnimationClass()}
        transition-all duration-300 ease-in-out
        hover:scale-105
      `}
      style={{
        animationDelay: `${animationDelay}ms`,
        animationFillMode: 'both'
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onClick}
    >

      {/* المحتوى */}
      <div className="relative p-6 z-10">
        <div className={`flex items-center ${iconPosition === 'left' ? 'flex-row' : 'justify-between'}`}>
          {iconPosition === 'left' && (
            <div className={`
              p-3 rounded-full mr-4
              ${currentColor.bg} bg-opacity-10
            `}>
              {Icon && (
                <Icon className={`w-8 h-8 ${currentColor.text}`} />
              )}
            </div>
          )}

          <div className="flex-1">
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              {title}
            </p>

            {isLoading ? (
              <div className="animate-pulse">
                <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded w-20 mb-2"></div>
              </div>
            ) : (
              <p className={`text-3xl font-bold ${currentColor.text} mb-1`}>
                {value}
              </p>
            )}

            {description && (
              <div className="text-xs text-gray-600 dark:text-gray-400">
                {description}
              </div>
            )}

            {trend && trendValue && (
              <div className={`flex items-center mt-2 text-xs ${
                trend === 'up' ? 'text-green-600 dark:text-green-400' :
                trend === 'down' ? 'text-red-600 dark:text-red-400' :
                'text-gray-600 dark:text-gray-400'
              }`}>
                <span className="mr-1">
                  {trend === 'up' ? '↗' : trend === 'down' ? '↘' : '→'}
                </span>
                {trendValue}
              </div>
            )}
          </div>

          {iconPosition !== 'left' && (
            <div className={`
              p-3 rounded-full
              ${currentColor.bg} bg-opacity-10
            `}>
              {Icon && (
                <Icon className={`w-8 h-8 ${currentColor.text}`} />
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  // إذا كان هناك رابط، استخدم Link
  if (href) {
    return (
      <Link href={href} className="block">
        {cardContent}
      </Link>
    );
  }

  // إذا لم يكن هناك رابط، أرجع المحتوى مباشرة
  return cardContent;
};

// CSS للأنيميشن - fadeInUp فقط (التأثيرات الأخرى في globals.css)
const styles = `
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fadeInUp {
    animation: fadeInUp 0.6s ease-out;
  }
`;

// إضافة الستايل للصفحة
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}

export default DashboardCard;
