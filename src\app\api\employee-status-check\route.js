import { getConnection } from '@/utils/db';
import { NextResponse } from 'next/server';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const employeeCode = searchParams.get('employeeCode');

    if (!employeeCode) {
      return NextResponse.json({
        success: false,
        error: 'كود الموظف مطلوب'
      }, { status: 400 });
    }

    const pool = await getConnection();

    // جلب حالة الموظف
    const employeeResult = await pool.request()
      .input('EmployeeCode', employeeCode)
      .query(`
        SELECT
          EmployeeCode, EmployeeName, JobTitle, Department, CurrentStatus,
          IsResidentEmployee, CompanyHousing, HousingCode, TransportMethod
        FROM Employees
        WHERE EmployeeCode = @EmployeeCode
      `);

    if (employeeResult.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'الموظف غير موجود'
      }, { status: 404 });
    }

    const employee = employeeResult.recordset[0];

    // تحديد الخيارات المتاحة حسب الحالة الحالية
    const availableActions = getAvailableActions(employee.CurrentStatus);

    // جلب البيانات المرتبطة
    const relatedData = await getRelatedData(pool, employeeCode);

    return NextResponse.json({
      success: true,
      data: {
        employee: {
          code: employee.EmployeeCode,
          name: employee.EmployeeName,
          jobTitle: employee.JobTitle,
          department: employee.Department,
          currentStatus: employee.CurrentStatus,
          isResidentEmployee: employee.IsResidentEmployee,
          companyHousing: employee.CompanyHousing,
          housingCode: employee.HousingCode,
          transportMethod: employee.TransportMethod
        },
        availableActions,
        relatedData
      }
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'حدث خطأ في فحص حالة الموظف',
      details: error.message
    }, { status: 500 });
  }
}

// تحديد الخيارات المتاحة حسب الحالة
function getAvailableActions(currentStatus) {
  const actions = {
    // الموظف النشط
    'ساري': [
      { id: 'edit', name: 'تعديل مباشر', type: 'primary', icon: 'edit' },
      { id: 'comprehensive_edit', name: 'تعديل شامل', type: 'secondary', icon: 'edit' },
      { id: 'documents', name: 'أرشيف المستندات', type: 'success', icon: 'folder' },
      { id: 'transfer', name: 'نقل الموظف', type: 'info', icon: 'arrow-right' },
      { id: 'resignation', name: 'استقالة الموظف', type: 'warning', icon: 'log-out' },
      { id: 'print', name: 'طباعة البيانات', type: 'secondary', icon: 'printer' },
      { id: 'delete', name: 'حذف الموظف', type: 'danger', icon: 'trash' }
    ],
    'سارى': [
      { id: 'edit', name: 'تعديل مباشر', type: 'primary', icon: 'edit' },
      { id: 'comprehensive_edit', name: 'تعديل شامل', type: 'secondary', icon: 'edit' },
      { id: 'documents', name: 'أرشيف المستندات', type: 'success', icon: 'folder' },
      { id: 'transfer', name: 'نقل الموظف', type: 'info', icon: 'arrow-right' },
      { id: 'resignation', name: 'استقالة الموظف', type: 'warning', icon: 'log-out' },
      { id: 'print', name: 'طباعة البيانات', type: 'secondary', icon: 'printer' },
      { id: 'delete', name: 'حذف الموظف', type: 'danger', icon: 'trash' }
    ],
    'نشط': [
      { id: 'edit', name: 'تعديل مباشر', type: 'primary', icon: 'edit' },
      { id: 'comprehensive_edit', name: 'تعديل شامل', type: 'secondary', icon: 'edit' },
      { id: 'documents', name: 'أرشيف المستندات', type: 'success', icon: 'folder' },
      { id: 'transfer', name: 'نقل الموظف', type: 'info', icon: 'arrow-right' },
      { id: 'resignation', name: 'استقالة الموظف', type: 'warning', icon: 'log-out' },
      { id: 'print', name: 'طباعة البيانات', type: 'secondary', icon: 'printer' },
      { id: 'delete', name: 'حذف الموظف', type: 'danger', icon: 'trash' }
    ],
    'يعمل': [
      { id: 'edit', name: 'تعديل مباشر', type: 'primary', icon: 'edit' },
      { id: 'comprehensive_edit', name: 'تعديل شامل', type: 'secondary', icon: 'edit' },
      { id: 'documents', name: 'أرشيف المستندات', type: 'success', icon: 'folder' },
      { id: 'transfer', name: 'نقل الموظف', type: 'info', icon: 'arrow-right' },
      { id: 'resignation', name: 'استقالة الموظف', type: 'warning', icon: 'log-out' },
      { id: 'print', name: 'طباعة البيانات', type: 'secondary', icon: 'printer' },
      { id: 'delete', name: 'حذف الموظف', type: 'danger', icon: 'trash' }
    ],

    // الموظف المستقيل
    'مستقيل': [
      { id: 'view', name: 'عرض البيانات', type: 'info', icon: 'eye' },
      { id: 'documents', name: 'أرشيف المستندات', type: 'success', icon: 'folder' },
      { id: 'print', name: 'طباعة البيانات', type: 'secondary', icon: 'printer' },
      { id: 'reactivate', name: 'إعادة تفعيل', type: 'success', icon: 'refresh-cw' }
    ],

    // الموظف المنقول
    'منقول': [
      { id: 'view', name: 'عرض البيانات', type: 'info', icon: 'eye' },
      { id: 'documents', name: 'أرشيف المستندات', type: 'success', icon: 'folder' },
      { id: 'print', name: 'طباعة البيانات', type: 'secondary', icon: 'printer' },
      { id: 'edit', name: 'تعديل البيانات', type: 'primary', icon: 'edit' }
    ],

    // الموظف المتوقف
    'متوقف': [
      { id: 'view', name: 'عرض البيانات', type: 'info', icon: 'eye' },
      { id: 'documents', name: 'أرشيف المستندات', type: 'success', icon: 'folder' },
      { id: 'print', name: 'طباعة البيانات', type: 'secondary', icon: 'printer' },
      { id: 'reactivate', name: 'إعادة تفعيل', type: 'success', icon: 'refresh-cw' }
    ]
  };

  return actions[currentStatus] || actions['ساري'];
}

// جلب البيانات المرتبطة
async function getRelatedData(pool, employeeCode) {
  try {
    const data = {};

    // 1. بيانات الاستقالة
    try {
      const resignationResult = await pool.request()
        .input('EmployeeCode', employeeCode)
        .query(`
          SELECT TOP 1
            ID, ResignationDate, LastWorkingDay, ResignationReason,
            FinalSettlementAmount, CreatedAt, IsActive
          FROM EmployeeResignations
          WHERE EmployeeCode = @EmployeeCode
          ORDER BY CreatedAt DESC
        `);

      if (resignationResult.recordset.length > 0) {
        data.resignation = resignationResult.recordset[0];
      }
    } catch (error) {
      // تجاهل الأخطاء إذا كان الجدول غير موجود
    }

    // 2. بيانات النقل
    try {
      const transferResult = await pool.request()
        .input('EmployeeCode', employeeCode)
        .query(`
          SELECT TOP 1
            ID, TransferDate, NewDepartment, NewJobTitle,
            TransferReason, CreatedAt, IsActive
          FROM EmployeeTransfers
          WHERE EmployeeCode = @EmployeeCode
          ORDER BY CreatedAt DESC
        `);

      if (transferResult.recordset.length > 0) {
        data.transfer = transferResult.recordset[0];
      }
    } catch (error) {
      // تجاهل الأخطاء إذا كان الجدول غير موجود
    }

    // 3. بيانات السكن
    try {
      const apartmentResult = await pool.request()
        .input('EmployeeCode', employeeCode)
        .query(`
          SELECT TOP 1
            ab.ID, ab.StartDate, ab.EndDate, ab.IsActive,
            a.ApartmentCode, a.LandlordName, a.Address, a.RentAmount
          FROM ApartmentBeneficiaries ab
          INNER JOIN Apartments a ON ab.ApartmentID = a.ID
          WHERE ab.EmployeeCode = @EmployeeCode
          ORDER BY ab.CreatedAt DESC
        `);

      if (apartmentResult.recordset.length > 0) {
        data.apartment = apartmentResult.recordset[0];
      }
    } catch (error) {
      // تجاهل الأخطاء إذا كان الجدول غير موجود
    }

    // 4. بيانات السيارة
    try {
      const carResult = await pool.request()
        .input('EmployeeCode', employeeCode)
        .query(`
          SELECT TOP 1
            cb.ID, cb.StartDate, cb.EndDate, cb.IsActive,
            c.CarCode, c.ContractorName, c.CarNumber, c.Route, c.RentAmount
          FROM CarBeneficiaries cb
          INNER JOIN Cars c ON cb.CarCode = c.CarCode
          WHERE cb.EmployeeCode = @EmployeeCode
          ORDER BY cb.CreatedAt DESC
        `);

      if (carResult.recordset.length > 0) {
        data.car = carResult.recordset[0];
      }
    } catch (error) {
      // تجاهل الأخطاء إذا كان الجدول غير موجود
    }

    // 5. بيانات الإجازات
    try {
      const leaveResult = await pool.request()
        .input('EmployeeCode', employeeCode)
        .query(`
          SELECT TOP 5
            ID, LeaveType, StartDate, EndDate, Status, CreatedAt
          FROM LeaveRequests
          WHERE EmployeeCode = @EmployeeCode
          ORDER BY CreatedAt DESC
        `);

      if (leaveResult.recordset.length > 0) {
        data.leaves = leaveResult.recordset;
      }
    } catch (error) {
      // تجاهل الأخطاء إذا كان الجدول غير موجود
    }

    return data;

  } catch (error) {
    console.error('خطأ في جلب البيانات المرتبطة:', error);
    return {};
  }
}
