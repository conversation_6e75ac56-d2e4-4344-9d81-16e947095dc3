/* fonts.css - ملف الخطوط الموحد - نسخة محلية */

/* تعريف الخطوط المحلية كبديل للخطوط الخارجية */
/* استخدام خطوط النظام بدلاً من Google Fonts لتجنب مشاكل الشبكة */

/* الفئات المساعدة للخطوط - نسخة محلية */
.font-cairo {
  font-family: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', 'Lucida Grande', sans-serif !important;
}

.font-tajawal {
  font-family: 'Segoe UI', 'Tahoma', 'Arial Unicode MS', 'Lucida Grande', sans-serif !important;
}

.font-ibm {
  font-family: ui-monospace, SFMono-Regular, 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', monospace !important;
}

/* أوزان الخطوط */
.font-thin {
  font-weight: 200 !important;
}

.font-light {
  font-weight: 300 !important;
}

.font-normal {
  font-weight: 400 !important;
}

.font-medium {
  font-weight: 500 !important;
}

.font-semibold {
  font-weight: 600 !important;
}

.font-bold {
  font-weight: 700 !important;
}

.font-extrabold {
  font-weight: 800 !important;
}

.font-black {
  font-weight: 900 !important;
}

/* أحجام الخطوط للعناوين */
.text-title-xl {
  font-size: 2.5rem;
  line-height: 3rem;
  font-weight: 800;
}

.text-title-lg {
  font-size: 2rem;
  line-height: 2.5rem;
  font-weight: 700;
}

.text-title-md {
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 700;
}

.text-title-sm {
  font-size: 1.25rem;
  line-height: 1.75rem;
  font-weight: 600;
}

/* تحسينات للقراءة بالعربية */
.arabic-improved {
  font-feature-settings: "kern", "liga";
  letter-spacing: -0.02em;
  word-spacing: 0.1em;
  line-height: 1.8;
}