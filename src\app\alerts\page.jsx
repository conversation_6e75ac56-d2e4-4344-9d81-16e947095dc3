'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/MainLayout';
import {
  FiAlertTriangle,
  FiClock,
  FiFileText,
  FiCalendar,
  FiHome,
  FiTruck,
  FiDollarSign,
  FiRefreshCw,
  FiEyeOff,
  FiFilter,
  FiSearch,
  FiPlay,
  FiBarChart
} from 'react-icons/fi';

export default function AlertsPage() {
  const [alerts, setAlerts] = useState([]);
  const [alertCounts, setAlertCounts] = useState({});
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('active');
  const [filters, setFilters] = useState({
    category: '',
    severity: '',
    alertType: '',
    employeeCode: ''
  });

  // فئات التنبيهات
  const alertCategories = [
    { value: '', label: 'جميع الفئات' },
    { value: 'Attendance', label: 'الحضور والغياب' },
    { value: 'Contracts', label: 'التعاقدات' },
    { value: 'Documents', label: 'المستندات' },
    { value: 'Housing', label: 'السكن' },
    { value: 'Transportation', label: 'المواصلات' },
    { value: 'Costs', label: 'التكاليف' },
    { value: 'System', label: 'النظام' }
  ];

  // مستويات الخطورة
  const severityLevels = [
    { value: '', label: 'جميع المستويات' },
    { value: 'critical', label: 'حرج' },
    { value: 'high', label: 'عالي' },
    { value: 'medium', label: 'متوسط' },
    { value: 'low', label: 'منخفض' }
  ];

  useEffect(() => {
    setupAlertsSystem();
    loadData();
  }, []);

  useEffect(() => {
    loadAlerts();
  }, [activeTab, filters]);

  // إعداد نظام التنبيهات
  const setupAlertsSystem = async () => {
    try {
      const response = await fetch('/api/alerts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'setup' })
      });

      const result = await response.json();
      if (!result.success) {

      }
    } catch (error) {

    }
  };

  // جلب البيانات
  const loadData = async () => {
    await Promise.all([
      loadAlerts(),
      loadAlertCounts()
    ]);
  };

  // جلب التنبيهات
  const loadAlerts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/alerts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getAlerts',
          includeDismissed: activeTab === 'dismissed',
          ...filters
        })
      });

      const result = await response.json();
      if (result.success) {
        setAlerts(result.data || []);
      } else {

      }
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  // جلب إحصائيات التنبيهات
  const loadAlertCounts = async () => {
    try {
      const response = await fetch('/api/alerts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getAlertCounts'
        })
      });

      const result = await response.json();
      if (result.success) {
        setAlertCounts(result.data || {});
      }
    } catch (error) {

    }
  };

  // إخفاء تنبيه
  const dismissAlert = async (alertId) => {
    try {
      const userInfo = localStorage.getItem('userInfo');
      const user = userInfo ? JSON.parse(userInfo) : null;

      const response = await fetch('/api/alerts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'dismissAlert',
          alertId: alertId,
          userCode: user?.username || 'system'
        })
      });

      if (response.ok) {
        loadData();
      }
    } catch (error) {

    }
  };

  // تشغيل فحص التنبيهات
  const runAlertChecks = async () => {
    try {
      setLoading(true);
      
      const checks = [
        { action: 'checkMissingAttendance' },
        { action: 'checkExpiringContracts', daysAhead: 30 },
        { action: 'checkMissingDocuments' }
      ];

      for (const check of checks) {
        await fetch('/api/alerts', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(check)
        });
      }

      await loadData();
      alert('تم تشغيل فحص التنبيهات بنجاح');
      
    } catch (error) {

      alert('حدث خطأ في تشغيل فحص التنبيهات');
    } finally {
      setLoading(false);
    }
  };

  // تحديث الفلاتر
  const updateFilter = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // إعادة تعيين الفلاتر
  const resetFilters = () => {
    setFilters({
      category: '',
      severity: '',
      alertType: '',
      employeeCode: ''
    });
  };

  // تنسيق التاريخ
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('ar-EG', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // أيقونة نوع التنبيه
  const getAlertIcon = (alertType, severity) => {
    const iconClass = `w-5 h-5 ${
      severity === 'critical' ? 'text-red-600' :
      severity === 'high' ? 'text-orange-500' :
      severity === 'medium' ? 'text-yellow-500' :
      'text-blue-500'
    }`;

    switch (alertType) {
      case 'MISSING_ATTENDANCE':
        return <FiClock className={iconClass} />;
      case 'EXPIRING_CONTRACT':
        return <FiCalendar className={iconClass} />;
      case 'MISSING_DOCUMENTS':
        return <FiFileText className={iconClass} />;
      case 'APARTMENT_VACANCY':
        return <FiHome className={iconClass} />;
      case 'CAR_UNASSIGNED':
        return <FiTruck className={iconClass} />;
      case 'COST_OVERDUE':
        return <FiDollarSign className={iconClass} />;
      default:
        return <FiAlertTriangle className={iconClass} />;
    }
  };

  // لون شدة التنبيه
  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 border-red-500 text-red-800 dark:bg-red-900/20 dark:border-red-400 dark:text-red-300';
      case 'high':
        return 'bg-orange-100 border-orange-500 text-orange-800 dark:bg-orange-900/20 dark:border-orange-400 dark:text-orange-300';
      case 'medium':
        return 'bg-yellow-100 border-yellow-500 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-400 dark:text-yellow-300';
      case 'low':
        return 'bg-blue-100 border-blue-500 text-blue-800 dark:bg-blue-900/20 dark:border-blue-400 dark:text-blue-300';
      default:
        return 'bg-gray-100 border-gray-500 text-gray-800 dark:bg-gray-900/20 dark:border-gray-400 dark:text-gray-300';
    }
  };

  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto p-6">
        {/* رأس الصفحة */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FiAlertTriangle className="text-3xl text-orange-600" />
              <div>
                <h1 className="text-3xl font-bold text-gray-800 dark:text-white">
                  نظام التنبيهات الذكية
                </h1>
                <p className="text-gray-600 dark:text-gray-300">
                  مراقبة وتتبع الأشياء المفقودة والمنتهية الصلاحية
                </p>
              </div>
            </div>
            <button
              onClick={runAlertChecks}
              disabled={loading}
              className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 flex items-center gap-2 disabled:opacity-50"
            >
              <FiPlay className={loading ? 'animate-spin' : ''} />
              تشغيل فحص التنبيهات
            </button>
          </div>
        </div>

        {/* الإحصائيات */}
        {alertCounts && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border-l-4 border-red-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
                    تنبيهات حرجة
                  </p>
                  <p className="text-3xl font-bold text-red-600">
                    {alertCounts.CriticalAlerts || 0}
                  </p>
                </div>
                <FiAlertTriangle className="text-2xl text-red-600" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border-l-4 border-orange-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
                    تنبيهات عالية
                  </p>
                  <p className="text-3xl font-bold text-orange-600">
                    {alertCounts.HighAlerts || 0}
                  </p>
                </div>
                <FiClock className="text-2xl text-orange-600" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border-l-4 border-yellow-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
                    تنبيهات متوسطة
                  </p>
                  <p className="text-3xl font-bold text-yellow-600">
                    {alertCounts.MediumAlerts || 0}
                  </p>
                </div>
                <FiFileText className="text-2xl text-yellow-600" />
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 border-l-4 border-blue-500">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">
                    إجمالي التنبيهات
                  </p>
                  <p className="text-3xl font-bold text-blue-600">
                    {alertCounts.TotalAlerts || 0}
                  </p>
                </div>
                <FiBarChart className="text-2xl text-blue-600" />
              </div>
            </div>
          </div>
        )}

        {/* التبويبات والمحتوى */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
          {/* التبويبات */}
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex space-x-8 px-6">
              <button
                onClick={() => setActiveTab('active')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'active'
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <FiAlertTriangle />
                  التنبيهات النشطة ({alertCounts.TotalAlerts || 0})
                </div>
              </button>

              <button
                onClick={() => setActiveTab('dismissed')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'dismissed'
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center gap-2">
                  <FiEyeOff />
                  التنبيهات المخفية
                </div>
              </button>
            </nav>
          </div>

          {/* المحتوى */}
          <div className="p-6">
            {/* فلاتر البحث */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    الفئة
                  </label>
                  <select
                    value={filters.category}
                    onChange={(e) => updateFilter('category', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                  >
                    {alertCategories.map(category => (
                      <option key={category.value} value={category.value}>
                        {category.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    مستوى الخطورة
                  </label>
                  <select
                    value={filters.severity}
                    onChange={(e) => updateFilter('severity', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                  >
                    {severityLevels.map(level => (
                      <option key={level.value} value={level.value}>
                        {level.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    كود الموظف
                  </label>
                  <input
                    type="text"
                    value={filters.employeeCode}
                    onChange={(e) => updateFilter('employeeCode', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                    placeholder="البحث بكود الموظف"
                  />
                </div>

                <div className="flex items-end gap-2">
                  <button
                    onClick={loadAlerts}
                    className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 flex items-center gap-2"
                  >
                    <FiSearch />
                    بحث
                  </button>
                  <button
                    onClick={resetFilters}
                    className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 flex items-center gap-2"
                  >
                    <FiRefreshCw />
                    إعادة تعيين
                  </button>
                </div>
              </div>
            </div>

            {/* قائمة التنبيهات */}
            {loading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto"></div>
                <p className="text-gray-600 dark:text-gray-300 mt-4">جاري تحميل التنبيهات...</p>
              </div>
            ) : alerts.length === 0 ? (
              <div className="text-center py-12">
                <FiAlertTriangle className="text-6xl text-gray-300 mx-auto mb-4" />
                <h3 className="text-xl font-medium text-gray-600 mb-2">لا توجد تنبيهات</h3>
                <p className="text-gray-500">لم يتم العثور على تنبيهات مطابقة للفلاتر المحددة</p>
              </div>
            ) : (
              <div className="space-y-4">
                {alerts.map((alert) => (
                  <div
                    key={alert.ID}
                    className={`p-4 rounded-lg border-l-4 ${getSeverityColor(alert.Severity)} ${
                      alert.IsDismissed ? 'opacity-60' : ''
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-3 flex-1">
                        {/* أيقونة التنبيه */}
                        <div className="flex-shrink-0 mt-1">
                          {getAlertIcon(alert.AlertType, alert.Severity)}
                        </div>

                        {/* محتوى التنبيه */}
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                              {alert.Title}
                            </h3>
                            <span className={`px-2 py-1 text-xs rounded-full ${getSeverityColor(alert.Severity)}`}>
                              {alert.Severity === 'critical' ? 'حرج' :
                               alert.Severity === 'high' ? 'عالي' :
                               alert.Severity === 'medium' ? 'متوسط' : 'منخفض'}
                            </span>
                          </div>

                          <p className="text-gray-700 dark:text-gray-300 mb-3">
                            {alert.Message}
                          </p>

                          <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                            <span>
                              <strong>التاريخ:</strong> {formatDate(alert.CreatedAt)}
                            </span>
                            {alert.EmployeeCode && (
                              <span>
                                <strong>الموظف:</strong> {alert.EmployeeName} ({alert.EmployeeCode})
                              </span>
                            )}
                            {alert.DaysOverdue > 0 && (
                              <span className="text-red-600">
                                <strong>متأخر:</strong> {alert.DaysOverdue} يوم
                              </span>
                            )}
                          </div>

                          {alert.IsDismissed && (
                            <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                              تم إخفاؤه بواسطة: {alert.DismissedBy} في {formatDate(alert.DismissedAt)}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* أزرار الإجراءات */}
                      {!alert.IsDismissed && (
                        <div className="flex items-center gap-2">
                          <button
                            onClick={() => dismissAlert(alert.ID)}
                            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 p-2"
                            title="إخفاء التنبيه"
                          >
                            <FiEyeOff className="w-5 h-5" />
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
