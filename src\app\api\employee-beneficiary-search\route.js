import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const limit = parseInt(searchParams.get('limit')) || 10;

    if (!query || query.trim().length === 0) {
      return NextResponse.json({
        success: true,
        suggestions: []
      });
    }

    const pool = await getConnection();
    const searchTerm = query.trim();

    // تحديد نوع البحث: رقم أم نص
    const isNumeric = /^\d+$/.test(searchTerm);

    let searchQuery;
    if (isNumeric) {
      // البحث بالكود
      searchQuery = `
        SELECT TOP (@Limit)
          EmployeeCode,
          EmployeeName,
          JobTitle,
          Department,
          CurrentStatus
        FROM Employees
        WHERE CAST(EmployeeCode AS NVARCHAR) LIKE @SearchTerm + '%'
          AND CurrentStatus IN ('نشط', 'ساري', 'سارى')
        ORDER BY EmployeeCode
      `;
    } else {
      // البحث بالاسم
      searchQuery = `
        SELECT TOP (@Limit)
          EmployeeCode,
          EmployeeName,
          JobTitle,
          Department,
          CurrentStatus
        FROM Employees
        WHERE EmployeeName LIKE '%' + @SearchTerm + '%'
          AND CurrentStatus IN ('نشط', 'ساري', 'سارى')
        ORDER BY EmployeeName
      `;
    }

    const result = await pool.request()
      .input('SearchTerm', sql.NVarChar, searchTerm)
      .input('Limit', sql.Int, limit)
      .query(searchQuery);

    const suggestions = result.recordset.map(emp => ({
      // المعرفات الأساسية
      id: emp.EmployeeCode,
      employeeCode: emp.EmployeeCode,
      employeeName: emp.EmployeeName,
      
      // بيانات إضافية
      jobTitle: emp.JobTitle || 'غير محدد',
      department: emp.Department || 'غير محدد',
      status: emp.CurrentStatus || 'غير محدد',
      
      // للعرض في القائمة
      displayText: `${emp.EmployeeName} (${emp.EmployeeCode})`,
      displayName: emp.EmployeeName,
      displayCode: emp.EmployeeCode,
      
      // للتوافق مع الأنظمة القديمة
      EmployeeCode: emp.EmployeeCode,
      EmployeeName: emp.EmployeeName,
      EmployeeID: emp.EmployeeCode,
      FullName: emp.EmployeeName,
      JobTitle: emp.JobTitle,
      Department: emp.Department
    }));

    return NextResponse.json({
      success: true,
      suggestions: suggestions,
      searchType: isNumeric ? 'code' : 'name',
      query: searchTerm,
      count: suggestions.length
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في البحث: ' + error.message
    }, { status: 500 });
  }
}

export async function POST(request) {
  try {
    const body = await request.json();
    const { searchTerm, limit = 10 } = body;

    if (!searchTerm || searchTerm.trim().length === 0) {
      return NextResponse.json({
        success: true,
        data: []
      });
    }

    const pool = await getConnection();
    const cleanSearchTerm = searchTerm.trim();

    // تحديد نوع البحث: رقم أم نص
    const isNumeric = /^\d+$/.test(cleanSearchTerm);

    let searchQuery;
    if (isNumeric) {
      // البحث بالكود
      searchQuery = `
        SELECT TOP (@Limit)
          EmployeeCode,
          EmployeeName,
          JobTitle,
          Department,
          CurrentStatus,
          Mobile,
          email
        FROM Employees
        WHERE CAST(EmployeeCode AS NVARCHAR) LIKE @SearchTerm + '%'
          AND CurrentStatus IN ('نشط', 'ساري', 'سارى')
        ORDER BY EmployeeCode
      `;
    } else {
      // البحث بالاسم
      searchQuery = `
        SELECT TOP (@Limit)
          EmployeeCode,
          EmployeeName,
          JobTitle,
          Department,
          CurrentStatus,
          Mobile,
          email
        FROM Employees
        WHERE EmployeeName LIKE '%' + @SearchTerm + '%'
          AND CurrentStatus IN ('نشط', 'ساري', 'سارى')
        ORDER BY EmployeeName
      `;
    }

    const result = await pool.request()
      .input('SearchTerm', sql.NVarChar, cleanSearchTerm)
      .input('Limit', sql.Int, limit)
      .query(searchQuery);

    const employees = result.recordset.map(emp => ({
      // المعرفات الأساسية
      employeeCode: emp.EmployeeCode,
      employeeName: emp.EmployeeName,
      
      // بيانات إضافية
      jobTitle: emp.JobTitle || 'غير محدد',
      department: emp.Department || 'غير محدد',
      status: emp.CurrentStatus || 'غير محدد',
      mobile: emp.Mobile || '',
      email: emp.email || '',
      
      // للعرض
      displayText: `${emp.EmployeeName} (${emp.EmployeeCode})`,
      displayName: emp.EmployeeName,
      displayCode: emp.EmployeeCode,
      
      // للتوافق
      EmployeeCode: emp.EmployeeCode,
      EmployeeName: emp.EmployeeName,
      EmployeeID: emp.EmployeeCode,
      FullName: emp.EmployeeName,
      JobTitle: emp.JobTitle,
      Department: emp.Department
    }));

    return NextResponse.json({
      success: true,
      data: employees,
      searchType: isNumeric ? 'code' : 'name',
      query: cleanSearchTerm,
      totalFound: employees.length
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في البحث: ' + error.message
    }, { status: 500 });
  }
}
