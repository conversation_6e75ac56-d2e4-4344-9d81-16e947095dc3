import { writeFile, mkdir, unlink } from 'fs/promises';
import { NextResponse } from 'next/server';
import path from 'path';
import { existsSync } from 'fs';
import { getConnection, sql } from '@/utils/db';

// تعريف المسارات للمستندات
const documentPaths = {
  'صورة شخصية (PDF)': 'archiv/photo/',
  'صورة شخصية (JPG)': 'archiv/pic/',
  'رقم قومي': 'archiv/NationalIDs/',
  'بيان حالة': 'archiv/StatusReports/',
  'استلام عمل': 'archiv/WorkReceipts/',
  'كارنية نقابة': 'archiv/UnionCards/',
  'بدل إعاشة': 'archiv/bh/',
  'بدل انتقال': 'archiv/bt/'
};

export async function POST(req) {
  try {
    const formData = await req.formData();
    const file = formData.get('file');
    const type = formData.get('type');
    const employeeId = formData.get('employeeId') || 'temp';
    if (!file || !type) {
      return NextResponse.json(
        { success: false, error: 'Missing file or type' },
        { status: 400 }
      );
    }    // التحقق من نوع المستند والملف
    const fileExt = file.name.split('.').pop().toLowerCase();
    const basePath = documentPaths[type];
    if (!basePath) {
      return NextResponse.json(
        { success: false, error: 'Invalid document type' },
        { status: 400 }
      );
    }

    // إنشاء المسار الكامل
    const uploadsPath = path.join(process.cwd(), basePath);
    // إنشاء المجلد إذا لم يكن موجوداً
    try {
      if (!existsSync(uploadsPath)) {
        await mkdir(uploadsPath, { recursive: true });
      }
    } catch (error) {
      return NextResponse.json(
        { success: false, error: 'Failed to create upload directory: ' + error.message },
        { status: 500 }
      );
    }    // تجهيز اسم الملف (سيكون رقم الموظف فقط مع امتداد الملف)
    const filename = `${employeeId}.${fileExt}`;
    const filepath = path.join(uploadsPath, filename);
    // إذا كان الملف موجود مسبقاً، سنقوم بحذفه
    if (existsSync(filepath)) {
      try {
        await unlink(filepath);
      } catch (error) {
      }
    }

    try {
      // حفظ الملف
      const bytes = await file.arrayBuffer();
      const buffer = Buffer.from(bytes);
      await writeFile(filepath, buffer);
      try {
        // حفظ المعلومات في قاعدة البيانات
        const pool = await getConnection();
        const query = `
          INSERT INTO Archive (
            EmployeeID,
            Item,
            Path
          ) VALUES (
            @EmployeeID,
            @Item,
            @Path
          )
        `;

        const request = pool.request();
        request.input('EmployeeID', sql.VarChar, employeeId);
        request.input('Item', sql.VarChar, type);
        request.input('Path', sql.VarChar, path.join(basePath, filename));
        
        await request.query(query);
        return NextResponse.json({
          success: true,
          path: path.join(basePath, filename)
        });
      } catch (dbError) {
        // File was saved but database update failed
        return NextResponse.json({
          success: true, // Still return success since file was saved
          path: path.join(basePath, filename),
          warning: 'File saved but database update failed: ' + dbError.message
        });
      }
    } catch (error) {
      return NextResponse.json(
        { success: false, error: 'Failed to save file: ' + error.message },
        { status: 500 }
      );
    }
  } catch (error) {
    return NextResponse.json(
      { success: false, error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
