import { NextResponse } from 'next/server';
import { getConnection, sql } from '@/utils/db';

// اكتشاف الأعمدة الصحيحة تلقائياً
async function discoverCorrectColumns(pool) {
  try {
    // أولاً: فحص بنية الجدول للحصول على أسماء الأعمدة
    const columnsResult = await pool.request().query(`
      SELECT COLUMN_NAME, DATA_TYPE
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_NAME = 'Employees'
      ORDER BY ORDINAL_POSITION
    `);

    const columns = columnsResult.recordset;
    let employeeCodeColumn = null;
    let employeeNameColumn = null;

    // البحث عن عمود الكود
    const codeColumns = columns.filter(col =>
      col.COLUMN_NAME.toLowerCase().includes('employee') ||
      col.COLUMN_NAME.toLowerCase().includes('code') ||
      col.COLUMN_NAME.toLowerCase().includes('id') ||
      col.COLUMN_NAME.toLowerCase().includes('num') ||
      col.COLUMN_NAME === 'EmployeeCode' ||
      col.COLUMN_NAME === 'EmployeeID' ||
      col.COLUMN_NAME === 'Num'
    );

    // البحث عن عمود الاسم
    const nameColumns = columns.filter(col =>
      col.COLUMN_NAME.toLowerCase().includes('name') ||
      col.COLUMN_NAME.toLowerCase().includes('اسم') ||
      col.COLUMN_NAME === 'EmployeeName' ||
      col.COLUMN_NAME === 'FullName' ||
      col.COLUMN_NAME === 'Name'
    );

    // اختيار أفضل عمود للكود
    if (codeColumns.length > 0) {
      // ترتيب الأولوية
      employeeCodeColumn = codeColumns.find(col => col.COLUMN_NAME === 'EmployeeCode')?.COLUMN_NAME ||
                          codeColumns.find(col => col.COLUMN_NAME === 'EmployeeID')?.COLUMN_NAME ||
                          codeColumns.find(col => col.COLUMN_NAME === 'Num')?.COLUMN_NAME ||
                          codeColumns[0].COLUMN_NAME;
    }

    // اختيار أفضل عمود للاسم
    if (nameColumns.length > 0) {
      // ترتيب الأولوية
      employeeNameColumn = nameColumns.find(col => col.COLUMN_NAME === 'EmployeeName')?.COLUMN_NAME ||
                          nameColumns.find(col => col.COLUMN_NAME === 'FullName')?.COLUMN_NAME ||
                          nameColumns.find(col => col.COLUMN_NAME === 'Name')?.COLUMN_NAME ||
                          nameColumns[0].COLUMN_NAME;
    }

    // التحقق من وجود البيانات في الأعمدة المختارة
    if (employeeCodeColumn && employeeNameColumn) {
      try {
        const testResult = await pool.request().query(`
          SELECT TOP 1 ${employeeCodeColumn}, ${employeeNameColumn} FROM Employees
          WHERE ${employeeCodeColumn} IS NOT NULL AND ${employeeNameColumn} IS NOT NULL
        `);

        if (testResult.recordset.length === 0) {
          throw new Error('لا توجد بيانات صالحة في الأعمدة المختارة');
        }
      } catch (testError) {

        // العودة للقيم الافتراضية
        employeeCodeColumn = 'EmployeeCode';
        employeeNameColumn = 'EmployeeName';
      }
    }

    const result = {
      employeeCodeColumn: employeeCodeColumn || 'EmployeeCode',
      employeeNameColumn: employeeNameColumn || 'EmployeeName'
    };

    return result;

  } catch (error) {

    // استخدام القيم الافتراضية الصحيحة
    return {
      employeeCodeColumn: 'EmployeeCode',
      employeeNameColumn: 'EmployeeName'
    };
  }
}

export async function POST(request) {
  try {
    let requestData;
    try {
      // قراءة البيانات كـ text أولاً للتعامل مع UTF-8
      const textData = await request.text();

      requestData = JSON.parse(textData);
    } catch (jsonError) {

      return NextResponse.json({
        success: false,
        error: 'خطأ في تنسيق البيانات المرسلة'
      }, { status: 400 });
    }

    const { searchTerm, limit = 10 } = requestData;

    if (!searchTerm || searchTerm.trim().length === 0) {
      return NextResponse.json({
        success: true,
        data: []
      });
    }

    const pool = await getConnection();
    const cleanSearchTerm = searchTerm.trim();

    // فحص إذا كان النص يحتوي على أحرف عربية صحيحة
    const hasArabic = /[\u0600-\u06FF]/.test(cleanSearchTerm);
    const isNumeric = /^\d+$/.test(cleanSearchTerm);

    console.log('🔍 تحليل النص:', {
      original: searchTerm,
      clean: cleanSearchTerm,
      hasArabic,
      isNumeric,
      length: cleanSearchTerm.length,
      charCodes: cleanSearchTerm.split('').map(c => c.charCodeAt(0))
    });

    // تحديد نوع البحث: رقم أم نص

    let allResults = [];

    // البحث في جدول Employees الصحيح
    try {
      // اكتشاف الأعمدة الصحيحة
      const { employeeCodeColumn, employeeNameColumn } = await discoverCorrectColumns(pool);

      let searchQuery1;
      if (isNumeric) {
        searchQuery1 = `
          SELECT TOP (@Limit)
            ${employeeCodeColumn} as EmployeeCode,
            ${employeeNameColumn} as EmployeeName,
            JobTitle,
            Department,
            'Employees' as SourceTable
          FROM Employees
          WHERE CAST(${employeeCodeColumn} AS NVARCHAR) LIKE @SearchTerm + '%'
          ORDER BY ${employeeCodeColumn}
        `;
      } else {
        searchQuery1 = `
          SELECT TOP (@Limit)
            ${employeeCodeColumn} as EmployeeCode,
            ${employeeNameColumn} as EmployeeName,
            JobTitle,
            Department,
            'Employees' as SourceTable
          FROM Employees
          WHERE ${employeeNameColumn} LIKE '%' + @SearchTerm + '%'
          ORDER BY ${employeeNameColumn}
        `;
      }

      const result1 = await pool.request()
        .input('SearchTerm', sql.NVarChar, cleanSearchTerm)
        .input('Limit', sql.Int, limit)
        .query(searchQuery1);

      allResults = allResults.concat(result1.recordset);

    } catch (error) {

      // إذا كان الخطأ متعلق بعمود غير موجود، جرب الأعمدة الافتراضية
      if (error.message.includes('Invalid column name')) {
        try {

          let fallbackQuery;
          if (isNumeric) {
            fallbackQuery = `
              SELECT TOP (@Limit)
                EmployeeCode,
                EmployeeName,
                JobTitle,
                Department,
                'Employees' as SourceTable
              FROM Employees
              WHERE CAST(EmployeeCode AS NVARCHAR) LIKE @SearchTerm + '%'
              ORDER BY EmployeeCode
            `;
          } else {
            fallbackQuery = `
              SELECT TOP (@Limit)
                EmployeeCode,
                EmployeeName,
                JobTitle,
                Department,
                'Employees' as SourceTable
              FROM Employees
              WHERE EmployeeName LIKE '%' + @SearchTerm + '%'
              ORDER BY EmployeeName
            `;
          }

          const fallbackResult = await pool.request()
            .input('SearchTerm', sql.NVarChar, cleanSearchTerm)
            .input('Limit', sql.Int, limit)
            .query(fallbackQuery);

          allResults = allResults.concat(fallbackResult.recordset);

        } catch (fallbackError) {

        }
      }
    }

    // إزالة التكرارات وتنسيق النتائج
    const uniqueResults = [];
    const seenIds = new Set();

    for (const emp of allResults) {
      const empId = emp.EmployeeCode?.toString();
      if (empId && !seenIds.has(empId)) {
        seenIds.add(empId);
        uniqueResults.push({
          employeeCode: empId,
          EmployeeCode: empId,
          employeeName: emp.EmployeeName || 'غير محدد',
          EmployeeName: emp.EmployeeName || 'غير محدد',
          jobTitle: emp.JobTitle || 'غير محدد',
          JobTitle: emp.JobTitle || 'غير محدد',
          department: emp.Department || 'غير محدد',
          Department: emp.Department || 'غير محدد',
          sourceTable: emp.SourceTable || 'Employees',

          // معرفات موحدة للصور والمستندات
          EmployeeID: empId,
          FullName: emp.EmployeeName || 'غير محدد',

          // للعرض في الاقتراحات
          id: empId,
          name: emp.EmployeeName || 'غير محدد',
          displayText: `${emp.EmployeeName || 'غير محدد'} (${empId})`
        });
      }
    }

    // ترتيب النتائج وتحديد العدد المطلوب
    const finalResults = uniqueResults
      .slice(0, limit)
      .sort((a, b) => {
        if (isNumeric) {
          return parseInt(a.employeeCode) - parseInt(b.employeeCode);
        } else {
          return a.employeeName.localeCompare(b.employeeName, 'ar');
        }
      });

    const response = NextResponse.json({
      success: true,
      data: finalResults,
      searchType: isNumeric ? 'code' : 'name',
      query: cleanSearchTerm,
      totalFound: finalResults.length
    });

    // إضافة headers للـ UTF-8
    response.headers.set('Content-Type', 'application/json; charset=utf-8');

    return response;

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في البحث: ' + error.message
    }, { status: 500 });
  }
}

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const limit = parseInt(searchParams.get('limit')) || 10;

    if (!query || query.trim().length === 0) {
      return NextResponse.json({
        success: true,
        suggestions: []
      });
    }

    const pool = await getConnection();
    const searchTerm = query.trim();

    // اكتشاف الأعمدة الصحيحة
    const { employeeCodeColumn, employeeNameColumn } = await discoverCorrectColumns(pool);

    // تحديد نوع البحث: رقم أم نص
    const isNumeric = /^\d+$/.test(searchTerm);

    let searchQuery;
    let searchParam;

    if (isNumeric) {
      // البحث بالكود
      searchQuery = `
        SELECT TOP (@Limit)
          ${employeeCodeColumn} as EmployeeCode,
          ${employeeNameColumn} as EmployeeName,
          JobTitle,
          Department
        FROM Employees
        WHERE CAST(${employeeCodeColumn} AS NVARCHAR) LIKE @SearchTerm + '%'
        ORDER BY ${employeeCodeColumn}
      `;
      searchParam = searchTerm;
    } else {
      // البحث بالاسم (في أي جزء من الاسم)
      searchQuery = `
        SELECT TOP (@Limit)
          ${employeeCodeColumn} as EmployeeCode,
          ${employeeNameColumn} as EmployeeName,
          JobTitle,
          Department
        FROM Employees
        WHERE ${employeeNameColumn} LIKE '%' + @SearchTerm + '%'
        ORDER BY ${employeeNameColumn}
      `;
      searchParam = searchTerm;
    }

    const dbRequest = pool.request();
    dbRequest.input('SearchTerm', sql.NVarChar, searchParam);
    dbRequest.input('Limit', sql.Int, limit);

    const result = await dbRequest.query(searchQuery);

    const suggestions = result.recordset.map(emp => ({
      // المعرفات
      id: emp.EmployeeCode,
      employeeId: emp.EmployeeCode,

      // التسميات الموحدة
      employeeCode: emp.EmployeeCode,
      EmployeeCode: emp.EmployeeCode,
      employeeName: emp.EmployeeName,
      EmployeeName: emp.EmployeeName,

      // باقي البيانات
      name: emp.EmployeeName,
      jobTitle: emp.JobTitle || 'غير محدد',
      JobTitle: emp.JobTitle || 'غير محدد',
      department: emp.Department || 'غير محدد',
      Department: emp.Department || 'غير محدد',

      // للعرض
      displayText: `${emp.EmployeeName} (${emp.EmployeeCode}) - ${emp.JobTitle || 'غير محدد'}`,
      displayName: emp.EmployeeName,
      displayCode: emp.EmployeeCode,
      displayJobTitle: emp.JobTitle || 'غير محدد',
      displayDepartment: emp.Department || 'غير محدد',
      searchType: isNumeric ? 'code' : 'name'
    }));

    const response = NextResponse.json({
      success: true,
      data: suggestions,
      suggestions: suggestions,
      searchType: isNumeric ? 'code' : 'name',
      query: searchTerm
    });

    // إضافة headers للـ UTF-8
    response.headers.set('Content-Type', 'application/json; charset=utf-8');

    return response;

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في البحث: ' + error.message
    }, { status: 500 });
  }
}
