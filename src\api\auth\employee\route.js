async function handler({ code }) {
  if (!code) {
    return {
      error: 'Employee code is required',
    };
  }

  if (code !== '1450') {
    return {
      error: 'Invalid employee code',
    };
  }

  const employee = await sql`
    SELECT 
      ma.employee_id,
      ma.employee_name,
      ma.department,
      ma.job_title
    FROM monthly_attendance ma 
    WHERE ma.code = ${code}
    LIMIT 1
  `;

  if (!employee || !employee[0]) {
    return {
      error: 'Employee not found',
    };
  }

  return {
    success: true,
    employee: employee[0],
  };
}
