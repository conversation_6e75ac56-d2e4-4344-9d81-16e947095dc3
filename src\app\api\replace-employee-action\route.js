import { NextResponse } from 'next/server';
import { getConnection } from '@/lib/db';
import sql from 'mssql';

export async function POST(request) {
  try {
    const { 
      conflictId, 
      conflictSource, 
      newRequestData 
    } = await request.json();

    if (!conflictId || !conflictSource || !newRequestData) {
      return NextResponse.json({
        success: false,
        error: 'بيانات غير مكتملة'
      }, { status: 400 });
    }

    const pool = await getConnection();

    // بدء المعاملة
    const transaction = new sql.Transaction(pool);
    await transaction.begin();

    try {
      // 1. حذف الإجراء القديم
      if (conflictSource === 'requests') {
        await transaction.request()
          .input('id', sql.Int, conflictId)
          .query(`
            DELETE FROM PaperRequests 
            WHERE ID = @id
          `);

      } else if (conflictSource === 'daily_attendance') {
        // حذف من التمام اليومي
        await transaction.request()
          .input('employeeCode', sql.NVarChar, newRequestData.employeeCode)
          .input('date', sql.Date, newRequestData.startDate)
          .query(`
            DELETE FROM DailyAttendance 
            WHERE EmployeeCode = @employeeCode 
              AND AttendanceDate = @date
          `);

      }

      // 2. إضافة الطلب الجديد
      const result = await addNewRequest(transaction, newRequestData);

      if (result.success) {
        await transaction.commit();

        return NextResponse.json({
          success: true,
          message: 'تم استبدال الإجراء بنجاح',
          newRequestId: result.newRequestId
        });
      } else {
        await transaction.rollback();
        return NextResponse.json({
          success: false,
          error: 'خطأ في إضافة الطلب الجديد: ' + result.error
        }, { status: 500 });
      }

    } catch (transactionError) {
      await transaction.rollback();
      throw transactionError;
    }

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: 'خطأ في استبدال الإجراء: ' + error.message
    }, { status: 500 });
  }
}

// دالة إضافة طلب جديد
async function addNewRequest(transaction, requestData) {
  try {
    const {
      requestType,
      employeeCode,
      employeeName,
      startDate,
      endDate,
      leaveType,
      destination,
      permissionType,
      reason,
      notes
    } = requestData;

    // تحويل التواريخ إلى تنسيق قاعدة البيانات
    const convertDateFormat = (dateStr) => {
      if (!dateStr) return null;
      
      // إذا كان التاريخ بتنسيق dd/mm/yyyy
      if (dateStr.includes('/')) {
        const [day, month, year] = dateStr.split('/');
        return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
      }
      
      return dateStr;
    };

    const formattedStartDate = convertDateFormat(startDate);
    const formattedEndDate = convertDateFormat(endDate);

    let insertQuery = '';
    let requestObj = transaction.request()
      .input('requestType', sql.NVarChar, requestType)
      .input('employeeCode', sql.NVarChar, employeeCode)
      .input('employeeName', sql.NVarChar, employeeName)
      .input('startDate', sql.Date, formattedStartDate)
      .input('endDate', sql.Date, formattedEndDate || formattedStartDate)
      .input('status', sql.NVarChar, 'قيد المراجعة')
      .input('requestDate', sql.DateTime, new Date())
      .input('notes', sql.NVarChar, notes || '');

    if (requestType === 'leave') {
      requestObj.input('leaveType', sql.NVarChar, leaveType);
      insertQuery = `
        INSERT INTO PaperRequests (
          RequestType, EmployeeCode, EmployeeName, StartDate, EndDate,
          LeaveType, Status, RequestDate, Notes
        ) VALUES (
          @requestType, @employeeCode, @employeeName, @startDate, @endDate,
          @leaveType, @status, @requestDate, @notes
        )
      `;
    } else if (requestType === 'mission') {
      requestObj.input('destination', sql.NVarChar, destination);
      insertQuery = `
        INSERT INTO PaperRequests (
          RequestType, EmployeeCode, EmployeeName, StartDate, EndDate,
          MissionDestination, Status, RequestDate, Notes
        ) VALUES (
          @requestType, @employeeCode, @employeeName, @startDate, @endDate,
          @destination, @status, @requestDate, @notes
        )
      `;
    } else if (requestType === 'permission') {
      requestObj.input('permissionStartTime', sql.NVarChar, permissionType);
      requestObj.input('permissionReason', sql.NVarChar, reason);
      insertQuery = `
        INSERT INTO PaperRequests (
          RequestType, EmployeeCode, EmployeeName, StartDate, EndDate,
          PermissionStartTime, PermissionReason, Status, RequestDate, Notes
        ) VALUES (
          @requestType, @employeeCode, @employeeName, @startDate, @endDate,
          @permissionStartTime, @permissionReason, @status, @requestDate, @notes
        )
      `;
    }

    const result = await requestObj.query(insertQuery);

    return {
      success: true,
      newRequestId: result.recordset?.insertId || 'unknown'
    };

  } catch (error) {

    return {
      success: false,
      error: error.message
    };
  }
}
