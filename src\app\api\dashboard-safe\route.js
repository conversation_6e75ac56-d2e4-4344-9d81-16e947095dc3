import { NextResponse } from 'next/server';
import { getConnection } from '@/lib/db';

export async function GET() {
  try {
    console.log('📊 جلب بيانات الداشبورد (الإصدار الآمن)...');
    
    const pool = await getConnection();

    // فحص الجداول المتاحة
    const tablesResult = await pool.request().query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_TYPE = 'BASE TABLE'
      ORDER BY TABLE_NAME
    `);

    const availableTables = tablesResult.recordset.map(row => row.TABLE_NAME);
    const hasPaperRequests = availableTables.includes('PaperRequests');
    const hasLeaveBalances = availableTables.includes('LeaveBalances');
    const hasDailyAttendance = availableTables.includes('DailyAttendance');
    const hasEmployees = availableTables.includes('Employees');

    // إحصائيات الموظفين
    let employeeStats = { totalEmployees: 0 };
    if (hasEmployees) {
      try {
        const empResult = await pool.request().query(`
          SELECT COUNT(*) as TotalEmployees
          FROM Employees
        `);
        employeeStats.totalEmployees = empResult.recordset[0].TotalEmployees;
      } catch (error) {

      }
    }

    // إحصائيات الطلبات (من PaperRequests أو قيم افتراضية)
    let requestStats = {
      totalRequests: 0,
      approvedRequests: 0,
      pendingRequests: 0,
      rejectedRequests: 0
    };

    if (hasPaperRequests) {
      try {
        const reqResult = await pool.request().query(`
          SELECT 
            COUNT(*) as TotalRequests,
            SUM(CASE WHEN Status = N'معتمد' OR Status = N'معتمدة' THEN 1 ELSE 0 END) as ApprovedRequests,
            SUM(CASE WHEN Status = N'قيد المراجعة' OR Status = N'pending' THEN 1 ELSE 0 END) as PendingRequests,
            SUM(CASE WHEN Status = N'مرفوض' OR Status = N'مرفوضة' THEN 1 ELSE 0 END) as RejectedRequests
          FROM PaperRequests
          WHERE RequestType = 'leave'
        `);
        
        if (reqResult.recordset.length > 0) {
          const stats = reqResult.recordset[0];
          requestStats = {
            totalRequests: stats.TotalRequests || 0,
            approvedRequests: stats.ApprovedRequests || 0,
            pendingRequests: stats.PendingRequests || 0,
            rejectedRequests: stats.RejectedRequests || 0
          };
        }
      } catch (error) {

      }
    }

    // إحصائيات أنواع الإجازات (من التمام اليومي)
    let leaveTypeStats = {
      annual: 0,
      emergency: 0,
      sick: 0,
      maternity: 0
    };

    if (hasDailyAttendance) {
      try {
        const leaveResult = await pool.request().query(`
          SELECT 
            SUM(CASE WHEN Attendance LIKE N'%اعتيادية%' THEN 1 ELSE 0 END) as Annual,
            SUM(CASE WHEN Attendance LIKE N'%عارضة%' THEN 1 ELSE 0 END) as Emergency,
            SUM(CASE WHEN Attendance LIKE N'%مرضية%' THEN 1 ELSE 0 END) as Sick,
            SUM(CASE WHEN Attendance LIKE N'%أمومة%' OR Attendance LIKE N'%وضع%' THEN 1 ELSE 0 END) as Maternity
          FROM DailyAttendance
          WHERE Attendance LIKE N'%إجازة%'
          AND YEAR(AttendanceDate) = YEAR(GETDATE())
        `);
        
        if (leaveResult.recordset.length > 0) {
          const stats = leaveResult.recordset[0];
          leaveTypeStats = {
            annual: stats.Annual || 0,
            emergency: stats.Emergency || 0,
            sick: stats.Sick || 0,
            maternity: stats.Maternity || 0
          };
        }
      } catch (error) {

      }
    }

    // إحصائيات الأرصدة
    let balanceStats = {
      totalAnnualBalance: 0,
      totalCasualBalance: 0,
      totalUsedAnnual: 0,
      totalUsedCasual: 0,
      employeesWithBalance: 0
    };

    if (hasLeaveBalances) {
      try {
        const balResult = await pool.request().query(`
          SELECT 
            COUNT(*) as EmployeesWithBalance,
            SUM(ISNULL(AnnualBalance, 15)) as TotalAnnualBalance,
            SUM(ISNULL(CasualBalance, 6)) as TotalCasualBalance,
            SUM(ISNULL(UsedRegular, 0)) as TotalUsedAnnual,
            SUM(ISNULL(UsedCasual, 0)) as TotalUsedCasual
          FROM LeaveBalances
          WHERE Year = YEAR(GETDATE())
        `);
        
        if (balResult.recordset.length > 0) {
          const stats = balResult.recordset[0];
          balanceStats = {
            totalAnnualBalance: stats.TotalAnnualBalance || 0,
            totalCasualBalance: stats.TotalCasualBalance || 0,
            totalUsedAnnual: stats.TotalUsedAnnual || 0,
            totalUsedCasual: stats.TotalUsedCasual || 0,
            employeesWithBalance: stats.EmployeesWithBalance || 0
          };
        }
      } catch (error) {

      }
    }

    // إحصائيات الحضور الشهري
    let attendanceStats = {
      totalWorkingDays: 0,
      totalPresentDays: 0,
      totalLeaveDays: 0,
      attendancePercentage: 0
    };

    if (hasDailyAttendance) {
      try {
        const attResult = await pool.request().query(`
          SELECT 
            COUNT(*) as TotalWorkingDays,
            SUM(CASE WHEN Attendance = N'حضور' THEN 1 ELSE 0 END) as TotalPresentDays,
            SUM(CASE WHEN Attendance LIKE N'%إجازة%' THEN 1 ELSE 0 END) as TotalLeaveDays
          FROM DailyAttendance
          WHERE MONTH(AttendanceDate) = MONTH(GETDATE())
          AND YEAR(AttendanceDate) = YEAR(GETDATE())
        `);
        
        if (attResult.recordset.length > 0) {
          const stats = attResult.recordset[0];
          attendanceStats = {
            totalWorkingDays: stats.TotalWorkingDays || 0,
            totalPresentDays: stats.TotalPresentDays || 0,
            totalLeaveDays: stats.TotalLeaveDays || 0,
            attendancePercentage: stats.TotalWorkingDays > 0 
              ? ((stats.TotalPresentDays / stats.TotalWorkingDays) * 100).toFixed(2)
              : 0
          };
        }
      } catch (error) {

      }
    }

    // الطلبات الأخيرة
    let recentRequests = [];
    if (hasPaperRequests) {
      try {
        const recentResult = await pool.request().query(`
          SELECT TOP 5
            ID,
            EmployeeName,
            LeaveType,
            StartDate,
            EndDate,
            Status,
            RequestDate
          FROM PaperRequests
          WHERE RequestType = 'leave'
          ORDER BY RequestDate DESC
        `);
        recentRequests = recentResult.recordset;
      } catch (error) {

      }
    }

    // تجميع البيانات النهائية
    const dashboardData = {
      // إحصائيات عامة
      totalEmployees: employeeStats.totalEmployees,
      totalRequests: requestStats.totalRequests,
      approvedRequests: requestStats.approvedRequests,
      pendingRequests: requestStats.pendingRequests,
      rejectedRequests: requestStats.rejectedRequests,
      
      // أنواع الإجازات
      leaveTypes: leaveTypeStats,
      
      // الأرصدة
      balances: balanceStats,
      
      // الحضور
      attendance: attendanceStats,
      
      // الطلبات الأخيرة
      recentRequests,
      
      // معلومات النظام
      systemInfo: {
        availableTables,
        dataSource: {
          requests: hasPaperRequests ? 'PaperRequests' : 'غير متاح',
          balances: hasLeaveBalances ? 'LeaveBalances' : 'غير متاح',
          attendance: hasDailyAttendance ? 'DailyAttendance' : 'غير متاح',
          employees: hasEmployees ? 'Employees' : 'غير متاح'
        },
        lastUpdated: new Date().toISOString()
      }
    };

    return NextResponse.json({
      success: true,
      message: '📊 تم جلب بيانات الداشبورد بنجاح (الإصدار الآمن)',
      data: dashboardData
    });

  } catch (error) {

    return NextResponse.json({
      success: false,
      error: error.message,
      fallbackData: {
        totalEmployees: 0,
        totalRequests: 0,
        approvedRequests: 0,
        pendingRequests: 0,
        rejectedRequests: 0,
        leaveTypes: { annual: 0, emergency: 0, sick: 0, maternity: 0 },
        balances: { totalAnnualBalance: 0, totalCasualBalance: 0, totalUsedAnnual: 0, totalUsedCasual: 0 },
        attendance: { totalWorkingDays: 0, totalPresentDays: 0, totalLeaveDays: 0, attendancePercentage: 0 },
        recentRequests: [],
        systemInfo: { error: 'فشل في الاتصال بقاعدة البيانات' }
      }
    }, { status: 500 });
  }
}
