import { NextResponse } from 'next/server';
import { getConnection } from '@/utils/db';

export async function GET() {
  try {
    const pool = await getConnection();
    const result = await pool.request().query(`
      SELECT 
        GovernorateID, 
        GovernorateName,
        CASE 
          WHEN GovernorateID = 1 THEN 1  -- القاهرة أولاً
          ELSE 2
        END as SortOrder
      FROM Governorates
      ORDER BY SortOrder, GovernorateName
    `);
    await pool.close();

    if (!result.recordset || result.recordset.length === 0) {
    }

    return NextResponse.json(result.recordset || []);
  } catch (error) {
    return NextResponse.json(
      { error: 'فشل في جلب قائمة المحافظات' },
      { status: 500 }
    );
  }
}

// Initialize governorates table
export async function POST() {
  try {
    const pool = await getConnection();

    // Create Governorates table if it doesn't exist
    await pool.request().query(`
      IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'Governorates')
      BEGIN
        CREATE TABLE Governorates (
          GovernorateId INT IDENTITY(1,1) PRIMARY KEY,
          NameAr NVARCHAR(100) NOT NULL,
          CreatedAt DATETIME DEFAULT GETDATE()
        )
      END
    `);

    // Governorates list
    const governorates = [
      'القاهرة',
      'الإسكندرية',
      'بورسعيد',
      'السويس',
      'دمياط',
      'الدقهلية',
      'الشرقية',
      'القليوبية',
      'كفر الشيخ',
      'الغربية',
      'المنوفية',
      'البحيرة',
      'الفيوم',
      'بني سويف',
      'مينا',
      'أسيوط',
      'سوهاج',
      'قنا',
      'الأقصر',
      'أسوان',
      'شمال سيناء',
      'جنوب سيناء',
      'البحر الأحمر',
      'الوادي الجديد',
      'مطروح',
      'حلوان',
      'أكتوبر',
      'مرسى مطروح'
    ];

    // Insert each governorate
    for (const gov of governorates) {
      await pool.request()
        .input('nameAr', sql.NVarChar, gov)
        .query(`
          IF NOT EXISTS (SELECT 1 FROM Governorates WHERE NameAr = @nameAr)
          INSERT INTO Governorates (NameAr)
          VALUES (@nameAr)
        `);
    }

    return NextResponse.json({
      success: true,
      message: 'Governorates initialized successfully'
    });
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        message: 'Error initializing governorates',
        error: error.message
      },
      { status: 500 }
    );
  }
}
