'use client';
import React from 'react';

import { useUpload } from '@/app/utilities/runtime-helpers';

function MainComponent() {
  const [selectedLang, setSelectedLang] = useState('ar');
  const [employee, setEmployee] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [upload, { loading: uploading }] = useUpload();

  const dir = selectedLang === 'ar' ? 'rtl' : 'ltr';
  const { id } = useParams();

  const [formData, setFormData] = useState({
    name: '',
    nameAr: '',
    employeeId: '',
    position: '',
    department: '',
    email: '',
    phone: '',
    nationality: '',
    dateOfBirth: '',
    address: '',
    education: [],
    documents: [],
    startDate: '',
    salary: '',
    bankAccount: '',
    emergencyContact: '',
  });

  useEffect(() => {
    const fetchEmployee = async () => {
      try {
        const response = await fetch(`/api/employee-data-handler?id=${id}`);
        if (!response.ok) {
          throw new Error(
            selectedLang === 'ar'
              ? 'خطأ في جلب البيانات'
              : 'Error fetching data'
          );
        }
        const data = await response.json();
        setEmployee(data);
        setFormData(data);
        setLoading(false);
      } catch (err) {
        setError(
          selectedLang === 'ar' ? 'خطأ في تحميل بيانات الموظف' : 'Error loading employee data'
        );
        setLoading(false);
      }
    };
    fetchEmployee();
  }, [id]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleFileUpload = async (e) => {
    const file = e.target.files[0];
    try {
      const { url, error } = await upload({ file });
      if (error) throw new Error(error);

      setFormData((prev) => ({
        ...prev,
        documents: [...prev.documents, { name: file.name, url }],
      }));
    } catch (err) {
      setError(err.message);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await fetch(`/api/employee-data-handler`, {
        method: 'PUT',
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error(
          selectedLang === 'ar' ? 'خطأ في رفع الملف' : 'Error uploading file'
        );
      }

      setSuccess(true);
      setIsEditing(false);
      setTimeout(() => setSuccess(false), 3000);
    } catch (err) {
      setError(
        selectedLang === 'ar' ? 'خطأ في حفظ البيانات' : 'Error saving data'
      );
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <i className="fas fa-spinner fa-spin text-4xl text-blue-600"></i>
      </div>
    );
  }

  return (
    <div
      dir={dir}
      className="min-h-screen bg-white dark:bg-gray-900 p-4 md:p-8"
    >
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <div className="flex items-center gap-4">
            <a
              href="/employees"
              className="flex items-center text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white"
            >
              <i
                className={`fas fa-arrow-${
                  selectedLang === 'ar' ? 'left' : 'right'
                } ml-2`}
              ></i>
              {selectedLang === 'ar' ? 'عودة' : 'Back'}
            </a>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              {selectedLang === 'ar' ? 'تفاصيل الموظف' : 'Employee Details'}
            </h1>
          </div>
          <div className="flex gap-4">
            <button
              onClick={() => setIsEditing(!isEditing)}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              {isEditing
                ? selectedLang === 'ar'
                  ? 'إلغاء'
                  : 'Cancel'
                : selectedLang === 'ar'
                  ? 'تعديل'
                  : 'Edit'}
            </button>
            <button
              onClick={() =>
                setSelectedLang(selectedLang === 'ar' ? 'en' : 'ar')
              }
              className="px-4 py-2 text-sm text-gray-700 border border-gray-200 rounded-md hover:bg-gray-900 hover:text-white transition-colors"
            >
              {selectedLang === 'ar' ? 'English' : 'العربية'}
            </button>
          </div>
        </div>

        {error && (
          <div className="mb-4 p-4 bg-red-100 text-red-700 rounded-md">
            {error}
          </div>
        )}

        {success && (
          <div className="mb-4 p-4 bg-green-100 text-green-700 rounded-md">
            {selectedLang === 'ar'
              ? 'تم حفظ البيانات بنجاح'
              : 'Data saved successfully'}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-8">
          <div className="bg-gray-50 dark:bg-gray-800 rounded-xl p-6">
            <h2 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">
              {selectedLang === 'ar'
                ? 'المعلومات الأساسية'
                : 'Basic Information'}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  {selectedLang === 'ar' ? 'الاسم' : 'Name'}
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  disabled={!isEditing}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
              </div>
              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  {selectedLang === 'ar' ? 'الاسم بالعربية' : 'Arabic Name'}
                </label>
                <input
                  type="text"
                  name="nameAr"
                  value={formData.nameAr}
                  onChange={handleInputChange}
                  disabled={!isEditing}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
              </div>
              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  {selectedLang === 'ar' ? 'رقم الموظف' : 'Employee ID'}
                </label>
                <input
                  type="text"
                  name="employeeId"
                  value={formData.employeeId}
                  onChange={handleInputChange}
                  disabled={!isEditing}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
              </div>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-800 rounded-xl p-6">
            <h2 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">
              {selectedLang === 'ar' ? 'معلومات الوظيفة' : 'Job Information'}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  {selectedLang === 'ar' ? 'المنصب' : 'Position'}
                </label>
                <input
                  type="text"
                  name="position"
                  value={formData.position}
                  onChange={handleInputChange}
                  disabled={!isEditing}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
              </div>
              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  {selectedLang === 'ar' ? 'القسم' : 'Department'}
                </label>
                <input
                  type="text"
                  name="department"
                  value={formData.department}
                  onChange={handleInputChange}
                  disabled={!isEditing}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
              </div>
              <div>
                <label className="block text-gray-700 dark:text-gray-300 mb-2">
                  {selectedLang === 'ar' ? 'تاريخ التعيين' : 'Start Date'}
                </label>
                <input
                  type="date"
                  name="startDate"
                  value={formData.startDate}
                  onChange={handleInputChange}
                  disabled={!isEditing}
                  className="w-full p-2 border border-gray-300 rounded-md"
                />
              </div>
            </div>
          </div>

          <div className="bg-gray-50 dark:bg-gray-800 rounded-xl p-6">
            <h2 className="text-xl font-bold mb-4 text-gray-900 dark:text-white">
              {selectedLang === 'ar' ? 'المستندات' : 'Documents'}
            </h2>
            <div className="space-y-4">
              {formData.documents.map((doc, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 bg-white dark:bg-gray-700 rounded-md"
                >
                  <span className="text-gray-700 dark:text-gray-300">
                    {doc.name}
                  </span>
                  <a
                    href={doc.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800"
                  >
                    <i className="fas fa-download"></i>
                  </a>
                </div>
              ))}
              {isEditing && (
                <div className="mt-4">
                  <input
                    type="file"
                    onChange={handleFileUpload}
                    className="w-full p-2 border border-gray-300 rounded-md"
                  />
                </div>
              )}
            </div>
          </div>

          {isEditing && (
            <div className="flex justify-end">
              <button
                type="submit"
                className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                disabled={uploading}
              >
                {selectedLang === 'ar' ? 'حفظ التغييرات' : 'Save Changes'}
              </button>
            </div>
          )}
        </form>
      </div>
    </div>
  );
}

export default MainComponent;
