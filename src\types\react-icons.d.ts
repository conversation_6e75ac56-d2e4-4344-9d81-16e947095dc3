// تصريحات TypeScript لمكتبة react-icons
declare module 'react-icons/fi' {
  import { ComponentType, SVGProps } from 'react';
  
  interface IconProps extends SVGProps<SVGSVGElement> {
    className?: string;
    size?: string | number;
    color?: string;
  }
  
  export const FiUsers: ComponentType<IconProps>;
  export const FiHome: ComponentType<IconProps>;
  export const FiTruck: ComponentType<IconProps>;
  export const FiDollarSign: ComponentType<IconProps>;
  export const FiTrendingUp: ComponentType<IconProps>;
  export const FiBarChart: ComponentType<IconProps>;
  export const FiPieChart: ComponentType<IconProps>;
  export const FiActivity: ComponentType<IconProps>;
  export const FiCalendar: ComponentType<IconProps>;
  export const FiLogOut: ComponentType<IconProps>;
  export const FiSettings: ComponentType<IconProps>;
  export const FiUser: ComponentType<IconProps>;
  export const FiRefreshCw: ComponentType<IconProps>;
  export const FiSun: ComponentType<IconProps>;
  export const FiMoon: ComponentType<IconProps>;
  export const FiArrowRight: ComponentType<IconProps>;
  export const FiPlus: ComponentType<IconProps>;
  export const FiUserX: ComponentType<IconProps>;
  export const FiArrowLeft: ComponentType<IconProps>;
  export const FiMapPin: ComponentType<IconProps>;
  export const FiShield: ComponentType<IconProps>;
  export const FiBell: ComponentType<IconProps>;
  export const FiCheckCircle: ComponentType<IconProps>;
  export const FiXCircle: ComponentType<IconProps>;
  export const FiAlertCircle: ComponentType<IconProps>;
  export const FiHeart: ComponentType<IconProps>;
  export const FiGlobe: ComponentType<IconProps>;
}
