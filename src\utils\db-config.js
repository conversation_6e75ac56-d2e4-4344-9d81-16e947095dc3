// إعدادات خاصة لقاعدة البيانات لتجنب تحذيرات ESM

// تعطيل debug logging لتجنب مشاكل supports-color
if (typeof process !== 'undefined' && process.env) {
  process.env.DEBUG = '';
  process.env.NODE_ENV = process.env.NODE_ENV || 'production';
}

// إعدادات mssql محسنة
export const dbConfig = {
  server: process.env.DB_SERVER || 'localhost\\DBOJESTA',
  database: process.env.DB_NAME || 'EMP',
  user: process.env.DB_USER || 'SA',
  password: process.env.DB_PASSWORD || 'admin@123',
  options: {
    encrypt: false,
    trustServerCertificate: true,
    enableArithAbort: true,
    instanceName: 'DBOJESTA'
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000
  },
  // تعطيل debug logging
  debug: {
    packet: false,
    data: false,
    payload: false,
    token: false
  }
};

// تعطيل تحذيرات Node.js
if (typeof process !== 'undefined' && process.removeAllListeners) {
  process.removeAllListeners('warning');
}

export default dbConfig;
