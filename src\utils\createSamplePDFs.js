// ملف لإنشاء ملفات PDF تجريبية للاختبار
const fs = require('fs');
const path = require('path');

const ARCHIVE_BASE_PATH = 'E:\\webapp\\createxyz-project\\archiv';

// إنشاء ملفات PDF تجريبية
function createSamplePDFs() {
  const folders = [
    'Resignation',
    'Transfer', 
    'last_period',
    'Clearance'
  ];

  const sampleEmployeeIds = ['1450', '5707', '6102', '1414'];

  // محتوى PDF بسيط (في الواقع سيكون ملف PDF حقيقي)
  const pdfContent = `%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj

2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj

3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj

4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
72 720 Td
(Sample Document) Tj
ET
endstream
endobj

xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000206 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
299
%%EOF`;

  folders.forEach(folder => {
    const folderPath = path.join(ARCHIVE_BASE_PATH, folder);
    
    // إنشاء المجلد إذا لم يكن موجوداً
    if (!fs.existsSync(folderPath)) {
      fs.mkdirSync(folderPath, { recursive: true });

    }

    // إنشاء ملفات PDF تجريبية لبعض الموظفين
    sampleEmployeeIds.forEach(employeeId => {
      const filePath = path.join(folderPath, `${employeeId}.pdf`);
      
      if (!fs.existsSync(filePath)) {
        fs.writeFileSync(filePath, pdfContent);

      }
    });
  });

}

// تشغيل الدالة
if (require.main === module) {
  createSamplePDFs();
}

module.exports = { createSamplePDFs };
