export async function PATCH(request, { params }) {
  try {
    const { id } = params;
    const data = await request.json();
    const pool = await getConnection();

    // جلب الحالة الحالية للطلب
    const currentStatusResult = await pool.request()
      .input('id', sql.Int, id)
      .query('SELECT Status FROM LeaveRequests WHERE ID = @id');

    if (currentStatusResult.recordset.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'لم يتم العثور على الطلب'
      }, { status: 404 });
    }

    const previousStatus = currentStatusResult.recordset[0].Status;
    // تحديث حالة الطلب
    const result = await pool.request()
      .input('id', sql.Int, id)
      .input('status', sql.NVarChar, data.status)
      .input('approvalNotes', sql.NVarChar, data.approvalNotes || '')
      .query(`
        UPDATE PaperRequests
        SET Status = @status,
            ApprovalNotes = @approvalNotes
        WHERE ID = @id;
      `);
    // تحديث رصيد الإجازات إذا تم تغيير الحالة
    if (data.status !== previousStatus) {
      await updateLeaveBalanceAfterStatusChange(pool, id, data.status, previousStatus);
    }

    return NextResponse.json({
      success: true,
      message: 'تم تحديث حالة الطلب بنجاح'
    });

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'خطأ في تحديث حالة الطلب: ' + error.message
    }, { status: 500 });
  }
} 