'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  <PERSON>LogOut,
  FiUser,
  FiCalendar,
  FiFileText,
  FiEye,
  FiPlus,
  FiDownload,
  FiRefreshCw,
  FiSearch,
  FiDollarSign,
  FiMessageSquare,
  FiExternalLink
} from 'react-icons/fi';

// مكون أرشيف الاستقالة
function ResignationArchive({ employeeId }) {
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (employeeId) {
      fetchResignationDocuments();
    }
  }, [employeeId]);

  const fetchResignationDocuments = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/document-archive', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'getResignationDocuments',
          employeeId: employeeId
        })
      });

      const data = await response.json();
      if (data.success) {
        setDocuments(data.documents);
      }
    } catch (error) {

    } finally {
      setLoading(false);
    }
  };

  const openDocument = (path) => {
    window.open(path, '_blank');
  };

  return (
    <div className="mt-6">
      <h4 className="text-lg font-semibold text-orange-600 border-b border-orange-200 pb-2 mb-4">
        أرشيف الاستقالة
      </h4>

      {loading ? (
        <div className="text-center py-4">
          <FiRefreshCw className="animate-spin text-2xl text-gray-400 mx-auto mb-2" />
          <p className="text-gray-500">جاري تحميل المستندات...</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {documents.length > 0 ? (
            documents.map((doc, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center gap-3 mb-3">
                  <span className="text-xl">{doc.icon}</span>
                  <span className="font-medium text-sm">{doc.name}</span>
                </div>
                <button
                  onClick={() => openDocument(doc.path)}
                  className="w-full text-white px-3 py-2 rounded hover:opacity-90 transition-colors text-sm flex items-center justify-center gap-2"
                  style={{ backgroundColor: doc.color }}
                >
                  <FiExternalLink />
                  عرض المستند
                </button>
              </div>
            ))
          ) : (
            <div className="col-span-3 text-center py-8 text-gray-500">
              <FiFileText className="text-4xl mx-auto mb-2 opacity-50" />
              <p>لا توجد مستندات استقالة متاحة</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default function ResignationsPage() {
  const router = useRouter();
  const [resignations, setResignations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedResignation, setSelectedResignation] = useState(null);
  const [showDetails, setShowDetails] = useState(false);

  // جلب بيانات الاستقالة
  const fetchResignations = async () => {
    try {
      setLoading(true);

      const response = await fetch('/api/resignations');
      const data = await response.json();

      if (data.success) {
        setResignations(data.data);

      } else {
        setError(data.error || 'فشل في جلب البيانات');
      }
    } catch (error) {

      setError('حدث خطأ في جلب البيانات');
    } finally {
      setLoading(false);
    }
  };

  // فحص تسجيل الدخول وجلب البيانات
  useEffect(() => {
    const isLoggedIn = localStorage.getItem('isLoggedIn');
    if (!isLoggedIn) {
      router.push('/login');
      return;
    }

    fetchResignations();
  }, [router]);

  // تصفية البيانات حسب البحث
  const filteredResignations = resignations.filter(resignation =>
    resignation.EmployeeName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    resignation.EmployeeID?.toString().includes(searchTerm) ||
    resignation.Department?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    resignation.ResignationReason?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // عرض تفاصيل الاستقالة
  const viewResignationDetails = (resignation) => {
    setSelectedResignation(resignation);
    setShowDetails(true);
  };

  // تصدير البيانات
  const exportData = () => {

    alert('سيتم تنفيذ تصدير البيانات قريباً');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <FiRefreshCw className="animate-spin text-4xl text-red-600 mx-auto mb-4" />
          <p className="text-gray-600">جاري تحميل حالات الاستقالة...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* رأس الصفحة */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <FiLogOut className="text-2xl text-red-600" />
              <h1 className="text-3xl font-bold text-gray-800">حالات الاستقالة</h1>
              <span className="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-medium">
                {resignations.length} حالة استقالة
              </span>
            </div>
            
            <div className="flex gap-3">
              <button
                onClick={() => router.push('/employees/resignations/add')}
                className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors flex items-center gap-2"
              >
                <FiPlus />
                إضافة استقالة جديدة
              </button>
              <button
                onClick={exportData}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
              >
                <FiDownload />
                تصدير البيانات
              </button>
              <button
                onClick={fetchResignations}
                className="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors flex items-center gap-2"
              >
                <FiRefreshCw />
                تحديث
              </button>
              <button
                onClick={() => router.push('/employees/search')}
                className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors flex items-center gap-2"
              >
                ← العودة للموظفين
              </button>
            </div>
          </div>

          {/* شريط البحث */}
          <div className="relative">
            <FiSearch className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="البحث بالاسم أو الكود أو القسم أو سبب الاستقالة..."
              className="w-full pr-10 pl-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* رسالة الخطأ */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {/* جدول الاستقالة */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الموظف
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    القسم والمسمى
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    تاريخ الاستقالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    سبب الاستقالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    التسوية النهائية
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredResignations.length > 0 ? (
                  filteredResignations.map((resignation, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-red-100 flex items-center justify-center">
                              <FiUser className="text-red-600" />
                            </div>
                          </div>
                          <div className="mr-4">
                            <div className="text-sm font-medium text-gray-900">
                              {resignation.EmployeeName || resignation.CurrentEmployeeName}
                            </div>
                            <div className="text-sm text-gray-500">
                              #{resignation.EmployeeID}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{resignation.Department}</div>
                        <div className="text-sm text-gray-500">{resignation.JobTitle}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center text-sm text-gray-900">
                          <FiCalendar className="ml-2 text-gray-400" />
                          {resignation.ResignationDate ? 
                            new Date(resignation.ResignationDate).toLocaleDateString('ar-EG') : 
                            'غير محدد'
                          }
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center text-sm text-gray-900 max-w-xs">
                          <FiMessageSquare className="ml-2 text-gray-400 flex-shrink-0" />
                          <span className="truncate">
                            {resignation.ResignationReason || 'غير محدد'}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center text-sm text-gray-900">
                          <FiDollarSign className="ml-2 text-green-500" />
                          {resignation.FinalSettlementAmount ? 
                            `${resignation.FinalSettlementAmount.toLocaleString()} ج.م` : 
                            'غير محدد'
                          }
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => viewResignationDetails(resignation)}
                          className="text-red-600 hover:text-red-900 flex items-center gap-1"
                        >
                          <FiEye />
                          عرض التفاصيل
                        </button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="6" className="px-6 py-12 text-center">
                      <div className="text-gray-500">
                        <FiLogOut className="text-4xl mx-auto mb-4 opacity-50" />
                        <p className="text-lg">لا توجد حالات استقالة</p>
                        <p className="text-sm">ابدأ بإضافة استقالة جديدة</p>
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* نافذة تفاصيل الاستقالة */}
        {showDetails && selectedResignation && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
              <div className="flex items-center justify-between p-6 border-b">
                <h3 className="text-xl font-bold text-gray-800">
                  تفاصيل استقالة {selectedResignation.EmployeeName}
                </h3>
                <button
                  onClick={() => setShowDetails(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>

              <div className="p-6 overflow-y-auto max-h-[70vh]">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* بيانات الموظف */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-blue-600 border-b border-blue-200 pb-2">
                      بيانات الموظف
                    </h4>
                    <div><span className="font-medium">الكود:</span> {selectedResignation.EmployeeID}</div>
                    <div><span className="font-medium">الاسم:</span> {selectedResignation.EmployeeName}</div>
                    <div><span className="font-medium">القسم:</span> {selectedResignation.Department}</div>
                    <div><span className="font-medium">المسمى الوظيفي:</span> {selectedResignation.JobTitle}</div>
                    <div><span className="font-medium">الحالة الحالية:</span> 
                      <span className="mr-2 px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">
                        {selectedResignation.CurrentStatus || 'مستقيل'}
                      </span>
                    </div>
                  </div>

                  {/* بيانات الاستقالة */}
                  <div className="space-y-4">
                    <h4 className="text-lg font-semibold text-red-600 border-b border-red-200 pb-2">
                      بيانات الاستقالة
                    </h4>
                    <div><span className="font-medium">تاريخ الاستقالة:</span> 
                      {selectedResignation.ResignationDate ? 
                        new Date(selectedResignation.ResignationDate).toLocaleDateString('ar-EG') : 
                        'غير محدد'
                      }
                    </div>
                    <div><span className="font-medium">آخر يوم عمل:</span> 
                      {selectedResignation.LastWorkingDay ? 
                        new Date(selectedResignation.LastWorkingDay).toLocaleDateString('ar-EG') : 
                        'غير محدد'
                      }
                    </div>
                    <div><span className="font-medium">التسوية النهائية:</span> 
                      {selectedResignation.FinalSettlementAmount ? 
                        `${selectedResignation.FinalSettlementAmount.toLocaleString()} ج.م` : 
                        'غير محدد'
                      }
                    </div>
                  </div>
                </div>

                {/* سبب الاستقالة */}
                <div className="mt-6">
                  <h4 className="text-lg font-semibold text-purple-600 border-b border-purple-200 pb-2 mb-4">
                    سبب الاستقالة
                  </h4>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-gray-700">
                      {selectedResignation.ResignationReason || 'لم يتم تحديد سبب الاستقالة'}
                    </p>
                  </div>
                </div>

                {/* أرشيف الاستقالة */}
                <ResignationArchive employeeId={selectedResignation.EmployeeID} />
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
